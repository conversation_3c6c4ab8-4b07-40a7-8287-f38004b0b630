<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20150824 at Sat Jan  9 01:05:10 2016
 By Everything Fonts
Copyright (c) <PERSON>, 2005. All rights reserved.
</metadata>
<defs>
<font id="Proxima_Nova_Bold_Italic" horiz-adv-x="579" >
  <font-face 
    font-family="Proxima_Nova_Bold_Italic"
    font-weight="700"
    font-style="italic"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 6 3 0 0 2 0 4"
    ascent="790"
    descent="-210"
    x-height="483"
    cap-height="667"
    bbox="-226 -282 1157 899"
    underline-thickness="20"
    underline-position="-153"
    unicode-range="U+000D-FB04"
  />
<missing-glyph horiz-adv-x="483" 
d="M437 -90h-351v842h351v-842zM405 -60v782h-288v-782h288z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="907" 
d="M492 0h-128l83 372h-80l24 111h80l6 26q18 79 64 123.5t117 44.5q64 0 106 -27l-44 -86q-17 13 -46 13q-55 0 -70 -68l-6 -26h99l-25 -111h-98zM601 -196q-90 0 -134 41l53 91q22 -28 63 -28q59 0 77 79l111 496h127l-111 -496q-19 -88 -63 -135.5t-123 -47.5zM853 535
q-29 0 -48.5 19t-19.5 46q0 39 26.5 62.5t58.5 23.5q30 0 49.5 -19t19.5 -46q0 -39 -27 -62.5t-59 -23.5zM165 0h-127l82 373h-80l25 110h79l6 27q17 75 63 121t123 46q81 0 127 -47l-63 -74q-21 21 -54 21q-54 0 -69 -69l-6 -25h99l-25 -110h-98z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="580" 
d="M165 0h-128l83 372h-80l24 111h80l6 26q18 79 64 123.5t117 44.5q64 0 106 -27l-44 -86q-17 13 -46 13q-55 0 -70 -68l-6 -26h99l-25 -111h-98zM273 -196q-90 0 -134 41l53 91q22 -28 63 -28q59 0 77 79l111 496h127l-111 -496q-19 -88 -63 -135.5t-123 -47.5zM525 535
q-29 0 -48.5 19t-19.5 46q0 39 26.5 62.5t58.5 23.5q30 0 49.5 -19t19.5 -46q0 -39 -27 -62.5t-59 -23.5z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="655" 
d="M165 0h-127l82 373h-80l25 110h79l6 27q17 75 63 121t123 46q81 0 127 -47l-63 -74q-21 21 -54 21q-54 0 -69 -69l-6 -25h99l-25 -110h-98zM492 0h-127l82 373h-80l25 110h79l6 27q17 75 63 121t123 46q81 0 127 -47l-63 -74q-21 21 -54 21q-54 0 -69 -69l-6 -25h99
l-25 -110h-98z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="580" 
d="M165 0h-128l83 372h-80l24 111h80l6 26q18 79 64 123.5t117 44.5q64 0 106 -27l-44 -86q-17 13 -46 13q-55 0 -70 -68l-6 -26h99l-25 -111h-98zM525 535q-29 0 -48.5 19t-19.5 46q0 39 26.5 62.5t58.5 23.5q30 0 49.5 -19t19.5 -46q0 -39 -27 -62.5t-59 -23.5zM463 0
h-127l107 483h127z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="580" 
d="M165 0h-128l83 372h-80l24 111h80l6 26q18 79 64 123.5t117 44.5q64 0 106 -27l-44 -86q-17 13 -46 13q-55 0 -70 -68l-6 -26h99l-25 -111h-98zM463 0h-127l147 667h127z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="907" 
d="M492 0h-128l83 372h-80l24 111h80l6 26q18 79 64 123.5t117 44.5q64 0 106 -27l-44 -86q-17 13 -46 13q-55 0 -70 -68l-6 -26h99l-25 -111h-98zM853 535q-29 0 -48.5 19t-19.5 46q0 39 26.5 62.5t58.5 23.5q30 0 49.5 -19t19.5 -46q0 -39 -27 -62.5t-59 -23.5zM791 0
h-127l107 483h127zM165 0h-127l82 373h-80l25 110h79l6 27q17 75 63 121t123 46q81 0 127 -47l-63 -74q-21 21 -54 21q-54 0 -69 -69l-6 -25h99l-25 -110h-98z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="907" 
d="M492 0h-128l83 372h-80l24 111h80l6 26q18 79 64 123.5t117 44.5q64 0 106 -27l-44 -86q-17 13 -46 13q-55 0 -70 -68l-6 -26h99l-25 -111h-98zM791 0h-127l147 667h127zM165 0h-127l82 373h-80l25 110h79l6 27q17 75 63 121t123 46q81 0 127 -47l-63 -74q-21 21 -54 21
q-54 0 -69 -69l-6 -25h99l-25 -110h-98z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="912" 
d="M165 0h-128l83 372h-80l24 111h80l6 26q18 79 64 123.5t117 44.5q64 0 106 -27l-44 -86q-17 13 -46 13q-55 0 -70 -68l-6 -26h99l-25 -111h-98zM631 -12q-103 0 -154 76l-14 -64h-127l147 667h127l-54 -244q62 72 146 72q83 0 133 -53t50 -147q0 -75 -28.5 -144t-88 -116
t-137.5 -47zM610 101q62 0 102 49.5t40 118.5q0 51 -30 82t-77 31q-64 0 -111 -58l-37 -168q36 -55 113 -55z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="907" 
d="M165 0h-128l83 372h-80l24 111h80l6 26q18 79 64 123.5t117 44.5q64 0 106 -27l-44 -86q-17 13 -46 13q-55 0 -70 -68l-6 -26h99l-25 -111h-98zM790 0h-127l63 290q5 20 5 30q0 62 -83 62q-56 0 -113 -57l-72 -325h-127l147 667h127l-54 -247q77 75 161 75
q69 0 110 -33.5t41 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="863" 
d="M165 0h-128l83 372h-80l24 111h80l6 26q18 79 64 123.5t117 44.5q64 0 106 -27l-44 -86q-17 13 -46 13q-55 0 -70 -68l-6 -26h99l-25 -111h-98zM808 0h-154l-89 189l-75 -65l-27 -124h-127l147 667h127l-87 -391l224 207h162l-240 -219z" />
    <glyph glyph-name=".notdef" horiz-adv-x="483" 
d="M437 -90h-351v842h351v-842zM405 -60v782h-288v-782h288z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="CR" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="256" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="256" 
d="M177 217h-112l79 450h153zM82 -12q-31 0 -52.5 22t-21.5 53q0 36 26.5 62t62.5 26q31 0 52 -22t21 -53q0 -36 -26 -62t-62 -26z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="406" 
d="M169 375h-56q8 201 9 221q0 34 23 57.5t56 23.5q25 0 43 -18t18 -45q0 -9 -7 -30zM351 375h-56q8 201 9 221q1 35 23.5 58t55.5 23q25 0 43 -18t18 -45q0 -16 -7 -30z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="618" 
d="M285 0h-95l95 169h-95l-94 -169h-94l95 169h-97l44 81h97l92 166h-97l42 80h100l94 171h94l-94 -171h94l94 171h95l-96 -171h98l-42 -80h-100l-92 -166h101l-43 -81h-103zM329 250l92 166h-94l-91 -166h93z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="614" 
d="M189 -100l22 98q-138 28 -203 114l88 104q51 -68 143 -93l35 157q-39 15 -65 28.5t-54.5 34.5t-43 50.5t-14.5 66.5q0 87 71.5 152t189.5 65h4l20 91h95l-23 -102q106 -25 169 -97l-88 -100q-41 47 -109 70l-32 -142q39 -15 66 -28.5t55.5 -35t43 -51t14.5 -66.5
q0 -96 -71.5 -162t-191.5 -66h-7l-19 -88h-95zM421 188q0 32 -61 59l-29 -135q44 3 67 25.5t23 50.5zM247 486q0 -29 60 -56l27 124q-37 -1 -62 -21t-25 -47z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="757" 
d="M151 0h-75l573 667h76zM530 -12q-67 0 -114 38.5t-47 100.5q0 85 54.5 139.5t130.5 54.5q67 0 114 -38.5t47 -100.5q0 -85 -54.5 -139.5t-130.5 -54.5zM532 66q38 0 65 30t27 78q0 29 -20.5 48.5t-51.5 19.5q-38 0 -64.5 -30t-26.5 -77q0 -30 20 -49.5t51 -19.5zM241 345
q-66 0 -112.5 38t-46.5 100q0 85 54.5 139.5t130.5 54.5q66 0 112.5 -38t46.5 -100q0 -85 -54 -139.5t-131 -54.5zM245 423q38 0 64.5 30t26.5 77q0 29 -20.5 48.5t-52.5 19.5q-38 0 -64.5 -30t-26.5 -77q0 -29 20.5 -48.5t52.5 -19.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="646" 
d="M566 0h-151l-28 37q-83 -49 -166 -49q-97 0 -156.5 44t-59.5 132t51 138t140 83q-17 56 -17 105q0 79 59 133t144 54q73 0 123.5 -36t50.5 -100q0 -34 -12.5 -62t-29.5 -45.5t-48.5 -35t-54.5 -26.5t-62 -23q14 -28 37 -68q30 -54 46 -81q55 60 90 122l91 -57
q-64 -91 -127 -151q35 -54 80 -114zM242 88q42 0 90 29q-40 58 -56 90q-37 70 -48 94q-84 -46 -84 -122q0 -44 28 -67.5t70 -23.5zM307 491q0 -30 11 -66q61 21 92.5 44t31.5 60q0 27 -15.5 41.5t-39.5 14.5q-31 0 -55.5 -25t-24.5 -69z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="225" 
d="M169 375h-56q8 201 9 221q0 34 23 57.5t56 23.5q25 0 43 -18t18 -45q0 -9 -7 -30z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="297" 
d="M195 -146l-92 -53q-80 119 -80 306q0 156 73 309.5t200 268.5l67 -68q-206 -248 -206 -545q0 -104 38 -218z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="297" 
d="M101 632l92 53q80 -122 80 -306q0 -156 -73.5 -309.5t-200.5 -268.5l-66 68q206 248 206 544q0 105 -38 219z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="351" 
d="M234 366h-65l31 114l-106 -48l-18 57l107 38l-85 60l43 50l79 -68l19 108h64l-31 -113l106 48l18 -58l-106 -39l85 -59l-45 -50l-78 68z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="504" 
d="M487 298h-180l-45 -203h-86l45 203h-180l17 79h180l43 195h87l-43 -195h180z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="256" 
d="M174 69q0 -56 -42 -113t-105 -87l-45 47q30 13 58.5 36t39.5 48h-11q-26 0 -43.5 17t-17.5 48q0 35 27 60.5t61 25.5q33 0 55.5 -21.5t22.5 -60.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="300" 
d="M257 188h-240l24 108h240z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="256" 
d="M82 -12q-31 0 -52.5 22t-21.5 53q0 36 26.5 62t62.5 26q31 0 52 -22t21 -53q0 -36 -26 -62t-62 -26z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="329" 
d="M35 -20h-95l393 707h96z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="620" 
d="M284 -12q-110 0 -175.5 71t-65.5 198q0 108 41 204t118.5 156t172.5 60q110 0 174.5 -70.5t64.5 -197.5q0 -109 -40.5 -204.5t-117.5 -156t-172 -60.5zM297 114q52 0 93.5 49t61 117t19.5 136q0 62 -27 98.5t-83 36.5q-77 0 -125.5 -96.5t-48.5 -204.5q0 -63 27 -99.5
t83 -36.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="411" 
d="M291 0h-142l106 484l-135 -113l-63 85l257 211h124z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="600" 
d="M495 0h-502l29 132q99 56 154.5 89t116 72.5t90 66.5t48.5 56.5t19 57.5q0 36 -33.5 57t-86.5 21q-95 0 -166 -60l-60 102q44 38 107 60.5t129 22.5q109 0 184 -51.5t75 -142.5q0 -94 -89.5 -180.5t-259.5 -177.5h272z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="587" 
d="M279 -12q-94 0 -174.5 37t-117.5 102l95 86q30 -47 85 -73t110 -26q57 0 88 25.5t31 66.5q0 70 -122 70q-59 0 -81 -2l29 128q11 -1 93 -1q130 0 130 77q0 33 -34.5 53t-96.5 20q-93 0 -162 -54l-51 97q91 83 227 83q121 0 192.5 -46t71.5 -128q0 -64 -55 -110t-125 -55
q124 -31 124 -146q0 -88 -74.5 -146t-182.5 -58z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="597" 
d="M432 0h-142l30 138h-319l28 124l346 405h204l-89 -404h85l-27 -125h-86zM347 263l62 278l-242 -278h180z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="608" 
d="M296 -12q-191 0 -278 128l101 90q57 -92 179 -92q57 0 91.5 31t34.5 77q0 43 -34.5 66.5t-91.5 23.5q-69 0 -122 -40l-92 37l79 358h458l-27 -125h-317l-34 -157q54 43 133 43q84 0 140.5 -50.5t56.5 -138.5q0 -105 -78 -178t-199 -73z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="608" 
d="M301 -12q-123 0 -192.5 68t-69.5 190q0 111 40.5 207.5t124 160t193.5 63.5q141 0 224 -91l-88 -100q-50 65 -144 65q-68 0 -117 -48t-70 -116q-3 -6 -9 -26q28 29 75.5 49.5t97.5 20.5q91 0 151 -50t60 -134q0 -107 -81 -183t-195 -76zM301 114q54 0 91 33.5t37 81.5
q0 40 -37 64t-92 24q-67 0 -121 -51q-1 -7 -1 -34q0 -54 34.5 -86t88.5 -32z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="545" 
d="M225 0h-163l356 542h-328l27 125h501l-22 -99z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="606" 
d="M285 -12q-114 0 -192.5 45.5t-78.5 133.5q0 66 49 117.5t136 73.5q-43 19 -71.5 54.5t-28.5 79.5q0 62 40.5 105t98 61.5t123.5 18.5q105 0 182.5 -44.5t77.5 -126.5q0 -65 -47.5 -112.5t-125.5 -62.5q50 -26 80.5 -65.5t30.5 -85.5q0 -88 -81 -140t-193 -52zM337 399
q59 4 95 25.5t36 56.5q0 31 -33.5 52t-82.5 21q-46 0 -78.5 -21t-32.5 -56q0 -26 29.5 -48t66.5 -30zM291 110q50 0 87 23.5t37 59.5q0 31 -31.5 54.5t-73.5 33.5q-62 -3 -103.5 -28t-41.5 -64q0 -38 34.5 -58.5t91.5 -20.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="608" 
d="M345 678q123 0 192.5 -68t69.5 -190q0 -82 -24 -158.5t-68 -137.5t-113.5 -98t-153.5 -37q-140 0 -222 90l87 101q47 -65 145 -65q68 0 116 47.5t71 115.5q7 22 8 27q-29 -30 -76 -50.5t-97 -20.5q-91 0 -151.5 50.5t-60.5 134.5q0 106 82 182.5t195 76.5zM345 552
q-54 0 -91 -33.5t-37 -81.5q0 -40 36.5 -64t91.5 -24q69 0 122 50q1 7 1 34q0 54 -34.5 86.5t-88.5 32.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="256" 
d="M82 -12q-31 0 -52.5 22t-21.5 53q0 36 26.5 62t62.5 26q31 0 52 -22t21 -53q0 -36 -26 -62t-62 -26zM156 328q-31 0 -52 22t-21 53q0 36 26 61.5t62 25.5q31 0 52.5 -21.5t21.5 -52.5q0 -36 -26.5 -62t-62.5 -26z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="256" 
d="M174 69q0 -56 -42 -113t-105 -87l-45 47q30 13 58.5 36t39.5 48h-11q-26 0 -43.5 17t-17.5 48q0 35 27 60.5t61 25.5q33 0 55.5 -21.5t22.5 -60.5zM156 328q-31 0 -52 22t-21 53q0 36 26 61.5t62 25.5q31 0 52.5 -21.5t21.5 -52.5q0 -36 -26.5 -62t-62.5 -26z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="504" 
d="M440 90l-402 199l21 91l490 200l-22 -98l-388 -152l321 -150z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="504" 
d="M507 388h-446l17 80h446zM466 201h-447l18 78h446z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="504" 
d="M485 289l-491 -199l22 98l389 150l-322 152l20 90l402 -200z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="456" 
d="M285 236l-116 -32q-17 31 -17 70q0 50 30.5 85.5t66.5 52t66.5 39t30.5 48.5q0 53 -81 53q-69 0 -122 -48l-60 98q89 75 203 75q90 0 150.5 -40t60.5 -108q0 -49 -23 -85t-55.5 -56t-65.5 -37t-56 -38t-23 -49q0 -17 11 -28zM179 -12q-30 0 -51.5 22t-21.5 53
q0 36 26.5 62t62.5 26q31 0 52 -22t21 -53q0 -36 -26 -62t-63 -26z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="783" 
d="M372 -70q-136 0 -226.5 90.5t-90.5 223.5q0 104 55.5 195.5t147.5 145t194 53.5q140 0 227.5 -89t87.5 -217q0 -116 -62 -181.5t-136 -65.5q-42 0 -67 21.5t-30 52.5l-1 6q-26 -37 -65 -58.5t-79 -21.5q-71 0 -113 47t-42 121q0 99 69 172t157 73q43 0 75.5 -18.5
t49.5 -48.5l11 53h113l-51 -244q-3 -18 -3 -24q0 -45 37 -45q35 0 64.5 41.5t29.5 119.5q0 119 -75.5 193t-200.5 74q-142 0 -248 -106.5t-106 -246.5q0 -118 80.5 -198t201.5 -80q96 0 183 55l21 -30q-98 -63 -208 -63zM371 175q63 0 111 65l25 117q-25 51 -81 51
q-58 0 -98 -44t-40 -101q0 -39 22.5 -63.5t60.5 -24.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="663" 
d="M376 0h-364l147 667h324q84 0 132.5 -46.5t48.5 -113.5t-45 -117.5t-107 -56.5q45 -12 70.5 -50.5t25.5 -84.5q0 -79 -58 -138.5t-174 -59.5zM422 400q47 0 68.5 24.5t21.5 58.5q0 26 -19.5 44t-48.5 18h-169l-32 -145h179zM365 123q46 0 73.5 25t27.5 62q0 30 -20 49
t-53 19h-178l-34 -155h184z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="687" 
d="M372 -12q-141 0 -234.5 82.5t-93.5 216.5q0 172 114 281.5t274 109.5q115 0 189.5 -53t106.5 -136l-137 -46q-19 52 -63.5 80.5t-102.5 28.5q-96 0 -164.5 -74t-68.5 -182q0 -79 51.5 -130.5t136.5 -51.5q43 0 86 21t71 56l109 -74q-53 -68 -126.5 -98.5t-147.5 -30.5z
" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="718" 
d="M274 0h-262l147 667h258q116 0 202.5 -81t86.5 -206q0 -54 -14.5 -106.5t-48 -102.5t-82 -87.5t-122.5 -60.5t-165 -23zM295 125h5q117 0 187.5 71.5t70.5 174.5q0 73 -47 122t-121 49h-116l-92 -417h113z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="583" 
d="M484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="571" 
d="M154 0h-142l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="720" 
d="M372 -12q-140 0 -234 82t-94 217q0 169 114 280t287 111q100 0 171 -47t104 -118l-131 -56q-17 42 -62.5 68.5t-101.5 26.5q-95 0 -164 -74t-69 -182q0 -78 51 -130t137 -52q73 0 133 51l18 80h-177l28 125h319l-58 -260q-103 -122 -271 -122z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="731" 
d="M612 0h-143l62 280h-315l-62 -280h-142l147 667h142l-57 -262h314l58 262h143z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="274" 
d="M154 0h-142l147 667h142z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="486" 
d="M165 -12q-59 0 -111 19t-83 56l77 106q36 -55 107 -55q93 0 118 107l98 446h142l-98 -448q-26 -119 -86.5 -175t-163.5 -56z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="636" 
d="M583 0h-167l-147 273l-69 -68l-46 -205h-142l147 667h142l-63 -286l291 286h183l-334 -313z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="523" 
d="M436 0h-424l147 667h142l-119 -542h281z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="855" 
d="M735 0h-143l106 481l-294 -481h-62l-82 481l-106 -481h-142l147 667h194l69 -417l255 417h205z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="729" 
d="M609 0h-137l-218 450l-100 -450h-142l147 667h146l212 -434l97 434h142z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="624" 
d="M154 0h-142l147 667h293q90 0 144 -53.5t54 -131.5q0 -40 -14.5 -80.5t-44.5 -78.5t-86.5 -62t-130.5 -24h-167zM234 362h149q52 0 85 30t33 75q0 33 -23 54t-60 21h-144z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -95 -40 -176.5t-110 -135.5l35 -54l-114 -55l-32 49q-60 -20 -126 -20zM380 114q23 0 48 5l-57 92l113 55l53 -84q74 76 74 188q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5
t-67 -181.5q0 -83 53 -132.5t135 -49.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="643" 
d="M552 0h-157l-79 237h-110l-52 -237h-142l147 667h285q84 0 145 -52t61 -136q0 -89 -53.5 -153t-136.5 -79zM381 362h2q55 0 86.5 28.5t31.5 74.5q0 33 -25 55t-59 22h-143l-40 -180h147z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="600" 
d="M301 -12q-97 0 -178 34t-124 90l88 104q35 -47 96 -75.5t129 -28.5q49 0 74.5 23.5t25.5 52.5q0 25 -33.5 44.5t-81 36.5t-95 38t-81 60t-33.5 93q0 87 71.5 152t189.5 65q80 0 150.5 -28.5t114.5 -79.5l-88 -100q-36 41 -90.5 63t-108.5 22q-38 0 -63.5 -20t-25.5 -48
q0 -23 33.5 -42t81.5 -35.5t96 -38t81.5 -61t33.5 -93.5q0 -96 -71.5 -162t-191.5 -66z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="582" 
d="M309 0h-143l119 542h-195l28 125h532l-27 -125h-195z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="733" 
d="M356 -12q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-29 -133 -103 -206t-213 -73z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="684" 
d="M377 0h-178l-111 667h155l72 -513l297 513h169z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="918" 
d="M673 0h-151l-16 458l-217 -458h-151l-44 667h156l10 -482l233 482h111l20 -482l223 482h163z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="668" 
d="M617 0h-161l-121 237l-214 -237h-178l321 351l-159 316h161l109 -221l195 221h179l-303 -333z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="645" 
d="M340 0h-142l60 273l-170 394h154l107 -268l223 268h170l-342 -394z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="592" 
d="M496 0h-508l25 115l401 427h-306l27 125h501l-24 -114l-403 -428h315z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="280" 
d="M166 -190h-216l193 868h216l-19 -85h-124l-155 -698h124z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="329" 
d="M178 -20l-80 707h92l80 -707h-92z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="280" 
d="M136 -190h-215l19 85h124l155 698h-124l18 85h216z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="441" 
d="M441 333h-89l-53 247l-162 -247h-99l230 334h93z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="564" 
d="M484 -125h-569l20 85h569z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="251" 
d="M320 556h-89l-130 144h117z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="586" 
d="M304 -12q-103 0 -154 76l-14 -64h-127l147 667h127l-54 -244q61 72 146 72q83 0 133 -53t50 -147q0 -75 -28.5 -144t-88 -116t-137.5 -47zM283 101q62 0 102 49.5t40 118.5q0 51 -30 82t-77 31q-64 0 -111 -58l-37 -168q36 -55 113 -55z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="499" 
d="M265 -12q-108 0 -172.5 60.5t-64.5 159.5q0 120 81 203.5t200 83.5q139 0 196 -95l-95 -73q-32 55 -101 55q-67 0 -109.5 -49t-42.5 -119q0 -53 34.5 -83t86.5 -30q60 0 101 48l69 -85q-72 -76 -183 -76z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l54 248h128l-148 -667h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="553" 
d="M278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="327" 
d="M165 0h-127l82 373h-80l25 110h79l6 27q17 75 63 121t123 46q81 0 127 -47l-63 -74q-21 21 -54 21q-54 0 -69 -69l-6 -25h99l-25 -110h-98z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="585" 
d="M212 -196q-156 0 -233 84l74 85q23 -31 65.5 -48t91.5 -17q109 0 137 125l10 44q-60 -73 -148 -73q-79 0 -130.5 48.5t-51.5 147.5q0 117 69 206t181 89q44 0 87 -20.5t69 -55.5l14 64h128l-100 -450q-14 -66 -42.5 -112.5t-65.5 -71t-75 -35t-80 -10.5zM269 117
q63 0 109 55l34 150q-36 60 -118 60q-59 0 -97 -45.5t-38 -111.5q0 -49 30 -78.5t80 -29.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="580" 
d="M463 0h-127l63 290q5 20 5 30q0 62 -83 62q-56 0 -113 -57l-72 -325h-127l147 667h127l-54 -247q77 75 161 75q69 0 110 -33.5t41 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="253" 
d="M198 535q-29 0 -48.5 19t-19.5 46q0 39 26.5 62.5t58.5 23.5q30 0 49.5 -19t19.5 -46q0 -39 -27 -62.5t-59 -23.5zM136 0h-127l107 483h127z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="253" 
d="M-54 -196q-90 0 -134 41l53 91q22 -28 63 -28q59 0 77 79l111 496h127l-111 -496q-19 -88 -63 -135.5t-123 -47.5zM198 535q-29 0 -48.5 19t-19.5 46q0 39 26.5 62.5t58.5 23.5q30 0 49.5 -19t19.5 -46q0 -39 -27 -62.5t-59 -23.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="535" 
d="M481 0h-154l-89 189l-75 -65l-27 -124h-127l147 667h127l-87 -391l224 207h162l-240 -219z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="253" 
d="M136 0h-127l147 667h127z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="852" 
d="M736 0h-128l65 292q4 24 4 32q0 27 -20 42.5t-49 15.5q-51 0 -101 -56l-71 -326h-128l65 292q5 25 5 35q-1 24 -19.5 39.5t-51.5 15.5q-49 0 -99 -56l-72 -326h-127l107 483h127l-14 -63q70 75 147 75q33 0 60.5 -11t42 -26t22.5 -28.5t8 -21.5v-2q72 89 167 89
q58 0 98 -33t40 -90q0 -20 -5 -40z" />
    <glyph glyph-name="n" unicode="n" 
d="M462 0h-127l64 288q5 22 5 32q0 32 -21 47t-53 15q-64 0 -122 -56l-72 -326h-127l107 483h127l-14 -63q77 75 161 75q69 0 110 -33.5t41 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="574" 
d="M266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="583" 
d="M304 -12q-103 0 -154 76l-55 -248h-127l148 667h127l-14 -60q61 72 146 72q83 0 133 -53t50 -147q0 -75 -28.5 -144t-88 -116t-137.5 -47zM283 101q62 0 102 49.5t40 118.5q0 51 -30 82t-77 31q-64 0 -111 -58l-37 -168q36 -55 113 -55z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="583" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-148 -667h-128l55 244q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="360" 
d="M136 0h-127l107 483h127l-15 -63q70 75 172 75l-28 -126q-24 6 -46 6q-69 0 -120 -58z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="477" 
d="M219 -12q-67 0 -127.5 22.5t-100.5 64.5l71 85q23 -28 71.5 -52t97.5 -24q31 0 50.5 15.5t19.5 35.5q0 23 -38.5 40.5t-84 30t-84 45t-38.5 81.5q0 67 53.5 115t145.5 48q61 0 115.5 -21.5t89.5 -57.5l-65 -81q-19 24 -59.5 43.5t-82.5 19.5q-32 0 -51 -13.5t-19 -33.5
q0 -16 25 -29t61 -24t72.5 -26t61.5 -44.5t25 -70.5q0 -72 -56.5 -120.5t-152.5 -48.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="338" 
d="M203 -12q-67 0 -105.5 26.5t-38.5 78.5q0 17 4 33l54 247h-80l25 110h80l29 132h128l-29 -132h98l-26 -110h-98l-46 -210q-2 -12 -2 -20q0 -42 47 -42q23 0 35 10l5 -102q-31 -21 -80 -21z" />
    <glyph glyph-name="u" unicode="u" 
d="M115 483h128l-65 -291q-4 -18 -4 -29q0 -31 22.5 -46.5t58.5 -15.5q56 0 115 57l72 325h127l-107 -483h-127l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="513" 
d="M271 0h-137l-87 483h132l51 -336l202 336h140z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="769" 
d="M568 0h-135l-30 329l-174 -329h-136l-40 483h127l19 -325l178 325h113l33 -325l163 325h136z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="504" 
d="M450 0h-138l-74 163l-147 -163h-145l225 248l-108 235h137l67 -149l133 149h146l-215 -235z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="513" 
d="M-48 -184l42 110q18 -8 49 -8q37 0 60 34l30 45l-86 486h132l51 -336l202 336h140l-352 -562q-39 -64 -82.5 -90.5t-104.5 -26.5q-46 0 -81 12z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="479" 
d="M382 0h-394l21 95l275 277h-213l24 111h390l-20 -92l-279 -280h220z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="288" 
d="M174 -190h-45q-57 0 -98 36t-41 92q0 18 4 31l39 172q2 12 2 18q0 21 -11 35t-29 14l16 72q53 0 67 67l38 171q18 80 67.5 120t122.5 40h61l-19 -85h-60q-63 0 -80 -74l-43 -195q-18 -74 -66 -89q29 -17 29 -56q0 -18 -4 -34l-38 -175q-2 -12 -2 -19q0 -25 15.5 -40.5
t39.5 -15.5h55z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="216" 
d="M171 -20h-85v707h85v-707z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="288" 
d="M113 678h45q57 0 98 -36t41 -92q0 -19 -4 -31l-38 -172q-2 -12 -2 -18q0 -22 10.5 -35.5t28.5 -13.5l-16 -72q-53 0 -67 -67l-38 -171q-18 -80 -67.5 -120t-122.5 -40h-61l19 85h60q63 0 80 74l44 195q16 74 65 90q-29 15 -29 55q0 18 4 34l38 175q2 12 2 19
q0 25 -15.5 40.5t-39.5 15.5h-55z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="510" 
d="M491 667l84 -10q-53 -245 -189 -245q-49 0 -73 27.5t-26 60.5t-10 60.5t-28 27.5q-29 0 -57 -52.5t-42 -123.5l-85 10q50 244 189 244q40 0 63.5 -18t30 -44t9.5 -52.5t10.5 -44.5t24.5 -18q29 0 57 53.5t42 124.5z" />
    <glyph glyph-name="nbspace" unicode="&#xa0;" horiz-adv-x="256" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="256" 
d="M76 265h112l-78 -449h-154zM171 495q31 0 52.5 -22t21.5 -54q0 -35 -26.5 -61t-62.5 -26q-31 0 -52 22t-21 53q0 36 26.5 62t61.5 26z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="499" 
d="M163 -100l22 98q-74 20 -115.5 75.5t-41.5 134.5q0 116 76.5 199t190.5 88l16 70h93l-18 -80q80 -21 119 -85l-95 -73q-20 31 -50 44l-60 -268q45 7 79 46l69 -85q-68 -72 -172 -76l-20 -88h-93zM157 214q0 -68 54 -97l58 260q-51 -14 -81.5 -59t-30.5 -104z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="550" 
d="M28 269l18 81h78q-33 68 -33 110q0 90 77 153.5t185 63.5q87 0 151.5 -35.5t84.5 -98.5l-129 -53q-7 33 -34.5 52t-65.5 19q-48 0 -81 -32.5t-33 -83.5q0 -14 3 -27.5t6.5 -22.5t10 -23t9.5 -22h142l-19 -81h-113q-7 -41 -35 -77.5t-58 -52.5q21 7 44 7q30 0 74 -16.5
t70 -16.5q46 0 81 37l31 -113q-48 -50 -123 -50q-46 0 -118.5 22t-101.5 22q-47 0 -111 -38l-27 97q61 27 100.5 72.5t39.5 94.5q0 8 -1 12h-122z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="623" 
d="M278 90q-65 7 -112 37l-98 -79l-66 83l92 74q-36 70 -16 155q16 73 64 126l-56 74l85 68l59 -77q69 33 154 24q66 -7 115 -39l95 77l66 -79l-92 -76q36 -68 17 -153q-17 -75 -66 -128l56 -69l-85 -68l-59 73q-70 -32 -153 -23zM302 202q59 -6 101.5 27.5t55.5 89.5
q11 55 -15 96t-84 47q-59 7 -101.5 -26.5t-53.5 -88.5q-13 -56 12.5 -97.5t84.5 -47.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="645" 
d="M341 0h-142l25 115h-230l17 78h230l18 80h-229l17 79h178l-136 315h154l107 -268l223 268h170l-274 -315h178l-18 -79h-228l-18 -80h228l-18 -78h-227z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="216" 
d="M171 -20h-85v316h85v-316zM171 371h-85v316h85v-316z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="484" 
d="M466 333q0 -50 -31.5 -90t-83.5 -58q60 -33 60 -99q0 -72 -57 -119.5t-147 -47.5q-154 0 -239 83l71 76q28 -32 73.5 -51.5t93.5 -19.5q35 0 56.5 17.5t21.5 42.5q0 22 -25 37.5t-60 27.5t-70.5 27t-60.5 44t-25 70q0 50 32.5 87t95.5 54q-73 32 -73 106q0 68 55.5 112.5
t142.5 44.5q138 0 213 -73l-66 -70q-23 25 -62 39.5t-80 14.5q-37 0 -57.5 -15.5t-20.5 -38.5q0 -21 25 -35.5t60.5 -26t71.5 -26t61 -43.5t25 -70zM335 304q0 44 -69 68q-47 -6 -71 -27t-24 -49q0 -25 22.5 -41t64.5 -30q39 11 58 32.5t19 46.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="295" 
d="M402 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM191 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="778" 
d="M754 334q0 -143 -101.5 -244t-244.5 -101q-142 0 -243 101t-101 244t100.5 244t243.5 101t244.5 -101t101.5 -244zM718 334q0 127 -91 218t-219 91q-127 0 -217.5 -91t-90.5 -218t90.5 -218t217.5 -91t218.5 91t91.5 218zM507 211l24 -34q-65 -54 -139 -54
q-86 0 -138.5 52t-52.5 132q0 100 70.5 169t162.5 69q111 0 160 -80l-38 -27q-37 67 -124 67q-71 0 -128 -57t-57 -140q0 -60 40 -102.5t106 -42.5q64 0 114 48z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="414" 
d="M269 575q-41 0 -68.5 -33.5t-27.5 -79.5q0 -35 18.5 -53t49.5 -18q38 0 70 34l27 122q-22 28 -69 28zM358 640h93l-70 -314h-93l8 39q-40 -47 -94 -47q-53 0 -89 34.5t-36 97.5q0 83 49 140.5t120 57.5q70 0 104 -47z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="527" 
d="M262 63h-113l-120 180l200 177h121l-204 -182zM452 63h-113l-120 180l200 177h121l-204 -182z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="515" 
d="M524 468l-58 -267h-82l42 187h-365l17 80h446z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="300" 
d="M257 188h-240l24 108h240z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="494" 
d="M508 465q0 -88 -62.5 -150t-150.5 -62t-149.5 62t-61.5 150t62 150t150 62t150 -61.5t62 -150.5zM476 465q0 75 -53 127.5t-128 52.5t-127 -52.5t-52 -127.5t52 -127.5t127 -52.5q74 0 127.5 53t53.5 127zM369 343h-40l-42 96h-44l-21 -96h-34l53 243h92q31 0 51.5 -18
t20.5 -46q0 -34 -25 -58t-55 -24zM369 519q0 37 -43 37h-57l-20 -86h65q24 0 39.5 14t15.5 35z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="363" 
d="M432 562h-362l16 72h362z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="311" 
d="M356 541q0 -56 -40 -95t-96 -39t-95 39t-39 95t39.5 96t94.5 40q56 0 96 -40t40 -96zM287 541q0 28 -19.5 47.5t-47.5 19.5q-27 0 -45.5 -19.5t-18.5 -47.5q0 -27 18 -46t45 -19q28 0 48 19t20 46z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="504" 
d="M493 326h-180l-45 -204h-87l45 204h-180l18 78h180l44 197h86l-44 -197h180zM421 0h-446l17 78h446z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="397" 
d="M399 421h-314l17 74q155 90 207 130.5t52 76.5q0 23 -19 35.5t-48 12.5q-69 0 -112 -43l-40 64q62 56 155 56q71 0 117 -31t46 -85q0 -53 -53 -102t-164 -111h173z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="397" 
d="M428 533q0 -47 -43 -83t-115 -36q-61 0 -108.5 19.5t-70.5 53.5l60 59q39 -55 114 -55q33 0 50 15t17 38q0 20 -24 31.5t-65 11.5q-27 0 -33 -1l16 76q22 -2 65 -2q72 0 72 47q0 21 -23 32t-57 11q-60 0 -103 -36l-33 60q63 53 145 53q76 0 122 -28.5t46 -78.5
q0 -39 -29.5 -68t-80.5 -33q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="251" 
d="M352 700l-194 -144h-91l166 144h119z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="591" 
d="M138 15l-45 -199h-128l152 667h127l-66 -292q-10 -45 9.5 -68t59.5 -23q65 0 121 56l72 327h128l-75 -338q-9 -44 31 -44q8 0 20 2l-16 -108q-30 -7 -61 -7q-105 0 -110 86q-70 -86 -155 -86q-49 0 -64 27z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="449" 
d="M326 -100h-63l157 705h-79l-156 -705h-63l94 423q-57 0 -97 44.5t-40 111.5q0 73 56.5 130.5t137.5 57.5h223z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="256" 
d="M121 164q-31 0 -52.5 22t-21.5 52q0 37 26 62.5t63 25.5q30 0 51.5 -22t21.5 -53q0 -35 -26.5 -61t-61.5 -26z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="221" 
d="M30 -196q-78 0 -119 41l35 48q37 -35 84 -35q50 0 50 35q0 24 -30 24q-17 0 -29 -14l-40 26l48 82h63l-39 -62q14 10 31 10q26 0 42.5 -16.5t16.5 -45.5q0 -42 -32 -67.5t-81 -25.5z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="286" 
d="M265 421h-97l60 273l-81 -66l-43 59l166 134h83z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="405" 
d="M238 318q-73 0 -117.5 40.5t-44.5 105.5q0 77 57 130.5t133 53.5q74 0 118 -40.5t44 -105.5q0 -78 -56.5 -131t-133.5 -53zM241 392q43 0 67.5 33.5t24.5 77.5q0 32 -18.5 50.5t-50.5 18.5q-43 0 -68 -34t-25 -77q0 -32 19 -50.5t51 -18.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="527" 
d="M77 420h113l120 -180l-200 -177h-121l204 182zM267 420h113l120 -180l-200 -177h-121l204 182z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="833" 
d="M231 267h-97l60 273l-81 -66l-43 59l166 134h83zM773 86h-53l-19 -86h-95l19 86h-197l15 65l213 249h133l-52 -239h52zM641 161l34 154l-134 -154h100zM734 667l-574 -667h-76l573 667h77z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="867" 
d="M231 267h-97l60 273l-81 -66l-43 59l166 134h83zM775 0h-314l17 74q155 90 207 130.5t52 76.5q0 23 -19 35.5t-48 12.5q-69 0 -112 -43l-40 64q62 56 155 56q71 0 117 -31t46 -85q0 -53 -53 -102t-164 -111h173zM734 667l-574 -667h-76l573 667h77z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="921" 
d="M861 86h-53l-19 -86h-95l19 86h-197l15 65l213 249h133l-52 -239h52zM729 161l34 154l-134 -154h100zM821 667l-574 -667h-76l573 667h77zM394 379q0 -47 -43 -83t-115 -36q-61 0 -108.5 19.5t-70.5 53.5l60 59q39 -55 114 -55q33 0 50 15t17 38q0 20 -24 31.5t-65 11.5
q-27 0 -33 -1l16 76q22 -2 65 -2q72 0 72 47q0 21 -23 32t-57 11q-60 0 -103 -36l-33 60q63 53 145 53q76 0 122 -28.5t46 -78.5q0 -39 -29.5 -68t-80.5 -33q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="398" 
d="M169 247l117 31q16 -29 16 -70q0 -50 -30.5 -85.5t-66.5 -52t-66.5 -39t-30.5 -48.5q0 -53 82 -53q70 0 121 48l60 -98q-89 -75 -203 -75q-90 0 -150.5 40t-60.5 108q0 49 23 85t55.5 56t65.5 37t56 38t23 49q0 17 -11 29zM275 494q31 0 52 -22t21 -53q0 -36 -26 -62
t-63 -26q-30 0 -51.5 22t-21.5 53q0 36 26.5 62t62.5 26z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM511 723h-89l-130 144h117z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM667 867l-194 -144h-91l166 144h119z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="684" 
d="M597 723h-76l-54 89l-90 -89h-80l124 144h114zM634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM517 721q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z
" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="684" 
d="M633 785q0 -29 -23 -52t-53 -23q-25 0 -42.5 17t-17.5 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM422 785q0 -29 -23 -52t-53 -23q-26 0 -43 17t-17 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238
l-40 287l-167 -287h207z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM452 688q-42 0 -68.5 26.5t-26.5 69.5q0 46 34.5 80.5t80.5 34.5q43 0 69 -26.5t26 -69.5q0 -47 -34 -81t-81 -34zM458 747q20 0 35 15t15 35q0 19 -11 31t-30 12q-20 0 -35.5 -15
t-15.5 -35q0 -19 11.5 -31t30.5 -12z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="968" 
d="M872 0h-472l25 113h-231l-91 -113h-166l553 667h529l-28 -125h-329l-31 -141h323l-28 -125h-322l-34 -151h329zM453 238l61 280l-224 -280h163z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="687" 
d="M303 -196q-78 0 -119 41l35 48q37 -35 84 -35q50 0 50 35q0 24 -30 24q-17 0 -29 -14l-40 26l39 67q-111 21 -180 99t-69 192q0 172 114 281.5t274 109.5q115 0 189.5 -53t106.5 -136l-137 -46q-19 52 -63.5 80.5t-102.5 28.5q-96 0 -164.5 -74t-68.5 -182
q0 -79 51.5 -130.5t136.5 -51.5q43 0 86 21t71 56l109 -74q-53 -68 -126.5 -98.5t-147.5 -30.5q-14 0 -21 1l-25 -40q14 10 31 10q25 0 42 -16.5t17 -45.5q0 -42 -32 -67.5t-81 -25.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="583" 
d="M484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329zM472 723h-89l-130 144h117z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="583" 
d="M627 867l-194 -144h-91l166 144h119zM484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="583" 
d="M557 723h-76l-54 89l-90 -89h-80l124 144h114zM484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="583" 
d="M593 785q0 -29 -23 -52t-53 -23q-25 0 -42.5 17t-17.5 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM382 785q0 -29 -23 -52t-53 -23q-26 0 -43 17t-17 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322
l-33 -151h329z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="274" 
d="M154 0h-142l147 667h142zM307 723h-89l-130 144h117z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="274" 
d="M154 0h-142l147 667h142zM462 867l-194 -144h-91l166 144h119z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="274" 
d="M392 723h-76l-54 89l-90 -89h-80l124 144h114zM154 0h-142l147 667h142z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="274" 
d="M428 785q0 -29 -23 -52t-53 -23q-26 0 -43 17t-17 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM217 785q0 -29 -23 -52t-53 -23q-25 0 -42.5 17t-17.5 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM154 0h-142l147 667h142z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="742" 
d="M299 0h-262l61 276h-83l26 115h82l61 276h258q116 0 202.5 -81t86.5 -206q0 -54 -14.5 -106.5t-48 -102.5t-82 -87.5t-122.5 -60.5t-165 -23zM327 125q116 1 186 72t70 174q0 73 -47 122t-121 49h-116l-33 -151h147l-25 -115h-148l-33 -151h120z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="729" 
d="M540 721q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5zM609 0h-137l-218 450l-100 -450h-142l147 667h146l212 -434l97 434h142z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="765" 
d="M554 723h-89l-130 144h117zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5
z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="765" 
d="M708 867l-194 -144h-91l166 144h119zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5
t135 -49.5z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="765" 
d="M639 723h-76l-54 89l-90 -89h-80l124 144h114zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5
q0 -83 53 -132.5t135 -49.5z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5zM559 721q-30 0 -50 17
t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="765" 
d="M674 785q0 -29 -23 -52t-53 -23q-26 0 -43 17t-17 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM463 785q0 -29 -23 -52t-53 -23q-25 0 -42.5 17t-17.5 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5
q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="504" 
d="M372 140l-108 138l-174 -143l-49 60l176 144l-108 138l63 52l106 -139l176 143l49 -60l-176 -145l107 -138z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="765" 
d="M372 -12q-122 0 -208 60l-47 -48h-102l95 97q-66 77 -66 190q0 164 112 277.5t276 113.5q112 0 197 -54l42 43h102l-88 -90q74 -80 74 -197q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 58 -29 103l-319 -325q48 -34 117 -34zM192 296
q0 -52 22 -93l316 320q-46 29 -107 29q-97 0 -164 -74.5t-67 -181.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="733" 
d="M356 -12q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-29 -133 -103 -206t-213 -73zM536 723h-89l-130 144h117z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="733" 
d="M691 867l-194 -144h-91l166 144h119zM356 -12q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-29 -133 -103 -206t-213 -73z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="733" 
d="M621 723h-76l-54 89l-90 -89h-80l124 144h114zM356 -12q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-29 -133 -103 -206t-213 -73z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="733" 
d="M657 785q0 -29 -23 -52t-53 -23q-25 0 -42.5 17t-17.5 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM446 785q0 -29 -23 -52t-53 -23q-26 0 -43 17t-17 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM356 -12q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397
q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-29 -133 -103 -206t-213 -73z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="645" 
d="M340 0h-142l60 273l-170 394h154l107 -268l223 268h170l-342 -394zM648 867l-194 -144h-91l166 144h119z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="624" 
d="M154 0h-142l147 667h142l-23 -108h162q80 0 133 -54t53 -130q0 -44 -15.5 -85.5t-46.5 -78t-86 -59t-127 -22.5h-168zM359 255q55 0 87 30.5t32 73.5q0 33 -23.5 54t-59.5 21h-145l-39 -179h148z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="643" 
d="M610 156q0 -69 -57.5 -118.5t-146.5 -49.5q-135 0 -214 88l70 80q57 -69 147 -69q33 0 53 16t20 37q0 18 -22.5 30t-54.5 22t-64 23t-54.5 39.5t-22.5 63.5q0 32 12 58.5t30.5 41.5t40 29t40 23t30.5 21t12 26q0 23 -23 37.5t-57 14.5q-42 0 -70 -25.5t-38 -68.5
l-105 -475h-127l105 475q20 89 81 145.5t163 56.5q82 0 144.5 -39t62.5 -101q0 -37 -18 -65t-43.5 -44.5t-51 -29.5t-43.5 -28t-18 -32q0 -15 22.5 -26.5t55 -21.5t64.5 -24.5t54.5 -42.5t22.5 -67z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM421 556h-89l-130 144h117z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM576 700l-194 -144h-91l166 144h119
z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM507 556h-76l-54 89l-90 -89h-80
l124 144h114z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM428 554q-30 0 -50 17t-36 34
t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM542 618q0 -29 -23 -52t-53 -23
q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM331 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM365 529q-42 0 -68.5 26.5
t-26.5 69.5q0 46 34.5 80.5t80.5 34.5q43 0 69 -26.5t26 -69.5q0 -47 -34 -81t-81 -34zM371 588q20 0 35 15t15 35q0 19 -11 31t-30 12q-20 0 -35.5 -15t-15.5 -35q0 -19 11.5 -31t30.5 -12z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="914" 
d="M301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM487 82l-18 -82h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47q48 0 89 -20t65 -56l13 64h128l-13 -59q64 71 147 71q85 0 131 -53
t46 -154q0 -52 -11 -87h-357q-1 -2 -1 -12q0 -35 36 -66t100 -31q72 0 116 36l40 -90q-76 -50 -153 -50q-127 0 -170 94zM532 287h239q1 2 1 10q0 40 -28 68.5t-81 28.5q-49 0 -85.5 -32.5t-45.5 -74.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="499" 
d="M211 -196q-78 0 -119 41l35 48q37 -35 84 -35q50 0 50 35q0 24 -30 24q-17 0 -29 -14l-40 26l38 66q-80 17 -126 73.5t-46 139.5q0 120 81 203.5t200 83.5q139 0 196 -95l-95 -73q-32 55 -101 55q-67 0 -109.5 -49t-42.5 -119q0 -53 34.5 -83t86.5 -30q60 0 101 48
l69 -85q-72 -76 -183 -76h-7l-24 -39q14 10 31 10q25 0 42 -16.5t17 -45.5q0 -42 -32 -67.5t-81 -25.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="553" 
d="M278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5zM419 556h-89
l-130 144h117z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="553" 
d="M575 700l-194 -144h-91l166 144h119zM278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5
q-49 0 -85.5 -32.5t-45.5 -74.5z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="553" 
d="M504 556h-76l-54 89l-90 -89h-80l124 144h114zM278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5
q-49 0 -85.5 -32.5t-45.5 -74.5z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="553" 
d="M541 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM330 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85
q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="253" 
d="M136 0h-127l107 483h127zM260 556h-89l-130 144h117z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="253" 
d="M414 700l-194 -144h-91l166 144h119zM136 0h-127l107 483h127z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="253" 
d="M345 556h-76l-54 89l-90 -89h-80l124 144h114zM136 0h-127l107 483h127z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="253" 
d="M382 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM171 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM136 0h-127l107 483h127z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="574" 
d="M153 485l-15 58l124 37q-18 15 -54 43l90 93q65 -51 106 -94l109 32l15 -58l-80 -24q103 -127 103 -266q0 -134 -77.5 -226t-204.5 -92q-110 0 -177 57.5t-67 152.5q0 110 70.5 186t172.5 76q119 0 164 -109q-19 82 -118 181zM271 101q59 0 98 41.5t39 103.5
q0 42 -30.5 71.5t-87.5 29.5t-95.5 -41t-38.5 -101q0 -49 30 -76.5t85 -27.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" 
d="M462 0h-127l64 288q5 22 5 32q0 32 -21 47t-53 15q-64 0 -122 -56l-72 -326h-127l107 483h127l-14 -63q77 75 161 75q69 0 110 -33.5t41 -89.5q0 -15 -5 -40zM428 554q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5
t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="574" 
d="M266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30zM421 556h-89l-130 144h117z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="574" 
d="M574 700l-194 -144h-91l166 144h119zM266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="574" 
d="M506 556h-76l-54 89l-90 -89h-80l124 144h114zM266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83
t83 -30z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="574" 
d="M266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30zM428 554q-30 0 -50 17t-36 34t-35 17
q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="574" 
d="M542 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM331 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86
q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="511" 
d="M494 298h-453l17 79h453zM230 96q-23 0 -39 16.5t-16 39.5q0 27 19.5 46.5t46.5 19.5q23 0 39.5 -16.5t16.5 -39.5q0 -27 -20 -46.5t-47 -19.5zM309 454q-23 0 -39 16.5t-16 39.5q0 27 19.5 46.5t46.5 19.5q23 0 39 -16.5t16 -39.5q0 -28 -19.5 -47t-46.5 -19z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="574" 
d="M67 0h-83l82 76q-39 54 -39 131q0 116 80 202t199 86q97 0 158 -47l37 35h84l-80 -75q41 -55 41 -133q0 -116 -80 -201.5t-200 -85.5q-99 0 -161 48zM271 101q62 0 102.5 50t40.5 118q0 25 -8 47l-208 -194q27 -21 73 -21zM158 214q0 -27 7 -45l207 193q-28 20 -70 20
q-62 0 -103 -50.5t-41 -117.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" 
d="M115 483h128l-65 -291q-4 -18 -4 -29q0 -31 22.5 -46.5t58.5 -15.5q56 0 115 57l72 325h127l-107 -483h-127l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39zM423 556h-89l-130 144h117z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" 
d="M577 700l-194 -144h-91l166 144h119zM115 483h128l-65 -291q-4 -18 -4 -29q0 -31 22.5 -46.5t58.5 -15.5q56 0 115 57l72 325h127l-107 -483h-127l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" 
d="M508 556h-76l-54 89l-90 -89h-80l124 144h114zM115 483h128l-65 -291q-4 -18 -4 -29q0 -31 22.5 -46.5t58.5 -15.5q56 0 115 57l72 325h127l-107 -483h-127l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" 
d="M545 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM334 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM115 483h128l-65 -291q-4 -18 -4 -29q0 -31 22.5 -46.5
t58.5 -15.5q56 0 115 57l72 325h127l-107 -483h-127l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="513" 
d="M-48 -184l42 110q18 -8 49 -8q37 0 60 34l30 45l-86 486h132l51 -336l202 336h140l-352 -562q-39 -64 -82.5 -90.5t-104.5 -26.5q-46 0 -81 12zM541 700l-194 -144h-91l166 144h119z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="583" 
d="M304 -12q-103 0 -154 76l-55 -248h-127l188 851h127l-54 -244q61 72 146 72q83 0 133 -53t50 -147q0 -75 -28.5 -144t-88 -116t-137.5 -47zM283 101q62 0 102 49.5t40 118.5q0 51 -30 82t-77 31q-64 0 -111 -58l-37 -168q36 -55 113 -55z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="513" 
d="M-48 -184l42 110q18 -8 49 -8q37 0 60 34l30 45l-86 486h132l51 -336l202 336h140l-352 -562q-39 -64 -82.5 -90.5t-104.5 -26.5q-46 0 -81 12zM508 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM297 618q0 -29 -23 -52
t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM630 729h-362l16 72h362z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM540 562h-362l16 72h362z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM632 786q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM542 619q-85 -88 -194 -88
q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="684" 
d="M623 -99l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 65 60 110h-58l-17 113h-286l-67 -113h-169l405 667h178l110 -667q-93 -43 -93 -100q0 -33 35 -33q27 0 47 34zM451 238l-40 287l-167 -287h207z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="586" 
d="M458 -99l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 65 60 110h-32l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47q48 0 89 -20t65 -56l13 64h128l-107 -483q-93 -43 -93 -100q0 -33 35 -33q27 0 47 34zM301 382q-61 0 -101.5 -50
t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="687" 
d="M710 872l-194 -144h-91l166 144h119zM372 -12q-141 0 -234.5 82.5t-93.5 216.5q0 172 114 281.5t274 109.5q115 0 189.5 -53t106.5 -136l-137 -46q-19 52 -63.5 80.5t-102.5 28.5q-96 0 -164.5 -74t-68.5 -182q0 -79 51.5 -130.5t136.5 -51.5q43 0 86 21t71 56l109 -74
q-53 -68 -126.5 -98.5t-147.5 -30.5z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="499" 
d="M580 700l-194 -144h-91l166 144h119zM265 -12q-108 0 -172.5 60.5t-64.5 159.5q0 120 81 203.5t200 83.5q139 0 196 -95l-95 -73q-32 55 -101 55q-67 0 -109.5 -49t-42.5 -119q0 -53 34.5 -83t86.5 -30q60 0 101 48l69 -85q-72 -76 -183 -76z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="687" 
d="M639 723h-76l-54 89l-90 -89h-80l124 144h114zM372 -12q-141 0 -234.5 82.5t-93.5 216.5q0 172 114 281.5t274 109.5q115 0 189.5 -53t106.5 -136l-137 -46q-19 52 -63.5 80.5t-102.5 28.5q-96 0 -164.5 -74t-68.5 -182q0 -79 51.5 -130.5t136.5 -51.5q43 0 86 21t71 56
l109 -74q-53 -68 -126.5 -98.5t-147.5 -30.5z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="499" 
d="M510 556h-76l-54 89l-90 -89h-80l124 144h114zM265 -12q-108 0 -172.5 60.5t-64.5 159.5q0 120 81 203.5t200 83.5q139 0 196 -95l-95 -73q-32 55 -101 55q-67 0 -109.5 -49t-42.5 -119q0 -53 34.5 -83t86.5 -30q60 0 101 48l69 -85q-72 -76 -183 -76z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="687" 
d="M372 -12q-141 0 -234.5 82.5t-93.5 216.5q0 172 114 281.5t274 109.5q115 0 189.5 -53t106.5 -136l-137 -46q-19 52 -63.5 80.5t-102.5 28.5q-96 0 -164.5 -74t-68.5 -182q0 -79 51.5 -130.5t136.5 -51.5q43 0 86 21t71 56l109 -74q-53 -68 -126.5 -98.5t-147.5 -30.5z
M569 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="499" 
d="M265 -12q-108 0 -172.5 60.5t-64.5 159.5q0 120 81 203.5t200 83.5q139 0 196 -95l-95 -73q-32 55 -101 55q-67 0 -109.5 -49t-42.5 -119q0 -53 34.5 -83t86.5 -30q60 0 101 48l69 -85q-72 -76 -183 -76zM441 618q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43
q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="687" 
d="M549 723h-114l-63 144h76l53 -90l91 90h80zM372 -12q-141 0 -234.5 82.5t-93.5 216.5q0 172 114 281.5t274 109.5q115 0 189.5 -53t106.5 -136l-137 -46q-19 52 -63.5 80.5t-102.5 28.5q-96 0 -164.5 -74t-68.5 -182q0 -79 51.5 -130.5t136.5 -51.5q43 0 86 21t71 56
l109 -74q-53 -68 -126.5 -98.5t-147.5 -30.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="499" 
d="M420 556h-114l-63 144h76l53 -90l91 90h80zM265 -12q-108 0 -172.5 60.5t-64.5 159.5q0 120 81 203.5t200 83.5q139 0 196 -95l-95 -73q-32 55 -101 55q-67 0 -109.5 -49t-42.5 -119q0 -53 34.5 -83t86.5 -30q60 0 101 48l69 -85q-72 -76 -183 -76z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="718" 
d="M527 723h-114l-63 144h76l53 -90l91 90h80zM274 0h-262l147 667h258q116 0 202.5 -81t86.5 -206q0 -54 -14.5 -106.5t-48 -102.5t-82 -87.5t-122.5 -60.5t-165 -23zM295 125h5q117 0 187.5 71.5t70.5 174.5q0 73 -47 122t-121 49h-116l-92 -417h113z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="690" 
d="M773 615q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM281 495q48 0 89 -20t65 -56l54 248h128l-148 -667h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5
t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="742" 
d="M299 0h-262l61 276h-83l26 115h82l61 276h258q116 0 202.5 -81t86.5 -206q0 -54 -14.5 -106.5t-48 -102.5t-82 -87.5t-122.5 -60.5t-165 -23zM327 125q116 1 186 72t70 174q0 73 -47 122t-121 49h-116l-33 -151h147l-25 -115h-148l-33 -151h120z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="590" 
d="M281 495q48 0 89 -20t65 -56l23 109h-150l14 72h152l15 67h128l-15 -67h51l-15 -72h-52l-117 -528h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168
q-37 56 -113 56z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="583" 
d="M484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329zM589 729h-362l16 72h362z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="553" 
d="M278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5zM538 562h-362
l16 72h362z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="583" 
d="M484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329zM592 786q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="553" 
d="M278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5zM540 619
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="583" 
d="M484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329zM490 791q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="553" 
d="M278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5zM436 618
q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="583" 
d="M484 0h-1q-93 -43 -93 -100q0 -33 35 -33q27 0 47 34l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 65 60 110h-375l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="553" 
d="M354 -99l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 55 47 99q-106 5 -167 62.5t-61 153.5q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-38 -26 -84 -38q-93 -43 -93 -100q0 -33 35 -33
q27 0 47 34zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="583" 
d="M466 723h-114l-63 144h76l53 -90l91 90h80zM484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="553" 
d="M415 556h-114l-63 144h76l53 -90l91 90h80zM278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5
q-49 0 -85.5 -32.5t-45.5 -74.5z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="720" 
d="M646 723h-76l-54 89l-90 -89h-80l124 144h114zM372 -12q-140 0 -234 82t-94 217q0 169 114 280t287 111q100 0 171 -47t104 -118l-131 -56q-17 42 -62.5 68.5t-101.5 26.5q-95 0 -164 -74t-69 -182q0 -78 51 -130t137 -52q73 0 133 51l18 80h-177l28 125h319l-58 -260
q-103 -122 -271 -122z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="585" 
d="M500 547h-76l-54 89l-90 -89h-80l124 144h114zM212 -196q-156 0 -233 84l74 85q23 -31 65.5 -48t91.5 -17q109 0 137 125l10 44q-60 -73 -148 -73q-79 0 -130.5 48.5t-51.5 147.5q0 117 69 206t181 89q44 0 87 -20.5t69 -55.5l14 64h128l-100 -450q-14 -66 -42.5 -112.5
t-65.5 -71t-75 -35t-80 -10.5zM269 117q63 0 109 55l34 150q-36 60 -118 60q-59 0 -97 -45.5t-38 -111.5q0 -49 30 -78.5t80 -29.5z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="720" 
d="M372 -12q-140 0 -234 82t-94 217q0 169 114 280t287 111q100 0 171 -47t104 -118l-131 -56q-17 42 -62.5 68.5t-101.5 26.5q-95 0 -164 -74t-69 -182q0 -78 51 -130t137 -52q73 0 133 51l18 80h-177l28 125h319l-58 -260q-103 -122 -271 -122zM682 786q-85 -88 -194 -88
q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="585" 
d="M212 -196q-156 0 -233 84l74 85q23 -31 65.5 -48t91.5 -17q109 0 137 125l10 44q-60 -73 -148 -73q-79 0 -130.5 48.5t-51.5 147.5q0 117 69 206t181 89q44 0 87 -20.5t69 -55.5l14 64h128l-100 -450q-14 -66 -42.5 -112.5t-65.5 -71t-75 -35t-80 -10.5zM269 117
q63 0 109 55l34 150q-36 60 -118 60q-59 0 -97 -45.5t-38 -111.5q0 -49 30 -78.5t80 -29.5zM537 619q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="720" 
d="M372 -12q-140 0 -234 82t-94 217q0 169 114 280t287 111q100 0 171 -47t104 -118l-131 -56q-17 42 -62.5 68.5t-101.5 26.5q-95 0 -164 -74t-69 -182q0 -78 51 -130t137 -52q73 0 133 51l18 80h-177l28 125h319l-58 -260q-103 -122 -271 -122zM577 785q0 -30 -23.5 -52.5
t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="585" 
d="M212 -196q-156 0 -233 84l74 85q23 -31 65.5 -48t91.5 -17q109 0 137 125l10 44q-60 -73 -148 -73q-79 0 -130.5 48.5t-51.5 147.5q0 117 69 206t181 89q44 0 87 -20.5t69 -55.5l14 64h128l-100 -450q-14 -66 -42.5 -112.5t-65.5 -71t-75 -35t-80 -10.5zM269 117
q63 0 109 55l34 150q-36 60 -118 60q-59 0 -97 -45.5t-38 -111.5q0 -49 30 -78.5t80 -29.5zM433 618q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="720" 
d="M373 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM372 -12q-140 0 -234 82t-94 217q0 169 114 280t287 111q100 0 171 -47t104 -118l-131 -56q-17 42 -62.5 68.5t-101.5 26.5
q-95 0 -164 -74t-69 -182q0 -78 51 -130t137 -52q73 0 133 51l18 80h-177l28 125h319l-58 -260q-103 -122 -271 -122z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="585" 
d="M301 608q0 46 34 92.5t85 71.5l36 -38q-63 -29 -81 -71h9q21 0 35 -13.5t14 -38.5q0 -28 -21 -48t-49 -20q-26 0 -44 17t-18 48zM212 -196q-156 0 -233 84l74 85q23 -31 65.5 -48t91.5 -17q109 0 137 125l10 44q-60 -73 -148 -73q-79 0 -130.5 48.5t-51.5 147.5
q0 117 69 206t181 89q44 0 87 -20.5t69 -55.5l14 64h128l-100 -450q-14 -66 -42.5 -112.5t-65.5 -71t-75 -35t-80 -10.5zM269 117q63 0 109 55l34 150q-36 60 -118 60q-59 0 -97 -45.5t-38 -111.5q0 -49 30 -78.5t80 -29.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="731" 
d="M621 723h-76l-54 89l-90 -89h-80l124 144h114zM612 0h-143l62 280h-315l-62 -280h-142l147 667h142l-57 -262h314l58 262h143z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="580" 
d="M547 723h-76l-54 89l-90 -89h-80l124 144h114zM463 0h-127l63 290q5 20 5 30q0 62 -83 62q-56 0 -113 -57l-72 -325h-127l147 667h127l-54 -247q77 75 161 75q69 0 110 -33.5t41 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="744" 
d="M618 0h-143l62 280h-315l-62 -280h-142l110 501h-65l16 73h65l21 93h142l-20 -93h314l21 93h143l-21 -93h64l-16 -73h-64zM250 405h314l21 96h-314z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="582" 
d="M465 0h-127l63 290q5 20 5 30q0 62 -83 62q-56 0 -113 -57l-72 -325h-127l116 528h-58l17 73h57l15 66h127l-15 -66h146l-16 -73h-146l-23 -108q77 75 161 75q69 0 110 -33.5t41 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="274" 
d="M154 0h-142l147 667h142zM314 721q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="253" 
d="M136 0h-127l107 483h127zM267 554q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="274" 
d="M154 0h-142l147 667h142zM425 729h-362l16 72h362z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="253" 
d="M136 0h-127l107 483h127zM378 562h-362l16 72h362z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="274" 
d="M154 0h-142l147 667h142zM427 786q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="253" 
d="M136 0h-127l107 483h127zM382 619q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="274" 
d="M143 -99l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 65 60 110h-46l147 667h142l-147 -667q-93 -43 -93 -100q0 -33 35 -33q27 0 47 34z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="253" 
d="M125 -99l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 65 60 110h-31l107 483h127l-107 -483q-93 -43 -93 -100q0 -33 35 -33q27 0 47 34zM198 535q-29 0 -48.5 19t-19.5 46q0 39 26.5 62.5t58.5 23.5q30 0 49.5 -19t19.5 -46q0 -39 -27 -62.5t-59 -23.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="274" 
d="M154 0h-142l147 667h142zM324 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="253" 
d="M136 0h-127l107 483h127z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="760" 
d="M154 0h-142l147 667h142zM439 -12q-59 0 -111 19t-83 56l77 106q36 -55 107 -55q93 0 118 107l98 446h142l-98 -448q-26 -119 -86.5 -175t-163.5 -56z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="505" 
d="M198 535q-29 0 -48.5 19t-19.5 46q0 39 26.5 62.5t58.5 23.5q30 0 49.5 -19t19.5 -46q0 -39 -27 -62.5t-59 -23.5zM136 0h-127l107 483h127zM199 -196q-90 0 -134 41l53 91q22 -28 63 -28q59 0 77 79l111 496h127l-111 -496q-19 -88 -63 -135.5t-123 -47.5zM451 535
q-29 0 -48.5 19t-19.5 46q0 39 26.5 62.5t58.5 23.5q30 0 49.5 -19t19.5 -46q0 -39 -27 -62.5t-59 -23.5z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="486" 
d="M606 723h-76l-54 89l-90 -89h-80l124 144h114zM165 -12q-59 0 -111 19t-83 56l77 106q36 -55 107 -55q93 0 118 107l98 446h142l-98 -448q-26 -119 -86.5 -175t-163.5 -56z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="253" 
d="M347 556h-76l-54 89l-90 -89h-80l124 144h114zM-54 -196q-90 0 -134 41l53 91q22 -28 63 -28q59 0 77 79l111 496h127l-111 -496q-19 -88 -63 -135.5t-123 -47.5z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="636" 
d="M313 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM583 0h-167l-147 273l-69 -68l-46 -205h-142l147 667h142l-63 -286l291 286h183l-334 -313z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="535" 
d="M259 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM481 0h-154l-89 189l-75 -65l-27 -124h-127l147 667h127l-87 -391l224 207h162l-240 -219z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="535" 
d="M481 0h-154l-89 189l-75 -65l-27 -124h-127l107 483h127l-47 -207l224 207h162l-240 -219z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="523" 
d="M601 867l-194 -144h-91l166 144h119zM436 0h-424l147 667h142l-119 -542h281z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="253" 
d="M452 867l-194 -144h-91l166 144h119zM136 0h-127l147 667h127z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="523" 
d="M257 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM436 0h-424l147 667h142l-119 -542h281z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="253" 
d="M109 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM136 0h-127l147 667h127z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="523" 
d="M499 616q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM436 0h-424l147 667h142l-119 -542h281z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="354" 
d="M451 616q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM136 0h-127l147 667h127z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="542" 
d="M436 0h-424l147 667h142l-119 -542h281zM422 261q-31 0 -52.5 22t-21.5 52q0 37 26 62.5t63 25.5q30 0 51.5 -22t21.5 -53q0 -35 -26.5 -61t-61.5 -26z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="384" 
d="M136 0h-127l147 667h127zM311 164q-31 0 -52.5 22t-21.5 52q0 37 26 62.5t63 25.5q30 0 51.5 -22t21.5 -53q0 -35 -26.5 -61t-61.5 -26z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="542" 
d="M2 192l26 116l81 41l70 318h142l-52 -236l86 44l-25 -116l-87 -44l-41 -190h281l-27 -125h-424l51 233z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="308" 
d="M-1 204l21 93l93 47l71 323h127l-55 -250l92 47l-21 -93l-92 -47l-71 -324h-127l55 251z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="729" 
d="M690 867l-194 -144h-91l166 144h119zM609 0h-137l-218 450l-100 -450h-142l147 667h146l212 -434l97 434h142z" />
    <glyph glyph-name="nacute" unicode="&#x144;" 
d="M575 700l-194 -144h-91l166 144h119zM462 0h-127l64 288q5 22 5 32q0 32 -21 47t-53 15q-64 0 -122 -56l-72 -326h-127l107 483h127l-14 -63q77 75 161 75q69 0 110 -33.5t41 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="729" 
d="M348 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM609 0h-137l-218 450l-100 -450h-142l147 667h146l212 -434l97 434h142z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" 
d="M270 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM462 0h-127l64 288q5 22 5 32q0 32 -21 47t-53 15q-64 0 -122 -56l-72 -326h-127l107 483h127l-14 -63q77 75 161 75
q69 0 110 -33.5t41 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="729" 
d="M530 723h-114l-63 144h76l53 -90l91 90h80zM609 0h-137l-218 450l-100 -450h-142l147 667h146l212 -434l97 434h142z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" 
d="M416 556h-114l-63 144h76l53 -90l91 90h80zM462 0h-127l64 288q5 22 5 32q0 32 -21 47t-53 15q-64 0 -122 -56l-72 -326h-127l107 483h127l-14 -63q77 75 161 75q69 0 110 -33.5t41 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" 
d="M272 700q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM462 0h-127l64 288q5 22 5 32q0 32 -21 47t-53 15q-64 0 -122 -56l-72 -326h-127l107 483h127l-14 -63q77 75 161 75
q69 0 110 -33.5t41 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="729" 
d="M366 -196q-60 0 -111.5 19.5t-82.5 55.5l75 94q16 -25 44.5 -40.5t62.5 -15.5q42 0 71 24t42 68l-213 441l-100 -450h-142l147 667h146l212 -434l97 434h142l-140 -633q-27 -119 -88.5 -174.5t-161.5 -55.5z" />
    <glyph glyph-name="eng" unicode="&#x14b;" 
d="M536 332l-77 -345q-19 -87 -63 -135t-123 -48q-92 0 -134 41l56 99q24 -27 61 -27q59 0 76 70l68 306q4 20 4 27q0 31 -20.5 46.5t-50.5 15.5q-67 0 -125 -56l-72 -326h-127l107 483h127l-14 -63q77 75 161 75q67 0 109 -32.5t42 -90.5q0 -17 -5 -40z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="765" 
d="M671 729h-362l16 72h362zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5z
" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="574" 
d="M538 562h-362l16 72h362zM266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5zM674 786q-85 -88 -194 -88
q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="574" 
d="M266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30zM541 619q-85 -88 -194 -88q-53 0 -94 23.5
t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="765" 
d="M581 867l-147 -144h-76l119 144h104zM730 867l-147 -144h-76l119 144h104zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5
q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="574" 
d="M449 700l-147 -144h-76l119 144h104zM598 700l-147 -144h-76l119 144h104zM266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5
t-41 -117.5q0 -53 30 -83t83 -30z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1084" 
d="M987 0h-472l17 76q-78 -88 -208 -88q-119 0 -199.5 84t-80.5 215q0 113 52.5 203.5t138.5 138.5t187 48q156 0 218 -115l22 105h472l-27 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329zM564 219l44 201q-16 62 -63.5 96.5t-113.5 34.5q-103 0 -171 -74.5
t-68 -180.5q0 -82 50.5 -132t130.5 -50q57 0 108 28t83 77z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="931" 
d="M895 201h-355q0 -1 -0.5 -5t-0.5 -7q0 -38 35.5 -69t95.5 -31q73 0 120 36l39 -87q-71 -50 -169 -50q-73 0 -126 33t-72 74q-30 -33 -49 -50.5t-60.5 -37t-88.5 -19.5q-107 0 -172 58.5t-65 160.5q0 114 78 201t199 87q69 0 118 -33.5t69 -74.5q27 31 48.5 50.5
t62.5 38.5t87 19q92 0 153.5 -57t61.5 -159q0 -35 -9 -78zM552 287h238q1 3 1 10q0 40 -28 68.5t-81 28.5q-50 0 -86.5 -34t-43.5 -73zM410 269q0 56 -31 84.5t-79 28.5q-59 0 -100.5 -49t-41.5 -119q0 -53 29.5 -83t81.5 -30q60 0 100.5 49.5t40.5 118.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="643" 
d="M650 867l-194 -144h-91l166 144h119zM552 0h-157l-79 237h-110l-52 -237h-142l147 667h285q84 0 145 -52t61 -136q0 -89 -53.5 -153t-136.5 -79zM381 362h2q55 0 86.5 28.5t31.5 74.5q0 33 -25 55t-59 22h-143l-40 -180h147z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="360" 
d="M499 700l-194 -144h-91l166 144h119zM136 0h-127l107 483h127l-15 -63q70 75 172 75l-28 -126q-24 6 -46 6q-69 0 -120 -58z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="643" 
d="M307 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM552 0h-157l-79 237h-110l-52 -237h-142l147 667h285q84 0 145 -52t61 -136q0 -89 -53.5 -153t-136.5 -79zM381 362h2
q55 0 86.5 28.5t31.5 74.5q0 33 -25 55t-59 22h-143l-40 -180h147z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="360" 
d="M195 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM136 0h-127l107 483h127l-15 -63q70 75 172 75l-28 -126q-24 6 -46 6q-69 0 -120 -58z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="643" 
d="M490 723h-114l-63 144h76l53 -90l91 90h80zM552 0h-157l-79 237h-110l-52 -237h-142l147 667h285q84 0 145 -52t61 -136q0 -89 -53.5 -153t-136.5 -79zM381 362h2q55 0 86.5 28.5t31.5 74.5q0 33 -25 55t-59 22h-143l-40 -180h147z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="360" 
d="M339 556h-114l-63 144h76l53 -90l91 90h80zM136 0h-127l107 483h127l-15 -63q70 75 172 75l-28 -126q-24 6 -46 6q-69 0 -120 -58z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="600" 
d="M629 867l-194 -144h-91l166 144h119zM301 -12q-97 0 -178 34t-124 90l88 104q35 -47 96 -75.5t129 -28.5q49 0 74.5 23.5t25.5 52.5q0 25 -33.5 44.5t-81 36.5t-95 38t-81 60t-33.5 93q0 87 71.5 152t189.5 65q80 0 150.5 -28.5t114.5 -79.5l-88 -100q-36 41 -90.5 63
t-108.5 22q-38 0 -63.5 -20t-25.5 -48q0 -23 33.5 -42t81.5 -35.5t96 -38t81.5 -61t33.5 -93.5q0 -96 -71.5 -162t-191.5 -66z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="477" 
d="M531 700l-194 -144h-91l166 144h119zM219 -12q-67 0 -127.5 22.5t-100.5 64.5l71 85q23 -28 71.5 -52t97.5 -24q31 0 50.5 15.5t19.5 35.5q0 23 -38.5 40.5t-84 30t-84 45t-38.5 81.5q0 67 53.5 115t145.5 48q61 0 115.5 -21.5t89.5 -57.5l-65 -81q-19 24 -59.5 43.5
t-82.5 19.5q-32 0 -51 -13.5t-19 -33.5q0 -16 25 -29t61 -24t72.5 -26t61.5 -44.5t25 -70.5q0 -72 -56.5 -120.5t-152.5 -48.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="600" 
d="M560 723h-76l-54 89l-90 -89h-80l124 144h114zM301 -12q-97 0 -178 34t-124 90l88 104q35 -47 96 -75.5t129 -28.5q49 0 74.5 23.5t25.5 52.5q0 25 -33.5 44.5t-81 36.5t-95 38t-81 60t-33.5 93q0 87 71.5 152t189.5 65q80 0 150.5 -28.5t114.5 -79.5l-88 -100
q-36 41 -90.5 63t-108.5 22q-38 0 -63.5 -20t-25.5 -48q0 -23 33.5 -42t81.5 -35.5t96 -38t81.5 -61t33.5 -93.5q0 -96 -71.5 -162t-191.5 -66z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="477" 
d="M461 556h-76l-54 89l-90 -89h-80l124 144h114zM219 -12q-67 0 -127.5 22.5t-100.5 64.5l71 85q23 -28 71.5 -52t97.5 -24q31 0 50.5 15.5t19.5 35.5q0 23 -38.5 40.5t-84 30t-84 45t-38.5 81.5q0 67 53.5 115t145.5 48q61 0 115.5 -21.5t89.5 -57.5l-65 -81
q-19 24 -59.5 43.5t-82.5 19.5q-32 0 -51 -13.5t-19 -33.5q0 -16 25 -29t61 -24t72.5 -26t61.5 -44.5t25 -70.5q0 -72 -56.5 -120.5t-152.5 -48.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="600" 
d="M221 -196q-78 0 -119 41l35 48q37 -35 84 -35q50 0 50 35q0 24 -30 24q-17 0 -29 -14l-40 26l39 67q-143 26 -212 116l88 104q35 -47 96 -75.5t129 -28.5q49 0 74.5 23.5t25.5 52.5q0 25 -33.5 44.5t-81 36.5t-95 38t-81 60t-33.5 93q0 87 71.5 152t189.5 65
q80 0 150.5 -28.5t114.5 -79.5l-88 -100q-36 41 -90.5 63t-108.5 22q-38 0 -63.5 -20t-25.5 -48q0 -23 33.5 -42t81.5 -35.5t96 -38t81.5 -61t33.5 -93.5q0 -96 -71.5 -162t-191.5 -66q-6 0 -16.5 0.5t-15.5 0.5l-25 -40q14 10 31 10q25 0 42 -16.5t17 -45.5
q0 -42 -32 -67.5t-81 -25.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="477" 
d="M160 -196q-78 0 -119 41l35 48q37 -35 84 -35q50 0 50 35q0 24 -30 24q-17 0 -29 -14l-40 26l38 65q-99 18 -158 81l71 85q23 -28 71.5 -52t97.5 -24q31 0 50.5 15.5t19.5 35.5q0 23 -38.5 40.5t-84 30t-84 45t-38.5 81.5q0 67 53.5 115t145.5 48q61 0 115.5 -21.5
t89.5 -57.5l-65 -81q-19 24 -59.5 43.5t-82.5 19.5q-32 0 -51 -13.5t-19 -33.5q0 -16 25 -29t61 -24t72.5 -26t61.5 -44.5t25 -70.5q0 -72 -56.5 -120.5t-152.5 -48.5h-12l-24 -39q14 10 31 10q26 0 42.5 -16.5t16.5 -45.5q0 -42 -32 -67.5t-81 -25.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="600" 
d="M470 723h-114l-63 144h76l53 -90l91 90h80zM301 -12q-97 0 -178 34t-124 90l88 104q35 -47 96 -75.5t129 -28.5q49 0 74.5 23.5t25.5 52.5q0 25 -33.5 44.5t-81 36.5t-95 38t-81 60t-33.5 93q0 87 71.5 152t189.5 65q80 0 150.5 -28.5t114.5 -79.5l-88 -100
q-36 41 -90.5 63t-108.5 22q-38 0 -63.5 -20t-25.5 -48q0 -23 33.5 -42t81.5 -35.5t96 -38t81.5 -61t33.5 -93.5q0 -96 -71.5 -162t-191.5 -66z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="477" 
d="M371 556h-114l-63 144h76l53 -90l91 90h80zM219 -12q-67 0 -127.5 22.5t-100.5 64.5l71 85q23 -28 71.5 -52t97.5 -24q31 0 50.5 15.5t19.5 35.5q0 23 -38.5 40.5t-84 30t-84 45t-38.5 81.5q0 67 53.5 115t145.5 48q61 0 115.5 -21.5t89.5 -57.5l-65 -81
q-19 24 -59.5 43.5t-82.5 19.5q-32 0 -51 -13.5t-19 -33.5q0 -16 25 -29t61 -24t72.5 -26t61.5 -44.5t25 -70.5q0 -72 -56.5 -120.5t-152.5 -48.5z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="582" 
d="M276 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM309 0h-143l119 542h-195l28 125h532l-27 -125h-195z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="338" 
d="M145 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM203 -12q-67 0 -105.5 26.5t-38.5 78.5q0 17 4 33l54 247h-80l25 110h80l29 132h128l-29 -132h98l-26 -110h-98l-46 -210
q-2 -12 -2 -20q0 -42 47 -42q23 0 35 10l5 -102q-31 -21 -80 -21z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="582" 
d="M450 723h-114l-63 144h76l53 -90l91 90h80zM309 0h-143l119 542h-195l28 125h532l-27 -125h-195z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="369" 
d="M490 700q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM203 -12q-67 0 -105.5 26.5t-38.5 78.5q0 17 4 33l54 247h-80l25 110h80l29 132h128l-29 -132h98l-26 -110h-98l-46 -210
q-2 -12 -2 -20q0 -42 47 -42q23 0 35 10l5 -102q-31 -21 -80 -21z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="582" 
d="M309 0h-143l57 263h-132l16 72h132l46 207h-195l28 125h532l-27 -125h-195l-46 -207h134l-17 -72h-133z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="338" 
d="M203 -12q-67 0 -105.5 26.5t-38.5 78.5q0 17 4 33l18 83h-80l16 73h80l20 91h-80l25 110h80l29 132h128l-29 -132h98l-26 -110h-98l-20 -91h68l-16 -73h-68l-10 -46q-2 -12 -2 -20q0 -42 47 -42q23 0 35 10l5 -102q-31 -21 -80 -21z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="733" 
d="M356 -12q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-29 -133 -103 -206t-213 -73zM543 721q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5
q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" 
d="M115 483h128l-65 -291q-4 -18 -4 -29q0 -31 22.5 -46.5t58.5 -15.5q56 0 115 57l72 325h127l-107 -483h-127l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39zM429 554q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5
t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="733" 
d="M356 -12q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-29 -133 -103 -206t-213 -73zM654 729h-362l16 72h362z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" 
d="M115 483h128l-65 -291q-4 -18 -4 -29q0 -31 22.5 -46.5t58.5 -15.5q56 0 115 57l72 325h127l-107 -483h-127l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39zM540 562h-362l16 72h362z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="733" 
d="M356 -12q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-29 -133 -103 -206t-213 -73zM657 786q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62
q74 0 133 62z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" 
d="M115 483h128l-65 -291q-4 -18 -4 -29q0 -31 22.5 -46.5t58.5 -15.5q56 0 115 57l72 325h127l-107 -483h-127l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39zM543 619q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="733" 
d="M477 688q-42 0 -68.5 26.5t-26.5 69.5q0 46 34.5 80.5t80.5 34.5q43 0 69 -26.5t26 -69.5q0 -47 -34 -81t-81 -34zM483 747q20 0 35 15t15 35q0 19 -11 31t-30 12q-20 0 -35.5 -15t-15.5 -35q0 -19 11.5 -31t30.5 -12zM356 -12q-141 0 -215.5 63t-74.5 170q0 20 5 45
l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-29 -133 -103 -206t-213 -73z" />
    <glyph glyph-name="uring" unicode="&#x16f;" 
d="M366 531q-42 0 -68.5 26.5t-26.5 69.5q0 46 34.5 80.5t80.5 34.5q43 0 69 -26.5t26 -69.5q0 -47 -34 -81t-81 -34zM372 590q20 0 35 15t15 35q0 19 -11 31t-30 12q-20 0 -35.5 -15t-15.5 -35q0 -19 11.5 -31t30.5 -12zM115 483h128l-65 -291q-4 -18 -4 -29
q0 -31 22.5 -46.5t58.5 -15.5q56 0 115 57l72 325h127l-107 -483h-127l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="733" 
d="M356 -12q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-29 -133 -103 -206t-213 -73zM566 867l-147 -144h-76l119 144h104zM715 867l-147 -144h-76l119 144h104z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" 
d="M115 483h128l-65 -291q-4 -18 -4 -29q0 -31 22.5 -46.5t58.5 -15.5q56 0 115 57l72 325h127l-107 -483h-127l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39zM451 700l-147 -144h-76l119 144h104zM600 700l-147 -144h-76l119 144h104z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="733" 
d="M443 -99l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 56 46 98q-135 3 -206.5 65.5t-71.5 167.5q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-51 -226 -220 -268q-91 -42 -91 -99q0 -33 35 -33
q27 0 47 34z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" 
d="M115 483h128l-65 -291q-4 -18 -4 -29q0 -31 22.5 -46.5t58.5 -15.5q56 0 115 57l72 325h127l-107 -483q-93 -43 -93 -100q0 -33 35 -33q27 0 47 34l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 65 60 110h-31l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39z
" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="918" 
d="M712 723h-76l-54 89l-90 -89h-80l124 144h114zM673 0h-151l-16 458l-217 -458h-151l-44 667h156l10 -482l233 482h111l20 -482l223 482h163z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="769" 
d="M601 556h-76l-54 89l-90 -89h-80l124 144h114zM568 0h-135l-30 329l-174 -329h-136l-40 483h127l19 -325l178 325h113l33 -325l163 325h136z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="645" 
d="M580 723h-76l-54 89l-90 -89h-80l124 144h114zM340 0h-142l60 273l-170 394h154l107 -268l223 268h170l-342 -394z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="513" 
d="M-48 -184l42 110q18 -8 49 -8q37 0 60 34l30 45l-86 486h132l51 -336l202 336h140l-352 -562q-39 -64 -82.5 -90.5t-104.5 -26.5q-46 0 -81 12zM472 556h-76l-54 89l-90 -89h-80l124 144h114z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="645" 
d="M615 785q0 -29 -23 -52t-53 -23q-25 0 -42.5 17t-17.5 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM404 785q0 -29 -23 -52t-53 -23q-26 0 -43 17t-17 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM340 0h-142l60 273l-170 394h154l107 -268l223 268h170l-342 -394z
" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="592" 
d="M616 867l-194 -144h-91l166 144h119zM496 0h-508l25 115l401 427h-306l27 125h501l-24 -114l-403 -428h315z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="479" 
d="M523 700l-194 -144h-91l166 144h119zM382 0h-394l21 95l275 277h-213l24 111h390l-20 -92l-279 -280h220z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="592" 
d="M496 0h-508l25 115l401 427h-306l27 125h501l-24 -114l-403 -428h315zM478 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="479" 
d="M382 0h-394l21 95l275 277h-213l24 111h390l-20 -92l-279 -280h220zM386 618q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="592" 
d="M458 723h-114l-63 144h76l53 -90l91 90h80zM496 0h-508l25 115l401 427h-306l27 125h501l-24 -114l-403 -428h315z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="479" 
d="M364 556h-114l-63 144h76l53 -90l91 90h80zM382 0h-394l21 95l275 277h-213l24 111h390l-20 -92l-279 -280h220z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="327" 
d="M165 0h-127l82 373h-80l25 110h79l6 26q17 76 63 122t123 46q81 0 127 -47l-63 -74q-21 21 -54 21q-53 0 -69 -68z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="454" 
d="M99 -161h-146l89 399h-54v112h78l37 166q16 75 61 118t117 43q86 0 137 -48l-53 -100q-19 23 -52 23q-55 0 -71 -67l-30 -135h113v-112h-138z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q163 0 257 -105q48 27 62 60h-7q-18 0 -30.5 12t-12.5 34q0 24 19 42t43 18q22 0 38 -15.5t16 -42.5q0 -40 -28.5 -80.5t-72.5 -63.5q43 -70 43 -157q0 -164 -111.5 -278t-275.5 -114zM380 114
q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" horiz-adv-x="574" 
d="M600 548q0 -39 -26.5 -78.5t-68.5 -62.5q41 -55 41 -132q0 -116 -80 -201.5t-200 -85.5q-113 0 -176 61t-63 158q0 116 80 202t199 86q103 0 168 -55q46 28 60 60q-1 -1 -7 -1q-18 0 -31 12.5t-13 33.5q0 25 19 43t44 18q22 0 38 -15.5t16 -42.5zM271 101q62 0 102.5 50
t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="733" 
d="M882 742q0 -51 -40 -95t-101 -68l-69 -312q-29 -133 -103 -206t-213 -73q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-8 -34q50 28 63 61q-1 -1 -7 -1q-18 0 -31 12.5t-13 34.5
q0 25 19 42.5t44 17.5q22 0 38 -15.5t16 -42.5z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" 
d="M667 560q0 -46 -31.5 -85.5t-83.5 -65.5l-90 -409h-127l14 62q-78 -74 -161 -74q-70 0 -111 33.5t-41 89.5q0 20 6 40l73 332h127l-64 -292q-4 -18 -4 -28q0 -31 22.5 -46.5t59.5 -15.5q56 0 113 57l73 325h127l-3 -14q26 20 35 43h-7q-18 0 -31 12.5t-13 33.5
q0 24 19 42.5t43 18.5q23 0 39 -16t16 -43z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="968" 
d="M852 867l-194 -144h-91l166 144h119zM872 0h-472l25 113h-231l-91 -113h-166l553 667h529l-28 -125h-329l-31 -141h323l-28 -125h-322l-34 -151h329zM453 238l61 280l-224 -280h163z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="914" 
d="M749 700l-194 -144h-91l166 144h119zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM487 82l-18 -82h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47q48 0 89 -20t65 -56l13 64h128
l-13 -59q64 71 147 71q85 0 131 -53t46 -154q0 -52 -11 -87h-357q-1 -2 -1 -12q0 -35 36 -66t100 -31q72 0 116 36l40 -90q-76 -50 -153 -50q-127 0 -170 94zM532 287h239q1 2 1 10q0 40 -28 68.5t-81 28.5q-49 0 -85.5 -32.5t-45.5 -74.5z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="765" 
d="M708 867l-194 -144h-91l166 144h119zM372 -12q-122 0 -208 60l-47 -48h-102l95 97q-66 77 -66 190q0 164 112 277.5t276 113.5q112 0 197 -54l42 43h102l-88 -90q74 -80 74 -197q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 58 -29 103
l-319 -325q48 -34 117 -34zM192 296q0 -52 22 -93l316 320q-46 29 -107 29q-97 0 -164 -74.5t-67 -181.5z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="574" 
d="M575 700l-194 -144h-91l166 144h119zM67 0h-83l82 76q-39 54 -39 131q0 116 80 202t199 86q97 0 158 -47l37 35h84l-80 -75q41 -55 41 -133q0 -116 -80 -201.5t-200 -85.5q-99 0 -161 48zM271 101q62 0 102.5 50t40.5 118q0 25 -8 47l-208 -194q27 -21 73 -21zM158 214
q0 -27 7 -45l207 193q-28 20 -70 20q-62 0 -103 -50.5t-41 -117.5z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="600" 
d="M282 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM301 -12q-97 0 -178 34t-124 90l88 104q35 -47 96 -75.5t129 -28.5q49 0 74.5 23.5t25.5 52.5q0 25 -33.5 44.5t-81 36.5
t-95 38t-81 60t-33.5 93q0 87 71.5 152t189.5 65q80 0 150.5 -28.5t114.5 -79.5l-88 -100q-36 41 -90.5 63t-108.5 22q-38 0 -63.5 -20t-25.5 -48q0 -23 33.5 -42t81.5 -35.5t96 -38t81.5 -61t33.5 -93.5q0 -96 -71.5 -162t-191.5 -66z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="477" 
d="M226 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM219 -12q-67 0 -127.5 22.5t-100.5 64.5l71 85q23 -28 71.5 -52t97.5 -24q31 0 50.5 15.5t19.5 35.5q0 23 -38.5 40.5t-84 30
t-84 45t-38.5 81.5q0 67 53.5 115t145.5 48q61 0 115.5 -21.5t89.5 -57.5l-65 -81q-19 24 -59.5 43.5t-82.5 19.5q-32 0 -51 -13.5t-19 -33.5q0 -16 25 -29t61 -24t72.5 -26t61.5 -44.5t25 -70.5q0 -72 -56.5 -120.5t-152.5 -48.5z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="582" 
d="M276 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM309 0h-143l119 542h-195l28 125h532l-27 -125h-195z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="338" 
d="M141 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM203 -12q-67 0 -105.5 26.5t-38.5 78.5q0 17 4 33l54 247h-80l25 110h80l29 132h128l-29 -132h98l-26 -110h-98l-46 -210
q-2 -12 -2 -20q0 -42 47 -42q23 0 35 10l5 -102q-31 -21 -80 -21z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="253" 
d="M-54 -196q-90 0 -134 41l53 91q22 -28 63 -28q59 0 77 79l111 496h127l-111 -496q-19 -88 -63 -135.5t-123 -47.5z" />
    <glyph glyph-name="hsuperior" unicode="&#x2b0;" horiz-adv-x="411" 
d="M375 326h-92l41 186q2 12 2 18q0 40 -51 40q-36 0 -73 -37l-46 -207h-92l96 434h92l-35 -160q48 48 105 48q49 0 76.5 -22t27.5 -61q0 -6 -4 -30z" />
    <glyph glyph-name="uni02BC" unicode="&#x2bc;" horiz-adv-x="255" 
d="M291 595q0 -56 -42 -112.5t-105 -87.5l-46 47q30 13 58.5 36t39.5 48h-10q-26 0 -44 17t-18 48q0 35 27.5 60.5t61.5 25.5q32 0 55 -22t23 -60z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="300" 
d="M369 556h-76l-54 89l-90 -89h-80l124 144h114z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="300" 
d="M278 556h-114l-63 144h76l53 -90l91 90h80z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="363" 
d="M439 591h-362l15 73h362z" />
    <glyph glyph-name="uni02CB" unicode="&#x2cb;" horiz-adv-x="251" 
d="M320 556h-89l-130 144h117z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="351" 
d="M432 619q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="174" 
d="M237 618q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="209" 
d="M180 531q-42 0 -68.5 26.5t-26.5 69.5q0 46 34.5 80.5t80.5 34.5q43 0 69 -26.5t26 -69.5q0 -47 -34 -81t-81 -34zM186 590q20 0 35 15t15 35q0 19 -11 31t-30 12q-20 0 -35.5 -15t-15.5 -35q0 -19 11.5 -31t30.5 -12z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="194" 
d="M68 -99l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 42 29.5 79.5t83.5 60.5l43 -30q-93 -43 -93 -100q0 -33 35 -33q27 0 47 34z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="327" 
d="M303 554q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="338" 
d="M290 700l-147 -144h-76l119 144h104zM439 700l-147 -144h-76l119 144h104z" />
    <glyph glyph-name="hookabovecomb" unicode="&#x309;" horiz-adv-x="194" 
d="M223 723l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni037E" unicode="&#x37e;" horiz-adv-x="256" 
d="M174 69q0 -56 -42 -113t-105 -87l-45 47q30 13 58.5 36t39.5 48h-11q-26 0 -43.5 17t-17.5 48q0 35 27 60.5t61 25.5q33 0 55.5 -21.5t22.5 -60.5zM156 328q-31 0 -52 22t-21 53q0 36 26 61.5t62 25.5q31 0 52.5 -21.5t21.5 -52.5q0 -36 -26.5 -62t-62.5 -26z" />
    <glyph glyph-name="tonos" unicode="&#x384;" 
d="M236 553h-56l69 212h105z" />
    <glyph glyph-name="dieresistonos" unicode="&#x385;" 
d="M371 553h-57l69 212h106zM592 618q0 -29 -24 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43.5 -17t17.5 -43zM292 618q0 -29 -23.5 -52t-52.5 -23q-26 0 -43.5 17t-17.5 43q0 30 23.5 52.5t53.5 22.5q26 0 43 -17t17 -43z" />
    <glyph glyph-name="Alphatonos" unicode="&#x386;" horiz-adv-x="684" 
d="M189 553h-56l69 212h105zM634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207z" />
    <glyph glyph-name="Epsilontonos" unicode="&#x388;" horiz-adv-x="619" 
d="M109 553h-56l69 212h105zM520 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329z" />
    <glyph glyph-name="Etatonos" unicode="&#x389;" horiz-adv-x="768" 
d="M109 553h-56l69 212h105zM648 0h-143l62 280h-315l-62 -280h-142l147 667h142l-57 -262h314l58 262h143z" />
    <glyph glyph-name="Iotatonos" unicode="&#x38a;" horiz-adv-x="311" 
d="M109 553h-56l69 212h105zM190 0h-142l147 667h142z" />
    <glyph glyph-name="Omicrontonos" unicode="&#x38c;" horiz-adv-x="765" 
d="M125 553h-56l69 212h105zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5z
" />
    <glyph glyph-name="Upsilontonos" unicode="&#x38e;" horiz-adv-x="730" 
d="M109 553h-56l69 212h105zM424 0h-142l60 273l-170 394h154l107 -268l223 268h170l-342 -394z" />
    <glyph glyph-name="Omegatonos" unicode="&#x38f;" horiz-adv-x="774" 
d="M125 553h-56l69 212h105zM35 125h136q-46 36 -76 91.5t-30 125.5q0 61 24.5 120t70.5 107.5t119 78.5t161 30q144 0 238 -75t94 -203q0 -97 -54 -167.5t-127 -107.5h105l-27 -125h-270l28 125q86 19 138.5 87.5t52.5 159.5q0 81 -49 130t-135 49q-102 0 -166.5 -75.5
t-64.5 -177.5q0 -120 100 -173l-27 -125h-269z" />
    <glyph glyph-name="iotadieresistonos" unicode="&#x390;" horiz-adv-x="265" 
d="M218 553h-57l69 212h106zM439 618q0 -29 -24 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43.5 -17t17.5 -43zM139 618q0 -29 -23.5 -52t-52.5 -23q-26 0 -43.5 17t-17.5 43q0 30 23.5 52.5t53.5 22.5q26 0 43 -17t17 -43zM141 -12q-136 0 -107 132
l83 363h128l-78 -338q-9 -44 31 -44q8 0 20 2l-16 -108q-30 -7 -61 -7z" />
    <glyph glyph-name="Alpha" unicode="&#x391;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207z" />
    <glyph glyph-name="Beta" unicode="&#x392;" horiz-adv-x="663" 
d="M376 0h-364l147 667h324q84 0 132.5 -46.5t48.5 -113.5t-45 -117.5t-107 -56.5q45 -12 70.5 -50.5t25.5 -84.5q0 -79 -58 -138.5t-174 -59.5zM422 400q47 0 68.5 24.5t21.5 58.5q0 26 -19.5 44t-48.5 18h-169l-32 -145h179zM365 123q46 0 73.5 25t27.5 62q0 30 -20 49
t-53 19h-178l-34 -155h184z" />
    <glyph glyph-name="Gamma" unicode="&#x393;" horiz-adv-x="571" 
d="M154 0h-142l147 667h472l-28 -125h-329z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="684" 
d="M634 0h-693l405 667h178zM468 125l-57 400l-234 -400h291z" />
    <glyph glyph-name="Epsilon" unicode="&#x395;" horiz-adv-x="583" 
d="M484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329z" />
    <glyph glyph-name="Zeta" unicode="&#x396;" horiz-adv-x="592" 
d="M496 0h-508l25 115l401 427h-306l27 125h501l-24 -114l-403 -428h315z" />
    <glyph glyph-name="Eta" unicode="&#x397;" horiz-adv-x="731" 
d="M612 0h-143l62 280h-315l-62 -280h-142l147 667h142l-57 -262h314l58 262h143z" />
    <glyph glyph-name="Theta" unicode="&#x398;" horiz-adv-x="765" 
d="M537 276h-302l27 125h303zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5z
" />
    <glyph glyph-name="Iota" unicode="&#x399;" horiz-adv-x="274" 
d="M154 0h-142l147 667h142z" />
    <glyph glyph-name="Kappa" unicode="&#x39a;" horiz-adv-x="636" 
d="M583 0h-167l-147 273l-69 -68l-46 -205h-142l147 667h142l-63 -286l291 286h183l-334 -313z" />
    <glyph glyph-name="Lambda" unicode="&#x39b;" horiz-adv-x="684" 
d="M634 0h-154l-69 525l-301 -525h-169l405 667h178z" />
    <glyph glyph-name="Mu" unicode="&#x39c;" horiz-adv-x="855" 
d="M735 0h-143l106 481l-294 -481h-62l-82 481l-106 -481h-142l147 667h194l69 -417l255 417h205z" />
    <glyph glyph-name="Nu" unicode="&#x39d;" horiz-adv-x="729" 
d="M609 0h-137l-218 450l-100 -450h-142l147 667h146l212 -434l97 434h142z" />
    <glyph glyph-name="Xi" unicode="&#x39e;" horiz-adv-x="626" 
d="M646 542h-532l27 125h532zM527 0h-532l27 125h533zM581 276h-520l28 125h520z" />
    <glyph glyph-name="Omicron" unicode="&#x39f;" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5z" />
    <glyph glyph-name="Pi" unicode="&#x3a0;" horiz-adv-x="731" 
d="M612 0h-143l119 542h-314l-120 -542h-142l147 667h600z" />
    <glyph glyph-name="Rho" unicode="&#x3a1;" horiz-adv-x="624" 
d="M154 0h-142l147 667h293q90 0 144 -53.5t54 -131.5q0 -40 -14.5 -80.5t-44.5 -78.5t-86.5 -62t-130.5 -24h-167zM234 362h149q52 0 85 30t33 75q0 33 -23 54t-60 21h-144z" />
    <glyph glyph-name="Sigma" unicode="&#x3a3;" horiz-adv-x="571" 
d="M297 542l159 -207l-249 -210h296l-27 -125h-473l30 138l250 215l-156 202l24 112h472l-28 -125h-298z" />
    <glyph glyph-name="Tau" unicode="&#x3a4;" horiz-adv-x="582" 
d="M309 0h-143l119 542h-195l28 125h532l-27 -125h-195z" />
    <glyph glyph-name="Upsilon" unicode="&#x3a5;" horiz-adv-x="645" 
d="M340 0h-142l60 273l-170 394h154l107 -268l223 268h170l-342 -394z" />
    <glyph glyph-name="Phi" unicode="&#x3a6;" horiz-adv-x="855" 
d="M444 0h-142l16 69q-269 30 -269 240q0 122 108.5 202t281.5 89l15 67h142l-16 -70q269 -30 269 -241q0 -121 -109 -201t-281 -90zM700 357q0 95 -149 115l-63 -279q102 11 157 54t55 110zM198 309q0 -95 148 -115l63 278q-101 -11 -156 -54t-55 -109z" />
    <glyph glyph-name="Chi" unicode="&#x3a7;" horiz-adv-x="668" 
d="M617 0h-161l-121 237l-214 -237h-178l321 351l-159 316h161l109 -221l195 221h179l-303 -333z" />
    <glyph glyph-name="Psi" unicode="&#x3a8;" horiz-adv-x="866" 
d="M451 0h-143l29 129q-238 30 -238 217q0 41 10 80l55 241h144l-59 -260q-5 -18 -5 -40q0 -91 122 -112l94 412h143l-94 -412q157 26 185 152l59 260h145l-65 -288q-23 -103 -117.5 -173.5t-236.5 -80.5z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="765" 
d="M26 125h136q-46 36 -76 91.5t-30 125.5q0 61 24.5 120t70.5 107.5t119 78.5t161 30q144 0 238 -75t94 -203q0 -97 -54 -167.5t-127 -107.5h105l-27 -125h-270l28 125q86 19 138.5 87.5t52.5 159.5q0 81 -49 130t-135 49q-102 0 -166.5 -75.5t-64.5 -177.5
q0 -120 100 -173l-27 -125h-269z" />
    <glyph glyph-name="Iotadieresis" unicode="&#x3aa;" horiz-adv-x="274" 
d="M154 0h-142l147 667h142zM432 802q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM221 802q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43z" />
    <glyph glyph-name="Upsilondieresis" unicode="&#x3ab;" horiz-adv-x="645" 
d="M340 0h-142l60 273l-170 394h154l107 -268l223 268h170l-342 -394zM618 802q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM407 802q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17
t17 -43z" />
    <glyph glyph-name="alphatonos" unicode="&#x3ac;" horiz-adv-x="600" 
d="M387 553h-56l69 212h105zM552 103l-16 -108q-30 -7 -61 -7q-104 0 -109 86q-72 -86 -156 -86t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47q48 0 89 -20t65 -56l14 64h127l-75 -338q-9 -44 31 -44q8 0 20 2zM266 101q65 0 111 57l37 168q-37 56 -113 56
q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31z" />
    <glyph glyph-name="epsilontonos" unicode="&#x3ad;" horiz-adv-x="504" 
d="M346 553h-56l69 212h105zM220 -12q-109 0 -162 34.5t-53 93.5q0 57 54.5 92.5t110.5 39.5q-46 8 -74.5 34t-28.5 63q0 66 71 108t180 42q133 0 205 -81l-77 -72q-46 52 -130 52q-53 0 -86 -16t-33 -41q0 -44 88 -44h100l-23 -98h-99q-121 0 -121 -60q0 -46 89 -46
q102 0 179 55l43 -81q-100 -75 -233 -75z" />
    <glyph glyph-name="etatonos" unicode="&#x3ae;" 
d="M383 553h-56l69 212h105zM420 -184h-127l107 477q4 18 4 27q0 62 -83 62q-55 0 -113 -56l-72 -326h-127l107 483h127l-14 -63q77 75 161 75q69 0 110 -33.5t41 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="iotatonos" unicode="&#x3af;" horiz-adv-x="265" 
d="M224 553h-56l69 212h105zM141 -12q-136 0 -107 132l83 363h128l-78 -338q-9 -44 31 -44q8 0 20 2l-16 -108q-30 -7 -61 -7z" />
    <glyph glyph-name="upsilondieresistonos" unicode="&#x3b0;" horiz-adv-x="577" 
d="M387 553h-57l69 212h106zM608 618q0 -29 -24 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43.5 -17t17.5 -43zM308 618q0 -29 -23.5 -52t-52.5 -23q-26 0 -43.5 17t-17.5 43q0 30 23.5 52.5t53.5 22.5q26 0 43 -17t17 -43zM260 -12
q-121 0 -172.5 67t-27.5 174l59 254h127l-60 -263q-12 -55 8 -87t71 -32q72 0 119 66t47 160q0 72 -25 124l123 44q31 -59 31 -158q0 -148 -84 -248.5t-216 -100.5z" />
    <glyph glyph-name="alpha" unicode="&#x3b1;" horiz-adv-x="600" 
d="M552 103l-16 -108q-30 -7 -61 -7q-104 0 -109 86q-72 -86 -156 -86t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47q48 0 89 -20t65 -56l14 64h127l-75 -338q-9 -44 31 -44q8 0 20 2zM266 101q65 0 111 57l37 168q-37 56 -113 56q-61 0 -101.5 -50t-40.5 -118
q0 -51 30 -82t77 -31z" />
    <glyph glyph-name="beta" unicode="&#x3b2;" horiz-adv-x="586" 
d="M308 -12q-107 0 -158 73l-56 -245h-127l143 632q18 79 66.5 132.5t104.5 75t116 21.5q97 0 151 -40.5t54 -114.5q0 -69 -54.5 -118t-125.5 -60q51 -10 86 -43.5t35 -96.5q0 -92 -72.5 -154t-162.5 -62zM238 448l-67 -295q31 -52 110 -52q60 0 97 31t37 78q0 34 -26.5 53
t-71.5 19h-39l25 113h35q57 0 91.5 27.5t34.5 72.5q0 32 -23.5 50t-64.5 18q-48 0 -87 -31t-51 -84z" />
    <glyph glyph-name="gamma" unicode="&#x3b3;" horiz-adv-x="513" 
d="M224 -184h-128l25 107q35 168 11.5 314.5t-92.5 245.5h144q39 -61 59 -159.5t10 -176.5q51 58 100.5 154.5t68.5 181.5h128q-27 -117 -106.5 -249.5t-176.5 -228.5z" />
    <glyph glyph-name="delta" unicode="&#x3b4;" horiz-adv-x="574" 
d="M259 -12q-116 0 -175.5 58t-59.5 148q0 76 48 138t121 94q-42 34 -42 90q0 66 64.5 114.5t164.5 48.5q118 0 186 -66l-71 -86q-46 51 -131 51q-38 0 -62.5 -15.5t-24.5 -36.5q0 -18 28 -31.5t67.5 -28t79 -35.5t67.5 -64.5t28 -105.5q0 -106 -81.5 -189.5t-206.5 -83.5z
M421 248q0 26 -9.5 46.5t-30.5 35.5t-38.5 24t-48.5 21q-64 -24 -99.5 -69.5t-35.5 -95.5q0 -47 29 -78t85 -31q62 0 105 44t43 103z" />
    <glyph glyph-name="epsilon" unicode="&#x3b5;" horiz-adv-x="504" 
d="M220 -12q-109 0 -162 34.5t-53 93.5q0 57 54.5 92.5t110.5 39.5q-46 8 -74.5 34t-28.5 63q0 66 71 108t180 42q133 0 205 -81l-77 -72q-46 52 -130 52q-53 0 -86 -16t-33 -41q0 -44 88 -44h100l-23 -98h-99q-121 0 -121 -60q0 -46 89 -46q102 0 179 55l43 -81
q-100 -75 -233 -75z" />
    <glyph glyph-name="zeta" unicode="&#x3b6;" horiz-adv-x="486" 
d="M265 -29q0 17 -29 17q-102 0 -159.5 47.5t-57.5 130.5q0 112 91 214.5t244 175.5h-234l26 111h399l-21 -92q-158 -76 -263.5 -178t-105.5 -203q0 -93 123 -93q128 0 128 -80q0 -64 -92 -178h-140q35 33 63 71t28 57z" />
    <glyph glyph-name="eta" unicode="&#x3b7;" 
d="M420 -184h-127l107 477q4 18 4 27q0 62 -83 62q-55 0 -113 -56l-72 -326h-127l107 483h127l-14 -63q77 75 161 75q69 0 110 -33.5t41 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="theta" unicode="&#x3b8;" horiz-adv-x="574" 
d="M266 -12q-103 0 -164 71t-61 198q0 109 39 205t112 156.5t163 60.5q103 0 163.5 -71t60.5 -199q0 -109 -38.5 -204.5t-111.5 -156t-163 -60.5zM274 101q49 0 88 50.5t63 127.5h-254q-2 -15 -2 -42q0 -62 26 -99t79 -37zM345 566q-48 0 -87 -49.5t-64 -125.5h253
q2 26 2 38q0 63 -25.5 100t-78.5 37z" />
    <glyph glyph-name="iota" unicode="&#x3b9;" horiz-adv-x="265" 
d="M141 -12q-136 0 -107 132l83 363h128l-78 -338q-9 -44 31 -44q8 0 20 2l-16 -108q-30 -7 -61 -7z" />
    <glyph glyph-name="kappa" unicode="&#x3ba;" horiz-adv-x="535" 
d="M481 0h-154l-89 189l-75 -65l-27 -124h-127l107 483h127l-47 -207l224 207h162l-240 -219z" />
    <glyph glyph-name="lambda" unicode="&#x3bb;" horiz-adv-x="513" 
d="M138 557l7 114q27 8 64 8q68 -2 107 -27.5t50 -89.5l99 -562h-132l-51 336l-202 -336h-140l307 489l-8 41q-8 36 -60 36q-23 0 -41 -9z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="591" 
d="M138 15l-45 -199h-128l152 667h127l-66 -292q-10 -45 9.5 -68t59.5 -23q65 0 121 56l72 327h128l-75 -338q-9 -44 31 -44q8 0 20 2l-16 -108q-30 -7 -61 -7q-105 0 -110 86q-70 -86 -155 -86q-49 0 -64 27z" />
    <glyph glyph-name="nu" unicode="&#x3bd;" horiz-adv-x="513" 
d="M262 0h-128l-84 483h136l51 -356q65 73 115 170t70 186h128q-27 -119 -108 -253t-180 -230z" />
    <glyph glyph-name="xi" unicode="&#x3be;" horiz-adv-x="486" 
d="M239 -12q-113 0 -170.5 40.5t-57.5 114.5q0 68 46 112.5t114 62.5q-37 7 -62 31.5t-25 67.5q0 41 37 81t89 58h-86l27 111h390l-26 -111h-190q-42 -11 -71 -38.5t-29 -60.5q0 -30 28 -47t75 -17h146l-25 -109h-144q-71 0 -112.5 -28t-41.5 -80q0 -36 33 -55.5t94 -19.5
q128 0 128 -80q0 -64 -92 -178h-135q31 33 58.5 71t27.5 57q0 17 -26 17z" />
    <glyph glyph-name="omicron" unicode="&#x3bf;" horiz-adv-x="574" 
d="M266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="631" 
d="M489 0h-128l85 372h-200l-84 -372h-128l85 372h-80l25 111h615l-26 -111h-80z" />
    <glyph glyph-name="rho" unicode="&#x3c1;" horiz-adv-x="583" 
d="M304 -12q-47 0 -88.5 20t-65.5 56l-56 -248h-127l96 426q25 116 99.5 184.5t185.5 68.5q98 0 154 -59t56 -154q0 -51 -17 -102t-48 -94.5t-80.5 -70.5t-108.5 -27zM283 101q62 0 102 49.5t40 118.5q0 49 -26 81t-70 32q-54 0 -90 -39t-50 -101l-19 -86q36 -55 113 -55z
" />
    <glyph glyph-name="sigma1" unicode="&#x3c2;" horiz-adv-x="492" 
d="M245 -12q-90 0 -153.5 60.5t-63.5 159.5q0 120 81 203.5t200 83.5q139 0 196 -95l-95 -73q-32 55 -101 55q-67 0 -109.5 -49t-42.5 -119q0 -113 144 -113q52 0 81.5 -21t29.5 -62q0 -60 -91 -175h-138q33 31 62 70t29 58q0 17 -29 17z" />
    <glyph glyph-name="sigma" unicode="&#x3c3;" horiz-adv-x="574" 
d="M534 260q0 -108 -76 -190t-192 -82q-113 0 -176 61t-63 158q0 116 88 196t222 80h273l-25 -113h-90q39 -41 39 -110zM414 265q0 76 -43 105h-48q-74 0 -119 -46t-45 -110q0 -53 29.5 -83t82.5 -30q62 0 102.5 49.5t40.5 114.5z" />
    <glyph glyph-name="tau" unicode="&#x3c4;" horiz-adv-x="482" 
d="M280 -12q-70 0 -106.5 34t-21.5 101l55 249h-156l26 111h440l-25 -111h-157l-49 -221q-5 -24 7.5 -37t34.5 -13q26 0 40 12l5 -100q-32 -25 -93 -25z" />
    <glyph glyph-name="upsilon" unicode="&#x3c5;" horiz-adv-x="577" 
d="M260 -12q-121 0 -172.5 67t-27.5 174l59 254h127l-60 -263q-12 -55 8 -87t71 -32q72 0 119 66t47 160q0 72 -25 124l123 44q31 -59 31 -158q0 -148 -84 -248.5t-216 -100.5z" />
    <glyph glyph-name="phi" unicode="&#x3c6;" horiz-adv-x="795" 
d="M771 270q0 -116 -97 -194.5t-270 -86.5l-39 -173h-127l40 177q-123 14 -186.5 72t-63.5 156q0 93 66 162t182 112l31 -99q-69 -25 -107 -69.5t-38 -98.5q0 -107 141 -124l89 391h63q152 0 234 -58.5t82 -166.5zM493 379l-63 -275q105 11 155.5 54.5t50.5 105.5
q0 52 -37.5 82t-105.5 33z" />
    <glyph glyph-name="chi" unicode="&#x3c7;" horiz-adv-x="504" 
d="M408 -184h-136l-61 224l-163 -224h-144l249 343l-87 324h136l54 -205l148 205h145l-236 -324z" />
    <glyph glyph-name="psi" unicode="&#x3c8;" horiz-adv-x="795" 
d="M365 -184h-127l40 177q-220 26 -220 194q0 30 8 68l53 228h127l-55 -243q-4 -13 -4 -34q0 -83 117 -101l128 562h127l-128 -562q148 23 175 135l55 243h127l-59 -260q-23 -100 -107 -163t-218 -71z" />
    <glyph glyph-name="omega" unicode="&#x3c9;" horiz-adv-x="852" 
d="M832 316q0 -132 -77 -230t-187 -98q-140 0 -163 137q-40 -67 -86 -102t-115 -35q-84 0 -132 56.5t-48 147.5q0 80 47.5 163.5t122.5 139.5l84 -76q-123 -96 -123 -218q0 -44 20 -72t59 -28q47 0 81.5 40t47.5 101l27 122h128l-27 -122q-14 -60 3.5 -100.5t66.5 -40.5
q63 0 102.5 62t39.5 144q0 72 -38 123l109 65q58 -63 58 -179z" />
    <glyph glyph-name="iotadieresis" unicode="&#x3ca;" horiz-adv-x="265" 
d="M384 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM173 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM141 -12q-136 0 -107 132l83 363h128l-78 -338
q-9 -44 31 -44q8 0 20 2l-16 -108q-30 -7 -61 -7z" />
    <glyph glyph-name="upsilondieresis" unicode="&#x3cb;" horiz-adv-x="577" 
d="M553 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM342 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM260 -12q-121 0 -172.5 67t-27.5 174l59 254h127l-60 -263
q-12 -55 8 -87t71 -32q72 0 119 66t47 160q0 72 -25 124l123 44q31 -59 31 -158q0 -148 -84 -248.5t-216 -100.5z" />
    <glyph glyph-name="omicrontonos" unicode="&#x3cc;" horiz-adv-x="574" 
d="M380 553h-56l69 212h105zM266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30z" />
    <glyph glyph-name="upsilontonos" unicode="&#x3cd;" horiz-adv-x="577" 
d="M390 553h-56l69 212h105zM260 -12q-121 0 -172.5 67t-27.5 174l59 254h127l-60 -263q-12 -55 8 -87t71 -32q72 0 119 66t47 160q0 72 -25 124l123 44q31 -59 31 -158q0 -148 -84 -248.5t-216 -100.5z" />
    <glyph glyph-name="omegatonos" unicode="&#x3ce;" horiz-adv-x="852" 
d="M520 553h-56l69 212h105zM832 316q0 -132 -77 -230t-187 -98q-140 0 -163 137q-40 -67 -86 -102t-115 -35q-84 0 -132 56.5t-48 147.5q0 80 47.5 163.5t122.5 139.5l84 -76q-123 -96 -123 -218q0 -44 20 -72t59 -28q47 0 81.5 40t47.5 101l27 122h128l-27 -122
q-14 -60 3.5 -100.5t66.5 -40.5q63 0 102.5 62t39.5 144q0 72 -38 123l109 65q58 -63 58 -179z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="583" 
d="M595 785q0 -29 -23 -52t-53 -23q-25 0 -42.5 17t-17.5 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM384 785q0 -29 -23 -52t-53 -23q-26 0 -43 17t-17 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322
l-33 -151h329z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="776" 
d="M457 -12l27 123q45 0 76.5 23t39.5 62l1 8q10 45 -16 69.5t-85 24.5q-67 0 -131 -22l-60 -276h-143l119 542h-195l27 125h532l-27 -125h-194l-32 -141q78 22 149 22q121 0 170.5 -61.5t29.5 -157.5l-2 -8q-20 -98 -100 -153t-186 -55z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="571" 
d="M633 867l-194 -144h-91l166 144h119zM154 0h-142l147 667h472l-28 -125h-329z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="687" 
d="M372 -12q-141 0 -234.5 82.5t-93.5 216.5q0 172 114 281.5t274 109.5q115 0 189.5 -53t106.5 -136l-137 -46q-19 52 -63.5 80.5t-102.5 28.5q-70 0 -128 -41.5t-85 -109.5h326l-28 -125h-317q7 -72 57 -117t130 -45q43 0 86 21t71 56l109 -74q-53 -68 -126.5 -98.5
t-147.5 -30.5z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="600" 
d="M301 -12q-97 0 -178 34t-124 90l88 104q35 -47 96 -75.5t129 -28.5q49 0 74.5 23.5t25.5 52.5q0 25 -33.5 44.5t-81 36.5t-95 38t-81 60t-33.5 93q0 87 71.5 152t189.5 65q80 0 150.5 -28.5t114.5 -79.5l-88 -100q-36 41 -90.5 63t-108.5 22q-38 0 -63.5 -20t-25.5 -48
q0 -23 33.5 -42t81.5 -35.5t96 -38t81.5 -61t33.5 -93.5q0 -96 -71.5 -162t-191.5 -66z" />
    <glyph glyph-name="afii10055" unicode="&#x406;" horiz-adv-x="274" 
d="M154 0h-142l147 667h142z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="274" 
d="M428 785q0 -29 -23 -52t-53 -23q-26 0 -43 17t-17 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM217 785q0 -29 -23 -52t-53 -23q-25 0 -42.5 17t-17.5 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM154 0h-142l147 667h142z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="486" 
d="M165 -12q-59 0 -111 19t-83 56l77 106q36 -55 107 -55q93 0 118 107l98 446h142l-98 -448q-26 -119 -86.5 -175t-163.5 -56z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="1081" 
d="M-31 -12l27 126q55 0 93 43t81 169l114 341h474l-53 -237h152q90 0 143.5 -53.5t53.5 -131.5q0 -40 -14.5 -80.5t-44 -78.5t-86 -62t-130.5 -24h-310l119 542h-203l-76 -226q-34 -101 -72.5 -167.5t-82.5 -100.5t-86.5 -47t-98.5 -13zM823 305h-145l-40 -180h147
q54 0 87.5 30t33.5 75q0 32 -23 53.5t-60 21.5z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="1081" 
d="M787 0h-318l62 288h-314l-63 -288h-142l146 667h142l-56 -254h315l56 254h143l-56 -254h160q87 0 138.5 -51t51.5 -127q0 -39 -13.5 -77.5t-42.5 -75t-83 -59.5t-126 -23zM829 288h-155l-36 -163h158q49 0 79 27t30 68q0 30 -21 49t-55 19z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="807" 
d="M557 0l38 177q28 121 -95 121q-67 0 -131 -22l-60 -276h-143l119 542h-195l27 125h532l-27 -125h-194l-32 -141q78 22 149 22q123 0 171 -66t23 -180l-38 -177h-144z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="636" 
d="M654 867l-194 -144h-91l166 144h119zM583 0h-167l-147 273l-69 -68l-46 -205h-142l147 667h142l-63 -286l291 286h183l-334 -313z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="645" 
d="M99 -12q-45 0 -83.5 13.5t-56.5 34.5l78 114q36 -36 79 -36q34 0 57 16t53 57l-138 480h156l88 -353l243 353h166l-393 -539q-49 -68 -107.5 -104t-141.5 -36zM607 786q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="731" 
d="M212 -127l27 127h-227l146 667h142l-119 -542h315l119 542h143l-146 -667h-230l-28 -127h-142z" />
    <glyph glyph-name="afii10017" unicode="&#x410;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" horiz-adv-x="624" 
d="M158 667h487l-27 -125h-345l-26 -118h142q97 0 152.5 -52.5t55.5 -130.5q0 -40 -14 -79.5t-44 -77t-86 -61t-130 -23.5h-311zM370 299h-150l-39 -174h152q53 0 84.5 29t31.5 73q0 31 -22 51.5t-57 20.5z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="663" 
d="M376 0h-364l147 667h324q84 0 132.5 -46.5t48.5 -113.5t-45 -117.5t-107 -56.5q45 -12 70.5 -50.5t25.5 -84.5q0 -79 -58 -138.5t-174 -59.5zM422 400q47 0 68.5 24.5t21.5 58.5q0 26 -19.5 44t-48.5 18h-169l-32 -145h179zM365 123q46 0 73.5 25t27.5 62q0 30 -20 49
t-53 19h-178l-34 -155h184z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="571" 
d="M154 0h-142l147 667h472l-28 -125h-329z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="743" 
d="M-55 -123l51 237q58 8 95 48.5t79 163.5l114 341h474l-120 -542h61l-55 -248h-142l27 123h-416l-27 -123h-141zM385 542l-76 -226q-43 -128 -123 -191h310l92 417h-203z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="583" 
d="M484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="951" 
d="M130 0h-184l359 354l-196 313h165l160 -281l62 281h143l-56 -255l257 255h186l-334 -313l205 -354h-164l-147 274l-42 -40l-51 -234h-143l51 233l-17 28z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="610" 
d="M296 -12q-97 0 -179.5 37t-118.5 102l94 86q31 -47 87.5 -73t114.5 -26q59 0 92.5 26t33.5 66q0 31 -31.5 50.5t-97.5 19.5h-133l28 125h146q137 0 137 77q0 33 -37.5 53t-100.5 20q-101 0 -168 -54l-51 97q41 38 103.5 60.5t129.5 22.5q122 0 196.5 -46.5t74.5 -127.5
q0 -64 -55 -110t-125 -55q56 -14 90 -50t34 -96q0 -87 -77.5 -145.5t-186.5 -58.5z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="729" 
d="M149 0h-137l147 667h142l-95 -434l404 434h146l-147 -667h-143l100 450z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="729" 
d="M149 0h-137l147 667h142l-95 -434l404 434h146l-147 -667h-143l100 450zM655 786q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="636" 
d="M583 0h-167l-147 273l-69 -68l-46 -205h-142l147 667h142l-63 -286l291 286h183l-334 -313z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="731" 
d="M-31 -12l27 126q55 0 93 43t81 169l114 341h474l-146 -667h-143l119 542h-203l-76 -226q-34 -101 -72.5 -167.5t-82.5 -100.5t-86.5 -47t-98.5 -13z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="855" 
d="M735 0h-143l106 481l-294 -481h-62l-82 481l-106 -481h-142l147 667h194l69 -417l255 417h205z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="731" 
d="M612 0h-143l62 280h-315l-62 -280h-142l147 667h142l-57 -262h314l58 262h143z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5z" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="731" 
d="M612 0h-143l119 542h-314l-120 -542h-142l147 667h600z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" horiz-adv-x="624" 
d="M154 0h-142l147 667h293q90 0 144 -53.5t54 -131.5q0 -40 -14.5 -80.5t-44.5 -78.5t-86.5 -62t-130.5 -24h-167zM234 362h149q52 0 85 30t33 75q0 33 -23 54t-60 21h-144z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="687" 
d="M372 -12q-141 0 -234.5 82.5t-93.5 216.5q0 172 114 281.5t274 109.5q115 0 189.5 -53t106.5 -136l-137 -46q-19 52 -63.5 80.5t-102.5 28.5q-96 0 -164.5 -74t-68.5 -182q0 -79 51.5 -130.5t136.5 -51.5q43 0 86 21t71 56l109 -74q-53 -68 -126.5 -98.5t-147.5 -30.5z
" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="582" 
d="M309 0h-143l119 542h-195l28 125h532l-27 -125h-195z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="645" 
d="M99 -12q-45 0 -83.5 13.5t-56.5 34.5l78 114q36 -36 79 -36q34 0 57 16t53 57l-138 480h156l88 -353l243 353h166l-393 -539q-49 -68 -107.5 -104t-141.5 -36z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="855" 
d="M444 0h-142l16 69q-269 30 -269 240q0 122 108.5 202t281.5 89l15 67h142l-16 -70q269 -30 269 -241q0 -121 -109 -201t-281 -90zM700 357q0 95 -149 115l-63 -279q102 11 157 54t55 110zM198 309q0 -95 148 -115l63 278q-101 -11 -156 -54t-55 -109z" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="668" 
d="M617 0h-161l-121 237l-214 -237h-178l321 351l-159 316h161l109 -221l195 221h179l-303 -333z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="743" 
d="M502 -123l27 123h-517l146 667h142l-119 -542h315l119 542h143l-120 -542h61l-55 -248h-142z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="649" 
d="M284 667l-41 -187q-14 -63 11 -92t85 -29q71 0 131 22l63 286h142l-146 -667h-142l55 256q-76 -22 -148 -22q-124 0 -172 65.5t-22 180.5l41 187h143z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="983" 
d="M866 667h143l-146 -667h-851l146 667h142l-119 -542h212l119 542h143l-120 -542h212z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="994" 
d="M866 667h143l-119 -542h60l-54 -248h-143l27 123h-768l146 667h142l-119 -542h212l119 542h143l-120 -542h212z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="778" 
d="M476 0h-310l119 542h-195l27 125h338l-53 -237h152q90 0 143.5 -53.5t53.5 -131.5q0 -40 -14.5 -80.5t-44 -78.5t-86 -62t-130.5 -24zM520 305h-145l-40 -180h147q54 0 87.5 30t33.5 75q0 32 -23 53.5t-60 21.5z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="867" 
d="M747 0h-143l146 667h143zM158 667h142l-52 -237h152q90 0 143.5 -53.5t53.5 -131.5q0 -40 -14.5 -80.5t-44 -78.5t-86 -62t-130.5 -24h-310zM366 305h-145l-40 -180h147q54 0 87.5 30t33.5 75q0 32 -23 53.5t-60 21.5z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" horiz-adv-x="624" 
d="M158 667h142l-52 -237h152q90 0 143.5 -53.5t53.5 -131.5q0 -40 -14.5 -80.5t-44 -78.5t-86 -62t-130.5 -24h-310zM366 305h-145l-40 -180h147q54 0 87.5 30t33.5 75q0 32 -23 53.5t-60 21.5z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="687" 
d="M353 678q141 0 234 -82.5t93 -216.5q0 -172 -113.5 -281.5t-273.5 -109.5q-115 0 -190 53t-107 136l137 46q19 -52 63.5 -80.5t103.5 -28.5q72 0 131 44.5t85 117.5h-327l28 125h313q-10 67 -59.5 109t-125.5 42q-43 0 -86 -21t-71 -55l-110 74q53 67 127 97.5t148 30.5z
" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="1043" 
d="M649 -12q-140 0 -232.5 80t-95.5 212h-106l-61 -280h-142l146 667h142l-57 -262h95q37 121 138 197t233 76q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM657 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5
q0 -83 52.5 -132.5t135.5 -49.5z" />
    <glyph glyph-name="afii10049" unicode="&#x42f;" horiz-adv-x="643" 
d="M162 0h-179l209 255q-44 19 -75.5 63t-31.5 104q0 41 14.5 81t44.5 78.5t86 62t131 23.5h309l-147 -667h-142l52 237h-88zM316 362h145l39 180h-148q-53 0 -86 -30t-33 -75q0 -32 23 -53.5t60 -21.5z" />
    <glyph glyph-name="afii10065" unicode="&#x430;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="577" 
d="M271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30zM266 -12q-113 0 -175 62t-62 166q0 47 16 104.5t37 96.5q51 93 121.5 144t179.5 67q96 16 101 39h132q-23 -104 -230 -130q-153 -20 -200 -121q72 77 169 77
q90 0 140.5 -60.5t50.5 -157.5q0 -116 -80 -201.5t-200 -85.5z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="586" 
d="M342 0h-333l106 483h294q62 0 97.5 -29.5t35.5 -77.5q0 -49 -30.5 -85t-78.5 -46q68 -28 68 -98q0 -61 -43.5 -104t-115.5 -43zM159 104h162q24 0 39.5 15t15.5 36q0 18 -13 28.5t-35 10.5h-149zM201 298h157q22 0 36 13.5t14 32.5q0 16 -11.5 25.5t-31.5 9.5h-147z" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="454" 
d="M488 483l-25 -111h-246l-81 -372h-127l106 483h373z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="587" 
d="M-62 -123l49 229q33 7 57 34.5t52 108.5l82 234h391l-82 -372h53l-51 -234h-128l27 123h-297l-27 -123h-126zM264 372l-44 -131q-28 -86 -84 -130h224l56 261h-152z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="553" 
d="M278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="750" 
d="M113 0h-166l254 264l-142 219h148l115 -184l41 184h128l-36 -165l176 165h164l-239 -219l139 -264h-150l-91 191l-34 -30l-35 -161h-128l35 160l-15 21z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="504" 
d="M289 495q210 0 210 -125q0 -57 -52.5 -92.5t-107.5 -39.5q47 -8 77 -34t30 -63q0 -66 -69.5 -109.5t-171.5 -43.5q-72 0 -129.5 22.5t-89.5 58.5l76 74q53 -54 141 -54q49 0 81 17t32 43q0 46 -83 46h-109l23 98h99q118 0 118 58q0 43 -87 43q-91 0 -174 -54l-47 80
q100 75 233 75z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="580" 
d="M131 0h-122l106 483h127l-65 -292l264 292h128l-106 -483h-127l66 303z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="580" 
d="M131 0h-122l106 483h127l-65 -292l264 292h128l-106 -483h-127l66 303zM543 619q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="535" 
d="M481 0h-154l-89 189l-75 -65l-27 -124h-127l107 483h127l-47 -207l224 207h162l-240 -219z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="580" 
d="M-39 -12l23 104q30 0 56 35.5t56 121.5l82 234h391l-106 -483h-127l80 372h-152l-46 -138q-42 -126 -104 -186t-153 -60z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="697" 
d="M580 0h-127l68 314l-197 -314h-58l-62 314l-68 -314h-127l106 483h162l55 -288l181 288h173z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" 
d="M136 0h-127l110 483h127l-40 -175h200l40 175h127l-110 -483h-127l44 196h-199z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" horiz-adv-x="574" 
d="M266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" 
d="M463 0h-127l84 372h-199l-85 -372h-127l110 483h454z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" horiz-adv-x="583" 
d="M304 -12q-103 0 -154 76l-55 -248h-127l148 667h127l-14 -60q61 72 146 72q83 0 133 -53t50 -147q0 -75 -28.5 -144t-88 -116t-137.5 -47zM283 101q62 0 102 49.5t40 118.5q0 51 -30 82t-77 31q-64 0 -111 -58l-37 -168q36 -55 113 -55z" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="499" 
d="M265 -12q-108 0 -172.5 60.5t-64.5 159.5q0 120 81 203.5t200 83.5q139 0 196 -95l-95 -73q-32 55 -101 55q-67 0 -109.5 -49t-42.5 -119q0 -53 34.5 -83t86.5 -30q60 0 101 48l69 -85q-72 -76 -183 -76z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="432" 
d="M226 0h-128l81 372h-135l25 111h398l-25 -111h-135z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="513" 
d="M-48 -184l42 110q18 -8 49 -8q37 0 60 34l30 45l-86 486h132l51 -336l202 336h140l-352 -562q-39 -64 -82.5 -90.5t-104.5 -26.5q-46 0 -81 12z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="841" 
d="M258 495q41 0 78.5 -19.5t57.5 -54.5l55 246h127l-54 -245q55 73 134 73q73 0 112 -55t39 -133t-27 -150t-80.5 -120.5t-120.5 -48.5q-41 0 -78.5 19.5t-57.5 54.5l-54 -246h-127l54 246q-52 -74 -135 -74q-73 0 -112 55t-39 133t27 150t80.5 120.5t120.5 48.5zM286 382
q-53 0 -88 -56.5t-35 -124.5q0 -43 20.5 -71.5t57.5 -28.5q52 0 96 55l37 171q-10 24 -34 39.5t-54 15.5zM552 101q52 0 87.5 56.5t35.5 124.5q0 44 -20.5 72t-58.5 28q-51 0 -94 -55l-38 -171q9 -23 33.5 -39t54.5 -16z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="504" 
d="M450 0h-138l-74 163l-147 -163h-145l225 248l-108 235h137l67 -149l133 149h146l-215 -235z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="587" 
d="M361 -123l27 123h-379l106 483h127l-82 -372h200l82 372h127l-82 -372h53l-51 -234h-128z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="564" 
d="M100 483h127l-29 -131q-11 -49 9.5 -69.5t72.5 -20.5q59 0 102 19l44 202h128l-106 -483h-128l39 179q-62 -30 -148 -30q-98 0 -132.5 40.5t-17.5 118.5z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="823" 
d="M684 483h128l-106 -483h-697l106 483h127l-82 -372h158l82 372h127l-82 -372h158z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="836" 
d="M684 483h128l-82 -372h53l-51 -234h-128l27 123h-622l106 483h127l-82 -372h158l82 372h127l-82 -372h158z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="643" 
d="M406 0h-308l81 372h-135l25 111h263l-37 -166h135q76 0 120 -38.5t44 -100.5q0 -72 -51.5 -125t-136.5 -53zM409 211h-137l-23 -104h151q28 0 46 17t18 42q0 45 -55 45z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="776" 
d="M660 0h-128l106 483h128zM115 483h127l-37 -166h135q76 0 120 -38.5t44 -100.5q0 -72 -51 -125t-136 -53h-308zM319 211h-137l-23 -104h151q29 0 46.5 17t17.5 42q0 45 -55 45z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="553" 
d="M115 483h127l-37 -166h135q76 0 120 -38.5t44 -100.5q0 -72 -51 -125t-136 -53h-308zM319 211h-137l-23 -104h151q29 0 46.5 17t17.5 42q0 45 -55 45z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="499" 
d="M232 495q108 0 173 -60.5t65 -159.5q0 -120 -81.5 -203.5t-199.5 -83.5q-140 0 -197 95l90 68q15 -25 43.5 -40t61.5 -15q48 0 85 27t55 71h-200l22 98h188q-7 44 -39 69t-77 25q-64 0 -107 -46l-65 79q72 76 183 76z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="809" 
d="M501 -12q-108 0 -171.5 56.5t-68.5 148.5h-83l-42 -193h-127l106 483h127l-39 -179h74q30 84 100.5 137.5t162.5 53.5q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-199 -85.5zM505 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5
q0 -52 30 -82.5t83 -30.5z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" horiz-adv-x="573" 
d="M142 0h-149l156 182q-36 14 -58.5 46t-22.5 77q0 72 51.5 125t136.5 53h308l-106 -483h-127l36 166h-94zM253 272h137l23 104h-151q-28 0 -46 -17t-18 -42q0 -45 55 -45z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="553" 
d="M540 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM329 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85
q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="584" 
d="M401 528h-146l-23 -108q79 75 186 75q78 0 108.5 -40t15.5 -112l-79 -356q-18 -85 -72.5 -134t-139.5 -49q-44 0 -66.5 7.5t-46.5 26.5l56 95q23 -25 59 -25q65 0 83 79l67 307q11 48 -7.5 68t-62.5 20q-67 0 -122 -57l-71 -325h-128l116 528h-58l15 73h58l16 66h127
l-15 -66h144z" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="454" 
d="M545 700l-194 -144h-91l166 144h119zM488 483l-25 -111h-246l-81 -372h-127l106 483h373z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="499" 
d="M265 -12q-108 0 -172.5 60.5t-64.5 159.5q0 120 81 203.5t200 83.5q139 0 196 -95l-89 -68q-35 55 -106 55q-47 0 -83.5 -26t-54.5 -69h199l-21 -98h-191q7 -46 39.5 -71.5t78.5 -25.5q65 0 106 46l65 -79q-72 -76 -183 -76z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="477" 
d="M219 -12q-67 0 -127.5 22.5t-100.5 64.5l71 85q23 -28 71.5 -52t97.5 -24q31 0 50.5 15.5t19.5 35.5q0 23 -38.5 40.5t-84 30t-84 45t-38.5 81.5q0 67 53.5 115t145.5 48q61 0 115.5 -21.5t89.5 -57.5l-65 -81q-19 24 -59.5 43.5t-82.5 19.5q-32 0 -51 -13.5t-19 -33.5
q0 -16 25 -29t61 -24t72.5 -26t61.5 -44.5t25 -70.5q0 -72 -56.5 -120.5t-152.5 -48.5z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="253" 
d="M198 535q-29 0 -48.5 19t-19.5 46q0 39 26.5 62.5t58.5 23.5q30 0 49.5 -19t19.5 -46q0 -39 -27 -62.5t-59 -23.5zM136 0h-127l107 483h127z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="253" 
d="M381 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM170 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM136 0h-127l107 483h127z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="253" 
d="M-54 -196q-90 0 -134 41l53 91q22 -28 63 -28q59 0 77 79l111 496h127l-111 -496q-19 -88 -63 -135.5t-123 -47.5zM198 535q-29 0 -48.5 19t-19.5 46q0 39 26.5 62.5t58.5 23.5q30 0 49.5 -19t19.5 -46q0 -39 -27 -62.5t-59 -23.5z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="880" 
d="M-39 -12l23 104q30 0 56 35.5t56 121.5l82 234h391l-37 -166h135q76 0 120 -38.5t44 -100.5q0 -72 -51 -125t-136 -53h-308l80 372h-152l-46 -138q-42 -126 -104 -186t-153 -60zM646 211h-137l-23 -104h151q29 0 46.5 17t17.5 42q0 45 -55 45z" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="880" 
d="M136 0h-127l106 483h127l-39 -176h200l39 176h127l-39 -176h142q73 0 116 -37t43 -97q0 -70 -49.5 -121.5t-132.5 -51.5h-313l42 196h-199zM650 200h-143l-21 -93h156q26 0 42 15t16 38q0 40 -50 40z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="584" 
d="M465 0h-127l63 290q5 20 5 30q0 62 -83 62q-56 0 -113 -57l-72 -325h-127l116 528h-58l17 73h57l15 66h127l-15 -66h146l-16 -73h-146l-23 -108q77 75 161 75q69 0 110 -33.5t41 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" horiz-adv-x="535" 
d="M565 700l-194 -144h-91l166 144h119zM481 0h-154l-89 189l-75 -65l-27 -124h-127l107 483h127l-47 -207l224 207h162l-240 -219z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="513" 
d="M-48 -184l42 110q18 -8 49 -8q37 0 60 34l30 45l-86 486h132l51 -336l202 336h140l-352 -562q-39 -64 -82.5 -90.5t-104.5 -26.5q-46 0 -81 12zM507 619q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="580" 
d="M144 -126l28 126h-163l106 483h127l-82 -372h200l82 372h127l-106 -483h-164l-27 -126h-128z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="571" 
d="M154 0h-142l146 667h329l27 123h143l-54 -248h-330z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="454" 
d="M217 372l-81 -372h-127l106 483h246l28 126h127l-53 -237h-246z" />
    <glyph glyph-name="uni1E02" unicode="&#x1e02;" horiz-adv-x="663" 
d="M376 0h-364l147 667h324q84 0 132.5 -46.5t48.5 -113.5t-45 -117.5t-107 -56.5q45 -12 70.5 -50.5t25.5 -84.5q0 -79 -58 -138.5t-174 -59.5zM422 400q47 0 68.5 24.5t21.5 58.5q0 26 -19.5 44t-48.5 18h-169l-32 -145h179zM365 123q46 0 73.5 25t27.5 62q0 30 -20 49
t-53 19h-178l-34 -155h184zM515 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E03" unicode="&#x1e03;" horiz-adv-x="586" 
d="M304 -12q-103 0 -154 76l-14 -64h-127l147 667h127l-54 -244q61 72 146 72q83 0 133 -53t50 -147q0 -75 -28.5 -144t-88 -116t-137.5 -47zM283 101q62 0 102 49.5t40 118.5q0 51 -30 82t-77 31q-64 0 -111 -58l-37 -168q36 -55 113 -55zM488 785q0 -30 -23.5 -52.5
t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E0A" unicode="&#x1e0a;" horiz-adv-x="718" 
d="M274 0h-262l147 667h258q116 0 202.5 -81t86.5 -206q0 -54 -14.5 -106.5t-48 -102.5t-82 -87.5t-122.5 -60.5t-165 -23zM295 125h5q117 0 187.5 71.5t70.5 174.5q0 73 -47 122t-121 49h-116l-92 -417h113zM549 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43
q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E0B" unicode="&#x1e0b;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l54 248h128l-148 -667h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM473 785q0 -30 -23.5 -52.5
t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E1E" unicode="&#x1e1e;" horiz-adv-x="571" 
d="M488 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5zM154 0h-142l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322z" />
    <glyph glyph-name="uni1E1F" unicode="&#x1e1f;" horiz-adv-x="327" 
d="M423 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5zM165 0h-127l82 373h-80l25 110h79l6 27q17 75 63 121t123 46q81 0 127 -47l-63 -74q-21 21 -54 21q-54 0 -69 -69l-6 -25h99l-25 -110h-98z" />
    <glyph glyph-name="uni1E22" unicode="&#x1e22;" horiz-adv-x="731" 
d="M612 0h-143l62 280h-315l-62 -280h-142l147 667h142l-57 -262h314l58 262h143zM551 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E23" unicode="&#x1e23;" horiz-adv-x="580" 
d="M463 0h-127l63 290q5 20 5 30q0 62 -83 62q-56 0 -113 -57l-72 -325h-127l147 667h127l-54 -247q77 75 161 75q69 0 110 -33.5t41 -89.5q0 -15 -5 -40zM478 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z
" />
    <glyph glyph-name="uni1E56" unicode="&#x1e56;" horiz-adv-x="624" 
d="M154 0h-142l147 667h293q90 0 144 -53.5t54 -131.5q0 -40 -14.5 -80.5t-44.5 -78.5t-86.5 -62t-130.5 -24h-167zM234 362h149q52 0 85 30t33 75q0 33 -23 54t-60 21h-144zM511 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23
q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E57" unicode="&#x1e57;" horiz-adv-x="583" 
d="M304 -12q-103 0 -154 76l-55 -248h-127l148 667h127l-14 -60q61 72 146 72q83 0 133 -53t50 -147q0 -75 -28.5 -144t-88 -116t-137.5 -47zM283 101q62 0 102 49.5t40 118.5q0 51 -30 82t-77 31q-64 0 -111 -58l-37 -168q36 -55 113 -55zM451 618q0 -30 -23.5 -52.5
t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E60" unicode="&#x1e60;" horiz-adv-x="600" 
d="M301 -12q-97 0 -178 34t-124 90l88 104q35 -47 96 -75.5t129 -28.5q49 0 74.5 23.5t25.5 52.5q0 25 -33.5 44.5t-81 36.5t-95 38t-81 60t-33.5 93q0 87 71.5 152t189.5 65q80 0 150.5 -28.5t114.5 -79.5l-88 -100q-36 41 -90.5 63t-108.5 22q-38 0 -63.5 -20t-25.5 -48
q0 -23 33.5 -42t81.5 -35.5t96 -38t81.5 -61t33.5 -93.5q0 -96 -71.5 -162t-191.5 -66zM492 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E61" unicode="&#x1e61;" horiz-adv-x="477" 
d="M219 -12q-67 0 -127.5 22.5t-100.5 64.5l71 85q23 -28 71.5 -52t97.5 -24q31 0 50.5 15.5t19.5 35.5q0 23 -38.5 40.5t-84 30t-84 45t-38.5 81.5q0 67 53.5 115t145.5 48q61 0 115.5 -21.5t89.5 -57.5l-65 -81q-19 24 -59.5 43.5t-82.5 19.5q-32 0 -51 -13.5t-19 -33.5
q0 -16 25 -29t61 -24t72.5 -26t61.5 -44.5t25 -70.5q0 -72 -56.5 -120.5t-152.5 -48.5zM392 618q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E6A" unicode="&#x1e6a;" horiz-adv-x="582" 
d="M309 0h-143l119 542h-195l28 125h532l-27 -125h-195zM478 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E6B" unicode="&#x1e6b;" horiz-adv-x="338" 
d="M203 -12q-67 0 -105.5 26.5t-38.5 78.5q0 17 4 33l54 247h-80l25 110h80l29 132h128l-29 -132h98l-26 -110h-98l-46 -210q-2 -12 -2 -20q0 -42 47 -42q23 0 35 10l5 -102q-31 -21 -80 -21zM341 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53
t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="918" 
d="M673 0h-151l-16 458l-217 -458h-151l-44 667h156l10 -482l233 482h111l20 -482l223 482h163zM625 723h-89l-130 144h117z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="769" 
d="M568 0h-135l-30 329l-174 -329h-136l-40 483h127l19 -325l178 325h113l33 -325l163 325h136zM515 556h-89l-130 144h117z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="918" 
d="M778 867l-194 -144h-91l166 144h119zM673 0h-151l-16 458l-217 -458h-151l-44 667h156l10 -482l233 482h111l20 -482l223 482h163z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="769" 
d="M670 700l-194 -144h-91l166 144h119zM568 0h-135l-30 329l-174 -329h-136l-40 483h127l19 -325l178 325h113l33 -325l163 325h136z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="918" 
d="M746 785q0 -29 -23 -52t-53 -23q-26 0 -43 17t-17 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM535 785q0 -29 -23 -52t-53 -23q-25 0 -42.5 17t-17.5 43q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM673 0h-151l-16 458l-217 -458h-151l-44 667h156l10 -482l233 482
h111l20 -482l223 482h163z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="769" 
d="M637 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM426 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM568 0h-135l-30 329l-174 -329h-136l-40 483h127l19 -325
l178 325h113l33 -325l163 325h136z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM328 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM273 -121q0 -30 -23.5 -52.5
t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM418 753l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM327 583l-40 20q25 47 81 47
q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="684" 
d="M583 682h-76l-54 67l-90 -67h-80l124 108h114zM634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM844 844l-194 -108h-91l166 108h119z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM507 556h-76l-54 89l-90 -89h-80
l124 144h114zM768 770l-194 -144h-91l166 144h119z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="684" 
d="M584 682h-76l-54 67l-90 -67h-80l124 108h114zM634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM337 736h-89l-130 108h117z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM507 556h-76l-54 89l-90 -89h-80
l124 144h114zM262 626h-89l-130 144h117z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="684" 
d="M583 682h-76l-54 67l-90 -67h-80l124 108h114zM634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM532 798l-40 15q24 35 81 35q30 0 52 -12t22 -34q0 -25 -24 -46h-57q29 20 29 41q0 19 -27 19q-22 0 -36 -18z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM507 556h-76l-54 89l-90 -89h-80
l124 144h114zM444 723l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="684" 
d="M582 678h-76l-54 44l-90 -44h-80l124 72h114zM634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM520 757q-38 0 -69 17t-52 17q-31 0 -44 -31h-68q27 67 121 67q38 0 69.5 -17t51.5 -17q30 0 45 31h68q-15 -34 -43 -50.5t-79 -16.5z
" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM499 523h-76l-54 89l-90 -89h-80
l124 144h114zM457 682q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM596 723h-76l-54 89l-90 -89h-80l124 144h114zM327 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM507 556h-76l-54 89l-90 -89h-80
l124 144h114zM273 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM661 834l-194 -84h-91l166 84h119zM621 738q-84 -60 -194 -60t-157 60l65 32q29 -42 105 -42q75 0 133 42z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM579 778l-194 -144h-91l166 144h119
zM538 602q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM532 754h-89l-130 84h117zM626 738q-84 -60 -194 -60t-157 60l65 32q29 -42 105 -42q75 0 133 42z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM449 636h-89l-130 144h117zM539 606
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM425 782l-40 12q25 27 81 27q29 0 51.5 -9.5t22.5 -26.5q0 -21 -24 -36h-57q29 15 29 32q0 15 -27 15q-22 0 -36 -14zM625 738q-84 -60 -194 -60t-157 60l65 32q29 -42 105 -42
q75 0 133 42z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM345 700l-40 20q25 47 81 47
q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24zM539 606q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM622 727q-85 -49 -194 -49q-110 0 -157 49l65 26q29 -34 105 -34q73 0 133 34zM520 760q-38 0 -69 17t-52 17q-31 0 -44 -31h-68q27 67 121 67q38 0 69.5 -17t51.5 -17q30 0 45 31
h68q-15 -34 -43 -50.5t-79 -16.5z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM539 606q-85 -88 -194 -88
q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62zM456 677q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="684" 
d="M634 0h-154l-17 113h-286l-67 -113h-169l405 667h178zM451 238l-40 287l-167 -287h207zM328 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5zM631 786q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47
q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="586" 
d="M281 495q48 0 89 -20t65 -56l13 64h128l-107 -483h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82t77 -31q66 0 111 57l37 168q-37 56 -113 56zM274 -121q0 -30 -23.5 -52.5
t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5zM542 619q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="583" 
d="M484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329zM287 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="553" 
d="M278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5zM272 -121
q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="583" 
d="M484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329zM378 753l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="553" 
d="M278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5zM326 583
l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="583" 
d="M484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329zM479 721q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="553" 
d="M278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5zM426 554
q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="583" 
d="M543 682h-76l-54 67l-90 -67h-80l124 108h114zM804 844l-194 -108h-91l166 108h119zM484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="553" 
d="M504 556h-76l-54 89l-90 -89h-80l124 144h114zM764 770l-194 -144h-91l166 144h119zM278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287
h242v10q0 40 -28.5 68.5t-82.5 28.5q-49 0 -85.5 -32.5t-45.5 -74.5z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="583" 
d="M543 682h-76l-54 67l-90 -67h-80l124 108h114zM484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329zM295 736h-89l-130 108h117z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="553" 
d="M506 556h-76l-54 89l-90 -89h-80l124 144h114zM278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5
q-49 0 -85.5 -32.5t-45.5 -74.5zM260 626h-89l-130 144h117z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="583" 
d="M542 682h-76l-54 67l-90 -67h-80l124 108h114zM484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329zM490 798l-40 15q24 35 81 35q30 0 52 -12t22 -34q0 -25 -24 -46h-57q29 20 29 41q0 19 -27 19q-22 0 -36 -18z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="553" 
d="M505 556h-76l-54 89l-90 -89h-80l124 144h114zM278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5
q-49 0 -85.5 -32.5t-45.5 -74.5zM442 723l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="583" 
d="M544 678h-76l-54 44l-90 -44h-80l124 72h114zM484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329zM479 757q-38 0 -69 17t-52 17q-31 0 -44 -31h-68q27 67 121 67q38 0 69.5 -17t51.5 -17q30 0 45 31h68q-15 -34 -43 -50.5t-79 -16.5z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="553" 
d="M499 523h-76l-54 89l-90 -89h-80l124 144h114zM278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5
q-49 0 -85.5 -32.5t-45.5 -74.5zM455 682q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="583" 
d="M557 723h-76l-54 89l-90 -89h-80l124 144h114zM484 0h-472l147 667h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-33 -151h329zM287 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="553" 
d="M506 556h-76l-54 89l-90 -89h-80l124 144h114zM278 -12q-115 0 -182.5 58t-67.5 159q0 120 80 205t199 85q95 0 156 -58.5t61 -157.5q0 -46 -10 -78h-359v-12q0 -36 35.5 -68t96.5 -32q75 0 122 36l40 -87q-73 -50 -171 -50zM168 287h242v10q0 40 -28.5 68.5t-82.5 28.5
q-49 0 -85.5 -32.5t-45.5 -74.5zM272 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="274" 
d="M154 0h-142l147 667h142zM213 753l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="253" 
d="M136 0h-127l107 483h127zM166 583l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="274" 
d="M154 0h-142l147 667h142zM122 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="253" 
d="M198 535q-29 0 -48.5 19t-19.5 46q0 39 26.5 62.5t58.5 23.5q30 0 49.5 -19t19.5 -46q0 -39 -27 -62.5t-59 -23.5zM136 0h-127l107 483h127zM113 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5zM368 -121
q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" horiz-adv-x="574" 
d="M266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30zM272 -121q0 -30 -23.5 -52.5t-53.5 -22.5
q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5zM460 753l-40 20
q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" horiz-adv-x="574" 
d="M266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30zM326 583l-40 20q25 47 81 47
q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="765" 
d="M624 682h-76l-54 67l-90 -67h-80l124 108h114zM885 844l-194 -108h-91l166 108h119zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5
q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" horiz-adv-x="574" 
d="M505 556h-76l-54 89l-90 -89h-80l124 144h114zM766 770l-194 -144h-91l166 144h119zM266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30
q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="765" 
d="M624 682h-76l-54 67l-90 -67h-80l124 108h114zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5
q0 -83 53 -132.5t135 -49.5zM376 736h-89l-130 108h117z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" horiz-adv-x="574" 
d="M506 556h-76l-54 89l-90 -89h-80l124 144h114zM266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83
t83 -30zM260 626h-89l-130 144h117z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="765" 
d="M623 682h-76l-54 67l-90 -67h-80l124 108h114zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5
q0 -83 53 -132.5t135 -49.5zM571 798l-40 15q24 35 81 35q30 0 52 -12t22 -34q0 -25 -24 -46h-57q29 20 29 41q0 19 -27 19q-22 0 -36 -18z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" horiz-adv-x="574" 
d="M505 556h-76l-54 89l-90 -89h-80l124 144h114zM266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83
t83 -30zM443 723l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="765" 
d="M618 678h-76l-54 44l-90 -44h-80l124 72h114zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5
q0 -83 53 -132.5t135 -49.5zM558 757q-38 0 -69 17t-52 17q-31 0 -44 -31h-68q27 67 121 67q38 0 69.5 -17t51.5 -17q30 0 45 31h68q-15 -34 -43 -50.5t-79 -16.5z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" horiz-adv-x="574" 
d="M499 523h-76l-54 89l-90 -89h-80l124 144h114zM266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83
t83 -30zM456 682q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="765" 
d="M638 723h-76l-54 89l-90 -89h-80l124 144h114zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q141 0 234 -82t93 -216q0 -164 -111.5 -278t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5
q0 -83 53 -132.5t135 -49.5zM369 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" horiz-adv-x="574" 
d="M505 556h-76l-54 89l-90 -89h-80l124 144h114zM266 -12q-113 0 -176 61t-63 158q0 116 80 202t199 86q113 0 176.5 -61.5t63.5 -158.5q0 -116 -80 -201.5t-200 -85.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83
t83 -30zM272 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="765" 
d="M708 867l-194 -144h-91l166 144h119zM372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q163 0 257 -105q48 27 62 60h-7q-18 0 -30.5 12t-12.5 34q0 24 19 42t43 18q22 0 38 -15.5t16 -42.5q0 -40 -28.5 -80.5t-72.5 -63.5q43 -70 43 -157q0 -164 -111.5 -278
t-275.5 -114zM380 114q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" horiz-adv-x="574" 
d="M574 700l-194 -144h-91l166 144h119zM600 548q0 -39 -26.5 -78.5t-68.5 -62.5q41 -55 41 -132q0 -116 -80 -201.5t-200 -85.5q-113 0 -176 61t-63 158q0 116 80 202t199 86q103 0 168 -55q46 28 60 60q-1 -1 -7 -1q-18 0 -31 12.5t-13 33.5q0 25 19 43t44 18
q22 0 38 -15.5t16 -42.5zM271 101q62 0 102.5 50t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q163 0 257 -105q48 27 62 60h-7q-18 0 -30.5 12t-12.5 34q0 24 19 42t43 18q22 0 38 -15.5t16 -42.5q0 -40 -28.5 -80.5t-72.5 -63.5q43 -70 43 -157q0 -164 -111.5 -278t-275.5 -114zM380 114
q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5zM554 723h-89l-130 144h117z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" horiz-adv-x="574" 
d="M600 548q0 -39 -26.5 -78.5t-68.5 -62.5q41 -55 41 -132q0 -116 -80 -201.5t-200 -85.5q-113 0 -176 61t-63 158q0 116 80 202t199 86q103 0 168 -55q46 28 60 60q-1 -1 -7 -1q-18 0 -31 12.5t-13 33.5q0 25 19 43t44 18q22 0 38 -15.5t16 -42.5zM271 101q62 0 102.5 50
t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30zM421 556h-89l-130 144h117z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q163 0 257 -105q48 27 62 60h-7q-18 0 -30.5 12t-12.5 34q0 24 19 42t43 18q22 0 38 -15.5t16 -42.5q0 -40 -28.5 -80.5t-72.5 -63.5q43 -70 43 -157q0 -164 -111.5 -278t-275.5 -114zM380 114
q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5zM460 753l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" horiz-adv-x="574" 
d="M600 548q0 -39 -26.5 -78.5t-68.5 -62.5q41 -55 41 -132q0 -116 -80 -201.5t-200 -85.5q-113 0 -176 61t-63 158q0 116 80 202t199 86q103 0 168 -55q46 28 60 60q-1 -1 -7 -1q-18 0 -31 12.5t-13 33.5q0 25 19 43t44 18q22 0 38 -15.5t16 -42.5zM271 101q62 0 102.5 50
t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30zM326 583l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q163 0 257 -105q48 27 62 60h-7q-18 0 -30.5 12t-12.5 34q0 24 19 42t43 18q22 0 38 -15.5t16 -42.5q0 -40 -28.5 -80.5t-72.5 -63.5q43 -70 43 -157q0 -164 -111.5 -278t-275.5 -114zM380 114
q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5zM560 721q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68
q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" horiz-adv-x="574" 
d="M600 548q0 -39 -26.5 -78.5t-68.5 -62.5q41 -55 41 -132q0 -116 -80 -201.5t-200 -85.5q-113 0 -176 61t-63 158q0 116 80 202t199 86q103 0 168 -55q46 28 60 60q-1 -1 -7 -1q-18 0 -31 12.5t-13 33.5q0 25 19 43t44 18q22 0 38 -15.5t16 -42.5zM271 101q62 0 102.5 50
t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30zM399 554q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="765" 
d="M372 -12q-142 0 -235 82t-93 217q0 164 112 277.5t276 113.5q163 0 257 -105q48 27 62 60h-7q-18 0 -30.5 12t-12.5 34q0 24 19 42t43 18q22 0 38 -15.5t16 -42.5q0 -40 -28.5 -80.5t-72.5 -63.5q43 -70 43 -157q0 -164 -111.5 -278t-275.5 -114zM380 114
q96 0 163.5 74.5t67.5 181.5q0 83 -53 132.5t-135 49.5q-97 0 -164 -74.5t-67 -181.5q0 -83 53 -132.5t135 -49.5zM368 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" horiz-adv-x="574" 
d="M600 548q0 -39 -26.5 -78.5t-68.5 -62.5q41 -55 41 -132q0 -116 -80 -201.5t-200 -85.5q-113 0 -176 61t-63 158q0 116 80 202t199 86q103 0 168 -55q46 28 60 60q-1 -1 -7 -1q-18 0 -31 12.5t-13 33.5q0 25 19 43t44 18q22 0 38 -15.5t16 -42.5zM271 101q62 0 102.5 50
t40.5 118q0 53 -30 83t-82 30q-62 0 -103 -50.5t-41 -117.5q0 -53 30 -83t83 -30zM272 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="733" 
d="M356 -12q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-29 -133 -103 -206t-213 -73zM351 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23
q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" 
d="M115 483h128l-65 -291q-4 -18 -4 -29q0 -31 22.5 -46.5t58.5 -15.5q56 0 115 57l72 325h127l-107 -483h-127l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39zM274 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23
q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="733" 
d="M356 -12q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-89 -400q-29 -133 -103 -206t-213 -73zM443 753l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57
q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" 
d="M115 483h128l-65 -291q-4 -18 -4 -29q0 -31 22.5 -46.5t58.5 -15.5q56 0 115 57l72 325h127l-107 -483h-127l14 63q-79 -75 -161 -75q-69 0 -110 34t-41 90q0 17 5 39zM328 583l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54
q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="733" 
d="M691 867l-194 -144h-91l166 144h119zM882 742q0 -51 -40 -95t-101 -68l-69 -312q-29 -133 -103 -206t-213 -73q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-8 -34q50 28 63 61
q-1 -1 -7 -1q-18 0 -31 12.5t-13 34.5q0 25 19 42.5t44 17.5q22 0 38 -15.5t16 -42.5z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" 
d="M577 700l-194 -144h-91l166 144h119zM667 560q0 -46 -31.5 -85.5t-83.5 -65.5l-90 -409h-127l14 62q-78 -74 -161 -74q-70 0 -111 33.5t-41 89.5q0 20 6 40l73 332h127l-64 -292q-4 -18 -4 -28q0 -31 22.5 -46.5t59.5 -15.5q56 0 113 57l73 325h127l-3 -14q26 20 35 43
h-7q-18 0 -31 12.5t-13 33.5q0 24 19 42.5t43 18.5q23 0 39 -16t16 -43z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="733" 
d="M536 723h-89l-130 144h117zM882 742q0 -51 -40 -95t-101 -68l-69 -312q-29 -133 -103 -206t-213 -73q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-8 -34q50 28 63 61q-1 -1 -7 -1
q-18 0 -31 12.5t-13 34.5q0 25 19 42.5t44 17.5q22 0 38 -15.5t16 -42.5z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" 
d="M423 556h-89l-130 144h117zM667 560q0 -46 -31.5 -85.5t-83.5 -65.5l-90 -409h-127l14 62q-78 -74 -161 -74q-70 0 -111 33.5t-41 89.5q0 20 6 40l73 332h127l-64 -292q-4 -18 -4 -28q0 -31 22.5 -46.5t59.5 -15.5q56 0 113 57l73 325h127l-3 -14q26 20 35 43h-7
q-18 0 -31 12.5t-13 33.5q0 24 19 42.5t43 18.5q23 0 39 -16t16 -43z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="733" 
d="M443 753l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24zM882 742q0 -51 -40 -95t-101 -68l-69 -312q-29 -133 -103 -206t-213 -73q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397
q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-8 -34q50 28 63 61q-1 -1 -7 -1q-18 0 -31 12.5t-13 34.5q0 25 19 42.5t44 17.5q22 0 38 -15.5t16 -42.5z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" 
d="M328 583l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24zM667 560q0 -46 -31.5 -85.5t-83.5 -65.5l-90 -409h-127l14 62q-78 -74 -161 -74q-70 0 -111 33.5t-41 89.5q0 20 6 40l73 332h127l-64 -292
q-4 -18 -4 -28q0 -31 22.5 -46.5t59.5 -15.5q56 0 113 57l73 325h127l-3 -14q26 20 35 43h-7q-18 0 -31 12.5t-13 33.5q0 24 19 42.5t43 18.5q23 0 39 -16t16 -43z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="733" 
d="M882 742q0 -51 -40 -95t-101 -68l-69 -312q-29 -133 -103 -206t-213 -73q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-8 -34q50 28 63 61q-1 -1 -7 -1q-18 0 -31 12.5t-13 34.5
q0 25 19 42.5t44 17.5q22 0 38 -15.5t16 -42.5zM543 721q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" 
d="M667 560q0 -46 -31.5 -85.5t-83.5 -65.5l-90 -409h-127l14 62q-78 -74 -161 -74q-70 0 -111 33.5t-41 89.5q0 20 6 40l73 332h127l-64 -292q-4 -18 -4 -28q0 -31 22.5 -46.5t59.5 -15.5q56 0 113 57l73 325h127l-3 -14q26 20 35 43h-7q-18 0 -31 12.5t-13 33.5
q0 24 19 42.5t43 18.5q23 0 39 -16t16 -43zM429 554q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="733" 
d="M351 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5zM882 742q0 -51 -40 -95t-101 -68l-69 -312q-29 -133 -103 -206t-213 -73q-141 0 -215.5 63t-74.5 170q0 20 5 45l88 401h144l-87 -397q-4 -12 -4 -33
q1 -53 38.5 -88.5t105.5 -35.5q138 0 172 157l88 397h145l-8 -34q50 28 63 61q-1 -1 -7 -1q-18 0 -31 12.5t-13 34.5q0 25 19 42.5t44 17.5q22 0 38 -15.5t16 -42.5z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" 
d="M274 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5zM667 560q0 -46 -31.5 -85.5t-83.5 -65.5l-90 -409h-127l14 62q-78 -74 -161 -74q-70 0 -111 33.5t-41 89.5q0 20 6 40l73 332h127l-64 -292
q-4 -18 -4 -28q0 -31 22.5 -46.5t59.5 -15.5q56 0 113 57l73 325h127l-3 -14q26 20 35 43h-7q-18 0 -31 12.5t-13 33.5q0 24 19 42.5t43 18.5q23 0 39 -16t16 -43z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="645" 
d="M340 0h-142l60 273l-170 394h154l107 -268l223 268h170l-342 -394zM493 723h-89l-130 144h117z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="513" 
d="M-48 -184l42 110q18 -8 49 -8q37 0 60 34l30 45l-86 486h132l51 -336l202 336h140l-352 -562q-39 -64 -82.5 -90.5t-104.5 -26.5q-46 0 -81 12zM387 556h-89l-130 144h117z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="51" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="593" 
d="M550 188h-533l24 108h533z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="833" 
d="M790 188h-773l24 108h773z" />
    <glyph glyph-name="afii00208" unicode="&#x2015;" horiz-adv-x="593" 
d="M550 188h-533l24 108h533z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="255" 
d="M91 477q0 57 42 113.5t105 86.5l45 -47q-30 -13 -58.5 -36t-39.5 -48q3 1 10 1q26 0 44 -17.5t18 -47.5q0 -35 -27 -60.5t-62 -25.5q-32 0 -54.5 21.5t-22.5 59.5z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="255" 
d="M291 595q0 -56 -42 -112.5t-105 -87.5l-46 47q30 13 58.5 36t39.5 48h-10q-26 0 -44 17t-18 48q0 35 27.5 60.5t61.5 25.5q32 0 55 -22t23 -60z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="256" 
d="M174 69q0 -56 -42 -113t-105 -87l-45 47q30 13 58.5 36t39.5 48h-11q-26 0 -43.5 17t-17.5 48q0 35 27 60.5t61 25.5q33 0 55.5 -21.5t22.5 -60.5z" />
    <glyph glyph-name="quotereversed" unicode="&#x201b;" horiz-adv-x="255" 
d="M120 543q0 64 33 99t73 35q32 0 52 -20.5t20 -51.5q0 -35 -24.5 -57t-57.5 -22q-18 0 -30 6q1 -27 17 -57.5t40 -51.5l-55 -33q-68 68 -68 153z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="459" 
d="M296 477q0 57 41.5 113.5t104.5 86.5l46 -47q-30 -13 -58.5 -36t-39.5 -48q2 1 10 1q26 0 44 -17.5t18 -47.5q0 -35 -27 -60.5t-62 -25.5q-32 0 -54.5 21.5t-22.5 59.5zM92 477q0 57 42 113.5t105 86.5l46 -47q-30 -13 -58.5 -36t-39.5 -48q2 1 10 1q26 0 44 -17.5
t18 -47.5q0 -35 -27 -60.5t-62 -25.5q-32 0 -55 21.5t-23 59.5z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="459" 
d="M291 595q0 -56 -42 -112.5t-105 -87.5l-46 47q30 13 58.5 36t39.5 48h-10q-26 0 -44 17t-18 48q0 35 27.5 60.5t61.5 25.5q32 0 55 -22t23 -60zM494 595q0 -56 -42 -112.5t-105 -87.5l-45 47q30 13 58.5 36t39.5 48h-11q-26 0 -43.5 17t-17.5 48q0 35 27 60.5t61 25.5
q33 0 55.5 -21.5t22.5 -60.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="459" 
d="M174 69q0 -56 -42 -113t-105 -87l-45 47q30 13 58.5 36t39.5 48h-11q-26 0 -43.5 17t-17.5 48q0 35 27 60.5t61 25.5q33 0 55.5 -21.5t22.5 -60.5zM377 69q0 -56 -42 -113t-105 -87l-45 47q30 13 58.5 36t39.5 48h-10q-26 0 -44 17t-18 48q0 35 27.5 60.5t61.5 25.5
q32 0 54.5 -21.5t22.5 -60.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="277" 
d="M318 522l-88 4l-40 -199h-68l49 199l-90 -4l14 62l88 -3l17 96h68l-26 -96l90 3z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="277" 
d="M332 584l-14 -62l-88 4l-43 -194l90 4l-14 -62l-88 3l-18 -97h-68l26 97l-89 -3l14 62l87 -4l43 194l-89 -4l14 62l88 -3l17 96h68l-26 -96z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="358" 
d="M299 254q0 -53 -39.5 -91.5t-93.5 -38.5q-46 0 -76.5 30.5t-30.5 76.5q0 53 39.5 91.5t93.5 38.5q46 0 76.5 -30.5t30.5 -76.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="768" 
d="M82 -12q-31 0 -52.5 22t-21.5 53q0 36 26.5 62t62.5 26q31 0 52 -22t21 -53q0 -36 -26 -62t-62 -26zM338 -12q-31 0 -52.5 22t-21.5 53q0 36 26.5 62t62.5 26q31 0 52 -22t21 -53q0 -36 -26 -62t-62 -26zM594 -12q-31 0 -52.5 22t-21.5 53q0 36 26.5 62t62.5 26
q31 0 52 -22t21 -53q0 -36 -26 -62t-62 -26z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1122" 
d="M151 0h-75l573 667h76zM530 -12q-67 0 -114 38.5t-47 100.5q0 85 54.5 139.5t130.5 54.5q67 0 114 -38.5t47 -100.5q0 -85 -54.5 -139.5t-130.5 -54.5zM532 66q38 0 65 30t27 78q0 29 -20.5 48.5t-51.5 19.5q-38 0 -64.5 -30t-26.5 -77q0 -30 20 -49.5t51 -19.5zM241 345
q-66 0 -112.5 38t-46.5 100q0 85 54.5 139.5t130.5 54.5q66 0 112.5 -38t46.5 -100q0 -85 -54 -139.5t-131 -54.5zM245 423q38 0 64.5 30t26.5 77q0 29 -20.5 48.5t-52.5 19.5q-38 0 -64.5 -30t-26.5 -77q0 -29 20.5 -48.5t52.5 -19.5zM894 -12q-67 0 -114 38.5t-47 100.5
q0 85 54.5 139.5t130.5 54.5q67 0 114 -38.5t47 -100.5q0 -85 -54.5 -139.5t-130.5 -54.5zM897 66q38 0 64.5 30t26.5 78q0 29 -20.5 48.5t-51.5 19.5q-38 0 -64.5 -30t-26.5 -77q0 -30 20 -49.5t52 -19.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="337" 
d="M262 63h-113l-120 180l200 177h121l-204 -182z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="337" 
d="M77 420h113l120 -180l-200 -177h-121l204 182z" />
    <glyph glyph-name="uni203E" unicode="&#x203e;" horiz-adv-x="363" 
d="M432 562h-362l16 72h362z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="159" 
d="M424 667l-574 -667h-76l573 667h77z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="397" 
d="M451 668q0 -38 -11.5 -80t-34.5 -82t-64 -66t-93 -26q-73 0 -114 44.5t-41 115.5q0 38 11.5 79.5t34.5 81.5t64 66t93 26q73 0 114 -44.5t41 -114.5zM355 671q0 79 -63 79q-34 0 -59 -33.5t-34.5 -73t-9.5 -73.5q0 -79 63 -79q35 0 59.5 33.5t34 72.5t9.5 74z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="397" 
d="M422 507h-53l-19 -86h-95l19 86h-197l15 65l213 249h133l-52 -239h52zM290 582l34 154l-134 -154h100z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="397" 
d="M425 571q0 -67 -47.5 -112t-126.5 -45q-116 0 -170 69l61 57q39 -49 107 -49q37 0 57.5 18.5t20.5 46.5q0 26 -19.5 40t-51.5 14q-45 0 -79 -32l-62 21l49 222h290l-17 -77h-193l-21 -93q37 29 81 29q51 0 86 -30t35 -79z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="397" 
d="M430 568q0 -65 -46 -109.5t-123 -44.5q-79 0 -123 41.5t-44 113.5q0 106 64.5 182t157.5 76q87 0 138 -47l-53 -64q-31 34 -88 34q-52 0 -85 -40.5t-33 -72.5v-4q47 53 105 53q55 0 92.5 -32t37.5 -86zM332 556q0 26 -20 40.5t-53 14.5q-38 0 -70 -35q-2 -5 -2 -18
q0 -30 20 -48.5t53 -18.5q31 0 51.5 19.5t20.5 45.5z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="371" 
d="M444 755l-221 -334h-107l220 323h-209l17 77h315z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="397" 
d="M421 526q0 -49 -42 -80.5t-120 -31.5q-76 0 -128 27t-52 81q0 41 33.5 73.5t86.5 38.5q-69 27 -69 84q0 53 46 81t110 28q66 0 119 -27t53 -78q0 -39 -28.5 -68t-81.5 -36q30 -12 51.5 -36t21.5 -56zM359 708q0 21 -20 33t-50 12q-29 0 -47.5 -12.5t-18.5 -35.5
q0 -20 19.5 -33t38.5 -16q78 7 78 52zM328 537q0 19 -19 34.5t-43 21.5q-37 -2 -63 -18t-26 -40q0 -21 22.5 -34.5t53.5 -13.5q33 0 54 14t21 36z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="397" 
d="M117 674q0 64 46 109t123 45q79 0 122.5 -41.5t43.5 -113.5q0 -106 -64.5 -182t-156.5 -76q-86 0 -139 47l54 64q31 -34 88 -34q51 0 84.5 40.5t33.5 73.5v3q-47 -53 -106 -53q-54 0 -91.5 32t-37.5 86zM214 685q0 -26 21 -40.5t53 -14.5q38 0 69 36q2 5 2 18
q0 30 -19.5 48.5t-52.5 18.5q-32 0 -52.5 -19.5t-20.5 -46.5z" />
    <glyph glyph-name="parenleftsuperior" unicode="&#x207d;" horiz-adv-x="192" 
d="M211 379l-62 -23q-53 72 -53 189q0 96 47 187t123 154l52 -23q-129 -170 -129 -356q0 -71 22 -128z" />
    <glyph glyph-name="parenrightsuperior" unicode="&#x207e;" horiz-adv-x="192" 
d="M152 863l63 23q52 -74 52 -189q0 -96 -47 -187.5t-122 -153.5l-53 23q129 170 129 356q0 71 -22 128z" />
    <glyph glyph-name="nsuperior" unicode="&#x207f;" horiz-adv-x="411" 
d="M375 326h-92l41 186q2 12 2 18q0 40 -51 40q-36 0 -73 -37l-46 -207h-92l69 314h92l-8 -40q48 48 105 48q49 0 76.5 -22t27.5 -61q0 -6 -4 -30z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="397" 
d="M335 101q0 -38 -11.5 -80t-34.5 -82t-64 -66t-93 -26q-73 0 -114 44.5t-41 115.5q0 38 11.5 79.5t34.5 81.5t64 66t93 26q73 0 114 -44.5t41 -114.5zM239 104q0 79 -63 79q-34 0 -59 -33.5t-34.5 -73t-9.5 -73.5q0 -79 63 -79q35 0 59.5 33.5t34 72.5t9.5 74z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="286" 
d="M140 -146h-97l60 273l-81 -66l-43 59l166 134h83z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="397" 
d="M274 -146h-314l17 74q155 90 207 130.5t52 76.5q0 23 -19 35.5t-48 12.5q-69 0 -112 -43l-40 64q62 56 155 56q71 0 117 -31t46 -85q0 -53 -53 -102t-164 -111h173z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="397" 
d="M303 -34q0 -47 -43 -83t-115 -36q-61 0 -108.5 19.5t-70.5 53.5l60 59q39 -55 114 -55q33 0 50 15t17 38q0 20 -24 31.5t-65 11.5q-27 0 -33 -1l16 76q22 -2 65 -2q72 0 72 47q0 21 -23 32t-57 11q-60 0 -103 -36l-33 60q63 53 145 53q76 0 122 -28.5t46 -78.5
q0 -39 -29.5 -68t-80.5 -33q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="397" 
d="M306 -60h-53l-19 -86h-95l19 86h-197l15 65l213 249h133l-52 -239h52zM174 15l34 154l-134 -154h100z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="397" 
d="M309 4q0 -67 -47.5 -112t-126.5 -45q-116 0 -170 69l61 57q39 -49 107 -49q37 0 57.5 18.5t20.5 46.5q0 26 -19.5 40t-51.5 14q-45 0 -79 -32l-62 21l49 222h290l-17 -77h-193l-21 -93q37 29 81 29q51 0 86 -30t35 -79z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="397" 
d="M314 1q0 -65 -46 -109.5t-123 -44.5q-79 0 -123 41.5t-44 113.5q0 106 64.5 182t157.5 76q87 0 138 -47l-53 -64q-31 34 -88 34q-52 0 -85 -40.5t-33 -72.5v-4q47 53 105 53q55 0 92.5 -32t37.5 -86zM216 -11q0 26 -20 40.5t-53 14.5q-38 0 -70 -35q-2 -5 -2 -18
q0 -30 20 -48.5t53 -18.5q31 0 51.5 19.5t20.5 45.5z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="371" 
d="M328 188l-221 -334h-107l220 323h-209l17 77h315z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="397" 
d="M305 -41q0 -49 -42 -80.5t-120 -31.5q-76 0 -128 27t-52 81q0 41 33.5 73.5t86.5 38.5q-69 27 -69 84q0 53 46 81t110 28q66 0 119 -27t53 -78q0 -39 -28.5 -68t-81.5 -36q30 -12 51.5 -36t21.5 -56zM243 141q0 21 -20 33t-50 12q-29 0 -47.5 -12.5t-18.5 -35.5
q0 -20 19.5 -33t38.5 -16q78 7 78 52zM212 -30q0 19 -19 34.5t-43 21.5q-37 -2 -63 -18t-26 -40q0 -21 22.5 -34.5t53.5 -13.5q33 0 54 14t21 36z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="397" 
d="M1 107q0 64 46 109t123 45q79 0 122.5 -41.5t43.5 -113.5q0 -106 -64.5 -182t-156.5 -76q-86 0 -139 47l54 64q31 -34 88 -34q51 0 84.5 40.5t33.5 73.5v3q-47 -53 -106 -53q-54 0 -91.5 32t-37.5 86zM98 118q0 -26 21 -40.5t53 -14.5q38 0 69 36q2 5 2 18
q0 30 -19.5 48.5t-52.5 18.5q-32 0 -52.5 -19.5t-20.5 -46.5z" />
    <glyph glyph-name="parenleftinferior" unicode="&#x208d;" horiz-adv-x="192" 
d="M86 -188l-62 -23q-53 72 -53 189q0 96 47 187t123 154l52 -23q-129 -170 -129 -356q0 -71 22 -128z" />
    <glyph glyph-name="parenrightinferior" unicode="&#x208e;" horiz-adv-x="192" 
d="M27 296l63 23q52 -74 52 -189q0 -96 -47 -187.5t-122 -153.5l-53 23q129 170 129 356q0 71 -22 128z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="696" 
d="M381 -12q-24 0 -48 3l-50 -91h-75l57 104q-21 7 -53 22l-70 -126h-76l90 162q-103 84 -103 225q0 172 114 281.5t274 109.5q31 0 54 -4l52 94h75l-61 -109q30 -11 53 -24l74 133h76l-97 -174q45 -42 70 -105l-137 -46l-7 16l-192 -345q41 3 79.5 23.5t65.5 53.5l109 -74
q-53 -68 -126.5 -98.5t-147.5 -30.5zM201 296q0 -61 28 -102l198 358q-93 -3 -159.5 -76.5t-66.5 -179.5zM277 145q26 -16 52 -23l219 393q-22 18 -51 27z" />
    <glyph glyph-name="frenchfranc" unicode="&#x20a3;" horiz-adv-x="580" 
d="M162 0h-142l24 109h-66l16 73h66l107 485h472l-28 -125h-329l-31 -141h322l-28 -125h-322l-21 -94h154l-16 -73h-154z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="550" 
d="M42 333l15 66h45q-11 28 -11 61q0 90 77 153.5t185 63.5q87 0 151.5 -35.5t84.5 -98.5l-129 -53q-7 33 -34.5 52t-65.5 19q-48 0 -81 -32.5t-33 -83.5q0 -24 8 -46h173l-14 -66h-132q6 -26 6 -43q0 -2 -0.5 -5t-0.5 -4h115l-15 -65h-120q-30 -52 -74 -77q21 7 44 7
q30 0 74 -16.5t70 -16.5q46 0 81 37l31 -113q-48 -50 -123 -50q-46 0 -118.5 22t-101.5 22q-47 0 -111 -38l-27 97q48 21 83.5 54.5t48.5 71.5h-127l15 65h118q-3 18 -17 52h-90z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="747" 
d="M617 0h-137l-112 231h-155l-51 -231h-142l51 231h-66l17 72h65l14 66h-65l15 72h66l50 226h146l110 -226h148l51 226h142l-50 -226h64l-15 -72h-65l-14 -66h65l-17 -72h-64zM244 369l-15 -66h104l-32 66h-57zM458 369l33 -66h49l15 66h-97z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="677" 
d="M163 0h-143l89 406h-65l16 74h66l41 187h291q87 0 141.5 -52t56.5 -135h67l-16 -74h-59q-20 -82 -88 -134.5t-181 -52.5h-168zM387 344q77 0 108 62h-243l-14 -62h149zM282 542l-14 -62h237q-6 27 -27.5 44.5t-53.5 17.5h-142z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="1072" 
d="M813 -12q-67 0 -127.5 22.5t-100.5 64.5l71 85q23 -28 71.5 -52t97.5 -24q31 0 50.5 15.5t19.5 35.5q0 23 -38.5 40.5t-84 30t-84 45t-38.5 81.5q0 67 53.5 115t145.5 48q61 0 115.5 -21.5t89.5 -57.5l-65 -81q-19 24 -59.5 43.5t-82.5 19.5q-32 0 -51 -13.5t-19 -33.5
q0 -14 18 -25.5t45.5 -19t59 -20t59 -27t45.5 -41t18 -61.5q0 -72 -56.5 -120.5t-152.5 -48.5zM552 0h-157l-79 237h-110l-52 -237h-142l147 667h285q84 0 145 -52t61 -136q0 -89 -53.5 -153t-136.5 -79zM381 362h2q55 0 86.5 28.5t31.5 74.5q0 33 -25 55t-59 22h-143
l-40 -180h147z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="918" 
d="M673 0h-151l-8 231h-116l-109 -231h-151l-15 231h-114l16 72h93l-5 66h-73l16 72h53l-15 226h156l4 -226h130l109 226h111l9 -226h129l105 226h163l-114 -226h50l-16 -72h-71l-33 -66h90l-17 -72h-110zM616 369l3 -66h60l30 66h-93zM256 369l1 -66h60l32 66h-93zM464 369
l-32 -66h79l-2 66h-45zM622 231l2 -46l21 46h-23zM259 231l1 -46l22 46h-23zM506 458l-8 -17h8v17z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="845" 
d="M542 0h-335l83 373h120l-58 -263h187q53 0 79.5 28.5t39.5 90.5l71 321h120l-73 -331q-25 -112 -75.5 -165.5t-158.5 -53.5zM130 550h246q230 0 230 -170q0 -31 -9 -71l-29 -133h-121l30 134q6 26 6 41q0 89 -104 89h-153l-97 -440h-120z" />
    <glyph glyph-name="dong" unicode="&#x20ab;" horiz-adv-x="590" 
d="M395 -171h-362l16 72h362zM281 495q48 0 89 -20t65 -56l23 109h-150l14 72h152l15 67h128l-15 -67h51l-15 -72h-52l-117 -528h-128l14 60q-61 -72 -145 -72t-134 53t-50 147q0 76 28.5 144.5t88 115.5t138.5 47zM301 382q-61 0 -101.5 -50t-40.5 -118q0 -51 30 -82
t77 -31q66 0 111 57l37 168q-37 56 -113 56z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="709" 
d="M400 -12q-123 0 -211.5 64.5t-110.5 172.5h-61l17 78h38q2 35 7 62h-31l17 78h36q45 109 142.5 172t216.5 63q115 0 189.5 -53t106.5 -136l-137 -46q-19 52 -63.5 80.5t-102.5 28.5t-108.5 -29t-83.5 -80h256l-18 -78h-271q-8 -32 -8 -62h265l-17 -78h-236q20 -51 66 -81
t110 -30q43 0 86 21t71 56l109 -74q-53 -68 -126.5 -98.5t-147.5 -30.5z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="526" 
d="M244 487l-28 -125l133 76q35 20 51 30.5t29.5 27.5t13.5 36q0 26 -22 41.5t-56 15.5q-99 0 -121 -102zM372 347l-182 -104l-20 -89q-2 -12 -2 -21q0 -16 13 -27t34 -11q23 0 36 11l5 -97q-27 -21 -83 -21q-65 0 -102.5 27t-37.5 76q0 18 4 35l83 374q18 85 82.5 131
t170.5 46q83 0 138.5 -37.5t55.5 -94.5q0 -25 -15.5 -52t-35 -46.5t-51.5 -42t-49 -32.5t-44 -25z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="1134" 
d="M967 350q-73 0 -117.5 40.5t-44.5 105.5q0 77 57 130.5t133 53.5q74 0 118 -40.5t44 -105.5q0 -78 -56.5 -131t-133.5 -53zM970 424q43 0 67.5 33.5t24.5 77.5q0 32 -18.5 50.5t-50.5 18.5q-43 0 -68 -34t-25 -77q0 -32 19 -50.5t51 -18.5zM609 0h-137l-218 450
l-100 -450h-142l147 667h146l212 -434l97 434h142z" />
    <glyph glyph-name="published" unicode="&#x2117;" horiz-adv-x="778" 
d="M754 334q0 -142 -101.5 -243.5t-244.5 -101.5q-142 0 -243 101t-101 244t101 244t243 101q143 0 244.5 -101.5t101.5 -243.5zM718 334q0 127 -91.5 218t-218.5 91t-217.5 -91t-90.5 -218t90.5 -218t217.5 -91t218.5 91t91.5 218zM595 430q0 -57 -42 -94.5t-105 -37.5
h-121l-36 -167h-46l89 405h137q52 0 88 -28.5t36 -77.5zM548 427q0 30 -22.5 48.5t-60.5 18.5h-94l-34 -155h118q41 0 67 25.5t26 62.5z" />
    <glyph glyph-name="servicemark" unicode="&#x2120;" horiz-adv-x="476" 
d="M484 447h-28l40 182l-115 -182h-8l-35 182l-40 -182h-28l48 220h42l29 -160l99 160h44zM247 511q0 -32 -23 -52t-62 -20q-69 0 -96 40l22 19q23 -33 72 -33q24 0 38 11t14 29q0 15 -18 26.5t-39 18.5t-39 21t-18 34q0 29 23.5 48.5t60.5 19.5q63 0 86 -35l-23 -17
q-20 28 -65 28q-22 0 -35 -11t-13 -26t18 -26t39.5 -17t39.5 -21t18 -37z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="454" 
d="M464 447h-28l40 182l-115 -182h-8l-35 182l-40 -182h-28l48 220h43l29 -160l99 160h43zM257 641h-62l-43 -194h-28l43 194h-62l5 26h152z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="765" 
d="M26 125h136q-46 36 -76 91.5t-30 125.5q0 61 24.5 120t70.5 107.5t119 78.5t161 30q144 0 238 -75t94 -203q0 -97 -54 -167.5t-127 -107.5h105l-27 -125h-270l28 125q86 19 138.5 87.5t52.5 159.5q0 81 -49 130t-135 49q-102 0 -166.5 -75.5t-64.5 -177.5
q0 -120 100 -173l-27 -125h-269z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="842" 
d="M829 324h-632q-4 0 -4 -5v-190q0 -15 9 -23q98 -103 240 -103q151 0 250 115h57q-54 -63 -135 -99.5t-174 -36.5q-161 0 -275.5 103t-114.5 248t114.5 248t275.5 103t275.5 -103t114.5 -248zM687 349v191q0 14 -10 24q-97 99 -236 99t-238 -102q-10 -10 -10 -24v-188
q0 -6 5 -6h485q4 0 4 6z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="867" 
d="M231 267h-97l60 273l-81 -66l-43 59l166 134h83zM734 667l-574 -667h-76l573 667h77zM804 112q0 -47 -43 -83t-115 -36q-61 0 -108.5 19.5t-70.5 53.5l60 59q39 -55 114 -55q33 0 50 15t17 38q0 20 -24 31.5t-65 11.5q-27 0 -33 -1l16 76q22 -2 65 -2q72 0 72 47
q0 21 -23 32t-57 11q-60 0 -103 -36l-33 60q63 53 145 53q76 0 122 -28.5t46 -78.5q0 -39 -29.5 -68t-80.5 -33q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="954" 
d="M365 267h-314l17 74q155 90 207 130.5t52 76.5q0 23 -19 35.5t-48 12.5q-69 0 -112 -43l-40 64q62 56 155 56q71 0 117 -31t46 -85q0 -53 -53 -102t-164 -111h173zM821 667l-574 -667h-76l573 667h77zM892 112q0 -47 -43 -83t-115 -36q-61 0 -108.5 19.5t-70.5 53.5
l60 59q39 -55 114 -55q33 0 50 15t17 38q0 20 -24 31.5t-65 11.5q-27 0 -33 -1l16 76q22 -2 65 -2q72 0 72 47q0 21 -23 32t-57 11q-60 0 -103 -36l-33 60q63 53 145 53q76 0 122 -28.5t46 -78.5q0 -39 -29.5 -68t-80.5 -33q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="867" 
d="M231 267h-97l60 273l-81 -66l-43 59l166 134h83zM806 105q0 -49 -42 -80.5t-120 -31.5q-76 0 -128 27t-52 81q0 41 33.5 73.5t86.5 38.5q-69 27 -69 84q0 53 46 81t110 28q66 0 119 -27t53 -78q0 -39 -28.5 -68t-81.5 -36q30 -12 51.5 -36t21.5 -56zM744 287q0 21 -20 33
t-50 12q-29 0 -47.5 -12.5t-18.5 -35.5q0 -20 19.5 -33t38.5 -16q78 7 78 52zM713 116q0 19 -19 34.5t-43 21.5q-37 -2 -63 -18t-26 -40q0 -21 22.5 -34.5t53.5 -13.5q33 0 54 14t21 36zM734 667l-574 -667h-76l573 667h77z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="954" 
d="M894 105q0 -49 -42 -80.5t-120 -31.5q-76 0 -128 27t-52 81q0 41 33.5 73.5t86.5 38.5q-69 27 -69 84q0 53 46 81t110 28q66 0 119 -27t53 -78q0 -39 -28.5 -68t-81.5 -36q30 -12 51.5 -36t21.5 -56zM832 287q0 21 -20 33t-50 12q-29 0 -47.5 -12.5t-18.5 -35.5
q0 -20 19.5 -33t38.5 -16q78 7 78 52zM801 116q0 19 -19 34.5t-43 21.5q-37 -2 -63 -18t-26 -40q0 -21 22.5 -34.5t53.5 -13.5q33 0 54 14t21 36zM821 667l-574 -667h-76l573 667h77zM394 379q0 -47 -43 -83t-115 -36q-61 0 -108.5 19.5t-70.5 53.5l60 59q39 -55 114 -55
q33 0 50 15t17 38q0 20 -24 31.5t-65 11.5q-27 0 -33 -1l16 76q22 -2 65 -2q72 0 72 47q0 21 -23 32t-57 11q-60 0 -103 -36l-33 60q63 53 145 53q76 0 122 -28.5t46 -78.5q0 -39 -29.5 -68t-80.5 -33q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="948" 
d="M887 105q0 -49 -42 -80.5t-120 -31.5q-76 0 -128 27t-52 81q0 41 33.5 73.5t86.5 38.5q-69 27 -69 84q0 53 46 81t110 28q66 0 119 -27t53 -78q0 -39 -28.5 -68t-81.5 -36q30 -12 51.5 -36t21.5 -56zM825 287q0 21 -20 33t-50 12q-29 0 -47.5 -12.5t-18.5 -35.5
q0 -20 19.5 -33t38.5 -16q78 7 78 52zM794 116q0 19 -19 34.5t-43 21.5q-37 -2 -63 -18t-26 -40q0 -21 22.5 -34.5t53.5 -13.5q33 0 54 14t21 36zM400 417q0 -67 -47.5 -112t-126.5 -45q-116 0 -170 69l61 57q39 -49 107 -49q37 0 57.5 18.5t20.5 46.5q0 26 -19 40t-52 14
q-45 0 -79 -32l-62 21l49 222h290l-17 -77h-193l-21 -93q37 29 81 29q51 0 86 -30t35 -79zM815 667l-574 -667h-76l573 667h77z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="873" 
d="M812 105q0 -49 -42 -80.5t-120 -31.5q-76 0 -128 27t-52 81q0 41 33.5 73.5t86.5 38.5q-69 27 -69 84q0 53 46 81t110 28q66 0 119 -27t53 -78q0 -39 -28.5 -68t-81.5 -36q30 -12 51.5 -36t21.5 -56zM750 287q0 21 -20 33t-50 12q-29 0 -47.5 -12.5t-18.5 -35.5
q0 -20 19.5 -33t38.5 -16q78 7 78 52zM719 116q0 19 -19 34.5t-43 21.5q-37 -2 -63 -18t-26 -40q0 -21 22.5 -34.5t53.5 -13.5q33 0 54 14t21 36zM419 601l-221 -334h-107l220 323h-209l17 77h315zM740 667l-574 -667h-76l573 667h77z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="582" 
d="M558 211h-269l12 -130l-281 205l281 204l-12 -129h269v-150z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="582" 
d="M236 17v269l-130 -12l205 281l204 -281l-129 12v-269h-150z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="582" 
d="M604 286l-281 -205l12 130h-268v150h268l-12 129z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="582" 
d="M386 555v-269l130 12l-205 -281l-204 281l129 -12v269h150z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="574" 
d="M269 -12q-111 0 -179.5 58t-68.5 152q0 109 72 185.5t175 76.5q119 0 164 -109q-15 66 -78 138.5t-146 133.5l90 93q253 -194 253 -410q0 -134 -77.5 -226t-204.5 -92zM271 101q59 0 98 41.5t39 103.5q0 42 -30.5 71.5t-87.5 29.5t-95.5 -41t-38.5 -101q0 -49 30 -76.5
t85 -27.5z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="684" 
d="M634 0h-693l405 667h178zM468 125l-57 400l-234 -400h291z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="788" 
d="M585 -90h-143l139 632h-243l-140 -632h-143l140 632h-105l28 125h738l-28 -125h-104z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="586" 
d="M285 542l149 -252l-259 -255h319l-28 -125h-495l27 125l261 260l-147 247l27 125h494l-27 -125h-321z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="504" 
d="M487 298h-447l18 79h446z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="159" 
d="M424 667l-574 -667h-76l573 667h77z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="256" 
d="M121 164q-31 0 -52.5 22t-21.5 52q0 37 26 62.5t63 25.5q30 0 51.5 -22t21.5 -53q0 -35 -26.5 -61t-61.5 -26z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="682" 
d="M350 0h-84l-46 302l-141 -47l-7 75l224 73l40 -276l317 540h97z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="690" 
d="M192 158q-69 0 -108 39.5t-39 106.5q0 81 51 144.5t136 63.5q52 0 91.5 -25.5t58.5 -76.5q70 102 154 102q69 0 108 -39.5t39 -106.5q0 -81 -50.5 -144.5t-136.5 -63.5q-51 0 -90.5 26t-58.5 75q-70 -101 -155 -101zM493 237q46 0 76 33.5t30 83.5q0 36 -23 58t-58 22
q-64 0 -117 -103q5 -44 30 -69t62 -25zM211 237q67 0 118 103q-5 44 -30 69t-62 25q-46 0 -76 -33.5t-30 -84.5q0 -35 23 -57t57 -22z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="373" 
d="M1 -100h-60l18 85h61q30 0 50.5 20t28.5 55l122 548q37 160 190 160h61l-19 -85h-61q-30 0 -50.5 -20t-28.5 -54l-122 -550q-35 -159 -190 -159z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="504" 
d="M19 201l19 85q43 -30 105 -30q45 0 110 26t123 26q63 0 108 -24l-19 -85q-43 30 -106 30q-45 0 -110.5 -26t-123.5 -26q-63 0 -106 24zM60 383l18 86q45 -30 105 -30q45 0 110.5 26t123.5 26q59 0 107 -23l-18 -86q-45 30 -106 30q-45 0 -110.5 -25.5t-123.5 -25.5
q-64 0 -106 22z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="504" 
d="M104 93h-80l94 108h-99l18 78h147l94 109h-217l17 80h268l92 106h81l-91 -106h96l-18 -80h-147l-94 -109h218l-17 -78h-269z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="504" 
d="M445 110l-403 198l21 92l491 200l-22 -98l-389 -153l322 -150zM421 0h-446l17 78h446z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="504" 
d="M489 308l-491 -198l22 97l389 150l-322 153l20 90l402 -200zM421 0h-447l18 78h446z" />
    <glyph glyph-name="triangleright" unicode="&#x22b2;" horiz-adv-x="587" 
d="M609 286l-535 -309v618z" />
    <glyph glyph-name="triangleleft" unicode="&#x22b3;" horiz-adv-x="587" 
d="M555 595v-618l-535 309z" />
    <glyph glyph-name="dotmath" unicode="&#x22c5;" horiz-adv-x="256" 
d="M121 164q-31 0 -52.5 22t-21.5 52q0 37 26 62.5t63 25.5q30 0 51.5 -22t21.5 -53q0 -35 -26.5 -61t-61.5 -26z" />
    <glyph glyph-name="uni2318" unicode="&#x2318;" horiz-adv-x="667" 
d="M198 153l15 67h-73q-30 0 -53 -22t-23 -52q0 -25 16 -42t41 -17q30 0 50 17.5t27 48.5zM263 447l13 57q2 12 2 18q0 26 -16.5 42t-42.5 16q-31 0 -53 -21t-22 -52q0 -27 18 -43.5t46 -16.5h55zM269 265h137l31 137h-137zM561 160q0 27 -18 43.5t-46 16.5h-55l-13 -57
q-2 -12 -2 -18q0 -26 16.5 -42t42.5 -16q32 0 53.5 21t21.5 52zM641 521q0 25 -16 42t-41 17q-30 0 -50 -18t-27 -48l-15 -67h73q30 0 53 22.5t23 51.5zM607 160q0 -48 -39 -83t-89 -35q-42 0 -70 28t-28 70q0 14 3 29l12 51h-137l-17 -75q-11 -46 -45 -74.5t-80 -28.5
q-41 0 -70 28t-29 69q0 49 38 87.5t87 38.5h80l31 137h-55q-45 0 -72.5 29t-27.5 76q0 48 38.5 83t88.5 35q42 0 70 -28t28 -70q0 -14 -3 -29l-12 -51h137l17 75q11 46 45 74.5t80 28.5q42 0 71 -28t29 -69q0 -50 -38 -88t-87 -38h-80l-31 -137h52q46 0 74.5 -29.5
t28.5 -75.5z" />
    <glyph glyph-name="blacksquare" unicode="&#x25a0;" horiz-adv-x="661" 
d="M638 0h-571v572h571v-572z" />
    <glyph glyph-name="triagup" unicode="&#x25b2;" horiz-adv-x="616" 
d="M637 29l-619 1l309 535z" />
    <glyph glyph-name="triagdn" unicode="&#x25bc;" horiz-adv-x="616" 
d="M637 510l-310 -535l-309 535h619z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="375" 
d="M144 0h-21l-84 334l231 333h21l85 -333zM142 45l197 293l-67 284l-196 -292z" />
    <glyph glyph-name="circlesolid" unicode="&#x25cf;" horiz-adv-x="695" 
d="M664 285q0 -123 -87 -210t-210 -87t-210 87t-87 210t87 210t210 87t210 -87t87 -210z" />
    <glyph glyph-name="blackstar" unicode="&#x2605;" horiz-adv-x="759" 
d="M779 421l-235 -171l90 -275l-235 170l-234 -170l89 275l-234 171h290l89 276l90 -276h290z" />
    <glyph glyph-name="ballotbox" unicode="&#x2610;" horiz-adv-x="661" 
d="M636 0h-571v572h571v-572zM550 78v415h-399v-415h399z" />
    <glyph glyph-name="boxchecked" unicode="&#x2611;" horiz-adv-x="661" 
d="M656 0h-571v572h486l92 167l68 -41l-75 -139v-559zM570 78v323l-168 -310l-202 246l61 57l129 -155l138 254h-357v-415h399z" />
    <glyph glyph-name="diamond" unicode="&#x2666;" horiz-adv-x="501" 
d="M521 285l-251 -250l-250 250l250 251z" />
    <glyph glyph-name="checkmark" unicode="&#x2713;" horiz-adv-x="531" 
d="M551 698l-329 -607l-202 246l61 57l129 -155l273 500z" />
    <glyph glyph-name="perthousand.onum" unicode="&#xf628;" horiz-adv-x="905" 
d="M338 441q0 -62 -43.5 -108.5t-108.5 -46.5q-57 0 -91.5 32t-34.5 88q0 63 43.5 109.5t108.5 46.5q56 0 91 -33t35 -88zM601 550l-505 -550h-71l504 550h72zM261 438q0 25 -14.5 41.5t-38.5 16.5q-29 0 -49.5 -26t-20.5 -61q0 -26 13.5 -41.5t37.5 -15.5q29 0 50.5 25.5
t21.5 60.5zM565 145q0 -62 -43.5 -108.5t-108.5 -46.5q-56 0 -90.5 32t-34.5 88q0 62 43.5 109t107.5 47q56 0 91 -32.5t35 -88.5zM488 142q0 25 -14 41.5t-38 16.5q-29 0 -50 -26t-21 -61q0 -25 14 -41t38 -16q29 0 50 25.5t21 60.5zM857 145q0 -62 -43.5 -108.5
t-108.5 -46.5q-56 0 -90.5 32t-34.5 88q0 63 43 109.5t108 46.5q56 0 91 -32.5t35 -88.5zM780 142q0 25 -14.5 41.5t-38.5 16.5q-28 0 -48.5 -26t-20.5 -61q0 -25 13.5 -41t37.5 -16q28 0 49.5 25.5t21.5 60.5z" />
    <glyph glyph-name="uniF637" unicode="&#xf637;" horiz-adv-x="411" 
d="M375 326h-92l41 186q2 12 2 18q0 40 -51 40q-36 0 -73 -37l-46 -207h-92l96 434h92l-35 -160q48 48 105 48q49 0 76.5 -22t27.5 -61q0 -6 -4 -30z" />
    <glyph glyph-name="zero.tzero" unicode="&#xf638;" horiz-adv-x="623" 
d="M285 -12q-96 0 -159 54l-37 -42h-102l91 104q-34 63 -34 153q0 108 41 204t118.5 156t172.5 60t157 -53l38 43h102l-92 -105q34 -63 34 -153q0 -109 -40.5 -204.5t-117.5 -156t-172 -60.5zM298 114q52 0 93.5 49t61 117t19.5 136q0 4 -0.5 10.5t-0.5 9.5l-253 -289
q28 -33 80 -33zM188 250v-20l253 289q-29 32 -79 32q-77 0 -125.5 -96.5t-48.5 -204.5z" />
    <glyph glyph-name="zero.tnum" unicode="&#xf639;" horiz-adv-x="623" 
d="M286 -12q-110 0 -175.5 71t-65.5 198q0 108 41 204t118.5 156t172.5 60q110 0 174.5 -70.5t64.5 -197.5q0 -109 -40.5 -204.5t-117.5 -156t-172 -60.5zM299 114q52 0 93.5 49t61 117t19.5 136q0 62 -27 98.5t-83 36.5q-77 0 -125.5 -96.5t-48.5 -204.5q0 -63 27 -99.5
t83 -36.5z" />
    <glyph glyph-name="two.tnum" unicode="&#xf63a;" horiz-adv-x="623" 
d="M515 0h-502l29 132q99 56 154.5 89t116 72.5t90 66.5t48.5 56.5t19 57.5q0 36 -33.5 57t-86.5 21q-95 0 -166 -60l-60 102q44 38 107 60.5t129 22.5q109 0 184 -51.5t75 -142.5q0 -94 -89.5 -180.5t-259.5 -177.5h272z" />
    <glyph glyph-name="three.tnum" unicode="&#xf63b;" horiz-adv-x="623" 
d="M304 -12q-94 0 -174.5 37t-117.5 102l95 86q30 -47 85 -73t110 -26q57 0 88 25.5t31 66.5q0 70 -122 70q-59 0 -81 -2l29 128q11 -1 93 -1q130 0 130 77q0 33 -34.5 53t-96.5 20q-93 0 -162 -54l-51 97q91 83 227 83q121 0 192.5 -46t71.5 -128q0 -64 -55 -110t-125 -55
q124 -31 124 -146q0 -88 -74.5 -146t-182.5 -58z" />
    <glyph glyph-name="four.tnum" unicode="&#xf63c;" horiz-adv-x="623" 
d="M442 0h-142l30 138h-319l28 124l346 405h204l-89 -404h85l-27 -125h-86zM357 263l62 278l-242 -278h180z" />
    <glyph glyph-name="five.tnum" unicode="&#xf63d;" horiz-adv-x="623" 
d="M301 -12q-191 0 -278 128l101 90q57 -92 179 -92q57 0 91.5 31t34.5 77q0 43 -34.5 66.5t-91.5 23.5q-69 0 -122 -40l-92 37l79 358h458l-27 -125h-317l-34 -157q54 43 133 43q84 0 140.5 -50.5t56.5 -138.5q0 -105 -78 -178t-199 -73z" />
    <glyph glyph-name="six.tnum" unicode="&#xf63e;" horiz-adv-x="623" 
d="M311 -12q-123 0 -192.5 68t-69.5 190q0 111 40.5 207.5t124 160t193.5 63.5q141 0 224 -91l-88 -100q-50 65 -144 65q-68 0 -117 -48t-70 -116q-3 -6 -9 -26q28 29 75.5 49.5t97.5 20.5q91 0 151 -50t60 -134q0 -107 -81 -183t-195 -76zM311 114q54 0 91 33.5t37 81.5
q0 40 -37 64t-92 24q-67 0 -121 -51q-1 -7 -1 -34q0 -54 34.5 -86t88.5 -32z" />
    <glyph glyph-name="seven.tnum" unicode="&#xf63f;" horiz-adv-x="623" 
d="M263 0h-163l356 542h-328l27 125h501l-22 -99z" />
    <glyph glyph-name="eight.tnum" unicode="&#xf640;" horiz-adv-x="623" 
d="M293 -12q-114 0 -192.5 45.5t-78.5 133.5q0 66 49 117.5t136 73.5q-43 19 -71.5 54.5t-28.5 79.5q0 62 40.5 105t98 61.5t123.5 18.5q105 0 182.5 -44.5t77.5 -126.5q0 -65 -47.5 -112.5t-125.5 -62.5q50 -26 80.5 -65.5t30.5 -85.5q0 -88 -81 -140t-193 -52zM345 399
q59 4 95 25.5t36 56.5q0 31 -33.5 52t-82.5 21q-46 0 -78.5 -21t-32.5 -56q0 -26 29.5 -48t66.5 -30zM299 110q50 0 87 23.5t37 59.5q0 31 -31.5 54.5t-73.5 33.5q-62 -3 -103.5 -28t-41.5 -64q0 -38 34.5 -58.5t91.5 -20.5z" />
    <glyph glyph-name="nine.tnum" unicode="&#xf641;" horiz-adv-x="623" 
d="M352 678q123 0 192.5 -68t69.5 -190q0 -82 -24 -158.5t-68 -137.5t-113.5 -98t-153.5 -37q-140 0 -222 90l87 101q47 -65 145 -65q68 0 116 47.5t71 115.5q7 22 8 27q-29 -30 -76 -50.5t-97 -20.5q-91 0 -151.5 50.5t-60.5 134.5q0 106 82 182.5t195 76.5zM352 552
q-54 0 -91 -33.5t-37 -81.5q0 -40 36.5 -64t91.5 -24q69 0 122 50q1 7 1 34q0 54 -34.5 86.5t-88.5 32.5z" />
    <glyph glyph-name="percent.onum" unicode="&#xf642;" horiz-adv-x="613" 
d="M338 441q0 -62 -43.5 -108.5t-108.5 -46.5q-57 0 -91.5 32t-34.5 88q0 63 43.5 109.5t108.5 46.5q56 0 91 -33t35 -88zM601 550l-505 -550h-71l504 550h72zM261 438q0 25 -14.5 41.5t-38.5 16.5q-29 0 -49.5 -26t-20.5 -61q0 -26 13.5 -41.5t37.5 -15.5q29 0 50.5 25.5
t21.5 60.5zM565 145q0 -62 -43.5 -108.5t-108.5 -46.5q-56 0 -90.5 32t-34.5 88q0 62 43.5 109t107.5 47q56 0 91 -32.5t35 -88.5zM488 142q0 25 -14 41.5t-38 16.5q-29 0 -50 -26t-21 -61q0 -25 14 -41t38 -16q29 0 50 25.5t21 60.5z" />
    <glyph glyph-name="zero.tonum" unicode="&#xf643;" horiz-adv-x="623" 
d="M600 320q0 -57 -20 -115t-56.5 -107t-95.5 -79.5t-129 -30.5q-125 0 -194.5 64t-69.5 177q0 58 20 115.5t56.5 107t95.5 80t130 30.5q125 0 194 -64t69 -178zM454 310q0 62 -31 94t-88 32q-71 0 -112.5 -62.5t-41.5 -133.5q0 -62 31 -94t88 -32q71 0 112.5 62.5
t41.5 133.5z" />
    <glyph glyph-name="one.tonum" unicode="&#xf644;" horiz-adv-x="623" 
d="M397 0h-142l80 367l-135 -113l-63 85l257 211h124z" />
    <glyph glyph-name="two.tonum" unicode="&#xf645;" horiz-adv-x="623" 
d="M487 0h-469l26 119q190 56 289.5 118.5t99.5 122.5q0 35 -29.5 55.5t-77.5 20.5q-97 0 -177 -65l-60 100q103 91 252 91q108 0 176 -44.5t68 -123.5q0 -46 -25 -89t-68 -76.5t-89.5 -58.5t-98.5 -45h211z" />
    <glyph glyph-name="three.tonum" unicode="&#xf646;" horiz-adv-x="623" 
d="M278 -127q-94 0 -174.5 37t-117.5 102l95 86q30 -47 85 -73t110 -26q57 0 88 25.5t31 66.5q0 70 -122 70q-59 0 -81 -2l29 128q11 -1 93 -1q130 0 130 77q0 33 -34.5 53t-96.5 20q-93 0 -162 -54l-51 97q91 83 227 83q121 0 192.5 -46t71.5 -128q0 -64 -55 -110t-125 -55
q124 -31 124 -146q0 -88 -74.5 -146t-182.5 -58z" />
    <glyph glyph-name="four.tonum" unicode="&#xf647;" horiz-adv-x="623" 
d="M533 27h-85l-32 -144h-142l32 144h-319l25 113l355 410h196l-87 -398h85zM333 152l60 272l-240 -272h180z" />
    <glyph glyph-name="five.tonum" unicode="&#xf648;" horiz-adv-x="623" 
d="M275 -129q-191 0 -278 128l101 90q57 -92 179 -92q57 0 91.5 31t34.5 77q0 43 -34.5 66.5t-91.5 23.5q-69 0 -122 -40l-92 37l79 358h458l-27 -125h-317l-34 -157q54 43 133 43q84 0 140.5 -50.5t56.5 -138.5q0 -105 -78 -178t-199 -73z" />
    <glyph glyph-name="six.tonum" unicode="&#xf649;" horiz-adv-x="623" 
d="M311 -12q-123 0 -192.5 68t-69.5 190q0 111 40.5 207.5t124 160t193.5 63.5q141 0 224 -91l-88 -100q-50 65 -144 65q-68 0 -117 -48t-70 -116q-3 -6 -9 -26q28 29 75.5 49.5t97.5 20.5q91 0 151 -50t60 -134q0 -107 -81 -183t-195 -76zM311 114q54 0 91 33.5t37 81.5
q0 40 -37 64t-92 24q-67 0 -121 -51q-1 -7 -1 -34q0 -54 34.5 -86t88.5 -32z" />
    <glyph glyph-name="seven.tonum" unicode="&#xf64a;" horiz-adv-x="623" 
d="M237 -117h-163l356 542h-328l27 125h501l-22 -99z" />
    <glyph glyph-name="eight.tonum" unicode="&#xf64b;" horiz-adv-x="623" 
d="M293 -12q-114 0 -192.5 45.5t-78.5 133.5q0 66 49 117.5t136 73.5q-43 19 -71.5 54.5t-28.5 79.5q0 62 40.5 105t98 61.5t123.5 18.5q105 0 182.5 -44.5t77.5 -126.5q0 -65 -47.5 -112.5t-125.5 -62.5q50 -26 80.5 -65.5t30.5 -85.5q0 -88 -81 -140t-193 -52zM345 399
q59 4 95 25.5t36 56.5q0 31 -33.5 52t-82.5 21q-46 0 -78.5 -21t-32.5 -56q0 -26 29.5 -48t66.5 -30zM299 110q50 0 87 23.5t37 59.5q0 31 -31.5 54.5t-73.5 33.5q-62 -3 -103.5 -28t-41.5 -64q0 -38 34.5 -58.5t91.5 -20.5z" />
    <glyph glyph-name="nine.tonum" unicode="&#xf64c;" horiz-adv-x="623" 
d="M326 562q123 0 192.5 -68t69.5 -190q0 -82 -24 -158.5t-68 -137.5t-113.5 -98t-153.5 -37q-140 0 -222 90l87 101q47 -65 145 -65q68 0 116 47.5t71 115.5q7 22 8 27q-29 -30 -76 -50.5t-97 -20.5q-91 0 -151.5 50.5t-60.5 134.5q0 106 82 182.5t195 76.5zM326 436
q-54 0 -91 -33.5t-37 -81.5q0 -40 36.5 -64t91.5 -24q69 0 122 50q1 7 1 34q0 54 -34.5 86.5t-88.5 32.5z" />
    <glyph glyph-name="colonmonetary.onum" unicode="&#xf64d;" horiz-adv-x="585" 
d="M60 -53l58 103q-86 70 -86 189q0 132 96 227.5t234 95.5q22 0 41 -3l23 40h57l-29 -52q22 -8 41 -18l39 70h58l-56 -99q42 -37 63 -92l-119 -43l-9 18l-157 -279h4q80 0 133 71l99 -61q-40 -56 -101.5 -91t-137.5 -35q-29 0 -59 5l-26 -46h-57l32 58q-25 8 -41 17
l-43 -75h-57zM168 249q0 -45 18 -76l153 272q-70 -8 -120.5 -64.5t-50.5 -131.5zM222 133q18 -14 40 -21l173 309q-19 14 -41 20z" />
    <glyph glyph-name="Euro.onum" unicode="&#xf64e;" horiz-adv-x="610" 
d="M411 300h-212q-6 -26 -6 -51h207l-13 -60h-183q35 -85 139 -85q80 0 133 71l99 -61q-40 -56 -101.5 -91t-137.5 -35q-110 0 -184 55t-91 146h-52l13 60h35q2 34 6 51h-30l13 61h35q38 89 120.5 145t185.5 56q87 0 149.5 -42.5t87.5 -111.5l-119 -43q-14 36 -48 58.5
t-75 22.5q-46 0 -86.5 -23t-67.5 -62h197z" />
    <glyph glyph-name="florin.onum" unicode="&#xf64f;" horiz-adv-x="397" 
d="M104 -119h-145l72 322h-43v90h63l23 102q37 166 175 166q66 0 104 -39l-45 -102q-10 16 -35 16q-45 0 -59 -63l-18 -80h84v-90h-104z" />
    <glyph glyph-name="numbersign.onum" unicode="&#xf650;" horiz-adv-x="517" 
d="M534 402l-35 -69h-80l-63 -113h80l-37 -70h-82l-83 -150h-85l82 150h-75l-82 -150h-86l83 150h-76l37 70h79l63 113h-79l36 69h81l83 148h85l-83 -148h75l84 148h85l-84 -148h77zM333 333h-75l-63 -113h76z" />
    <glyph glyph-name="sterling.onum" unicode="&#xf651;" horiz-adv-x="450" 
d="M11 224l15 69h52q-16 43 -16 79q0 79 65.5 134t148.5 55q70 0 123.5 -29.5t71.5 -81.5l-108 -43q-8 30 -30 45t-49 15q-39 0 -65.5 -29.5t-26.5 -76.5q0 -31 15 -68h125l-16 -69h-93q0 -28 -21.5 -64t-41.5 -48q9 4 21 4q26 0 61.5 -12t54.5 -12q34 0 65 29l29 -91
q-38 -40 -104 -40q-40 0 -95.5 16t-85.5 16q-39 0 -87 -29l-24 80q48 19 79 52t31 73q0 12 -3 26h-91z" />
    <glyph glyph-name="yen.onum" unicode="&#xf652;" horiz-adv-x="565" 
d="M295 0h-131l24 108h-194l15 67h194l11 50h-194l16 68h146l-120 257h147l94 -213l183 213h151l-233 -257h144l-15 -68h-189l-11 -50h189l-15 -67h-189z" />
    <glyph glyph-name="cent.onum" unicode="&#xf654;" horiz-adv-x="585" 
d="M208 -83l18 81q-90 22 -142 86.5t-52 154.5q0 129 92.5 223.5t226.5 98.5l14 62h91l-16 -72q57 -15 98.5 -53t60.5 -90l-119 -43q-18 46 -66 68l-73 -328q66 11 110 70l99 -61q-40 -55 -100 -90t-135 -36l-16 -71h-91zM168 249q0 -98 84 -133l73 327q-66 -13 -111.5 -68
t-45.5 -126z" />
    <glyph glyph-name="zero.dnom" unicode="&#xf655;" horiz-adv-x="397" 
d="M367 247q0 -38 -11.5 -80t-34.5 -82t-64 -66t-93 -26q-73 0 -114 44.5t-41 115.5q0 38 11.5 79.5t34.5 81.5t64 66t93 26q73 0 114 -44.5t41 -114.5zM271 250q0 79 -63 79q-34 0 -59 -33.5t-34.5 -73t-9.5 -73.5q0 -79 63 -79q35 0 59.5 33.5t34 72.5t9.5 74z" />
    <glyph glyph-name="one.dnom" unicode="&#xf656;" horiz-adv-x="286" 
d="M172 0h-97l60 273l-81 -66l-43 59l166 134h83z" />
    <glyph glyph-name="two.dnom" unicode="&#xf657;" horiz-adv-x="397" 
d="M306 0h-314l17 74q155 90 207 130.5t52 76.5q0 23 -19 35.5t-48 12.5q-69 0 -112 -43l-40 64q62 56 155 56q71 0 117 -31t46 -85q0 -53 -53 -102t-164 -111h173z" />
    <glyph glyph-name="three.dnom" unicode="&#xf658;" horiz-adv-x="397" 
d="M335 112q0 -47 -43 -83t-115 -36q-61 0 -108.5 19.5t-70.5 53.5l60 59q39 -55 114 -55q33 0 50 15t17 38q0 20 -24 31.5t-65 11.5q-27 0 -33 -1l16 76q22 -2 65 -2q72 0 72 47q0 21 -23 32t-57 11q-60 0 -103 -36l-33 60q63 53 145 53q76 0 122 -28.5t46 -78.5
q0 -39 -29.5 -68t-80.5 -33q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="four.dnom" unicode="&#xf659;" horiz-adv-x="397" 
d="M338 86h-53l-19 -86h-95l19 86h-197l15 65l213 249h133l-52 -239h52zM206 161l34 154l-134 -154h100z" />
    <glyph glyph-name="five.dnom" unicode="&#xf65a;" horiz-adv-x="397" 
d="M341 150q0 -67 -47.5 -112t-126.5 -45q-116 0 -170 69l61 57q39 -49 107 -49q37 0 57.5 18.5t20.5 46.5q0 26 -19.5 40t-51.5 14q-45 0 -79 -32l-62 21l49 222h290l-17 -77h-193l-21 -93q37 29 81 29q51 0 86 -30t35 -79z" />
    <glyph glyph-name="six.dnom" unicode="&#xf65b;" horiz-adv-x="397" 
d="M346 147q0 -65 -46 -109.5t-123 -44.5q-79 0 -123 41.5t-44 113.5q0 106 64.5 182t157.5 76q87 0 138 -47l-53 -64q-31 34 -88 34q-52 0 -85 -40.5t-33 -72.5v-4q47 53 105 53q55 0 92.5 -32t37.5 -86zM248 135q0 26 -20 40.5t-53 14.5q-38 0 -70 -35q-2 -5 -2 -18
q0 -30 20 -48.5t53 -18.5q31 0 51.5 19.5t20.5 45.5z" />
    <glyph glyph-name="seven.dnom" unicode="&#xf65c;" horiz-adv-x="371" 
d="M360 334l-221 -334h-107l220 323h-209l17 77h315z" />
    <glyph glyph-name="eight.dnom" unicode="&#xf65d;" horiz-adv-x="397" 
d="M337 105q0 -49 -42 -80.5t-120 -31.5q-76 0 -128 27t-52 81q0 41 33.5 73.5t86.5 38.5q-69 27 -69 84q0 53 46 81t110 28q66 0 119 -27t53 -78q0 -39 -28.5 -68t-81.5 -36q30 -12 51.5 -36t21.5 -56zM275 287q0 21 -20 33t-50 12q-29 0 -47.5 -12.5t-18.5 -35.5
q0 -20 19.5 -33t38.5 -16q78 7 78 52zM244 116q0 19 -19 34.5t-43 21.5q-37 -2 -63 -18t-26 -40q0 -21 22.5 -34.5t53.5 -13.5q33 0 54 14t21 36z" />
    <glyph glyph-name="nine.dnom" unicode="&#xf65e;" horiz-adv-x="397" 
d="M33 253q0 64 46 109t123 45q79 0 122.5 -41.5t43.5 -113.5q0 -106 -64.5 -182t-156.5 -76q-86 0 -139 47l54 64q31 -34 88 -34q51 0 84.5 40.5t33.5 73.5v3q-47 -53 -106 -53q-54 0 -91.5 32t-37.5 86zM130 264q0 -26 21 -40.5t53 -14.5q38 0 69 36q2 5 2 18
q0 30 -19.5 48.5t-52.5 18.5q-32 0 -52.5 -19.5t-20.5 -46.5z" />
    <glyph glyph-name="zero.numr" unicode="&#xf661;" horiz-adv-x="397" 
d="M426 514q0 -38 -11.5 -80t-34.5 -82t-64 -66t-93 -26q-73 0 -114 44.5t-41 115.5q0 38 11.5 79.5t34.5 81.5t64 66t93 26q73 0 114 -44.5t41 -114.5zM330 517q0 79 -63 79q-34 0 -59 -33.5t-34.5 -73t-9.5 -73.5q0 -79 63 -79q35 0 59.5 33.5t34 72.5t9.5 74z" />
    <glyph glyph-name="one.numr" unicode="&#xf662;" horiz-adv-x="286" 
d="M231 267h-97l60 273l-81 -66l-43 59l166 134h83z" />
    <glyph glyph-name="two.numr" unicode="&#xf663;" horiz-adv-x="397" 
d="M365 267h-314l17 74q155 90 207 130.5t52 76.5q0 23 -19 35.5t-48 12.5q-69 0 -112 -43l-40 64q62 56 155 56q71 0 117 -31t46 -85q0 -53 -53 -102t-164 -111h173z" />
    <glyph glyph-name="three.numr" unicode="&#xf664;" horiz-adv-x="397" 
d="M394 379q0 -47 -43 -83t-115 -36q-61 0 -108.5 19.5t-70.5 53.5l60 59q39 -55 114 -55q33 0 50 15t17 38q0 20 -24 31.5t-65 11.5q-27 0 -33 -1l16 76q22 -2 65 -2q72 0 72 47q0 21 -23 32t-57 11q-60 0 -103 -36l-33 60q63 53 145 53q76 0 122 -28.5t46 -78.5
q0 -39 -29.5 -68t-80.5 -33q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="four.numr" unicode="&#xf665;" horiz-adv-x="397" 
d="M397 353h-53l-19 -86h-95l19 86h-197l15 65l213 249h133l-52 -239h52zM265 428l34 154l-134 -154h100z" />
    <glyph glyph-name="five.numr" unicode="&#xf666;" horiz-adv-x="397" 
d="M400 417q0 -67 -47.5 -112t-126.5 -45q-116 0 -170 69l61 57q39 -49 107 -49q37 0 57.5 18.5t20.5 46.5q0 26 -19 40t-52 14q-45 0 -79 -32l-62 21l49 222h290l-17 -77h-193l-21 -93q37 29 81 29q51 0 86 -30t35 -79z" />
    <glyph glyph-name="six.numr" unicode="&#xf667;" horiz-adv-x="397" 
d="M405 414q0 -65 -46 -109.5t-123 -44.5q-79 0 -123 41.5t-44 113.5q0 106 64.5 182t157.5 76q87 0 138 -47l-53 -64q-31 34 -88 34q-52 0 -85 -40.5t-33 -72.5v-4q47 53 105 53q55 0 92.5 -32t37.5 -86zM307 402q0 26 -20 40.5t-53 14.5q-38 0 -70 -35q-2 -5 -2 -18
q0 -30 20 -48.5t53 -18.5q31 0 51.5 19.5t20.5 45.5z" />
    <glyph glyph-name="seven.numr" unicode="&#xf668;" horiz-adv-x="371" 
d="M419 601l-221 -334h-107l220 323h-209l17 77h315z" />
    <glyph glyph-name="eight.numr" unicode="&#xf669;" horiz-adv-x="397" 
d="M396 372q0 -49 -42 -80.5t-120 -31.5q-76 0 -128 27t-52 81q0 41 33.5 73.5t86.5 38.5q-69 27 -69 84q0 53 46 81t110 28q66 0 119 -27t53 -78q0 -39 -28.5 -68t-81.5 -36q30 -12 51.5 -36t21.5 -56zM334 554q0 21 -20 33t-50 12q-29 0 -47.5 -12.5t-18.5 -35.5
q0 -20 19.5 -33t38.5 -16q78 7 78 52zM303 383q0 19 -19 34.5t-43 21.5q-37 -2 -63 -18t-26 -40q0 -21 22.5 -34.5t53.5 -13.5q33 0 54 14t21 36z" />
    <glyph glyph-name="nine.numr" unicode="&#xf66a;" horiz-adv-x="397" 
d="M92 520q0 64 46 109t123 45q79 0 122.5 -41.5t43.5 -113.5q0 -106 -64.5 -182t-156.5 -76q-86 0 -139 47l54 64q31 -34 88 -34q51 0 84.5 40.5t33.5 73.5v3q-47 -53 -106 -53q-54 0 -91.5 32t-37.5 86zM189 531q0 -26 21 -40.5t53 -14.5q38 0 69 36q2 5 2 18
q0 30 -19.5 48.5t-52.5 18.5q-32 0 -52.5 -19.5t-20.5 -46.5z" />
    <glyph glyph-name="Abreve.smcp" unicode="&#xf66d;" horiz-adv-x="597" 
d="M285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM564 669q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="Amacron.smcp" unicode="&#xf66e;" horiz-adv-x="597" 
d="M560 612h-362l16 72h362zM285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163z" />
    <glyph glyph-name="Aogonek.smcp" unicode="&#xf66f;" horiz-adv-x="597" 
d="M285 550h161l102 -550h-1q-93 -43 -93 -100q0 -33 35 -33q27 0 47 34l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 65 60 110h-49l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163z" />
    <glyph glyph-name="AEacute.smcp" unicode="&#xf670;" horiz-adv-x="845" 
d="M752 750l-194 -144h-91l166 144h119zM749 0h-415l20 90h-189l-74 -90h-151l463 550h467l-25 -113h-283l-22 -101h276l-25 -113h-276l-24 -110h284zM379 205l47 211l-173 -211h126z" />
    <glyph glyph-name="Cacute.smcp" unicode="&#xf671;" horiz-adv-x="585" 
d="M630 750l-194 -144h-91l166 144h119zM311 -12q-126 0 -202.5 70t-76.5 181q0 132 96 227.5t234 95.5q87 0 149.5 -42.5t87.5 -111.5l-119 -43q-14 36 -48 58.5t-75 22.5q-76 0 -132.5 -58.5t-56.5 -138.5q0 -66 42 -105.5t108 -39.5q80 0 133 71l99 -61
q-40 -56 -101.5 -91t-137.5 -35z" />
    <glyph glyph-name="Ccaron.smcp" unicode="&#xf672;" horiz-adv-x="585" 
d="M472 606h-114l-63 144h76l53 -90l91 90h80zM311 -12q-126 0 -202.5 70t-76.5 181q0 132 96 227.5t234 95.5q87 0 149.5 -42.5t87.5 -111.5l-119 -43q-14 36 -48 58.5t-75 22.5q-76 0 -132.5 -58.5t-56.5 -138.5q0 -66 42 -105.5t108 -39.5q80 0 133 71l99 -61
q-40 -56 -101.5 -91t-137.5 -35z" />
    <glyph glyph-name="Ccircumflex.smcp" unicode="&#xf673;" horiz-adv-x="585" 
d="M563 606h-76l-54 89l-90 -89h-80l124 144h114zM311 -12q-126 0 -202.5 70t-76.5 181q0 132 96 227.5t234 95.5q87 0 149.5 -42.5t87.5 -111.5l-119 -43q-14 36 -48 58.5t-75 22.5q-76 0 -132.5 -58.5t-56.5 -138.5q0 -66 42 -105.5t108 -39.5q80 0 133 71l99 -61
q-40 -56 -101.5 -91t-137.5 -35z" />
    <glyph glyph-name="Cdotaccent.smcp" unicode="&#xf674;" horiz-adv-x="585" 
d="M311 -12q-126 0 -202.5 70t-76.5 181q0 132 96 227.5t234 95.5q87 0 149.5 -42.5t87.5 -111.5l-119 -43q-14 36 -48 58.5t-75 22.5q-76 0 -132.5 -58.5t-56.5 -138.5q0 -66 42 -105.5t108 -39.5q80 0 133 71l99 -61q-40 -56 -101.5 -91t-137.5 -35zM493 668
q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="Dcaron.smcp" unicode="&#xf675;" horiz-adv-x="626" 
d="M455 606h-114l-63 144h76l53 -90l91 90h80zM254 0h-245l121 550h200q119 0 198 -64t79 -171q0 -44 -11 -86.5t-37.5 -84.5t-66 -73.5t-101 -51t-137.5 -19.5zM266 115h13q93 0 143 58t50 132q0 60 -42 95t-115 35h-78l-70 -320h99z" />
    <glyph glyph-name="Dcroat.smcp" unicode="&#xf676;" horiz-adv-x="626" 
d="M254 0h-245l51 232h-55l20 91h55l50 227h200q119 0 198 -64t79 -171q0 -44 -11 -86.5t-37.5 -84.5t-66 -73.5t-101 -51t-137.5 -19.5zM279 115q93 0 143 58t50 132q0 60 -42 95t-115 35h-78l-25 -112h112l-20 -91h-112l-25 -117h112z" />
    <glyph glyph-name="Ebreve.smcp" unicode="&#xf677;" horiz-adv-x="520" 
d="M424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282zM535 669q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="Ecaron.smcp" unicode="&#xf678;" horiz-adv-x="520" 
d="M410 606h-114l-63 144h76l53 -90l91 90h80zM424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282z" />
    <glyph glyph-name="Edotaccent.smcp" unicode="&#xf679;" horiz-adv-x="520" 
d="M424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282zM431 668q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="Emacron.smcp" unicode="&#xf67a;" horiz-adv-x="520" 
d="M424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282zM532 612h-362l16 72h362z" />
    <glyph glyph-name="Eng.smcp" unicode="&#xf67b;" horiz-adv-x="648" 
d="M312 -196q-119 0 -174 70l76 94q29 -47 87 -47q76 0 96 77l-177 361l-79 -359h-132l121 550h142l172 -347l77 347h132l-123 -556q-20 -97 -72.5 -143.5t-145.5 -46.5z" />
    <glyph glyph-name="Eogonek.smcp" unicode="&#xf67c;" horiz-adv-x="520" 
d="M413 -99l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 65 60 110h-319l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282l-24 -113q-93 -43 -93 -100q0 -33 35 -33q27 0 47 34z" />
    <glyph glyph-name="Gbreve.smcp" unicode="&#xf67d;" horiz-adv-x="616" 
d="M316 -12q-122 0 -203 68t-81 180q0 132 94.5 229t237.5 97q78 0 141 -36.5t91 -95.5l-118 -52q-15 29 -48.5 48.5t-75.5 19.5q-79 0 -132 -59.5t-53 -138.5q0 -64 43.5 -104t109.5 -40q57 0 93 24l17 76h-131l24 106h262l-54 -247q-99 -75 -217 -75zM599 669
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="Gcircumflex.smcp" unicode="&#xf67e;" horiz-adv-x="616" 
d="M316 -12q-122 0 -203 68t-81 180q0 132 94.5 229t237.5 97q78 0 141 -36.5t91 -95.5l-118 -52q-15 29 -48.5 48.5t-75.5 19.5q-79 0 -132 -59.5t-53 -138.5q0 -64 43.5 -104t109.5 -40q57 0 93 24l17 76h-131l24 106h262l-54 -247q-99 -75 -217 -75zM564 606h-76l-54 89
l-90 -89h-80l124 144h114z" />
    <glyph glyph-name="Gcommaaccent.smcp" unicode="&#xf67f;" horiz-adv-x="616" 
d="M316 -12q-122 0 -203 68t-81 180q0 132 94.5 229t237.5 97q78 0 141 -36.5t91 -95.5l-118 -52q-15 29 -48.5 48.5t-75.5 19.5q-79 0 -132 -59.5t-53 -138.5q0 -64 43.5 -104t109.5 -40q57 0 93 24l17 76h-131l24 106h262l-54 -247q-99 -75 -217 -75zM317 -121
q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5z" />
    <glyph glyph-name="Gdotaccent.smcp" unicode="&#xf680;" horiz-adv-x="616" 
d="M316 -12q-122 0 -203 68t-81 180q0 132 94.5 229t237.5 97q78 0 141 -36.5t91 -95.5l-118 -52q-15 29 -48.5 48.5t-75.5 19.5q-79 0 -132 -59.5t-53 -138.5q0 -64 43.5 -104t109.5 -40q57 0 93 24l17 76h-131l24 106h262l-54 -247q-99 -75 -217 -75zM494 668
q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="Hcircumflex.smcp" unicode="&#xf681;" horiz-adv-x="651" 
d="M555 606h-76l-54 89l-90 -89h-80l124 144h114zM534 0h-132l50 224h-261l-50 -224h-132l121 550h132l-46 -211h261l46 211h132z" />
    <glyph glyph-name="Hbar.smcp" unicode="&#xf682;" horiz-adv-x="663" 
d="M540 0h-132l50 224h-261l-50 -224h-132l90 411h-62l16 73h62l15 66h132l-15 -66h261l15 66h132l-15 -66h60l-15 -73h-61zM222 339h261l15 72h-261z" />
    <glyph glyph-name="Ibreve.smcp" unicode="&#xf683;" horiz-adv-x="258" 
d="M141 0h-132l121 550h132zM393 669q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="IJ.smcp" unicode="&#xf684;" horiz-adv-x="690" 
d="M396 -12q-118 0 -174 71l75 92q30 -47 87 -47q80 0 100 87l79 359h132l-81 -370q-23 -102 -75.5 -147t-142.5 -45zM141 0h-132l121 550h132z" />
    <glyph glyph-name="Imacron.smcp" unicode="&#xf685;" horiz-adv-x="258" 
d="M390 612h-362l16 72h362zM141 0h-132l121 550h132z" />
    <glyph glyph-name="Iogonek.smcp" unicode="&#xf686;" horiz-adv-x="258" 
d="M141 0q-93 -43 -93 -100q0 -33 35 -33q27 0 47 34l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 65 60 110h-36l121 550h132z" />
    <glyph glyph-name="Itilde.smcp" unicode="&#xf687;" horiz-adv-x="258" 
d="M141 0h-132l121 550h132zM280 604q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="Jcircumflex.smcp" unicode="&#xf688;" horiz-adv-x="433" 
d="M533 606h-76l-54 89l-90 -89h-80l124 144h114zM138 -12q-118 0 -174 71l75 92q30 -47 87 -47q80 0 100 87l79 359h132l-81 -370q-23 -102 -75.5 -147t-142.5 -45z" />
    <glyph glyph-name="Kcommaaccent.smcp" unicode="&#xf689;" horiz-adv-x="566" 
d="M274 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM511 0h-158l-124 216l-50 -47l-38 -169h-132l121 550h132l-50 -230l243 230h162l-287 -259z" />
    <glyph glyph-name="Lacute.smcp" unicode="&#xf68a;" horiz-adv-x="469" 
d="M383 0h-374l121 550h132l-96 -435h242zM549 750l-194 -144h-91l166 144h119z" />
    <glyph glyph-name="Lcaron.smcp" unicode="&#xf68b;" horiz-adv-x="469" 
d="M442 495q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM383 0h-374l121 550h132l-96 -435h242z" />
    <glyph glyph-name="Lcommaaccent.smcp" unicode="&#xf68c;" horiz-adv-x="469" 
d="M235 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM383 0h-374l121 550h132l-96 -435h242z" />
    <glyph glyph-name="Ldot.smcp" unicode="&#xf68d;" horiz-adv-x="499" 
d="M383 0h-374l121 550h132l-96 -435h242zM387 224q-31 0 -52.5 22t-21.5 52q0 37 26 62.5t63 25.5q30 0 51.5 -22t21.5 -53q0 -35 -26.5 -61t-61.5 -26z" />
    <glyph glyph-name="Nacute.smcp" unicode="&#xf68e;" horiz-adv-x="648" 
d="M622 750l-194 -144h-91l166 144h119zM532 0h-136l-175 366l-80 -366h-132l121 550h142l171 -354l78 354h132z" />
    <glyph glyph-name="Ncaron.smcp" unicode="&#xf68f;" horiz-adv-x="648" 
d="M462 606h-114l-63 144h76l53 -90l91 90h80zM532 0h-136l-175 366l-80 -366h-132l121 550h142l171 -354l78 354h132z" />
    <glyph glyph-name="Ncommaaccent.smcp" unicode="&#xf690;" horiz-adv-x="648" 
d="M306 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM532 0h-136l-175 366l-80 -366h-132l121 550h142l171 -354l78 354h132z" />
    <glyph glyph-name="Obreve.smcp" unicode="&#xf691;" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM595 676q-85 -88 -194 -88
q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="Ohungarumlaut.smcp" unicode="&#xf692;" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM502 750l-147 -144h-76
l119 144h104zM651 750l-147 -144h-76l119 144h104z" />
    <glyph glyph-name="Omacron.smcp" unicode="&#xf693;" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM590 612h-362l16 72h362z" />
    <glyph glyph-name="Oslashacute.smcp" unicode="&#xf694;" horiz-adv-x="655" 
d="M311 -12q-99 0 -169 45l-33 -33h-107l83 83q-53 65 -53 158q0 131 93.5 226t229.5 95q97 0 167 -45l33 33h107l-83 -82q55 -68 55 -159q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 39 -17 71l-248 -247q34 -20 80 -20zM167 250q0 -38 16 -70l247 246
q-32 20 -78 20q-80 0 -132.5 -59t-52.5 -137zM627 750l-194 -144h-91l166 144h119z" />
    <glyph glyph-name="Racute.smcp" unicode="&#xf695;" horiz-adv-x="573" 
d="M594 750l-194 -144h-91l166 144h119zM485 0h-149l-68 189h-85l-42 -189h-132l121 550h245q89 0 140.5 -43.5t51.5 -112.5q0 -75 -46 -130t-122 -66zM338 303h7q38 0 61 22.5t23 55.5q0 24 -17.5 39t-47.5 15h-126l-30 -132h130z" />
    <glyph glyph-name="Rcaron.smcp" unicode="&#xf696;" horiz-adv-x="573" 
d="M434 606h-114l-63 144h76l53 -90l91 90h80zM485 0h-149l-68 189h-85l-42 -189h-132l121 550h245q89 0 140.5 -43.5t51.5 -112.5q0 -75 -46 -130t-122 -66zM338 303h7q38 0 61 22.5t23 55.5q0 24 -17.5 39t-47.5 15h-126l-30 -132h130z" />
    <glyph glyph-name="Rcommaaccent.smcp" unicode="&#xf697;" horiz-adv-x="573" 
d="M273 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM485 0h-149l-68 189h-85l-42 -189h-132l121 550h245q89 0 140.5 -43.5t51.5 -112.5q0 -75 -46 -130t-122 -66zM338 303h7
q38 0 61 22.5t23 55.5q0 24 -17.5 39t-47.5 15h-126l-30 -132h130z" />
    <glyph glyph-name="Sacute.smcp" unicode="&#xf698;" horiz-adv-x="516" 
d="M562 750l-194 -144h-91l166 144h119zM249 -12q-83 0 -152 24.5t-110 71.5l77 96q29 -36 81 -57.5t112 -21.5q43 0 62 17t19 43q0 21 -28 36t-68.5 28t-81 30t-68.5 51t-28 81q0 68 63.5 121.5t160.5 53.5q72 0 135 -26t101 -67l-77 -89q-28 31 -74.5 50.5t-90.5 19.5
q-35 0 -57 -16.5t-22 -39.5q0 -20 28.5 -34t68.5 -27t80.5 -30t69 -51.5t28.5 -83.5q0 -73 -60 -126.5t-169 -53.5z" />
    <glyph glyph-name="Scedilla.smcp" unicode="&#xf699;" horiz-adv-x="516" 
d="M180 -196q-78 0 -119 41l35 48q37 -35 84 -35q50 0 50 35q0 24 -30 24q-17 0 -29 -14l-40 26l38 65q-118 19 -182 90l77 96q29 -36 81 -57.5t112 -21.5q43 0 62 17t19 43q0 21 -28 36t-68.5 28t-81 30t-68.5 51t-28 81q0 68 63.5 121.5t160.5 53.5q72 0 135 -26t101 -67
l-77 -89q-28 31 -74.5 50.5t-90.5 19.5q-35 0 -57 -16.5t-22 -39.5q0 -20 28.5 -34t68.5 -27t80.5 -30t69 -51.5t28.5 -83.5q0 -73 -60 -126.5t-169 -53.5q-15 0 -22 1l-24 -40q14 10 31 10q26 0 42.5 -16.5t16.5 -45.5q0 -42 -32 -67.5t-81 -25.5z" />
    <glyph glyph-name="Scircumflex.smcp" unicode="&#xf69a;" horiz-adv-x="516" 
d="M492 606h-76l-54 89l-90 -89h-80l124 144h114zM249 -12q-83 0 -152 24.5t-110 71.5l77 96q29 -36 81 -57.5t112 -21.5q43 0 62 17t19 43q0 21 -28 36t-68.5 28t-81 30t-68.5 51t-28 81q0 68 63.5 121.5t160.5 53.5q72 0 135 -26t101 -67l-77 -89q-28 31 -74.5 50.5
t-90.5 19.5q-35 0 -57 -16.5t-22 -39.5q0 -20 28.5 -34t68.5 -27t80.5 -30t69 -51.5t28.5 -83.5q0 -73 -60 -126.5t-169 -53.5z" />
    <glyph glyph-name="Scommaaccent.smcp" unicode="&#xf69b;" horiz-adv-x="516" 
d="M246 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM249 -12q-83 0 -152 24.5t-110 71.5l77 96q29 -36 81 -57.5t112 -21.5q43 0 62 17t19 43q0 21 -28 36t-68.5 28t-81 30
t-68.5 51t-28 81q0 68 63.5 121.5t160.5 53.5q72 0 135 -26t101 -67l-77 -89q-28 31 -74.5 50.5t-90.5 19.5q-35 0 -57 -16.5t-22 -39.5q0 -20 28.5 -34t68.5 -27t80.5 -30t69 -51.5t28.5 -83.5q0 -73 -60 -126.5t-169 -53.5z" />
    <glyph glyph-name="Tbar.smcp" unicode="&#xf69c;" horiz-adv-x="486" 
d="M255 0h-132l45 206h-102l17 73h101l34 156h-156l26 115h445l-26 -115h-156l-35 -156h104l-17 -73h-103z" />
    <glyph glyph-name="Tcaron.smcp" unicode="&#xf69d;" horiz-adv-x="486" 
d="M381 606h-114l-63 144h76l53 -90l91 90h80zM255 0h-132l95 435h-156l26 115h445l-26 -115h-156z" />
    <glyph glyph-name="Tcommaaccent.smcp" unicode="&#xf69e;" horiz-adv-x="486" 
d="M227 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM255 0h-132l95 435h-156l26 115h445l-26 -115h-156z" />
    <glyph glyph-name="Ubreve.smcp" unicode="&#xf69f;" horiz-adv-x="646" 
d="M314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-25 -115 -86.5 -173t-176.5 -58zM587 669q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62
q74 0 133 62z" />
    <glyph glyph-name="Uhungarumlaut.smcp" unicode="&#xf6a0;" horiz-adv-x="646" 
d="M314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-25 -115 -86.5 -173t-176.5 -58zM496 750l-147 -144h-76l119 144h104zM645 750l-147 -144h-76l119 144h104z" />
    <glyph glyph-name="Umacron.smcp" unicode="&#xf6a1;" horiz-adv-x="646" 
d="M314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-25 -115 -86.5 -173t-176.5 -58zM584 612h-362l16 72h362z" />
    <glyph glyph-name="Uogonek.smcp" unicode="&#xf6a2;" horiz-adv-x="646" 
d="M394 -99l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 55 46 98q-118 4 -181 55.5t-63 131.5q0 20 5 43l74 332h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-41 -185 -175 -220q-90 -43 -90 -99q0 -33 35 -33
q27 0 47 34z" />
    <glyph glyph-name="Uring.smcp" unicode="&#xf6a3;" horiz-adv-x="646" 
d="M314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-25 -115 -86.5 -173t-176.5 -58zM408 571q-42 0 -68.5 26.5t-26.5 69.5q0 46 34.5 80.5t80.5 34.5
q43 0 69 -26.5t26 -69.5q0 -47 -34 -81t-81 -34zM414 630q20 0 35 15t15 35q0 19 -11 31t-30 12q-20 0 -35.5 -15t-15.5 -35q0 -19 11.5 -31t30.5 -12z" />
    <glyph glyph-name="Utilde.smcp" unicode="&#xf6a4;" horiz-adv-x="646" 
d="M314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-25 -115 -86.5 -173t-176.5 -58zM473 604q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5
t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="Wcircumflex.smcp" unicode="&#xf6a6;" horiz-adv-x="797" 
d="M627 606h-76l-54 89l-90 -89h-80l124 144h114zM578 0h-138l-17 358l-175 -358h-137l-45 550h147l12 -376l188 376h103l22 -377l179 377h148z" />
    <glyph glyph-name="Ycircumflex.smcp" unicode="&#xf6a9;" horiz-adv-x="565" 
d="M514 606h-76l-54 89l-90 -89h-80l124 144h114zM295 0h-131l50 226l-152 324h147l94 -213l183 213h151l-293 -324z" />
    <glyph glyph-name="Zacute.smcp" unicode="&#xf6ab;" horiz-adv-x="523" 
d="M557 750l-194 -144h-91l166 144h119zM434 0h-453l21 96l336 338h-261l26 116h445l-20 -96l-336 -338h267z" />
    <glyph glyph-name="Zdotaccent.smcp" unicode="&#xf6ac;" horiz-adv-x="523" 
d="M434 0h-453l21 96l336 338h-261l26 116h445l-20 -96l-336 -338h267zM419 668q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="Idotaccent.smcp" unicode="&#xf6ad;" horiz-adv-x="258" 
d="M141 0h-132l121 550h132zM288 668q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="parenleft.case" unicode="&#xf6ae;" horiz-adv-x="297" 
d="M215 -56l-92 -53q-80 119 -80 306q0 156 73 309.5t200 268.5l67 -68q-206 -248 -206 -545q0 -104 38 -218z" />
    <glyph glyph-name="parenright.case" unicode="&#xf6af;" horiz-adv-x="297" 
d="M121 722l92 53q80 -122 80 -306q0 -156 -73.5 -309.5t-200.5 -268.5l-66 68q206 248 206 544q0 105 -38 219z" />
    <glyph glyph-name="bracketleft.case" unicode="&#xf6b0;" horiz-adv-x="280" 
d="M186 -100h-216l193 868h216l-19 -85h-124l-155 -698h124z" />
    <glyph glyph-name="bracketright.case" unicode="&#xf6b1;" horiz-adv-x="280" 
d="M156 -100h-215l19 85h124l155 698h-124l18 85h216z" />
    <glyph glyph-name="braceleft.case" unicode="&#xf6b2;" horiz-adv-x="288" 
d="M194 -100h-45q-57 0 -98 36t-41 92q0 18 4 31l39 172q2 12 2 18q0 21 -11 35t-29 14l16 72q53 0 67 67l38 171q18 80 67.5 120t122.5 40h61l-19 -85h-60q-63 0 -80 -74l-43 -195q-18 -74 -66 -89q29 -17 29 -56q0 -18 -4 -34l-38 -175q-2 -12 -2 -19q0 -25 15.5 -40.5
t39.5 -15.5h55z" />
    <glyph glyph-name="braceright.case" unicode="&#xf6b3;" horiz-adv-x="288" 
d="M133 768h45q57 0 98 -36t41 -92q0 -19 -4 -31l-38 -172q-2 -12 -2 -18q0 -22 10.5 -35.5t28.5 -13.5l-16 -72q-53 0 -67 -67l-38 -171q-18 -80 -67.5 -120t-122.5 -40h-61l19 85h60q63 0 80 74l44 195q16 74 65 90q-29 15 -29 55q0 18 4 34l38 175q2 12 2 19
q0 25 -15.5 40.5t-39.5 15.5h-55z" />
    <glyph glyph-name="exclamdown.case" unicode="&#xf6b4;" horiz-adv-x="256" 
d="M116 449h112l-78 -449h-154zM211 679q31 0 52.5 -22t21.5 -54q0 -35 -26.5 -61t-62.5 -26q-31 0 -52 22t-21 53q0 36 26.5 62t61.5 26z" />
    <glyph glyph-name="questiondown.case" unicode="&#xf6b5;" horiz-adv-x="456" 
d="M210 431l117 31q16 -29 16 -70q0 -50 -30.5 -85.5t-66.5 -52t-66.5 -39t-30.5 -48.5q0 -53 82 -53q70 0 121 48l60 -98q-89 -75 -203 -75q-90 0 -150.5 40t-60.5 108q0 49 23 85t56 56t65.5 37t55.5 38t23 49q0 17 -11 29zM316 678q31 0 52 -22t21 -53q0 -36 -26 -62
t-63 -26q-30 0 -51.5 22t-21.5 53q0 36 26.5 62t62.5 26z" />
    <glyph glyph-name="guillemotleft.case" unicode="&#xf6b6;" horiz-adv-x="527" 
d="M284 162h-113l-120 180l200 177h121l-204 -182zM474 162h-113l-120 180l200 177h121l-204 -182z" />
    <glyph glyph-name="guillemotright.case" unicode="&#xf6b7;" horiz-adv-x="527" 
d="M98 519h113l120 -180l-200 -177h-121l204 182zM288 519h113l120 -180l-200 -177h-121l204 182z" />
    <glyph glyph-name="guilsinglleft.case" unicode="&#xf6b8;" horiz-adv-x="337" 
d="M284 162h-113l-120 180l200 177h121l-204 -182z" />
    <glyph glyph-name="guilsinglright.case" unicode="&#xf6b9;" horiz-adv-x="337" 
d="M99 519h113l120 -180l-200 -177h-121l204 182z" />
    <glyph glyph-name="hyphen.case" unicode="&#xf6ba;" horiz-adv-x="300" 
d="M279 287h-240l24 108h240z" />
    <glyph glyph-name="endash.case" unicode="&#xf6bb;" horiz-adv-x="593" 
d="M572 287h-533l24 108h533z" />
    <glyph glyph-name="emdash.case" unicode="&#xf6bc;" horiz-adv-x="833" 
d="M812 287h-773l24 108h773z" />
    <glyph glyph-name="periodcentered.case" unicode="&#xf6bd;" horiz-adv-x="256" 
d="M142 262q-31 0 -52.5 22t-21.5 52q0 37 26 62.5t63 25.5q30 0 51.5 -22t21.5 -53q0 -35 -26.5 -61t-61.5 -26z" />
    <glyph glyph-name="bullet.case" unicode="&#xf6be;" horiz-adv-x="358" 
d="M321 353q0 -53 -39.5 -91.5t-93.5 -38.5q-46 0 -76.5 30.5t-30.5 76.5q0 53 39.5 91.5t93.5 38.5q46 0 76.5 -30.5t30.5 -76.5z" />
    <glyph glyph-name="cent.case" unicode="&#xf6bf;" horiz-adv-x="499" 
d="M185 0l20 89q-74 20 -115.5 75.5t-41.5 134.5q0 116 77 199t191 88l18 81h93l-21 -91q80 -21 119 -85l-95 -73q-18 28 -49 44l-60 -268q46 9 78 46l69 -85q-68 -72 -173 -76l-17 -79h-93zM177 305q0 -68 54 -97l58 260q-51 -14 -81.5 -59t-30.5 -104z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="255" 
d="M113 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5z" />
    <glyph glyph-name="one.tnum" unicode="&#xf6dc;" horiz-adv-x="623" 
d="M397 0h-142l106 484l-135 -113l-63 85l257 211h124z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="1178" 
d="M898 -12q-103 0 -154 76l-55 -248h-127l148 667h127l-14 -60q61 72 146 72q83 0 133 -53t50 -147q0 -75 -28.5 -144t-88 -116t-137.5 -47zM877 101q62 0 102 49.5t40 118.5q0 51 -30 82t-77 31q-64 0 -111 -58l-37 -168q36 -55 113 -55zM552 0h-157l-79 237h-110
l-52 -237h-142l147 667h285q84 0 145 -52t61 -136q0 -89 -53.5 -153t-136.5 -79zM381 362h2q55 0 86.5 28.5t31.5 74.5q0 33 -25 55t-59 22h-143l-40 -180h147z" />
    <glyph glyph-name="centinferior" unicode="&#xf6df;" horiz-adv-x="312" 
d="M63 -151l12 60q-46 12 -73 45t-27 81q0 68 47.5 117.5t118.5 53.5l10 42h61l-11 -49q51 -16 72 -54l-66 -44q-8 18 -22 26l-34 -154q21 6 40 29l52 -50q-42 -46 -107 -50l-12 -53h-61zM64 39q0 -42 27 -59l34 153q-28 -9 -44.5 -35t-16.5 -59z" />
    <glyph glyph-name="centsuperior" unicode="&#xf6e0;" horiz-adv-x="312" 
d="M188 416l12 60q-46 12 -73 45t-27 81q0 68 47.5 117.5t118.5 53.5l10 42h61l-11 -49q51 -16 72 -54l-66 -44q-8 18 -22 26l-34 -154q21 6 40 29l52 -50q-42 -46 -107 -50l-12 -53h-61zM189 606q0 -42 27 -59l34 153q-28 -9 -44.5 -35t-16.5 -59z" />
    <glyph glyph-name="commainferior" unicode="&#xf6e1;" horiz-adv-x="157" 
d="M53 -94q0 -33 -24.5 -72t-67.5 -62l-28 33q16 7 34.5 21.5t23.5 27.5q-3 -1 -5 -1q-15 0 -27 13t-12 33q0 24 17.5 40.5t41.5 16.5q20 0 33.5 -13.5t13.5 -36.5z" />
    <glyph glyph-name="commasuperior" unicode="&#xf6e2;" horiz-adv-x="157" 
d="M178 473q0 -33 -24.5 -72t-67.5 -62l-28 33q16 7 34.5 21.5t23.5 27.5q-3 -1 -5 -1q-15 0 -27 13t-12 33q0 24 17.5 40.5t41.5 16.5q20 0 33.5 -13.5t13.5 -36.5z" />
    <glyph glyph-name="dollarinferior" unicode="&#xf6e3;" horiz-adv-x="383" 
d="M66 -206l12 59q-90 16 -125 68l63 59q29 -40 78 -54l21 93q-54 18 -85.5 42.5t-31.5 70.5q0 51 44.5 89.5t119.5 38.5h6l13 55h61l-14 -63q67 -17 104 -59l-64 -53q-22 27 -56 40l-19 -88q53 -19 83.5 -43t30.5 -68q0 -58 -42.5 -96t-124.5 -38h-2l-11 -53h-61zM101 149
q0 -21 35 -36l17 77q-24 -2 -38 -13t-14 -28zM204 -38q0 21 -32 36l-18 -78q50 6 50 42z" />
    <glyph glyph-name="dollarsuperior" unicode="&#xf6e4;" horiz-adv-x="383" 
d="M191 361l12 59q-90 16 -125 68l63 59q29 -40 78 -54l21 93q-54 18 -85.5 42.5t-31.5 70.5q0 51 44.5 89.5t119.5 38.5h6l13 55h61l-14 -63q67 -17 104 -59l-64 -53q-22 27 -56 40l-19 -88q53 -19 83.5 -43t30.5 -68q0 -58 -42.5 -96t-124.5 -38h-2l-11 -53h-61zM226 716
q0 -21 35 -36l17 77q-24 -2 -38 -13t-14 -28zM329 529q0 21 -32 36l-18 -78q50 6 50 42z" />
    <glyph glyph-name="hypheninferior" unicode="&#xf6e5;" horiz-adv-x="183" 
d="M115 17h-148l16 71h147z" />
    <glyph glyph-name="hyphensuperior" unicode="&#xf6e6;" horiz-adv-x="183" 
d="M240 584h-148l16 71h147z" />
    <glyph glyph-name="periodinferior" unicode="&#xf6e7;" horiz-adv-x="158" 
d="M57 -92q0 -23 -18.5 -41t-42.5 -18q-20 0 -34 14t-14 34q0 24 18.5 41.5t42.5 17.5q20 0 34 -13.5t14 -34.5z" />
    <glyph glyph-name="periodsuperior" unicode="&#xf6e8;" horiz-adv-x="158" 
d="M182 475q0 -23 -18.5 -41t-42.5 -18q-20 0 -34 14t-14 34q0 24 18.5 41.5t42.5 17.5q20 0 34 -13.5t14 -34.5z" />
    <glyph glyph-name="asuperior" unicode="&#xf6e9;" horiz-adv-x="414" 
d="M269 575q-41 0 -68.5 -33.5t-27.5 -79.5q0 -35 18.5 -53t49.5 -18q38 0 70 34l27 122q-22 28 -69 28zM358 640h93l-70 -314h-93l8 39q-40 -47 -94 -47q-53 0 -89 34.5t-36 97.5q0 83 49 140.5t120 57.5q70 0 104 -47z" />
    <glyph glyph-name="bsuperior" unicode="&#xf6ea;" horiz-adv-x="413" 
d="M250 390q40 0 67.5 34t27.5 80q0 35 -18.5 53t-49.5 18q-38 0 -69 -33l-27 -122q22 -30 69 -30zM161 326h-93l96 434h93l-35 -159q41 47 95 47q52 0 88 -35t36 -98q0 -83 -49 -140t-119 -57q-71 0 -104 46z" />
    <glyph glyph-name="dsuperior" unicode="&#xf6eb;" horiz-adv-x="414" 
d="M269 575q-41 0 -68.5 -33.5t-27.5 -79.5q0 -35 18.5 -53t49.5 -18q38 0 70 34l27 122q-22 28 -69 28zM385 760h92l-96 -434h-93l8 39q-40 -47 -94 -47q-53 0 -89 34.5t-36 97.5q0 83 49 140.5t120 57.5q70 0 104 -47z" />
    <glyph glyph-name="esuperior" unicode="&#xf6ec;" horiz-adv-x="391" 
d="M246 318q-75 0 -122.5 37.5t-47.5 107.5q0 75 54 130t134 55q62 0 105.5 -37t43.5 -104q0 -30 -7 -55h-236q-1 -3 -1 -6q0 -23 24 -41t61 -18q45 0 80 22l26 -57q-47 -34 -114 -34zM179 517h151v5q0 26 -18.5 43t-49.5 17q-33 0 -54.5 -19.5t-28.5 -45.5z" />
    <glyph glyph-name="isuperior" unicode="&#xf6ed;" horiz-adv-x="188" 
d="M190 671q-20 0 -33.5 13.5t-13.5 33.5q0 25 19 43t44 18q20 0 33.5 -13.5t13.5 -33.5q0 -25 -18.5 -43t-44.5 -18zM156 326h-92l69 314h92z" />
    <glyph glyph-name="lsuperior" unicode="&#xf6ee;" horiz-adv-x="188" 
d="M156 326h-92l96 434h92z" />
    <glyph glyph-name="msuperior" unicode="&#xf6ef;" horiz-adv-x="596" 
d="M559 326h-92l42 193q3 7 3 16q0 16 -11.5 25.5t-31.5 9.5q-34 0 -66 -37l-46 -207h-92l43 193q2 10 2 16q0 35 -44 35q-32 0 -64 -37l-46 -207h-92l69 314h92l-8 -40q41 48 99 48q37 0 62.5 -15.5t32.5 -41.5q46 57 107 57q40 0 66.5 -21t26.5 -57q0 -12 -2 -20z" />
    <glyph glyph-name="osuperior" unicode="&#xf6f0;" horiz-adv-x="405" 
d="M238 318q-73 0 -117.5 40.5t-44.5 105.5q0 77 57 130.5t133 53.5q74 0 118 -40.5t44 -105.5q0 -78 -56.5 -131t-133.5 -53zM241 392q43 0 67.5 33.5t24.5 77.5q0 32 -18.5 50.5t-50.5 18.5q-43 0 -68 -34t-25 -77q0 -32 19 -50.5t51 -18.5z" />
    <glyph glyph-name="rsuperior" unicode="&#xf6f1;" horiz-adv-x="258" 
d="M156 326h-92l69 314h92l-9 -40q56 49 114 49l-19 -83q-6 2 -23 2q-23 0 -48 -10t-38 -25z" />
    <glyph glyph-name="ssuperior" unicode="&#xf6f2;" horiz-adv-x="336" 
d="M203 318q-96 0 -155 62l50 52q15 -20 45.5 -35.5t62.5 -15.5q21 0 33.5 8.5t12.5 21.5q0 17 -25 29t-55.5 20.5t-55.5 30t-25 53.5q0 43 36 73.5t99 30.5q43 0 80.5 -14.5t57.5 -37.5l-46 -50q-37 39 -93 39q-44 0 -44 -31q0 -15 25.5 -25.5t56 -17.5t56 -29t25.5 -57
q0 -44 -38 -75.5t-103 -31.5z" />
    <glyph glyph-name="tsuperior" unicode="&#xf6f3;" horiz-adv-x="235" 
d="M187 318q-41 0 -68 16.5t-27 53.5q0 11 3 23l36 163h-53l14 66h53l19 86h93l-19 -86h65l-14 -66h-65l-33 -146q-2 -10 -2 -14q0 -23 28 -23q16 0 24 8l6 -64q-22 -17 -60 -17z" />
    <glyph glyph-name="Lslash.smcp" unicode="&#xf6f9;" horiz-adv-x="480" 
d="M-14 140l25 116l73 37l57 257h132l-40 -180l92 47l-26 -117l-92 -47l-30 -138h242l-25 -115h-374l39 177z" />
    <glyph glyph-name="OE.smcp" unicode="&#xf6fa;" horiz-adv-x="929" 
d="M833 0h-415l13 61q-25 -35 -67 -54t-90 -19q-107 0 -174.5 71t-67.5 182q0 130 93 225.5t232 95.5q53 0 98.5 -22t67.5 -64l16 74h415l-25 -113h-283l-22 -101h277l-25 -113h-277l-23 -110h282zM460 193l34 153q-14 49 -52.5 74.5t-87.5 25.5q-79 0 -133 -58t-54 -138
q0 -66 41 -106t106 -40q96 0 146 89z" />
    <glyph glyph-name="Scaron.smcp" unicode="&#xf6fd;" horiz-adv-x="516" 
d="M403 606h-114l-63 144h76l53 -90l91 90h80zM249 -12q-83 0 -152 24.5t-110 71.5l77 96q29 -36 81 -57.5t112 -21.5q43 0 62 17t19 43q0 21 -28 36t-68.5 28t-81 30t-68.5 51t-28 81q0 68 63.5 121.5t160.5 53.5q72 0 135 -26t101 -67l-77 -89q-28 31 -74.5 50.5
t-90.5 19.5q-35 0 -57 -16.5t-22 -39.5q0 -20 28.5 -34t68.5 -27t80.5 -30t69 -51.5t28.5 -83.5q0 -73 -60 -126.5t-169 -53.5z" />
    <glyph glyph-name="Zcaron.smcp" unicode="&#xf6ff;" horiz-adv-x="523" 
d="M398 606h-114l-63 144h76l53 -90l91 90h80zM434 0h-453l21 96l336 338h-261l26 116h445l-20 -96l-336 -338h267z" />
    <glyph glyph-name="exclam.smcp" unicode="&#xf721;" horiz-adv-x="256" 
d="M265 550l-94 -345h-102l57 345h139zM82 -12q-31 0 -52.5 22t-21.5 53q0 36 26.5 62t62.5 26q31 0 52 -22t21 -53q0 -36 -26 -62t-62 -26z" />
    <glyph glyph-name="dollar.onum" unicode="&#xf724;" horiz-adv-x="516" 
d="M147 -74l15 70q-115 19 -175 88l77 96q40 -52 123 -71l28 125q-69 24 -110 58t-41 95q0 68 63.5 121.5t160.5 53.5l15 66h90l-17 -77q91 -22 148 -82l-77 -89q-35 38 -96 58l-25 -113q70 -25 111 -59.5t41 -97.5q0 -73 -59.5 -126t-167.5 -54l-14 -62h-90zM338 161
q0 23 -39 42l-23 -101q62 7 62 59zM203 394q0 -22 38 -40l21 94q-27 -4 -43 -19.5t-16 -34.5z" />
    <glyph glyph-name="ampersand.smcp" unicode="&#xf726;" horiz-adv-x="567" 
d="M487 0h-138l-23 28q-69 -39 -141 -39q-83 0 -135.5 39.5t-52.5 111.5q0 74 43 115t118 67q-16 50 -16 86q0 65 50.5 109.5t124.5 44.5q63 0 108.5 -31.5t45.5 -82.5q0 -35 -13.5 -62t-42 -45.5t-53.5 -29t-65 -23.5q15 -28 32 -54q8 -14 21.5 -35.5t17.5 -28.5
q49 52 81 114l84 -48q-54 -82 -116 -140q42 -60 70 -96zM205 77q35 0 72 20q-37 59 -45 74q-27 45 -42 77q-69 -35 -69 -94q0 -35 22.5 -56t61.5 -21zM256 408q0 -22 11 -53q50 16 76 34.5t26 49.5q0 20 -13 31t-34 11q-29 0 -47.5 -20t-18.5 -53z" />
    <glyph glyph-name="zero.onum" unicode="&#xf730;" horiz-adv-x="620" 
d="M598 320q0 -57 -20 -115t-56.5 -107t-95.5 -79.5t-129 -30.5q-125 0 -194.5 64t-69.5 177q0 58 20 115.5t56.5 107t95.5 80t130 30.5q125 0 194 -64t69 -178zM452 310q0 62 -31 94t-88 32q-71 0 -112.5 -62.5t-41.5 -133.5q0 -62 31 -94t88 -32q71 0 112.5 62.5
t41.5 133.5z" />
    <glyph glyph-name="one.onum" unicode="&#xf731;" horiz-adv-x="411" 
d="M291 0h-142l80 367l-135 -113l-63 85l257 211h124z" />
    <glyph glyph-name="two.onum" unicode="&#xf732;" horiz-adv-x="582" 
d="M465 0h-469l26 119q190 56 289.5 118.5t99.5 122.5q0 35 -29.5 55.5t-77.5 20.5q-97 0 -177 -65l-60 100q103 91 252 91q108 0 176 -44.5t68 -123.5q0 -46 -25 -89t-68 -76.5t-89.5 -58.5t-98.5 -45h211z" />
    <glyph glyph-name="three.onum" unicode="&#xf733;" 
d="M245 -127q-94 0 -174.5 37t-117.5 102l95 86q30 -47 85 -73t110 -26q57 0 88 25.5t31 66.5q0 70 -122 70q-59 0 -81 -2l29 128q11 -1 93 -1q130 0 130 77q0 33 -34.5 53t-96.5 20q-93 0 -162 -54l-51 97q91 83 227 83q121 0 192.5 -46t71.5 -128q0 -64 -55 -110t-125 -55
q124 -31 124 -146q0 -88 -74.5 -146t-182.5 -58z" />
    <glyph glyph-name="four.onum" unicode="&#xf734;" horiz-adv-x="597" 
d="M523 27h-85l-32 -144h-142l32 144h-319l25 113l355 410h196l-87 -398h85zM323 152l60 272l-240 -272h180z" />
    <glyph glyph-name="five.onum" unicode="&#xf735;" horiz-adv-x="608" 
d="M270 -129q-191 0 -278 128l101 90q57 -92 179 -92q57 0 91.5 31t34.5 77q0 43 -34.5 66.5t-91.5 23.5q-69 0 -122 -40l-92 37l79 358h458l-27 -125h-317l-34 -157q54 43 133 43q84 0 140.5 -50.5t56.5 -138.5q0 -105 -78 -178t-199 -73z" />
    <glyph glyph-name="six.onum" unicode="&#xf736;" horiz-adv-x="608" 
d="M301 -12q-123 0 -192.5 68t-69.5 190q0 111 40.5 207.5t124 160t193.5 63.5q141 0 224 -91l-88 -100q-50 65 -144 65q-68 0 -117 -48t-70 -116q-3 -6 -9 -26q28 29 75.5 49.5t97.5 20.5q91 0 151 -50t60 -134q0 -107 -81 -183t-195 -76zM301 114q54 0 91 33.5t37 81.5
q0 40 -37 64t-92 24q-67 0 -121 -51q-1 -7 -1 -34q0 -54 34.5 -86t88.5 -32z" />
    <glyph glyph-name="seven.onum" unicode="&#xf737;" horiz-adv-x="545" 
d="M199 -117h-163l356 542h-328l27 125h501l-22 -99z" />
    <glyph glyph-name="eight.onum" unicode="&#xf738;" horiz-adv-x="606" 
d="M285 -12q-114 0 -192.5 45.5t-78.5 133.5q0 66 49 117.5t136 73.5q-43 19 -71.5 54.5t-28.5 79.5q0 62 40.5 105t98 61.5t123.5 18.5q105 0 182.5 -44.5t77.5 -126.5q0 -65 -47.5 -112.5t-125.5 -62.5q50 -26 80.5 -65.5t30.5 -85.5q0 -88 -81 -140t-193 -52zM337 399
q59 4 95 25.5t36 56.5q0 31 -33.5 52t-82.5 21q-46 0 -78.5 -21t-32.5 -56q0 -26 29.5 -48t66.5 -30zM291 110q50 0 87 23.5t37 59.5q0 31 -31.5 54.5t-73.5 33.5q-62 -3 -103.5 -28t-41.5 -64q0 -38 34.5 -58.5t91.5 -20.5z" />
    <glyph glyph-name="nine.onum" unicode="&#xf739;" horiz-adv-x="608" 
d="M319 562q123 0 192.5 -68t69.5 -190q0 -82 -24 -158.5t-68 -137.5t-113.5 -98t-153.5 -37q-140 0 -222 90l87 101q47 -65 145 -65q68 0 116 47.5t71 115.5q7 22 8 27q-29 -30 -76 -50.5t-97 -20.5q-91 0 -151.5 50.5t-60.5 134.5q0 106 82 182.5t195 76.5zM319 436
q-54 0 -91 -33.5t-37 -81.5q0 -40 36.5 -64t91.5 -24q69 0 122 50q1 7 1 34q0 54 -34.5 86.5t-88.5 32.5z" />
    <glyph glyph-name="question.smcp" unicode="&#xf73f;" horiz-adv-x="396" 
d="M424 435q0 -45 -29.5 -76t-64.5 -44.5t-64.5 -32t-29.5 -40.5q0 -12 13 -25l-104 -26q-18 24 -18 51q0 31 16.5 54t40 36t47 24t40 24t16.5 30q0 20 -20 31.5t-53 11.5q-64 0 -118 -48l-46 86q80 70 184 70q83 0 136.5 -35.5t53.5 -90.5zM150 -12q-31 0 -52.5 22
t-21.5 53q0 36 26.5 62t62.5 26q31 0 52.5 -22t21.5 -53q0 -36 -26.5 -62t-62.5 -26z" />
    <glyph glyph-name="A.smcp" unicode="&#xf761;" horiz-adv-x="597" 
d="M285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163z" />
    <glyph glyph-name="B.smcp" unicode="&#xf762;" horiz-adv-x="586" 
d="M342 0h-333l121 550h279q77 0 123.5 -34.5t46.5 -92.5q0 -52 -34.5 -94.5t-90.5 -50.5q37 -12 58.5 -42.5t21.5 -65.5q0 -74 -51.5 -122t-140.5 -48zM166 112h164q30 0 50 17.5t20 44.5q0 22 -15 36t-41 14h-154zM365 336h9q29 0 46 17.5t17 40.5q0 21 -16 32.5t-41 11.5
h-142l-23 -102h150z" />
    <glyph glyph-name="C.smcp" unicode="&#xf763;" horiz-adv-x="585" 
d="M311 -12q-126 0 -202.5 70t-76.5 181q0 132 96 227.5t234 95.5q87 0 149.5 -42.5t87.5 -111.5l-119 -43q-14 36 -48 58.5t-75 22.5q-76 0 -132.5 -58.5t-56.5 -138.5q0 -66 42 -105.5t108 -39.5q80 0 133 71l99 -61q-40 -56 -101.5 -91t-137.5 -35z" />
    <glyph glyph-name="D.smcp" unicode="&#xf764;" horiz-adv-x="626" 
d="M254 0h-245l121 550h200q119 0 198 -64t79 -171q0 -44 -11 -86.5t-37.5 -84.5t-66 -73.5t-101 -51t-137.5 -19.5zM266 115h13q93 0 143 58t50 132q0 60 -42 95t-115 35h-78l-70 -320h99z" />
    <glyph glyph-name="E.smcp" unicode="&#xf765;" horiz-adv-x="520" 
d="M424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282z" />
    <glyph glyph-name="F.smcp" unicode="&#xf766;" horiz-adv-x="509" 
d="M141 0h-132l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277z" />
    <glyph glyph-name="G.smcp" unicode="&#xf767;" horiz-adv-x="616" 
d="M316 -12q-122 0 -203 68t-81 180q0 132 94.5 229t237.5 97q78 0 141 -36.5t91 -95.5l-118 -52q-15 29 -48.5 48.5t-75.5 19.5q-79 0 -132 -59.5t-53 -138.5q0 -64 43.5 -104t109.5 -40q57 0 93 24l17 76h-131l24 106h262l-54 -247q-99 -75 -217 -75z" />
    <glyph glyph-name="H.smcp" unicode="&#xf768;" horiz-adv-x="651" 
d="M534 0h-132l50 224h-261l-50 -224h-132l121 550h132l-46 -211h261l46 211h132z" />
    <glyph glyph-name="I.smcp" unicode="&#xf769;" horiz-adv-x="258" 
d="M141 0h-132l121 550h132z" />
    <glyph glyph-name="J.smcp" unicode="&#xf76a;" horiz-adv-x="433" 
d="M138 -12q-118 0 -174 71l75 92q30 -47 87 -47q80 0 100 87l79 359h132l-81 -370q-23 -102 -75.5 -147t-142.5 -45z" />
    <glyph glyph-name="K.smcp" unicode="&#xf76b;" horiz-adv-x="566" 
d="M511 0h-158l-124 216l-50 -47l-38 -169h-132l121 550h132l-50 -230l243 230h162l-287 -259z" />
    <glyph glyph-name="L.smcp" unicode="&#xf76c;" horiz-adv-x="469" 
d="M383 0h-374l121 550h132l-96 -435h242z" />
    <glyph glyph-name="M.smcp" unicode="&#xf76d;" horiz-adv-x="756" 
d="M640 0h-132l80 365l-233 -365h-61l-72 365l-81 -365h-132l121 550h175l65 -335l213 335h178z" />
    <glyph glyph-name="N.smcp" unicode="&#xf76e;" horiz-adv-x="648" 
d="M532 0h-136l-175 366l-80 -366h-132l121 550h142l171 -354l78 354h132z" />
    <glyph glyph-name="O.smcp" unicode="&#xf76f;" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5z" />
    <glyph glyph-name="P.smcp" unicode="&#xf770;" horiz-adv-x="554" 
d="M141 0h-132l121 550h245q87 0 139 -44t52 -115q0 -81 -59 -141.5t-153 -60.5h-171zM208 303h139q37 0 59.5 22.5t22.5 55.5q0 25 -18 39.5t-50 14.5h-123z" />
    <glyph glyph-name="Q.smcp" unicode="&#xf771;" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -73 -31.5 -138.5t-87.5 -110.5l24 -37l-93 -57l-26 41q-54 -19 -109 -19zM314 104q19 0 41 5l-42 67l91 58l44 -69q51 57 51 135q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59
t-52.5 -137q0 -63 40 -104.5t107 -41.5z" />
    <glyph glyph-name="R.smcp" unicode="&#xf772;" horiz-adv-x="573" 
d="M485 0h-149l-68 189h-85l-42 -189h-132l121 550h245q89 0 140.5 -43.5t51.5 -112.5q0 -75 -46 -130t-122 -66zM338 303h7q38 0 61 22.5t23 55.5q0 24 -17.5 39t-47.5 15h-126l-30 -132h130z" />
    <glyph glyph-name="S.smcp" unicode="&#xf773;" horiz-adv-x="516" 
d="M249 -12q-83 0 -152 24.5t-110 71.5l77 96q29 -36 81 -57.5t112 -21.5q43 0 62 17t19 43q0 21 -28 36t-68.5 28t-81 30t-68.5 51t-28 81q0 68 63.5 121.5t160.5 53.5q72 0 135 -26t101 -67l-77 -89q-28 31 -74.5 50.5t-90.5 19.5q-35 0 -57 -16.5t-22 -39.5
q0 -20 28.5 -34t68.5 -27t80.5 -30t69 -51.5t28.5 -83.5q0 -73 -60 -126.5t-169 -53.5z" />
    <glyph glyph-name="T.smcp" unicode="&#xf774;" horiz-adv-x="486" 
d="M255 0h-132l95 435h-156l26 115h445l-26 -115h-156z" />
    <glyph glyph-name="U.smcp" unicode="&#xf775;" horiz-adv-x="646" 
d="M314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-25 -115 -86.5 -173t-176.5 -58z" />
    <glyph glyph-name="V.smcp" unicode="&#xf776;" horiz-adv-x="597" 
d="M325 0h-161l-102 550h148l64 -407l245 407h150z" />
    <glyph glyph-name="W.smcp" unicode="&#xf777;" horiz-adv-x="797" 
d="M578 0h-138l-17 358l-175 -358h-137l-45 550h147l12 -376l188 376h103l22 -377l179 377h148z" />
    <glyph glyph-name="X.smcp" unicode="&#xf778;" horiz-adv-x="584" 
d="M534 0h-153l-99 194l-183 -194h-157l271 282l-137 268h153l88 -180l167 180h158l-255 -267z" />
    <glyph glyph-name="Y.smcp" unicode="&#xf779;" horiz-adv-x="565" 
d="M295 0h-131l50 226l-152 324h147l94 -213l183 213h151l-293 -324z" />
    <glyph glyph-name="Z.smcp" unicode="&#xf77a;" horiz-adv-x="523" 
d="M434 0h-453l21 96l336 338h-261l26 116h445l-20 -96l-336 -338h267z" />
    <glyph glyph-name="Agrave.smcp" unicode="&#xf7e0;" horiz-adv-x="597" 
d="M285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM444 606h-89l-130 144h117z" />
    <glyph glyph-name="Aacute.smcp" unicode="&#xf7e1;" horiz-adv-x="597" 
d="M598 750l-194 -144h-91l166 144h119zM285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163z" />
    <glyph glyph-name="Acircumflex.smcp" unicode="&#xf7e2;" horiz-adv-x="597" 
d="M528 606h-76l-54 89l-90 -89h-80l124 144h114zM285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163z" />
    <glyph glyph-name="Atilde.smcp" unicode="&#xf7e3;" horiz-adv-x="597" 
d="M285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM450 604q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="Adieresis.smcp" unicode="&#xf7e4;" horiz-adv-x="597" 
d="M564 668q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM353 668q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151z
M373 205l-35 211l-128 -211h163z" />
    <glyph glyph-name="Aring.smcp" unicode="&#xf7e5;" horiz-adv-x="597" 
d="M384 571q-42 0 -68.5 26.5t-26.5 69.5q0 46 34.5 80.5t80.5 34.5q43 0 69 -26.5t26 -69.5q0 -47 -34 -81t-81 -34zM390 630q20 0 35 15t15 35q0 19 -11 31t-30 12q-20 0 -35.5 -15t-15.5 -35q0 -19 11.5 -31t30.5 -12zM285 550h161l102 -550h-146l-16 90h-240l-54 -90
h-151zM373 205l-35 211l-128 -211h163z" />
    <glyph glyph-name="AE.smcp" unicode="&#xf7e6;" horiz-adv-x="845" 
d="M749 0h-415l20 90h-189l-74 -90h-151l463 550h467l-25 -113h-283l-22 -101h276l-25 -113h-276l-24 -110h284zM379 205l47 211l-173 -211h126z" />
    <glyph glyph-name="Ccedilla.smcp" unicode="&#xf7e7;" horiz-adv-x="585" 
d="M253 -196q-78 0 -119 41l35 48q37 -35 84 -35q50 0 50 35q0 24 -30 24q-17 0 -29 -14l-40 26l38 66q-97 18 -153.5 84t-56.5 160q0 132 96 227.5t234 95.5q87 0 149.5 -42.5t87.5 -111.5l-119 -43q-14 36 -48 58.5t-75 22.5q-76 0 -132.5 -58.5t-56.5 -138.5
q0 -66 42 -105.5t108 -39.5q80 0 133 71l99 -61q-40 -56 -101.5 -91t-137.5 -35h-11l-24 -39q14 10 31 10q25 0 42 -16.5t17 -45.5q0 -42 -32 -67.5t-81 -25.5z" />
    <glyph glyph-name="Egrave.smcp" unicode="&#xf7e8;" horiz-adv-x="520" 
d="M424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282zM414 606h-89l-130 144h117z" />
    <glyph glyph-name="Eacute.smcp" unicode="&#xf7e9;" horiz-adv-x="520" 
d="M570 750l-194 -144h-91l166 144h119zM424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282z" />
    <glyph glyph-name="Ecircumflex.smcp" unicode="&#xf7ea;" horiz-adv-x="520" 
d="M500 606h-76l-54 89l-90 -89h-80l124 144h114zM424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282z" />
    <glyph glyph-name="Edieresis.smcp" unicode="&#xf7eb;" horiz-adv-x="520" 
d="M536 668q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM325 668q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM424 0h-415l121 550h415l-25 -113h-282l-23 -101h277
l-25 -113h-277l-24 -110h282z" />
    <glyph glyph-name="Igrave.smcp" unicode="&#xf7ec;" horiz-adv-x="258" 
d="M141 0h-132l121 550h132zM272 606h-89l-130 144h117z" />
    <glyph glyph-name="Iacute.smcp" unicode="&#xf7ed;" horiz-adv-x="258" 
d="M428 750l-194 -144h-91l166 144h119zM141 0h-132l121 550h132z" />
    <glyph glyph-name="Icircumflex.smcp" unicode="&#xf7ee;" horiz-adv-x="258" 
d="M358 606h-76l-54 89l-90 -89h-80l124 144h114zM141 0h-132l121 550h132z" />
    <glyph glyph-name="Idieresis.smcp" unicode="&#xf7ef;" horiz-adv-x="258" 
d="M394 668q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM183 668q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM141 0h-132l121 550h132z" />
    <glyph glyph-name="Eth.smcp" unicode="&#xf7f0;" horiz-adv-x="626" 
d="M254 0h-245l51 232h-55l20 91h55l50 227h200q119 0 198 -64t79 -171q0 -44 -11 -86.5t-37.5 -84.5t-66 -73.5t-101 -51t-137.5 -19.5zM279 115q93 0 143 58t50 132q0 60 -42 95t-115 35h-78l-25 -112h112l-20 -91h-112l-25 -117h112z" />
    <glyph glyph-name="Ntilde.smcp" unicode="&#xf7f1;" horiz-adv-x="648" 
d="M532 0h-136l-175 366l-80 -366h-132l121 550h142l171 -354l78 354h132zM476 604q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="Ograve.smcp" unicode="&#xf7f2;" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM472 606h-89l-130 144h117z
" />
    <glyph glyph-name="Oacute.smcp" unicode="&#xf7f3;" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM626 750l-194 -144h-91
l166 144h119z" />
    <glyph glyph-name="Ocircumflex.smcp" unicode="&#xf7f4;" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM557 606h-76l-54 89l-90 -89
h-80l124 144h114z" />
    <glyph glyph-name="Otilde.smcp" unicode="&#xf7f5;" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM479 604q-30 0 -50 17t-36 34
t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="Odieresis.smcp" unicode="&#xf7f6;" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM593 668q0 -29 -23 -52
t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM382 668q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43z" />
    <glyph glyph-name="Oslash.smcp" unicode="&#xf7f8;" horiz-adv-x="655" 
d="M311 -12q-99 0 -169 45l-33 -33h-107l83 83q-53 65 -53 158q0 131 93.5 226t229.5 95q97 0 167 -45l33 33h107l-83 -82q55 -68 55 -159q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 39 -17 71l-248 -247q34 -20 80 -20zM167 250q0 -38 16 -70l247 246
q-32 20 -78 20q-80 0 -132.5 -59t-52.5 -137z" />
    <glyph glyph-name="Ugrave.smcp" unicode="&#xf7f9;" horiz-adv-x="646" 
d="M314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-25 -115 -86.5 -173t-176.5 -58zM465 606h-89l-130 144h117z" />
    <glyph glyph-name="Uacute.smcp" unicode="&#xf7fa;" horiz-adv-x="646" 
d="M621 750l-194 -144h-91l166 144h119zM314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-25 -115 -86.5 -173t-176.5 -58z" />
    <glyph glyph-name="Ucircumflex.smcp" unicode="&#xf7fb;" horiz-adv-x="646" 
d="M551 606h-76l-54 89l-90 -89h-80l124 144h114zM314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-25 -115 -86.5 -173t-176.5 -58z" />
    <glyph glyph-name="Udieresis.smcp" unicode="&#xf7fc;" horiz-adv-x="646" 
d="M588 668q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM377 668q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332
h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-25 -115 -86.5 -173t-176.5 -58z" />
    <glyph glyph-name="Yacute.smcp" unicode="&#xf7fd;" horiz-adv-x="565" 
d="M584 750l-194 -144h-91l166 144h119zM295 0h-131l50 226l-152 324h147l94 -213l183 213h151l-293 -324z" />
    <glyph glyph-name="Thorn.smcp" unicode="&#xf7fe;" horiz-adv-x="554" 
d="M141 0h-132l121 550h132l-17 -82h111q85 0 138 -42.5t53 -115.5q0 -81 -58 -142t-154 -61h-170zM319 222h10q37 0 59 22t22 55q0 26 -18 40t-50 14h-123l-29 -131h129z" />
    <glyph glyph-name="Ydieresis.smcp" unicode="&#xf7ff;" horiz-adv-x="565" 
d="M551 668q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM340 668q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM295 0h-131l50 226l-152 324h147l94 -213l183 213h151
l-293 -324z" />
    <glyph glyph-name="uniF8FF" unicode="&#xf8ff;" horiz-adv-x="667" 
d="M198 153l15 67h-73q-30 0 -53 -22t-23 -52q0 -25 16 -42t41 -17q30 0 50 17.5t27 48.5zM263 447l13 57q2 12 2 18q0 26 -16.5 42t-42.5 16q-31 0 -53 -21t-22 -52q0 -27 18 -43.5t46 -16.5h55zM269 265h137l31 137h-137zM561 160q0 27 -18 43.5t-46 16.5h-55l-13 -57
q-2 -12 -2 -18q0 -26 16.5 -42t42.5 -16q32 0 53.5 21t21.5 52zM641 521q0 25 -16 42t-41 17q-30 0 -50 -18t-27 -48l-15 -67h73q30 0 53 22.5t23 51.5zM607 160q0 -48 -39 -83t-89 -35q-42 0 -70 28t-28 70q0 14 3 29l12 51h-137l-17 -75q-11 -46 -45 -74.5t-80 -28.5
q-41 0 -70 28t-29 69q0 49 38 87.5t87 38.5h80l31 137h-55q-45 0 -72.5 29t-27.5 76q0 48 38.5 83t88.5 35q42 0 70 -28t28 -70q0 -14 -3 -29l-12 -51h137l17 75q11 46 45 74.5t80 28.5q42 0 71 -28t29 -69q0 -50 -38 -88t-87 -38h-80l-31 -137h52q46 0 74.5 -29.5
t28.5 -75.5z" />
    <glyph glyph-name="zero.pzero" horiz-adv-x="620" 
d="M284 -12q-96 0 -159 54l-37 -42h-102l91 104q-34 63 -34 153q0 108 41 204t118.5 156t172.5 60t157 -53l38 43h102l-92 -105q34 -63 34 -153q0 -109 -40.5 -204.5t-117.5 -156t-172 -60.5zM297 114q52 0 93.5 49t61 117t19.5 136q0 4 -0.5 10.5t-0.5 9.5l-253 -289
q28 -33 80 -33zM187 250v-20l253 289q-29 32 -79 32q-77 0 -125.5 -96.5t-48.5 -204.5z" />
    <glyph glyph-name="zero.tonumzero" horiz-adv-x="623" 
d="M600 320q0 -57 -20 -115t-56.5 -107t-95.5 -79.5t-129 -30.5q-104 0 -172 45l-33 -33h-107l87 86q-39 57 -39 143q0 58 20 115.5t56.5 107t95.5 80t130 30.5q102 0 169 -45l34 33h107l-87 -86q40 -60 40 -144zM454 310q0 21 -5 43l-222 -220q28 -19 73 -19
q71 0 112.5 62.5t41.5 133.5zM181 240q0 -26 4 -43l221 220q-30 19 -71 19q-71 0 -112.5 -62.5t-41.5 -133.5z" />
    <glyph glyph-name="zero.onumzero" horiz-adv-x="620" 
d="M599 320q0 -57 -20 -115t-56.5 -107t-95.5 -79.5t-129 -30.5q-104 0 -172 45l-33 -33h-107l87 86q-39 57 -39 143q0 58 20 115.5t56.5 107t95.5 80t130 30.5q102 0 169 -45l34 33h107l-87 -86q40 -60 40 -144zM453 310q0 21 -5 43l-222 -220q28 -19 73 -19
q71 0 112.5 62.5t41.5 133.5zM180 240q0 -26 4 -43l221 220q-30 19 -71 19q-71 0 -112.5 -62.5t-41.5 -133.5z" />
    <glyph glyph-name="G.alt1" horiz-adv-x="722" 
d="M355 -12q-132 0 -221.5 83.5t-89.5 215.5q0 174 116.5 282.5t287.5 108.5q98 0 170 -48.5t102 -116.5l-131 -56q-16 41 -61 68t-97 27q-98 0 -168.5 -74t-70.5 -182q0 -80 50.5 -131t130.5 -51q68 0 117 36.5t72 94.5h-208l28 125h358q-30 -187 -130 -284.5t-255 -97.5z
" />
    <glyph glyph-name="a.alt1" horiz-adv-x="543" 
d="M435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45
q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5z" />
    <glyph glyph-name="f.alt1" horiz-adv-x="327" 
d="M165 0h-128l83 372h-80l24 111h80l6 26q18 79 64 123.5t117 44.5q64 0 106 -27l-44 -86q-17 13 -46 13q-55 0 -70 -68l-6 -26h99l-25 -111h-98z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="298" 
d="M177 -12q-68 0 -106 27t-38 76q0 16 3 35l120 541h127l-112 -508q-3 -11 -3 -20q0 -17 13.5 -27.5t34.5 -10.5q24 0 36 10l4 -102q-27 -21 -79 -21z" />
    <glyph glyph-name="y.alt1" 
d="M206 -196q-154 0 -234 84l75 85q22 -31 66 -48t91 -17q59 0 91 31.5t46 93.5l11 49q-79 -73 -160 -73q-67 0 -109 32t-42 91q0 15 5 40l70 311h127l-61 -272q-3 -18 -3 -28q0 -61 82 -61q57 0 113 56l68 305h127l-97 -439q-55 -240 -266 -240z" />
    <glyph glyph-name="G.smcp.alt1" horiz-adv-x="632" 
d="M311 -12q-122 0 -200.5 69t-78.5 181q0 138 98.5 231t232.5 93q79 0 141.5 -36t93.5 -97l-118 -53q-15 31 -49 50t-74 19q-77 0 -133 -57t-56 -138q0 -67 43.5 -107t111.5 -40q47 0 86.5 27t55.5 72h-164l23 107h298q-19 -148 -106 -234.5t-205 -86.5z" />
    <glyph glyph-name="Gbreve.alt1" horiz-adv-x="736" 
d="M677 786q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62zM355 -12q-132 0 -221.5 83.5t-89.5 215.5q0 174 116.5 282.5t287.5 108.5q98 0 170 -48.5t102 -116.5l-131 -56q-16 41 -61 68t-97 27q-98 0 -168.5 -74t-70.5 -182
q0 -80 50.5 -131t130.5 -51q68 0 117 36.5t72 94.5h-208l28 125h358q-30 -187 -130 -284.5t-255 -97.5z" />
    <glyph glyph-name="Gcommaaccent.alt1" horiz-adv-x="736" 
d="M367 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM355 -12q-132 0 -221.5 83.5t-89.5 215.5q0 174 116.5 282.5t287.5 108.5q98 0 170 -48.5t102 -116.5l-131 -56q-16 41 -61 68
t-97 27q-98 0 -168.5 -74t-70.5 -182q0 -80 50.5 -131t130.5 -51q68 0 117 36.5t72 94.5h-208l28 125h358q-30 -187 -130 -284.5t-255 -97.5z" />
    <glyph glyph-name="Gcircumflex.alt1" horiz-adv-x="736" 
d="M642 723h-76l-54 89l-90 -89h-80l124 144h114zM355 -12q-132 0 -221.5 83.5t-89.5 215.5q0 174 116.5 282.5t287.5 108.5q98 0 170 -48.5t102 -116.5l-131 -56q-16 41 -61 68t-97 27q-98 0 -168.5 -74t-70.5 -182q0 -80 50.5 -131t130.5 -51q68 0 117 36.5t72 94.5h-208
l28 125h358q-30 -187 -130 -284.5t-255 -97.5z" />
    <glyph glyph-name="Gdotaccent.alt1" horiz-adv-x="736" 
d="M572 785q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5zM355 -12q-132 0 -221.5 83.5t-89.5 215.5q0 174 116.5 282.5t287.5 108.5q98 0 170 -48.5t102 -116.5l-131 -56q-16 41 -61 68t-97 27q-98 0 -168.5 -74
t-70.5 -182q0 -80 50.5 -131t130.5 -51q68 0 117 36.5t72 94.5h-208l28 125h358q-30 -187 -130 -284.5t-255 -97.5z" />
    <glyph glyph-name="aacute.alt1" horiz-adv-x="543" 
d="M558 700l-194 -144h-91l166 144h119zM435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74
q63 0 101 40l13 59q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5z" />
    <glyph glyph-name="abreve.alt1" horiz-adv-x="543" 
d="M435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45
q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM524 619q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="acircumflex.alt1" horiz-adv-x="543" 
d="M489 556h-76l-54 89l-90 -89h-80l124 144h114zM435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51z
M232 74q63 0 101 40l13 59q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5z" />
    <glyph glyph-name="adieresis.alt1" horiz-adv-x="543" 
d="M524 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM313 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5
t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5z" />
    <glyph glyph-name="agrave.alt1" horiz-adv-x="543" 
d="M435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45
q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM404 556h-89l-130 144h117z" />
    <glyph glyph-name="amacron.alt1" horiz-adv-x="543" 
d="M522 562h-362l16 72h362zM435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59
q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5z" />
    <glyph glyph-name="aogonek.alt1" horiz-adv-x="543" 
d="M435 0h-1q-93 -43 -93 -100q0 -33 35 -33q27 0 47 34l43 -36q-39 -51 -95 -51q-39 0 -66 20t-27 56q0 65 60 110h-31l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46
l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5z" />
    <glyph glyph-name="aring.alt1" horiz-adv-x="543" 
d="M347 531q-42 0 -68.5 26.5t-26.5 69.5q0 46 34.5 80.5t80.5 34.5q43 0 69 -26.5t26 -69.5q0 -47 -34 -81t-81 -34zM353 590q20 0 35 15t15 35q0 19 -11 31t-30 12q-20 0 -35.5 -15t-15.5 -35q0 -19 11.5 -31t30.5 -12zM435 0h-128l11 49q-58 -61 -144 -61
q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5
z" />
    <glyph glyph-name="atilde.alt1" horiz-adv-x="543" 
d="M435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45
q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM410 554q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EA1.alt1" horiz-adv-x="543" 
d="M435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45
q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM255 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EA3.alt1" horiz-adv-x="543" 
d="M435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45
q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM308 580l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EA5.alt1" horiz-adv-x="543" 
d="M488 556h-76l-54 89l-90 -89h-80l124 144h114zM748 770l-194 -144h-91l166 144h119zM435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59
q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5z" />
    <glyph glyph-name="uni1EAD.alt1" horiz-adv-x="543" 
d="M488 556h-76l-54 89l-90 -89h-80l124 144h114zM435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51z
M232 74q63 0 101 40l13 59q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM255 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EA7.alt1" horiz-adv-x="543" 
d="M490 556h-76l-54 89l-90 -89h-80l124 144h114zM435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51z
M232 74q63 0 101 40l13 59q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM244 626h-89l-130 144h117z" />
    <glyph glyph-name="uni1EA9.alt1" horiz-adv-x="543" 
d="M488 556h-76l-54 89l-90 -89h-80l124 144h114zM435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51z
M232 74q63 0 101 40l13 59q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM425 723l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EAF.alt1" horiz-adv-x="543" 
d="M566 778l-194 -144h-91l166 144h119zM435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74
q63 0 101 40l13 59q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM521 602q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="uni1EAB.alt1" horiz-adv-x="543" 
d="M481 523h-76l-54 89l-90 -89h-80l124 144h114zM435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51z
M232 74q63 0 101 40l13 59q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM438 682q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EB1.alt1" horiz-adv-x="543" 
d="M435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45
q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM430 636h-89l-130 144h117zM521 606q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="uni1EB3.alt1" horiz-adv-x="543" 
d="M435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45
q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM328 699l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24zM521 606q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="uni1EB5.alt1" horiz-adv-x="543" 
d="M521 606q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62zM435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59
q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM437 677q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61
h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EB7.alt1" horiz-adv-x="543" 
d="M435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45
q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5zM256 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5zM524 619q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="880" 
d="M841 201h-344q-1 -1 -1 -13q0 -36 34.5 -67.5t93.5 -31.5q66 0 112 36l40 -87q-66 -50 -150 -50q-143 0 -208 93q-76 -93 -205 -93q-85 0 -141.5 36t-56.5 104q0 81 56.5 128.5t131.5 47.5q110 0 170 -65l9 41q5 21 5 39q0 33 -32.5 53t-81.5 20q-80 0 -145 -46l-31 90
q82 59 202 59q139 0 166 -97q69 97 190 97q87 0 141.5 -56t54.5 -160q0 -28 -10 -78zM511 286h226q1 4 1 11q0 41 -26.5 69t-73.5 28q-51 0 -83 -32t-44 -76zM357 162l2 11q-14 21 -45 33t-67 12q-40 0 -71 -21t-31 -53q0 -27 25.5 -42.5t63.5 -15.5q42 0 79 21.5t44 54.5z
" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="880" 
d="M841 201h-344q-1 -1 -1 -13q0 -36 34.5 -67.5t93.5 -31.5q66 0 112 36l40 -87q-66 -50 -150 -50q-143 0 -208 93q-76 -93 -205 -93q-85 0 -141.5 36t-56.5 104q0 81 56.5 128.5t131.5 47.5q110 0 170 -65l9 41q5 21 5 39q0 33 -32.5 53t-81.5 20q-80 0 -145 -46l-31 90
q82 59 202 59q139 0 166 -97q69 97 190 97q87 0 141.5 -56t54.5 -160q0 -28 -10 -78zM511 286h226q1 4 1 11q0 41 -26.5 69t-73.5 28q-51 0 -83 -32t-44 -76zM357 162l2 11q-14 21 -45 33t-67 12q-40 0 -71 -21t-31 -53q0 -27 25.5 -42.5t63.5 -15.5q42 0 79 21.5t44 54.5z
M717 700l-194 -144h-91l166 144h119z" />
    <glyph glyph-name="fl.alt1" horiz-adv-x="626" 
d="M165 0h-128l83 372h-80l24 111h80l6 26q18 79 64 123.5t117 44.5q64 0 106 -27l-44 -86q-17 13 -46 13q-55 0 -70 -68l-6 -26h99l-25 -111h-98zM504 -12q-68 0 -106 27t-38 76q0 16 3 35l120 541h127l-112 -508q-3 -10 -3 -20q0 -17 13.5 -27.5t34.5 -10.5q24 0 36 10
l4 -102q-27 -21 -79 -21z" />
    <glyph glyph-name="f_f_l.alt1" horiz-adv-x="952" 
d="M492 0h-128l83 372h-80l24 111h80l6 26q18 79 64 123.5t117 44.5q64 0 106 -27l-44 -86q-17 13 -46 13q-55 0 -70 -68l-6 -26h99l-25 -111h-98zM832 -12q-68 0 -106 27t-38 76q0 16 3 35l120 541h127l-112 -508q-3 -10 -3 -20q0 -17 13.5 -27.5t34.5 -10.5q24 0 36 10
l4 -102q-27 -21 -79 -21zM165 0h-127l82 373h-80l25 110h79l6 27q17 75 63 121t123 46q81 0 127 -47l-63 -74q-21 21 -54 21q-54 0 -69 -69l-6 -25h99l-25 -110h-98z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="298" 
d="M450 867l-194 -144h-91l166 144h119zM177 -12q-68 0 -106 27t-38 76q0 16 3 35l120 541h127l-112 -508q-3 -11 -3 -20q0 -17 13.5 -27.5t34.5 -10.5q24 0 36 10l4 -102q-27 -21 -79 -21z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="298" 
d="M116 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM177 -12q-68 0 -106 27t-38 76q0 16 3 35l120 541h127l-112 -508q-3 -11 -3 -20q0 -17 13.5 -27.5t34.5 -10.5q24 0 36 10
l4 -102q-27 -21 -79 -21z" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="356" 
d="M451 616q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM177 -12q-68 0 -106 27t-38 76q0 16 3 35l120 541h127l-112 -508q-3 -11 -3 -20q0 -17 13.5 -27.5t34.5 -10.5q24 0 36 10
l4 -102q-27 -21 -79 -21z" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="384" 
d="M177 -12q-68 0 -106 27t-38 76q0 16 3 35l120 541h127l-112 -508q-3 -11 -3 -20q0 -17 13.5 -27.5t34.5 -10.5q24 0 36 10l4 -102q-27 -21 -79 -21zM311 164q-31 0 -52.5 22t-21.5 52q0 37 26 62.5t63 25.5q30 0 51.5 -22t21.5 -53q0 -35 -26.5 -61t-61.5 -26z" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="325" 
d="M-1 204l21 93l92 47l72 323h127l-55 -250l92 47l-21 -93l-92 -47l-36 -165q-3 -11 -3 -20q0 -17 13.5 -27.5t34.5 -10.5q24 0 36 10l4 -102q-27 -21 -79 -21q-68 0 -106 27t-38 76q0 16 3 35l28 125z" />
    <glyph glyph-name="yacute.alt1" 
d="M576 700l-194 -144h-91l166 144h119zM206 -196q-154 0 -234 84l75 85q22 -31 66 -48t91 -17q59 0 91 31.5t46 93.5l11 49q-79 -73 -160 -73q-67 0 -109 32t-42 91q0 15 5 40l70 311h127l-61 -272q-3 -18 -3 -28q0 -61 82 -61q57 0 113 56l68 305h127l-97 -439
q-55 -240 -266 -240z" />
    <glyph glyph-name="ycircumflex.alt1" 
d="M508 556h-76l-54 89l-90 -89h-80l124 144h114zM206 -196q-154 0 -234 84l75 85q22 -31 66 -48t91 -17q59 0 91 31.5t46 93.5l11 49q-79 -73 -160 -73q-67 0 -109 32t-42 91q0 15 5 40l70 311h127l-61 -272q-3 -18 -3 -28q0 -61 82 -61q57 0 113 56l68 305h127l-97 -439
q-55 -240 -266 -240z" />
    <glyph glyph-name="ydieresis.alt1" 
d="M543 618q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM332 618q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM206 -196q-154 0 -234 84l75 85q22 -31 66 -48t91 -17
q59 0 91 31.5t46 93.5l11 49q-79 -73 -160 -73q-67 0 -109 32t-42 91q0 15 5 40l70 311h127l-61 -272q-3 -18 -3 -28q0 -61 82 -61q57 0 113 56l68 305h127l-97 -439q-55 -240 -266 -240z" />
    <glyph glyph-name="ygrave.alt1" 
d="M206 -196q-154 0 -234 84l75 85q22 -31 66 -48t91 -17q59 0 91 31.5t46 93.5l11 49q-79 -73 -160 -73q-67 0 -109 32t-42 91q0 15 5 40l70 311h127l-61 -272q-3 -18 -3 -28q0 -61 82 -61q57 0 113 56l68 305h127l-97 -439q-55 -240 -266 -240zM422 556h-89l-130 144h117z
" />
    <glyph glyph-name="uni1EA0.smcp" horiz-adv-x="597" 
d="M285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM284 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EA2.smcp" horiz-adv-x="597" 
d="M285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM349 636l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EA4.smcp" horiz-adv-x="597" 
d="M528 606h-76l-54 89l-90 -89h-80l124 144h114zM788 820l-194 -144h-91l166 144h119zM285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163z" />
    <glyph glyph-name="uni1EAC.smcp" horiz-adv-x="597" 
d="M528 606h-76l-54 89l-90 -89h-80l124 144h114zM285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM284 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EA6.smcp" horiz-adv-x="597" 
d="M528 606h-76l-54 89l-90 -89h-80l124 144h114zM285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM281 676h-89l-130 144h117z" />
    <glyph glyph-name="uni1EA8.smcp" horiz-adv-x="597" 
d="M529 606h-76l-54 89l-90 -89h-80l124 144h114zM285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM465 773l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EAE.smcp" horiz-adv-x="597" 
d="M602 828l-194 -144h-91l166 144h119zM285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM559 656q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="uni1EAA.smcp" horiz-adv-x="597" 
d="M521 573h-76l-54 89l-90 -89h-80l124 144h114zM285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM479 732q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5
q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EB0.smcp" horiz-adv-x="597" 
d="M285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM460 686h-89l-130 144h117zM561 656q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="uni1EB2.smcp" horiz-adv-x="597" 
d="M285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM370 752l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24zM561 656q-85 -88 -194 -88q-53 0 -94 23.5
t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="uni1EB4.smcp" horiz-adv-x="597" 
d="M561 656q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62zM285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM475 720q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5
q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EB6.smcp" horiz-adv-x="597" 
d="M285 550h161l102 -550h-146l-16 90h-240l-54 -90h-151zM373 205l-35 211l-128 -211h163zM284 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5zM564 669q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47
q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="uni1E02.smcp" horiz-adv-x="586" 
d="M342 0h-333l121 550h279q77 0 123.5 -34.5t46.5 -92.5q0 -52 -34.5 -94.5t-90.5 -50.5q37 -12 58.5 -42.5t21.5 -65.5q0 -74 -51.5 -122t-140.5 -48zM166 112h164q30 0 50 17.5t20 44.5q0 22 -15 36t-41 14h-154zM365 336h9q29 0 46 17.5t17 40.5q0 21 -16 32.5t-41 11.5
h-142l-23 -102h150zM460 668q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E0A.smcp" horiz-adv-x="626" 
d="M254 0h-245l121 550h200q119 0 198 -64t79 -171q0 -44 -11 -86.5t-37.5 -84.5t-66 -73.5t-101 -51t-137.5 -19.5zM266 115h13q93 0 143 58t50 132q0 60 -42 95t-115 35h-78l-70 -320h99zM476 668q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53
t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EB8.smcp" horiz-adv-x="520" 
d="M424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282zM256 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EBA.smcp" horiz-adv-x="520" 
d="M424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282zM322 636l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EBC.smcp" horiz-adv-x="520" 
d="M424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282zM422 604q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EBE.smcp" horiz-adv-x="520" 
d="M500 606h-76l-54 89l-90 -89h-80l124 144h114zM760 820l-194 -144h-91l166 144h119zM424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282z" />
    <glyph glyph-name="uni1EC0.smcp" horiz-adv-x="520" 
d="M499 606h-76l-54 89l-90 -89h-80l124 144h114zM424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282zM253 676h-89l-130 144h117z" />
    <glyph glyph-name="uni1EC2.smcp" horiz-adv-x="520" 
d="M500 606h-76l-54 89l-90 -89h-80l124 144h114zM424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282zM436 773l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EC4.smcp" horiz-adv-x="520" 
d="M493 573h-76l-54 89l-90 -89h-80l124 144h114zM424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282zM449 732q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68
q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EC6.smcp" horiz-adv-x="520" 
d="M500 606h-76l-54 89l-90 -89h-80l124 144h114zM424 0h-415l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277l-24 -110h282zM256 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E1E.smcp" horiz-adv-x="509" 
d="M431 668q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5zM141 0h-132l121 550h415l-25 -113h-282l-23 -101h277l-25 -113h-277z" />
    <glyph glyph-name="Gbreve.smcp.alt1" horiz-adv-x="632" 
d="M311 -12q-122 0 -200.5 69t-78.5 181q0 138 98.5 231t232.5 93q79 0 141.5 -36t93.5 -97l-118 -53q-15 31 -49 50t-74 19q-77 0 -133 -57t-56 -138q0 -67 43.5 -107t111.5 -40q47 0 86.5 27t55.5 72h-164l23 107h298q-19 -148 -106 -234.5t-205 -86.5zM599 669
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l65 47q29 -62 105 -62q74 0 133 62z" />
    <glyph glyph-name="Gcommaaccent.smcp.alt1" horiz-adv-x="632" 
d="M316 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM311 -12q-122 0 -200.5 69t-78.5 181q0 138 98.5 231t232.5 93q79 0 141.5 -36t93.5 -97l-118 -53q-15 31 -49 50t-74 19
q-77 0 -133 -57t-56 -138q0 -67 43.5 -107t111.5 -40q47 0 86.5 27t55.5 72h-164l23 107h298q-19 -148 -106 -234.5t-205 -86.5z" />
    <glyph glyph-name="Gcircumflex.smcp.alt1" horiz-adv-x="632" 
d="M563 606h-76l-54 89l-90 -89h-80l124 144h114zM311 -12q-122 0 -200.5 69t-78.5 181q0 138 98.5 231t232.5 93q79 0 141.5 -36t93.5 -97l-118 -53q-15 31 -49 50t-74 19q-77 0 -133 -57t-56 -138q0 -67 43.5 -107t111.5 -40q47 0 86.5 27t55.5 72h-164l23 107h298
q-19 -148 -106 -234.5t-205 -86.5z" />
    <glyph glyph-name="Gdotaccent.smcp.alt1" horiz-adv-x="632" 
d="M311 -12q-122 0 -200.5 69t-78.5 181q0 138 98.5 231t232.5 93q79 0 141.5 -36t93.5 -97l-118 -53q-15 31 -49 50t-74 19q-77 0 -133 -57t-56 -138q0 -67 43.5 -107t111.5 -40q47 0 86.5 27t55.5 72h-164l23 107h298q-19 -148 -106 -234.5t-205 -86.5zM495 668
q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E22.smcp" horiz-adv-x="651" 
d="M534 0h-132l50 224h-261l-50 -224h-132l121 550h132l-46 -211h261l46 211h132zM486 668q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EC8.smcp" horiz-adv-x="258" 
d="M141 0h-132l121 550h132zM179 636l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1ECA.smcp" horiz-adv-x="258" 
d="M141 0h-132l121 550h132zM114 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1ECC.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM314 -121q0 -30 -23.5 -52.5
t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1ECE.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM378 636l-40 20q25 47 81 47
q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1ED0.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM556 606h-76l-54 89l-90 -89
h-80l124 144h114zM816 820l-194 -144h-91l166 144h119z" />
    <glyph glyph-name="uni1ED2.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM556 606h-76l-54 89l-90 -89
h-80l124 144h114zM310 676h-89l-130 144h117z" />
    <glyph glyph-name="uni1ED4.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM557 606h-76l-54 89l-90 -89
h-80l124 144h114zM493 773l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1ED6.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM549 573h-76l-54 89l-90 -89
h-80l124 144h114zM507 732q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1ED8.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q125 0 202 -70.5t77 -182.5q0 -131 -93 -226t-230 -95zM314 104q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM557 606h-76l-54 89l-90 -89
h-80l124 144h114zM312 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="Ohorn.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q139 0 218 -87q44 26 57 57q-1 -1 -7 -1q-18 0 -30.5 12.5t-12.5 34.5q0 25 18.5 42.5t43.5 17.5q22 0 38 -15t16 -42q0 -40 -27 -79t-70 -63q35 -56 35 -130q0 -131 -93 -226t-230 -95zM314 104
q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5z" />
    <glyph glyph-name="uni1EDA.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q139 0 218 -87q44 26 57 57q-1 -1 -7 -1q-18 0 -30.5 12.5t-12.5 34.5q0 25 18.5 42.5t43.5 17.5q22 0 38 -15t16 -42q0 -40 -27 -79t-70 -63q35 -56 35 -130q0 -131 -93 -226t-230 -95zM314 104
q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM626 750l-194 -144h-91l166 144h119z" />
    <glyph glyph-name="uni1EDC.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q139 0 218 -87q44 26 57 57q-1 -1 -7 -1q-18 0 -30.5 12.5t-12.5 34.5q0 25 18.5 42.5t43.5 17.5q22 0 38 -15t16 -42q0 -40 -27 -79t-70 -63q35 -56 35 -130q0 -131 -93 -226t-230 -95zM314 104
q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM472 606h-89l-130 144h117z" />
    <glyph glyph-name="uni1EDE.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q139 0 218 -87q44 26 57 57q-1 -1 -7 -1q-18 0 -30.5 12.5t-12.5 34.5q0 25 18.5 42.5t43.5 17.5q22 0 38 -15t16 -42q0 -40 -27 -79t-70 -63q35 -56 35 -130q0 -131 -93 -226t-230 -95zM314 104
q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM378 636l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EE0.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q139 0 218 -87q44 26 57 57q-1 -1 -7 -1q-18 0 -30.5 12.5t-12.5 34.5q0 25 18.5 42.5t43.5 17.5q22 0 38 -15t16 -42q0 -40 -27 -79t-70 -63q35 -56 35 -130q0 -131 -93 -226t-230 -95zM314 104
q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM479 604q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q24 0 42 -10.5t27.5 -23t23 -23t28.5 -10.5q30 0 45 61h68
q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EE2.smcp" horiz-adv-x="655" 
d="M311 -12q-125 0 -202 70.5t-77 182.5q0 131 93.5 226t229.5 95q139 0 218 -87q44 26 57 57q-1 -1 -7 -1q-18 0 -30.5 12.5t-12.5 34.5q0 25 18.5 42.5t43.5 17.5q22 0 38 -15t16 -42q0 -40 -27 -79t-70 -63q35 -56 35 -130q0 -131 -93 -226t-230 -95zM314 104
q80 0 132.5 59t52.5 137q0 63 -40 104.5t-107 41.5q-80 0 -132.5 -59t-52.5 -137q0 -63 40 -104.5t107 -41.5zM314 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1E56.smcp" horiz-adv-x="554" 
d="M141 0h-132l121 550h245q87 0 139 -44t52 -115q0 -81 -59 -141.5t-153 -60.5h-171zM208 303h139q37 0 59.5 22.5t22.5 55.5q0 25 -18 39.5t-50 14.5h-123zM456 668q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z
" />
    <glyph glyph-name="uni1E60.smcp" horiz-adv-x="516" 
d="M249 -12q-83 0 -152 24.5t-110 71.5l77 96q29 -36 81 -57.5t112 -21.5q43 0 62 17t19 43q0 21 -28 36t-68.5 28t-81 30t-68.5 51t-28 81q0 68 63.5 121.5t160.5 53.5q72 0 135 -26t101 -67l-77 -89q-28 31 -74.5 50.5t-90.5 19.5q-35 0 -57 -16.5t-22 -39.5
q0 -20 28.5 -34t68.5 -27t80.5 -30t69 -51.5t28.5 -83.5q0 -73 -60 -126.5t-169 -53.5zM425 668q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni021A.smcp" horiz-adv-x="486" 
d="M227 -121q0 -45 -34 -91t-84 -70l-36 37q26 12 49 31t31 39h-8q-21 0 -35 13.5t-14 37.5q0 27 21.5 47.5t48.5 20.5q25 0 43 -17.5t18 -47.5zM255 0h-132l95 435h-156l26 115h445l-26 -115h-156z" />
    <glyph glyph-name="uni1E6A.smcp" horiz-adv-x="486" 
d="M255 0h-132l95 435h-156l26 115h445l-26 -115h-156zM404 668q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="uni1EE4.smcp" horiz-adv-x="646" 
d="M314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-25 -115 -86.5 -173t-176.5 -58zM307 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53
t53.5 23q25 0 42.5 -17.5t17.5 -43.5z" />
    <glyph glyph-name="Uhorn.smcp" horiz-adv-x="646" 
d="M314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-70 -317q-4 -22 -4 -31q0 -46 33 -72t89 -26q108 0 135 125l71 321h133l-3 -14q28 23 36 44q-1 -1 -7 -1q-18 0 -31 12.5t-13 34.5q0 25 19 42.5t44 17.5q23 0 39 -15t16 -42q0 -46 -32 -86t-84 -67
l-57 -257q-25 -115 -86.5 -173t-176.5 -58z" />
    <glyph glyph-name="uni1EE6.smcp" horiz-adv-x="646" 
d="M314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-71 -321q-2 -18 -2 -27q0 -47 32.5 -72.5t88.5 -25.5q108 0 135 125l71 321h133l-73 -331q-25 -115 -86.5 -173t-176.5 -58zM373 636l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57
q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24z" />
    <glyph glyph-name="uni1EE8.smcp" horiz-adv-x="646" 
d="M621 750l-194 -144h-91l166 144h119zM314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-70 -317q-4 -22 -4 -31q0 -46 33 -72t89 -26q108 0 135 125l71 321h133l-3 -14q28 23 36 44q-1 -1 -7 -1q-18 0 -31 12.5t-13 34.5q0 25 19 42.5t44 17.5q23 0 39 -15
t16 -42q0 -46 -32 -86t-84 -67l-57 -257q-25 -115 -86.5 -173t-176.5 -58z" />
    <glyph glyph-name="uni1EEA.smcp" horiz-adv-x="646" 
d="M465 606h-89l-130 144h117zM314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-70 -317q-4 -22 -4 -31q0 -46 33 -72t89 -26q108 0 135 125l71 321h133l-3 -14q28 23 36 44q-1 -1 -7 -1q-18 0 -31 12.5t-13 34.5q0 25 19 42.5t44 17.5q23 0 39 -15t16 -42
q0 -46 -32 -86t-84 -67l-57 -257q-25 -115 -86.5 -173t-176.5 -58z" />
    <glyph glyph-name="uni1EEC.smcp" horiz-adv-x="646" 
d="M373 636l-40 20q25 47 81 47q29 0 51.5 -16.5t22.5 -45.5q0 -34 -24 -61h-57q29 26 29 54q0 12 -7.5 19t-19.5 7q-22 0 -36 -24zM314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-70 -317q-4 -22 -4 -31q0 -46 33 -72t89 -26q108 0 135 125l71 321h133
l-3 -14q28 23 36 44q-1 -1 -7 -1q-18 0 -31 12.5t-13 34.5q0 25 19 42.5t44 17.5q23 0 39 -15t16 -42q0 -46 -32 -86t-84 -67l-57 -257q-25 -115 -86.5 -173t-176.5 -58z" />
    <glyph glyph-name="uni1EEE.smcp" horiz-adv-x="646" 
d="M314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-70 -317q-4 -22 -4 -31q0 -46 33 -72t89 -26q108 0 135 125l71 321h133l-3 -14q28 23 36 44q-1 -1 -7 -1q-18 0 -31 12.5t-13 34.5q0 25 19 42.5t44 17.5q23 0 39 -15t16 -42q0 -46 -32 -86t-84 -67
l-57 -257q-25 -115 -86.5 -173t-176.5 -58zM473 604q-30 0 -50 17t-36 34t-35 17q-31 0 -44 -62h-68q13 67 41.5 100.5t79.5 33.5q30 0 50.5 -17t36 -33.5t34.5 -16.5q30 0 45 61h68q-15 -67 -43.5 -100.5t-78.5 -33.5z" />
    <glyph glyph-name="uni1EF0.smcp" horiz-adv-x="646" 
d="M306 -121q0 -30 -23.5 -52.5t-53.5 -22.5q-26 0 -43 17t-17 43q0 30 23.5 53t53.5 23q25 0 42.5 -17.5t17.5 -43.5zM314 -12q-126 0 -194.5 51.5t-68.5 135.5q0 20 5 43l74 332h133l-70 -317q-4 -22 -4 -31q0 -46 33 -72t89 -26q108 0 135 125l71 321h133l-3 -14
q28 23 36 44q-1 -1 -7 -1q-18 0 -31 12.5t-13 34.5q0 25 19 42.5t44 17.5q23 0 39 -15t16 -42q0 -46 -32 -86t-84 -67l-57 -257q-25 -115 -86.5 -173t-176.5 -58z" />
    <glyph glyph-name="Wacute.smcp" horiz-adv-x="797" 
d="M578 0h-138l-17 358l-175 -358h-137l-45 550h147l12 -376l188 376h103l22 -377l179 377h148zM696 750l-194 -144h-91l166 144h119z" />
    <glyph glyph-name="Wdieresis.smcp" horiz-adv-x="797" 
d="M578 0h-138l-17 358l-175 -358h-137l-45 550h147l12 -376l188 376h103l22 -377l179 377h148zM663 668q0 -29 -23 -52t-53 -23q-25 0 -42.5 17.5t-17.5 42.5q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43zM452 668q0 -29 -23 -52t-53 -23q-26 0 -43 17.5t-17 42.5
q0 30 23 52.5t53 22.5q26 0 43 -17t17 -43z" />
    <glyph glyph-name="Wgrave.smcp" horiz-adv-x="797" 
d="M578 0h-138l-17 358l-175 -358h-137l-45 550h147l12 -376l188 376h103l22 -377l179 377h148zM541 606h-89l-130 144h117z" />
    <glyph glyph-name="Ygrave.smcp" horiz-adv-x="565" 
d="M295 0h-131l50 226l-152 324h147l94 -213l183 213h151l-293 -324zM429 606h-89l-130 144h117z" />
    <glyph glyph-name="afii10065.alt1" horiz-adv-x="543" 
d="M435 0h-128l11 49q-58 -61 -144 -61q-66 0 -112.5 36.5t-46.5 103.5q0 80 52 128t127 48q102 0 166 -65l12 55q3 12 3 25q0 33 -31 53t-81 20q-73 0 -135 -46l-31 90q84 59 192 59q94 0 155 -38t61 -116q0 -25 -6 -51zM232 74q63 0 101 40l13 59q-31 45 -109 45
q-42 0 -67 -22t-25 -58q0 -31 23 -47.5t64 -16.5z" />
    <hkern u1="K" u2="a" k="14" />
    <hkern u1="L" u2="a" k="7" />
    <hkern u1="P" u2="a" k="20" />
    <hkern u1="R" u2="a" k="20" />
    <hkern u1="T" u2="a" k="78" />
    <hkern u1="V" u2="a" k="67" />
    <hkern u1="W" u2="a" k="40" />
    <hkern u1="Y" u2="a" k="111" />
    <hkern u1="a" u2="&#x2122;" k="13" />
    <hkern u1="a" u2="&#x201d;" k="13" />
    <hkern u1="a" u2="&#x201c;" k="13" />
    <hkern u1="a" u2="&#x2019;" k="13" />
    <hkern u1="a" u2="&#x2018;" k="13" />
    <hkern u1="a" u2="&#x1ef2;" k="110" />
    <hkern u1="a" u2="&#x1e84;" k="47" />
    <hkern u1="a" u2="&#x1e82;" k="47" />
    <hkern u1="a" u2="&#x1e80;" k="47" />
    <hkern u1="a" u2="&#x1e6a;" k="94" />
    <hkern u1="a" u2="&#x42a;" k="94" />
    <hkern u1="a" u2="&#x427;" k="94" />
    <hkern u1="a" u2="&#x422;" k="94" />
    <hkern u1="a" u2="&#x40b;" k="94" />
    <hkern u1="a" u2="&#x402;" k="94" />
    <hkern u1="a" u2="&#x3ab;" k="110" />
    <hkern u1="a" u2="&#x3a5;" k="110" />
    <hkern u1="a" u2="&#x3a4;" k="94" />
    <hkern u1="a" u2="&#x38e;" k="110" />
    <hkern u1="a" u2="&#x2bc;" k="13" />
    <hkern u1="a" u2="&#x21a;" k="94" />
    <hkern u1="a" u2="&#x178;" k="110" />
    <hkern u1="a" u2="&#x176;" k="110" />
    <hkern u1="a" u2="&#x174;" k="47" />
    <hkern u1="a" u2="&#x166;" k="94" />
    <hkern u1="a" u2="&#x164;" k="94" />
    <hkern u1="a" u2="&#x162;" k="94" />
    <hkern u1="a" u2="&#xdd;" k="110" />
    <hkern u1="a" u2="&#xae;" k="13" />
    <hkern u1="a" u2="Y" k="110" />
    <hkern u1="a" u2="W" k="47" />
    <hkern u1="a" u2="V" k="73" />
    <hkern u1="a" u2="T" k="94" />
    <hkern u1="a" u2="&#x3f;" k="43" />
    <hkern u1="a" u2="&#x27;" k="13" />
    <hkern u1="a" u2="&#x22;" k="13" />
    <hkern u1="&#xdd;" u2="a" k="111" />
    <hkern u1="&#x136;" u2="a" k="14" />
    <hkern u1="&#x139;" u2="a" k="7" />
    <hkern u1="&#x13b;" u2="a" k="7" />
    <hkern u1="&#x141;" u2="a" k="7" />
    <hkern u1="&#x154;" u2="a" k="20" />
    <hkern u1="&#x156;" u2="a" k="20" />
    <hkern u1="&#x158;" u2="a" k="20" />
    <hkern u1="&#x162;" u2="a" k="78" />
    <hkern u1="&#x164;" u2="a" k="78" />
    <hkern u1="&#x166;" u2="a" k="78" />
    <hkern u1="&#x174;" u2="a" k="40" />
    <hkern u1="&#x176;" u2="a" k="111" />
    <hkern u1="&#x178;" u2="a" k="111" />
    <hkern u1="&#x21a;" u2="a" k="78" />
    <hkern u1="&#x38e;" u2="a" k="111" />
    <hkern u1="&#x393;" u2="a" k="78" />
    <hkern u1="&#x39a;" u2="a" k="14" />
    <hkern u1="&#x3a1;" u2="a" k="20" />
    <hkern u1="&#x3a4;" u2="a" k="78" />
    <hkern u1="&#x3a5;" u2="a" k="111" />
    <hkern u1="&#x3ab;" u2="a" k="111" />
    <hkern u1="&#x403;" u2="a" k="78" />
    <hkern u1="&#x40c;" u2="a" k="14" />
    <hkern u1="&#x40e;" u2="a" k="67" />
    <hkern u1="&#x413;" u2="a" k="78" />
    <hkern u1="&#x416;" u2="a" k="14" />
    <hkern u1="&#x41a;" u2="a" k="14" />
    <hkern u1="&#x420;" u2="a" k="20" />
    <hkern u1="&#x422;" u2="a" k="78" />
    <hkern u1="&#x423;" u2="a" k="67" />
    <hkern u1="&#x490;" u2="a" k="78" />
    <hkern u1="&#x1e56;" u2="a" k="20" />
    <hkern u1="&#x1e6a;" u2="a" k="78" />
    <hkern u1="&#x1e80;" u2="a" k="40" />
    <hkern u1="&#x1e82;" u2="a" k="40" />
    <hkern u1="&#x1e84;" u2="a" k="40" />
    <hkern u1="&#x1ef2;" u2="a" k="111" />
    <hkern u1="&#x2215;" u2="&#xf659;" k="50" />
    <hkern u1="&#x2215;" u2="&#xf656;" k="-1" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="dagger"
	k="94" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V.smcp"
	k="34" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="daggerdbl"
	k="34" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="question"
	k="74" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="94" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="44" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="J,Jcircumflex,afii10057"
	k="-20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="asterisk"
	k="91" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="43" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="74" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="51" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V"
	k="57" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="77" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="54" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="23" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="v,gamma,nu"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="bullet.case"
	k="44" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="44" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="dagger"
	k="74" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V.smcp"
	k="37" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="24" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="daggerdbl"
	k="20" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question"
	k="54" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="80" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="54" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="24" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="asterisk"
	k="57" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="19" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="110" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="55" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V"
	k="57" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="93" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="17" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="54" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question.smcp"
	k="47" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="bullet"
	k="17" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-13" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="14" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="V"
	k="17" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Jcircumflex.smcp,J.smcp"
	k="14" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="question"
	k="47" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="28" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="V"
	k="20" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="27" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="question"
	k="7" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="-7" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-7" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="7" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="V"
	k="14" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="33" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="bullet.case"
	k="7" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="13" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="7" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="7" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V.smcp"
	k="13" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="question"
	k="27" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="13" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="7" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="13" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="71" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="29" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V"
	k="36" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="73" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="20" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-4" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="X.smcp"
	k="7" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="question"
	k="23" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="J,Jcircumflex,afii10057"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="37" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="34" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="V"
	k="37" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="63" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Jcircumflex.smcp,J.smcp"
	k="23" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="17" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="24" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V.smcp"
	k="24" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="question"
	k="53" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="27" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="34" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="31" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="71" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V"
	k="50" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="83" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="34" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="17" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="4" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="14" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X.smcp"
	k="27" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="11" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="47" />
    <hkern g1="Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,OE.smcp,E.smcp,AE.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="7" />
    <hkern g1="F,uni1E1E"
	g2="J,Jcircumflex,afii10057"
	k="70" />
    <hkern g1="F,uni1E1E"
	g2="Jcircumflex.smcp,J.smcp"
	k="63" />
    <hkern g1="F,uni1E1E"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="50" />
    <hkern g1="F,uni1E1E"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="43" />
    <hkern g1="F,uni1E1E"
	g2="X.smcp"
	k="7" />
    <hkern g1="F,uni1E1E"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="F,uni1E1E"
	g2="ampersand"
	k="13" />
    <hkern g1="F,uni1E1E"
	g2="ampersand.smcp"
	k="27" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="J,Jcircumflex,afii10057"
	k="43" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="7" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="53" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="27" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="27" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="17" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="26" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="ampersand.smcp"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="question"
	k="13" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="V"
	k="27" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="7" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="7" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="question"
	k="37" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="7" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="7" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="57" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="23" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="question"
	k="7" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="35" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="7" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="V"
	k="20" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="37" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="V.smcp"
	k="47" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="54" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="56" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="47" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="11" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="33" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="bullet.case"
	k="67" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="70" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="X.smcp"
	k="23" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="ampersand.smcp"
	k="7" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="27" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="x,afii10072,afii10087"
	k="33" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="47" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="14" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="18" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="14" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="V"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="53" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="bullet"
	k="44" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="34" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="dagger"
	k="130" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V.smcp"
	k="57" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="31" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="daggerdbl"
	k="91" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="question"
	k="107" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="136" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="74" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="44" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="27" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="asterisk"
	k="167" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="24" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="73" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="127" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="17" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="93" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="7" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="37" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="34" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="v,gamma,nu"
	k="54" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="54" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="bullet.case"
	k="104" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="74" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="ampersand"
	k="4" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="11" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="dagger"
	k="100" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V.smcp"
	k="74" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="34" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="daggerdbl"
	k="21" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="question"
	k="71" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="110" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="84" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="60" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="44" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="asterisk"
	k="114" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="104" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="57" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V"
	k="97" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="130" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="11" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="100" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="question.smcp"
	k="54" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="bullet"
	k="21" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="21" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="J,Jcircumflex,afii10057"
	k="120" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="6" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="V"
	k="7" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="20" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Jcircumflex.smcp,J.smcp"
	k="103" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="74" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="87" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="20" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand"
	k="33" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand.smcp"
	k="43" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="hyphen,periodcentered,endash,emdash"
	k="27" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="17" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="J,Jcircumflex,afii10057"
	k="21" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="25" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="47" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="74" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="44" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="44" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="34" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="7" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="V"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="24" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="7" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="bullet.case"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand"
	k="6" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand.smcp"
	k="17" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="13" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="V.smcp"
	k="4" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="question"
	k="34" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="4" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="38" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="54" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="V.smcp"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="13" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="23" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="V"
	k="14" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="35" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="23" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="14" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="v,gamma,nu"
	k="13" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="13" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="4" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="x,afii10072,afii10087"
	k="17" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="V.smcp"
	k="10" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="question"
	k="30" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="17" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="4" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="61" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="6" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="V"
	k="26" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="43" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="20" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="-10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="V.smcp"
	k="18" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="58" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="14" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="18" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="11" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="J,Jcircumflex,afii10057"
	k="87" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-4" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="28" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="21" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="18" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="68" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="28" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="v,gamma,nu"
	k="28" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="28" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="bullet.case"
	k="61" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Jcircumflex.smcp,J.smcp"
	k="104" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="31" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="90" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="54" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="X.smcp"
	k="18" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="97" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand.smcp"
	k="81" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="98" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="x,afii10072,afii10087"
	k="38" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="81" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="21" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="68" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="14" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="J,Jcircumflex,afii10057"
	k="61" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="14" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="V"
	k="7" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="bullet"
	k="15" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="77" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-7" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="40" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="27" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand"
	k="34" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand.smcp"
	k="37" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="21" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="J,Jcircumflex,afii10057"
	k="26" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="Jcircumflex.smcp,J.smcp"
	k="26" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="19" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="23" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="question"
	k="20" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="28" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="V"
	k="20" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="27" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-4" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="4" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="7" />
    <hkern g1="V,afii10062,afii10037"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="34" />
    <hkern g1="V,afii10062,afii10037"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="7" />
    <hkern g1="V,afii10062,afii10037"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="4" />
    <hkern g1="V,afii10062,afii10037"
	g2="J,Jcircumflex,afii10057"
	k="97" />
    <hkern g1="V,afii10062,afii10037"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-13" />
    <hkern g1="V,afii10062,afii10037"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="27" />
    <hkern g1="V,afii10062,afii10037"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="56" />
    <hkern g1="V,afii10062,afii10037"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="17" />
    <hkern g1="V,afii10062,afii10037"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="13" />
    <hkern g1="V,afii10062,afii10037"
	g2="v,gamma,nu"
	k="17" />
    <hkern g1="V,afii10062,afii10037"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="13" />
    <hkern g1="V,afii10062,afii10037"
	g2="bullet.case"
	k="34" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="30" />
    <hkern g1="V,afii10062,afii10037"
	g2="Jcircumflex.smcp,J.smcp"
	k="90" />
    <hkern g1="V,afii10062,afii10037"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="23" />
    <hkern g1="V,afii10062,afii10037"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="37" />
    <hkern g1="V,afii10062,afii10037"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="37" />
    <hkern g1="V,afii10062,afii10037"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand"
	k="26" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand.smcp"
	k="44" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen,periodcentered,endash,emdash"
	k="57" />
    <hkern g1="V,afii10062,afii10037"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="60" />
    <hkern g1="V,afii10062,afii10037"
	g2="x,afii10072,afii10087"
	k="26" />
    <hkern g1="V,afii10062,afii10037"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="50" />
    <hkern g1="V,afii10062,afii10037"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="7" />
    <hkern g1="V,afii10062,afii10037"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="56" />
    <hkern g1="V.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="3" />
    <hkern g1="V.smcp"
	g2="J,Jcircumflex,afii10057"
	k="47" />
    <hkern g1="V.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="18" />
    <hkern g1="V.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="6" />
    <hkern g1="V.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="70" />
    <hkern g1="V.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-10" />
    <hkern g1="V.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="24" />
    <hkern g1="V.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="14" />
    <hkern g1="V.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="67" />
    <hkern g1="V.smcp"
	g2="ampersand"
	k="23" />
    <hkern g1="V.smcp"
	g2="ampersand.smcp"
	k="3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="17" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="7" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex,afii10057"
	k="59" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v,gamma,nu"
	k="7" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="7" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Jcircumflex.smcp,J.smcp"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="37" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="64" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand.smcp"
	k="13" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="37" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x,afii10072,afii10087"
	k="26" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="29" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="7" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="30" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="11" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="J,Jcircumflex,afii10057"
	k="27" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="18" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="6" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="50" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-3" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="16" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="57" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="ampersand"
	k="7" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="ampersand.smcp"
	k="9" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="13" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="V.smcp"
	k="6" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="6" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="17" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="J,Jcircumflex,afii10057"
	k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="9" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="13" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="13" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="33" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="bullet.case"
	k="74" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Jcircumflex.smcp,J.smcp"
	k="116" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="66" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="57" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="ampersand"
	k="44" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="ampersand.smcp"
	k="46" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="123" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="124" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="x,afii10072,afii10087"
	k="77" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="colon,semicolon"
	k="70" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="37" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="13" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="90" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="14" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="J,Jcircumflex,afii10057"
	k="79" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="18" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="13" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="94" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-7" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="27" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="47" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="70" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand"
	k="50" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand.smcp"
	k="50" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="53" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="14" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="bullet.case"
	k="44" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="57" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="21" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="V"
	k="7" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="26" />
    <hkern g1="ampersand"
	g2="V.smcp"
	k="51" />
    <hkern g1="ampersand"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="11" />
    <hkern g1="ampersand"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="68" />
    <hkern g1="ampersand"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="20" />
    <hkern g1="ampersand"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="81" />
    <hkern g1="ampersand"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="44" />
    <hkern g1="ampersand"
	g2="V"
	k="64" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="98" />
    <hkern g1="ampersand"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="81" />
    <hkern g1="ampersand"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="20" />
    <hkern g1="ampersand.smcp"
	g2="V.smcp"
	k="41" />
    <hkern g1="ampersand.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="26" />
    <hkern g1="ampersand.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="71" />
    <hkern g1="ampersand.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="30" />
    <hkern g1="ampersand.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="110" />
    <hkern g1="ampersand.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="26" />
    <hkern g1="ampersand.smcp"
	g2="V"
	k="80" />
    <hkern g1="ampersand.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="59" />
    <hkern g1="ampersand.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="77" />
    <hkern g1="asterisk"
	g2="J,Jcircumflex,afii10057"
	k="123" />
    <hkern g1="asterisk"
	g2="Jcircumflex.smcp,J.smcp"
	k="107" />
    <hkern g1="asterisk"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="57" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="91" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="question"
	k="53" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="98" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="V"
	k="60" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="117" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="4" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="x,afii10072,afii10087"
	k="34" />
    <hkern g1="bullet"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="41" />
    <hkern g1="bullet"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="4" />
    <hkern g1="bullet.case"
	g2="J,Jcircumflex,afii10057"
	k="80" />
    <hkern g1="bullet.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="61" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="29" />
    <hkern g1="bullet.case"
	g2="V"
	k="54" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="94" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="24" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="44" />
    <hkern g1="bullet.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="60" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="question"
	k="27" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="7" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="71" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="27" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="V"
	k="43" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="67" />
    <hkern g1="colon,semicolon"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="question"
	k="43" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="7" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="asterisk"
	k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="91" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="V"
	k="57" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="110" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="4" />
    <hkern g1="eth"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="4" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="question"
	k="-54" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="-64" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="asterisk"
	k="-64" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-27" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-67" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="V"
	k="-67" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="-63" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="47" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand"
	k="7" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand.smcp"
	k="7" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="exclam,B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Imacron,Iogonek,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Racute,Rcommaaccent,Rcaron"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="parenright,bracketright,braceright,parenright.case,bracketright.case,braceright.case"
	k="-67" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="question"
	k="27" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="68" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="V"
	k="56" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="j,jcircumflex,afii10105"
	k="-53" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="J,Jcircumflex,afii10057"
	k="64" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="6" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="30" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="94" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="44" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="57" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="74" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="21" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="13" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="37" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="57" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="120" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="53" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="46" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="45" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="26" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="V"
	k="26" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="73" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="bullet"
	k="24" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="24" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="6" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V.smcp"
	k="67" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="11" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="83" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="60" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="57" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="30" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="97" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="64" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V"
	k="84" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="7" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="27" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="27" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="37" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="v,gamma,nu"
	k="64" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="54" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="4" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="j,jcircumflex,afii10105"
	k="-40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="seven"
	k="73" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one"
	k="99" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="zero,six"
	k="21" />
    <hkern g1="questiondown"
	g2="j,jcircumflex,afii10105"
	k="-160" />
    <hkern g1="questiondown.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="97" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="67" />
    <hkern g1="questiondown.case"
	g2="V"
	k="77" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="97" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="7" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="J,Jcircumflex,afii10057"
	k="106" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="97" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="83" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="ampersand"
	k="-4" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="34" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="13" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="V"
	k="27" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="50" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="87" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="19" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="question"
	k="47" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="74" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="49" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="V"
	k="46" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="97" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="7" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="31" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="16" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="V"
	k="33" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="37" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="question"
	k="27" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="68" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="V"
	k="56" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="v,gamma,nu,chi"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="28" />
    <hkern g1="v,gamma,nu,chi"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="7" />
    <hkern g1="v,gamma,nu,chi"
	g2="V"
	k="17" />
    <hkern g1="v,gamma,nu,chi"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="v,gamma,nu,chi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="v,gamma,nu,chi"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="64" />
    <hkern g1="v,gamma,nu,chi"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="34" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="28" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="13" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="14" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="37" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="37" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="question"
	k="6" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="28" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="7" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="V"
	k="13" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="54" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="34" />
    <hkern g1="X,Chi,afii10039"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="47" />
    <hkern g1="X,Chi,afii10039"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="37" />
    <hkern g1="X,Chi,afii10039"
	g2="v,gamma,nu"
	k="34" />
    <hkern g1="X,Chi,afii10039"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="34" />
    <hkern g1="X,Chi,afii10039"
	g2="bullet.case"
	k="60" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="74" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen,periodcentered,endash,emdash"
	k="46" />
    <hkern g1="X.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="27" />
    <hkern g1="X.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="18" />
    <hkern g1="X.smcp"
	g2="ampersand"
	k="20" />
    <hkern g1="zero,nine"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="21" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="94" />
    <hkern g1="x,afii10087"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="38" />
    <hkern g1="x,afii10087"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="26" />
    <hkern g1="x,afii10087"
	g2="V"
	k="26" />
    <hkern g1="x,afii10087"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="77" />
    <hkern g1="x,afii10087"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="34" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.case,bracketleft.case,braceleft.case"
	g2="j,jcircumflex,afii10105"
	k="-110" />
    <hkern g1="one.numr"
	g2="fraction"
	k="-18" />
    <hkern g1="seven.numr"
	g2="fraction"
	k="50" />
    <hkern g1="fraction"
	g2="one.dnom"
	k="-1" />
    <hkern g1="fraction"
	g2="four.dnom"
	k="50" />
  </font>
</defs></svg>
