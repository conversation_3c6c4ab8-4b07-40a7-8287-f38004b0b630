﻿using SNP.PDF.Printer.Enums;
using SNP.PDF.Printer.Models.Base;

namespace SNP.PDF.Printer.Models.Entities
{
    public class MetaEntity
    {
        public MetaEntity(PrintType printType)
        {
            Type = printType;
        }

        public MetaEntity(Meta meta, PrintType printType) : this(printType)
        {
            FileName = meta.FileName;
        }

        public string FileName { get; set; }

        public PrintType Type { get; set; }

        public void Update(Meta meta, PrintType printType)
        {
            FileName = meta.FileName;
            Type = printType;
        }
    }
}
