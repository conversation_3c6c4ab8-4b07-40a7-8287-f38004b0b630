﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;

namespace Snp.PDF.Printer.Services.v2.Helper
{
    public static class ZipFileHelper
    {
        /// <summary>
        /// nén nhiều file thành file zip
        /// </summary>
        /// <param name="fileZipName"></param>
        /// <param name="files"></param>
        /// <param name="exResult"></param>
        public static void CreateZipFile(string fileZipName, IEnumerable<string> files, out Exception exResult)
        {
            try
            {
                exResult = null;

                using (var zip = ZipFile.Open(fileZipName, ZipArchiveMode.Create))
                {
                    foreach (var file in files)
                    {
                        // Add the entry for each file
                        zip.CreateEntryFromFile(file, Path.GetFileName(file), CompressionLevel.Optimal);
                    }
                }
            }
            catch (Exception ex)
            {
                exResult = ex;
            }
        }
    }
}
