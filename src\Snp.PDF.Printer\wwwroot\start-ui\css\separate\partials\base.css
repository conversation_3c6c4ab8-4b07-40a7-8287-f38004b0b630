/* ==========================================================================
   HTML5 display definitions
   ========================================================================== */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
  display: block;
}
audio,
canvas,
video {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
[hidden] {
  display: none;
}
/* ==========================================================================
   Base
   ========================================================================== */
/**
 * 1. Correct text resizing oddly in IE 6/7 when body `font-size` is set using
 *    `em` units.
 * 2. Prevent iOS text size adjust after orientation change, without disabling
 *    user zoom.
 */
html {
  font-size: 100%;
  /* 1 */
  -ms-text-size-adjust: 100%;
  /* 2 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}
html,
button,
input,
select,
textarea {
  font-family: sans-serif;
}
body {
  margin: 0;
}
*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
/* ==========================================================================
   Links
   ========================================================================== */
*:focus {
  outline: none;
}
*:active,
*:hover {
  outline: 0;
}
/* ==========================================================================
   Typography
   ========================================================================== */
abbr[title] {
  border-bottom: 1px dotted;
}
b,
strong {
  font-weight: bold;
}
blockquote {
  margin: 1em 40px;
}
dfn {
  font-style: italic;
}
hr {
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  height: 0;
}
mark {
  background: #ff0;
  color: #000;
}
pre {
  margin: 0px;
}
/**
 * Correct font family set oddly in IE 6, Safari 4/5, and Chrome.
 */
code,
kbd,
pre,
samp {
  font-family: monospace, serif;
  _font-family: 'courier new', monospace;
  font-size: 1em;
}
/**
 * Improve readability of pre-formatted text in all browsers.
 */
pre {
  white-space: pre;
  white-space: pre-wrap;
  word-wrap: break-word;
}
/**
 * Address CSS quotes not supported in IE 6/7.
 */
q {
  quotes: none;
}
/**
 * Address `quotes` property not supported in Safari 4.
 */
q:before,
q:after {
  content: '';
  content: none;
}
/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%;
}
/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
/* ==========================================================================
   Lists
   ========================================================================== */
/**
 * Address margins set differently in IE 6/7.
 */
dl,
menu,
ol,
ul {
  margin: 0;
}
dd {
  margin: 0 0 0 40px;
}
/**
 * Correct list images handled incorrectly in IE 7.
 */
nav ul,
nav ol {
  list-style: none;
  list-style-image: none;
}
/* ==========================================================================
   Embedded content
   ========================================================================== */
/**
 * 1. Remove border when inside `a` element in IE 6/7/8/9 and Firefox 3.
 * 2. Improve image quality when scaled in IE 7.
 */
img {
  border: 0;
  /* 1 */
  -ms-interpolation-mode: bicubic;
  /* 2 */
}
/**
 * Correct overflow displayed oddly in IE 9.
 */
svg:not(:root) {
  overflow: hidden;
}
/* ==========================================================================
   Figures
   ========================================================================== */
/**
 * Address margin not present in IE 6/7/8/9, Safari 5, and Opera 11.
 */
figure {
  margin: 0;
}
/* ==========================================================================
   Forms
   ========================================================================== */
/**
 * Correct margin displayed oddly in IE 6/7.
 */
form {
  margin: 0;
}
/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
/**
 * 1. Correct color not being inherited in IE 6/7/8/9.
 * 2. Correct text not wrapping in Firefox 3.
 * 3. Correct alignment displayed oddly in IE 6/7.
 */
legend {
  border: 0;
  /* 1 */
  padding: 0;
  white-space: normal;
  /* 2 */
  *margin-left: -7px;
  /* 3 */
}
/**
 * 1. Correct font size not being inherited in all browsers.
 * 2. Address margins set differently in IE 6/7, Firefox 3+, Safari 5,
 *    and Chrome.
 * 3. Improve appearance and consistency in all browsers.
 */
button,
input,
select,
textarea {
  font-size: 100%;
  /* 1 */
  margin: 0;
  /* 2 */
  vertical-align: baseline;
  /* 3 */
  *vertical-align: middle;
  /* 3 */
}
/**
 * Address Firefox 3+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
button,
input {
  line-height: normal;
}
/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Chrome, Safari 5+, and IE 6+.
 * Correct `select` style inheritance in Firefox 4+ and Opera.
 */
button,
select {
  text-transform: none;
}
/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 * 4. Remove inner spacing in IE 7 without affecting normal text inputs.
 *    Known issue: inner spacing remains in IE 6.
 */
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
  cursor: pointer;
  /* 3 */
  *overflow: visible;
  /* 4 */
}
/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default;
}
/**
 * 1. Address box sizing set to content-box in IE 8/9.
 * 2. Remove excess padding in IE 8/9.
 * 3. Remove excess padding in IE 7.
 *    Known issue: excess padding remains in IE 6.
 */
input[type="checkbox"],
input[type="radio"] {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
  *height: 13px;
  /* 3 */
  *width: 13px;
  /* 3 */
}
/**
 * 1. Address `appearance` set to `searchfield` in Safari 5 and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari 5 and Chrome
 *    (include `-moz` to future-proof).
 */
input[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  /* 2 */
  box-sizing: content-box;
}
/**
 * Remove inner padding and search cancel button in Safari 5 and Chrome
 * on OS X.
 */
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
/**
 * Remove inner padding and border in Firefox 3+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
/**
 * 1. Remove default vertical scrollbar in IE 6/7/8/9.
 * 2. Improve readability and alignment in all browsers.
 */
textarea {
  overflow: auto;
  /* 1 */
  vertical-align: top;
  /* 2 */
}
/* ==========================================================================
   Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}
/* ==========================================================================
   Opinionated defaults
   ========================================================================== */
html {
  font-size: 1em;
  line-height: 1.4;
}
* {
  padding: 0px;
  margin: 0px;
}
img {
  border: none;
}
ul {
  list-style: none;
}
hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
  margin: 1em 0;
  padding: 0;
}
textarea {
  resize: vertical;
}
fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}
audio,
canvas,
img,
video {
  vertical-align: middle;
}
.browsehappy {
  margin: 0.2em 0;
  background: #ccc;
  color: #000;
  padding: 0.2em 0;
}
::-moz-selection {
  background: #b3d4fc;
  text-shadow: none;
}
::selection {
  background: #b3d4fc;
  text-shadow: none;
}
/* ==========================================================================
   Mixins
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   Base
   ========================================================================== */
* {
  outline: none !important;
}
body {
  background: #eceff4;
}
/* ==========================================================================
   Site Header Search
   ========================================================================== */
.site-header-search {
  width: 100%;
  height: 30px;
  position: relative;
  padding: 0 35px 0 0;
  border: solid 1px #c5d6de;
  -webkit-border-radius: 25rem;
          border-radius: 25rem;
  background: #fff;
  overflow: hidden;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.site-header-search input[type="text"] {
  width: 100%;
  padding: 4px 0 0 14px;
  border: none;
  background: none;
  font-size: 0.875rem /*14/16*/;
  font-weight: 600;
  color: #343434;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.site-header-search button {
  display: block;
  position: absolute;
  right: 0;
  top: 0;
  width: 35px;
  height: 100%;
  line-height: 30px;
  text-align: center;
  color: #adb7be;
  border: none;
  background: none;
  font-size: 0.9375rem /*15/16*/;
}
.site-header-search button:hover {
  color: #00a8ff;
}
.site-header-search .overlay {
  display: none;
}
.site-header-search.closed {
  width: 37px;
  border-color: transparent;
}
.site-header-search.closed .overlay {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  cursor: pointer;
}
.site-header-search.closed input[type="text"] {
  opacity: 0;
}
.site-header-search.closed:hover button {
  color: #00a8ff;
}
/* ==========================================================================
   Site Header
   ========================================================================== */
.site-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  background: #fff;
  border-bottom: solid 1px #c5d6de;
  padding: 20px 15px 0 0;
  z-index: 80;
  /* ==========================================================================
	  Notification & messages
	  ========================================================================== */
  /* ==========================================================================
	  Help
	  ========================================================================== */
  /* ==========================================================================
	  User Menu
	  ========================================================================== */
  /* ==========================================================================
	  Language
	  ========================================================================== */
  /* ==========================================================================
	  Common Elements
	  ========================================================================== */
  /* ==========================================================================
	  Mobile Right Menu
	  ========================================================================== */
}
.site-header .btn-nav {
  margin-left: 20px;
  margin-top: -5px;
  float: left;
}
.site-header .site-logo {
  display: block;
  float: left;
  height: 40px;
  zoom: 1;
  color: #343434;
  line-height: 40px;
}
.site-header .site-logo:before,
.site-header .site-logo:after {
  content: " ";
  display: table;
}
.site-header .site-logo:after {
  clear: both;
}
.site-header .site-logo img {
  float: left;
  height: 50px;
  position: relative;
  top: -5px;
}
@media (max-width: 767px) {
  .site-header .site-logo img {
    height: auto;
    width: 35px;
    top: 6px;
  }
}
.site-header .site-logo .site-logo-txt {
  float: left;
  font-size: 1.5rem /*24/16*/;
  font-weight: 300;
}
.site-header .site-logo .site-logo-txt strong {
  font-weight: 600;
}
.site-header .site-header-content {
  float: right;
  height: 40px;
  padding: 5px 0;
  width: 100%;
  margin-left: -210px;
}
.site-header .site-header-content .site-header-content-in {
  zoom: 1;
  margin-left: 210px;
}
.site-header .site-header-content .site-header-content-in:before,
.site-header .site-header-content .site-header-content-in:after {
  content: " ";
  display: table;
}
.site-header .site-header-content .site-header-content-in:after {
  clear: both;
}
@media (max-width: 767px) {
  .site-header .site-header-content {
    margin-left: -80px;
  }
  .site-header .site-header-content .site-header-content-in {
    margin-left: 80px;
  }
}
.site-header .site-header-shown {
  float: right;
  zoom: 1;
}
.site-header .site-header-shown:before,
.site-header .site-header-shown:after {
  content: " ";
  display: table;
}
.site-header .site-header-shown:after {
  clear: both;
}
.site-header .header-alarm {
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  position: relative;
  display: block;
  color: #adb7be;
  font-size: 1.125rem /*18/16*/;
}
.site-header .header-alarm:hover {
  color: #00a8ff;
}
.site-header .header-alarm i {
  vertical-align: middle;
}
.site-header .header-alarm.active:after {
  content: '';
  display: block;
  width: 8px;
  height: 8px;
  border: solid 1px #fff;
  background: #fa424a;
  -webkit-border-radius: 50%;
          border-radius: 50%;
  position: absolute;
  left: 50%;
  top: 6px;
  margin-left: 1px;
}
.site-header .dropdown.dropdown-notification {
  float: left;
}
.site-header .dropdown.dropdown-notification.open .header-alarm {
  color: #00a8ff;
}
.site-header .dropdown.dropdown-notification.messages {
  margin-left: 11px;
}
.site-header .dropdown.dropdown-notification.messages .header-alarm:after {
  margin-left: 4px;
}
.site-header .dropdown.dropdown-notification .dropdown-menu-notif-more {
  border-top: solid 1px #d8e2e7;
  text-align: center;
  padding: 10px 15px;
  font-size: 1rem;
}
.site-header .dropdown.dropdown-notification .dropdown-menu-notif-more a {
  text-decoration: none;
  color: #0082c6;
  border-bottom: solid 1px transparent;
}
.site-header .dropdown.dropdown-notification .dropdown-menu-notif-more a:hover {
  border-bottom-color: rgba(0, 130, 198, 0.5);
}
.site-header .dropdown-menu-notif {
  width: 290px;
  padding: 0;
  line-height: normal;
  font-size: 0.9375rem /*15/16*/;
}
.site-header .dropdown-menu-notif a {
  text-decoration: none;
  color: #0082c6;
  border-bottom: solid 1px transparent;
}
.site-header .dropdown-menu-notif a:hover {
  border-bottom-color: rgba(0, 130, 198, 0.5);
}
.site-header .dropdown-menu-notif .dropdown-menu-notif-header {
  padding: 12px 15px;
  border-bottom: solid 1px #d8e2e7;
  font-weight: 600;
}
.site-header .dropdown-menu-notif .dropdown-menu-notif-header .label {
  font-size: 0.875rem /*14/16*/;
  font-weight: 400;
  padding-left: .5em;
  padding-right: .5em;
}
.site-header .dropdown-menu-notif .dropdown-menu-notif-list {
  height: 192px;
  overflow: auto;
}
.site-header .dropdown-menu-notif .dropdown-menu-notif-item {
  padding: 8px 15px 8px 57px;
  position: relative;
}
.site-header .dropdown-menu-notif .dropdown-menu-notif-item:nth-child(even) {
  background: #fbfcfd;
}
.site-header .dropdown-menu-notif .dropdown-menu-notif-item .photo {
  width: 32px;
  height: 32px;
  position: absolute;
  left: 15px;
  top: 50%;
  margin-top: -16px;
}
.site-header .dropdown-menu-notif .dropdown-menu-notif-item .photo img {
  -webkit-border-radius: 50%;
          border-radius: 50%;
  display: block;
  width: 100%;
}
.site-header .dropdown-menu-notif .dropdown-menu-notif-item .dot {
  display: inline-block;
  vertical-align: middle;
  width: 6px;
  height: 6px;
  margin: 0 0 6px;
  -webkit-border-radius: 50%;
          border-radius: 50%;
  background: #fa424a;
  position: relative;
  top: 2px;
}
.site-header .dropdown-menu-messages {
  width: 290px;
  padding-bottom: 0;
}
.site-header .dropdown-menu-messages .dropdown-menu-messages-header {
  border-bottom: solid 1px #d8e2e7;
  padding: 7px 15px 12px;
  zoom: 1;
}
.site-header .dropdown-menu-messages .dropdown-menu-messages-header:before,
.site-header .dropdown-menu-messages .dropdown-menu-messages-header:after {
  content: " ";
  display: table;
}
.site-header .dropdown-menu-messages .dropdown-menu-messages-header:after {
  clear: both;
}
.site-header .dropdown-menu-messages .dropdown-menu-messages-header .nav {
  float: left;
  zoom: 1;
}
.site-header .dropdown-menu-messages .dropdown-menu-messages-header .nav:before,
.site-header .dropdown-menu-messages .dropdown-menu-messages-header .nav:after {
  content: " ";
  display: table;
}
.site-header .dropdown-menu-messages .dropdown-menu-messages-header .nav:after {
  clear: both;
}
.site-header .dropdown-menu-messages .dropdown-menu-messages-header .nav-item {
  float: left;
  margin: 0 15px 0 0;
}
.site-header .dropdown-menu-messages .dropdown-menu-messages-header .nav-link {
  font-size: 1rem;
  font-weight: 600;
  color: #919fa9;
  -webkit-transition: none;
  -o-transition: none;
  transition: none;
}
.site-header .dropdown-menu-messages .dropdown-menu-messages-header .nav-link.active {
  color: #343434;
}
.site-header .dropdown-menu-messages .dropdown-menu-messages-header .nav-link .label {
  font-size: .875rem;
  padding-left: .5em;
  padding-right: .5em;
  font-weight: 400;
}
.site-header .dropdown-menu-messages .dropdown-menu-messages-header .create {
  float: right;
  height: 18px;
  font-size: 1rem;
  border: none;
  background: none;
  color: #919fa9;
  position: relative;
  top: -1px;
}
.site-header .dropdown-menu-messages .dropdown-menu-messages-header .create:hover {
  color: #00a8ff;
}
.site-header .dropdown-menu-messages .mess-item {
  display: block;
  color: #343434;
  padding: 14px 15px 14px 57px;
  position: relative;
  line-height: 18px;
}
.site-header .dropdown-menu-messages .mess-item:nth-child(odd) {
  background-color: #fbfcfd;
}
.site-header .dropdown-menu-messages .mess-item:hover {
  background-color: #ecf2f5;
}
.site-header .dropdown-menu-messages .mess-item span {
  display: block;
}
.site-header .dropdown-menu-messages .mess-item .avatar-preview {
  position: absolute;
  left: 15px;
  top: 15px;
}
.site-header .dropdown-menu-messages .mess-item .mess-item-name,
.site-header .dropdown-menu-messages .mess-item .mess-item-txt {
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  -o-text-overflow: ellipsis;
     text-overflow: ellipsis;
  font-size: .9375rem;
}
.site-header .dropdown-menu-messages .mess-item .mess-item-name {
  font-weight: 600;
}
.site-header .dropdown-menu-messages .mess-item .mess-item-txt {
  color: #919fa9;
}
.site-header .dropdown-menu-messages .tab-pane {
  height: 192px;
  overflow: auto;
}
.site-header .help-dropdown {
  float: left;
  position: relative;
  height: 30px;
  margin: 0 0 0 10px;
}
.site-header .help-dropdown > button {
  height: 30px;
  line-height: 30px;
  border: none;
  background: none;
  color: #adb7be;
}
.site-header .help-dropdown.opened > button,
.site-header .help-dropdown > button:hover {
  color: #00a8ff;
}
.site-header .help-dropdown .help-dropdown-popup {
  display: none;
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -400px;
  margin-top: 7px;
  background: #fff;
  border: solid 1px #d8e2e7;
  -webkit-border-radius: .25rem;
          border-radius: .25rem;
  zoom: 1;
  width: 800px;
}
.site-header .help-dropdown .help-dropdown-popup:before,
.site-header .help-dropdown .help-dropdown-popup:after {
  content: " ";
  display: table;
}
.site-header .help-dropdown .help-dropdown-popup:after {
  clear: both;
}
.site-header .help-dropdown .help-dropdown-popup:before,
.site-header .help-dropdown .help-dropdown-popup:after {
  content: '';
  display: block;
  position: absolute;
  left: 50%;
  width: 0;
  height: 0;
  border-style: solid;
}
.site-header .help-dropdown .help-dropdown-popup:before {
  top: -10px;
  margin-left: -8px;
  border-width: 0 8px 10px 8px;
  border-color: transparent transparent #d8e2e7 transparent;
}
.site-header .help-dropdown .help-dropdown-popup:after {
  top: -9px;
  margin-left: -7px;
  border-width: 0 7px 9px 7px;
  border-color: transparent transparent #ffffff transparent;
}
.site-header .help-dropdown .help-dropdown-popup-side {
  display: table-cell;
  vertical-align: top;
  /*float: left;*/
  width: 230px;
  background: #f6f8fa;
  -webkit-border-radius: .25rem 0 0 .25rem;
          border-radius: .25rem 0 0 .25rem;
  padding: 30px;
  border-right: solid 1px #d8e2e7;
  position: relative;
  z-index: 2;
  font-weight: 600;
}
.site-header .help-dropdown .help-dropdown-popup-side a {
  color: #343434;
}
.site-header .help-dropdown .help-dropdown-popup-side a:hover {
  color: #00a8ff;
}
.site-header .help-dropdown .help-dropdown-popup-side a.active {
  color: #00a8ff;
}
.site-header .help-dropdown .help-dropdown-popup-side li {
  margin: 0 0 .5rem;
}
.site-header .help-dropdown .help-dropdown-popup-cont {
  /*float: right; width: 100%;*/
  /*margin-left: -230px;*/
  display: table-cell;
  vertical-align: top;
}
.site-header .help-dropdown .help-dropdown-popup-cont-in {
  /*margin-left: 230px;*/
  padding: 30px;
}
.site-header .help-dropdown .help-dropdown-popup-item {
  border-top: solid 1px #d8e2e7;
  padding: 10px 0;
  display: block;
  color: #343434;
}
.site-header .help-dropdown .help-dropdown-popup-item:hover {
  color: #00a8ff;
}
.site-header .help-dropdown .help-dropdown-popup-item:first-child {
  border-top: none;
  padding-top: 0;
}
.site-header .help-dropdown .help-dropdown-popup-item .describe {
  display: block;
  color: #919fa9;
  font-size: .875rem;
}
.site-header .help-dropdown .jscroll {
  max-height: 265px;
  overflow: auto;
}
.site-header .help-dropdown.opened .help-dropdown-popup {
  display: table;
  width: 800px;
}
.site-header .user-menu.dropdown {
  float: left;
  margin: 0 0 0 15px;
  height: 30px;
  line-height: 30px;
}
.site-header .user-menu.dropdown .dropdown-toggle {
  display: block;
  color: #adb7be;
  height: 30px;
  border: none;
  background: none;
  line-height: 30px;
}
.site-header .user-menu.dropdown .dropdown-toggle:after {
  border-top: 5px solid;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}
.site-header .user-menu.dropdown .dropdown-toggle img {
  float: left;
  display: block;
  height: 32px;
  width: 32px;
  margin: -1px 3px -1px 0;
  -webkit-border-radius: 50%;
          border-radius: 50%;
  border: solid 1px transparent;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.site-header .user-menu.dropdown.open .dropdown-toggle,
.site-header .user-menu.dropdown .dropdown-toggle:hover {
  color: #00a8ff;
}
.site-header .dropdown.dropdown-lang {
  float: left;
}
.site-header .dropdown.dropdown-lang .flag-icon {
  -webkit-box-shadow: 0 0 1px #c5d6de, 0 0 1px #c5d6de, 0 0 1px #c5d6de;
          box-shadow: 0 0 1px #c5d6de, 0 0 1px #c5d6de, 0 0 1px #c5d6de;
}
.site-header .dropdown.dropdown-lang .dropdown-toggle {
  height: 30px;
  width: 45px;
  text-align: right;
  padding: 0 5px 0 0;
  background: none;
  border: none;
  color: #adb7be;
}
.site-header .dropdown.dropdown-lang .dropdown-toggle:after {
  display: none;
}
.site-header .dropdown.dropdown-lang .dropdown-toggle:hover {
  color: #00a8ff;
}
.site-header .dropdown.dropdown-lang .dropdown-menu {
  width: 250px;
}
.site-header .dropdown.dropdown-lang .dropdown-menu .flag-icon {
  margin: 0 5px 0 0;
  position: relative;
  top: -1px;
}
.site-header .dropdown.dropdown-lang.open .dropdown-toggle {
  color: #00a8ff;
}
.site-header .burger-right {
  display: none;
  float: right;
  height: 30px;
  margin: 0 0 0 10px;
  line-height: 30px;
  cursor: pointer;
  color: #adb7be;
  border: none;
  background: none;
  font-size: 1.375rem /*22/16*/;
  position: relative;
  top: 1px;
}
.site-header .burger-right:hover {
  color: #00a8ff;
}
.site-header .burger-right i {
  vertical-align: middle;
}
.site-header .dropdown {
  float: right;
}
.site-header .dropdown.dropdown-typical {
  float: left;
  margin-right: -10px;
}
.site-header .dropdown.dropdown-typical .lbl {
  font-size: 15px;
  line-height: 16px;
}
.site-header .dropdown a.dropdown-toggle {
  height: 30px;
  line-height: 30px;
}
.site-header .dropdown a.dropdown-toggle.no-arr:after {
  display: none;
}
.site-header .dropdown a.dropdown-toggle .label {
  padding: 3px 6px 2px;
  margin-left: 3px;
  font-size: 0.8125rem /*13/16*/;
  position: relative;
  top: -2px;
}
.site-header .dropdown .dropdown-item {
  font-size: 15px;
}
.site-header .dropdown .btn.dropdown-toggle {
  height: 30px;
  padding: 0 12px;
  font-size: 0.8125rem /*13/16*/;
  line-height: 28px;
  background-color: #00a8ff;
  border-color: #00a8ff;
  color: #fff;
}
.site-header .dropdown .btn.dropdown-toggle:after {
  border-top: 5px solid;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}
.site-header .dropdown.open .btn.dropdown-toggle {
  background: #fff;
  color: #00a8ff;
}
.site-header .site-header-search-container {
  float: right;
  width: 158px;
  margin: 0 10px 0 0;
  zoom: 1;
}
.site-header .site-header-search-container:before,
.site-header .site-header-search-container:after {
  content: " ";
  display: table;
}
.site-header .site-header-search-container:after {
  clear: both;
}
.site-header .site-header-search-container .site-header-search {
  float: right;
}
.site-header .site-header-collapsed {
  float: left;
  width: 100%;
  margin-right: -199px;
}
.site-header .site-header-collapsed .site-header-collapsed-in {
  margin-right: 199px;
  zoom: 1;
}
.site-header .site-header-collapsed .site-header-collapsed-in:before,
.site-header .site-header-collapsed .site-header-collapsed-in:after {
  content: " ";
  display: table;
}
.site-header .site-header-collapsed .site-header-collapsed-in:after {
  clear: both;
}
.site-header .mobile-menu-right-overlay {
  display: none;
}
.site-header .hamburger {
  float: left;
  position: relative;
  top: 5px;
  margin: 0 0 0 12px;
}
@media (max-width: 1530px) and (min-width: 1055px) {
  .site-header .dropdown.dropdown-typical a.dropdown-toggle .font-icon {
    margin-right: 0;
  }
  .site-header .dropdown.dropdown-typical a.dropdown-toggle .lbl {
    display: none;
  }
}
@media (max-width: 1199px) {
  .site-header .site-header-search-container {
    width: 110px;
  }
}
@media (max-width: 1056px) {
  .site-header {
    padding-right: 0;
  }
  .site-header .site-logo-text {
    display: none;
  }
  .site-header .help-dropdown {
    display: none;
  }
  .site-header .burger-right {
    display: block;
  }
  .site-header .site-header-collapsed {
    position: fixed;
    right: -270px;
    top: 0;
    z-index: 90;
    height: 100%;
    width: 270px;
    background: #fff;
    border-left: solid 1px #c5d6de;
    padding: 15px;
    overflow: auto;
    -webkit-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    float: none;
    margin: 0;
  }
  .site-header .site-header-collapsed .site-header-collapsed-in {
    margin: 0;
  }
  .site-header .site-header-collapsed .site-header-search.closed {
    width: 100%;
    border-color: #c5d6de;
  }
  .site-header .site-header-collapsed .site-header-search.closed .overlay {
    display: none;
  }
  .site-header .site-header-collapsed .site-header-search.closed input[type="text"] {
    opacity: 1;
  }
  .site-header .site-header-collapsed .site-header-search.closed:hover button {
    color: #c5d6de;
  }
  .site-header .site-header-collapsed .site-header-search-container {
    width: 100%;
    padding: 10px 0;
    float: none;
  }
  .site-header .site-header-collapsed .btn-nav {
    margin-left: 0;
    margin-top: 5px;
    width: 100%;
  }
  .site-header .site-header-collapsed .dropdown {
    float: none;
    margin: 0;
    zoom: 1;
  }
  .site-header .site-header-collapsed .dropdown:before,
  .site-header .site-header-collapsed .dropdown:after {
    content: " ";
    display: table;
  }
  .site-header .site-header-collapsed .dropdown:after {
    clear: both;
  }
  .site-header .site-header-collapsed .dropdown .btn.dropdown-toggle {
    width: 100%;
    margin-bottom: 5px;
    margin-top: 8px;
  }
  .site-header .site-header-collapsed .dropdown .dropdown-menu {
    display: none;
    position: static;
    width: 100%;
    margin-top: 0;
  }
  .site-header .site-header-collapsed .dropdown.open a.dropdown-toggle {
    color: #343434;
  }
  .site-header .site-header-collapsed .dropdown.open a.dropdown-toggle:after,
  .site-header .site-header-collapsed .dropdown.open a.dropdown-toggle .font-icon {
    color: #adb7be;
  }
  .site-header .site-header-collapsed .dropdown.open .btn.dropdown-toggle {
    color: #fff;
    background-color: #00a8ff;
  }
  .site-header .site-header-collapsed .dropdown.mobile-opened .dropdown-menu {
    display: block;
  }
  .site-header .site-header-collapsed .dropdown.mobile-opened .dropdown-toggle:after {
    -webkit-transform: rotate(180deg);
        -ms-transform: rotate(180deg);
         -o-transform: rotate(180deg);
            transform: rotate(180deg);
  }
  .site-header .site-header-collapsed .dropdown.mobile-opened a.dropdown-toggle {
    color: #00a8ff;
  }
  .site-header .site-header-collapsed .dropdown.mobile-opened a.dropdown-toggle:after,
  .site-header .site-header-collapsed .dropdown.mobile-opened a.dropdown-toggle .font-icon {
    color: #00a8ff;
  }
  .site-header .site-header-collapsed .dropdown.mobile-opened .btn.dropdown-toggle {
    color: #00a8ff;
    background-color: #fff;
  }
  .site-header .site-header-collapsed .dropdown.dropdown-typical .dropdown-more .dropdown-more-caption:before {
    -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
         -o-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .site-header .site-header-collapsed .dropdown.dropdown-typical .dropdown-more .dropdown-more-sub {
    display: none;
    position: static;
    left: 0;
    padding: 0;
    margin: 0;
  }
  .site-header .site-header-collapsed .dropdown.dropdown-typical .dropdown-more .dropdown-more-sub-in {
    border-right: none;
    -webkit-border-radius: 0;
            border-radius: 0;
  }
  .site-header .site-header-collapsed .dropdown.dropdown-typical .dropdown-more:hover .dropdown-more-sub {
    display: none;
  }
  .site-header .site-header-collapsed .dropdown.dropdown-typical .dropdown-more.opened .dropdown-more-caption {
    color: #00a8ff;
  }
  .site-header .site-header-collapsed .dropdown.dropdown-typical .dropdown-more.opened .dropdown-more-caption:before {
    -webkit-transform: rotate(-90deg);
        -ms-transform: rotate(-90deg);
         -o-transform: rotate(-90deg);
            transform: rotate(-90deg);
    border-left-color: #00a8ff;
  }
  .site-header .site-header-collapsed .dropdown.dropdown-typical .dropdown-more.opened .dropdown-more-sub {
    display: block;
  }
  .menu-right-opened .site-header .mobile-menu-right-overlay {
    display: block;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 85;
    background: rgba(48, 75, 88, 0.5);
  }
  .menu-right-opened .site-header .site-header-collapsed {
    right: 0;
  }
}
@media (max-width: 767px) {
  .site-header .burger-right {
    margin-left: 7px;
  }
  .site-header .dropdown.dropdown-notification.messages {
    margin-left: 8px;
  }
  .site-header .dropdown.dropdown-lang .dropdown-toggle {
    width: 42px;
  }
  .site-header .dropdown.dropdown-lang .dropdown-menu {
    margin-right: -52px;
  }
  .site-header .user-menu.dropdown {
    margin-left: 12px;
  }
  .site-header .dropdown-menu-notif {
    margin-right: -172px;
  }
  .site-header .dropdown-menu-messages {
    margin-right: -134px;
  }
}
/* ==========================================================================
   Hamburger
   ========================================================================== */
.hamburger {
  display: none;
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  width: 30px;
  height: 30px;
  font-size: 0;
  text-indent: -9999px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  -webkit-border-radius: 0;
          border-radius: 0;
  border: none;
  cursor: pointer;
  -webkit-transition: background 0.3s;
  -o-transition: background 0.3s;
  transition: background 0.3s;
  background: none;
}
.hamburger span {
  display: block;
  margin: 0 4px;
  position: absolute;
  top: 50%;
  margin-top: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: #adb7be;
  -webkit-transition: -webkit-transform 0.3s;
  transition: -webkit-transform 0.3s;
  -o-transition: transform 0.3s, -o-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s, -o-transform 0.3s;
}
.hamburger span:before,
.hamburger span:after {
  position: absolute;
  display: block;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #adb7be;
  content: "";
}
.hamburger span:before {
  top: -7px;
  -webkit-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
  -webkit-transition: width 0.3s, top 0.3s, -webkit-transform 0.3s;
  transition: width 0.3s, top 0.3s, -webkit-transform 0.3s;
  -o-transition: transform 0.3s, width 0.3s, top 0.3s, -o-transform 0.3s;
  transition: transform 0.3s, width 0.3s, top 0.3s;
  transition: transform 0.3s, width 0.3s, top 0.3s, -webkit-transform 0.3s, -o-transform 0.3s;
}
.hamburger span:after {
  bottom: -7px;
  -webkit-transform-origin: bottom right;
      -ms-transform-origin: bottom right;
       -o-transform-origin: bottom right;
          transform-origin: bottom right;
  -webkit-transition: width 0.3s, bottom 0.3s, -webkit-transform 0.3s;
  transition: width 0.3s, bottom 0.3s, -webkit-transform 0.3s;
  -o-transition: transform 0.3s, width 0.3s, bottom 0.3s, -o-transform 0.3s;
  transition: transform 0.3s, width 0.3s, bottom 0.3s;
  transition: transform 0.3s, width 0.3s, bottom 0.3s, -webkit-transform 0.3s, -o-transform 0.3s;
}
.hamburger.is-active {
  background: none;
}
.hamburger.is-active span {
  -webkit-transform: rotate(180deg);
      -ms-transform: rotate(180deg);
       -o-transform: rotate(180deg);
          transform: rotate(180deg);
}
.hamburger.is-active span:before,
.hamburger.is-active span:after {
  width: 50%;
}
.hamburger.is-active span:before {
  top: 0;
  -webkit-transform: translateX(12px) translateY(1px) rotate(45deg);
      -ms-transform: translateX(12px) translateY(1px) rotate(45deg);
       -o-transform: translateX(12px) translateY(1px) rotate(45deg);
          transform: translateX(12px) translateY(1px) rotate(45deg);
}
.hamburger.is-active span:after {
  bottom: 0;
  -webkit-transform: translateX(12px) translateY(-1px) rotate(-45deg);
      -ms-transform: translateX(12px) translateY(-1px) rotate(-45deg);
       -o-transform: translateX(12px) translateY(-1px) rotate(-45deg);
          transform: translateX(12px) translateY(-1px) rotate(-45deg);
}
.hamburger:hover span,
.hamburger:hover span:before,
.hamburger:hover span:after {
  background-color: #00a8ff;
}
@media (max-width: 1056px) {
  .hamburger {
    display: block;
  }
}
/* ==========================================================================
   STYLE: Wet Asphalt Theme
   ========================================================================== */
.wet-aspalt-theme .site-header {
  background-color: #304b58;
  border-bottom-color: #304b58;
}
.wet-aspalt-theme .site-header .dropdown.dropdown-lang .flag-icon {
  opacity: .9;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.wet-aspalt-theme .site-header .dropdown.dropdown-lang .dropdown-toggle {
  color: #fff;
}
.wet-aspalt-theme .site-header .dropdown.dropdown-lang .dropdown-toggle:hover,
.wet-aspalt-theme .site-header .dropdown.dropdown-lang.open .dropdown-toggle {
  color: #c5d6de;
}
.wet-aspalt-theme .site-header .header-alarm {
  color: #fff;
}
.wet-aspalt-theme .site-header .header-alarm.active:after {
  border-color: #304b58;
}
.wet-aspalt-theme .site-header .header-alarm:hover,
.wet-aspalt-theme .site-header .dropdown.dropdown-notification.open .header-alarm {
  color: #c5d6de;
}
.wet-aspalt-theme .site-header .user-menu.dropdown .dropdown-toggle {
  color: #fff;
}
.wet-aspalt-theme .site-header .user-menu.dropdown.open .dropdown-toggle,
.wet-aspalt-theme .site-header .user-menu.dropdown .dropdown-toggle:hover {
  color: #c5d6de;
}
.wet-aspalt-theme .site-header .hamburger span,
.wet-aspalt-theme .site-header .hamburger span:after,
.wet-aspalt-theme .site-header .hamburger span:before {
  background-color: #fff;
}
.wet-aspalt-theme .site-header .burger-right {
  color: #fff;
}
@media (min-width: 1055px) {
  .wet-aspalt-theme .site-header .dropdown.dropdown-typical a.dropdown-toggle,
  .wet-aspalt-theme .site-header .dropdown.dropdown-typical a.dropdown-toggle .font-icon,
  .wet-aspalt-theme .site-header .dropdown.dropdown-typical a.dropdown-toggle:after {
    color: #fff;
  }
  .wet-aspalt-theme .site-header .dropdown.dropdown-typical.open a.dropdown-toggle,
  .wet-aspalt-theme .site-header .dropdown.dropdown-typical a.dropdown-toggle:hover {
    color: #c5d6de;
  }
  .wet-aspalt-theme .site-header .dropdown.dropdown-typical.open a.dropdown-toggle:after,
  .wet-aspalt-theme .site-header .dropdown.dropdown-typical a.dropdown-toggle:hover:after,
  .wet-aspalt-theme .site-header .dropdown.dropdown-typical.open a.dropdown-toggle .font-icon,
  .wet-aspalt-theme .site-header .dropdown.dropdown-typical a.dropdown-toggle:hover .font-icon {
    color: #c5d6de;
  }
  .wet-aspalt-theme .site-header .dropdown .btn.dropdown-toggle {
    border-color: #fff;
    background-color: #fff;
    color: #304b58;
  }
  .wet-aspalt-theme .site-header .help-dropdown button {
    color: #fff;
  }
  .wet-aspalt-theme .site-header .help-dropdown.opened button,
  .wet-aspalt-theme .site-header .help-dropdown button:hover {
    color: #c5d6de;
  }
  .wet-aspalt-theme .site-header .site-header-search {
    background: none;
    border-color: #c5d6de;
  }
  .wet-aspalt-theme .site-header .site-header-search.closed {
    border-color: transparent;
  }
  .wet-aspalt-theme .site-header .site-header-search input[placeholder],
  .wet-aspalt-theme .site-header .site-header-search [placeholder],
  .wet-aspalt-theme .site-header .site-header-search *[placeholder] {
    color: #fff;
  }
  .wet-aspalt-theme .site-header .site-header-search ::-webkit-input-placeholder {
    color: #fff !important;
  }
  .wet-aspalt-theme .site-header .site-header-search ::-moz-placeholder {
    color: #fff !important;
    opacity: 1 !important;
  }
  .wet-aspalt-theme .site-header .site-header-search :-moz-placeholder {
    color: #fff !important;
    opacity: 1 !important;
  }
  .wet-aspalt-theme .site-header .site-header-search ::-moz-placeholder {
    color: #fff !important;
  }
  .wet-aspalt-theme .site-header .site-header-search :-ms-input-placeholder {
    color: #fff !important;
  }
  .wet-aspalt-theme .site-header .site-header-search button {
    color: #fff;
  }
  .wet-aspalt-theme .site-header .site-header-search button:hover,
  .wet-aspalt-theme .site-header .site-header-search.closed:hover button {
    color: #c5d6de;
  }
}
/* ==========================================================================
   STYLE: Dark Theme
   ========================================================================== */
.site-header .site-logo-text {
  float: left;
  width: 240px;
  height: 50px;
  line-height: 50px;
  color: #fff;
  font-size: 1.25rem;
  font-weight: 600;
  background: #272727;
  margin: 0 -1px 0 -0.9375rem;
  position: relative;
  padding: 0;
  text-align: center;
}
.with-side-menu-compact .site-header .site-logo-text {
  width: 100px;
}
.with-side-menu-compact.dark-theme .site-header .site-header-content {
  margin-left: -100px;
}
.with-side-menu-compact.dark-theme .site-header .site-header-content .site-header-content-in {
  margin-left: 100px;
}
@media (max-width: 767px) {
  .with-side-menu-compact.dark-theme .site-header .site-header-content {
    margin-left: -110px;
  }
  .with-side-menu-compact.dark-theme .site-header .site-header-content .site-header-content-in {
    margin-left: 110px;
  }
}
.dark-theme .site-header {
  background-color: #343434;
  height: 50px;
  padding-top: 0;
  border-bottom: none;
}
.dark-theme .site-header .site-header-content {
  padding: 0;
  margin-left: -240px;
}
.dark-theme .site-header .site-header-content .site-header-content-in {
  margin-left: 240px;
}
@media (max-width: 767px) {
  .dark-theme .site-header .site-header-content {
    margin-left: -130px;
  }
  .dark-theme .site-header .site-header-content .site-header-content-in {
    margin-left: 130px;
  }
}
.dark-theme .site-header .site-header-shown {
  padding-top: 10px;
}
.dark-theme .site-header .header-alarm.active:after {
  border-color: #343434;
}
.dark-theme .site-header .hamburger {
  top: 8px;
}
.dark-theme .site-header.site-header-light {
  background: #fff;
}
.dark-theme .site-header.site-header-light .header-alarm.active:after {
  border-color: #fff;
}
@media (min-width: 1055px) {
  .dark-theme .site-header .site-header-collapsed .site-header-collapsed-in {
    margin-right: 112px;
  }
  .dark-theme .site-header .dropdown .btn.dropdown-toggle {
    position: relative;
    top: 10px;
    border-color: #919fa9;
    background-color: #919fa9;
    color: #fff;
  }
  .dark-theme .site-header .dropdown .dropdown-menu {
    margin-top: 14px;
  }
  .dark-theme .site-header .dropdown.dropdown-typical {
    margin: 0 1px 0 0;
  }
  .dark-theme .site-header .dropdown.dropdown-typical a.dropdown-toggle {
    color: #fff;
    height: 50px;
    line-height: 50px;
    display: block;
  }
  .dark-theme .site-header .dropdown.dropdown-typical a.dropdown-toggle .font-icon,
  .dark-theme .site-header .dropdown.dropdown-typical a.dropdown-toggle:after {
    color: #919fa9;
  }
  .dark-theme .site-header .dropdown.dropdown-typical.open a.dropdown-toggle,
  .dark-theme .site-header .dropdown.dropdown-typical a.dropdown-toggle:hover {
    background-color: #919fa9;
  }
  .dark-theme .site-header .dropdown.dropdown-typical.open a.dropdown-toggle:after,
  .dark-theme .site-header .dropdown.dropdown-typical a.dropdown-toggle:hover:after,
  .dark-theme .site-header .dropdown.dropdown-typical.open a.dropdown-toggle .font-icon,
  .dark-theme .site-header .dropdown.dropdown-typical a.dropdown-toggle:hover .font-icon {
    color: #fff;
  }
  .dark-theme .site-header .dropdown.dropdown-typical .dropdown-menu {
    margin-top: 8px;
  }
  .dark-theme .site-header .dropdown.dropdown-typical .dropdown-menu .font-icon {
    color: #919fa9;
  }
  .dark-theme .site-header .dropdown.dropdown-typical .dropdown-menu .dropdown-item:hover .font-icon {
    color: #00a8ff;
  }
  .dark-theme .site-header .site-header-search-container {
    padding-top: 10px;
  }
  .dark-theme .site-header .site-header-search {
    background: #fff;
    border-color: #919fa9;
  }
  .dark-theme .site-header .site-header-search.closed {
    border-color: transparent;
    background: transparent;
  }
  .dark-theme .site-header.site-header-light .dropdown.dropdown-typical a.dropdown-toggle {
    color: #919fa9;
  }
  .dark-theme .site-header.site-header-light .dropdown.dropdown-typical.open a.dropdown-toggle,
  .dark-theme .site-header.site-header-light .dropdown.dropdown-typical a.dropdown-toggle:hover {
    color: #fff;
  }
}
/* ==========================================================================
   STYLE: Dark Theme Blue
   ========================================================================== */
@media (min-width: 1055px) {
  .dark-theme.dark-theme-blue .site-header .dropdown.dropdown-typical.open a.dropdown-toggle,
  .dark-theme.dark-theme-blue .site-header .dropdown.dropdown-typical a.dropdown-toggle:hover {
    background-color: #00a8ff;
  }
  .dark-theme.dark-theme-blue .site-header .dropdown.dropdown-typical.open a.dropdown-toggle:before {
    border-top-color: #00a8ff;
  }
  .dark-theme.dark-theme-blue .site-header .dropdown .dropdown-menu .dropdown-item:hover,
  .dark-theme.dark-theme-blue .site-header .dropdown .dropdown-menu .dropdown-item:hover .font-icon,
  .dark-theme.dark-theme-blue .site-header .dropdown .dropdown-menu .dropdown-more:hover .dropdown-more-caption {
    color: #00a8ff;
  }
  .dark-theme.dark-theme-blue .site-header .dropdown .dropdown-menu .dropdown-more:hover .dropdown-more-caption:before {
    border-left-color: #00a8ff;
  }
}
/* ==========================================================================
   STYLE: Dark Theme Green
   ========================================================================== */
@media (min-width: 1055px) {
  .dark-theme.dark-theme-green .site-header .dropdown.dropdown-typical.open a.dropdown-toggle,
  .dark-theme.dark-theme-green .site-header .dropdown.dropdown-typical a.dropdown-toggle:hover {
    background-color: #46c35f;
  }
  .dark-theme.dark-theme-green .site-header .dropdown.dropdown-typical.open a.dropdown-toggle:before {
    border-top-color: #46c35f;
  }
  .dark-theme.dark-theme-green .site-header .dropdown .dropdown-menu .dropdown-item:hover,
  .dark-theme.dark-theme-green .site-header .dropdown .dropdown-menu .dropdown-item:hover .font-icon,
  .dark-theme.dark-theme-green .site-header .dropdown .dropdown-menu .dropdown-more:hover .dropdown-more-caption {
    color: #46c35f;
  }
  .dark-theme.dark-theme-green .site-header .dropdown .dropdown-menu .dropdown-more:hover .dropdown-more-caption:before {
    border-left-color: #46c35f;
  }
  .dark-theme.dark-theme-green .site-header .site-header-search button:hover,
  .dark-theme.dark-theme-green .site-header .site-header-search.closed:hover button,
  .dark-theme.dark-theme-green .site-header .header-alarm:hover,
  .dark-theme.dark-theme-green .site-header .dropdown.dropdown-notification.open .header-alarm {
    color: #46c35f;
  }
  .dark-theme.dark-theme-green .site-header .user-menu.dropdown.open .dropdown-toggle,
  .dark-theme.dark-theme-green .site-header .user-menu.dropdown .dropdown-toggle:hover {
    color: #46c35f;
  }
}
/* ==========================================================================
   STYLE: Dark Theme Ultramarine
   ========================================================================== */
@media (min-width: 1055px) {
  .dark-theme.dark-theme-ultramarine  .dropdown.dropdown-typical.open a.dropdown-toggle,
  .dark-theme.dark-theme-ultramarine  .dropdown.dropdown-typical a.dropdown-toggle:hover {
    background-color: #1a5bc3;
  }
  .dark-theme.dark-theme-ultramarine  .dropdown.dropdown-typical.open a.dropdown-toggle:before {
    border-top-color: #1a5bc3;
  }
  .dark-theme.dark-theme-ultramarine  .dropdown .dropdown-menu .dropdown-item:hover,
  .dark-theme.dark-theme-ultramarine  .dropdown .dropdown-menu .dropdown-item:hover .font-icon,
  .dark-theme.dark-theme-ultramarine  .dropdown .dropdown-menu .dropdown-more:hover .dropdown-more-caption {
    color: #1a5bc3;
  }
  .dark-theme.dark-theme-ultramarine  .dropdown .dropdown-menu .dropdown-more:hover .dropdown-more-caption:before {
    border-left-color: #1a5bc3;
  }
  .dark-theme.dark-theme-ultramarine  .site-header-search button:hover,
  .dark-theme.dark-theme-ultramarine  .site-header-search.closed:hover button,
  .dark-theme.dark-theme-ultramarine  .header-alarm:hover,
  .dark-theme.dark-theme-ultramarine  .dropdown.dropdown-notification.open .header-alarm {
    color: #1a5bc3;
  }
  .dark-theme.dark-theme-ultramarine  .user-menu.dropdown.open .dropdown-toggle,
  .dark-theme.dark-theme-ultramarine  .user-menu.dropdown .dropdown-toggle:hover {
    color: #1a5bc3;
  }
}
/* ==========================================================================
   STYLE: Theme Side (common styles)
   ========================================================================== */
@media (min-width: 1055px) {
  .theme-side-ebony-clay .site-header .site-logo,
  .theme-side-madison-caribbean .site-header .site-logo,
  .theme-side-caesium-dark-caribbean .site-header .site-logo,
  .theme-side-tin .site-header .site-logo,
  .theme-side-litmus-blue .site-header .site-logo,
  .theme-rebecca-purple .site-header .site-logo,
  .theme-picton-blue .site-header .site-logo,
  .theme-picton-blue-white-ebony .site-header .site-logo {
    height: 80px;
    width: 240px;
    padding: 20px 0 0;
    text-align: center;
    margin: -20px 0 0 -15px;
  }
  .theme-side-ebony-clay .site-header .site-logo img,
  .theme-side-madison-caribbean .site-header .site-logo img,
  .theme-side-caesium-dark-caribbean .site-header .site-logo img,
  .theme-side-tin .site-header .site-logo img,
  .theme-side-litmus-blue .site-header .site-logo img,
  .theme-rebecca-purple .site-header .site-logo img,
  .theme-picton-blue .site-header .site-logo img,
  .theme-picton-blue-white-ebony .site-header .site-logo img {
    float: none;
  }
  .theme-side-ebony-clay .site-header .site-header-content,
  .theme-side-madison-caribbean .site-header .site-header-content,
  .theme-side-caesium-dark-caribbean .site-header .site-header-content,
  .theme-side-tin .site-header .site-header-content,
  .theme-side-litmus-blue .site-header .site-header-content,
  .theme-rebecca-purple .site-header .site-header-content,
  .theme-picton-blue .site-header .site-header-content,
  .theme-picton-blue-white-ebony .site-header .site-header-content {
    margin-left: -240px;
  }
  .theme-side-ebony-clay .site-header .site-header-content-in,
  .theme-side-madison-caribbean .site-header .site-header-content-in,
  .theme-side-caesium-dark-caribbean .site-header .site-header-content-in,
  .theme-side-tin .site-header .site-header-content-in,
  .theme-side-litmus-blue .site-header .site-header-content-in,
  .theme-rebecca-purple .site-header .site-header-content-in,
  .theme-picton-blue .site-header .site-header-content-in,
  .theme-picton-blue-white-ebony .site-header .site-header-content-in {
    margin-left: 240px;
  }
  .theme-side-ebony-clay .site-header .site-logo {
    background-color: #212b30;
  }
  .theme-side-madison-caribbean .site-header .site-logo {
    background-color: #263543;
  }
  .theme-side-caesium-dark-caribbean .site-header .site-logo {
    background-color: #22222f;
  }
  .theme-side-tin .site-header .site-logo {
    background-color: #383838;
  }
  .theme-side-litmus-blue .site-header .site-logo {
    background-color: #282c38;
  }
}
.theme-rebecca-purple .site-header {
  background-color: #3e4eb2;
  border-bottom-color: #3e4eb2;
}
.theme-rebecca-purple .site-header .site-header-search.closed {
  background: none;
}
.theme-rebecca-purple .site-header .site-header-search.closed button {
  color: #fff;
}
.theme-rebecca-purple .site-header .site-header-search.closed:hover button {
  color: #00a8ff;
}
.theme-rebecca-purple .site-header .header-alarm.active:after {
  border-color: #3e4eb2;
  background-color: #fed832;
}
.theme-rebecca-purple .site-header .header-alarm i {
  color: #fff;
}
.theme-rebecca-purple .site-header .dropdown.user-menu .dropdown-toggle:after {
  color: #9fa7d8;
}
@media (min-width: 1055px) {
  .theme-rebecca-purple .site-header .dropdown.dropdown-typical .lbl {
    color: #fff;
  }
  .theme-rebecca-purple .site-header .dropdown.dropdown-typical a.dropdown-toggle:after {
    color: #9fa7d8;
  }
  .theme-rebecca-purple .site-header .dropdown.dropdown-typical a.dropdown-toggle .font-icon {
    color: #fff;
  }
  .theme-rebecca-purple .site-header .help-dropdown > button {
    color: #fff;
  }
}
.theme-picton-blue .site-header {
  background-color: #5fa7e7;
  border-bottom-color: #5fa7e7;
}
.theme-picton-blue .site-header .site-header-search.closed {
  background: none;
}
.theme-picton-blue .site-header .site-header-search.closed button {
  color: #fff;
}
.theme-picton-blue .site-header .site-header-search.closed:hover button {
  color: #fed832;
}
.theme-picton-blue .site-header .header-alarm.active:after {
  border-color: #5fa7e7;
  background-color: #fed832;
}
.theme-picton-blue .site-header .header-alarm i {
  color: #fff;
}
.theme-picton-blue .site-header .dropdown.user-menu .dropdown-toggle:after {
  color: #fff;
}
@media (min-width: 1055px) {
  .theme-picton-blue .site-header .dropdown.dropdown-typical .lbl {
    color: #fff;
  }
  .theme-picton-blue .site-header .dropdown.dropdown-typical a.dropdown-toggle:after,
  .theme-picton-blue .site-header .dropdown.dropdown-typical a.dropdown-toggle .font-icon {
    color: #fff;
  }
  .theme-picton-blue .site-header .help-dropdown > button {
    color: #fff;
  }
  .theme-picton-blue .site-header .dropdown > .btn {
    border-color: #fff !important;
    background-color: #fff;
    color: #00a8ff;
  }
}
.theme-picton-blue .site-header .hamburger span {
  background-color: #fff;
}
.theme-picton-blue .site-header .hamburger span:before,
.theme-picton-blue .site-header .hamburger span:after {
  background-color: #fff;
}
.theme-picton-blue .site-header .burger-right {
  color: #fff;
}
.theme-picton-blue-white-ebony .site-header .site-logo {
  background-color: #5fa7e7;
}
.theme-picton-blue-white-ebony .site-header .header-alarm i,
.theme-picton-blue-white-ebony .site-header .dropdown.user-menu .dropdown-toggle:after {
  color: #76838e;
}
@media (min-width: 1055px) {
  .theme-picton-blue-white-ebony .site-header .dropdown.dropdown-typical .lbl {
    color: #76838e;
  }
  .theme-picton-blue-white-ebony .site-header .dropdown.dropdown-typical a.dropdown-toggle:after,
  .theme-picton-blue-white-ebony .site-header .dropdown.dropdown-typical a.dropdown-toggle .font-icon {
    color: #76838e;
  }
  .theme-picton-blue-white-ebony .site-header .help-dropdown > button {
    color: #76838e;
  }
}
.theme-picton-blue-white-ebony .site-header .hamburger span {
  background-color: #76838e;
}
.theme-picton-blue-white-ebony .site-header .hamburger span:before,
.theme-picton-blue-white-ebony .site-header .hamburger span:after {
  background-color: #76838e;
}
.theme-picton-blue-white-ebony .site-header .burger-right {
  color: #76838e;
}
/* ==========================================================================
   Footer
   ========================================================================== */
html {
  position: relative;
  min-height: 100%;
}
