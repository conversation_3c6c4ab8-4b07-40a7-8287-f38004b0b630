<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20150824 at Sat Jan  9 01:08:53 2016
 By Everything Fonts
Copyright (c) <PERSON>, 2005. All rights reserved.
</metadata>
<defs>
<font id="Proxima_Nova_Light_Italic" horiz-adv-x="571" >
  <font-face 
    font-family="Proxima_Nova_Light_Italic"
    font-weight="300"
    font-style="italic"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 6 3 0 0 2 0 4"
    ascent="790"
    descent="-210"
    x-height="483"
    cap-height="667"
    bbox="-225 -259 1148 886"
    underline-thickness="20"
    underline-position="-153"
    slope="-12.4"
    unicode-range="U+000D-FB04"
  />
<missing-glyph horiz-adv-x="517" 
d="M454 -90h-351v842h351v-842zM422 -60v782h-288v-782h288z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="743" 
d="M361 0h-52l97 437h-80l10 46h80l9 44q33 150 150 150q45 0 76 -21l-22 -38q-19 14 -53 14q-40 0 -63 -26.5t-35 -78.5l-10 -44h98l-10 -46h-98zM456 -196q-72 0 -107 42l28 38q26 -35 72 -35q73 0 94 89l121 545h52l-121 -545q-15 -68 -48.5 -101t-90.5 -33zM710 559
q-15 0 -25.5 10.5t-10.5 24.5q0 19 14 31.5t31 12.5q15 0 25.5 -10t10.5 -25q0 -19 -14 -31.5t-31 -12.5zM97 0h-53l97 437h-80l10 46h80l9 44q35 150 151 150q58 0 95 -37l-30 -33q-23 25 -65 25q-40 0 -63 -26t-35 -79l-10 -44h99l-11 -46h-98z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="478" 
d="M96 0h-52l97 437h-80l10 46h80l9 44q33 150 150 150q44 0 76 -21l-22 -38q-19 14 -53 14q-40 0 -63 -26.5t-35 -78.5l-10 -44h98l-10 -46h-98zM191 -196q-73 0 -107 42l28 38q26 -35 72 -35q73 0 94 89l121 545h52l-121 -545q-15 -68 -48.5 -101t-90.5 -33zM445 559
q-15 0 -25.5 10.5t-10.5 24.5q0 19 14 31.5t31 12.5q16 0 26 -10t10 -25q0 -19 -14 -31.5t-31 -12.5z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="530" 
d="M97 0h-53l97 437h-80l10 46h80l9 44q35 150 151 150q58 0 95 -37l-30 -33q-23 25 -65 25q-40 0 -63 -26t-35 -79l-10 -44h99l-11 -46h-98zM362 0h-53l97 437h-80l10 46h80l9 44q35 150 151 150q58 0 95 -37l-30 -33q-23 25 -65 25q-40 0 -63 -26t-35 -79l-10 -44h99
l-11 -46h-98z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="478" 
d="M96 0h-52l97 437h-80l10 46h80l9 44q33 150 150 150q44 0 76 -21l-22 -38q-19 14 -53 14q-40 0 -63 -26.5t-35 -78.5l-10 -44h98l-10 -46h-98zM445 559q-15 0 -25.5 10.5t-10.5 24.5q0 19 14 31.5t31 12.5q16 0 26 -10t10 -25q0 -19 -14 -31.5t-31 -12.5zM344 0h-52
l107 483h52z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="478" 
d="M96 0h-52l97 437h-80l10 46h80l9 44q33 150 150 150q44 0 76 -21l-22 -38q-19 14 -53 14q-40 0 -63 -26.5t-35 -78.5l-10 -44h98l-10 -46h-98zM344 0h-52l147 667h52z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="743" 
d="M361 0h-52l97 437h-80l10 46h80l9 44q33 150 150 150q45 0 76 -21l-22 -38q-19 14 -53 14q-40 0 -63 -26.5t-35 -78.5l-10 -44h98l-10 -46h-98zM710 559q-15 0 -25.5 10.5t-10.5 24.5q0 19 14 31.5t31 12.5q15 0 25.5 -10t10.5 -25q0 -19 -14 -31.5t-31 -12.5zM609 0h-52
l107 483h52zM97 0h-53l97 437h-80l10 46h80l9 44q35 150 151 150q58 0 95 -37l-30 -33q-23 25 -65 25q-40 0 -63 -26t-35 -79l-10 -44h99l-11 -46h-98z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="743" 
d="M361 0h-52l97 437h-80l10 46h80l9 44q33 150 150 150q45 0 76 -21l-22 -38q-19 14 -53 14q-40 0 -63 -26.5t-35 -78.5l-10 -44h98l-10 -46h-98zM609 0h-52l147 667h52zM97 0h-53l97 437h-80l10 46h80l9 44q35 150 151 150q58 0 95 -37l-30 -33q-23 25 -65 25
q-40 0 -63 -26t-35 -79l-10 -44h99l-11 -46h-98z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="835" 
d="M96 0h-52l97 437h-80l10 46h80l9 44q33 150 150 150q44 0 76 -21l-22 -38q-19 14 -53 14q-40 0 -63 -26.5t-35 -78.5l-10 -44h98l-10 -46h-98zM536 -12q-58 0 -103 25.5t-71 68.5l-18 -82h-52l147 667h52l-56 -257q65 85 166 85q84 0 136 -53.5t52 -145.5
q0 -125 -71 -216.5t-182 -91.5zM531 35q88 0 144.5 76t56.5 176q0 74 -40 117.5t-106 43.5q-49 0 -93 -26.5t-71 -66.5l-51 -232q19 -38 61.5 -63t98.5 -25z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="805" 
d="M96 0h-52l97 437h-80l10 46h80l9 44q33 150 150 150q44 0 76 -21l-22 -38q-19 14 -53 14q-40 0 -63 -26.5t-35 -78.5l-10 -44h98l-10 -46h-98zM670 0h-52l72 328q6 27 6 35q0 43 -29.5 64t-78.5 21q-81 0 -164 -85l-80 -363h-52l147 667h52l-56 -257q87 85 174 85
q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="770" 
d="M96 0h-52l97 437h-80l10 46h80l9 44q33 150 150 150q44 0 76 -21l-22 -38q-19 14 -53 14q-40 0 -63 -26.5t-35 -78.5l-10 -44h98l-10 -46h-98zM697 0h-64l-145 233l-112 -88l-32 -145h-52l147 667h52l-101 -456l342 272h73l-274 -218z" />
    <glyph glyph-name=".notdef" horiz-adv-x="517" 
d="M454 -90h-351v842h351v-842zM422 -60v782h-288v-782h288z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="CR" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="259" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="219" 
d="M114 174h-41l96 493h67zM59 -10q-17 0 -28.5 12t-11.5 29q0 20 14 33.5t34 13.5q17 0 28.5 -12t11.5 -29q0 -19 -14 -33t-34 -14z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="317" 
d="M147 427h-29q23 192 25 206q2 20 13 32t29 12q13 0 22.5 -9.5t9.5 -23.5l-5 -20zM278 427h-29q23 192 25 206q2 20 13 32t29 12q14 0 23 -9.5t9 -23.5q0 -10 -5 -20z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="579" 
d="M230 0h-48l104 186h-118l-103 -186h-47l104 186h-111l21 41h112l119 213h-112l20 41h115l102 186h47l-102 -186h117l102 186h48l-104 -186h111l-20 -41h-113l-120 -213h116l-21 -41h-116zM307 227l120 213h-117l-119 -213h116z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="584" 
d="M196 -100l21 94q-65 10 -115.5 40t-75.5 71l42 41q57 -81 160 -101l62 277q-31 13 -50.5 22.5t-44.5 25.5t-39.5 32t-24.5 39.5t-10 50.5q0 76 65.5 131t159.5 55q15 0 23 -1l20 91h46l-22 -97q112 -22 163 -97l-43 -40q-45 65 -131 85l-56 -252q39 -16 64 -29.5
t52.5 -34.5t41 -49t13.5 -62q0 -81 -63 -142.5t-173 -61.5q-13 0 -20 1l-19 -89h-46zM454 185q0 41 -31 67.5t-91 52.5l-59 -265h13q82 0 125 44.5t43 100.5zM183 498q0 -37 30.5 -62t90.5 -51l53 240q-5 1 -16 1q-65 0 -111.5 -39t-46.5 -89z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="720" 
d="M119 0h-45l573 667h46zM494 -12q-62 0 -103.5 37.5t-41.5 96.5q0 84 52 139t123 55q62 0 103.5 -37.5t41.5 -96.5q0 -84 -52 -139t-123 -55zM495 27q51 0 89 43.5t38 107.5q0 42 -28 70.5t-71 28.5q-51 0 -89 -44t-38 -107q0 -43 28 -71t71 -28zM234 350q-61 0 -102.5 37
t-41.5 96q0 85 52 139.5t123 54.5q61 0 102.5 -37.5t41.5 -95.5q0 -85 -52 -139.5t-123 -54.5zM236 388q51 0 89 44t38 107q0 43 -28.5 71t-71.5 28q-51 0 -89 -43.5t-38 -107.5q0 -43 28.5 -71t71.5 -28z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="639" 
d="M526 0h-72q-12 15 -50 65q-90 -77 -189 -77q-84 0 -139 43t-55 120q0 47 15.5 84t46 63.5t61.5 44t75 36.5q-25 64 -25 119q0 73 53 126t123 53q60 0 98.5 -29.5t38.5 -83.5q0 -24 -7.5 -46t-16 -37.5t-29 -32t-33.5 -25.5t-42.5 -23t-42.5 -19.5t-47 -19.5
q24 -51 58 -105q24 -43 70 -113q70 75 113 157l43 -25q-69 -111 -128 -172q34 -46 81 -103zM220 34q74 0 156 70q-54 78 -76 118q-42 71 -64 117q-74 -34 -114.5 -74.5t-40.5 -106.5q0 -59 40 -91.5t99 -32.5zM250 499q0 -44 22 -99q45 17 70 29t54.5 31.5t43 43.5t13.5 55
q0 35 -23.5 54.5t-58.5 19.5q-45 0 -83 -36.5t-38 -97.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="187" 
d="M147 427h-29q23 192 25 206q2 20 13 32t29 12q13 0 22.5 -9.5t9.5 -23.5l-5 -20z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="227" 
d="M117 -179l-36 -20q-56 132 -56 283q0 316 251 601l26 -26q-221 -298 -221 -599q0 -104 36 -239z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="227" 
d="M109 665l35 20q57 -130 57 -283q0 -316 -251 -601l-26 26q221 297 221 599q0 104 -36 239z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="335" 
d="M220 399h-39l33 119l-112 -52l-11 34l115 41l-91 63l25 30l84 -73l20 116h38l-32 -118l112 51l11 -34l-115 -41l91 -63l-26 -30l-83 73z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="496" 
d="M483 316h-197l-48 -217h-44l48 217h-197l9 42h197l46 211h45l-46 -211h197z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="219" 
d="M109 30q0 -41 -30.5 -83t-72.5 -64l-24 27q28 14 49.5 37.5t28.5 46.5h-7q-14 0 -24 10.5t-10 25.5q0 20 14 34t33 14t31 -12.5t12 -35.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="300" 
d="M264 218h-240l10 48h240z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="219" 
d="M59 -10q-17 0 -28.5 12t-11.5 29q0 20 14 33.5t34 13.5q17 0 28.5 -12t11.5 -29q0 -19 -14 -33t-34 -14z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="282" 
d="M-13 -20h-46l393 707h47z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="608" 
d="M275 -12q-101 0 -155.5 69t-54.5 187q0 101 35.5 198.5t108 166t164.5 68.5q101 0 154.5 -68.5t53.5 -186.5q0 -101 -35 -198.5t-107 -166.5t-164 -69zM280 40q54 0 101 36.5t77 94t47 124.5t17 131q0 91 -37.5 145t-117.5 54q-54 0 -101 -36.5t-77 -93.5t-47 -124
t-17 -131q0 -91 37.5 -145.5t117.5 -54.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="308" 
d="M171 0h-57l129 589l-131 -114l-27 37l181 155h52z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="582" 
d="M442 0h-440l14 60q94 60 152 98.5t126.5 90.5t106.5 91t64 84t26 85q0 54 -42 85t-111 31q-106 0 -173 -67l-31 40q36 36 91 57.5t115 21.5q89 0 151 -42.5t62 -123.5q0 -46 -26 -95.5t-64.5 -92t-103.5 -93.5t-120 -88.5t-137 -89.5h351z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="544" 
d="M240 -12q-81 0 -148.5 39t-90.5 100l46 30q22 -54 75 -85.5t118 -31.5q80 0 128 45t48 115q0 53 -43.5 85t-115.5 32q-46 0 -57 -1l13 54q9 -1 89 -1q72 0 122.5 35.5t50.5 101.5q0 51 -44.5 85t-117.5 34q-95 0 -171 -70l-28 40q38 38 92 60t110 22q96 0 156.5 -44
t60.5 -122q0 -74 -55.5 -121t-125.5 -52q52 -12 86.5 -49t34.5 -92q0 -87 -65 -148t-168 -61z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="541" 
d="M354 0h-57l40 182h-316l13 55l386 430h81l-96 -433h98l-11 -52h-98zM348 234l83 376l-340 -376h257z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="582" 
d="M271 -12q-166 0 -229 135l46 33q51 -116 188 -116q76 0 130 52.5t54 129.5q0 67 -45 104.5t-116 37.5q-77 0 -146 -51l-38 23l73 331h385l-11 -52h-328l-55 -247q60 46 143 46t140.5 -49.5t57.5 -137.5q0 -98 -70.5 -168.5t-178.5 -70.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="584" 
d="M275 -12q-103 0 -158.5 62.5t-55.5 177.5q0 55 11 114t36.5 119.5t61 108t89.5 77.5t118 30q138 0 192 -104l-43 -37q-40 89 -152 89q-51 0 -94 -25.5t-71.5 -69t-47.5 -90.5t-31 -102q-1 -3 -5 -19q32 35 87 64t113 29q89 0 144 -46.5t55 -126.5q0 -102 -72 -176.5
t-177 -74.5zM277 40q79 0 133 57.5t54 133.5q0 60 -43 95.5t-114 35.5q-103 0 -189 -96q-2 -18 -2 -53q0 -78 42 -125.5t119 -47.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="502" 
d="M133 0h-66l414 615h-366l11 52h437l-9 -40z" />
    <glyph glyph-name="eight" unicode="8" 
d="M266 -12q-101 0 -165 44t-64 123q0 76 57.5 127.5t151.5 68.5q-51 19 -86.5 56t-35.5 87q0 87 68.5 135t156.5 48q89 0 152 -43.5t63 -117.5q0 -73 -54 -119.5t-142 -58.5q52 -22 91.5 -62t39.5 -98q0 -82 -68 -136t-165 -54zM313 368q86 7 138 43.5t52 98.5
q0 48 -45.5 81t-110.5 33q-68 0 -116.5 -36t-48.5 -97q0 -43 41.5 -77.5t89.5 -45.5zM267 40q68 0 121 39t53 103q0 50 -44 86.5t-95 50.5q-89 -6 -147 -48.5t-58 -106.5q0 -59 49 -91.5t121 -32.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="584" 
d="M347 678q103 0 158.5 -62.5t55.5 -177.5q0 -55 -11 -114.5t-36 -119.5t-61 -107.5t-90 -77.5t-118 -30q-138 0 -191 104l42 37q40 -89 153 -89q51 0 93.5 25.5t71 69t47.5 90.5t32 102q1 3 2 7.5t1.5 7t0.5 3.5q-32 -35 -87 -63.5t-113 -28.5q-89 0 -144 46.5t-55 126.5
q0 102 72 176.5t177 74.5zM345 626q-79 0 -133 -57.5t-54 -133.5q0 -60 43 -95.5t114 -35.5q105 0 189 96q2 18 2 53q0 78 -42 125.5t-119 47.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="219" 
d="M59 -10q-17 0 -28.5 12t-11.5 29q0 20 14 33.5t34 13.5q17 0 28.5 -12t11.5 -29q0 -19 -14 -33t-34 -14zM150 403q-17 0 -28.5 11.5t-11.5 28.5q0 19 14 33t34 14q17 0 28.5 -11.5t11.5 -28.5q0 -20 -14 -33.5t-34 -13.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="219" 
d="M109 30q0 -41 -30.5 -83t-72.5 -64l-24 27q28 14 49.5 37.5t28.5 46.5h-7q-14 0 -24 10.5t-10 25.5q0 20 14 34t33 14t31 -12.5t12 -35.5zM150 403q-17 0 -28.5 11.5t-11.5 28.5q0 19 14 33t34 14q17 0 28.5 -11.5t11.5 -28.5q0 -20 -14 -33.5t-34 -13.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="496" 
d="M432 90l-388 226l9 37l488 227l-12 -52l-429 -197l342 -194z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="496" 
d="M504 411h-438l9 42h438zM461 216h-439l10 41h438z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="496" 
d="M483 316l-489 -226l12 52l430 194l-343 197l10 47l388 -227z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="464" 
d="M244 201l-45 -25q-26 33 -26 72q0 40 26.5 69.5t63.5 49t74.5 38.5t64 49t26.5 70q0 46 -37 73.5t-101 27.5q-98 0 -168 -72l-28 39q86 85 200 85q88 0 142.5 -40.5t54.5 -105.5q0 -49 -27.5 -86t-66.5 -59t-78 -41t-66.5 -44t-27.5 -56q0 -25 19 -44zM172 -10
q-17 0 -28.5 12t-11.5 29q0 19 14 33t34 14q17 0 28.5 -12t11.5 -29q0 -20 -14 -33.5t-34 -13.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="783" 
d="M372 -70q-136 0 -226.5 90.5t-90.5 223.5q0 106 55.5 197.5t147 144t194.5 52.5q139 0 227 -92.5t88 -229.5q0 -108 -48 -169.5t-114 -61.5q-39 0 -63 25t-25 60l-1 6q-28 -39 -72 -65t-92 -26q-69 0 -111 43.5t-42 116.5q0 104 73.5 179t165.5 75q47 0 81 -23t49 -59
l13 65h49l-59 -282q-2 -12 -2 -19q0 -25 14.5 -40t35.5 -15q40 0 78 46.5t38 144.5q0 125 -80 207t-206 82q-145 0 -253.5 -109.5t-108.5 -251.5q0 -121 82 -202.5t207 -81.5q99 0 187 57l17 -25q-98 -63 -208 -63zM362 126q95 0 164 101l32 148q-11 32 -40 57.5t-74 25.5
q-79 0 -136 -64.5t-57 -145.5q0 -54 29.5 -88t81.5 -34z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="614" 
d="M314 0h-285l147 667h257q69 0 117 -42t48 -109q0 -70 -45.5 -121t-109.5 -59q42 -10 69.5 -50t27.5 -88q0 -82 -58.5 -140t-167.5 -58zM386 368q77 0 113 42t36 100q0 45 -33.5 75t-81.5 30h-198l-54 -247h218zM317 52q76 0 120 44.5t44 104.5q0 50 -33 82.5t-89 32.5
h-203l-59 -264h220z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="672" 
d="M367 -12q-132 0 -216 79.5t-84 209.5q0 88 31.5 164.5t83.5 127.5t118.5 80t136.5 29q88 0 156 -37t106 -106l-55 -25q-64 116 -211 116q-120 0 -212 -97t-92 -250q0 -108 65.5 -173.5t175.5 -65.5q102 0 188 76l41 -34q-98 -94 -232 -94z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="692" 
d="M241 0h-212l147 667h208q113 0 192 -81t79 -203q0 -56 -16 -110.5t-50 -104t-81.5 -87t-116.5 -59.5t-150 -22zM246 52h5q154 0 249 96t95 233q0 101 -63.5 167.5t-158.5 66.5h-151l-125 -563h149z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="564" 
d="M452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="542" 
d="M86 0h-57l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="711" 
d="M367 -12q-135 0 -217.5 81t-82.5 208q0 172 109.5 286.5t263.5 114.5q87 0 153 -36.5t104 -100.5l-51 -26q-30 54 -86.5 82.5t-126.5 28.5q-121 0 -212.5 -99.5t-91.5 -247.5q0 -109 65.5 -174t175.5 -65q108 0 194 80l35 160h-252l11 51h310l-53 -238
q-108 -105 -248 -105z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="703" 
d="M567 0h-58l70 317h-423l-70 -317h-57l147 667h57l-65 -298h422l66 298h58z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="223" 
d="M86 0h-57l147 667h57z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="470" 
d="M156 -12q-58 0 -104.5 22.5t-70.5 65.5l38 40q41 -76 134 -76q66 0 107.5 41t57.5 112l104 474h58l-104 -474q-46 -205 -220 -205z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="586" 
d="M508 0h-68l-208 318l-94 -85l-52 -233h-57l147 667h57l-79 -358l396 358h78l-354 -318z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="517" 
d="M409 0h-380l147 667h57l-136 -615h323z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="790" 
d="M653 0h-58l131 595l-374 -595h-22l-112 595l-132 -595h-57l147 667h82l104 -556l350 556h88z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="699" 
d="M562 0h-56l-291 582l-129 -582h-57l147 667h58l290 -573l128 573h57z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="P" unicode="P" 
d="M86 0h-57l147 667h234q77 0 126 -49t49 -118q0 -40 -14 -78.5t-42.5 -71.5t-78 -53t-112.5 -20h-190zM159 329h186q85 0 132 48.5t47 118.5q0 48 -35.5 83.5t-87.5 35.5h-179z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -101 -41.5 -186.5t-112.5 -140.5l45 -60l-46 -29l-44 59q-80 -44 -170 -44zM370 40q70 0 134 36l-86 117l46 29l85 -116q58 48 91.5 120.5t33.5 160.5q0 113 -69 176t-173 63
q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="594" 
d="M489 0h-65l-126 276h-151l-61 -276h-57l147 667h229q72 0 126.5 -46t54.5 -122q0 -94 -62.5 -155.5t-165.5 -65.5zM343 328h2q84 0 131.5 48.5t47.5 119.5q0 51 -38 85t-88 34h-176l-63 -287h184z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="581" 
d="M279 -12q-84 0 -153.5 33t-101.5 84l42 41q75 -106 218 -106q82 0 125 44.5t43 100.5q0 40 -34.5 69.5t-83 49.5t-97.5 41.5t-83.5 58.5t-34.5 88q0 76 65.5 131t159.5 55q75 0 137 -28.5t93 -75.5l-43 -40q-30 45 -81.5 68.5t-110.5 23.5q-65 0 -111.5 -39t-46.5 -89
q0 -37 34.5 -64.5t83.5 -47.5t98 -42t83.5 -60.5t34.5 -91.5q0 -81 -63 -142.5t-173 -61.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="564" 
d="M257 0h-58l136 615h-218l11 52h494l-11 -52h-218z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="686" 
d="M327 -12q-114 0 -180 58.5t-66 158.5q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-30 -132 -93.5 -200t-185.5 -68z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="647" 
d="M305 0h-71l-126 667h63l109 -603l376 603h69z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="867" 
d="M604 0h-64l-34 573l-286 -573h-64l-44 667h63l30 -587l294 587h52l34 -587l290 587h66z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="645" 
d="M572 0h-65l-170 302l-295 -302h-78l343 346l-181 321h67l157 -283l276 283h78l-323 -328z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="618" 
d="M284 0h-58l63 285l-181 382h64l153 -328l297 328h74l-349 -382z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="581" 
d="M477 0h-481l11 50l522 565h-396l10 52h474l-10 -49l-523 -566h404z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="228" 
d="M114 -190h-173l192 868h174l-10 -43h-130l-173 -782h129z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="282" 
d="M178 -20l-80 707h45l80 -707h-45z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="228" 
d="M94 -190h-173l10 43h128l174 782h-130l10 43h174z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="428" 
d="M428 333h-45l-83 293l-212 -293h-50l248 334h42z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="564" 
d="M494 -83h-569l10 43h569z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="216" 
d="M285 556h-44l-140 144h62z" />
    <glyph glyph-name="a" unicode="a" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
" />
    <glyph glyph-name="b" unicode="b" 
d="M271 -12q-58 0 -103 25.5t-71 68.5l-18 -82h-52l147 667h52l-56 -257q65 85 166 85q84 0 136 -53.5t52 -145.5q0 -125 -71 -216.5t-182 -91.5zM266 35q88 0 144.5 76t56.5 176q0 74 -40 117.5t-106 43.5q-49 0 -93 -26.5t-71 -66.5l-51 -232q19 -38 61.5 -63t98.5 -25z
" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="494" 
d="M261 -12q-96 0 -155.5 57t-59.5 157q0 120 78 206.5t190 86.5q121 0 173 -88l-40 -30q-41 71 -131 71q-96 0 -155.5 -73.5t-59.5 -173.5q0 -80 46 -123t119 -43q79 0 131 62l30 -36q-70 -73 -166 -73z" />
    <glyph glyph-name="d" unicode="d" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l58 266h53l-148 -667h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="567" 
d="M276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5
t-70.5 -129.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="265" 
d="M97 0h-53l97 437h-80l10 46h80l9 44q35 150 151 150q58 0 95 -37l-30 -33q-23 25 -65 25q-40 0 -63 -26t-35 -79l-10 -44h99l-11 -46h-98z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="570" 
d="M212 -196q-72 0 -122.5 18t-85.5 64l37 37q48 -74 170 -74q138 0 173 156l15 72q-66 -85 -171 -85q-81 0 -132 51.5t-51 149.5q0 122 69.5 212t181.5 90q51 0 99.5 -26.5t75.5 -67.5l19 82h53l-107 -478q-43 -201 -224 -201zM247 38q44 0 88.5 25t74.5 62l53 234
q-22 40 -67.5 64.5t-97.5 24.5q-87 0 -142.5 -76t-55.5 -172q0 -75 40 -118.5t107 -43.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="540" 
d="M405 0h-52l72 328q6 27 6 35q0 43 -29.5 64t-78.5 21q-81 0 -164 -85l-80 -363h-52l147 667h52l-56 -257q87 85 174 85q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="214" 
d="M180 559q-15 0 -25.5 10.5t-10.5 24.5q0 19 14 31.5t31 12.5q15 0 25.5 -10t10.5 -25q0 -19 -14 -31.5t-31 -12.5zM79 0h-52l107 483h52z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="214" 
d="M-74 -196q-72 0 -107 42l28 38q26 -35 72 -35q73 0 94 89l121 545h52l-121 -545q-15 -68 -48.5 -101t-90.5 -33zM180 559q-15 0 -25.5 10.5t-10.5 24.5q0 19 14 31.5t31 12.5q15 0 25.5 -10t10.5 -25q0 -19 -14 -31.5t-31 -12.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="505" 
d="M432 0h-64l-145 233l-112 -88l-32 -145h-52l147 667h52l-101 -456l342 272h73l-274 -218z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="214" 
d="M79 0h-52l147 667h52z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="789" 
d="M654 0h-52l74 336q4 16 4 29q0 38 -25.5 60.5t-62.5 22.5q-36 0 -75.5 -24t-69.5 -60l-80 -364h-53l75 336q4 30 4 35q-1 34 -24.5 55.5t-65.5 21.5q-70 0 -144 -84l-80 -364h-52l107 483h52l-16 -73q74 85 154 85q55 0 88 -31t33 -62v-2q75 95 167 95q51 0 86.5 -31.5
t35.5 -86.5q0 -13 -6 -41z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="540" 
d="M405 0h-52l72 328q6 27 6 35q0 43 -29 64t-76 21q-85 0 -167 -84l-80 -364h-52l107 483h52l-16 -73q87 85 174 85q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41z" />
    <glyph glyph-name="o" unicode="o" 
d="M258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="567" 
d="M271 -12q-58 0 -103 25.5t-71 68.5l-59 -266h-52l148 667h52l-16 -73q65 85 166 85q84 0 136 -53.5t52 -145.5q0 -125 -71 -216.5t-182 -91.5zM266 35q88 0 144.5 76t56.5 176q0 74 -40 117.5t-106 43.5q-49 0 -93 -26.5t-71 -66.5l-51 -232q19 -38 61.5 -63t98.5 -25z
" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="567" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-148 -667h-53l58 257q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="317" 
d="M79 0h-52l107 483h52l-18 -78q40 46 80.5 67t102.5 21l-12 -56q-18 3 -35 3q-42 0 -81.5 -26t-65.5 -62z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="459" 
d="M202 -12q-133 0 -194 85l37 36q20 -32 62.5 -54.5t95.5 -22.5q58 0 93 29.5t35 70.5q0 28 -26.5 49.5t-64.5 36t-76 30t-64.5 42t-26.5 62.5q0 61 47.5 102t128.5 41q59 0 107 -23t69 -56l-34 -34q-16 29 -57.5 49t-88.5 20q-54 0 -86.5 -24.5t-32.5 -62.5
q0 -27 26.5 -47t64.5 -34t76 -30t64.5 -44t26.5 -66q0 -64 -49 -109.5t-133 -45.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="275" 
d="M151 -12q-96 0 -96 81q0 11 3 26l76 342h-80l10 46h80l29 132h53l-29 -132h98l-11 -46h-98l-74 -335q-2 -10 -2 -21q0 -46 49 -46q27 0 51 18l10 -42q-30 -23 -69 -23z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="540" 
d="M134 483h52l-72 -327q-6 -30 -6 -36q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="481" 
d="M216 0h-59l-97 483h56l81 -422l269 422h61z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="719" 
d="M491 0h-50l-44 413l-227 -413h-50l-50 483h53l37 -410l228 410h44l45 -410l220 410h58z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="479" 
d="M408 0h-59l-113 213l-207 -213h-66l245 248l-127 235h59l105 -199l193 199h66l-233 -235z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="481" 
d="M-46 -186l20 50q18 -9 47 -9q39 0 76 55l57 87l-94 486h56l81 -422l269 422h61l-381 -589q-57 -90 -135 -90q-32 0 -57 10z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="469" 
d="M359 0h-357l8 43l371 394h-282l10 46h353l-9 -42l-373 -395h288z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="249" 
d="M135 -190h-49q-40 0 -70.5 27t-30.5 70q0 15 3 26l50 220q2 12 2 17q0 23 -10.5 38.5t-28.5 15.5l9 40q53 0 68 71l49 219q14 61 50.5 92.5t88.5 31.5h62l-10 -43h-62q-33 0 -55 -21t-30 -60l-51 -227q-15 -69 -64 -87q29 -15 29 -54q0 -18 -4 -34l-48 -218
q-2 -12 -2 -22q0 -25 16.5 -42t41.5 -17h56z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="208" 
d="M146 -20h-43v707h43v-707z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="249" 
d="M113 678h49q40 0 70 -27t30 -70q0 -18 -3 -27l-49 -219q-2 -12 -2 -18q0 -22 10.5 -37.5t28.5 -15.5l-10 -40q-52 0 -67 -71l-49 -219q-14 -61 -50 -92.5t-89 -31.5h-62l10 43h61q68 0 86 80l51 228q15 69 64 88q-29 14 -29 53q0 18 4 34l48 218q2 12 2 21q0 26 -16.5 43
t-41.5 17h-56z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="499" 
d="M522 667l42 -5q-54 -234 -174 -234q-45 0 -66.5 30.5t-24 67.5t-15.5 67.5t-44 30.5q-39 0 -73 -52t-56 -145l-42 6q24 103 66.5 167.5t106.5 64.5q37 0 58 -20.5t27 -49.5t10.5 -57.5t17 -49t38.5 -20.5q39 0 72.5 52.5t56.5 146.5z" />
    <glyph glyph-name="nbspace" unicode="&#xa0;" horiz-adv-x="259" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="219" 
d="M103 309h41l-96 -493h-68zM158 493q17 0 28.5 -12t11.5 -29q0 -19 -14 -33t-34 -14q-17 0 -28.5 11.5t-11.5 28.5q0 20 14 34t34 14z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="494" 
d="M180 -100l21 95q-71 15 -113 69t-42 138q0 120 77 206t189 87l16 70h44l-17 -73q90 -13 132 -85l-40 -30q-33 58 -102 69l-91 -411h12q79 0 131 62l30 -36q-70 -73 -166 -73q-12 0 -17 1l-20 -89h-44zM101 201q0 -64 30 -105t81 -55l90 406q-90 -6 -145.5 -78t-55.5 -168
z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="505" 
d="M23 267l9 42h132q-5 10 -26 41.5t-30.5 56.5t-9.5 55q0 84 69 149.5t166 65.5q67 0 122.5 -32t70.5 -81l-53 -24q-10 38 -48.5 62.5t-89.5 24.5q-72 0 -123.5 -46t-51.5 -121q0 -30 10 -55t28.5 -53.5t25.5 -42.5h147l-9 -42h-126q2 -14 2 -20q0 -50 -36 -97t-81 -75
q27 8 48 8q34 0 86 -22t81 -22q53 0 96 41l16 -47q-56 -47 -114 -47q-46 0 -103.5 23.5t-101.5 23.5q-46 0 -116 -40l-12 48q77 30 129.5 86t52.5 113q0 14 -3 27h-157z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="588" 
d="M297 98q-97 0 -158 57l-102 -82l-30 39l100 81q-28 45 -28 107q0 116 81 194l-57 74l38 31l57 -74q63 41 140 41q97 0 157 -57l102 84l31 -38l-103 -84q29 -47 29 -107q0 -115 -80 -193l56 -71l-39 -30l-55 70q-62 -42 -139 -42zM136 305q0 -74 45.5 -116t118.5 -42
q84 0 141.5 64t57.5 147q0 67 -43 113t-122 46q-86 0 -142 -64t-56 -148z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="618" 
d="M287 0h-58l28 126h-262l9 41h262l26 118h-261l8 42h233l-161 340h64l153 -328l297 328h74l-311 -340h227l-9 -42h-256l-26 -118h255l-9 -41h-255z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="208" 
d="M146 -20h-43v316h43v-316zM146 371h-43v316h43v-316z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="461" 
d="M423 317q0 -47 -32 -85.5t-96 -55.5q73 -38 73 -105q0 -61 -49 -106.5t-131 -45.5q-137 0 -204 80l38 33q23 -34 67.5 -53t97.5 -19q56 0 91.5 30t35.5 75q0 30 -26.5 51t-63.5 35t-74.5 29.5t-64 43.5t-26.5 67q0 50 40 87.5t113 46.5q-99 38 -99 112q0 55 46.5 97.5
t125.5 42.5q127 0 189 -75l-36 -30q-47 64 -150 64q-53 0 -88 -26.5t-35 -64.5q0 -30 26.5 -51t64.5 -34.5t76 -28.5t64.5 -44t26.5 -70zM369 307q0 36 -27 57t-78 39q-76 -9 -114 -37.5t-38 -66.5q0 -37 31.5 -57.5t105.5 -46.5q62 18 91 48t29 64z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="262" 
d="M349 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM145 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="778" 
d="M754 334q0 -143 -101.5 -244t-244.5 -101q-142 0 -243 101t-101 244t100.5 244t243.5 101t244.5 -101t101.5 -244zM726 334q0 131 -93.5 224t-224.5 93t-223.5 -93t-92.5 -224t93 -224t223 -93q131 0 224.5 93t93.5 224zM505 197l17 -25q-62 -52 -133 -52
q-88 0 -140 53.5t-52 133.5q0 101 72 171.5t164 70.5q108 0 155 -80l-27 -20q-37 70 -128 70q-77 0 -140 -61.5t-63 -150.5q0 -66 44.5 -111.5t114.5 -45.5q67 0 116 47z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="409" 
d="M267 613q-55 0 -93 -46.5t-38 -110.5q0 -49 26 -76t68 -27q59 0 108 57l34 152q-13 23 -41.5 37t-63.5 14zM389 640h43l-70 -314h-43l11 50q-47 -58 -113 -58q-53 0 -89.5 35t-36.5 100q0 78 49 136.5t121 58.5q80 0 117 -57z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="402" 
d="M202 63h-53l-120 180l200 177h61l-204 -182zM327 63h-53l-120 180l200 177h61l-204 -182z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="499" 
d="M513 453l-52 -237h-44l44 195h-395l9 42h438z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="300" 
d="M264 218h-240l10 48h240z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="494" 
d="M508 465q0 -88 -62.5 -150t-150.5 -62t-149.5 62t-61.5 150t62 150t150 62t150 -61.5t62 -150.5zM481 465q0 77 -54.5 131t-131.5 54t-130.5 -54t-53.5 -131q0 -76 54 -130.5t130 -54.5t131 54.5t55 130.5zM366 343h-36l-42 96h-48l-21 -96h-30l53 243h91q28 0 48.5 -17
t20.5 -47q0 -35 -25 -58.5t-55 -23.5zM370 518q0 19 -12 29.5t-30 10.5h-62l-20 -90h69q23 0 39 14.5t16 35.5z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="363" 
d="M436 577h-362l8 37h362z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="282" 
d="M327 560q0 -48 -34.5 -81.5t-82.5 -33.5t-82 33.5t-34 81.5t34 82.5t82 34.5t82.5 -34.5t34.5 -82.5zM288 560q0 32 -23 55t-55 23q-31 0 -53.5 -23t-22.5 -55t22 -54t53 -22q32 0 55.5 22.5t23.5 53.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="496" 
d="M484 323h-196l-49 -218h-44l48 218h-197l9 41h197l47 212h44l-46 -212h197zM413 0h-438l8 41h439z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="384" 
d="M372 421h-281l9 39q63 38 96 59t76.5 51.5t65 52t37 47.5t15.5 50q0 34 -27 51.5t-64 17.5q-71 0 -113 -47l-23 30q53 55 136 55q55 0 96 -26t41 -79q0 -28 -16 -57t-37.5 -52.5t-62 -53t-71 -49.5t-82.5 -51h213z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="384" 
d="M399 536q0 -48 -39.5 -85t-103.5 -37q-110 0 -152 72l32 25q38 -59 119 -59q45 0 71.5 25.5t26.5 64.5q0 29 -29 47.5t-75 18.5q-22 0 -28 -1l8 39q30 -2 59 -2q45 0 72.5 20.5t27.5 56.5q0 31 -28.5 49.5t-69.5 18.5q-59 0 -109 -44l-20 29q59 53 131 53q60 0 100.5 -26
t40.5 -74q0 -45 -31.5 -73t-80.5 -32q31 -6 54.5 -29.5t23.5 -56.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="216" 
d="M317 700l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="553" 
d="M88 44l-51 -228h-53l151 667h53l-74 -327q-14 -61 12.5 -92t77.5 -31q44 0 93 24t81 58l81 368h53l-85 -384q-15 -64 34 -64q13 0 21 2l-6 -46q-12 -3 -32 -3q-83 0 -73 86q-90 -86 -182 -86q-75 0 -101 56z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="449" 
d="M291 -100h-38l162 730h-93l-162 -730h-38l94 423q-57 0 -97 44.5t-40 111.5q0 73 56.5 130.5t137.5 57.5h188z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="219" 
d="M106 201q-17 0 -29 11.5t-12 28.5q0 20 14.5 33.5t34.5 13.5q17 0 28.5 -11.5t11.5 -28.5q0 -19 -14 -33t-34 -14z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="198" 
d="M15 -189q-65 0 -104 37l23 30q33 -34 83 -34q32 0 49 14.5t17 36.5q0 33 -40 33q-23 0 -37 -17l-24 16l49 84h38l-41 -68q15 12 34 12q26 0 42 -15t16 -42q0 -38 -29 -62.5t-76 -24.5z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="214" 
d="M193 421h-46l74 337l-77 -66l-22 30l119 99h40z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="405" 
d="M237 318q-66 0 -105.5 39t-39.5 102q0 77 52 133t127 56q66 0 105.5 -39t39.5 -102q0 -77 -52 -133t-127 -56zM237 355q59 0 97 45.5t38 106.5q0 45 -28.5 74t-73.5 29q-59 0 -96 -44.5t-37 -106.5q0 -46 27.5 -75t72.5 -29z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="402" 
d="M77 420h53l120 -180l-200 -177h-61l204 182zM202 420h53l120 -180l-200 -177h-61l204 182z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="742" 
d="M159 267h-46l74 337l-77 -66l-22 30l119 99h40zM671 107h-60l-24 -107h-44l23 107h-196l7 35l237 258h61l-57 -256h60zM574 144l47 211l-196 -211h149zM661 667l-574 -667h-45l573 667h46z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="781" 
d="M159 267h-46l74 337l-77 -66l-22 30l119 99h40zM676 0h-281l9 39q63 38 96 59t76.5 51.5t65 52t37 47.5t15.5 50q0 34 -27 51.5t-64 17.5q-71 0 -113 -47l-23 30q53 55 136 55q55 0 96 -26t41 -79q0 -28 -16 -57t-37.5 -52.5t-62 -53t-71 -49.5t-82.5 -51h213zM661 667
l-574 -667h-45l573 667h46z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="859" 
d="M788 107h-60l-24 -107h-44l23 107h-196l7 35l237 258h61l-57 -256h60zM691 144l47 211l-196 -211h149zM778 667l-574 -667h-45l573 667h46zM365 382q0 -48 -39.5 -85t-103.5 -37q-110 0 -152 72l32 25q38 -59 119 -59q45 0 71.5 25.5t26.5 64.5q0 29 -29 47.5t-75 18.5
q-22 0 -28 -1l8 39q30 -2 59 -2q45 0 72.5 20.5t27.5 56.5q0 31 -28.5 49.5t-69.5 18.5q-59 0 -109 -44l-20 29q59 53 131 53q60 0 100.5 -26t40.5 -74q0 -45 -31.5 -73t-80.5 -32q31 -6 54.5 -29.5t23.5 -56.5z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="393" 
d="M218 282l46 24q26 -32 26 -71q0 -40 -26.5 -69.5t-64 -49l-75 -39t-64 -49.5t-26.5 -70q0 -45 37 -72.5t102 -27.5q98 0 167 71l28 -38q-85 -85 -200 -85q-88 0 -142.5 40t-54.5 105q0 50 27.5 87t67 59t78.5 41t66.5 43.5t27.5 55.5q0 25 -20 45zM290 492
q17 0 28.5 -11.5t11.5 -28.5q0 -20 -14 -33.5t-34 -13.5q-16 0 -28 11.5t-12 28.5q0 20 14 33.5t34 13.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM483 723h-44l-140 144h62z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM622 867l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="647" 
d="M555 723h-38l-62 113l-109 -113h-41l129 144h54zM578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM499 721q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z
" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="647" 
d="M578 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM374 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM578 0h-63l-31 164h-352
l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM434 691q-38 0 -62.5 24.5t-24.5 62.5q0 42 31.5 73.5t73.5 31.5q38 0 62.5 -24.5t24.5 -62.5q0 -43 -31 -74t-74 -31zM435 723q29 0 50.5 20.5t21.5 49.5q0 26 -15.5 42t-40.5 16
q-29 0 -50 -21t-21 -49q0 -26 15 -42t40 -16z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="939" 
d="M825 0h-423l36 164h-271l-138 -164h-69l568 667h444l-11 -52h-365l-55 -247h358l-12 -52h-358l-58 -264h365zM450 216l85 385l-327 -385h242z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="672" 
d="M308 -189q-65 0 -104 37l23 30q33 -34 83 -34q32 0 49 14.5t17 36.5q0 33 -40 33q-23 0 -37 -17l-24 16l38 65q-111 16 -178.5 92.5t-67.5 192.5q0 88 31.5 164.5t83.5 127.5t118.5 80t136.5 29q88 0 156 -37t106 -106l-55 -25q-64 116 -211 116q-120 0 -212 -97
t-92 -250q0 -108 65.5 -173.5t175.5 -65.5q102 0 188 76l41 -34q-98 -94 -232 -94q-13 0 -19 1l-27 -46q15 12 34 12q26 0 42 -15t16 -42q0 -38 -29 -62.5t-76 -24.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="564" 
d="M452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365zM455 723h-44l-140 144h62z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="564" 
d="M593 867l-204 -144h-46l186 144h64zM452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="564" 
d="M524 723h-38l-62 113l-109 -113h-41l129 144h54zM452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="564" 
d="M549 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM345 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM452 0h-423l147 667h423
l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="223" 
d="M86 0h-57l147 667h57zM272 723h-44l-140 144h62z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="223" 
d="M86 0h-57l147 667h57zM412 867l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="223" 
d="M343 723h-38l-62 113l-109 -113h-41l129 144h54zM86 0h-57l147 667h57z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="223" 
d="M367 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM163 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM86 0h-57l147 667h57z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="721" 
d="M271 0h-212l68 309h-102l11 50h102l68 308h208q113 0 192 -81t79 -203q0 -56 -16 -110.5t-50 -104t-81.5 -87t-116.5 -59.5t-150 -22zM362 309h-178l-57 -257h154q154 0 249 96t95 233q0 101 -63.5 167.5t-158.5 66.5h-151l-57 -256h178z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="699" 
d="M524 721q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37zM562 0h-56l-291 582l-129 -582h-57l147 667h58l290 -573l128 573h57z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="764" 
d="M543 723h-44l-140 144h62zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="764" 
d="M681 867l-204 -144h-46l186 144h64zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z
" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="764" 
d="M614 723h-38l-62 113l-109 -113h-41l129 144h54zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248
q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63zM558 721q-25 0 -41.5 16t-23 35.5
t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="764" 
d="M638 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM434 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM367 -12q-129 0 -214.5 77
t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="496" 
d="M381 155l-117 150l-186 -153l-26 31l187 154l-117 150l33 27l116 -151l188 153l25 -32l-187 -153l116 -150z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="764" 
d="M367 -12q-112 0 -189 56l-38 -44h-51l59 69q-81 79 -81 208q0 169 108 285t262 116q106 0 185 -54l37 43h52l-58 -68q83 -80 83 -210q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 101 -60 166l-399 -465q63 -48 155 -48zM129 279q0 -102 57 -165
l398 466q-64 46 -152 46q-124 0 -213.5 -99t-89.5 -248z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="686" 
d="M327 -12q-114 0 -180 58.5t-66 158.5q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-30 -132 -93.5 -200t-185.5 -68zM503 723h-44l-140 144h62z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="686" 
d="M644 867l-204 -144h-46l186 144h64zM327 -12q-114 0 -180 58.5t-66 158.5q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-30 -132 -93.5 -200t-185.5 -68z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="686" 
d="M574 723h-38l-62 113l-109 -113h-41l129 144h54zM327 -12q-114 0 -180 58.5t-66 158.5q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-30 -132 -93.5 -200t-185.5 -68z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="686" 
d="M599 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM395 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM327 -12q-114 0 -180 58.5
t-66 158.5q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-30 -132 -93.5 -200t-185.5 -68z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="618" 
d="M284 0h-58l63 285l-181 382h64l153 -328l297 328h74l-349 -382zM608 867l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" 
d="M86 0h-57l147 667h57l-27 -124h181q71 0 120.5 -48.5t49.5 -118.5q0 -88 -62.5 -155.5t-184.5 -67.5h-190zM316 205q87 0 133.5 49t46.5 118q0 50 -36 84.5t-87 34.5h-178l-63 -286h184z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="578" 
d="M537 141q0 -61 -47.5 -107t-132.5 -46q-68 0 -114 21t-85 69l37 33q60 -79 163 -79q59 0 92.5 31t33.5 71q0 28 -26 48t-63 33t-73.5 27t-62.5 40.5t-26 63.5t21 64.5t50.5 43t59.5 29t51 31.5t21 42q0 33 -33 53.5t-72 20.5q-50 0 -88 -32.5t-51 -88.5l-113 -509h-52
l112 509q17 74 67 121t125 47q63 0 111 -31.5t48 -81.5q0 -36 -21 -61.5t-51 -40t-60 -28t-51 -33.5t-21 -48q0 -26 26 -45t63 -32t73.5 -28t62.5 -42t26 -65z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M402 556h-44l-140 144h62z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M540 700l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M472 556h-38l-62 113l-109 -113h-41l129 144h54z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M417 554q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M497 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM293 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M356 543q-38 0 -62.5 24.5t-24.5 62.5q0 42 31.5 73.5t73.5 31.5q38 0 62.5 -24.5t24.5 -62.5q0 -43 -31 -74t-74 -31zM357 575q29 0 50.5 20.5t21.5 49.5q0 26 -15.5 42t-40.5 16q-29 0 -50 -21t-21 -49q0 -26 15 -42t40 -16z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="948" 
d="M303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25zM466 135l-30 -135h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5q57 0 102.5 -25.5t71.5 -68.5l17 82h53
l-20 -92q30 43 79.5 73.5t107.5 30.5q91 0 140.5 -57.5t49.5 -149.5q0 -35 -7 -63h-404q-2 -12 -2 -28q0 -70 46.5 -117t130.5 -47q77 0 141 53l21 -39q-79 -59 -161 -59q-81 0 -133 40t-66 107zM494 266h355q1 5 1 22q0 73 -40 117.5t-116 44.5q-72 0 -129.5 -54.5
t-70.5 -129.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="494" 
d="M207 -189q-65 0 -104 37l23 30q33 -34 83 -34q32 0 49 14.5t17 36.5q0 33 -40 33q-23 0 -37 -17l-24 16l38 65q-76 14 -121 68.5t-45 141.5q0 120 78 206.5t190 86.5q121 0 173 -88l-40 -30q-41 71 -131 71q-96 0 -155.5 -73.5t-59.5 -173.5q0 -80 46 -123t119 -43
q79 0 131 62l30 -36q-70 -73 -166 -73h-14l-27 -45q15 12 34 12q26 0 42 -15t16 -42q0 -38 -29 -62.5t-76 -24.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="567" 
d="M276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5
t-70.5 -129.5zM409 556h-44l-140 144h62z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="567" 
d="M548 700l-204 -144h-46l186 144h64zM276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22
q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5t-70.5 -129.5z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="567" 
d="M479 556h-38l-62 113l-109 -113h-41l129 144h54zM276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22
q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5t-70.5 -129.5z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="567" 
d="M503 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM299 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM276 -12q-107 0 -168.5 58.5
t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5t-70.5 -129.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="214" 
d="M79 0h-52l107 483h52zM231 556h-44l-140 144h62z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="214" 
d="M370 700l-204 -144h-46l186 144h64zM79 0h-52l107 483h52z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="214" 
d="M301 556h-38l-62 113l-109 -113h-41l129 144h54zM79 0h-52l107 483h52z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="214" 
d="M325 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM121 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM79 0h-52l107 483h52z" />
    <glyph glyph-name="eth" unicode="&#xf0;" 
d="M189 523l-9 32l137 41q-32 29 -70 58l39 40q55 -42 92 -80l117 35l9 -32l-100 -30q120 -138 120 -286q0 -131 -75 -222t-188 -91q-95 0 -155 58t-60 147q0 122 74.5 208t178.5 86q51 0 97.5 -25.5t70.5 -79.5q-38 102 -122 187zM262 35q83 0 143 72.5t60 172.5
q0 64 -41 112t-120 48q-83 0 -142.5 -70t-59.5 -170q0 -73 42.5 -119t117.5 -46z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="540" 
d="M405 0h-52l72 328q6 27 6 35q0 43 -29 64t-76 21q-85 0 -167 -84l-80 -364h-52l107 483h52l-16 -73q87 85 174 85q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41zM410 554q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37
q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" 
d="M258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45zM411 556h-44l-140 144h62z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" 
d="M548 700l-204 -144h-46l186 144h64zM258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45z
" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" 
d="M480 556h-38l-62 113l-109 -113h-41l129 144h54zM258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122
t115.5 -45z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" 
d="M258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45zM425 554q-25 0 -41.5 16t-23 35.5
t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" 
d="M505 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM301 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM258 -12q-97 0 -155 58
t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="511" 
d="M498 316h-453l9 42h453zM228 100q-15 0 -25 10.5t-10 25.5q0 17 12.5 29.5t30.5 12.5q15 0 25.5 -10.5t10.5 -25.5q0 -18 -13 -30t-31 -12zM315 492q-15 0 -25.5 10.5t-10.5 25.5q0 17 13 29.5t30 12.5q15 0 25.5 -10.5t10.5 -25.5q0 -18 -12.5 -30t-30.5 -12z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" 
d="M65 0h-46l66 66q-40 54 -40 134q0 118 77 206.5t189 88.5q90 0 147 -51l38 39h46l-61 -62q43 -55 43 -138q0 -118 -77 -206.5t-189 -88.5q-95 0 -152 54zM259 35q88 0 148 74.5t60 171.5q0 59 -26 100l-297 -301q42 -45 115 -45zM102 202q0 -58 22 -96l297 301
q-43 41 -111 41q-88 0 -148 -74.5t-60 -171.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="540" 
d="M134 483h52l-72 -327q-6 -30 -6 -36q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41zM394 556h-44l-140 144h62z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="540" 
d="M532 700l-204 -144h-46l186 144h64zM134 483h52l-72 -327q-6 -30 -6 -36q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="540" 
d="M464 556h-38l-62 113l-109 -113h-41l129 144h54zM134 483h52l-72 -327q-6 -30 -6 -36q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="540" 
d="M488 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM284 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM134 483h52l-72 -327
q-6 -30 -6 -36q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="481" 
d="M-46 -186l20 50q18 -9 47 -9q39 0 76 55l57 87l-94 486h56l81 -422l269 422h61l-381 -589q-57 -90 -135 -90q-32 0 -57 10zM500 700l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="567" 
d="M271 -12q-58 0 -103 25.5t-71 68.5l-59 -266h-52l188 851h52l-56 -257q65 85 166 85q84 0 136 -53.5t52 -145.5q0 -125 -71 -216.5t-182 -91.5zM266 35q88 0 144.5 76t56.5 176q0 74 -40 117.5t-106 43.5q-49 0 -93 -26.5t-71 -66.5l-51 -232q19 -38 61.5 -63t98.5 -25z
" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="481" 
d="M-46 -186l20 50q18 -9 47 -9q39 0 76 55l57 87l-94 486h56l81 -422l269 422h61l-381 -589q-57 -90 -135 -90q-32 0 -57 10zM456 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM252 605q0 -17 -12.5 -29
t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM614 744h-362l8 37h362z" />
    <glyph glyph-name="amacron" unicode="&#x101;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M534 577h-362l8 37h362z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM614 796q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="abreve" unicode="&#x103;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M533 629q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="647" 
d="M579 -110l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5t-21.5 51.5q0 68 77 116l-31 164h-352l-102 -164h-69l420 667h71l126 -667q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43zM478 216l-72 390l-244 -390h316z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" 
d="M437 -110l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5t-21.5 51.5q0 75 88 123l16 66q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43zM303 448
q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="672" 
d="M684 872l-204 -144h-46l186 144h64zM367 -12q-132 0 -216 79.5t-84 209.5q0 88 31.5 164.5t83.5 127.5t118.5 80t136.5 29q88 0 156 -37t106 -106l-55 -25q-64 116 -211 116q-120 0 -212 -97t-92 -250q0 -108 65.5 -173.5t175.5 -65.5q102 0 188 76l41 -34
q-98 -94 -232 -94z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="494" 
d="M553 700l-204 -144h-46l186 144h64zM261 -12q-96 0 -155.5 57t-59.5 157q0 120 78 206.5t190 86.5q121 0 173 -88l-40 -30q-41 71 -131 71q-96 0 -155.5 -73.5t-59.5 -173.5q0 -80 46 -123t119 -43q79 0 131 62l30 -36q-70 -73 -166 -73z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="672" 
d="M614 723h-38l-62 113l-109 -113h-41l129 144h54zM367 -12q-132 0 -216 79.5t-84 209.5q0 88 31.5 164.5t83.5 127.5t118.5 80t136.5 29q88 0 156 -37t106 -106l-55 -25q-64 116 -211 116q-120 0 -212 -97t-92 -250q0 -108 65.5 -173.5t175.5 -65.5q102 0 188 76l41 -34
q-98 -94 -232 -94z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="494" 
d="M484 556h-38l-62 113l-109 -113h-41l129 144h54zM261 -12q-96 0 -155.5 57t-59.5 157q0 120 78 206.5t190 86.5q121 0 173 -88l-40 -30q-41 71 -131 71q-96 0 -155.5 -73.5t-59.5 -173.5q0 -80 46 -123t119 -43q79 0 131 62l30 -36q-70 -73 -166 -73z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="672" 
d="M367 -12q-132 0 -216 79.5t-84 209.5q0 88 31.5 164.5t83.5 127.5t118.5 80t136.5 29q88 0 156 -37t106 -106l-55 -25q-64 116 -211 116q-120 0 -212 -97t-92 -250q0 -108 65.5 -173.5t175.5 -65.5q102 0 188 76l41 -34q-98 -94 -232 -94zM535 770q0 -18 -12.5 -30
t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="494" 
d="M261 -12q-96 0 -155.5 57t-59.5 157q0 120 78 206.5t190 86.5q121 0 173 -88l-40 -30q-41 71 -131 71q-96 0 -155.5 -73.5t-59.5 -173.5q0 -80 46 -123t119 -43q79 0 131 62l30 -36q-70 -73 -166 -73zM407 603q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5
q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="672" 
d="M519 723h-54l-68 144h37l62 -113l109 113h42zM367 -12q-132 0 -216 79.5t-84 209.5q0 88 31.5 164.5t83.5 127.5t118.5 80t136.5 29q88 0 156 -37t106 -106l-55 -25q-64 116 -211 116q-120 0 -212 -97t-92 -250q0 -108 65.5 -173.5t175.5 -65.5q102 0 188 76l41 -34
q-98 -94 -232 -94z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="494" 
d="M388 556h-54l-68 144h37l62 -113l109 113h42zM261 -12q-96 0 -155.5 57t-59.5 157q0 120 78 206.5t190 86.5q121 0 173 -88l-40 -30q-41 71 -131 71q-96 0 -155.5 -73.5t-59.5 -173.5q0 -80 46 -123t119 -43q79 0 131 62l30 -36q-70 -73 -166 -73z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="692" 
d="M487 723h-54l-68 144h37l62 -113l109 113h42zM241 0h-212l147 667h208q113 0 192 -81t79 -203q0 -56 -16 -110.5t-50 -104t-81.5 -87t-116.5 -59.5t-150 -22zM246 52h5q154 0 249 96t95 233q0 101 -63.5 167.5t-158.5 66.5h-151l-125 -563h149z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="625" 
d="M710 635q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM299 495q57 0 102.5 -25.5t71.5 -68.5l58 266h53l-148 -667h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145
q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="721" 
d="M271 0h-212l68 309h-102l11 50h102l68 308h208q113 0 192 -81t79 -203q0 -56 -16 -110.5t-50 -104t-81.5 -87t-116.5 -59.5t-150 -22zM362 309h-178l-57 -257h154q154 0 249 96t95 233q0 101 -63.5 167.5t-158.5 66.5h-151l-57 -256h178z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="574" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l30 137h-177l7 37h178l20 92h53l-21 -92h55l-7 -37h-56l-119 -538h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5
t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="564" 
d="M452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365zM584 744h-362l8 37h362z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="567" 
d="M276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5
t-70.5 -129.5zM540 577h-362l8 37h362z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="564" 
d="M452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365zM584 796q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="567" 
d="M276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5
t-70.5 -129.5zM539 629q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="564" 
d="M452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365zM447 771q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="567" 
d="M276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5
t-70.5 -129.5zM402 603q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="564" 
d="M452 0h-1q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5t-21.5 51.5q0 68 76 116h-358l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="567" 
d="M354 -110l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5t-21.5 51.5q0 60 58 104q-105 1 -165 59.5t-60 153.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-40 -32 -91 -47
q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5t-70.5 -129.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="564" 
d="M429 723h-54l-68 144h37l62 -113l109 113h42zM452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="567" 
d="M384 556h-54l-68 144h37l62 -113l109 113h42zM276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22
q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5t-70.5 -129.5z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="711" 
d="M615 723h-38l-62 113l-109 -113h-41l129 144h54zM367 -12q-135 0 -217.5 81t-82.5 208q0 172 109.5 286.5t263.5 114.5q87 0 153 -36.5t104 -100.5l-51 -26q-30 54 -86.5 82.5t-126.5 28.5q-121 0 -212.5 -99.5t-91.5 -247.5q0 -109 65.5 -174t175.5 -65q108 0 194 80
l35 160h-252l11 51h310l-53 -238q-108 -105 -248 -105z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="570" 
d="M466 550h-38l-62 113l-109 -113h-41l129 144h54zM212 -196q-72 0 -122.5 18t-85.5 64l37 37q48 -74 170 -74q138 0 173 156l15 72q-66 -85 -171 -85q-81 0 -132 51.5t-51 149.5q0 122 69.5 212t181.5 90q51 0 99.5 -26.5t75.5 -67.5l19 82h53l-107 -478
q-43 -201 -224 -201zM247 38q44 0 88.5 25t74.5 62l53 234q-22 40 -67.5 64.5t-97.5 24.5q-87 0 -142.5 -76t-55.5 -172q0 -75 40 -118.5t107 -43.5z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="711" 
d="M367 -12q-135 0 -217.5 81t-82.5 208q0 172 109.5 286.5t263.5 114.5q87 0 153 -36.5t104 -100.5l-51 -26q-30 54 -86.5 82.5t-126.5 28.5q-121 0 -212.5 -99.5t-91.5 -247.5q0 -109 65.5 -174t175.5 -65q108 0 194 80l35 160h-252l11 51h310l-53 -238
q-108 -105 -248 -105zM676 796q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="570" 
d="M212 -196q-72 0 -122.5 18t-85.5 64l37 37q48 -74 170 -74q138 0 173 156l15 72q-66 -85 -171 -85q-81 0 -132 51.5t-51 149.5q0 122 69.5 212t181.5 90q51 0 99.5 -26.5t75.5 -67.5l19 82h53l-107 -478q-43 -201 -224 -201zM247 38q44 0 88.5 25t74.5 62l53 234
q-22 40 -67.5 64.5t-97.5 24.5q-87 0 -142.5 -76t-55.5 -172q0 -75 40 -118.5t107 -43.5zM528 629q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="711" 
d="M367 -12q-135 0 -217.5 81t-82.5 208q0 172 109.5 286.5t263.5 114.5q87 0 153 -36.5t104 -100.5l-51 -26q-30 54 -86.5 82.5t-126.5 28.5q-121 0 -212.5 -99.5t-91.5 -247.5q0 -109 65.5 -174t175.5 -65q108 0 194 80l35 160h-252l11 51h310l-53 -238
q-108 -105 -248 -105zM537 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="570" 
d="M212 -196q-72 0 -122.5 18t-85.5 64l37 37q48 -74 170 -74q138 0 173 156l15 72q-66 -85 -171 -85q-81 0 -132 51.5t-51 149.5q0 122 69.5 212t181.5 90q51 0 99.5 -26.5t75.5 -67.5l19 82h53l-107 -478q-43 -201 -224 -201zM247 38q44 0 88.5 25t74.5 62l53 234
q-22 40 -67.5 64.5t-97.5 24.5q-87 0 -142.5 -76t-55.5 -172q0 -75 40 -118.5t107 -43.5zM390 603q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="711" 
d="M344 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM367 -12q-135 0 -217.5 81t-82.5 208q0 172 109.5 286.5t263.5 114.5q87 0 153 -36.5t104 -100.5l-51 -26
q-30 54 -86.5 82.5t-126.5 28.5q-121 0 -212.5 -99.5t-91.5 -247.5q0 -109 65.5 -174t175.5 -65q108 0 194 80l35 160h-252l11 51h310l-53 -238q-108 -105 -248 -105z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="570" 
d="M317 604q0 39 28.5 78.5t66.5 59.5l23 -24q-27 -14 -47.5 -36.5t-26.5 -44.5h6q13 0 22.5 -10t9.5 -24q0 -18 -13 -30.5t-31 -12.5q-17 0 -27.5 11.5t-10.5 32.5zM212 -196q-72 0 -122.5 18t-85.5 64l37 37q48 -74 170 -74q138 0 173 156l15 72q-66 -85 -171 -85
q-81 0 -132 51.5t-51 149.5q0 122 69.5 212t181.5 90q51 0 99.5 -26.5t75.5 -67.5l19 82h53l-107 -478q-43 -201 -224 -201zM247 38q44 0 88.5 25t74.5 62l53 234q-22 40 -67.5 64.5t-97.5 24.5q-87 0 -142.5 -76t-55.5 -172q0 -75 40 -118.5t107 -43.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="703" 
d="M582 723h-38l-62 113l-109 -113h-41l129 144h54zM567 0h-58l70 317h-423l-70 -317h-57l147 667h57l-65 -298h422l66 298h58z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="540" 
d="M504 723h-38l-62 113l-109 -113h-41l129 144h54zM405 0h-52l72 328q6 27 6 35q0 43 -29.5 64t-78.5 21q-81 0 -164 -85l-80 -363h-52l147 667h52l-56 -257q87 85 174 85q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="724" 
d="M578 0h-58l70 317h-423l-70 -317h-57l110 501h-82l9 38h82l28 128h57l-28 -128h422l29 128h58l-28 -128h79l-8 -38h-80zM179 369h422l29 132h-422z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="541" 
d="M406 0h-52l72 328q6 27 6 35q0 43 -29.5 64t-78.5 21q-81 0 -164 -85l-80 -363h-52l118 538h-71l8 38h72l20 91h52l-20 -91h162l-9 -38h-161l-28 -128q87 85 174 85q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="223" 
d="M86 0h-57l147 667h57zM287 721q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="214" 
d="M79 0h-52l107 483h52zM246 554q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="223" 
d="M86 0h-57l147 667h57zM403 744h-362l8 37h362z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="214" 
d="M79 0h-52l107 483h52zM362 577h-362l8 37h362z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="223" 
d="M86 0h-57l147 667h57zM402 796q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="214" 
d="M79 0h-52l107 483h52zM362 629q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="223" 
d="M87 -110l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5t-21.5 51.5q0 74 84 120l146 663h57l-147 -667q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="214" 
d="M80 -110l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5t-21.5 51.5q0 74 89 123l106 476h52l-107 -483q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43zM180 559q-15 0 -25.5 10.5t-10.5 24.5q0 19 14 31.5t31 12.5q15 0 25.5 -10t10.5 -25q0 -19 -14 -31.5t-31 -12.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="223" 
d="M86 0h-57l147 667h57zM265 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="214" 
d="M79 0h-52l107 483h52z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="693" 
d="M86 0h-57l147 667h57zM379 -12q-58 0 -104.5 22.5t-70.5 65.5l38 40q41 -76 134 -76q66 0 107.5 41t57.5 112l104 474h58l-104 -474q-46 -205 -220 -205z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="427" 
d="M180 559q-15 0 -25.5 10.5t-10.5 24.5q0 19 14 31.5t31 12.5q15 0 25.5 -10t10.5 -25q0 -19 -14 -31.5t-31 -12.5zM79 0h-52l107 483h52zM140 -196q-72 0 -107 42l28 38q26 -35 72 -35q73 0 94 89l121 545h52l-121 -545q-15 -68 -48.5 -101t-90.5 -33zM394 559
q-15 0 -25.5 10.5t-10.5 24.5q0 19 14 31.5t31 12.5q15 0 25.5 -10t10.5 -25q0 -19 -14 -31.5t-31 -12.5z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="470" 
d="M591 723h-38l-62 113l-109 -113h-41l129 144h54zM156 -12q-58 0 -104.5 22.5t-70.5 65.5l38 40q41 -76 134 -76q66 0 107.5 41t57.5 112l104 474h58l-104 -474q-46 -205 -220 -205z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="214" 
d="M302 556h-38l-62 113l-109 -113h-41l129 144h54zM-74 -196q-72 0 -107 42l28 38q26 -35 72 -35q73 0 94 89l121 545h52l-121 -545q-15 -68 -48.5 -101t-90.5 -33z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="586" 
d="M265 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM508 0h-68l-208 318l-94 -85l-52 -233h-57l147 667h57l-79 -358l396 358h78l-354 -318z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="505" 
d="M227 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM432 0h-64l-145 233l-112 -88l-32 -145h-52l147 667h52l-101 -456l342 272h73l-274 -218z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="505" 
d="M432 0h-64l-145 233l-112 -88l-32 -145h-52l107 483h52l-61 -272l342 272h73l-274 -218z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="517" 
d="M573 867l-204 -144h-46l186 144h64zM409 0h-380l147 667h57l-136 -615h323z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="214" 
d="M407 867l-204 -144h-46l186 144h64zM79 0h-52l147 667h52z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="517" 
d="M226 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM409 0h-380l147 667h57l-136 -615h323z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="214" 
d="M67 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM79 0h-52l147 667h52z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="517" 
d="M408 635q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM409 0h-380l147 667h57l-136 -615h323z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="264" 
d="M367 635q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM79 0h-52l147 667h52z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="517" 
d="M409 0h-380l147 667h57l-136 -615h323zM376 298q-17 0 -29 11.5t-12 28.5q0 20 14.5 33.5t34.5 13.5q17 0 28.5 -11.5t11.5 -28.5q0 -19 -14 -33t-34 -14z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="273" 
d="M79 0h-52l147 667h52zM240 201q-17 0 -29 11.5t-12 28.5q0 20 14.5 33.5t34.5 13.5q17 0 28.5 -11.5t11.5 -28.5q0 -19 -14 -33t-34 -14z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="552" 
d="M22 233l12 53l105 54l72 327h57l-65 -294l120 62l-12 -53l-120 -62l-59 -268h323l-11 -52h-380l63 287z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="277" 
d="M9 238l11 46l114 58l72 325h52l-65 -295l114 58l-10 -46l-114 -58l-72 -326h-52l65 296z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="699" 
d="M649 867l-204 -144h-46l186 144h64zM562 0h-56l-291 582l-129 -582h-57l147 667h58l290 -573l128 573h57z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="540" 
d="M533 700l-204 -144h-46l186 144h64zM405 0h-52l72 328q6 27 6 35q0 43 -29 64t-76 21q-85 0 -167 -84l-80 -364h-52l107 483h52l-16 -73q87 85 174 85q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="699" 
d="M308 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM562 0h-56l-291 582l-129 -582h-57l147 667h58l290 -573l128 573h57z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="540" 
d="M231 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM405 0h-52l72 328q6 27 6 35q0 43 -29 64t-76 21q-85 0 -167 -84l-80 -364h-52l107 483h52l-16 -73q87 85 174 85
q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="699" 
d="M485 723h-54l-68 144h37l62 -113l109 113h42zM562 0h-56l-291 582l-129 -582h-57l147 667h58l290 -573l128 573h57z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="540" 
d="M368 556h-54l-68 144h37l62 -113l109 113h42zM405 0h-52l72 328q6 27 6 35q0 43 -29 64t-76 21q-85 0 -167 -84l-80 -364h-52l107 483h52l-16 -73q87 85 174 85q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="540" 
d="M249 690q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM405 0h-52l72 328q6 27 6 35q0 43 -29 64t-76 21q-85 0 -167 -84l-80 -364h-52l107 483h52l-16 -73q87 85 174 85
q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="699" 
d="M344 -196q-124 0 -175 88l38 37q38 -76 134 -76q126 0 164 149l-290 580l-129 -582h-57l147 667h58l290 -573l128 573h57l-146 -662q-49 -201 -219 -201z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="540" 
d="M479 336l-88 -398q-30 -134 -139 -134q-72 0 -107 42l31 41q27 -36 70 -36q73 0 93 87l86 390q6 26 6 36q0 43 -29.5 63.5t-75.5 20.5q-85 0 -167 -84l-80 -364h-52l107 483h52l-16 -73q87 85 174 85q62 0 101.5 -29.5t39.5 -88.5q0 -11 -6 -41z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="764" 
d="M674 744h-362l8 37h362zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" 
d="M541 577h-362l8 37h362zM258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63zM673 796q-85 -88 -194 -88
q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" 
d="M258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45zM541 629q-85 -88 -194 -88
q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="764" 
d="M568 867l-147 -144h-39l129 144h57zM707 867l-147 -144h-39l129 144h57zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99
t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" 
d="M434 700l-147 -144h-39l129 144h57zM573 700l-147 -144h-39l129 144h57zM258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5
t-60 -171.5q0 -77 41.5 -122t115.5 -45z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1115" 
d="M1001 0h-423l25 114q-92 -126 -255 -126q-125 0 -203 81t-78 208q0 173 110.5 286.5t264.5 113.5q88 0 154 -41t94 -131l35 162h423l-11 -52h-365l-55 -247h358l-12 -52h-358l-58 -264h365zM621 194l51 231q-13 95 -75.5 147.5t-157.5 52.5q-128 0 -219 -99.5t-91 -246.5
q0 -109 65 -174t170 -65q77 0 146 41t111 113z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="975" 
d="M920 225h-404q-2 -10 -2 -28q0 -70 46 -117t129 -47q82 0 144 52l21 -38q-74 -59 -167 -59q-90 0 -147 49t-63 102q-14 -23 -23.5 -37.5t-31 -38.5t-43 -38.5t-54 -25.5t-69.5 -11q-95 0 -153 57t-58 155q0 118 76 206.5t188 88.5q86 0 136.5 -50t56.5 -101
q85 151 223 151q90 0 146.5 -56.5t56.5 -152.5q0 -29 -8 -61zM521 266h356q2 12 2 22q0 71 -42 116.5t-117 45.5q-76 0 -133 -58.5t-66 -125.5zM463 281q0 83 -44.5 125t-110.5 42q-86 0 -146 -73t-60 -173q0 -76 41 -121.5t114 -45.5q87 0 146.5 73t59.5 173z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="594" 
d="M604 867l-204 -144h-46l186 144h64zM489 0h-65l-126 276h-151l-61 -276h-57l147 667h229q72 0 126.5 -46t54.5 -122q0 -94 -62.5 -155.5t-165.5 -65.5zM343 328h2q84 0 131.5 48.5t47.5 119.5q0 51 -38 85t-88 34h-176l-63 -287h184z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="317" 
d="M450 700l-204 -144h-46l186 144h64zM79 0h-52l107 483h52l-18 -78q40 46 80.5 67t102.5 21l-12 -56q-18 3 -35 3q-42 0 -81.5 -26t-65.5 -62z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="594" 
d="M265 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM489 0h-65l-126 276h-151l-61 -276h-57l147 667h229q72 0 126.5 -46t54.5 -122q0 -94 -62.5 -155.5t-165.5 -65.5z
M343 328h2q84 0 131.5 48.5t47.5 119.5q0 51 -38 85t-88 34h-176l-63 -287h184z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="317" 
d="M148 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM79 0h-52l107 483h52l-18 -78q40 46 80.5 67t102.5 21l-12 -56q-18 3 -35 3q-42 0 -81.5 -26t-65.5 -62z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="594" 
d="M440 723h-54l-68 144h37l62 -113l109 113h42zM489 0h-65l-126 276h-151l-61 -276h-57l147 667h229q72 0 126.5 -46t54.5 -122q0 -94 -62.5 -155.5t-165.5 -65.5zM343 328h2q84 0 131.5 48.5t47.5 119.5q0 51 -38 85t-88 34h-176l-63 -287h184z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="317" 
d="M286 556h-54l-68 144h37l62 -113l109 113h42zM79 0h-52l107 483h52l-18 -78q40 46 80.5 67t102.5 21l-12 -56q-18 3 -35 3q-42 0 -81.5 -26t-65.5 -62z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="581" 
d="M593 867l-204 -144h-46l186 144h64zM279 -12q-84 0 -153.5 33t-101.5 84l42 41q75 -106 218 -106q82 0 125 44.5t43 100.5q0 40 -34.5 69.5t-83 49.5t-97.5 41.5t-83.5 58.5t-34.5 88q0 76 65.5 131t159.5 55q75 0 137 -28.5t93 -75.5l-43 -40q-30 45 -81.5 68.5
t-110.5 23.5q-65 0 -111.5 -39t-46.5 -89q0 -37 34.5 -64.5t83.5 -47.5t98 -42t83.5 -60.5t34.5 -91.5q0 -81 -63 -142.5t-173 -61.5z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="459" 
d="M488 700l-204 -144h-46l186 144h64zM202 -12q-133 0 -194 85l37 36q20 -32 62.5 -54.5t95.5 -22.5q58 0 93 29.5t35 70.5q0 28 -26.5 49.5t-64.5 36t-76 30t-64.5 42t-26.5 62.5q0 61 47.5 102t128.5 41q59 0 107 -23t69 -56l-34 -34q-16 29 -57.5 49t-88.5 20
q-54 0 -86.5 -24.5t-32.5 -62.5q0 -27 26.5 -47t64.5 -34t76 -30t64.5 -44t26.5 -66q0 -64 -49 -109.5t-133 -45.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="581" 
d="M524 723h-38l-62 113l-109 -113h-41l129 144h54zM279 -12q-84 0 -153.5 33t-101.5 84l42 41q75 -106 218 -106q82 0 125 44.5t43 100.5q0 40 -34.5 69.5t-83 49.5t-97.5 41.5t-83.5 58.5t-34.5 88q0 76 65.5 131t159.5 55q75 0 137 -28.5t93 -75.5l-43 -40
q-30 45 -81.5 68.5t-110.5 23.5q-65 0 -111.5 -39t-46.5 -89q0 -37 34.5 -64.5t83.5 -47.5t98 -42t83.5 -60.5t34.5 -91.5q0 -81 -63 -142.5t-173 -61.5z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="459" 
d="M420 556h-38l-62 113l-109 -113h-41l129 144h54zM202 -12q-133 0 -194 85l37 36q20 -32 62.5 -54.5t95.5 -22.5q58 0 93 29.5t35 70.5q0 28 -26.5 49.5t-64.5 36t-76 30t-64.5 42t-26.5 62.5q0 61 47.5 102t128.5 41q59 0 107 -23t69 -56l-34 -34q-16 29 -57.5 49
t-88.5 20q-54 0 -86.5 -24.5t-32.5 -62.5q0 -27 26.5 -47t64.5 -34t76 -30t64.5 -44t26.5 -66q0 -64 -49 -109.5t-133 -45.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="581" 
d="M204 -189q-65 0 -104 37l23 30q33 -34 83 -34q32 0 49 14.5t17 36.5q0 33 -40 33q-23 0 -37 -17l-24 16l39 68q-63 11 -112.5 40.5t-73.5 69.5l42 41q75 -106 218 -106q82 0 125 44.5t43 100.5q0 40 -34.5 69.5t-83 49.5t-97.5 41.5t-83.5 58.5t-34.5 88q0 76 65.5 131
t159.5 55q75 0 137 -28.5t93 -75.5l-43 -40q-30 45 -81.5 68.5t-110.5 23.5q-65 0 -111.5 -39t-46.5 -89q0 -37 34.5 -64.5t83.5 -47.5t98 -42t83.5 -60.5t34.5 -91.5q0 -81 -63 -142.5t-173 -61.5q-12 0 -34 2l-28 -47q15 12 34 12q26 0 42 -15t16 -42q0 -38 -29 -62.5
t-76 -24.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="459" 
d="M142 -189q-65 0 -104 37l23 30q33 -34 83 -34q32 0 49 14.5t17 36.5q0 33 -40 33q-23 0 -37 -17l-24 16l38 66q-92 14 -139 80l37 36q20 -32 62.5 -54.5t95.5 -22.5q58 0 93 29.5t35 70.5q0 28 -26.5 49.5t-64.5 36t-76 30t-64.5 42t-26.5 62.5q0 61 47.5 102t128.5 41
q59 0 107 -23t69 -56l-34 -34q-16 29 -57.5 49t-88.5 20q-54 0 -86.5 -24.5t-32.5 -62.5q0 -27 26.5 -47t64.5 -34t76 -30t64.5 -44t26.5 -66q0 -64 -49 -109.5t-133 -45.5q-13 0 -20 1l-27 -46q15 12 34 12q26 0 42 -15t16 -42q0 -38 -29 -62.5t-76 -24.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="581" 
d="M429 723h-54l-68 144h37l62 -113l109 113h42zM279 -12q-84 0 -153.5 33t-101.5 84l42 41q75 -106 218 -106q82 0 125 44.5t43 100.5q0 40 -34.5 69.5t-83 49.5t-97.5 41.5t-83.5 58.5t-34.5 88q0 76 65.5 131t159.5 55q75 0 137 -28.5t93 -75.5l-43 -40
q-30 45 -81.5 68.5t-110.5 23.5q-65 0 -111.5 -39t-46.5 -89q0 -37 34.5 -64.5t83.5 -47.5t98 -42t83.5 -60.5t34.5 -91.5q0 -81 -63 -142.5t-173 -61.5z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="459" 
d="M324 556h-54l-68 144h37l62 -113l109 113h42zM202 -12q-133 0 -194 85l37 36q20 -32 62.5 -54.5t95.5 -22.5q58 0 93 29.5t35 70.5q0 28 -26.5 49.5t-64.5 36t-76 30t-64.5 42t-26.5 62.5q0 61 47.5 102t128.5 41q59 0 107 -23t69 -56l-34 -34q-16 29 -57.5 49t-88.5 20
q-54 0 -86.5 -24.5t-32.5 -62.5q0 -27 26.5 -47t64.5 -34t76 -30t64.5 -44t26.5 -66q0 -64 -49 -109.5t-133 -45.5z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="564" 
d="M242 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM257 0h-58l136 615h-218l11 52h494l-11 -52h-218z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="275" 
d="M87 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM151 -12q-96 0 -96 81q0 11 3 26l76 342h-80l10 46h80l29 132h53l-29 -132h98l-11 -46h-98l-74 -335q-2 -10 -2 -21
q0 -46 49 -46q27 0 51 18l10 -42q-30 -23 -69 -23z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="564" 
d="M409 723h-54l-68 144h37l62 -113l109 113h42zM257 0h-58l136 615h-218l11 52h494l-11 -52h-218z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="285" 
d="M387 690q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM151 -12q-96 0 -96 81q0 11 3 26l76 342h-80l10 46h80l29 132h53l-29 -132h98l-11 -46h-98l-74 -335q-2 -10 -2 -21
q0 -46 49 -46q27 0 51 18l10 -42q-30 -23 -69 -23z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="564" 
d="M257 0h-58l68 308h-158l9 37h157l60 270h-218l11 52h494l-11 -52h-218l-60 -270h158l-9 -37h-157z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="275" 
d="M151 -12q-96 0 -96 81q0 11 3 26l31 140h-80l8 37h80l37 165h-80l10 46h80l29 132h53l-29 -132h98l-11 -46h-98l-37 -165h71l-8 -37h-71l-29 -133q-2 -10 -2 -21q0 -46 49 -46q27 0 51 18l10 -42q-30 -23 -69 -23z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="686" 
d="M327 -12q-114 0 -180 58.5t-66 158.5q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-30 -132 -93.5 -200t-185.5 -68zM519 721q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36
q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="540" 
d="M134 483h52l-72 -327q-6 -30 -6 -36q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41zM408 554q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37
q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="686" 
d="M327 -12q-114 0 -180 58.5t-66 158.5q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-30 -132 -93.5 -200t-185.5 -68zM634 744h-362l8 37h362z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="540" 
d="M134 483h52l-72 -327q-6 -30 -6 -36q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41zM524 577h-362l8 37h362z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="686" 
d="M327 -12q-114 0 -180 58.5t-66 158.5q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-30 -132 -93.5 -200t-185.5 -68zM635 796q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75
t163 75z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="540" 
d="M134 483h52l-72 -327q-6 -30 -6 -36q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41zM523 629q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="686" 
d="M453 691q-38 0 -62.5 24.5t-24.5 62.5q0 42 31.5 73.5t73.5 31.5q38 0 62.5 -24.5t24.5 -62.5q0 -43 -31 -74t-74 -31zM454 723q29 0 50.5 20.5t21.5 49.5q0 26 -15.5 42t-40.5 16q-29 0 -50 -21t-21 -49q0 -26 15 -42t40 -16zM327 -12q-114 0 -180 58.5t-66 158.5
q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-30 -132 -93.5 -200t-185.5 -68z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="540" 
d="M348 548q-38 0 -62.5 24.5t-24.5 62.5q0 42 31.5 73.5t73.5 31.5q38 0 62.5 -24.5t24.5 -62.5q0 -43 -31 -74t-74 -31zM349 580q29 0 50.5 20.5t21.5 49.5q0 26 -15.5 42t-40.5 16q-29 0 -50 -21t-21 -49q0 -26 15 -42t40 -16zM134 483h52l-72 -327q-6 -30 -6 -36
q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="686" 
d="M327 -12q-114 0 -180 58.5t-66 158.5q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-30 -132 -93.5 -200t-185.5 -68zM529 867l-147 -144h-39l129 144h57zM668 867l-147 -144h-39l129 144h57z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="540" 
d="M134 483h52l-72 -327q-6 -30 -6 -36q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41zM418 700l-147 -144h-39l129 144h57zM557 700l-147 -144h-39l129 144h57z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="686" 
d="M327 -12q-114 0 -180 58.5t-66 158.5q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-24 -105 -68 -169t-120 -87q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43l26 -18q-36 -58 -90 -58
q-34 0 -55.5 18.5t-21.5 51.5q0 60 58 104h-9z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="540" 
d="M134 483h52l-72 -327q-6 -30 -6 -36q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5t-21.5 51.5q0 75 89 123l15 66q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5
q0 19 6 41z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="867" 
d="M663 723h-38l-62 113l-109 -113h-41l129 144h54zM604 0h-64l-34 573l-286 -573h-64l-44 667h63l30 -587l294 587h52l34 -587l290 587h66z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="719" 
d="M553 556h-38l-62 113l-109 -113h-41l129 144h54zM491 0h-50l-44 413l-227 -413h-50l-50 483h53l37 -410l228 410h44l45 -410l220 410h58z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="618" 
d="M540 723h-38l-62 113l-109 -113h-41l129 144h54zM284 0h-58l63 285l-181 382h64l153 -328l297 328h74l-349 -382z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="481" 
d="M-46 -186l20 50q18 -9 47 -9q39 0 76 55l57 87l-94 486h56l81 -422l269 422h61l-381 -589q-57 -90 -135 -90q-32 0 -57 10zM433 556h-38l-62 113l-109 -113h-41l129 144h54z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="618" 
d="M565 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM361 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM284 0h-58l63 285l-181 382h64
l153 -328l297 328h74l-349 -382z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="581" 
d="M587 867l-204 -144h-46l186 144h64zM477 0h-481l11 50l522 565h-396l10 52h474l-10 -49l-523 -566h404z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="469" 
d="M494 700l-204 -144h-46l186 144h64zM359 0h-357l8 43l371 394h-282l10 46h353l-9 -42l-373 -395h288z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="581" 
d="M477 0h-481l11 50l522 565h-396l10 52h474l-10 -49l-523 -566h404zM441 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="469" 
d="M359 0h-357l8 43l371 394h-282l10 46h353l-9 -42l-373 -395h288zM348 603q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="581" 
d="M424 723h-54l-68 144h37l62 -113l109 113h42zM477 0h-481l11 50l522 565h-396l10 52h474l-10 -49l-523 -566h404z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="469" 
d="M328 556h-54l-68 144h37l62 -113l109 113h42zM359 0h-357l8 43l371 394h-282l10 46h353l-9 -42l-373 -395h288z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="265" 
d="M97 0h-53l97 437h-80l10 46h80l9 44q33 150 151 150q58 0 95 -37l-30 -33q-23 25 -65 25q-40 0 -63 -26t-35 -79z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="422" 
d="M37 -161h-59l94 421h-74v49h84l49 221q34 147 149 147q61 0 99 -38l-29 -40q-22 26 -61 26q-78 0 -101 -102l-48 -214h151v-49h-161z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q158 0 241 -107q54 29 67 77h-6q-13 0 -21.5 9t-8.5 23q0 17 12 29t29 12q16 0 26.5 -11t10.5 -31q0 -38 -27 -75.5t-65 -57.5q41 -68 41 -157q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5
q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" 
d="M564 545q0 -35 -23.5 -70.5t-57.5 -56.5q41 -57 41 -135q0 -118 -77 -206.5t-189 -88.5q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q92 0 151 -54q49 32 60 73h-7q-12 0 -21 9.5t-9 22.5q0 17 12.5 29t29.5 12q16 0 26.5 -11t10.5 -31zM259 35q88 0 148 74.5
t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="686" 
d="M826 744q0 -49 -42 -92.5t-107 -68.5l-71 -327q-30 -132 -93.5 -200t-185.5 -68q-114 0 -180 58.5t-66 158.5q0 23 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-11 -49q85 42 99 95h-7q-12 0 -21 9.5t-9 22.5
q0 18 12.5 30t29.5 12q16 0 26.5 -11.5t10.5 -31.5z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" horiz-adv-x="540" 
d="M629 547q0 -48 -38.5 -90t-99.5 -67l-86 -390h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41l73 336h53l-73 -327q-5 -25 -5 -36q0 -43 29 -64t78 -21q84 0 164 84l81 364h52l-13 -57q75 38 88 90h-6q-12 0 -21 9.5t-9 22.5q0 17 12 29.5t29 12.5
q16 0 26.5 -11.5t10.5 -31.5z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="939" 
d="M784 867l-204 -144h-46l186 144h64zM825 0h-423l36 164h-271l-138 -164h-69l568 667h444l-11 -52h-365l-55 -247h358l-12 -52h-358l-58 -264h365zM450 216l85 385l-327 -385h242z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="948" 
d="M726 700l-204 -144h-46l186 144h64zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25zM466 135l-30 -135h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5
q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-20 -92q30 43 79.5 73.5t107.5 30.5q91 0 140.5 -57.5t49.5 -149.5q0 -35 -7 -63h-404q-2 -12 -2 -28q0 -70 46.5 -117t130.5 -47q77 0 141 53l21 -39q-79 -59 -161 -59q-81 0 -133 40t-66 107zM494 266h355q1 5 1 22q0 73 -40 117.5
t-116 44.5q-72 0 -129.5 -54.5t-70.5 -129.5z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="764" 
d="M682 867l-204 -144h-46l186 144h64zM367 -12q-112 0 -189 56l-38 -44h-51l59 69q-81 79 -81 208q0 169 108 285t262 116q106 0 185 -54l37 43h52l-58 -68q83 -80 83 -210q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 101 -60 166l-399 -465
q63 -48 155 -48zM129 279q0 -102 57 -165l398 466q-64 46 -152 46q-124 0 -213.5 -99t-89.5 -248z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" 
d="M548 700l-204 -144h-46l186 144h64zM65 0h-46l66 66q-40 54 -40 134q0 118 77 206.5t189 88.5q90 0 147 -51l38 39h46l-61 -62q43 -55 43 -138q0 -118 -77 -206.5t-189 -88.5q-95 0 -152 54zM259 35q88 0 148 74.5t60 171.5q0 59 -26 100l-297 -301q42 -45 115 -45z
M102 202q0 -58 22 -96l297 301q-43 41 -111 41q-88 0 -148 -74.5t-60 -171.5z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="581" 
d="M253 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM279 -12q-84 0 -153.5 33t-101.5 84l42 41q75 -106 218 -106q82 0 125 44.5t43 100.5q0 40 -34.5 69.5t-83 49.5
t-97.5 41.5t-83.5 58.5t-34.5 88q0 76 65.5 131t159.5 55q75 0 137 -28.5t93 -75.5l-43 -40q-30 45 -81.5 68.5t-110.5 23.5q-65 0 -111.5 -39t-46.5 -89q0 -37 34.5 -64.5t83.5 -47.5t98 -42t83.5 -60.5t34.5 -91.5q0 -81 -63 -142.5t-173 -61.5z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="459" 
d="M185 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM202 -12q-133 0 -194 85l37 36q20 -32 62.5 -54.5t95.5 -22.5q58 0 93 29.5t35 70.5q0 28 -26.5 49.5t-64.5 36t-76 30
t-64.5 42t-26.5 62.5q0 61 47.5 102t128.5 41q59 0 107 -23t69 -56l-34 -34q-16 29 -57.5 49t-88.5 20q-54 0 -86.5 -24.5t-32.5 -62.5q0 -27 26.5 -47t64.5 -34t76 -30t64.5 -44t26.5 -66q0 -64 -49 -109.5t-133 -45.5z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="564" 
d="M242 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM257 0h-58l136 615h-218l11 52h494l-11 -52h-218z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="275" 
d="M80 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM151 -12q-96 0 -96 81q0 11 3 26l76 342h-80l10 46h80l29 132h53l-29 -132h98l-11 -46h-98l-74 -335q-2 -10 -2 -21
q0 -46 49 -46q27 0 51 18l10 -42q-30 -23 -69 -23z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="214" 
d="M-74 -196q-72 0 -107 42l28 38q26 -35 72 -35q73 0 94 89l121 545h52l-121 -545q-15 -68 -48.5 -101t-90.5 -33z" />
    <glyph glyph-name="hsuperior" unicode="&#x2b0;" horiz-adv-x="392" 
d="M344 326h-43l46 207q2 10 2 20q0 59 -71 59q-48 0 -104 -54l-50 -232h-43l96 434h43l-37 -167q51 55 115 55q46 0 71.5 -20.5t25.5 -57.5q0 -12 -4 -32z" />
    <glyph glyph-name="uni02BC" unicode="&#x2bc;" horiz-adv-x="219" 
d="M242 629q0 -41 -30.5 -82.5t-72.5 -63.5l-24 26q28 14 49.5 37.5t28.5 46.5h-7q-14 0 -24 10.5t-10 25.5q0 20 14 34t33 14t31 -12.5t12 -35.5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="250" 
d="M319 556h-38l-62 113l-109 -113h-41l129 144h54z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="250" 
d="M223 556h-54l-68 144h37l62 -113l109 113h42z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="363" 
d="M439 591h-362l8 38h362z" />
    <glyph glyph-name="uni02CB" unicode="&#x2cb;" horiz-adv-x="216" 
d="M285 556h-44l-140 144h62z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="351" 
d="M435 629q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="85" 
d="M159 603q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="190" 
d="M173 548q-38 0 -62.5 24.5t-24.5 62.5q0 42 31.5 73.5t73.5 31.5q38 0 62.5 -24.5t24.5 -62.5q0 -43 -31 -74t-74 -31zM174 580q29 0 50.5 20.5t21.5 49.5q0 26 -15.5 42t-40.5 16q-29 0 -50 -21t-21 -49q0 -26 15 -42t40 -16z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="171" 
d="M63 -110l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5t-21.5 51.5q0 85 115 136l25 -20q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="307" 
d="M292 554q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="291" 
d="M253 700l-147 -144h-39l129 144h57zM392 700l-147 -144h-39l129 144h57z" />
    <glyph glyph-name="hookabovecomb" unicode="&#x309;" horiz-adv-x="171" 
d="M208 731l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni037E" unicode="&#x37e;" horiz-adv-x="219" 
d="M109 30q0 -41 -30.5 -83t-72.5 -64l-24 27q28 14 49.5 37.5t28.5 46.5h-7q-14 0 -24 10.5t-10 25.5q0 20 14 34t33 14t31 -12.5t12 -35.5zM150 403q-17 0 -28.5 11.5t-11.5 28.5q0 19 14 33t34 14q17 0 28.5 -11.5t11.5 -28.5q0 -20 -14 -33.5t-34 -13.5z" />
    <glyph glyph-name="tonos" unicode="&#x384;" horiz-adv-x="540" 
d="M201 570h-30l62 195h64z" />
    <glyph glyph-name="dieresistonos" unicode="&#x385;" horiz-adv-x="540" 
d="M348 570h-30l62 195h64zM517 605q0 -16 -12.5 -28.5t-29.5 -12.5q-14 0 -23.5 9.5t-9.5 23.5q0 17 12 29t29 12q14 0 24 -9.5t10 -23.5zM262 605q0 -17 -12 -29t-29 -12q-14 0 -24 9.5t-10 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5z" />
    <glyph glyph-name="Alphatonos" unicode="&#x386;" horiz-adv-x="647" 
d="M208 570h-30l62 195h64zM578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316z" />
    <glyph glyph-name="Epsilontonos" unicode="&#x388;" horiz-adv-x="572" 
d="M94 570h-30l62 195h64zM460 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365z" />
    <glyph glyph-name="Etatonos" unicode="&#x389;" horiz-adv-x="712" 
d="M94 570h-30l62 195h64zM575 0h-58l70 317h-423l-70 -317h-57l147 667h57l-65 -298h422l66 298h58z" />
    <glyph glyph-name="Iotatonos" unicode="&#x38a;" horiz-adv-x="232" 
d="M94 570h-30l62 195h64zM94 0h-57l147 667h57z" />
    <glyph glyph-name="Omicrontonos" unicode="&#x38c;" horiz-adv-x="764" 
d="M136 570h-30l62 195h64zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="Upsilontonos" unicode="&#x38e;" horiz-adv-x="665" 
d="M94 570h-30l62 195h64zM330 0h-58l63 285l-181 382h64l153 -328l297 328h74l-349 -382z" />
    <glyph glyph-name="Omegatonos" unicode="&#x38f;" horiz-adv-x="766" 
d="M136 570h-30l62 195h64zM36 52h157q-120 88 -120 237q0 101 45 189.5t130.5 144t190.5 55.5q134 0 215.5 -78t81.5 -212q0 -121 -62 -206t-156 -130h129l-10 -52h-228l11 52q107 22 180.5 115t73.5 219q0 109 -65 174t-171 65q-132 0 -219.5 -101.5t-87.5 -244.5
q0 -81 37 -138.5t95 -88.5l-11 -52h-227z" />
    <glyph glyph-name="iotadieresistonos" unicode="&#x390;" horiz-adv-x="227" 
d="M193 570h-30l62 195h64zM362 605q0 -16 -12.5 -28.5t-29.5 -12.5q-14 0 -23.5 9.5t-9.5 23.5q0 17 12.5 29t28.5 12q14 0 24 -9.5t10 -23.5zM107 605q0 -17 -12.5 -29t-28.5 -12q-14 0 -24 9.5t-10 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM118 -12
q-95 0 -71 105l87 390h52l-85 -384q-15 -64 34 -64q13 0 21 2l-6 -46q-12 -3 -32 -3z" />
    <glyph glyph-name="Alpha" unicode="&#x391;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316z" />
    <glyph glyph-name="Beta" unicode="&#x392;" horiz-adv-x="614" 
d="M314 0h-285l147 667h257q69 0 117 -42t48 -109q0 -70 -45.5 -121t-109.5 -59q42 -10 69.5 -50t27.5 -88q0 -82 -58.5 -140t-167.5 -58zM386 368q77 0 113 42t36 100q0 45 -33.5 75t-81.5 30h-198l-54 -247h218zM317 52q76 0 120 44.5t44 104.5q0 50 -33 82.5t-89 32.5
h-203l-59 -264h220z" />
    <glyph glyph-name="Gamma" unicode="&#x393;" horiz-adv-x="542" 
d="M86 0h-57l147 667h423l-11 -52h-366z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="647" 
d="M578 0h-617l420 667h71zM508 52l-102 554l-347 -554h449z" />
    <glyph glyph-name="Epsilon" unicode="&#x395;" horiz-adv-x="564" 
d="M452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365z" />
    <glyph glyph-name="Zeta" unicode="&#x396;" horiz-adv-x="581" 
d="M477 0h-481l11 50l522 565h-396l10 52h474l-10 -49l-523 -566h404z" />
    <glyph glyph-name="Eta" unicode="&#x397;" horiz-adv-x="703" 
d="M567 0h-58l70 317h-423l-70 -317h-57l147 667h57l-65 -298h422l66 298h58z" />
    <glyph glyph-name="Theta" unicode="&#x398;" horiz-adv-x="764" 
d="M594 316h-401l11 52h401zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="Iota" unicode="&#x399;" horiz-adv-x="223" 
d="M86 0h-57l147 667h57z" />
    <glyph glyph-name="Kappa" unicode="&#x39a;" horiz-adv-x="586" 
d="M508 0h-68l-208 318l-94 -85l-52 -233h-57l147 667h57l-79 -358l396 358h78l-354 -318z" />
    <glyph glyph-name="Lambda" unicode="&#x39b;" horiz-adv-x="647" 
d="M578 0h-63l-109 606l-376 -606h-69l420 667h71z" />
    <glyph glyph-name="Mu" unicode="&#x39c;" horiz-adv-x="790" 
d="M653 0h-58l131 595l-374 -595h-22l-112 595l-132 -595h-57l147 667h82l104 -556l350 556h88z" />
    <glyph glyph-name="Nu" unicode="&#x39d;" horiz-adv-x="699" 
d="M562 0h-56l-291 582l-129 -582h-57l147 667h58l290 -573l128 573h57z" />
    <glyph glyph-name="Xi" unicode="&#x39e;" horiz-adv-x="611" 
d="M635 615h-494l11 52h494zM499 0h-494l11 52h494zM562 316h-481l11 52h482z" />
    <glyph glyph-name="Omicron" unicode="&#x39f;" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="Pi" unicode="&#x3a0;" horiz-adv-x="703" 
d="M567 0h-58l135 615h-422l-136 -615h-57l147 667h538z" />
    <glyph glyph-name="Rho" unicode="&#x3a1;" 
d="M86 0h-57l147 667h234q77 0 126 -49t49 -118q0 -40 -14 -78.5t-42.5 -71.5t-78 -53t-112.5 -20h-190zM159 329h186q85 0 132 48.5t47 118.5q0 48 -35.5 83.5t-87.5 35.5h-179z" />
    <glyph glyph-name="Sigma" unicode="&#x3a3;" horiz-adv-x="534" 
d="M210 615l184 -277l-303 -286h350l-10 -52h-424l11 55l308 291l-181 272l10 49h423l-12 -52h-356z" />
    <glyph glyph-name="Tau" unicode="&#x3a4;" horiz-adv-x="564" 
d="M257 0h-58l136 615h-218l11 52h494l-11 -52h-218z" />
    <glyph glyph-name="Upsilon" unicode="&#x3a5;" horiz-adv-x="618" 
d="M284 0h-58l63 285l-181 382h64l153 -328l297 328h74l-349 -382z" />
    <glyph glyph-name="Phi" unicode="&#x3a6;" horiz-adv-x="785" 
d="M367 0h-57l15 65q-122 9 -187 70t-65 159q0 125 107 214t267 93l15 66h57l-15 -67q121 -9 186 -70t65 -158q0 -124 -107 -213.5t-267 -94.5zM134 294q0 -74 53 -122t149 -55l99 432q-134 -7 -217.5 -79t-83.5 -176zM694 372q0 75 -53.5 122.5t-148.5 54.5l-99 -432
q132 7 216.5 79t84.5 176z" />
    <glyph glyph-name="Chi" unicode="&#x3a7;" horiz-adv-x="645" 
d="M572 0h-65l-170 302l-295 -302h-78l343 346l-181 321h67l157 -283l276 283h78l-323 -328z" />
    <glyph glyph-name="Psi" unicode="&#x3a8;" horiz-adv-x="766" 
d="M358 0h-58l28 125q-218 15 -218 199q0 36 10 79l61 264h58l-63 -274q-7 -26 -7 -65q0 -65 42 -105t129 -46l112 490h58l-112 -490q106 7 173.5 63t88.5 153l63 274h58l-66 -288q-25 -112 -112 -181.5t-217 -73.5z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="764" 
d="M34 52h157q-120 88 -120 237q0 101 45 189.5t130.5 144t190.5 55.5q134 0 215.5 -78t81.5 -212q0 -121 -62 -206t-156 -130h129l-10 -52h-228l11 52q107 22 180.5 115t73.5 219q0 109 -65 174t-171 65q-132 0 -219.5 -101.5t-87.5 -244.5q0 -81 37 -138.5t95 -88.5
l-11 -52h-227z" />
    <glyph glyph-name="Iotadieresis" unicode="&#x3aa;" horiz-adv-x="223" 
d="M86 0h-57l147 667h57zM370 789q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM166 789q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5z" />
    <glyph glyph-name="Upsilondieresis" unicode="&#x3ab;" horiz-adv-x="618" 
d="M284 0h-58l63 285l-181 382h64l153 -328l297 328h74l-349 -382zM568 789q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM364 789q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5
t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5z" />
    <glyph glyph-name="alphatonos" unicode="&#x3ac;" horiz-adv-x="585" 
d="M366 570h-30l62 195h64zM513 37l-6 -46q-12 -3 -32 -3q-85 0 -72 91q-69 -91 -169 -91q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-85 -384q-13 -64 35 -64q12 0 20 2zM248 35q49 0 93.5 27t70.5 66l51 232q-19 38 -61.5 63
t-98.5 25q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43z" />
    <glyph glyph-name="epsilontonos" unicode="&#x3ad;" horiz-adv-x="519" 
d="M351 570h-30l62 195h64zM226 -12q-100 0 -150 33.5t-50 90.5q0 43 31.5 75t68.5 45.5t72 15.5q-43 5 -75.5 31t-32.5 71q0 65 70 105t164 40q130 0 192 -77l-35 -30q-45 62 -157 62q-74 0 -126.5 -29t-52.5 -74q0 -38 40 -59t102 -21h112l-10 -44h-112q-86 0 -140 -25.5
t-54 -80.5q0 -39 38.5 -61.5t106.5 -22.5q112 0 196 70l23 -35q-98 -80 -221 -80z" />
    <glyph glyph-name="etatonos" unicode="&#x3ae;" horiz-adv-x="540" 
d="M355 570h-30l62 195h64zM363 -184h-52l114 512q6 27 6 35q0 43 -29.5 64t-78.5 21q-82 0 -164 -84l-80 -364h-52l107 483h52l-16 -73q87 85 174 85q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41z" />
    <glyph glyph-name="iotatonos" unicode="&#x3af;" horiz-adv-x="227" 
d="M194 570h-30l62 195h64zM118 -12q-95 0 -71 105l87 390h52l-85 -384q-15 -64 34 -64q13 0 21 2l-6 -46q-12 -3 -32 -3z" />
    <glyph glyph-name="upsilondieresistonos" unicode="&#x3b0;" horiz-adv-x="555" 
d="M373 570h-30l62 195h64zM542 605q0 -16 -12.5 -28.5t-29.5 -12.5q-14 0 -23.5 9.5t-9.5 23.5q0 17 12.5 29t28.5 12q14 0 24 -9.5t10 -23.5zM287 605q0 -17 -12.5 -29t-28.5 -12q-14 0 -24 9.5t-10 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM234 -12
q-101 0 -141.5 62t-17.5 164l62 269h52l-62 -273q-19 -83 8 -129t99 -46q96 0 161.5 84t65.5 201q0 93 -39 153l48 22q44 -68 44 -175q0 -137 -80.5 -234.5t-199.5 -97.5z" />
    <glyph glyph-name="alpha" unicode="&#x3b1;" horiz-adv-x="585" 
d="M513 37l-6 -46q-12 -3 -32 -3q-85 0 -72 91q-69 -91 -169 -91q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-85 -384q-13 -64 35 -64q12 0 20 2zM248 35q49 0 93.5 27t70.5 66l51 232q-19 38 -61.5 63t-98.5 25
q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43z" />
    <glyph glyph-name="beta" unicode="&#x3b2;" 
d="M260 -12q-61 0 -103 24t-61 64l-59 -260h-52l146 642q25 110 97 164.5t160 54.5q82 0 130.5 -38.5t48.5 -109.5q0 -42 -18 -77t-46 -56t-57.5 -33.5t-57.5 -17.5q45 -5 82.5 -40t37.5 -98q0 -94 -73 -156.5t-175 -62.5zM183 458l-77 -337q15 -37 54.5 -61.5t94.5 -24.5
q89 0 143.5 49t54.5 124q0 55 -41 83.5t-107 28.5h-32l10 46h32q80 0 137 41t57 116q0 49 -34.5 78t-91.5 29q-66 0 -123.5 -44.5t-76.5 -127.5z" />
    <glyph glyph-name="gamma" unicode="&#x3b3;" horiz-adv-x="481" 
d="M171 -184h-53l34 147q32 157 3.5 293t-102.5 227h64q55 -69 84 -188.5t15 -224.5q82 85 147.5 196.5t89.5 216.5h52q-27 -115 -105 -243t-186 -236z" />
    <glyph glyph-name="delta" unicode="&#x3b4;" 
d="M256 -12q-103 0 -158 56.5t-55 137.5q0 85 57 152t149 100q-70 38 -70 99q0 58 57 102t141 44q118 0 164 -71l-35 -31q-36 58 -131 58q-62 0 -102.5 -29.5t-40.5 -70.5q0 -29 29.5 -50t72 -38t85 -38.5t72 -63t29.5 -100.5q0 -100 -74 -178.5t-190 -78.5zM467 242
q0 35 -12.5 62t-38.5 46t-47.5 30t-58.5 26q-2 1 -3.5 1.5l-3 1t-3.5 1.5q-95 -27 -147.5 -87t-52.5 -137q0 -61 41 -106t118 -45q87 0 147.5 63t60.5 144z" />
    <glyph glyph-name="epsilon" unicode="&#x3b5;" horiz-adv-x="519" 
d="M226 -12q-100 0 -150 33.5t-50 90.5q0 43 31.5 75t68.5 45.5t72 15.5q-43 5 -75.5 31t-32.5 71q0 65 70 105t164 40q130 0 192 -77l-35 -30q-45 62 -157 62q-74 0 -126.5 -29t-52.5 -74q0 -38 40 -59t102 -21h112l-10 -44h-112q-86 0 -140 -25.5t-54 -80.5
q0 -39 38.5 -61.5t106.5 -22.5q112 0 196 70l23 -35q-98 -80 -221 -80z" />
    <glyph glyph-name="zeta" unicode="&#x3b6;" horiz-adv-x="491" 
d="M326 -44q0 18 -14 25t-45 7q-104 0 -165 48.5t-61 132.5q0 245 420 452h-305l11 46h375l-10 -42q-216 -111 -324.5 -218.5t-108.5 -227.5q0 -68 49 -106t135 -38q53 0 77 -14t24 -47q0 -47 -67 -131h-63q72 77 72 113z" />
    <glyph glyph-name="eta" unicode="&#x3b7;" horiz-adv-x="540" 
d="M363 -184h-52l114 512q6 27 6 35q0 43 -29.5 64t-78.5 21q-82 0 -164 -84l-80 -364h-52l107 483h52l-16 -73q87 85 174 85q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41z" />
    <glyph glyph-name="theta" unicode="&#x3b8;" 
d="M259 -12q-98 0 -149 68.5t-51 187.5q0 101 34.5 199t105 167t160.5 69q98 0 148 -69t50 -188q0 -101 -34 -198.5t-104 -166.5t-160 -69zM262 35q80 0 140.5 81t85.5 196h-369q-5 -43 -5 -75q0 -92 35.5 -147t112.5 -55zM354 632q-79 0 -139.5 -80t-85.5 -193h368
q5 40 5 70q0 92 -35.5 147.5t-112.5 55.5z" />
    <glyph glyph-name="iota" unicode="&#x3b9;" horiz-adv-x="227" 
d="M118 -12q-95 0 -71 105l87 390h52l-85 -384q-15 -64 34 -64q13 0 21 2l-6 -46q-12 -3 -32 -3z" />
    <glyph glyph-name="kappa" unicode="&#x3ba;" horiz-adv-x="505" 
d="M432 0h-64l-145 233l-112 -88l-32 -145h-52l107 483h52l-61 -272l342 272h73l-274 -218z" />
    <glyph glyph-name="lambda" unicode="&#x3bb;" horiz-adv-x="481" 
d="M146 624l2 48q25 7 52 7q87 -2 104 -90l116 -589h-56l-81 422l-269 -422h-61l315 487l-18 86q-7 33 -21.5 46t-39.5 13q-24 0 -43 -8z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="553" 
d="M88 44l-51 -228h-53l151 667h53l-74 -327q-14 -61 12.5 -92t77.5 -31q44 0 93 24t81 58l81 368h53l-85 -384q-15 -64 34 -64q13 0 21 2l-6 -46q-12 -3 -32 -3q-83 0 -73 86q-90 -86 -182 -86q-75 0 -101 56z" />
    <glyph glyph-name="nu" unicode="&#x3bd;" horiz-adv-x="481" 
d="M210 0h-53l-94 483h58l80 -429q93 97 160.5 210t91.5 219h52q-27 -116 -106.5 -245.5t-188.5 -237.5z" />
    <glyph glyph-name="xi" unicode="&#x3be;" horiz-adv-x="491" 
d="M274 -12q-118 0 -179 41t-61 118q0 44 18 80t46.5 58.5t60 36t63.5 17.5q-41 7 -74 37t-33 82q0 49 44.5 96t103.5 67h-106l12 46h363l-12 -46h-187q-64 -14 -112 -60t-48 -95q0 -48 40.5 -73.5t102.5 -25.5h141l-11 -46h-135q-94 0 -156.5 -43t-62.5 -124
q0 -57 49.5 -88t141.5 -31q53 0 77 -14t24 -47q0 -47 -67 -131h-58q67 80 67 113q0 32 -52 32z" />
    <glyph glyph-name="omicron" unicode="&#x3bf;" 
d="M258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="561" 
d="M416 0h-53l100 437h-274l-99 -437h-53l100 437h-80l10 46h539l-11 -46h-80z" />
    <glyph glyph-name="rho" unicode="&#x3c1;" horiz-adv-x="567" 
d="M271 -12q-57 0 -102.5 25.5t-71.5 68.5l-60 -266h-52l96 426q25 115 94 184t167 69q88 0 135 -56.5t47 -145.5q0 -123 -71.5 -214t-181.5 -91zM266 35q88 0 144.5 76t56.5 176q0 69 -33 115t-97 46q-76 0 -129.5 -57.5t-74.5 -148.5l-27 -119q19 -38 61.5 -63t98.5 -25z
" />
    <glyph glyph-name="sigma1" unicode="&#x3c2;" horiz-adv-x="479" 
d="M265 -12q-102 0 -160.5 56t-58.5 158q0 120 78 206.5t190 86.5q121 0 173 -88l-40 -30q-41 71 -131 71q-96 0 -155.5 -73.5t-59.5 -173.5q0 -81 48 -123.5t135 -42.5q98 0 98 -62q0 -46 -67 -130h-59q72 78 72 112q0 33 -63 33z" />
    <glyph glyph-name="sigma" unicode="&#x3c3;" 
d="M522 280q0 -116 -76 -204t-188 -88q-97 0 -154 58t-57 154q0 116 85.5 199.5t222.5 83.5h244l-10 -47h-129q62 -58 62 -156zM468 279q0 55 -20 96.5t-54 60.5h-47q-111 0 -177.5 -71t-66.5 -163q0 -77 41.5 -122t114.5 -45q88 0 148.5 74.5t60.5 169.5z" />
    <glyph glyph-name="tau" unicode="&#x3c4;" horiz-adv-x="463" 
d="M267 -12q-50 0 -77.5 26t-15.5 80l76 343h-176l11 46h405l-10 -46h-177l-76 -338q-7 -32 7.5 -48t40.5 -16q27 0 51 18l10 -42q-30 -23 -69 -23z" />
    <glyph glyph-name="upsilon" unicode="&#x3c5;" horiz-adv-x="555" 
d="M234 -12q-101 0 -141.5 62t-17.5 164l62 269h52l-62 -273q-19 -83 8 -129t99 -46q96 0 161.5 84t65.5 201q0 93 -39 153l48 22q44 -68 44 -175q0 -137 -80.5 -234.5t-199.5 -97.5z" />
    <glyph glyph-name="phi" unicode="&#x3c6;" horiz-adv-x="722" 
d="M677 278q0 -118 -93.5 -204t-251.5 -86h-2l-39 -172h-52l39 175q-116 10 -172.5 67.5t-56.5 153.5q0 97 65.5 170t172.5 113l16 -43q-198 -81 -198 -240q0 -160 184 -176l104 459h26q126 0 192 -58.5t66 -158.5zM434 447l-94 -412q136 2 208 75t72 168q0 164 -186 169z
" />
    <glyph glyph-name="chi" unicode="&#x3c7;" horiz-adv-x="479" 
d="M366 -184h-56l-96 294l-231 -294h-62l269 343l-106 324h56l89 -275l216 275h62l-254 -324z" />
    <glyph glyph-name="psi" unicode="&#x3c8;" horiz-adv-x="722" 
d="M291 -184h-52l39 174q-209 16 -209 171q0 28 10 71l58 251h52l-59 -259q-8 -34 -8 -59q0 -53 41 -87.5t126 -41.5l144 631h52l-145 -632q94 2 159.5 50t84.5 130l61 268h52l-63 -279q-23 -99 -104 -157.5t-200 -58.5z" />
    <glyph glyph-name="omega" unicode="&#x3c9;" horiz-adv-x="789" 
d="M750 330q0 -141 -68 -241.5t-161 -100.5q-78 0 -110.5 44t-34.5 107q-26 -64 -72 -107.5t-107 -43.5q-76 0 -114.5 51.5t-38.5 137.5q0 80 41.5 172.5t120.5 145.5l32 -37q-65 -46 -101.5 -122t-36.5 -157q0 -144 104 -144q59 0 102 58.5t63 148.5l28 123h52l-27 -123
q-20 -88 3 -147.5t94 -59.5q74 0 125.5 88.5t51.5 205.5q0 90 -39 136l42 30q51 -57 51 -165z" />
    <glyph glyph-name="iotadieresis" unicode="&#x3ca;" horiz-adv-x="227" 
d="M326 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM122 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM118 -12q-95 0 -71 105l87 390
h52l-85 -384q-15 -64 34 -64q13 0 21 2l-6 -46q-12 -3 -32 -3z" />
    <glyph glyph-name="upsilondieresis" unicode="&#x3cb;" horiz-adv-x="555" 
d="M507 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM303 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM234 -12q-101 0 -141.5 62
t-17.5 164l62 269h52l-62 -273q-19 -83 8 -129t99 -46q96 0 161.5 84t65.5 201q0 93 -39 153l48 22q44 -68 44 -175q0 -137 -80.5 -234.5t-199.5 -97.5z" />
    <glyph glyph-name="omicrontonos" unicode="&#x3cc;" 
d="M371 570h-30l62 195h64zM258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45z" />
    <glyph glyph-name="upsilontonos" unicode="&#x3cd;" horiz-adv-x="555" 
d="M373 570h-30l62 195h64zM234 -12q-101 0 -141.5 62t-17.5 164l62 269h52l-62 -273q-19 -83 8 -129t99 -46q96 0 161.5 84t65.5 201q0 93 -39 153l48 22q44 -68 44 -175q0 -137 -80.5 -234.5t-199.5 -97.5z" />
    <glyph glyph-name="omegatonos" unicode="&#x3ce;" horiz-adv-x="789" 
d="M480 570h-30l62 195h64zM750 330q0 -141 -68 -241.5t-161 -100.5q-78 0 -110.5 44t-34.5 107q-26 -64 -72 -107.5t-107 -43.5q-76 0 -114.5 51.5t-38.5 137.5q0 80 41.5 172.5t120.5 145.5l32 -37q-65 -46 -101.5 -122t-36.5 -157q0 -144 104 -144q59 0 102 58.5
t63 148.5l28 123h52l-27 -123q-20 -88 3 -147.5t94 -59.5q74 0 125.5 88.5t51.5 205.5q0 90 -39 136l42 30q51 -57 51 -165z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="564" 
d="M549 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM345 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM452 0h-423l147 667h423
l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="773" 
d="M442 -12l11 52q68 0 117.5 44t65.5 121l2 6q19 86 -15 130t-118 44q-81 0 -169 -21l-79 -364h-58l135 615h-218l11 52h494l-10 -52h-219l-44 -200q90 22 172 22q110 0 154 -61t22 -165l-2 -6q-24 -108 -93.5 -162.5t-158.5 -54.5z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="542" 
d="M601 867l-204 -144h-46l186 144h64zM86 0h-57l147 667h423l-11 -52h-366z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="672" 
d="M367 -12q-132 0 -216 79.5t-84 209.5q0 88 31.5 164.5t83.5 127.5t118.5 80t136.5 29q88 0 156 -37t106 -106l-55 -25q-64 116 -211 116q-101 0 -185 -70.5t-110 -187.5h388l-12 -52h-384q-1 -12 -1 -37q0 -108 65.5 -173.5t175.5 -65.5q102 0 188 76l41 -34
q-98 -94 -232 -94z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="581" 
d="M279 -12q-84 0 -153.5 33t-101.5 84l42 41q75 -106 218 -106q82 0 125 44.5t43 100.5q0 40 -34.5 69.5t-83 49.5t-97.5 41.5t-83.5 58.5t-34.5 88q0 76 65.5 131t159.5 55q75 0 137 -28.5t93 -75.5l-43 -40q-30 45 -81.5 68.5t-110.5 23.5q-65 0 -111.5 -39t-46.5 -89
q0 -37 34.5 -64.5t83.5 -47.5t98 -42t83.5 -60.5t34.5 -91.5q0 -81 -63 -142.5t-173 -61.5z" />
    <glyph glyph-name="afii10055" unicode="&#x406;" horiz-adv-x="223" 
d="M86 0h-57l147 667h57z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="223" 
d="M367 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM163 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM86 0h-57l147 667h57z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="470" 
d="M156 -12q-58 0 -104.5 22.5t-70.5 65.5l38 40q41 -76 134 -76q66 0 107.5 41t57.5 112l104 474h58l-104 -474q-46 -205 -220 -205z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="1051" 
d="M-21 -12l11 52q34 0 60.5 12t55 44t57 91.5t60.5 150.5l115 329h375l-61 -277h185q90 0 134 -53.5t32 -132.5q-12 -84 -76.5 -144t-168.5 -60h-249l135 615h-266l-99 -282q-38 -109 -74.5 -179.5t-74 -105.5t-72 -47.5t-79.5 -12.5zM826 338h-186l-63 -286h186
q70 0 119 42t60 106q11 61 -21 99.5t-95 38.5z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="1051" 
d="M769 0h-260l69 317h-422l-70 -317h-57l146 667h57l-65 -298h422l66 298h58l-66 -298h196q85 0 127.5 -51t30.5 -125q-12 -80 -73 -136.5t-159 -56.5zM833 317h-197l-59 -265h198q65 0 110 38.5t56 98.5q10 56 -20 92t-88 36z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="781" 
d="M592 0l47 218q18 81 -16.5 124t-117.5 43q-81 0 -169 -21l-79 -364h-58l135 615h-218l11 52h494l-10 -52h-219l-44 -200q90 22 172 22q110 0 154.5 -59.5t22.5 -159.5l-47 -218h-58z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="586" 
d="M600 867l-204 -144h-46l186 144h64zM508 0h-68l-208 318l-94 -85l-52 -233h-57l147 667h57l-79 -358l396 358h78l-354 -318z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="618" 
d="M84 -12q-83 0 -112 59l40 43q28 -50 81 -50q36 0 61 20t70 79l36 47l-153 481h62l133 -431l323 431h70l-422 -554q-49 -65 -90.5 -95t-98.5 -30zM595 796q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="703" 
d="M241 -127l27 127h-239l146 667h57l-135 -615h423l135 615h58l-146 -667h-241l-27 -127h-58z" />
    <glyph glyph-name="afii10017" unicode="&#x410;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" 
d="M175 667h416l-11 -52h-358l-50 -225h177q76 0 125.5 -49t49.5 -118q0 -88 -62 -155.5t-183 -67.5h-250zM340 338h-180l-63 -286h187q85 0 132 48.5t47 118.5q0 48 -35.5 83.5t-87.5 35.5z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="614" 
d="M314 0h-285l147 667h257q69 0 117 -42t48 -109q0 -70 -45.5 -121t-109.5 -59q42 -10 69.5 -50t27.5 -88q0 -82 -58.5 -140t-167.5 -58zM386 368q77 0 113 42t36 100q0 45 -33.5 75t-81.5 30h-198l-54 -247h218zM317 52q76 0 120 44.5t44 104.5q0 50 -33 82.5t-89 32.5
h-203l-59 -264h220z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="542" 
d="M86 0h-57l147 667h423l-11 -52h-366z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="717" 
d="M-46 -123l37 172q36 2 61.5 13t54 40.5t56.5 87t60 148.5l115 329h375l-136 -615h60l-38 -175h-57l27 123h-531l-27 -123h-57zM378 615l-99 -282q-79 -226 -175 -281h416l124 563h-266z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="564" 
d="M452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="892" 
d="M48 0h-78l387 349l-214 318h68l224 -339l74 339h58l-72 -326l359 326h79l-354 -318l235 -349h-68l-208 317l-59 -52l-58 -265h-58l57 263l-31 48z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="573" 
d="M255 -12q-88 0 -157.5 38.5t-92.5 100.5l46 30q23 -54 78 -85.5t126 -31.5q85 0 135.5 45t50.5 115q0 53 -46 85t-125 32h-119l12 52h152q74 0 129.5 36t55.5 101q0 51 -47 85t-125 34q-107 0 -181 -70l-28 40q38 38 94.5 60t117.5 22q101 0 164 -44t63 -122
q0 -74 -60 -121t-131 -51q54 -9 92.5 -47.5t38.5 -94.5q0 -87 -67.5 -148t-175.5 -61z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="699" 
d="M85 0h-56l147 667h58l-127 -573l544 573h58l-147 -667h-58l129 582z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="699" 
d="M85 0h-56l147 667h58l-127 -573l544 573h58l-147 -667h-58l129 582zM640 796q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="586" 
d="M508 0h-68l-208 318l-94 -85l-52 -233h-57l147 667h57l-79 -358l396 358h78l-354 -318z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="703" 
d="M-22 -12l11 52q69 0 121 61t113 237l115 329h375l-146 -667h-58l135 615h-266l-99 -282q-31 -88 -61 -150.5t-57.5 -100t-59 -59t-59.5 -28.5t-64 -7z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="790" 
d="M653 0h-58l131 595l-374 -595h-22l-112 595l-132 -595h-57l147 667h82l104 -556l350 556h88z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="703" 
d="M567 0h-58l70 317h-423l-70 -317h-57l147 667h57l-65 -298h422l66 298h58z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="703" 
d="M567 0h-58l135 615h-422l-136 -615h-57l147 667h538z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" 
d="M86 0h-57l147 667h234q77 0 126 -49t49 -118q0 -40 -14 -78.5t-42.5 -71.5t-78 -53t-112.5 -20h-190zM159 329h186q85 0 132 48.5t47 118.5q0 48 -35.5 83.5t-87.5 35.5h-179z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="672" 
d="M367 -12q-132 0 -216 79.5t-84 209.5q0 88 31.5 164.5t83.5 127.5t118.5 80t136.5 29q88 0 156 -37t106 -106l-55 -25q-64 116 -211 116q-120 0 -212 -97t-92 -250q0 -108 65.5 -173.5t175.5 -65.5q102 0 188 76l41 -34q-98 -94 -232 -94z" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="564" 
d="M257 0h-58l136 615h-218l11 52h494l-11 -52h-218z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="618" 
d="M84 -12q-83 0 -112 59l40 43q28 -50 81 -50q36 0 61 20t70 79l36 47l-153 481h62l133 -431l323 431h70l-422 -554q-49 -65 -90.5 -95t-98.5 -30z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="785" 
d="M367 0h-57l15 65q-122 9 -187 70t-65 159q0 125 107 214t267 93l15 66h57l-15 -67q121 -9 186 -70t65 -158q0 -124 -107 -213.5t-267 -94.5zM134 294q0 -74 53 -122t149 -55l99 432q-134 -7 -217.5 -79t-83.5 -176zM694 372q0 75 -53.5 122.5t-148.5 54.5l-99 -432
q132 7 216.5 79t84.5 176z" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="645" 
d="M572 0h-65l-170 302l-295 -302h-78l343 346l-181 321h67l157 -283l276 283h78l-323 -328z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="717" 
d="M542 -123l27 123h-540l146 667h57l-135 -615h423l135 615h58l-136 -615h60l-38 -175h-57z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="595" 
d="M211 667l-37 -171q-18 -81 17 -124t118 -43q81 0 168 21l70 317h57l-146 -667h-57l65 299q-92 -22 -173 -22q-110 0 -154.5 59.5t-22.5 159.5l38 171h57z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="962" 
d="M914 667h57l-146 -667h-796l146 667h57l-135 -615h312l135 615h58l-135 -615h312z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="975" 
d="M914 667h57l-135 -615h60l-38 -175h-58l27 123h-798l146 667h57l-135 -615h312l135 615h58l-135 -615h312z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="741" 
d="M448 0h-249l135 615h-218l11 52h276l-61 -277h177q77 0 126 -49t49 -118q0 -40 -14 -78.5t-42.5 -71.5t-77.5 -53t-112 -20zM509 338h-178l-64 -286h186q86 0 133 48.5t47 118.5q0 48 -35.5 83.5t-88.5 35.5z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="754" 
d="M617 0h-57l146 667h57zM175 667h57l-60 -277h176q77 0 126.5 -49t49.5 -118q0 -40 -14 -78.5t-42.5 -71.5t-77.5 -53t-112 -20h-249zM338 338h-178l-63 -286h186q86 0 133 48.5t47 118.5q0 48 -35.5 83.5t-89.5 35.5z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" 
d="M175 667h57l-60 -277h176q77 0 126.5 -49t49.5 -118q0 -40 -14 -78.5t-42.5 -71.5t-77.5 -53t-112 -20h-249zM338 338h-178l-63 -286h186q86 0 133 48.5t47 118.5q0 48 -35.5 83.5t-89.5 35.5z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="672" 
d="M344 678q132 0 215.5 -80t83.5 -209q0 -88 -31.5 -164.5t-83 -127.5t-118 -80t-136.5 -29q-89 0 -157 37t-105 106l54 25q64 -116 212 -116q104 0 189.5 75.5t107.5 200.5h-378l11 52h372q1 6 1 19q0 108 -65.5 173.5t-175.5 65.5q-102 0 -188 -76l-41 35q99 93 233 93z
" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="974" 
d="M548 -12q-141 0 -214 92.5t-50 236.5h-128l-70 -317h-57l146 667h57l-65 -298h127q36 135 140.5 222t239.5 87q140 0 213 -93t51 -237q-23 -153 -132.5 -256.5t-257.5 -103.5zM555 40q120 0 210.5 87t111.5 219q21 124 -37 202t-173 78q-121 0 -211 -86t-111 -220
q-21 -124 36.5 -202t173.5 -78z" />
    <glyph glyph-name="afii10049" unicode="&#x42f;" horiz-adv-x="594" 
d="M71 0h-75l256 280q-56 9 -100.5 52t-44.5 112q0 93 66 158t183 65h248l-148 -667h-57l61 276h-140zM300 328h171l63 287h-186q-84 0 -131.5 -48.5t-47.5 -119.5q0 -51 38 -85t93 -34z" />
    <glyph glyph-name="afii10065" unicode="&#x430;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="577" 
d="M259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45zM258 -12q-97 0 -155 58t-58 154q0 71 35 160q45 121 122 180.5t199 74.5q78 12 109 24t35 28h49q-5 -24 -23 -41.5t-50.5 -28.5t-58 -16.5t-67.5 -11.5
q-118 -18 -175.5 -63.5t-92.5 -127.5q76 117 198 117q91 0 145 -58t54 -154q0 -118 -77 -206.5t-189 -88.5z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="541" 
d="M279 0h-252l106 483h218q57 0 94.5 -27.5t37.5 -79.5q0 -48 -29 -84t-80 -46q30 -12 49.5 -40t19.5 -62q0 -63 -44 -103.5t-120 -40.5zM89 47h190q48 0 79 28.5t31 71.5q0 34 -23.5 56.5t-64.5 22.5h-173zM138 272h191q46 0 72 28t26 67q0 34 -25.5 52t-66.5 18h-160z
" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="425" 
d="M450 483l-11 -46h-264l-96 -437h-52l106 483h317z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="553" 
d="M-63 -123l36 166q39 5 72.5 47.5t72.5 151.5l85 241h308l-95 -436h57l-37 -170h-53l28 123h-394l-28 -123h-52zM239 437l-71 -201q-54 -152 -115 -189h310l85 390h-209z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="567" 
d="M276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5
t-70.5 -129.5z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="695" 
d="M39 0h-74l282 265l-177 218h65l187 -233l51 233h53l-49 -224l279 224h73l-273 -218l166 -265h-65l-145 232l-50 -39l-42 -193h-53l42 193l-29 35z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="519" 
d="M305 495q92 0 139.5 -31.5t47.5 -88.5q0 -61 -52.5 -95t-110.5 -38q44 -6 79.5 -33.5t35.5 -74.5q0 -66 -66 -106t-156 -40q-148 0 -211 84l34 31q58 -70 178 -70q69 0 118 29.5t49 74.5q0 41 -42 63.5t-104 22.5h-116l10 44h112q82 0 133 26t51 78q0 38 -34.5 58.5
t-97.5 20.5q-111 0 -195 -63l-25 36q95 72 223 72z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="540" 
d="M77 0h-50l106 483h52l-89 -402l364 402h51l-106 -483h-52l89 408z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="540" 
d="M77 0h-50l106 483h52l-89 -402l364 402h51l-106 -483h-52l89 408zM524 629q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="505" 
d="M432 0h-64l-145 233l-112 -88l-32 -145h-52l107 483h52l-61 -272l342 272h73l-274 -218z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="540" 
d="M-38 -12l10 45q36 0 71 48.5t75 160.5l85 241h308l-106 -483h-52l95 437h-209l-71 -201q-47 -135 -95 -191.5t-111 -56.5z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="618" 
d="M483 0h-53l90 412l-255 -412h-19l-77 412l-90 -412h-52l106 483h68l71 -392l243 392h74z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" horiz-adv-x="540" 
d="M79 0h-52l110 483h52l-47 -208h273l48 208h52l-110 -483h-52l52 228h-274z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" 
d="M258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" horiz-adv-x="540" 
d="M405 0h-52l99 437h-273l-100 -437h-52l110 483h378z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" horiz-adv-x="567" 
d="M271 -12q-58 0 -103 25.5t-71 68.5l-59 -266h-52l148 667h52l-16 -73q65 85 166 85q84 0 136 -53.5t52 -145.5q0 -125 -71 -216.5t-182 -91.5zM266 35q88 0 144.5 76t56.5 176q0 74 -40 117.5t-106 43.5q-49 0 -93 -26.5t-71 -66.5l-51 -232q19 -38 61.5 -63t98.5 -25z
" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="494" 
d="M261 -12q-96 0 -155.5 57t-59.5 157q0 120 78 206.5t190 86.5q121 0 173 -88l-40 -30q-41 71 -131 71q-96 0 -155.5 -73.5t-59.5 -173.5q0 -80 46 -123t119 -43q79 0 131 62l30 -36q-70 -73 -166 -73z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="391" 
d="M167 0h-52l95 437h-141l10 46h337l-11 -46h-142z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="481" 
d="M-46 -186l20 50q18 -9 47 -9q39 0 76 55l57 87l-94 486h56l81 -422l269 422h61l-381 -589q-57 -90 -135 -90q-32 0 -57 10z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="772" 
d="M262 495q46 0 81.5 -24.5t50.5 -65.5l58 262h52l-56 -257q62 85 139 85q66 0 101 -51t35 -132q0 -78 -27 -152t-78 -123t-111 -49q-46 0 -81.5 24.5t-50.5 65.5l-58 -262h-52l56 257q-59 -85 -138 -85q-67 0 -102 51t-35 132q0 78 27 152t78 123t111 49zM270 448
q-48 0 -88 -41.5t-61 -103.5t-21 -126q0 -68 27 -105t75 -37q71 0 130 87l53 238q-10 36 -42 62t-73 26zM500 35q71 0 120.5 85.5t49.5 186.5q0 67 -27.5 104t-75.5 37q-68 0 -130 -86l-52 -239q9 -36 41 -62t74 -26z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="479" 
d="M408 0h-59l-113 213l-207 -213h-66l245 248l-127 235h59l105 -199l193 199h66l-233 -235z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="553" 
d="M383 -123l28 123h-384l106 483h52l-96 -436h273l97 436h52l-96 -436h58l-37 -170h-53z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="535" 
d="M128 483h52l-31 -140q-16 -73 9.5 -101t92.5 -28q86 0 150 33l53 236h52l-106 -483h-52l44 204q-77 -36 -162 -36q-90 0 -122.5 38t-13.5 125z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="777" 
d="M696 483h52l-106 -483h-615l106 483h52l-96 -436h228l97 436h52l-96 -436h229z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="793" 
d="M696 483h52l-96 -436h58l-37 -170h-53l28 123h-621l106 483h52l-96 -436h228l97 436h52l-96 -436h229z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="597" 
d="M358 0h-243l95 437h-141l10 46h194l-44 -197h180q67 0 100 -39t24 -98q-9 -62 -56.5 -105.5t-118.5 -43.5zM399 240h-180l-42 -193h185q47 0 78 27.5t38 71.5q8 43 -12 68.5t-67 25.5z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="684" 
d="M549 0h-53l106 483h53zM133 483h52l-44 -197h168q64 0 100.5 -33.5t36.5 -88.5q0 -68 -47 -116t-123 -48h-249zM299 240h-168l-42 -193h192q49 0 79.5 32.5t30.5 81.5q0 37 -24 58t-68 21z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="509" 
d="M133 483h52l-44 -197h168q64 0 100.5 -33.5t36.5 -88.5q0 -68 -47 -116t-123 -48h-249zM299 240h-168l-42 -193h192q49 0 79.5 32.5t30.5 81.5q0 37 -24 58t-68 21z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="494" 
d="M261 495q96 0 155.5 -57t59.5 -157q0 -120 -78 -206.5t-190 -86.5q-121 0 -173 88l38 29q41 -72 132 -72q82 0 137.5 53.5t72.5 135.5h-263l10 47h259v12q0 81 -46 125t-118 44q-81 0 -133 -62l-29 34q70 73 166 73z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="750" 
d="M437 -12q-97 0 -155 58t-58 154q0 18 1 27h-96l-50 -227h-52l106 483h52l-46 -210h93q22 95 93 158.5t164 63.5q98 0 156 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM438 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5
q0 -77 41.5 -122t115.5 -45z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" horiz-adv-x="537" 
d="M77 0h-65l180 201q-47 7 -75 37.5t-28 80.5q0 68 46.5 116t122.5 48h251l-106 -483h-53l43 197h-145zM224 243h179l43 194h-192q-50 0 -80.5 -33t-30.5 -82q0 -36 22.5 -57.5t58.5 -21.5z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="567" 
d="M503 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM299 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM276 -12q-107 0 -168.5 58.5
t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5t-70.5 -129.5z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="541" 
d="M359 538h-162l-27 -128q36 34 89.5 59.5t102.5 25.5q152 0 119 -155l-89 -402q-14 -66 -56 -100t-105 -34q-56 0 -90 37l31 41q25 -33 64 -33q38 0 66 22t38 67l85 391q15 66 -9 92.5t-81 26.5q-45 0 -94 -24t-81 -59l-80 -365h-52l117 538h-72l8 38h72l21 91h52l-20 -91
h160z" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="425" 
d="M509 700l-204 -144h-46l186 144h64zM450 483l-11 -46h-264l-96 -437h-52l106 483h317z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="494" 
d="M261 -12q-96 0 -155.5 57t-59.5 157q0 120 78 206.5t190 86.5q121 0 173 -88l-38 -28q-41 71 -132 71q-79 0 -135 -51t-74 -130h263l-10 -47h-260v-20q0 -81 46 -125t118 -44q80 0 133 62l29 -34q-70 -73 -166 -73z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="459" 
d="M202 -12q-133 0 -194 85l37 36q20 -32 62.5 -54.5t95.5 -22.5q58 0 93 29.5t35 70.5q0 28 -26.5 49.5t-64.5 36t-76 30t-64.5 42t-26.5 62.5q0 61 47.5 102t128.5 41q59 0 107 -23t69 -56l-34 -34q-16 29 -57.5 49t-88.5 20q-54 0 -86.5 -24.5t-32.5 -62.5
q0 -27 26.5 -47t64.5 -34t76 -30t64.5 -44t26.5 -66q0 -64 -49 -109.5t-133 -45.5z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="214" 
d="M180 559q-15 0 -25.5 10.5t-10.5 24.5q0 19 14 31.5t31 12.5q15 0 25.5 -10t10.5 -25q0 -19 -14 -31.5t-31 -12.5zM79 0h-52l107 483h52z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="214" 
d="M325 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM121 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM79 0h-52l107 483h52z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="214" 
d="M-74 -196q-72 0 -107 42l28 38q26 -35 72 -35q73 0 94 89l121 545h52l-121 -545q-15 -68 -48.5 -101t-90.5 -33zM180 559q-15 0 -25.5 10.5t-10.5 24.5q0 19 14 31.5t31 12.5q15 0 25.5 -10t10.5 -25q0 -19 -14 -31.5t-31 -12.5z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="836" 
d="M-39 -12l10 45q37 0 72 48t75 161l85 241h308l-43 -197h167q64 0 100.5 -33.5t36.5 -88.5q0 -68 -46.5 -116t-122.5 -48h-250l95 437h-209l-71 -201q-47 -135 -95.5 -191.5t-111.5 -56.5zM625 240h-168l-42 -193h192q50 0 80 32.5t30 81.5q0 37 -24 58t-68 21z" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="836" 
d="M79 0h-52l106 483h52l-46 -210h274l46 210h52l-46 -210h176q61 0 96 -32t35 -84q0 -65 -44.5 -111t-117.5 -46h-257l50 227h-274zM631 227h-176l-40 -180h199q46 0 74.5 30.5t28.5 75.5q0 35 -22 54.5t-64 19.5z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="541" 
d="M406 0h-52l72 328q6 27 6 35q0 43 -29.5 64t-78.5 21q-81 0 -164 -85l-80 -363h-52l118 538h-71l8 38h72l20 91h52l-20 -91h162l-9 -38h-161l-28 -128q87 85 174 85q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" horiz-adv-x="505" 
d="M525 700l-204 -144h-46l186 144h64zM432 0h-64l-145 233l-112 -88l-32 -145h-52l107 483h52l-61 -272l342 272h73l-274 -218z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="481" 
d="M-46 -186l20 50q18 -9 47 -9q39 0 76 55l57 87l-94 486h56l81 -422l269 422h61l-381 -589q-57 -90 -135 -90q-32 0 -57 10zM492 629q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="540" 
d="M162 -126l27 126h-162l106 483h52l-96 -436h273l97 436h52l-106 -483h-164l-27 -126h-52z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="542" 
d="M86 0h-57l146 667h365l27 124h58l-38 -176h-365z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="425" 
d="M175 437l-96 -437h-52l106 483h264l27 126h53l-38 -172h-264z" />
    <glyph glyph-name="uni1E02" unicode="&#x1e02;" horiz-adv-x="614" 
d="M314 0h-285l147 667h257q69 0 117 -42t48 -109q0 -70 -45.5 -121t-109.5 -59q42 -10 69.5 -50t27.5 -88q0 -82 -58.5 -140t-167.5 -58zM386 368q77 0 113 42t36 100q0 45 -33.5 75t-81.5 30h-198l-54 -247h218zM317 52q76 0 120 44.5t44 104.5q0 50 -33 82.5t-89 32.5
h-203l-59 -264h220zM461 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E03" unicode="&#x1e03;" 
d="M271 -12q-58 0 -103 25.5t-71 68.5l-18 -82h-52l147 667h52l-56 -257q65 85 166 85q84 0 136 -53.5t52 -145.5q0 -125 -71 -216.5t-182 -91.5zM266 35q88 0 144.5 76t56.5 176q0 74 -40 117.5t-106 43.5q-49 0 -93 -26.5t-71 -66.5l-51 -232q19 -38 61.5 -63t98.5 -25z
M449 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E0A" unicode="&#x1e0a;" horiz-adv-x="692" 
d="M241 0h-212l147 667h208q113 0 192 -81t79 -203q0 -56 -16 -110.5t-50 -104t-81.5 -87t-116.5 -59.5t-150 -22zM246 52h5q154 0 249 96t95 233q0 101 -63.5 167.5t-158.5 66.5h-151l-125 -563h149zM505 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5
q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E0B" unicode="&#x1e0b;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l58 266h53l-148 -667h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M431 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E1E" unicode="&#x1e1e;" horiz-adv-x="542" 
d="M446 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24zM86 0h-57l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358z" />
    <glyph glyph-name="uni1E1F" unicode="&#x1e1f;" horiz-adv-x="265" 
d="M368 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24zM97 0h-53l97 437h-80l10 46h80l9 44q35 150 151 150q58 0 95 -37l-30 -33q-23 25 -65 25q-40 0 -63 -26t-35 -79l-10 -44h99l-11 -46h-98z" />
    <glyph glyph-name="uni1E22" unicode="&#x1e22;" horiz-adv-x="703" 
d="M567 0h-58l70 317h-423l-70 -317h-57l147 667h57l-65 -298h422l66 298h58zM504 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E23" unicode="&#x1e23;" horiz-adv-x="540" 
d="M405 0h-52l72 328q6 27 6 35q0 43 -29.5 64t-78.5 21q-81 0 -164 -85l-80 -363h-52l147 667h52l-56 -257q87 85 174 85q64 0 102.5 -30.5t38.5 -87.5q0 -19 -6 -41zM426 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10
t9.5 -24z" />
    <glyph glyph-name="uni1E56" unicode="&#x1e56;" 
d="M86 0h-57l147 667h234q77 0 126 -49t49 -118q0 -40 -14 -78.5t-42.5 -71.5t-78 -53t-112.5 -20h-190zM159 329h186q85 0 132 48.5t47 118.5q0 48 -35.5 83.5t-87.5 35.5h-179zM452 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13
q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E57" unicode="&#x1e57;" horiz-adv-x="567" 
d="M271 -12q-58 0 -103 25.5t-71 68.5l-59 -266h-52l148 667h52l-16 -73q65 85 166 85q84 0 136 -53.5t52 -145.5q0 -125 -71 -216.5t-182 -91.5zM266 35q88 0 144.5 76t56.5 176q0 74 -40 117.5t-106 43.5q-49 0 -93 -26.5t-71 -66.5l-51 -232q19 -38 61.5 -63t98.5 -25z
M412 603q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E60" unicode="&#x1e60;" horiz-adv-x="581" 
d="M279 -12q-84 0 -153.5 33t-101.5 84l42 41q75 -106 218 -106q82 0 125 44.5t43 100.5q0 40 -34.5 69.5t-83 49.5t-97.5 41.5t-83.5 58.5t-34.5 88q0 76 65.5 131t159.5 55q75 0 137 -28.5t93 -75.5l-43 -40q-30 45 -81.5 68.5t-110.5 23.5q-65 0 -111.5 -39t-46.5 -89
q0 -37 34.5 -64.5t83.5 -47.5t98 -42t83.5 -60.5t34.5 -91.5q0 -81 -63 -142.5t-173 -61.5zM446 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E61" unicode="&#x1e61;" horiz-adv-x="459" 
d="M202 -12q-133 0 -194 85l37 36q20 -32 62.5 -54.5t95.5 -22.5q58 0 93 29.5t35 70.5q0 28 -26.5 49.5t-64.5 36t-76 30t-64.5 42t-26.5 62.5q0 61 47.5 102t128.5 41q59 0 107 -23t69 -56l-34 -34q-16 29 -57.5 49t-88.5 20q-54 0 -86.5 -24.5t-32.5 -62.5
q0 -27 26.5 -47t64.5 -34t76 -30t64.5 -44t26.5 -66q0 -64 -49 -109.5t-133 -45.5zM342 603q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E6A" unicode="&#x1e6a;" horiz-adv-x="564" 
d="M257 0h-58l136 615h-218l11 52h494l-11 -52h-218zM435 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E6B" unicode="&#x1e6b;" horiz-adv-x="275" 
d="M151 -12q-96 0 -96 81q0 11 3 26l76 342h-80l10 46h80l29 132h53l-29 -132h98l-11 -46h-98l-74 -335q-2 -10 -2 -21q0 -46 49 -46q27 0 51 18l10 -42q-30 -23 -69 -23zM271 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10
t9.5 -24z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="867" 
d="M604 0h-64l-34 573l-286 -573h-64l-44 667h63l30 -587l294 587h52l34 -587l290 587h66zM592 723h-44l-140 144h62z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="719" 
d="M491 0h-50l-44 413l-227 -413h-50l-50 483h53l37 -410l228 410h44l45 -410l220 410h58zM481 556h-44l-140 144h62z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="867" 
d="M729 867l-204 -144h-46l186 144h64zM604 0h-64l-34 573l-286 -573h-64l-44 667h63l30 -587l294 587h52l34 -587l290 587h66z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="719" 
d="M621 700l-204 -144h-46l186 144h64zM491 0h-50l-44 413l-227 -413h-50l-50 483h53l37 -410l228 410h44l45 -410l220 410h58z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="867" 
d="M686 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM482 772q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM604 0h-64l-34 573l-286 -573
h-64l-44 667h63l30 -587l294 587h52l34 -587l290 587h66z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="719" 
d="M576 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM372 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM491 0h-50l-44 413l-227 -413
h-50l-50 483h53l37 -410l228 410h44l45 -410l220 410h58z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM276 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M232 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM394 761l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M312 591l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="647" 
d="M538 682h-38l-62 85l-109 -85h-41l129 108h54zM578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM761 845l-204 -108h-46l186 108h64z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M472 556h-38l-62 113l-109 -113h-41l129 144h54zM696 770l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="647" 
d="M538 682h-38l-62 85l-109 -85h-41l129 108h54zM578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM342 737h-44l-140 108h62z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M472 556h-38l-62 113l-109 -113h-41l129 144h54zM277 626h-44l-140 144h62z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="647" 
d="M539 682h-38l-62 85l-109 -85h-41l129 108h54zM578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM471 804l-24 10q24 34 71 34q26 0 45 -11.5t19 -31.5q0 -27 -31 -49h-36q36 24 36 47q0 12 -10 18.5t-24 6.5q-29 0 -46 -24z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M472 556h-38l-62 113l-109 -113h-41l129 144h54zM397 731l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="647" 
d="M535 677h-38l-62 56l-109 -56h-41l129 72h54zM578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM499 757q-39 0 -64 25.5t-53 25.5q-46 0 -69 -48h-36q27 67 112 67q25 0 41.5 -8t23.5 -17.5t20 -17.5t32 -8q46 0 69 48h37
q-28 -67 -113 -67z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M464 523h-38l-62 113l-109 -113h-41l129 144h54zM446 682q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM554 723h-38l-62 113l-109 -113h-41l129 144h54zM276 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M472 556h-38l-62 113l-109 -113h-41l129 144h54zM232 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM609 842l-204 -103h-46l186 103h64zM607 765q-85 -82 -194 -82q-52 0 -93.5 22t-63.5 60l33 23q37 -69 130 -69q94 0 163 69z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M528 761l-204 -144h-46l186 144h64zM527 607q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM496 745h-44l-140 103h62zM608 765q-85 -82 -194 -82q-52 0 -93.5 22t-63.5 60l33 23q37 -69 130 -69q94 0 163 69z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M432 617h-44l-140 144h62zM528 612q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM400 788l-24 9q24 33 71 33q26 0 45 -11t19 -30q0 -27 -31 -47h-36q36 22 36 45q0 11 -10 17.5t-24 6.5q-28 0 -46 -23zM608 765q-85 -82 -194 -82q-52 0 -93.5 22t-63.5 60l33 23
q37 -69 130 -69q94 0 163 69z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M330 680l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 32 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32zM529 612q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM603 740q-86 -56 -194 -56q-109 0 -157 56l33 16q38 -48 130 -48t163 48zM500 762q-25 0 -41.5 8t-23 17.5t-20 17.5t-32.5 8q-46 0 -69 -48h-36q27 67 112 67q32 0 49 -13t30.5 -25.5
t37.5 -12.5q46 0 69 48h37q-28 -67 -113 -67z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M531 619q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75zM445 681q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="647" 
d="M578 0h-63l-31 164h-352l-102 -164h-69l420 667h71zM478 216l-72 390l-244 -390h316zM276 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24zM614 796q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25
q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" 
d="M299 495q57 0 102.5 -25.5t71.5 -68.5l17 82h53l-107 -483h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z
M232 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24zM533 629q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="564" 
d="M452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365zM246 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="567" 
d="M276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5
t-70.5 -129.5zM238 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="564" 
d="M452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365zM365 761l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="567" 
d="M276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5
t-70.5 -129.5zM318 591l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="564" 
d="M452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365zM470 721q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="567" 
d="M276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5
t-70.5 -129.5zM424 554q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="564" 
d="M509 682h-38l-62 85l-109 -85h-41l129 108h54zM731 845l-204 -108h-46l186 108h64zM452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="567" 
d="M478 556h-38l-62 113l-109 -113h-41l129 144h54zM703 770l-204 -144h-46l186 144h64zM276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38
q-77 -59 -168 -59zM108 266h360q1 5 1 22q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5t-70.5 -129.5z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="564" 
d="M509 682h-38l-62 85l-109 -85h-41l129 108h54zM452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365zM312 737h-44l-140 108h62z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="567" 
d="M479 556h-38l-62 113l-109 -113h-41l129 144h54zM276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22
q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5t-70.5 -129.5zM283 626h-44l-140 144h62z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="564" 
d="M510 682h-38l-62 85l-109 -85h-41l129 108h54zM452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365zM441 804l-24 10q24 34 71 34q26 0 45 -11.5t19 -31.5q0 -27 -31 -49h-36q36 24 36 47q0 12 -10 18.5t-24 6.5q-29 0 -46 -24z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="567" 
d="M479 556h-38l-62 113l-109 -113h-41l129 144h54zM276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22
q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5t-70.5 -129.5zM404 731l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="564" 
d="M513 678h-38l-62 56l-109 -56h-41l129 72h54zM452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365zM470 757q-25 0 -41.5 8t-23 17.5t-20 17.5t-32.5 8q-46 0 -69 -48h-36q27 67 112 67q25 0 41.5 -8t23.5 -17.5t20 -17.5t32 -8q46 0 69 48h37
q-28 -67 -113 -67z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="567" 
d="M472 523h-38l-62 113l-109 -113h-41l129 144h54zM276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22
q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5t-70.5 -129.5zM453 682q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="564" 
d="M524 723h-38l-62 113l-109 -113h-41l129 144h54zM452 0h-423l147 667h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-59 -264h365zM246 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="567" 
d="M479 556h-38l-62 113l-109 -113h-41l129 144h54zM276 -12q-107 0 -168.5 58.5t-61.5 154.5q0 121 78 207.5t190 86.5q94 0 149.5 -59t55.5 -150q0 -29 -8 -61h-408q-2 -12 -2 -28q0 -71 46 -117.5t131 -46.5q84 0 146 52l20 -38q-77 -59 -168 -59zM108 266h360q1 5 1 22
q0 72 -42.5 117t-118.5 45q-72 0 -129.5 -54.5t-70.5 -129.5zM238 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="223" 
d="M86 0h-57l147 667h57zM182 761l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="214" 
d="M79 0h-52l107 483h52zM140 591l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="223" 
d="M86 0h-57l147 667h57zM65 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="214" 
d="M180 559q-15 0 -25.5 10.5t-10.5 24.5q0 19 14 31.5t31 12.5q15 0 25.5 -10t10.5 -25q0 -19 -14 -31.5t-31 -12.5zM79 0h-52l107 483h52zM60 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63zM336 -136q0 -18 -12.5 -30
t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" 
d="M258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45zM239 -136q0 -18 -12.5 -30t-29.5 -12
q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63zM454 761l-24 13q24 46 71 46
q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" 
d="M258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45zM319 591l-24 13q24 46 71 46
q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="764" 
d="M598 682h-38l-62 85l-109 -85h-41l129 108h54zM821 845l-204 -108h-46l186 108h64zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63
q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" 
d="M480 556h-38l-62 113l-109 -113h-41l129 144h54zM705 770l-204 -144h-46l186 144h64zM258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45
q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="764" 
d="M597 682h-38l-62 85l-109 -85h-41l129 108h54zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176
t172.5 -63zM401 737h-44l-140 108h62z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" 
d="M480 556h-38l-62 113l-109 -113h-41l129 144h54zM258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122
t115.5 -45zM284 626h-44l-140 144h62z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="764" 
d="M599 682h-38l-62 85l-109 -85h-41l129 108h54zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176
t172.5 -63zM530 804l-24 10q24 34 71 34q26 0 45 -11.5t19 -31.5q0 -27 -31 -49h-36q36 24 36 47q0 12 -10 18.5t-24 6.5q-29 0 -46 -24z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" 
d="M480 556h-38l-62 113l-109 -113h-41l129 144h54zM258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122
t115.5 -45zM405 731l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="764" 
d="M594 678h-38l-62 56l-109 -56h-41l129 72h54zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176
t172.5 -63zM559 757q-39 0 -64 25.5t-53 25.5q-46 0 -69 -48h-36q27 67 112 67q25 0 41.5 -8t23.5 -17.5t20 -17.5t32 -8q46 0 69 48h37q-28 -67 -113 -67z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" 
d="M472 523h-38l-62 113l-109 -113h-41l129 144h54zM258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122
t115.5 -45zM454 682q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="764" 
d="M613 723h-38l-62 113l-109 -113h-41l129 144h54zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q129 0 214 -77t85 -212q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248
q0 -113 68.5 -176t172.5 -63zM336 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" 
d="M479 556h-38l-62 113l-109 -113h-41l129 144h54zM258 -12q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q97 0 155 -58t58 -154q0 -118 -77 -206.5t-189 -88.5zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122
t115.5 -45zM239 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="764" 
d="M681 867l-204 -144h-46l186 144h64zM367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q158 0 241 -107q54 29 67 77h-6q-13 0 -21.5 9t-8.5 23q0 17 12 29t29 12q16 0 26.5 -11t10.5 -31q0 -38 -27 -75.5t-65 -57.5q41 -68 41 -157q0 -169 -108 -285t-261 -116
zM370 40q124 0 214 99.5t90 247.5q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" 
d="M548 700l-204 -144h-46l186 144h64zM564 545q0 -35 -23.5 -70.5t-57.5 -56.5q41 -57 41 -135q0 -118 -77 -206.5t-189 -88.5q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q92 0 151 -54q49 32 60 73h-7q-12 0 -21 9.5t-9 22.5q0 17 12.5 29t29.5 12q16 0 26.5 -11
t10.5 -31zM259 35q88 0 148 74.5t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q158 0 241 -107q54 29 67 77h-6q-13 0 -21.5 9t-8.5 23q0 17 12 29t29 12q16 0 26.5 -11t10.5 -31q0 -38 -27 -75.5t-65 -57.5q41 -68 41 -157q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5
q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63zM543 723h-44l-140 144h62z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" 
d="M564 545q0 -35 -23.5 -70.5t-57.5 -56.5q41 -57 41 -135q0 -118 -77 -206.5t-189 -88.5q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q92 0 151 -54q49 32 60 73h-7q-12 0 -21 9.5t-9 22.5q0 17 12.5 29t29.5 12q16 0 26.5 -11t10.5 -31zM259 35q88 0 148 74.5
t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45zM411 556h-44l-140 144h62z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q158 0 241 -107q54 29 67 77h-6q-13 0 -21.5 9t-8.5 23q0 17 12 29t29 12q16 0 26.5 -11t10.5 -31q0 -38 -27 -75.5t-65 -57.5q41 -68 41 -157q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5
q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63zM454 761l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" 
d="M564 545q0 -35 -23.5 -70.5t-57.5 -56.5q41 -57 41 -135q0 -118 -77 -206.5t-189 -88.5q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q92 0 151 -54q49 32 60 73h-7q-12 0 -21 9.5t-9 22.5q0 17 12.5 29t29.5 12q16 0 26.5 -11t10.5 -31zM259 35q88 0 148 74.5
t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45zM320 591l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q158 0 241 -107q54 29 67 77h-6q-13 0 -21.5 9t-8.5 23q0 17 12 29t29 12q16 0 26.5 -11t10.5 -31q0 -38 -27 -75.5t-65 -57.5q41 -68 41 -157q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5
q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63zM559 721q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" 
d="M564 545q0 -35 -23.5 -70.5t-57.5 -56.5q41 -57 41 -135q0 -118 -77 -206.5t-189 -88.5q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q92 0 151 -54q49 32 60 73h-7q-12 0 -21 9.5t-9 22.5q0 17 12.5 29t29.5 12q16 0 26.5 -11t10.5 -31zM259 35q88 0 148 74.5
t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45zM414 554q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97
t-71.5 -37z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="764" 
d="M367 -12q-129 0 -214.5 77t-85.5 212q0 169 108 285t262 116q158 0 241 -107q54 29 67 77h-6q-13 0 -21.5 9t-8.5 23q0 17 12 29t29 12q16 0 26.5 -11t10.5 -31q0 -38 -27 -75.5t-65 -57.5q41 -68 41 -157q0 -169 -108 -285t-261 -116zM370 40q124 0 214 99.5t90 247.5
q0 113 -69 176t-173 63q-124 0 -213.5 -99t-89.5 -248q0 -113 68.5 -176t172.5 -63zM336 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" 
d="M564 545q0 -35 -23.5 -70.5t-57.5 -56.5q41 -57 41 -135q0 -118 -77 -206.5t-189 -88.5q-97 0 -155 58t-58 154q0 118 77 206.5t189 88.5q92 0 151 -54q49 32 60 73h-7q-12 0 -21 9.5t-9 22.5q0 17 12.5 29t29.5 12q16 0 26.5 -11t10.5 -31zM259 35q88 0 148 74.5
t60 171.5q0 77 -41.5 122t-115.5 45q-88 0 -148 -74.5t-60 -171.5q0 -77 41.5 -122t115.5 -45zM239 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="686" 
d="M327 -12q-114 0 -180 58.5t-66 158.5q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-30 -132 -93.5 -200t-185.5 -68zM296 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5
q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="540" 
d="M134 483h52l-72 -327q-6 -30 -6 -36q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41zM222 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10
t9.5 -24z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="686" 
d="M327 -12q-114 0 -180 58.5t-66 158.5q0 29 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-90 -411q-30 -132 -93.5 -200t-185.5 -68zM415 761l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36
q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" horiz-adv-x="540" 
d="M134 483h52l-72 -327q-6 -30 -6 -36q0 -43 29 -64t78 -21q83 0 164 85l81 363h52l-107 -483h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41zM303 591l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5
t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="686" 
d="M644 867l-204 -144h-46l186 144h64zM826 744q0 -49 -42 -92.5t-107 -68.5l-71 -327q-30 -132 -93.5 -200t-185.5 -68q-114 0 -180 58.5t-66 158.5q0 23 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-11 -49
q85 42 99 95h-7q-12 0 -21 9.5t-9 22.5q0 18 12.5 30t29.5 12q16 0 26.5 -11.5t10.5 -31.5z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" horiz-adv-x="540" 
d="M532 700l-204 -144h-46l186 144h64zM629 547q0 -48 -38.5 -90t-99.5 -67l-86 -390h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41l73 336h53l-73 -327q-5 -25 -5 -36q0 -43 29 -64t78 -21q84 0 164 84l81 364h52l-13 -57q75 38 88 90h-6
q-12 0 -21 9.5t-9 22.5q0 17 12 29.5t29 12.5q16 0 26.5 -11.5t10.5 -31.5z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="686" 
d="M503 723h-44l-140 144h62zM826 744q0 -49 -42 -92.5t-107 -68.5l-71 -327q-30 -132 -93.5 -200t-185.5 -68q-114 0 -180 58.5t-66 158.5q0 23 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-11 -49q85 42 99 95
h-7q-12 0 -21 9.5t-9 22.5q0 18 12.5 30t29.5 12q16 0 26.5 -11.5t10.5 -31.5z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" horiz-adv-x="540" 
d="M394 556h-44l-140 144h62zM629 547q0 -48 -38.5 -90t-99.5 -67l-86 -390h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41l73 336h53l-73 -327q-5 -25 -5 -36q0 -43 29 -64t78 -21q84 0 164 84l81 364h52l-13 -57q75 38 88 90h-6q-12 0 -21 9.5
t-9 22.5q0 17 12 29.5t29 12.5q16 0 26.5 -11.5t10.5 -31.5z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="686" 
d="M415 761l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32zM826 744q0 -49 -42 -92.5t-107 -68.5l-71 -327q-30 -132 -93.5 -200t-185.5 -68q-114 0 -180 58.5t-66 158.5q0 23 5 51l90 411h58l-90 -410
q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-11 -49q85 42 99 95h-7q-12 0 -21 9.5t-9 22.5q0 18 12.5 30t29.5 12q16 0 26.5 -11.5t10.5 -31.5z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" horiz-adv-x="540" 
d="M303 591l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32zM629 547q0 -48 -38.5 -90t-99.5 -67l-86 -390h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41l73 336h53l-73 -327
q-5 -25 -5 -36q0 -43 29 -64t78 -21q84 0 164 84l81 364h52l-13 -57q75 38 88 90h-6q-12 0 -21 9.5t-9 22.5q0 17 12 29.5t29 12.5q16 0 26.5 -11.5t10.5 -31.5z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="686" 
d="M826 744q0 -49 -42 -92.5t-107 -68.5l-71 -327q-30 -132 -93.5 -200t-185.5 -68q-114 0 -180 58.5t-66 158.5q0 23 5 51l90 411h58l-90 -410q-6 -24 -6 -45q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-11 -49q85 42 99 95h-7q-12 0 -21 9.5t-9 22.5
q0 18 12.5 30t29.5 12q16 0 26.5 -11.5t10.5 -31.5zM519 721q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" horiz-adv-x="540" 
d="M629 547q0 -48 -38.5 -90t-99.5 -67l-86 -390h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41l73 336h53l-73 -327q-5 -25 -5 -36q0 -43 29 -64t78 -21q84 0 164 84l81 364h52l-13 -57q75 38 88 90h-6q-12 0 -21 9.5t-9 22.5q0 17 12 29.5t29 12.5
q16 0 26.5 -11.5t10.5 -31.5zM408 554q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="686" 
d="M296 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24zM826 744q0 -49 -42 -92.5t-107 -68.5l-71 -327q-30 -132 -93.5 -200t-185.5 -68q-114 0 -180 58.5t-66 158.5q0 23 5 51l90 411h58l-90 -410q-6 -24 -6 -45
q0 -77 50.5 -124.5t138.5 -47.5q95 0 146 55t75 162l90 410h58l-11 -49q85 42 99 95h-7q-12 0 -21 9.5t-9 22.5q0 18 12.5 30t29.5 12q16 0 26.5 -11.5t10.5 -31.5z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" horiz-adv-x="540" 
d="M222 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24zM629 547q0 -48 -38.5 -90t-99.5 -67l-86 -390h-52l16 73q-87 -85 -174 -85q-64 0 -102.5 30.5t-38.5 87.5q0 19 6 41l73 336h53l-73 -327q-5 -25 -5 -36
q0 -43 29 -64t78 -21q84 0 164 84l81 364h52l-13 -57q75 38 88 90h-6q-12 0 -21 9.5t-9 22.5q0 17 12 29.5t29 12.5q16 0 26.5 -11.5t10.5 -31.5z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="618" 
d="M284 0h-58l63 285l-181 382h64l153 -328l297 328h74l-349 -382zM469 723h-44l-140 144h62z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="481" 
d="M-46 -186l20 50q18 -9 47 -9q39 0 76 55l57 87l-94 486h56l81 -422l269 422h61l-381 -589q-57 -90 -135 -90q-32 0 -57 10zM362 556h-44l-140 144h62z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="52" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="593" 
d="M557 218h-533l10 48h533z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="833" 
d="M797 218h-773l10 48h773z" />
    <glyph glyph-name="afii00208" unicode="&#x2015;" horiz-adv-x="593" 
d="M557 218h-533l10 48h533z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="219" 
d="M116 531q0 41 30.5 82.5t72.5 63.5l24 -26q-28 -14 -49.5 -37.5t-28.5 -46.5h7q14 0 24 -10.5t10 -26.5q0 -19 -14 -33t-33 -14t-31 12.5t-12 35.5z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="219" 
d="M242 629q0 -41 -30.5 -82.5t-72.5 -63.5l-24 26q28 14 49.5 37.5t28.5 46.5h-7q-14 0 -24 10.5t-10 25.5q0 20 14 34t33 14t31 -12.5t12 -35.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="219" 
d="M109 30q0 -41 -30.5 -83t-72.5 -64l-24 27q28 14 49.5 37.5t28.5 46.5h-7q-14 0 -24 10.5t-10 25.5q0 20 14 34t33 14t31 -12.5t12 -35.5z" />
    <glyph glyph-name="quotereversed" unicode="&#x201b;" horiz-adv-x="219" 
d="M149 586q0 45 19 68t43 23q18 0 29.5 -11t11.5 -26q0 -20 -14 -33t-32 -13q-9 0 -21 3q-4 -23 7 -52t31 -48l-28 -20q-46 45 -46 109z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="360" 
d="M267 531q0 41 30 82.5t72 63.5l24 -26q-28 -15 -49.5 -38t-28.5 -46h7q14 0 24.5 -10.5t10.5 -26.5q0 -19 -14.5 -33t-33.5 -14t-30.5 12.5t-11.5 35.5zM126 531q0 41 30 82.5t72 63.5l25 -26q-28 -14 -49.5 -37.5t-28.5 -46.5h7q14 0 24 -10.5t10 -26.5q0 -19 -14.5 -33
t-33.5 -14q-18 0 -30 12.5t-12 35.5z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="360" 
d="M242 629q0 -41 -30.5 -82.5t-72.5 -63.5l-24 26q28 14 49.5 37.5t28.5 46.5h-7q-14 0 -24 10.5t-10 25.5q0 20 14 34t33 14t31 -12.5t12 -35.5zM382 629q0 -41 -30 -82.5t-72 -63.5l-24 26q28 14 49.5 37.5t28.5 46.5h-7q-14 0 -24 10.5t-10 25.5q0 20 14 34t33 14
t30.5 -12.5t11.5 -35.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="360" 
d="M109 30q0 -41 -30.5 -83t-72.5 -64l-24 27q28 14 49.5 37.5t28.5 46.5h-7q-14 0 -24 10.5t-10 25.5q0 20 14 34t33 14t31 -12.5t12 -35.5zM249 30q0 -41 -30.5 -83t-71.5 -64l-24 27q28 15 49.5 38t28.5 46h-7q-14 0 -24.5 10.5t-10.5 25.5q0 20 14.5 34t33.5 14
t30.5 -12.5t11.5 -35.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="257" 
d="M297 535l-88 4l-43 -212h-39l51 212l-90 -4l8 35l88 -3l21 110h39l-29 -110l90 3z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="257" 
d="M305 570l-8 -35l-88 4l-49 -221l90 4l-8 -35l-88 3l-21 -110h-39l29 110l-90 -3l8 35l87 -4l50 221l-90 -4l8 35l88 -3l21 110h39l-29 -110z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="358" 
d="M283 252q0 -45 -35 -78.5t-81 -33.5q-39 0 -65.5 26.5t-26.5 65.5q0 46 34.5 79.5t80.5 33.5q40 0 66.5 -26.5t26.5 -66.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="657" 
d="M59 -10q-17 0 -28.5 12t-11.5 29q0 20 14 33.5t34 13.5q17 0 28.5 -12t11.5 -29q0 -19 -14 -33t-34 -14zM278 -10q-17 0 -28.5 12t-11.5 29q0 19 14 33t34 14q17 0 28.5 -12t11.5 -29q0 -19 -14 -33t-34 -14zM497 -10q-17 0 -28.5 12t-11.5 29q0 19 14 33t34 14
q17 0 28.5 -12t11.5 -29q0 -19 -14 -33t-34 -14z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1067" 
d="M119 0h-45l573 667h46zM494 -12q-62 0 -103.5 37.5t-41.5 96.5q0 84 52 139t123 55q62 0 103.5 -37.5t41.5 -96.5q0 -84 -52 -139t-123 -55zM495 27q51 0 89 43.5t38 107.5q0 42 -28 70.5t-71 28.5q-51 0 -89 -44t-38 -107q0 -43 28 -71t71 -28zM234 350q-61 0 -102.5 37
t-41.5 96q0 85 52 139.5t123 54.5q61 0 102.5 -37.5t41.5 -95.5q0 -85 -52 -139.5t-123 -54.5zM236 388q51 0 89 44t38 107q0 43 -28.5 71t-71.5 28q-51 0 -89 -43.5t-38 -107.5q0 -43 28.5 -71t71.5 -28zM841 -12q-62 0 -103.5 37.5t-41.5 96.5q0 84 52 139t123 55
q62 0 103.5 -37.5t41.5 -96.5q0 -84 -52 -139t-123 -55zM842 27q51 0 89 43.5t38 107.5q0 42 -28 70.5t-71 28.5q-51 0 -89 -44t-38 -107q0 -43 28 -71t71 -28z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="277" 
d="M202 63h-53l-120 180l200 177h61l-204 -182z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="277" 
d="M77 420h53l120 -180l-200 -177h-61l204 182z" />
    <glyph glyph-name="uni203E" unicode="&#x203e;" horiz-adv-x="363" 
d="M436 577h-362l8 37h362z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="130" 
d="M394 667l-574 -667h-45l573 667h46z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="384" 
d="M429 673q0 -36 -10.5 -78t-31.5 -84t-60 -69.5t-89 -27.5q-66 0 -101 42.5t-35 112.5q0 36 10.5 77.5t31.5 83.5t60 69.5t89 27.5q66 0 101 -42t35 -112zM383 673q0 116 -91 116q-38 0 -68 -23t-45.5 -59t-23 -71.5t-7.5 -67.5q0 -116 91 -116q38 0 68 23t45.5 59
t23 71.5t7.5 67.5z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="384" 
d="M397 528h-60l-24 -107h-44l23 107h-196l7 35l237 258h61l-57 -256h60zM300 565l47 211l-196 -211h149z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="384" 
d="M395 563q0 -64 -44 -106.5t-111 -42.5q-106 0 -144 73l33 25q36 -60 111 -60q48 0 78.5 30t30.5 75q0 38 -26.5 58.5t-66.5 20.5q-54 0 -94 -39l-31 13l47 211h248l-8 -38h-203l-32 -145q38 33 90 33q50 0 86 -29t36 -79z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="384" 
d="M401 563q0 -60 -41 -104.5t-111 -44.5t-106 39t-36 104q0 109 56.5 189.5t140.5 80.5q40 0 74 -13.5t50 -36.5l-29 -31q-26 43 -96 43q-46 0 -82 -37t-51 -76t-15 -64v-5q55 65 121 65q53 0 89 -28.5t36 -80.5zM355 557q0 37 -26 57.5t-69 20.5q-56 0 -108 -64
q-2 -5 -2 -22q0 -45 27 -71t71 -26q49 0 78 31.5t29 73.5z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="359" 
d="M426 790l-255 -369h-50l252 362h-228l8 38h280z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="384" 
d="M391 527q0 -49 -42 -81t-106 -32q-63 0 -105 28t-42 77q0 43 36.5 74.5t92.5 37.5q-78 29 -78 86q0 52 42 81t98 29q55 0 98 -25.5t43 -71.5q0 -42 -30 -71t-89 -37q35 -13 58.5 -37.5t23.5 -57.5zM383 721q0 34 -27 51.5t-69 17.5q-40 0 -68 -20.5t-28 -54.5
q0 -29 27.5 -48t53.5 -25q42 3 76.5 22t34.5 57zM346 529q0 26 -22.5 47.5t-59.5 33.5q-56 -4 -89.5 -28t-33.5 -60q0 -31 30 -51.5t70 -20.5q42 0 73.5 22.5t31.5 56.5z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="384" 
d="M132 679q0 60 41 104.5t111 44.5t105.5 -39t35.5 -105q0 -108 -56 -188.5t-140 -80.5q-88 0 -125 50l30 31q26 -43 95 -43q46 0 82 37t51 76.5t15 64.5v4q-55 -65 -121 -65q-53 0 -88.5 28.5t-35.5 80.5zM177 685q0 -37 26.5 -57.5t69.5 -20.5q55 0 107 64q2 5 2 22
q0 45 -26.5 71t-71.5 26q-49 0 -78 -31.5t-29 -73.5z" />
    <glyph glyph-name="parenleftsuperior" unicode="&#x207d;" horiz-adv-x="148" 
d="M165 368l-28 -12q-39 74 -39 171q0 195 156 359l22 -12q-134 -186 -134 -362q0 -80 23 -144z" />
    <glyph glyph-name="parenrightsuperior" unicode="&#x207e;" horiz-adv-x="148" 
d="M163 874l29 12q38 -74 38 -171q0 -196 -155 -359l-23 12q134 186 134 362q0 80 -23 144z" />
    <glyph glyph-name="nsuperior" unicode="&#x207f;" horiz-adv-x="392" 
d="M344 326h-43l46 207q3 15 3 23q0 27 -18 41.5t-49 14.5q-52 0 -109 -55l-50 -231h-43l69 314h43l-10 -47q51 55 115 55q46 0 71 -22t25 -60q0 -13 -3 -28z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="384" 
d="M313 106q0 -36 -10.5 -78t-31.5 -84t-60 -69.5t-89 -27.5q-66 0 -101 42.5t-35 112.5q0 36 10.5 77.5t31.5 83.5t60 69.5t89 27.5q66 0 101 -42t35 -112zM267 106q0 116 -91 116q-38 0 -68 -23t-45.5 -59t-23 -71.5t-7.5 -67.5q0 -116 91 -116q38 0 68 23t45.5 59
t23 71.5t7.5 67.5z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="214" 
d="M68 -146h-46l74 337l-77 -66l-22 30l119 99h40z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="384" 
d="M247 -146h-281l9 39q63 38 96 59t76.5 51.5t65 52t37 47.5t15.5 50q0 34 -27 51.5t-64 17.5q-71 0 -113 -47l-23 30q53 55 136 55q55 0 96 -26t41 -79q0 -28 -16 -57t-37.5 -52.5t-62 -53t-71 -49.5t-82.5 -51h213z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="384" 
d="M274 -31q0 -48 -39.5 -85t-103.5 -37q-110 0 -152 72l32 25q38 -59 119 -59q45 0 71.5 25.5t26.5 64.5q0 29 -29 47.5t-75 18.5q-22 0 -28 -1l8 39q30 -2 59 -2q45 0 72.5 20.5t27.5 56.5q0 31 -28.5 49.5t-69.5 18.5q-59 0 -109 -44l-20 29q59 53 131 53q60 0 100.5 -26
t40.5 -74q0 -45 -31.5 -73t-80.5 -32q31 -6 54.5 -29.5t23.5 -56.5z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="384" 
d="M281 -39h-60l-24 -107h-44l23 107h-196l7 35l237 258h61l-57 -256h60zM184 -2l47 211l-196 -211h149z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="384" 
d="M279 -4q0 -64 -44 -106.5t-111 -42.5q-106 0 -144 73l33 25q36 -60 111 -60q48 0 78.5 30t30.5 75q0 38 -26.5 58.5t-66.5 20.5q-54 0 -94 -39l-31 13l47 211h248l-8 -38h-203l-32 -145q38 33 90 33q50 0 86 -29t36 -79z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="384" 
d="M285 -4q0 -60 -41 -104.5t-111 -44.5t-106 39t-36 104q0 109 56.5 189.5t140.5 80.5q40 0 74 -13.5t50 -36.5l-29 -31q-26 43 -96 43q-46 0 -82 -37t-51 -76t-15 -64v-5q55 65 121 65q53 0 89 -28.5t36 -80.5zM239 -10q0 37 -26 57.5t-69 20.5q-56 0 -108 -64
q-2 -5 -2 -22q0 -45 27 -71t71 -26q49 0 78 31.5t29 73.5z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="359" 
d="M310 223l-255 -369h-50l252 362h-228l8 38h280z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="384" 
d="M275 -40q0 -49 -42 -81t-106 -32q-63 0 -105 28t-42 77q0 43 36.5 74.5t92.5 37.5q-78 29 -78 86q0 52 42 81t98 29q55 0 98 -25.5t43 -71.5q0 -42 -30 -71t-89 -37q35 -13 58.5 -37.5t23.5 -57.5zM267 154q0 34 -27 51.5t-69 17.5q-40 0 -68 -20.5t-28 -54.5
q0 -29 27.5 -48t53.5 -25q42 3 76.5 22t34.5 57zM230 -38q0 26 -22.5 47.5t-59.5 33.5q-56 -4 -89.5 -28t-33.5 -60q0 -31 30 -51.5t70 -20.5q42 0 73.5 22.5t31.5 56.5z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="384" 
d="M16 112q0 60 41 104.5t111 44.5t105.5 -39t35.5 -105q0 -108 -56 -188.5t-140 -80.5q-88 0 -125 50l30 31q26 -43 95 -43q46 0 82 37t51 76.5t15 64.5v4q-55 -65 -121 -65q-53 0 -88.5 28.5t-35.5 80.5zM61 118q0 -37 26.5 -57.5t69.5 -20.5q55 0 107 64q2 5 2 22
q0 45 -26.5 71t-71.5 26q-49 0 -78 -31.5t-29 -73.5z" />
    <glyph glyph-name="parenleftinferior" unicode="&#x208d;" horiz-adv-x="148" 
d="M40 -199l-28 -12q-39 73 -39 171q0 195 156 359l22 -12q-134 -186 -134 -362q0 -80 23 -144z" />
    <glyph glyph-name="parenrightinferior" unicode="&#x208e;" horiz-adv-x="148" 
d="M38 307l29 12q38 -74 38 -171q0 -196 -155 -359l-23 12q134 186 134 362q0 80 -23 144z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="674" 
d="M370 -12q-19 0 -53 4l-52 -92h-42l55 100q-37 8 -74 30l-73 -130h-43l85 151q-103 81 -103 226q0 88 31.5 164.5t83.5 127.5t118.5 80t136.5 29q39 0 77 -8l55 98h43l-61 -109q38 -13 71 -37l81 146h43l-95 -170q27 -25 48 -63l-55 -25q-14 24 -23 34l-279 -503
q9 -1 28 -1q102 0 188 76l41 -34q-98 -94 -232 -94zM132 279q0 -111 69 -177l289 519q-21 5 -54 5q-120 0 -212 -97t-92 -250zM230 78q32 -20 75 -31l292 525q-31 25 -70 39z" />
    <glyph glyph-name="frenchfranc" unicode="&#x20a3;" horiz-adv-x="553" 
d="M97 0h-57l33 149h-84l9 38h83l106 480h423l-11 -52h-366l-54 -247h358l-12 -52h-358l-29 -129h222l-9 -38h-221z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="505" 
d="M36 325l9 39h85q-32 53 -32 98q0 84 69 149.5t166 65.5q67 0 122.5 -32t70.5 -81l-53 -24q-10 38 -48.5 62.5t-89.5 24.5q-72 0 -123.5 -46t-51.5 -121q0 -47 31 -96h192l-9 -39h-159q23 -39 23 -77h119l-8 -38h-117q-12 -40 -44 -76.5t-67 -58.5q27 8 48 8q34 0 86 -22
t81 -22q53 0 96 41l16 -47q-56 -47 -114 -47q-46 0 -103.5 23.5t-101.5 23.5q-46 0 -116 -40l-12 48q67 26 116 72t62 97h-168l8 38h164q-2 35 -28 77h-119z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="722" 
d="M573 0h-56l-123 246h-243l-54 -246h-57l54 246h-83l9 38h82l24 106h-82l7 38h83l53 239h58l121 -239h243l54 239h57l-53 -239h81l-7 -38h-82l-24 -106h82l-9 -38h-81zM385 390l53 -106h139l24 106h-216zM183 390l-23 -106h215l-53 106h-139zM226 582l-34 -154h111z
M458 246l77 -152l34 152h-111z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="624" 
d="M98 0h-58l99 452h-82l9 40h82l39 175h239q72 0 121 -49.5t49 -117.5v-8h77l-9 -40h-73q-16 -75 -76.5 -125t-165.5 -50h-190zM355 329q71 0 116 34t58 89h-332l-27 -123h185zM233 615l-27 -123h329v4q0 48 -35.5 83.5t-87.5 35.5h-179z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="996" 
d="M738 -12q-133 0 -194 85l37 36q20 -32 62.5 -54.5t95.5 -22.5q58 0 93 29.5t35 70.5q0 28 -26.5 49.5t-64.5 36t-76 30t-64.5 42t-26.5 62.5q0 61 47.5 102t128.5 41q59 0 107 -23t69 -56l-34 -34q-16 29 -57.5 49t-88.5 20q-54 0 -86.5 -24.5t-32.5 -62.5
q0 -27 26.5 -47t64.5 -34t76 -30t64.5 -44t26.5 -66q0 -64 -49 -109.5t-133 -45.5zM489 0h-65l-126 276h-151l-61 -276h-57l147 667h229q72 0 126.5 -46t54.5 -122q0 -94 -62.5 -155.5t-165.5 -65.5zM343 328h2q84 0 131.5 48.5t47.5 119.5q0 51 -38 85t-88 34h-176
l-63 -287h184z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="867" 
d="M604 0h-64l-15 246h-182l-123 -246h-64l-16 246h-116l8 38h105l-7 106h-74l8 38h64l-16 239h63l12 -239h192l120 239h52l14 -239h192l118 239h66l-121 -239h61l-7 -38h-73l-54 -106h103l-9 -38h-113zM567 390l6 -106h113l52 106h-171zM189 390l5 -106h113l53 106h-171z
M415 390l-53 -106h161l-6 106h-102zM575 246l10 -166l82 166h-92zM196 246l9 -166l83 166h-92zM506 573l-72 -145h80z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="810" 
d="M491 0h-267l87 393h52l-76 -345h207q73 0 115.5 40t59.5 124l75 338h53l-76 -340q-24 -108 -76 -159t-154 -51zM148 550h205q86 0 144.5 -41t58.5 -127q0 -25 -8 -63l-35 -163h-53l37 167q6 24 6 49q0 64 -42 97t-109 33h-162l-111 -502h-52z" />
    <glyph glyph-name="dong" unicode="&#x20ab;" horiz-adv-x="574" 
d="M394 -131h-362l8 37h362zM299 495q57 0 102.5 -25.5t71.5 -68.5l30 137h-177l7 37h178l20 92h53l-21 -92h55l-7 -37h-56l-119 -538h-53l17 73q-67 -85 -166 -85q-85 0 -137 53t-52 145q0 126 71 217.5t183 91.5zM303 448q-88 0 -144.5 -76.5t-56.5 -176.5q0 -74 40 -117
t106 -43q49 0 93.5 26.5t70.5 66.5l51 232q-19 38 -61.5 63t-98.5 25z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="690" 
d="M390 -12q-123 0 -205 69.5t-93 184.5h-58l9 41h47q0 50 13 102h-37l9 41h40q42 115 138 183.5t207 68.5q88 0 156 -37t106 -106l-55 -25q-64 116 -211 116q-86 0 -162.5 -54t-114.5 -146h338l-10 -41h-342q-13 -52 -13 -102h333l-9 -41h-322q11 -93 75 -147.5t164 -54.5
q102 0 188 76l41 -34q-98 -94 -232 -94z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="454" 
d="M192 512l-35 -159l177 103q36 21 53 32.5t33.5 31.5t16.5 42q0 32 -31 53.5t-71 21.5q-54 0 -91.5 -32.5t-51.5 -92.5zM346 415l-201 -118l-44 -196q-3 -12 -3 -25q0 -19 12.5 -31t37.5 -12q30 0 51 19l10 -41q-29 -23 -70 -23q-42 0 -68.5 19.5t-26.5 54.5q0 15 3 32
l94 422q16 77 66 119t130 42q62 0 106.5 -31.5t44.5 -79.5q0 -25 -12.5 -47.5t-37.5 -42.5t-43 -31.5t-49 -29.5z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="1104" 
d="M936 350q-66 0 -105.5 39t-39.5 102q0 77 52 133t127 56q66 0 105.5 -39t39.5 -102q0 -77 -52 -133t-127 -56zM936 387q59 0 97 45.5t38 106.5q0 45 -28.5 74t-73.5 29q-59 0 -96 -44.5t-37 -106.5q0 -46 27.5 -75t72.5 -29zM562 0h-56l-291 582l-129 -582h-57l147 667
h58l290 -573l128 573h57z" />
    <glyph glyph-name="published" unicode="&#x2117;" horiz-adv-x="778" 
d="M754 334q0 -142 -101.5 -243.5t-244.5 -101.5q-142 0 -243 101t-101 244t101 244t243 101q143 0 244.5 -101.5t101.5 -243.5zM726 334q0 130 -93.5 223.5t-224.5 93.5q-130 0 -223 -93t-93 -224t93 -224t223 -93q131 0 224.5 93.5t93.5 223.5zM589 435q0 -56 -41 -92
t-102 -36h-123l-38 -176h-33l89 405h128q52 0 86 -28t34 -73zM556 432q0 33 -25.5 52.5t-66.5 19.5h-97l-36 -165h121q47 0 75.5 26t28.5 67z" />
    <glyph glyph-name="servicemark" unicode="&#x2120;" horiz-adv-x="476" 
d="M484 447h-28l40 182l-115 -182h-8l-35 182l-40 -182h-28l48 220h42l29 -160l99 160h44zM247 511q0 -32 -23 -52t-62 -20q-69 0 -96 40l22 19q23 -33 72 -33q24 0 38 11t14 29q0 15 -18 26.5t-39 18.5t-39 21t-18 34q0 29 23.5 48.5t60.5 19.5q63 0 86 -35l-23 -17
q-20 28 -65 28q-22 0 -35 -11t-13 -26t18 -26t39.5 -17t39.5 -21t18 -37z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="454" 
d="M464 447h-28l40 182l-115 -182h-8l-35 182l-40 -182h-28l48 220h43l29 -160l99 160h43zM257 641h-62l-43 -194h-28l43 194h-62l5 26h152z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="764" 
d="M34 52h157q-120 88 -120 237q0 101 45 189.5t130.5 144t190.5 55.5q134 0 215.5 -78t81.5 -212q0 -121 -62 -206t-156 -130h129l-10 -52h-228l11 52q107 22 180.5 115t73.5 219q0 109 -65 174t-171 65q-132 0 -219.5 -101.5t-87.5 -244.5q0 -81 37 -138.5t95 -88.5
l-11 -52h-227z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="842" 
d="M829 324h-632q-4 0 -4 -5v-190q0 -15 9 -23q98 -103 240 -103q151 0 250 115h57q-54 -63 -135 -99.5t-174 -36.5q-161 0 -275.5 103t-114.5 248t114.5 248t275.5 103t275.5 -103t114.5 -248zM687 349v191q0 14 -10 24q-97 99 -236 99t-238 -102q-10 -10 -10 -24v-188
q0 -6 5 -6h485q4 0 4 6z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="781" 
d="M159 267h-46l74 337l-77 -66l-22 30l119 99h40zM661 667l-574 -667h-45l573 667h46zM703 115q0 -48 -39.5 -85t-103.5 -37q-110 0 -152 72l32 25q38 -59 119 -59q45 0 71.5 25.5t26.5 64.5q0 29 -29 47.5t-75 18.5q-22 0 -28 -1l8 39q30 -2 59 -2q45 0 72.5 20.5
t27.5 56.5q0 31 -28.5 49.5t-69.5 18.5q-59 0 -109 -44l-20 29q59 53 131 53q60 0 100.5 -26t40.5 -74q0 -45 -31.5 -73t-80.5 -32q31 -6 54.5 -29.5t23.5 -56.5z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="898" 
d="M338 267h-281l9 39q63 38 96 59t76.5 51.5t65 52t37 47.5t15.5 50q0 34 -27 51.5t-64 17.5q-71 0 -113 -47l-23 30q53 55 136 55q55 0 96 -26t41 -79q0 -28 -16 -57t-37.5 -52.5t-62 -53t-71 -49.5t-82.5 -51h213zM778 667l-574 -667h-45l573 667h46zM820 115
q0 -48 -39.5 -85t-103.5 -37q-110 0 -152 72l32 25q38 -59 119 -59q45 0 71.5 25.5t26.5 64.5q0 29 -29 47.5t-75 18.5q-22 0 -28 -1l8 39q30 -2 59 -2q45 0 72.5 20.5t27.5 56.5q0 31 -28.5 49.5t-69.5 18.5q-59 0 -109 -44l-20 29q59 53 131 53q60 0 100.5 -26t40.5 -74
q0 -45 -31.5 -73t-80.5 -32q31 -6 54.5 -29.5t23.5 -56.5z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="781" 
d="M159 267h-46l74 337l-77 -66l-22 30l119 99h40zM704 106q0 -49 -42 -81t-106 -32q-63 0 -105 28t-42 77q0 43 36.5 74.5t92.5 37.5q-78 29 -78 86q0 52 42 81t98 29q55 0 98 -25.5t43 -71.5q0 -42 -30 -71t-89 -37q35 -13 58.5 -37.5t23.5 -57.5zM696 300q0 34 -27 51.5
t-69 17.5q-40 0 -68 -20.5t-28 -54.5q0 -29 27.5 -48t53.5 -25q42 3 76.5 22t34.5 57zM659 108q0 26 -22.5 47.5t-59.5 33.5q-56 -4 -89.5 -28t-33.5 -60q0 -31 30 -51.5t70 -20.5q42 0 73.5 22.5t31.5 56.5zM661 667l-574 -667h-45l573 667h46z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="898" 
d="M821 106q0 -49 -42 -81t-106 -32q-63 0 -105 28t-42 77q0 43 36.5 74.5t92.5 37.5q-78 29 -78 86q0 52 42 81t98 29q55 0 98 -25.5t43 -71.5q0 -42 -30 -71t-89 -37q35 -13 58.5 -37.5t23.5 -57.5zM813 300q0 34 -27 51.5t-69 17.5q-40 0 -68 -20.5t-28 -54.5
q0 -29 27.5 -48t53.5 -25q42 3 76.5 22t34.5 57zM776 108q0 26 -22.5 47.5t-59.5 33.5q-56 -4 -89.5 -28t-33.5 -60q0 -31 30 -51.5t70 -20.5q42 0 73.5 22.5t31.5 56.5zM778 667l-574 -667h-45l573 667h46zM365 382q0 -48 -39.5 -85t-103.5 -37q-110 0 -152 72l32 25
q38 -59 119 -59q45 0 71.5 25.5t26.5 64.5q0 29 -29 47.5t-75 18.5q-22 0 -28 -1l8 39q30 -2 59 -2q45 0 72.5 20.5t27.5 56.5q0 31 -28.5 49.5t-69.5 18.5q-59 0 -109 -44l-20 29q59 53 131 53q60 0 100.5 -26t40.5 -74q0 -45 -31.5 -73t-80.5 -32q31 -6 54.5 -29.5
t23.5 -56.5z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="896" 
d="M819 106q0 -49 -42 -81t-106 -32q-63 0 -105 28t-42 77q0 43 36.5 74.5t92.5 37.5q-78 29 -78 86q0 52 42 81t98 29q55 0 98 -25.5t43 -71.5q0 -42 -30 -71t-89 -37q35 -13 58.5 -37.5t23.5 -57.5zM811 300q0 34 -27 51.5t-69 17.5q-40 0 -68 -20.5t-28 -54.5
q0 -29 27.5 -48t53.5 -25q42 3 76.5 22t34.5 57zM774 108q0 26 -22.5 47.5t-59.5 33.5q-56 -4 -89.5 -28t-33.5 -60q0 -31 30 -51.5t70 -20.5q42 0 73.5 22.5t31.5 56.5zM370 409q0 -64 -44 -106.5t-111 -42.5q-106 0 -144 73l33 25q36 -60 111 -60q48 0 78.5 30t30.5 75
q0 38 -26.5 58.5t-66.5 20.5q-54 0 -94 -39l-31 13l47 211h248l-8 -38h-203l-32 -145q38 33 90 33q50 0 86 -29t36 -79zM776 667l-574 -667h-45l573 667h46z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="811" 
d="M734 106q0 -49 -42 -81t-106 -32q-63 0 -105 28t-42 77q0 43 36.5 74.5t92.5 37.5q-78 29 -78 86q0 52 42 81t98 29q55 0 98 -25.5t43 -71.5q0 -42 -30 -71t-89 -37q35 -13 58.5 -37.5t23.5 -57.5zM726 300q0 34 -27 51.5t-69 17.5q-40 0 -68 -20.5t-28 -54.5
q0 -29 27.5 -48t53.5 -25q42 3 76.5 22t34.5 57zM689 108q0 26 -22.5 47.5t-59.5 33.5q-56 -4 -89.5 -28t-33.5 -60q0 -31 30 -51.5t70 -20.5q42 0 73.5 22.5t31.5 56.5zM401 636l-255 -369h-50l252 362h-228l8 38h280zM691 667l-574 -667h-45l573 667h46z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="582" 
d="M558 211h-269l12 -130l-281 205l281 204l-12 -129h269v-150z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="582" 
d="M236 17v269l-130 -12l205 281l204 -281l-129 12v-269h-150z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="582" 
d="M604 286l-281 -205l12 130h-268v150h268l-12 129z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="582" 
d="M386 555v-269l130 12l-205 -281l-204 281l129 -12v269h150z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M261 -12q-95 0 -155.5 58t-60.5 147q0 122 74.5 208t179.5 86q51 0 97.5 -25.5t70.5 -79.5q-57 152 -220 272l39 40q109 -79 173.5 -182t64.5 -211q0 -131 -75 -222t-188 -91zM262 35q83 0 143 72.5t60 172.5q0 64 -41 112t-120 48q-83 0 -142.5 -70t-59.5 -170
q0 -73 42.5 -119t117.5 -46z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="647" 
d="M578 0h-617l420 667h71zM508 52l-102 554l-347 -554h449z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="673" 
d="M453 -90h-58l156 705h-265l-157 -705h-57l156 705h-111l11 52h603l-11 -52h-111z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M207 615l175 -322l-314 -331h382l-11 -52h-455l11 52l318 336l-172 317l11 52h454l-11 -52h-388z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="496" 
d="M482 314h-438l9 42h439z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="130" 
d="M394 667l-574 -667h-45l573 667h46z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="219" 
d="M106 201q-17 0 -29 11.5t-12 28.5q0 20 14.5 33.5t34.5 13.5q17 0 28.5 -11.5t11.5 -28.5q0 -19 -14 -33t-34 -14z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="743" 
d="M382 0h-46l-64 338l-179 -67l-6 39l219 81l64 -335l385 611h46z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="641" 
d="M177 175q-61 0 -96 35.5t-35 95.5q0 76 47.5 132.5t120.5 56.5q51 0 87 -31.5t49 -83.5q69 115 153 115q61 0 96 -35.5t35 -95.5q0 -76 -47.5 -132.5t-121.5 -56.5q-50 0 -86 31.5t-49 82.5q-69 -114 -153 -114zM468 217q55 0 88.5 42t33.5 103q0 40 -25 65.5t-66 25.5
q-43 0 -75 -32t-64 -89q8 -55 36 -85t72 -30zM181 217q75 0 140 121q-8 55 -36.5 85t-72.5 30q-55 0 -89 -42t-34 -102q0 -40 25.5 -66t66.5 -26z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="337" 
d="M2 -100h-61l9 43h61q68 0 86 81l138 620q27 124 139 124h62l-10 -43h-62q-67 0 -85 -81l-138 -620q-27 -124 -139 -124z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="496" 
d="M22 216l11 47q39 -35 101 -35q48 0 112 30.5t120 30.5q64 0 105 -28l-11 -47q-40 34 -103 34q-47 0 -111 -30.5t-120 -30.5q-61 0 -104 29zM65 407l10 47q40 -34 102 -34q47 0 111 30.5t120 30.5q61 0 105 -28l-10 -48q-41 35 -103 35q-47 0 -111 -30.5t-121 -30.5
q-63 0 -103 28z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="496" 
d="M85 93h-48l106 123h-121l10 41h146l132 154h-244l9 42h271l104 121h49l-104 -121h118l-9 -42h-145l-133 -154h244l-9 -41h-271z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="496" 
d="M433 95l-388 225l9 38l488 227l-12 -52l-429 -197l342 -195zM413 0h-438l8 41h439z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="496" 
d="M484 320l-489 -225l12 51l430 195l-343 197l10 47l388 -227zM412 0h-438l9 41h438z" />
    <glyph glyph-name="triangleright" unicode="&#x22b2;" horiz-adv-x="587" 
d="M609 286l-535 -309v618z" />
    <glyph glyph-name="triangleleft" unicode="&#x22b3;" horiz-adv-x="587" 
d="M555 595v-618l-535 309z" />
    <glyph glyph-name="dotmath" unicode="&#x22c5;" horiz-adv-x="219" 
d="M106 201q-17 0 -29 11.5t-12 28.5q0 20 14.5 33.5t34.5 13.5q17 0 28.5 -11.5t11.5 -28.5q0 -19 -14 -33t-34 -14z" />
    <glyph glyph-name="uni2318" unicode="&#x2318;" horiz-adv-x="667" 
d="M209 153l18 78h-86q-36 0 -61.5 -26t-25.5 -62q0 -29 19 -48.5t47 -19.5q36 0 59 21t30 57zM271 435l16 69q2 14 2 21q0 30 -19 48t-50 18q-36 0 -61.5 -24.5t-25.5 -60.5q0 -32 20 -51.5t53 -19.5h65zM269 265h137l31 137h-137zM572 161q0 32 -20 51t-52 19h-66l-15 -68
q-2 -8 -2 -21q0 -31 19 -49t50 -18q35 0 60.5 25t25.5 61zM652 524q0 29 -19 48t-48 19q-71 0 -89 -77l-17 -79h86q36 0 61.5 26.5t25.5 62.5zM607 160q0 -48 -39 -83t-89 -35q-42 0 -70 28t-28 70q0 14 3 29l15 62h-137l-20 -86q-11 -46 -45 -74.5t-80 -28.5q-41 0 -70 28
t-29 69q0 49 38 87.5t87 38.5h91l31 137h-66q-45 0 -72.5 29t-27.5 76q0 48 38.5 83t88.5 35q42 0 70 -28t28 -70q0 -14 -3 -29l-14 -63h137l19 87q11 46 45 74.5t80 28.5q42 0 71 -28t29 -69q0 -50 -38 -88t-87 -38h-91l-31 -137h63q46 0 74.5 -29t28.5 -76z" />
    <glyph glyph-name="blacksquare" unicode="&#x25a0;" horiz-adv-x="661" 
d="M638 0h-571v572h571v-572z" />
    <glyph glyph-name="triagup" unicode="&#x25b2;" horiz-adv-x="616" 
d="M637 29l-619 1l309 535z" />
    <glyph glyph-name="triagdn" unicode="&#x25bc;" horiz-adv-x="616" 
d="M637 510l-310 -535l-309 535h619z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="375" 
d="M144 0h-21l-84 334l231 333h21l85 -333zM142 45l197 293l-67 284l-196 -292z" />
    <glyph glyph-name="circlesolid" unicode="&#x25cf;" horiz-adv-x="695" 
d="M664 285q0 -123 -87 -210t-210 -87t-210 87t-87 210t87 210t210 87t210 -87t87 -210z" />
    <glyph glyph-name="blackstar" unicode="&#x2605;" horiz-adv-x="759" 
d="M779 421l-235 -171l90 -275l-235 170l-234 -170l89 275l-234 171h290l89 276l90 -276h290z" />
    <glyph glyph-name="ballotbox" unicode="&#x2610;" horiz-adv-x="661" 
d="M636 0h-571v572h571v-572zM591 41v490h-482v-490h482z" />
    <glyph glyph-name="boxchecked" unicode="&#x2611;" horiz-adv-x="661" 
d="M711 698l-69 -126h14v-572h-571v572h508l81 150zM611 41v473l-229 -423l-202 246l33 31l163 -201l195 364h-442v-490h482z" />
    <glyph glyph-name="diamond" unicode="&#x2666;" horiz-adv-x="501" 
d="M521 285l-251 -250l-250 250l250 251z" />
    <glyph glyph-name="checkmark" unicode="&#x2713;" horiz-adv-x="531" 
d="M551 698l-329 -607l-202 246l33 31l163 -201l298 555z" />
    <glyph glyph-name="perthousand.onum" unicode="&#xf628;" horiz-adv-x="866" 
d="M328 445q0 -62 -42.5 -108.5t-104.5 -46.5q-53 0 -85.5 31.5t-32.5 84.5q0 63 42 109.5t105 46.5q53 0 85.5 -32t32.5 -85zM572 550l-505 -550h-44l504 550h45zM286 443q0 36 -20.5 60t-56.5 24q-44 0 -74.5 -36t-30.5 -84q0 -35 21 -59t57 -24q44 0 74 36t30 83z
M532 143q0 -62 -42.5 -108.5t-105.5 -46.5q-52 0 -84.5 32t-32.5 85q0 62 42.5 109t104.5 47q53 0 85.5 -32t32.5 -86zM490 142q0 36 -20 60t-57 24q-44 0 -74.5 -36.5t-30.5 -83.5q0 -35 21 -59t57 -24q44 0 74 36t30 83zM816 143q0 -62 -42.5 -108.5t-104.5 -46.5
q-53 0 -85.5 31.5t-32.5 85.5q0 62 42.5 109t104.5 47q53 0 85.5 -32t32.5 -86zM775 142q0 36 -20.5 60t-57.5 24q-44 0 -74 -36.5t-30 -83.5q0 -35 20.5 -59t56.5 -24q44 0 74.5 36t30.5 83z" />
    <glyph glyph-name="uniF637" unicode="&#xf637;" horiz-adv-x="392" 
d="M344 326h-43l46 207q2 10 2 20q0 59 -71 59q-48 0 -104 -54l-50 -232h-43l96 434h43l-37 -167q51 55 115 55q46 0 71.5 -20.5t25.5 -57.5q0 -12 -4 -32z" />
    <glyph glyph-name="zero.tzero" unicode="&#xf638;" horiz-adv-x="588" 
d="M265 -12q-100 0 -155 68l-47 -56h-53l76 92q-31 61 -31 152q0 101 35.5 198.5t108 166t164.5 68.5q99 0 154 -67l47 57h53l-78 -93q32 -62 32 -152q0 -101 -35 -198.5t-107 -166.5t-164 -69zM270 40q54 0 101 36.5t77 94t47 124.5t17 131q0 53 -14 98l-351 -422
q40 -62 123 -62zM115 240q0 -57 13 -97l351 421q-40 61 -122 61q-54 0 -101 -36.5t-77 -93.5t-47 -124t-17 -131z" />
    <glyph glyph-name="zero.tnum" unicode="&#xf639;" horiz-adv-x="588" 
d="M265 -12q-101 0 -155.5 69t-54.5 187q0 101 35.5 198.5t108 166t164.5 68.5q101 0 154.5 -68.5t53.5 -186.5q0 -101 -35 -198.5t-107 -166.5t-164 -69zM270 40q54 0 101 36.5t77 94t47 124.5t17 131q0 91 -37.5 145t-117.5 54q-54 0 -101 -36.5t-77 -93.5t-47 -124
t-17 -131q0 -91 37.5 -145.5t117.5 -54.5z" />
    <glyph glyph-name="two.tnum" unicode="&#xf63a;" horiz-adv-x="588" 
d="M455 0h-440l14 60q94 60 152 98.5t126.5 90.5t106.5 91t64 84t26 85q0 54 -42 85t-111 31q-106 0 -173 -67l-31 40q36 36 91 57.5t115 21.5q89 0 151 -42.5t62 -123.5q0 -46 -26 -95.5t-64.5 -92t-103.5 -93.5t-120 -88.5t-137 -89.5h351z" />
    <glyph glyph-name="three.tnum" unicode="&#xf63b;" horiz-adv-x="588" 
d="M275 -12q-81 0 -148.5 39t-90.5 100l46 30q22 -54 75 -85.5t118 -31.5q80 0 128 45t48 115q0 53 -43.5 85t-115.5 32q-46 0 -57 -1l13 54q9 -1 89 -1q72 0 122.5 35.5t50.5 101.5q0 51 -44.5 85t-117.5 34q-95 0 -171 -70l-28 40q38 38 92 60t110 22q96 0 156.5 -44
t60.5 -122q0 -74 -55.5 -121t-125.5 -52q52 -12 86.5 -49t34.5 -92q0 -87 -65 -148t-168 -61z" />
    <glyph glyph-name="four.tnum" unicode="&#xf63c;" horiz-adv-x="588" 
d="M376 0h-57l40 182h-316l13 55l386 430h81l-96 -433h98l-11 -52h-98zM370 234l83 376l-340 -376h257z" />
    <glyph glyph-name="five.tnum" unicode="&#xf63d;" horiz-adv-x="588" 
d="M274 -12q-166 0 -229 135l46 33q51 -116 188 -116q76 0 130 52.5t54 129.5q0 67 -45 104.5t-116 37.5q-77 0 -146 -51l-38 23l73 331h385l-11 -52h-328l-55 -247q60 46 143 46t140.5 -49.5t57.5 -137.5q0 -98 -70.5 -168.5t-178.5 -70.5z" />
    <glyph glyph-name="six.tnum" unicode="&#xf63e;" horiz-adv-x="588" 
d="M281 -12q-103 0 -158.5 62.5t-55.5 177.5q0 55 11 114t36.5 119.5t61 108t89.5 77.5t118 30q138 0 192 -104l-43 -37q-40 89 -152 89q-51 0 -94 -25.5t-71.5 -69t-47.5 -90.5t-31 -102q-1 -3 -5 -19q32 35 87 64t113 29q89 0 144 -46.5t55 -126.5q0 -102 -72 -176.5
t-177 -74.5zM283 40q79 0 133 57.5t54 133.5q0 60 -43 95.5t-114 35.5q-103 0 -189 -96q-2 -18 -2 -53q0 -78 42 -125.5t119 -47.5z" />
    <glyph glyph-name="seven.tnum" unicode="&#xf63f;" horiz-adv-x="588" 
d="M176 0h-66l414 615h-366l11 52h437l-9 -40z" />
    <glyph glyph-name="eight.tnum" unicode="&#xf640;" horiz-adv-x="588" 
d="M274 -12q-101 0 -165 44t-64 123q0 76 57.5 127.5t151.5 68.5q-51 19 -86.5 56t-35.5 87q0 87 68.5 135t156.5 48q89 0 152 -43.5t63 -117.5q0 -73 -54 -119.5t-142 -58.5q52 -22 91.5 -62t39.5 -98q0 -82 -68 -136t-165 -54zM321 368q86 7 138 43.5t52 98.5
q0 48 -45.5 81t-110.5 33q-68 0 -116.5 -36t-48.5 -97q0 -43 41.5 -77.5t89.5 -45.5zM275 40q68 0 121 39t53 103q0 50 -44 86.5t-95 50.5q-89 -6 -147 -48.5t-58 -106.5q0 -59 49 -91.5t121 -32.5z" />
    <glyph glyph-name="nine.tnum" unicode="&#xf641;" horiz-adv-x="588" 
d="M345 678q103 0 158.5 -62.5t55.5 -177.5q0 -55 -11 -114.5t-36 -119.5t-61 -107.5t-90 -77.5t-118 -30q-138 0 -191 104l42 37q40 -89 153 -89q51 0 93.5 25.5t71 69t47.5 90.5t32 102q1 3 2 7.5t1.5 7t0.5 3.5q-32 -35 -87 -63.5t-113 -28.5q-89 0 -144 46.5t-55 126.5
q0 102 72 176.5t177 74.5zM343 626q-79 0 -133 -57.5t-54 -133.5q0 -60 43 -95.5t114 -35.5q105 0 189 96q2 18 2 53q0 78 -42 125.5t-119 47.5z" />
    <glyph glyph-name="percent.onum" unicode="&#xf642;" horiz-adv-x="582" 
d="M328 445q0 -62 -42.5 -108.5t-104.5 -46.5q-53 0 -85.5 31.5t-32.5 84.5q0 63 42 109.5t105 46.5q53 0 85.5 -32t32.5 -85zM572 550l-505 -550h-44l504 550h45zM286 443q0 36 -20.5 60t-56.5 24q-44 0 -74.5 -36t-30.5 -84q0 -35 21 -59t57 -24q44 0 74 36t30 83z
M532 143q0 -62 -42.5 -108.5t-105.5 -46.5q-52 0 -84.5 32t-32.5 85q0 62 42.5 109t104.5 47q53 0 85.5 -32t32.5 -86zM490 142q0 36 -20 60t-57 24q-44 0 -74.5 -36.5t-30.5 -83.5q0 -35 21 -59t57 -24q44 0 74 36t30 83z" />
    <glyph glyph-name="zero.tonum" unicode="&#xf643;" horiz-adv-x="588" 
d="M554 328q0 -42 -11 -87.5t-34.5 -91t-55.5 -81t-78.5 -58t-100.5 -22.5q-112 0 -170 62.5t-58 171.5q0 53 18 110.5t51.5 109.5t89 86t121.5 34q112 0 170 -62.5t58 -171.5zM494 325q0 89 -42 137t-126 48q-53 0 -97 -29t-69.5 -73.5t-39.5 -92.5t-14 -90q0 -89 42 -137
t126 -48q43 0 80.5 19.5t62.5 50t43 69t26 76t8 70.5z" />
    <glyph glyph-name="one.tonum" unicode="&#xf644;" horiz-adv-x="588" 
d="M318 0h-57l103 472l-131 -114l-27 37l181 155h52z" />
    <glyph glyph-name="two.tonum" unicode="&#xf645;" horiz-adv-x="588" 
d="M442 0h-415l10 50q215 85 328.5 166.5t113.5 170.5q0 55 -42.5 89t-107.5 34q-109 0 -190 -78l-30 39q91 91 224 91q88 0 147 -44.5t59 -122.5q0 -53 -31.5 -104t-88.5 -94t-122 -78t-145 -67h301z" />
    <glyph glyph-name="three.tonum" unicode="&#xf646;" horiz-adv-x="588" 
d="M250 -127q-81 0 -148.5 39t-90.5 100l46 30q22 -54 75 -85.5t118 -31.5q80 0 128 45t48 115q0 53 -43.5 85t-115.5 32q-46 0 -57 -1l13 54q9 -1 89 -1q72 0 122.5 35.5t50.5 101.5q0 51 -44.5 85t-117.5 34q-95 0 -171 -70l-28 40q38 38 92 60t110 22q96 0 156.5 -44
t60.5 -122q0 -74 -55.5 -121t-125.5 -52q52 -12 86.5 -49t34.5 -92q0 -87 -65 -148t-168 -61z" />
    <glyph glyph-name="four.tonum" unicode="&#xf647;" horiz-adv-x="588" 
d="M488 66h-97l-41 -183h-57l40 183h-316l12 52l391 432h77l-95 -432h97zM344 118l83 375l-340 -375h257z" />
    <glyph glyph-name="five.tonum" unicode="&#xf648;" horiz-adv-x="588" 
d="M248 -129q-166 0 -229 135l46 33q51 -116 188 -116q76 0 130 52.5t54 129.5q0 67 -45 104.5t-116 37.5q-77 0 -146 -51l-38 23l73 331h385l-11 -52h-328l-55 -247q60 46 143 46t140.5 -49.5t57.5 -137.5q0 -98 -70.5 -168.5t-178.5 -70.5z" />
    <glyph glyph-name="six.tonum" unicode="&#xf649;" horiz-adv-x="588" 
d="M281 -12q-103 0 -158.5 62.5t-55.5 177.5q0 55 11 114t36.5 119.5t61 108t89.5 77.5t118 30q138 0 192 -104l-43 -37q-40 89 -152 89q-51 0 -94 -25.5t-71.5 -69t-47.5 -90.5t-31 -102q-1 -3 -5 -19q32 35 87 64t113 29q89 0 144 -46.5t55 -126.5q0 -102 -72 -176.5
t-177 -74.5zM283 40q79 0 133 57.5t54 133.5q0 60 -43 95.5t-114 35.5q-103 0 -189 -96q-2 -18 -2 -53q0 -78 42 -125.5t119 -47.5z" />
    <glyph glyph-name="seven.tonum" unicode="&#xf64a;" horiz-adv-x="588" 
d="M150 -117h-66l414 615h-366l11 52h437l-9 -40z" />
    <glyph glyph-name="eight.tonum" unicode="&#xf64b;" horiz-adv-x="588" 
d="M274 -12q-101 0 -165 44t-64 123q0 76 57.5 127.5t151.5 68.5q-51 19 -86.5 56t-35.5 87q0 87 68.5 135t156.5 48q89 0 152 -43.5t63 -117.5q0 -73 -54 -119.5t-142 -58.5q52 -22 91.5 -62t39.5 -98q0 -82 -68 -136t-165 -54zM321 368q86 7 138 43.5t52 98.5
q0 48 -45.5 81t-110.5 33q-68 0 -116.5 -36t-48.5 -97q0 -43 41.5 -77.5t89.5 -45.5zM275 40q68 0 121 39t53 103q0 50 -44 86.5t-95 50.5q-89 -6 -147 -48.5t-58 -106.5q0 -59 49 -91.5t121 -32.5z" />
    <glyph glyph-name="nine.tonum" unicode="&#xf64c;" horiz-adv-x="588" 
d="M319 562q103 0 158.5 -62.5t55.5 -177.5q0 -55 -11 -114.5t-36 -119.5t-61 -107.5t-90 -77.5t-118 -30q-138 0 -191 104l42 37q40 -89 153 -89q51 0 93.5 25.5t71 69t47.5 90.5t32 102q1 3 2 7.5t1.5 7t0.5 3.5q-32 -35 -87 -63.5t-113 -28.5q-89 0 -144 46.5t-55 126.5
q0 102 72 176.5t177 74.5zM317 510q-79 0 -133 -57.5t-54 -133.5q0 -60 43 -95.5t114 -35.5q105 0 189 96q2 18 2 53q0 78 -42 125.5t-119 47.5z" />
    <glyph glyph-name="colonmonetary.onum" unicode="&#xf64d;" horiz-adv-x="581" 
d="M77 -53l58 103q-82 68 -82 187q0 132 97 228.5t228 96.5q20 0 40 -3l23 40h36l-26 -47q34 -10 56 -23l39 70h37l-49 -88q35 -26 56 -62l-46 -23q-16 23 -35 40l-237 -424q27 -5 51 -5q101 0 169 71l39 -31q-92 -89 -209 -89q-37 0 -75 8l-27 -49h-36l31 58q-30 11 -54 26
l-47 -84h-37zM111 240q0 -83 51 -139l230 411q-5 1 -15 1q-106 0 -186 -80t-80 -193zM187 79q24 -18 54 -29l241 434q-27 16 -56 23z" />
    <glyph glyph-name="Euro.onum" unicode="&#xf64e;" horiz-adv-x="610" 
d="M431 318h-280q-11 -39 -11 -78v-10h271l-7 -36h-260q15 -72 71.5 -114.5t136.5 -42.5q101 0 169 71l39 -31q-92 -89 -209 -89q-106 0 -178.5 55.5t-87.5 150.5h-61l7 36h51v7q0 40 11 81h-42l8 38h47q37 91 120 148.5t181 57.5q68 0 125 -30.5t87 -82.5l-46 -23
q-25 40 -70.5 63.5t-96.5 23.5q-76 0 -141.5 -43t-98.5 -114h274z" />
    <glyph glyph-name="florin.onum" unicode="&#xf64f;" horiz-adv-x="354" 
d="M30 -119h-58l76 340h-54v41h62l34 150q33 149 141 149q48 0 79 -30l-28 -41q-15 19 -45 19q-67 0 -89 -102l-33 -145h116v-41h-125z" />
    <glyph glyph-name="numbersign.onum" unicode="&#xf650;" horiz-adv-x="488" 
d="M495 390l-18 -37h-92l-84 -152h93l-21 -39h-95l-89 -162h-45l90 162h-97l-88 -162h-45l89 162h-88l21 39h90l84 152h-90l19 37h91l90 160h45l-90 -160h95l91 160h45l-91 -160h90zM339 353h-94l-85 -152h96z" />
    <glyph glyph-name="sterling.onum" unicode="&#xf651;" horiz-adv-x="415" 
d="M9 223l8 39h90q-37 74 -37 115q0 78 55 131t134 53q55 0 101.5 -28t61.5 -74l-46 -19q-10 38 -42.5 58t-72.5 20q-57 0 -97.5 -37.5t-40.5 -94.5q0 -14 2 -27t7.5 -27.5t9 -22.5t12 -25t10.5 -22h134l-9 -39h-110q4 -22 4 -32q0 -36 -26 -73t-57 -55q9 3 22 3
q30 0 75 -15.5t67 -15.5q44 0 76 32l18 -40q-41 -39 -99 -39q-37 0 -84.5 17.5t-82.5 17.5q-36 0 -92 -32l-11 43q64 25 103.5 67t39.5 84q0 15 -7 38h-116z" />
    <glyph glyph-name="yen.onum" unicode="&#xf652;" horiz-adv-x="539" 
d="M245 0h-55l25 116h-221l8 38h222l18 80h-222l9 39h193l-143 277h64l135 -266l249 266h67l-262 -277h189l-9 -39h-216l-18 -80h216l-8 -38h-216z" />
    <glyph glyph-name="cent.onum" unicode="&#xf654;" horiz-adv-x="581" 
d="M244 -96l19 89q-95 16 -152.5 81t-57.5 163q0 132 97 228.5t228 96.5h12l12 55h45l-14 -61q107 -22 157 -107l-46 -23q-40 63 -122 82l-104 -471h5q101 0 169 71l39 -31q-92 -89 -209 -89h-16l-18 -84h-44zM111 240q0 -78 44.5 -131.5t118.5 -67.5l105 472h-2
q-106 0 -186 -80t-80 -193z" />
    <glyph glyph-name="zero.dnom" unicode="&#xf655;" horiz-adv-x="384" 
d="M345 252q0 -36 -10.5 -78t-31.5 -84t-60 -69.5t-89 -27.5q-66 0 -101 42.5t-35 112.5q0 36 10.5 77.5t31.5 83.5t60 69.5t89 27.5q66 0 101 -42t35 -112zM299 252q0 116 -91 116q-38 0 -68 -23t-45.5 -59t-23 -71.5t-7.5 -67.5q0 -116 91 -116q38 0 68 23t45.5 59
t23 71.5t7.5 67.5z" />
    <glyph glyph-name="one.dnom" unicode="&#xf656;" horiz-adv-x="214" 
d="M100 0h-46l74 337l-77 -66l-22 30l119 99h40z" />
    <glyph glyph-name="two.dnom" unicode="&#xf657;" horiz-adv-x="384" 
d="M279 0h-281l9 39q63 38 96 59t76.5 51.5t65 52t37 47.5t15.5 50q0 34 -27 51.5t-64 17.5q-71 0 -113 -47l-23 30q53 55 136 55q55 0 96 -26t41 -79q0 -28 -16 -57t-37.5 -52.5t-62 -53t-71 -49.5t-82.5 -51h213z" />
    <glyph glyph-name="three.dnom" unicode="&#xf658;" horiz-adv-x="384" 
d="M306 115q0 -48 -39.5 -85t-103.5 -37q-110 0 -152 72l32 25q38 -59 119 -59q45 0 71.5 25.5t26.5 64.5q0 29 -29 47.5t-75 18.5q-22 0 -28 -1l8 39q30 -2 59 -2q45 0 72.5 20.5t27.5 56.5q0 31 -28.5 49.5t-69.5 18.5q-59 0 -109 -44l-20 29q59 53 131 53q60 0 100.5 -26
t40.5 -74q0 -45 -31.5 -73t-80.5 -32q31 -6 54.5 -29.5t23.5 -56.5z" />
    <glyph glyph-name="four.dnom" unicode="&#xf659;" horiz-adv-x="384" 
d="M313 107h-60l-24 -107h-44l23 107h-196l7 35l237 258h61l-57 -256h60zM216 144l47 211l-196 -211h149z" />
    <glyph glyph-name="five.dnom" unicode="&#xf65a;" horiz-adv-x="384" 
d="M311 142q0 -64 -44 -106.5t-111 -42.5q-106 0 -144 73l33 25q36 -60 111 -60q48 0 78.5 30t30.5 75q0 38 -26.5 58.5t-66.5 20.5q-54 0 -94 -39l-31 13l47 211h248l-8 -38h-203l-32 -145q38 33 90 33q50 0 86 -29t36 -79z" />
    <glyph glyph-name="six.dnom" unicode="&#xf65b;" horiz-adv-x="384" 
d="M317 142q0 -60 -41 -104.5t-111 -44.5t-106 39t-36 104q0 109 56.5 189.5t140.5 80.5q40 0 74 -13.5t50 -36.5l-29 -31q-26 43 -96 43q-46 0 -82 -37t-51 -76t-15 -64v-5q55 65 121 65q53 0 89 -28.5t36 -80.5zM271 136q0 37 -26 57.5t-69 20.5q-56 0 -108 -64
q-2 -5 -2 -22q0 -45 27 -71t71 -26q49 0 78 31.5t29 73.5z" />
    <glyph glyph-name="seven.dnom" unicode="&#xf65c;" horiz-adv-x="359" 
d="M342 369l-255 -369h-50l252 362h-228l8 38h280z" />
    <glyph glyph-name="eight.dnom" unicode="&#xf65d;" horiz-adv-x="384" 
d="M307 106q0 -49 -42 -81t-106 -32q-63 0 -105 28t-42 77q0 43 36.5 74.5t92.5 37.5q-78 29 -78 86q0 52 42 81t98 29q55 0 98 -25.5t43 -71.5q0 -42 -30 -71t-89 -37q35 -13 58.5 -37.5t23.5 -57.5zM299 300q0 34 -27 51.5t-69 17.5q-40 0 -68 -20.5t-28 -54.5
q0 -29 27.5 -48t53.5 -25q42 3 76.5 22t34.5 57zM262 108q0 26 -22.5 47.5t-59.5 33.5q-56 -4 -89.5 -28t-33.5 -60q0 -31 30 -51.5t70 -20.5q42 0 73.5 22.5t31.5 56.5z" />
    <glyph glyph-name="nine.dnom" unicode="&#xf65e;" horiz-adv-x="384" 
d="M48 258q0 60 41 104.5t111 44.5t105.5 -39t35.5 -105q0 -108 -56 -188.5t-140 -80.5q-88 0 -125 50l30 31q26 -43 95 -43q46 0 82 37t51 76.5t15 64.5v4q-55 -65 -121 -65q-53 0 -88.5 28.5t-35.5 80.5zM93 264q0 -37 26.5 -57.5t69.5 -20.5q55 0 107 64q2 5 2 22
q0 45 -26.5 71t-71.5 26q-49 0 -78 -31.5t-29 -73.5z" />
    <glyph glyph-name="zero.numr" unicode="&#xf661;" horiz-adv-x="384" 
d="M404 519q0 -36 -10.5 -78t-31.5 -84t-60 -69.5t-89 -27.5q-66 0 -101 42.5t-35 112.5q0 36 10.5 77.5t31.5 83.5t60 69.5t89 27.5q66 0 101 -42t35 -112zM358 519q0 116 -91 116q-38 0 -68 -23t-45.5 -59t-23 -71.5t-7.5 -67.5q0 -116 91 -116q38 0 68 23t45.5 59
t23 71.5t7.5 67.5z" />
    <glyph glyph-name="one.numr" unicode="&#xf662;" horiz-adv-x="214" 
d="M159 267h-46l74 337l-77 -66l-22 30l119 99h40z" />
    <glyph glyph-name="two.numr" unicode="&#xf663;" horiz-adv-x="384" 
d="M338 267h-281l9 39q63 38 96 59t76.5 51.5t65 52t37 47.5t15.5 50q0 34 -27 51.5t-64 17.5q-71 0 -113 -47l-23 30q53 55 136 55q55 0 96 -26t41 -79q0 -28 -16 -57t-37.5 -52.5t-62 -53t-71 -49.5t-82.5 -51h213z" />
    <glyph glyph-name="three.numr" unicode="&#xf664;" horiz-adv-x="384" 
d="M365 382q0 -48 -39.5 -85t-103.5 -37q-110 0 -152 72l32 25q38 -59 119 -59q45 0 71.5 25.5t26.5 64.5q0 29 -29 47.5t-75 18.5q-22 0 -28 -1l8 39q30 -2 59 -2q45 0 72.5 20.5t27.5 56.5q0 31 -28.5 49.5t-69.5 18.5q-59 0 -109 -44l-20 29q59 53 131 53q60 0 100.5 -26
t40.5 -74q0 -45 -31.5 -73t-80.5 -32q31 -6 54.5 -29.5t23.5 -56.5z" />
    <glyph glyph-name="four.numr" unicode="&#xf665;" horiz-adv-x="384" 
d="M372 374h-60l-24 -107h-44l23 107h-196l7 35l237 258h61l-57 -256h60zM275 411l47 211l-196 -211h149z" />
    <glyph glyph-name="five.numr" unicode="&#xf666;" horiz-adv-x="384" 
d="M370 409q0 -64 -44 -106.5t-111 -42.5q-106 0 -144 73l33 25q36 -60 111 -60q48 0 78.5 30t30.5 75q0 38 -26.5 58.5t-66.5 20.5q-54 0 -94 -39l-31 13l47 211h248l-8 -38h-203l-32 -145q38 33 90 33q50 0 86 -29t36 -79z" />
    <glyph glyph-name="six.numr" unicode="&#xf667;" horiz-adv-x="384" 
d="M376 409q0 -60 -41 -104.5t-111 -44.5t-106 39t-36 104q0 109 56.5 189.5t140.5 80.5q40 0 74 -13.5t50 -36.5l-29 -31q-26 43 -96 43q-46 0 -82 -37t-51 -76t-15 -64v-5q55 65 121 65q53 0 89 -28.5t36 -80.5zM330 403q0 37 -26 57.5t-69 20.5q-56 0 -108 -64
q-2 -5 -2 -22q0 -45 27 -71t71 -26q49 0 78 31.5t29 73.5z" />
    <glyph glyph-name="seven.numr" unicode="&#xf668;" horiz-adv-x="359" 
d="M401 636l-255 -369h-50l252 362h-228l8 38h280z" />
    <glyph glyph-name="eight.numr" unicode="&#xf669;" horiz-adv-x="384" 
d="M366 373q0 -49 -42 -81t-106 -32q-63 0 -105 28t-42 77q0 43 36.5 74.5t92.5 37.5q-78 29 -78 86q0 52 42 81t98 29q55 0 98 -25.5t43 -71.5q0 -42 -30 -71t-89 -37q35 -13 58.5 -37.5t23.5 -57.5zM358 567q0 34 -27 51.5t-69 17.5q-40 0 -68 -20.5t-28 -54.5
q0 -29 27.5 -48t53.5 -25q42 3 76.5 22t34.5 57zM321 375q0 26 -22.5 47.5t-59.5 33.5q-56 -4 -89.5 -28t-33.5 -60q0 -31 30 -51.5t70 -20.5q42 0 73.5 22.5t31.5 56.5z" />
    <glyph glyph-name="nine.numr" unicode="&#xf66a;" horiz-adv-x="384" 
d="M107 525q0 60 41 104.5t111 44.5t105.5 -39t35.5 -105q0 -108 -56 -188.5t-140 -80.5q-88 0 -125 50l30 31q26 -43 95 -43q46 0 82 37t51 76.5t15 64.5v4q-55 -65 -121 -65q-53 0 -88.5 28.5t-35.5 80.5zM152 531q0 -37 26.5 -57.5t69.5 -20.5q55 0 107 64q2 5 2 22
q0 45 -26.5 71t-71.5 26q-49 0 -78 -31.5t-29 -73.5z" />
    <glyph glyph-name="Abreve.smcp" unicode="&#xf66d;" horiz-adv-x="562" 
d="M316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM545 679q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="Amacron.smcp" unicode="&#xf66e;" horiz-adv-x="562" 
d="M544 627h-362l8 37h362zM316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263z" />
    <glyph glyph-name="Aogonek.smcp" unicode="&#xf66f;" horiz-adv-x="562" 
d="M316 550h64l116 -550q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5t-21.5 51.5q0 70 79 118l-28 131h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263z" />
    <glyph glyph-name="AEacute.smcp" unicode="&#xf670;" horiz-adv-x="811" 
d="M687 750l-204 -144h-46l186 144h64zM701 0h-369l30 133h-226l-114 -133h-64l476 550h388l-10 -49h-314l-43 -194h307l-10 -49h-308l-46 -209h314zM372 182l69 308l-266 -308h197z" />
    <glyph glyph-name="Cacute.smcp" unicode="&#xf671;" horiz-adv-x="581" 
d="M616 750l-204 -144h-46l186 144h64zM322 -12q-118 0 -193.5 68t-75.5 181q0 132 97 228.5t228 96.5q68 0 125 -30.5t87 -82.5l-46 -23q-25 40 -70.5 63.5t-96.5 23.5q-106 0 -186 -80t-80 -193q0 -91 60 -147t152 -56q101 0 169 71l39 -31q-92 -89 -209 -89z" />
    <glyph glyph-name="Ccaron.smcp" unicode="&#xf672;" horiz-adv-x="581" 
d="M453 606h-54l-68 144h37l62 -113l109 113h42zM322 -12q-118 0 -193.5 68t-75.5 181q0 132 97 228.5t228 96.5q68 0 125 -30.5t87 -82.5l-46 -23q-25 40 -70.5 63.5t-96.5 23.5q-106 0 -186 -80t-80 -193q0 -91 60 -147t152 -56q101 0 169 71l39 -31q-92 -89 -209 -89z
" />
    <glyph glyph-name="Ccircumflex.smcp" unicode="&#xf673;" horiz-adv-x="581" 
d="M550 606h-38l-62 113l-109 -113h-41l129 144h54zM322 -12q-118 0 -193.5 68t-75.5 181q0 132 97 228.5t228 96.5q68 0 125 -30.5t87 -82.5l-46 -23q-25 40 -70.5 63.5t-96.5 23.5q-106 0 -186 -80t-80 -193q0 -91 60 -147t152 -56q101 0 169 71l39 -31q-92 -89 -209 -89z
" />
    <glyph glyph-name="Cdotaccent.smcp" unicode="&#xf674;" horiz-adv-x="581" 
d="M322 -12q-118 0 -193.5 68t-75.5 181q0 132 97 228.5t228 96.5q68 0 125 -30.5t87 -82.5l-46 -23q-25 40 -70.5 63.5t-96.5 23.5q-106 0 -186 -80t-80 -193q0 -91 60 -147t152 -56q101 0 169 71l39 -31q-92 -89 -209 -89zM470 653q0 -18 -12.5 -30t-29.5 -12
q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="Dcaron.smcp" unicode="&#xf675;" horiz-adv-x="611" 
d="M423 606h-54l-68 144h37l62 -113l109 113h42zM224 0h-197l121 550h166q113 0 184.5 -69.5t71.5 -168.5q0 -57 -22 -111.5t-63 -100t-109 -73t-152 -27.5zM226 49h8q128 0 203 78.5t75 182.5q0 84 -56.5 137.5t-149.5 53.5h-114l-100 -452h134z" />
    <glyph glyph-name="Dcroat.smcp" unicode="&#xf676;" horiz-adv-x="611" 
d="M224 0h-197l57 260h-68l10 41h67l55 249h166q113 0 184.5 -69.5t71.5 -168.5q0 -57 -22 -111.5t-63 -100t-109 -73t-152 -27.5zM234 49q128 0 203 78.5t75 182.5q0 84 -56.5 137.5t-149.5 53.5h-114l-45 -200h142l-9 -41h-142l-46 -211h142z" />
    <glyph glyph-name="Ebreve.smcp" unicode="&#xf677;" horiz-adv-x="505" 
d="M395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313zM530 679q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="Ecaron.smcp" unicode="&#xf678;" horiz-adv-x="505" 
d="M375 606h-54l-68 144h37l62 -113l109 113h42zM395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313z" />
    <glyph glyph-name="Edotaccent.smcp" unicode="&#xf679;" horiz-adv-x="505" 
d="M395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313zM393 653q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="Emacron.smcp" unicode="&#xf67a;" horiz-adv-x="505" 
d="M395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313zM531 627h-362l8 37h362z" />
    <glyph glyph-name="Eng.smcp" unicode="&#xf67b;" horiz-adv-x="625" 
d="M292 -196q-110 0 -153 78l40 35q33 -64 110 -64q60 0 94.5 33.5t45.5 90.5l5 25l-249 469l-103 -471h-55l121 550h55l251 -464l102 464h55l-127 -579q-36 -167 -192 -167z" />
    <glyph glyph-name="Eogonek.smcp" unicode="&#xf67c;" horiz-adv-x="505" 
d="M396 -110l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5t-21.5 51.5q0 68 76 116h-304l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313l-10 -49q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43z" />
    <glyph glyph-name="Gbreve.smcp" unicode="&#xf67d;" horiz-adv-x="630" 
d="M323 -12q-116 0 -193 67.5t-77 177.5q0 132 94.5 230.5t231.5 98.5q67 0 125 -30t93 -79l-46 -27q-25 37 -72.5 62t-102.5 25q-111 0 -188 -83t-77 -194q0 -87 61 -143.5t152 -56.5q84 0 147 44l33 150h-186l10 47h241l-48 -223q-86 -66 -198 -66zM610 679
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="Gcircumflex.smcp" unicode="&#xf67e;" horiz-adv-x="630" 
d="M323 -12q-116 0 -193 67.5t-77 177.5q0 132 94.5 230.5t231.5 98.5q67 0 125 -30t93 -79l-46 -27q-25 37 -72.5 62t-102.5 25q-111 0 -188 -83t-77 -194q0 -87 61 -143.5t152 -56.5q84 0 147 44l33 150h-186l10 47h241l-48 -223q-86 -66 -198 -66zM551 606h-38l-62 113
l-109 -113h-41l129 144h54z" />
    <glyph glyph-name="Gcommaaccent.smcp" unicode="&#xf67f;" horiz-adv-x="630" 
d="M323 -12q-116 0 -193 67.5t-77 177.5q0 132 94.5 230.5t231.5 98.5q67 0 125 -30t93 -79l-46 -27q-25 37 -72.5 62t-102.5 25q-111 0 -188 -83t-77 -194q0 -87 61 -143.5t152 -56.5q84 0 147 44l33 150h-186l10 47h241l-48 -223q-86 -66 -198 -66zM305 -122
q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5z" />
    <glyph glyph-name="Gdotaccent.smcp" unicode="&#xf680;" horiz-adv-x="630" 
d="M323 -12q-116 0 -193 67.5t-77 177.5q0 132 94.5 230.5t231.5 98.5q67 0 125 -30t93 -79l-46 -27q-25 37 -72.5 62t-102.5 25q-111 0 -188 -83t-77 -194q0 -87 61 -143.5t152 -56.5q84 0 147 44l33 150h-186l10 47h241l-48 -223q-86 -66 -198 -66zM473 653
q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="Hcircumflex.smcp" unicode="&#xf681;" horiz-adv-x="629" 
d="M518 606h-38l-62 113l-109 -113h-41l129 144h54zM494 0h-54l57 259h-358l-57 -259h-55l121 550h55l-54 -243h358l54 243h54z" />
    <glyph glyph-name="Hbar.smcp" unicode="&#xf682;" horiz-adv-x="650" 
d="M505 0h-54l57 259h-358l-57 -259h-55l90 411h-80l9 38h80l22 101h55l-23 -101h358l23 101h54l-22 -101h78l-8 -38h-79zM160 307h358l23 104h-358z" />
    <glyph glyph-name="Ibreve.smcp" unicode="&#xf683;" horiz-adv-x="216" 
d="M82 0h-55l121 550h55zM373 679q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="IJ.smcp" unicode="&#xf684;" horiz-adv-x="633" 
d="M341 -12q-110 0 -153 78l39 35q35 -64 111 -64q113 0 140 123l86 390h55l-86 -392q-38 -170 -192 -170zM82 0h-55l121 550h55z" />
    <glyph glyph-name="Imacron.smcp" unicode="&#xf685;" horiz-adv-x="216" 
d="M374 627h-362l8 37h362zM82 0h-55l121 550h55z" />
    <glyph glyph-name="Iogonek.smcp" unicode="&#xf686;" horiz-adv-x="216" 
d="M82 0q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5t-21.5 51.5q0 73 86 121l120 545h55z" />
    <glyph glyph-name="Itilde.smcp" unicode="&#xf687;" horiz-adv-x="216" 
d="M82 0h-55l121 550h55zM257 604q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="Jcircumflex.smcp" unicode="&#xf688;" horiz-adv-x="417" 
d="M513 606h-38l-62 113l-109 -113h-41l129 144h54zM125 -12q-110 0 -153 78l39 35q35 -64 111 -64q113 0 140 123l86 390h55l-86 -392q-38 -170 -192 -170z" />
    <glyph glyph-name="Kcommaaccent.smcp" unicode="&#xf689;" horiz-adv-x="521" 
d="M234 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM447 0h-68l-180 257l-75 -66l-42 -191h-55l121 550h55l-64 -291l334 291h72l-305 -262z" />
    <glyph glyph-name="Lacute.smcp" unicode="&#xf68a;" horiz-adv-x="454" 
d="M359 0h-332l121 550h55l-111 -501h277zM522 750l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="Lcaron.smcp" unicode="&#xf68b;" horiz-adv-x="454" 
d="M367 515q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM359 0h-332l121 550h55l-111 -501h277z" />
    <glyph glyph-name="Lcommaaccent.smcp" unicode="&#xf68c;" horiz-adv-x="454" 
d="M209 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM359 0h-332l121 550h55l-111 -501h277z" />
    <glyph glyph-name="Ldot.smcp" unicode="&#xf68d;" horiz-adv-x="460" 
d="M359 0h-332l121 550h55l-111 -501h277zM349 246q-17 0 -29 11.5t-12 28.5q0 20 14.5 33.5t34.5 13.5q17 0 28.5 -11.5t11.5 -28.5q0 -19 -14 -33t-34 -14z" />
    <glyph glyph-name="Nacute.smcp" unicode="&#xf68e;" horiz-adv-x="625" 
d="M587 750l-204 -144h-46l186 144h64zM490 0h-54l-250 474l-104 -474h-55l121 550h55l250 -467l103 467h55z" />
    <glyph glyph-name="Ncaron.smcp" unicode="&#xf68f;" horiz-adv-x="625" 
d="M422 606h-54l-68 144h37l62 -113l109 113h42zM490 0h-54l-250 474l-104 -474h-55l121 550h55l250 -467l103 467h55z" />
    <glyph glyph-name="Ncommaaccent.smcp" unicode="&#xf690;" horiz-adv-x="625" 
d="M272 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM490 0h-54l-250 474l-104 -474h-55l121 550h55l250 -467l103 467h55z" />
    <glyph glyph-name="Obreve.smcp" unicode="&#xf691;" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM599 682
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="Ohungarumlaut.smcp" unicode="&#xf692;" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM496 750l-147 -144h-39
l129 144h57zM635 750l-147 -144h-39l129 144h57z" />
    <glyph glyph-name="Omacron.smcp" unicode="&#xf693;" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM599 627h-362l8 37h362z
" />
    <glyph glyph-name="Oslashacute.smcp" unicode="&#xf694;" horiz-adv-x="666" 
d="M312 -12q-99 0 -168 51l-37 -39h-54l61 66q-61 67 -61 172q0 130 90 227t222 97q98 0 166 -50l36 38h54l-60 -65q63 -68 63 -173q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 80 -44 135l-342 -366q51 -42 132 -42zM110 240q0 -80 42 -133l342 365
q-54 41 -130 41q-110 0 -182 -81.5t-72 -191.5zM607 750l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="Racute.smcp" unicode="&#xf695;" horiz-adv-x="533" 
d="M548 750l-204 -144h-46l186 144h64zM430 0h-66l-108 224h-125l-49 -224h-55l121 550h198q74 0 119.5 -40t45.5 -101q0 -74 -49.5 -127t-147.5 -56zM301 273h9q67 0 105 38t38 95q0 43 -32 69t-82 26h-146l-51 -228h159z" />
    <glyph glyph-name="Rcaron.smcp" unicode="&#xf696;" horiz-adv-x="533" 
d="M384 606h-54l-68 144h37l62 -113l109 113h42zM430 0h-66l-108 224h-125l-49 -224h-55l121 550h198q74 0 119.5 -40t45.5 -101q0 -74 -49.5 -127t-147.5 -56zM301 273h9q67 0 105 38t38 95q0 43 -32 69t-82 26h-146l-51 -228h159z" />
    <glyph glyph-name="Rcommaaccent.smcp" unicode="&#xf697;" horiz-adv-x="533" 
d="M233 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM430 0h-66l-108 224h-125l-49 -224h-55l121 550h198q74 0 119.5 -40t45.5 -101q0 -74 -49.5 -127t-147.5 -56zM301 273h9
q67 0 105 38t38 95q0 43 -32 69t-82 26h-146l-51 -228h159z" />
    <glyph glyph-name="Sacute.smcp" unicode="&#xf698;" horiz-adv-x="499" 
d="M528 750l-204 -144h-46l186 144h64zM236 -12q-155 0 -226 97l41 37q27 -39 77 -62t110 -23q74 0 108 33.5t34 80.5q0 35 -30 59.5t-72.5 40t-85.5 33t-73 49t-30 75.5q0 64 59 109t138 45q65 0 117.5 -24.5t81.5 -64.5l-38 -36q-23 35 -68 55.5t-94 20.5q-56 0 -96.5 -30
t-40.5 -74q0 -31 30 -53.5t72.5 -37.5t85.5 -33t73 -51t30 -81q0 -67 -51 -116t-152 -49z" />
    <glyph glyph-name="Scedilla.smcp" unicode="&#xf699;" horiz-adv-x="499" 
d="M165 -189q-65 0 -104 37l23 30q33 -34 83 -34q32 0 49 14.5t17 36.5q0 33 -40 33q-23 0 -37 -17l-24 16l39 67q-109 18 -161 91l41 37q27 -39 77 -62t110 -23q74 0 108 33.5t34 80.5q0 35 -30 59.5t-72.5 40t-85.5 33t-73 49t-30 75.5q0 64 59 109t138 45
q65 0 117.5 -24.5t81.5 -64.5l-38 -36q-23 35 -68 55.5t-94 20.5q-56 0 -96.5 -30t-40.5 -74q0 -31 30 -53.5t72.5 -37.5t85.5 -33t73 -51t30 -81q0 -67 -51 -116t-152 -49q-5 0 -15 0.5t-15 0.5l-28 -46q15 12 34 12q26 0 42 -15t16 -42q0 -38 -29 -62.5t-76 -24.5z" />
    <glyph glyph-name="Scircumflex.smcp" unicode="&#xf69a;" horiz-adv-x="499" 
d="M460 606h-38l-62 113l-109 -113h-41l129 144h54zM236 -12q-155 0 -226 97l41 37q27 -39 77 -62t110 -23q74 0 108 33.5t34 80.5q0 35 -30 59.5t-72.5 40t-85.5 33t-73 49t-30 75.5q0 64 59 109t138 45q65 0 117.5 -24.5t81.5 -64.5l-38 -36q-23 35 -68 55.5t-94 20.5
q-56 0 -96.5 -30t-40.5 -74q0 -31 30 -53.5t72.5 -37.5t85.5 -33t73 -51t30 -81q0 -67 -51 -116t-152 -49z" />
    <glyph glyph-name="Scommaaccent.smcp" unicode="&#xf69b;" horiz-adv-x="499" 
d="M215 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM236 -12q-155 0 -226 97l41 37q27 -39 77 -62t110 -23q74 0 108 33.5t34 80.5q0 35 -30 59.5t-72.5 40t-85.5 33t-73 49
t-30 75.5q0 64 59 109t138 45q65 0 117.5 -24.5t81.5 -64.5l-38 -36q-23 35 -68 55.5t-94 20.5q-56 0 -96.5 -30t-40.5 -74q0 -31 30 -53.5t72.5 -37.5t85.5 -33t73 -51t30 -81q0 -67 -51 -116t-152 -49z" />
    <glyph glyph-name="Tbar.smcp" unicode="&#xf69c;" horiz-adv-x="465" 
d="M206 0h-55l56 256h-122l9 38h121l46 207h-176l11 49h408l-11 -49h-177l-46 -207h125l-9 -38h-124z" />
    <glyph glyph-name="Tcaron.smcp" unicode="&#xf69d;" horiz-adv-x="465" 
d="M341 606h-54l-68 144h37l62 -113l109 113h42zM206 0h-55l110 501h-176l11 49h408l-11 -49h-177z" />
    <glyph glyph-name="Tcommaaccent.smcp" unicode="&#xf69e;" horiz-adv-x="465" 
d="M192 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM206 0h-55l110 501h-176l11 49h408l-11 -49h-177z" />
    <glyph glyph-name="Ubreve.smcp" unicode="&#xf69f;" horiz-adv-x="613" 
d="M287 -12q-104 0 -162 46.5t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-24 -111 -79.5 -166.5t-157.5 -55.5zM573 679q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25
q37 -75 130 -75t163 75z" />
    <glyph glyph-name="Uhungarumlaut.smcp" unicode="&#xf6a0;" horiz-adv-x="613" 
d="M287 -12q-104 0 -162 46.5t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-24 -111 -79.5 -166.5t-157.5 -55.5zM466 750l-147 -144h-39l129 144h57zM605 750l-147 -144h-39l129 144h57z
" />
    <glyph glyph-name="Umacron.smcp" unicode="&#xf6a1;" horiz-adv-x="613" 
d="M287 -12q-104 0 -162 46.5t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-24 -111 -79.5 -166.5t-157.5 -55.5zM573 627h-362l8 37h362z" />
    <glyph glyph-name="Uogonek.smcp" unicode="&#xf6a2;" horiz-adv-x="613" 
d="M287 -12q-104 0 -162 46.5t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-38 -173 -153 -210q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5
t-21.5 51.5q0 60 58 104h-2z" />
    <glyph glyph-name="Uring.smcp" unicode="&#xf6a3;" horiz-adv-x="613" 
d="M287 -12q-104 0 -162 46.5t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-24 -111 -79.5 -166.5t-157.5 -55.5zM392 574q-38 0 -62.5 24.5t-24.5 62.5q0 42 31.5 73.5t73.5 31.5
q38 0 62.5 -24.5t24.5 -62.5q0 -43 -31 -74t-74 -31zM393 606q29 0 50.5 20.5t21.5 49.5q0 26 -15.5 42t-40.5 16q-29 0 -50 -21t-21 -49q0 -26 15 -42t40 -16z" />
    <glyph glyph-name="Utilde.smcp" unicode="&#xf6a4;" horiz-adv-x="613" 
d="M287 -12q-104 0 -162 46.5t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-24 -111 -79.5 -166.5t-157.5 -55.5zM458 604q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36
q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="Wcircumflex.smcp" unicode="&#xf6a6;" horiz-adv-x="749" 
d="M579 606h-38l-62 113l-109 -113h-41l129 144h54zM515 0h-58l-35 463l-238 -463h-58l-44 550h59l32 -471l243 471h49l34 -472l241 472h61z" />
    <glyph glyph-name="Ycircumflex.smcp" unicode="&#xf6a9;" horiz-adv-x="539" 
d="M476 606h-38l-62 113l-109 -113h-41l129 144h54zM245 0h-55l52 235l-163 315h64l135 -266l249 266h67l-298 -315z" />
    <glyph glyph-name="Zacute.smcp" unicode="&#xf6ab;" horiz-adv-x="513" 
d="M524 750l-204 -144h-46l186 144h64zM418 0h-431l9 43l452 458h-350l10 49h424l-9 -44l-452 -457h357z" />
    <glyph glyph-name="Zdotaccent.smcp" unicode="&#xf6ac;" horiz-adv-x="513" 
d="M418 0h-431l9 43l452 458h-350l10 49h424l-9 -44l-452 -457h357zM380 653q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="Idotaccent.smcp" unicode="&#xf6ad;" horiz-adv-x="216" 
d="M82 0h-55l121 550h55zM235 653q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="parenleft.case" unicode="&#xf6ae;" horiz-adv-x="227" 
d="M137 -89l-36 -20q-56 132 -56 283q0 316 251 601l26 -26q-221 -298 -221 -599q0 -104 36 -239z" />
    <glyph glyph-name="parenright.case" unicode="&#xf6af;" horiz-adv-x="227" 
d="M129 755l35 20q57 -130 57 -283q0 -316 -251 -601l-26 26q221 297 221 599q0 104 -36 239z" />
    <glyph glyph-name="bracketleft.case" unicode="&#xf6b0;" horiz-adv-x="228" 
d="M134 -100h-173l192 868h174l-10 -43h-130l-173 -782h129z" />
    <glyph glyph-name="bracketright.case" unicode="&#xf6b1;" horiz-adv-x="228" 
d="M114 -100h-173l10 43h128l174 782h-130l10 43h174z" />
    <glyph glyph-name="braceleft.case" unicode="&#xf6b2;" horiz-adv-x="249" 
d="M155 -100h-49q-40 0 -70.5 27t-30.5 70q0 15 3 26l50 220q2 12 2 17q0 23 -10.5 38.5t-28.5 15.5l9 40q53 0 68 71l49 219q14 61 50.5 92.5t88.5 31.5h62l-10 -43h-62q-33 0 -55 -21t-30 -60l-51 -227q-15 -69 -64 -87q29 -15 29 -54q0 -18 -4 -34l-48 -218
q-2 -12 -2 -22q0 -25 16.5 -42t41.5 -17h56z" />
    <glyph glyph-name="braceright.case" unicode="&#xf6b3;" horiz-adv-x="249" 
d="M133 768h49q40 0 70 -27t30 -70q0 -18 -3 -27l-49 -219q-2 -12 -2 -18q0 -22 10.5 -37.5t28.5 -15.5l-10 -40q-52 0 -67 -71l-49 -219q-14 -61 -50 -92.5t-89 -31.5h-62l10 43h61q68 0 86 80l51 228q15 69 64 88q-29 14 -29 53q0 18 4 34l48 218q2 12 2 21q0 26 -16.5 43
t-41.5 17h-56z" />
    <glyph glyph-name="exclamdown.case" unicode="&#xf6b4;" horiz-adv-x="219" 
d="M143 493h41l-96 -493h-68zM198 677q17 0 28.5 -12t11.5 -29q0 -19 -14 -33t-34 -14q-17 0 -28.5 11.5t-11.5 28.5q0 20 14 34t34 14z" />
    <glyph glyph-name="questiondown.case" unicode="&#xf6b5;" horiz-adv-x="464" 
d="M259 466l46 24q26 -32 26 -71q0 -40 -26.5 -69.5t-64 -49l-75 -39t-64 -49.5t-26.5 -70q0 -45 37 -72.5t102 -27.5q98 0 167 71l28 -38q-85 -85 -200 -85q-88 0 -142.5 40t-54.5 105q0 50 27.5 87t67 59t78.5 41t66.5 43.5t27.5 55.5q0 25 -20 45zM331 676
q17 0 28.5 -11.5t11.5 -28.5q0 -20 -14 -33.5t-34 -13.5q-17 0 -28.5 11.5t-11.5 28.5q0 20 14 33.5t34 13.5z" />
    <glyph glyph-name="guillemotleft.case" unicode="&#xf6b6;" horiz-adv-x="402" 
d="M224 162h-53l-120 180l200 177h61l-204 -182zM349 162h-53l-120 180l200 177h61l-204 -182z" />
    <glyph glyph-name="guillemotright.case" unicode="&#xf6b7;" horiz-adv-x="402" 
d="M99 519h53l120 -180l-200 -177h-61l204 182zM224 519h53l120 -180l-200 -177h-61l204 182z" />
    <glyph glyph-name="guilsinglleft.case" unicode="&#xf6b8;" horiz-adv-x="277" 
d="M224 162h-53l-120 180l200 177h61l-204 -182z" />
    <glyph glyph-name="guilsinglright.case" unicode="&#xf6b9;" horiz-adv-x="277" 
d="M99 519h53l120 -180l-200 -177h-61l204 182z" />
    <glyph glyph-name="hyphen.case" unicode="&#xf6ba;" horiz-adv-x="300" 
d="M286 317h-240l10 48h240z" />
    <glyph glyph-name="endash.case" unicode="&#xf6bb;" horiz-adv-x="593" 
d="M579 317h-533l10 48h533z" />
    <glyph glyph-name="emdash.case" unicode="&#xf6bc;" horiz-adv-x="833" 
d="M819 317h-773l10 48h773z" />
    <glyph glyph-name="periodcentered.case" unicode="&#xf6bd;" horiz-adv-x="219" 
d="M127 299q-17 0 -29 11.5t-12 28.5q0 20 14.5 33.5t34.5 13.5q17 0 28.5 -11.5t11.5 -28.5q0 -19 -14 -33t-34 -14z" />
    <glyph glyph-name="bullet.case" unicode="&#xf6be;" horiz-adv-x="358" 
d="M305 351q0 -45 -35 -78.5t-81 -33.5q-39 0 -65.5 26.5t-26.5 65.5q0 46 34.5 79.5t80.5 33.5q40 0 66.5 -26.5t26.5 -66.5z" />
    <glyph glyph-name="cent.case" unicode="&#xf6bf;" horiz-adv-x="494" 
d="M202 0l19 85q-71 16 -113 70.5t-42 137.5q0 120 77.5 206t189.5 87l18 81h44l-19 -84q90 -15 131 -85l-40 -30q-33 56 -101 68l-92 -410h12q79 0 131 62l30 -36q-70 -73 -166 -73h-17l-18 -79h-44zM121 292q0 -64 30 -105t81 -55l90 406q-90 -6 -145.5 -78t-55.5 -168z
" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="219" 
d="M70 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5z" />
    <glyph glyph-name="one.tnum" unicode="&#xf6dc;" horiz-adv-x="588" 
d="M318 0h-57l129 589l-131 -114l-27 37l181 155h52z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="1103" 
d="M807 -12q-58 0 -103 25.5t-71 68.5l-59 -266h-52l148 667h52l-16 -73q65 85 166 85q84 0 136 -53.5t52 -145.5q0 -125 -71 -216.5t-182 -91.5zM802 35q88 0 144.5 76t56.5 176q0 74 -40 117.5t-106 43.5q-49 0 -93 -26.5t-71 -66.5l-51 -232q19 -38 61.5 -63t98.5 -25z
M489 0h-65l-126 276h-151l-61 -276h-57l147 667h229q72 0 126.5 -46t54.5 -122q0 -94 -62.5 -155.5t-165.5 -65.5zM343 328h2q84 0 131.5 48.5t47.5 119.5q0 51 -38 85t-88 34h-176l-63 -287h184z" />
    <glyph glyph-name="centinferior" unicode="&#xf6df;" horiz-adv-x="305" 
d="M71 -151l13 57q-46 11 -72 44t-26 82q0 70 47.5 121.5t115.5 52.5l10 42h33l-10 -45q56 -10 81 -54l-33 -21q-17 33 -56 41l-51 -234h1q45 0 80 38l25 -24q-45 -47 -106 -47h-8l-11 -53h-33zM29 33q0 -76 62 -93l51 231q-49 -4 -81 -44.5t-32 -93.5z" />
    <glyph glyph-name="centsuperior" unicode="&#xf6e0;" horiz-adv-x="305" 
d="M196 416l13 57q-46 11 -72 44t-26 82q0 70 47.5 121.5t115.5 52.5l10 42h33l-10 -45q56 -10 81 -54l-33 -21q-17 33 -56 41l-51 -234h1q45 0 80 38l25 -24q-45 -47 -106 -47h-8l-11 -53h-33zM154 600q0 -76 62 -93l51 231q-49 -4 -81 -44.5t-32 -93.5z" />
    <glyph glyph-name="commainferior" unicode="&#xf6e1;" horiz-adv-x="131" 
d="M12 -120q0 -28 -21 -57t-46 -44l-17 20q42 24 48 52q-2 -1 -4 -1q-9 0 -16.5 7.5t-7.5 19.5q0 14 11 23.5t25 9.5q12 0 20 -8t8 -22z" />
    <glyph glyph-name="commasuperior" unicode="&#xf6e2;" horiz-adv-x="131" 
d="M137 447q0 -28 -21 -57t-46 -44l-17 20q42 24 48 52q-2 -1 -4 -1q-9 0 -16.5 7.5t-7.5 19.5q0 14 11 23.5t25 9.5q12 0 20 -8t8 -22z" />
    <glyph glyph-name="dollarinferior" unicode="&#xf6e3;" horiz-adv-x="364" 
d="M71 -206l12 57q-85 13 -121 69l34 28q32 -49 95 -61l34 155q-26 9 -41.5 16.5t-34 20t-27 29.5t-8.5 40q0 45 40.5 78.5t104.5 33.5q10 0 14 -1l13 56h33l-13 -61q69 -15 100 -61l-32 -25q-24 38 -76 51l-31 -143q34 -13 54 -24t37.5 -33.5t17.5 -52.5q0 -48 -37.5 -83.5
t-110.5 -35.5h-13l-11 -53h-33zM228 -38q0 25 -18.5 40t-53.5 28l-33 -147h5q46 0 73 22t27 57zM62 152q0 -23 18.5 -37t54.5 -27l31 137h-7q-41 0 -69 -20.5t-28 -52.5z" />
    <glyph glyph-name="dollarsuperior" unicode="&#xf6e4;" horiz-adv-x="364" 
d="M196 361l12 57q-85 13 -121 69l34 28q32 -49 95 -61l34 155q-26 9 -41.5 16.5t-34 20t-27 29.5t-8.5 40q0 45 40.5 78.5t104.5 33.5q10 0 14 -1l13 56h33l-13 -61q69 -15 100 -61l-32 -25q-24 38 -76 51l-31 -143q34 -13 54 -24t37.5 -33.5t17.5 -52.5q0 -48 -37.5 -83.5
t-110.5 -35.5h-13l-11 -53h-33zM353 529q0 25 -18.5 40t-53.5 28l-33 -147h5q46 0 73 22t27 57zM187 719q0 -23 18.5 -37t54.5 -27l31 137h-7q-41 0 -69 -20.5t-28 -52.5z" />
    <glyph glyph-name="hypheninferior" unicode="&#xf6e5;" horiz-adv-x="184" 
d="M120 35h-149l8 36h148z" />
    <glyph glyph-name="hyphensuperior" unicode="&#xf6e6;" horiz-adv-x="184" 
d="M245 603h-149l8 36h148z" />
    <glyph glyph-name="periodinferior" unicode="&#xf6e7;" horiz-adv-x="136" 
d="M18 -118q0 -13 -11 -23t-25 -10q-12 0 -20 8t-8 20q0 13 11 23t25 10q12 0 20 -7.5t8 -20.5z" />
    <glyph glyph-name="periodsuperior" unicode="&#xf6e8;" horiz-adv-x="136" 
d="M143 449q0 -13 -11 -23t-25 -10q-12 0 -20 8t-8 20q0 13 11 23t25 10q12 0 20 -7.5t8 -20.5z" />
    <glyph glyph-name="asuperior" unicode="&#xf6e9;" horiz-adv-x="409" 
d="M267 613q-55 0 -93 -46.5t-38 -110.5q0 -49 26 -76t68 -27q59 0 108 57l34 152q-13 23 -41.5 37t-63.5 14zM389 640h43l-70 -314h-43l11 50q-47 -58 -113 -58q-53 0 -89.5 35t-36.5 100q0 78 49 136.5t121 58.5q80 0 117 -57z" />
    <glyph glyph-name="bsuperior" unicode="&#xf6ea;" horiz-adv-x="408" 
d="M248 353q54 0 92 46.5t38 110.5q0 49 -26 76t-68 27q-58 0 -108 -56l-34 -152q13 -24 42 -38t64 -14zM126 326h-43l96 434h43l-38 -170q49 58 113 58q54 0 90 -35t36 -100q0 -79 -48.5 -137t-121.5 -58q-80 0 -117 57z" />
    <glyph glyph-name="dsuperior" unicode="&#xf6eb;" horiz-adv-x="409" 
d="M267 613q-55 0 -93 -46.5t-38 -110.5q0 -49 26 -76t68 -27q59 0 108 57l34 152q-13 23 -41.5 37t-63.5 14zM415 760h43l-96 -434h-43l11 50q-47 -58 -113 -58q-53 0 -89.5 35t-36.5 100q0 78 49 136.5t121 58.5q80 0 117 -57z" />
    <glyph glyph-name="esuperior" unicode="&#xf6ec;" horiz-adv-x="401" 
d="M248 318q-72 0 -114 38.5t-42 104.5q0 75 53.5 131t128.5 56q62 0 100 -37.5t38 -98.5q0 -19 -4 -43h-272q-1 -6 -1 -12q0 -45 31.5 -75t83.5 -30q56 0 101 35l15 -28q-54 -41 -118 -41zM141 502h232v9q0 45 -28 74.5t-74 29.5q-50 0 -85 -34t-45 -79z" />
    <glyph glyph-name="isuperior" unicode="&#xf6ed;" horiz-adv-x="170" 
d="M185 690q-11 0 -19 7.5t-8 18.5q0 14 11 24t25 10q11 0 18.5 -7.5t7.5 -18.5q0 -13 -10.5 -23.5t-24.5 -10.5zM124 326h-43l69 314h43z" />
    <glyph glyph-name="lsuperior" unicode="&#xf6ee;" horiz-adv-x="170" 
d="M124 326h-43l96 434h43z" />
    <glyph glyph-name="msuperior" unicode="&#xf6ef;" horiz-adv-x="561" 
d="M512 326h-42l47 214q3 12 3 22q0 23 -14 36.5t-39 13.5q-50 0 -99 -55l-50 -231h-42l47 214q3 12 3 22q0 50 -56 50q-47 0 -96 -55l-50 -231h-43l69 314h43l-10 -47q16 20 44.5 37.5t60.5 17.5q33 0 55.5 -16.5t27.5 -42.5q53 59 112 59q35 0 58 -19.5t23 -55.5
q0 -12 -3 -30z" />
    <glyph glyph-name="osuperior" unicode="&#xf6f0;" horiz-adv-x="405" 
d="M237 318q-66 0 -105.5 39t-39.5 102q0 77 52 133t127 56q66 0 105.5 -39t39.5 -102q0 -77 -52 -133t-127 -56zM237 355q59 0 97 45.5t38 106.5q0 45 -28.5 74t-73.5 29q-59 0 -96 -44.5t-37 -106.5q0 -46 27.5 -75t72.5 -29z" />
    <glyph glyph-name="rsuperior" unicode="&#xf6f1;" horiz-adv-x="233" 
d="M124 326h-43l69 314h43l-12 -52q33 31 60 45t62 14l-10 -43q-4 2 -18 2q-26 0 -55.5 -15.5t-45.5 -37.5z" />
    <glyph glyph-name="ssuperior" unicode="&#xf6f2;" horiz-adv-x="327" 
d="M196 318q-86 0 -132 60l27 23q15 -22 43 -36.5t62 -14.5q36 0 59 17t23 41q0 21 -18 35t-43.5 22.5t-51 18.5t-43.5 28t-18 45q0 35 33.5 63t87.5 28q81 0 122 -54l-26 -24q-34 46 -97 46q-35 0 -56 -16t-21 -41q0 -18 18 -30t43.5 -20t51 -17.5t43.5 -30t18 -50.5
q0 -35 -33.5 -64t-91.5 -29z" />
    <glyph glyph-name="tsuperior" unicode="&#xf6f3;" horiz-adv-x="193" 
d="M161 318q-65 0 -65 54q0 9 2 19l47 216h-53l7 33h53l19 86h43l-19 -86h65l-7 -33h-65l-46 -211q-2 -10 -2 -16q0 -27 31 -27q17 0 33 12l7 -31q-21 -16 -50 -16z" />
    <glyph glyph-name="Lslash.smcp" unicode="&#xf6f9;" horiz-adv-x="482" 
d="M-3 185l12 52l112 58l56 255h55l-50 -224l114 59l-12 -54l-114 -58l-49 -224h277l-10 -49h-332l53 242z" />
    <glyph glyph-name="OE.smcp" unicode="&#xf6fa;" horiz-adv-x="964" 
d="M854 0h-369l21 96q-73 -108 -202 -108q-115 0 -183 69t-68 181q0 84 39.5 158t112 120t160.5 46q74 0 131 -34.5t82 -106.5l28 129h369l-11 -49h-313l-44 -194h308l-11 -49h-307l-46 -209h313zM526 190l38 169q-18 79 -73 116.5t-126 37.5q-107 0 -181 -80t-74 -193
q0 -93 57 -148t146 -55q70 0 126.5 39.5t86.5 113.5z" />
    <glyph glyph-name="Scaron.smcp" unicode="&#xf6fd;" horiz-adv-x="499" 
d="M365 606h-54l-68 144h37l62 -113l109 113h42zM236 -12q-155 0 -226 97l41 37q27 -39 77 -62t110 -23q74 0 108 33.5t34 80.5q0 35 -30 59.5t-72.5 40t-85.5 33t-73 49t-30 75.5q0 64 59 109t138 45q65 0 117.5 -24.5t81.5 -64.5l-38 -36q-23 35 -68 55.5t-94 20.5
q-56 0 -96.5 -30t-40.5 -74q0 -31 30 -53.5t72.5 -37.5t85.5 -33t73 -51t30 -81q0 -67 -51 -116t-152 -49z" />
    <glyph glyph-name="Zcaron.smcp" unicode="&#xf6ff;" horiz-adv-x="513" 
d="M362 606h-54l-68 144h37l62 -113l109 113h42zM418 0h-431l9 43l452 458h-350l10 49h424l-9 -44l-452 -457h357z" />
    <glyph glyph-name="exclam.smcp" unicode="&#xf721;" horiz-adv-x="219" 
d="M209 550l-101 -401h-39l75 401h65zM59 -10q-17 0 -28.5 12t-11.5 29q0 20 14 33.5t34 13.5q17 0 28.5 -12t11.5 -29q0 -19 -14 -33t-34 -14z" />
    <glyph glyph-name="dollar.onum" unicode="&#xf724;" horiz-adv-x="499" 
d="M158 -74l15 67q-109 18 -163 92l41 37q42 -63 133 -80l50 225q-34 13 -54.5 23t-44 27t-35 39.5t-11.5 51.5q0 64 59 109t138 45q2 0 6.5 -0.5t6.5 -0.5l15 67h45l-16 -72q94 -18 142 -83l-38 -36q-36 55 -115 71l-45 -203q35 -13 56.5 -24t46.5 -29t37 -43t12 -56
q0 -67 -51 -116t-152 -49q-13 0 -19 1l-14 -63h-45zM380 151q0 35 -27 57.5t-78 42.5l-47 -214h10q74 0 108 33.5t34 80.5zM148 409q0 -31 25 -50.5t72 -38.5l43 193h-3q-56 0 -96.5 -30t-40.5 -74z" />
    <glyph glyph-name="ampersand.smcp" unicode="&#xf726;" horiz-adv-x="552" 
d="M449 0h-66q-15 17 -43 53q-80 -65 -165 -65q-76 0 -123 39t-47 108q0 71 44 111.5t123 70.5q-23 55 -23 96q0 63 46 106t107 43q50 0 85.5 -25.5t35.5 -74.5q0 -20 -6 -37.5t-13.5 -30t-25 -26t-28 -20.5t-36 -18.5t-36 -15.5t-40.5 -15q20 -39 52 -85q14 -21 36.5 -54
t25.5 -37q60 63 101 142l40 -22q-59 -97 -115 -155q13 -18 71 -88zM180 30q64 0 133 57q-32 44 -66 95q-31 46 -58 98q-62 -25 -96 -57t-34 -84q0 -48 32.5 -78.5t88.5 -30.5zM200 413q0 -33 20 -79q38 14 59.5 23t46.5 24.5t36.5 34.5t11.5 44q0 62 -71 62
q-44 0 -73.5 -30.5t-29.5 -78.5z" />
    <glyph glyph-name="zero.onum" unicode="&#xf730;" horiz-adv-x="608" 
d="M564 328q0 -42 -11 -87.5t-34.5 -91t-55.5 -81t-78.5 -58t-100.5 -22.5q-112 0 -170 62.5t-58 171.5q0 53 18 110.5t51.5 109.5t89 86t121.5 34q112 0 170 -62.5t58 -171.5zM504 325q0 89 -42 137t-126 48q-53 0 -97 -29t-69.5 -73.5t-39.5 -92.5t-14 -90q0 -89 42 -137
t126 -48q43 0 80.5 19.5t62.5 50t43 69t26 76t8 70.5z" />
    <glyph glyph-name="one.onum" unicode="&#xf731;" horiz-adv-x="308" 
d="M171 0h-57l103 472l-131 -114l-27 37l181 155h52z" />
    <glyph glyph-name="two.onum" unicode="&#xf732;" horiz-adv-x="576" 
d="M425 0h-415l10 50q215 85 328.5 166.5t113.5 170.5q0 55 -42.5 89t-107.5 34q-109 0 -190 -78l-30 39q91 91 224 91q88 0 147 -44.5t59 -122.5q0 -53 -31.5 -104t-88.5 -94t-122 -78t-145 -67h301z" />
    <glyph glyph-name="three.onum" unicode="&#xf733;" horiz-adv-x="542" 
d="M212 -127q-81 0 -148.5 39t-90.5 100l46 30q22 -54 75 -85.5t118 -31.5q80 0 128 45t48 115q0 53 -43.5 85t-115.5 32q-46 0 -57 -1l13 54q9 -1 89 -1q72 0 122.5 35.5t50.5 101.5q0 51 -44.5 85t-117.5 34q-95 0 -171 -70l-28 40q38 38 92 60t110 22q96 0 156.5 -44
t60.5 -122q0 -74 -55.5 -121t-125.5 -52q52 -12 86.5 -49t34.5 -92q0 -87 -65 -148t-168 -61z" />
    <glyph glyph-name="four.onum" unicode="&#xf734;" horiz-adv-x="541" 
d="M466 66h-97l-41 -183h-57l40 183h-316l12 52l391 432h77l-95 -432h97zM322 118l83 375l-340 -375h257z" />
    <glyph glyph-name="five.onum" unicode="&#xf735;" horiz-adv-x="582" 
d="M245 -129q-166 0 -229 135l46 33q51 -116 188 -116q76 0 130 52.5t54 129.5q0 67 -45 104.5t-116 37.5q-77 0 -146 -51l-38 23l73 331h385l-11 -52h-328l-55 -247q60 46 143 46t140.5 -49.5t57.5 -137.5q0 -98 -70.5 -168.5t-178.5 -70.5z" />
    <glyph glyph-name="six.onum" unicode="&#xf736;" horiz-adv-x="584" 
d="M275 -12q-103 0 -158.5 62.5t-55.5 177.5q0 55 11 114t36.5 119.5t61 108t89.5 77.5t118 30q138 0 192 -104l-43 -37q-40 89 -152 89q-51 0 -94 -25.5t-71.5 -69t-47.5 -90.5t-31 -102q-1 -3 -5 -19q32 35 87 64t113 29q89 0 144 -46.5t55 -126.5q0 -102 -72 -176.5
t-177 -74.5zM277 40q79 0 133 57.5t54 133.5q0 60 -43 95.5t-114 35.5q-103 0 -189 -96q-2 -18 -2 -53q0 -78 42 -125.5t119 -47.5z" />
    <glyph glyph-name="seven.onum" unicode="&#xf737;" horiz-adv-x="502" 
d="M107 -117h-66l414 615h-366l11 52h437l-9 -40z" />
    <glyph glyph-name="eight.onum" unicode="&#xf738;" 
d="M266 -12q-101 0 -165 44t-64 123q0 76 57.5 127.5t151.5 68.5q-51 19 -86.5 56t-35.5 87q0 87 68.5 135t156.5 48q89 0 152 -43.5t63 -117.5q0 -73 -54 -119.5t-142 -58.5q52 -22 91.5 -62t39.5 -98q0 -82 -68 -136t-165 -54zM313 368q86 7 138 43.5t52 98.5
q0 48 -45.5 81t-110.5 33q-68 0 -116.5 -36t-48.5 -97q0 -43 41.5 -77.5t89.5 -45.5zM267 40q68 0 121 39t53 103q0 50 -44 86.5t-95 50.5q-89 -6 -147 -48.5t-58 -106.5q0 -59 49 -91.5t121 -32.5z" />
    <glyph glyph-name="nine.onum" unicode="&#xf739;" horiz-adv-x="584" 
d="M321 562q103 0 158.5 -62.5t55.5 -177.5q0 -55 -11 -114.5t-36 -119.5t-61 -107.5t-90 -77.5t-118 -30q-138 0 -191 104l42 37q40 -89 153 -89q51 0 93.5 25.5t71 69t47.5 90.5t32 102q1 3 2 7.5t1.5 7t0.5 3.5q-32 -35 -87 -63.5t-113 -28.5q-89 0 -144 46.5t-55 126.5
q0 102 72 176.5t177 74.5zM319 510q-79 0 -133 -57.5t-54 -133.5q0 -60 43 -95.5t114 -35.5q105 0 189 96q2 18 2 53q0 78 -42 125.5t-119 47.5z" />
    <glyph glyph-name="question.smcp" unicode="&#xf73f;" horiz-adv-x="397" 
d="M412 438q0 -41 -23.5 -71.5t-57 -48t-67.5 -32.5t-57.5 -33.5t-23.5 -42.5q0 -19 22 -38l-42 -21q-28 26 -28 57q0 33 22.5 57.5t55 40l65 31t55 39.5t22.5 56q0 39 -32.5 60.5t-88.5 21.5q-86 0 -155 -69l-26 37q77 79 186 79q78 0 125.5 -34.5t47.5 -88.5zM138 -10
q-17 0 -28.5 12t-11.5 29q0 19 14 33t34 14q17 0 29 -12t12 -29q0 -19 -14.5 -33t-34.5 -14z" />
    <glyph glyph-name="A.smcp" unicode="&#xf761;" horiz-adv-x="562" 
d="M316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263z" />
    <glyph glyph-name="B.smcp" unicode="&#xf762;" horiz-adv-x="541" 
d="M283 0h-256l121 550h213q68 0 113.5 -31.5t45.5 -92.5q0 -53 -33.5 -94.5t-91.5 -52.5q34 -13 56.5 -45t22.5 -69q0 -73 -51 -119t-140 -46zM92 49h193q56 0 93.5 33t37.5 84q0 41 -27.5 66.5t-75.5 25.5h-175zM327 307h17q54 0 85.5 33.5t31.5 78.5q0 41 -31 61.5
t-80 20.5h-157l-45 -194h179z" />
    <glyph glyph-name="C.smcp" unicode="&#xf763;" horiz-adv-x="581" 
d="M322 -12q-118 0 -193.5 68t-75.5 181q0 132 97 228.5t228 96.5q68 0 125 -30.5t87 -82.5l-46 -23q-25 40 -70.5 63.5t-96.5 23.5q-106 0 -186 -80t-80 -193q0 -91 60 -147t152 -56q101 0 169 71l39 -31q-92 -89 -209 -89z" />
    <glyph glyph-name="D.smcp" unicode="&#xf764;" horiz-adv-x="611" 
d="M224 0h-197l121 550h166q113 0 184.5 -69.5t71.5 -168.5q0 -57 -22 -111.5t-63 -100t-109 -73t-152 -27.5zM226 49h8q128 0 203 78.5t75 182.5q0 84 -56.5 137.5t-149.5 53.5h-114l-100 -452h134z" />
    <glyph glyph-name="E.smcp" unicode="&#xf765;" horiz-adv-x="505" 
d="M395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313z" />
    <glyph glyph-name="F.smcp" unicode="&#xf766;" horiz-adv-x="484" 
d="M82 0h-55l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308z" />
    <glyph glyph-name="G.smcp" unicode="&#xf767;" horiz-adv-x="630" 
d="M323 -12q-116 0 -193 67.5t-77 177.5q0 132 94.5 230.5t231.5 98.5q67 0 125 -30t93 -79l-46 -27q-25 37 -72.5 62t-102.5 25q-111 0 -188 -83t-77 -194q0 -87 61 -143.5t152 -56.5q84 0 147 44l33 150h-186l10 47h241l-48 -223q-86 -66 -198 -66z" />
    <glyph glyph-name="H.smcp" unicode="&#xf768;" horiz-adv-x="629" 
d="M494 0h-54l57 259h-358l-57 -259h-55l121 550h55l-54 -243h358l54 243h54z" />
    <glyph glyph-name="I.smcp" unicode="&#xf769;" horiz-adv-x="216" 
d="M82 0h-55l121 550h55z" />
    <glyph glyph-name="J.smcp" unicode="&#xf76a;" horiz-adv-x="417" 
d="M125 -12q-110 0 -153 78l39 35q35 -64 111 -64q113 0 140 123l86 390h55l-86 -392q-38 -170 -192 -170z" />
    <glyph glyph-name="K.smcp" unicode="&#xf76b;" horiz-adv-x="521" 
d="M447 0h-68l-180 257l-75 -66l-42 -191h-55l121 550h55l-64 -291l334 291h72l-305 -262z" />
    <glyph glyph-name="L.smcp" unicode="&#xf76c;" horiz-adv-x="454" 
d="M359 0h-332l121 550h55l-111 -501h277z" />
    <glyph glyph-name="M.smcp" unicode="&#xf76d;" horiz-adv-x="703" 
d="M568 0h-55l105 472l-309 -472h-23l-99 472l-105 -472h-55l121 550h75l95 -449l293 449h78z" />
    <glyph glyph-name="N.smcp" unicode="&#xf76e;" horiz-adv-x="625" 
d="M490 0h-54l-250 474l-104 -474h-55l121 550h55l250 -467l103 467h55z" />
    <glyph glyph-name="O.smcp" unicode="&#xf76f;" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57z" />
    <glyph glyph-name="P.smcp" unicode="&#xf770;" horiz-adv-x="509" 
d="M82 0h-55l121 550h195q76 0 121.5 -38.5t45.5 -100.5q0 -77 -54.5 -132t-144.5 -55h-180zM142 273h172q63 0 100.5 39t37.5 97q0 43 -31 67.5t-86 24.5h-142z" />
    <glyph glyph-name="Q.smcp" unicode="&#xf771;" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -77 -34 -146.5t-94 -114.5l34 -49l-41 -29l-36 50q-67 -35 -141 -35zM313 37q62 0 112 26l-71 100l42 30l71 -102q47 39 73.5 97t26.5 122q0 89 -54.5 146t-148.5 57
q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57z" />
    <glyph glyph-name="R.smcp" unicode="&#xf772;" horiz-adv-x="533" 
d="M430 0h-66l-108 224h-125l-49 -224h-55l121 550h198q74 0 119.5 -40t45.5 -101q0 -74 -49.5 -127t-147.5 -56zM301 273h9q67 0 105 38t38 95q0 43 -32 69t-82 26h-146l-51 -228h159z" />
    <glyph glyph-name="S.smcp" unicode="&#xf773;" horiz-adv-x="499" 
d="M236 -12q-155 0 -226 97l41 37q27 -39 77 -62t110 -23q74 0 108 33.5t34 80.5q0 35 -30 59.5t-72.5 40t-85.5 33t-73 49t-30 75.5q0 64 59 109t138 45q65 0 117.5 -24.5t81.5 -64.5l-38 -36q-23 35 -68 55.5t-94 20.5q-56 0 -96.5 -30t-40.5 -74q0 -31 30 -53.5
t72.5 -37.5t85.5 -33t73 -51t30 -81q0 -67 -51 -116t-152 -49z" />
    <glyph glyph-name="T.smcp" unicode="&#xf774;" horiz-adv-x="465" 
d="M206 0h-55l110 501h-176l11 49h408l-11 -49h-177z" />
    <glyph glyph-name="U.smcp" unicode="&#xf775;" horiz-adv-x="613" 
d="M287 -12q-104 0 -162 46.5t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-24 -111 -79.5 -166.5t-157.5 -55.5z" />
    <glyph glyph-name="V.smcp" unicode="&#xf776;" horiz-adv-x="562" 
d="M259 0h-64l-116 550h61l99 -488l315 488h63z" />
    <glyph glyph-name="W.smcp" unicode="&#xf777;" horiz-adv-x="749" 
d="M515 0h-58l-35 463l-238 -463h-58l-44 550h59l32 -471l243 471h49l34 -472l241 472h61z" />
    <glyph glyph-name="X.smcp" unicode="&#xf778;" horiz-adv-x="560" 
d="M492 0h-66l-145 244l-251 -244h-70l294 282l-160 268h66l134 -229l236 229h70l-278 -267z" />
    <glyph glyph-name="Y.smcp" unicode="&#xf779;" horiz-adv-x="539" 
d="M245 0h-55l52 235l-163 315h64l135 -266l249 266h67l-298 -315z" />
    <glyph glyph-name="Z.smcp" unicode="&#xf77a;" horiz-adv-x="513" 
d="M418 0h-431l9 43l452 458h-350l10 49h424l-9 -44l-452 -457h357z" />
    <glyph glyph-name="Agrave.smcp" unicode="&#xf7e0;" horiz-adv-x="562" 
d="M316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM415 606h-44l-140 144h62z" />
    <glyph glyph-name="Aacute.smcp" unicode="&#xf7e1;" horiz-adv-x="562" 
d="M552 750l-204 -144h-46l186 144h64zM316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263z" />
    <glyph glyph-name="Acircumflex.smcp" unicode="&#xf7e2;" horiz-adv-x="562" 
d="M484 606h-38l-62 113l-109 -113h-41l129 144h54zM316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263z" />
    <glyph glyph-name="Atilde.smcp" unicode="&#xf7e3;" horiz-adv-x="562" 
d="M316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM430 604q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37
z" />
    <glyph glyph-name="Adieresis.smcp" unicode="&#xf7e4;" horiz-adv-x="562" 
d="M509 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM305 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM316 550h64l116 -550h-60
l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263z" />
    <glyph glyph-name="Aring.smcp" unicode="&#xf7e5;" horiz-adv-x="562" 
d="M366 574q-38 0 -62.5 24.5t-24.5 62.5q0 42 31.5 73.5t73.5 31.5q38 0 62.5 -24.5t24.5 -62.5q0 -43 -31 -74t-74 -31zM367 606q29 0 50.5 20.5t21.5 49.5q0 26 -15.5 42t-40.5 16q-29 0 -50 -21t-21 -49q0 -26 15 -42t40 -16zM316 550h64l116 -550h-60l-29 133h-299
l-85 -133h-65zM400 182l-63 308l-200 -308h263z" />
    <glyph glyph-name="AE.smcp" unicode="&#xf7e6;" horiz-adv-x="811" 
d="M701 0h-369l30 133h-226l-114 -133h-64l476 550h388l-10 -49h-314l-43 -194h307l-10 -49h-308l-46 -209h314zM372 182l69 308l-266 -308h197z" />
    <glyph glyph-name="Ccedilla.smcp" unicode="&#xf7e7;" horiz-adv-x="581" 
d="M263 -189q-65 0 -104 37l23 30q33 -34 83 -34q32 0 49 14.5t17 36.5q0 33 -40 33q-23 0 -37 -17l-24 16l38 65q-98 15 -156.5 80.5t-58.5 164.5q0 132 97 228.5t228 96.5q68 0 125 -30.5t87 -82.5l-46 -23q-25 40 -70.5 63.5t-96.5 23.5q-106 0 -186 -80t-80 -193
q0 -91 60 -147t152 -56q101 0 169 71l39 -31q-92 -89 -209 -89q-13 0 -19 1l-27 -46q15 12 34 12q26 0 42 -15t16 -42q0 -38 -29 -62.5t-76 -24.5z" />
    <glyph glyph-name="Egrave.smcp" unicode="&#xf7e8;" horiz-adv-x="505" 
d="M395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313zM400 606h-44l-140 144h62z" />
    <glyph glyph-name="Eacute.smcp" unicode="&#xf7e9;" horiz-adv-x="505" 
d="M537 750l-204 -144h-46l186 144h64zM395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313z" />
    <glyph glyph-name="Ecircumflex.smcp" unicode="&#xf7ea;" horiz-adv-x="505" 
d="M470 606h-38l-62 113l-109 -113h-41l129 144h54zM395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313z" />
    <glyph glyph-name="Edieresis.smcp" unicode="&#xf7eb;" horiz-adv-x="505" 
d="M495 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM291 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM395 0h-368l121 550h368
l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313z" />
    <glyph glyph-name="Igrave.smcp" unicode="&#xf7ec;" horiz-adv-x="216" 
d="M82 0h-55l121 550h55zM242 606h-44l-140 144h62z" />
    <glyph glyph-name="Iacute.smcp" unicode="&#xf7ed;" horiz-adv-x="216" 
d="M381 750l-204 -144h-46l186 144h64zM82 0h-55l121 550h55z" />
    <glyph glyph-name="Icircumflex.smcp" unicode="&#xf7ee;" horiz-adv-x="216" 
d="M315 606h-38l-62 113l-109 -113h-41l129 144h54zM82 0h-55l121 550h55z" />
    <glyph glyph-name="Idieresis.smcp" unicode="&#xf7ef;" horiz-adv-x="216" 
d="M338 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM134 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM82 0h-55l121 550h55z" />
    <glyph glyph-name="Eth.smcp" unicode="&#xf7f0;" horiz-adv-x="611" 
d="M224 0h-197l57 260h-68l10 41h67l55 249h166q113 0 184.5 -69.5t71.5 -168.5q0 -57 -22 -111.5t-63 -100t-109 -73t-152 -27.5zM234 49q128 0 203 78.5t75 182.5q0 84 -56.5 137.5t-149.5 53.5h-114l-45 -200h142l-9 -41h-142l-46 -211h142z" />
    <glyph glyph-name="Ntilde.smcp" unicode="&#xf7f1;" horiz-adv-x="625" 
d="M490 0h-54l-250 474l-104 -474h-55l121 550h55l250 -467l103 467h55zM464 604q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="Ograve.smcp" unicode="&#xf7f2;" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM468 606h-44l-140 144h62
z" />
    <glyph glyph-name="Oacute.smcp" unicode="&#xf7f3;" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM606 750l-204 -144h-46
l186 144h64z" />
    <glyph glyph-name="Ocircumflex.smcp" unicode="&#xf7f4;" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM539 606h-38l-62 113
l-109 -113h-41l129 144h54z" />
    <glyph glyph-name="Otilde.smcp" unicode="&#xf7f5;" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM484 604q-25 0 -41.5 16
t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="Odieresis.smcp" unicode="&#xf7f6;" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM563 655q0 -17 -12.5 -29
t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM359 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5z" />
    <glyph glyph-name="Oslash.smcp" unicode="&#xf7f8;" horiz-adv-x="666" 
d="M312 -12q-99 0 -168 51l-37 -39h-54l61 66q-61 67 -61 172q0 130 90 227t222 97q98 0 166 -50l36 38h54l-60 -65q63 -68 63 -173q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 80 -44 135l-342 -366q51 -42 132 -42zM110 240q0 -80 42 -133l342 365
q-54 41 -130 41q-110 0 -182 -81.5t-72 -191.5z" />
    <glyph glyph-name="Ugrave.smcp" unicode="&#xf7f9;" horiz-adv-x="613" 
d="M287 -12q-104 0 -162 46.5t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-24 -111 -79.5 -166.5t-157.5 -55.5zM441 606h-44l-140 144h62z" />
    <glyph glyph-name="Uacute.smcp" unicode="&#xf7fa;" horiz-adv-x="613" 
d="M581 750l-204 -144h-46l186 144h64zM287 -12q-104 0 -162 46.5t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-24 -111 -79.5 -166.5t-157.5 -55.5z" />
    <glyph glyph-name="Ucircumflex.smcp" unicode="&#xf7fb;" horiz-adv-x="613" 
d="M512 606h-38l-62 113l-109 -113h-41l129 144h54zM287 -12q-104 0 -162 46.5t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-24 -111 -79.5 -166.5t-157.5 -55.5z" />
    <glyph glyph-name="Udieresis.smcp" unicode="&#xf7fc;" horiz-adv-x="613" 
d="M537 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM333 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM287 -12q-104 0 -162 46.5
t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-24 -111 -79.5 -166.5t-157.5 -55.5z" />
    <glyph glyph-name="Yacute.smcp" unicode="&#xf7fd;" horiz-adv-x="539" 
d="M545 750l-204 -144h-46l186 144h64zM245 0h-55l52 235l-163 315h64l135 -266l249 266h67l-298 -315z" />
    <glyph glyph-name="Thorn.smcp" unicode="&#xf7fe;" horiz-adv-x="509" 
d="M82 0h-55l121 550h55l-21 -97h139q74 0 120.5 -38t46.5 -101q0 -76 -54 -131.5t-145 -55.5h-179zM279 176h13q63 0 100.5 39t37.5 96q0 45 -32 69t-85 24h-143l-50 -228h159z" />
    <glyph glyph-name="Ydieresis.smcp" unicode="&#xf7ff;" horiz-adv-x="539" 
d="M501 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM297 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM245 0h-55l52 235l-163 315h64
l135 -266l249 266h67l-298 -315z" />
    <glyph glyph-name="uniF8FF" unicode="&#xf8ff;" horiz-adv-x="667" 
d="M209 153l18 78h-86q-36 0 -61.5 -26t-25.5 -62q0 -29 19 -48.5t47 -19.5q36 0 59 21t30 57zM271 435l16 69q2 14 2 21q0 30 -19 48t-50 18q-36 0 -61.5 -24.5t-25.5 -60.5q0 -32 20 -51.5t53 -19.5h65zM269 265h137l31 137h-137zM572 161q0 32 -20 51t-52 19h-66l-15 -68
q-2 -8 -2 -21q0 -31 19 -49t50 -18q35 0 60.5 25t25.5 61zM652 524q0 29 -19 48t-48 19q-71 0 -89 -77l-17 -79h86q36 0 61.5 26.5t25.5 62.5zM607 160q0 -48 -39 -83t-89 -35q-42 0 -70 28t-28 70q0 14 3 29l15 62h-137l-20 -86q-11 -46 -45 -74.5t-80 -28.5q-41 0 -70 28
t-29 69q0 49 38 87.5t87 38.5h91l31 137h-66q-45 0 -72.5 29t-27.5 76q0 48 38.5 83t88.5 35q42 0 70 -28t28 -70q0 -14 -3 -29l-14 -63h137l19 87q11 46 45 74.5t80 28.5q42 0 71 -28t29 -69q0 -50 -38 -88t-87 -38h-91l-31 -137h63q46 0 74.5 -29t28.5 -76z" />
    <glyph glyph-name="zero.pzero" horiz-adv-x="608" 
d="M275 -12q-100 0 -155 68l-47 -56h-53l76 92q-31 61 -31 152q0 101 35.5 198.5t108 166t164.5 68.5q99 0 154 -67l47 57h53l-78 -93q32 -62 32 -152q0 -101 -35 -198.5t-107 -166.5t-164 -69zM280 40q54 0 101 36.5t77 94t47 124.5t17 131q0 53 -14 98l-351 -422
q40 -62 123 -62zM125 240q0 -57 13 -97l351 421q-40 61 -122 61q-54 0 -101 -36.5t-77 -93.5t-47 -124t-17 -131z" />
    <glyph glyph-name="zero.tonumzero" horiz-adv-x="588" 
d="M554 328q0 -42 -11 -87.5t-34.5 -91t-55.5 -81t-78.5 -58t-100.5 -22.5q-105 0 -164 56l-41 -44h-54l70 74q-39 58 -39 148q0 53 18 110.5t51.5 109.5t89 86t121.5 34q104 0 163 -56l41 44h54l-70 -74q40 -58 40 -148zM494 325q0 64 -21 106l-324 -345q43 -46 125 -46
q43 0 80.5 19.5t62.5 50t43 69t26 76t8 70.5zM106 225q0 -63 21 -106l323 345q-43 46 -124 46q-53 0 -97 -29t-69.5 -73.5t-39.5 -92.5t-14 -90z" />
    <glyph glyph-name="zero.onumzero" horiz-adv-x="608" 
d="M565 328q0 -42 -11 -87.5t-34.5 -91t-55.5 -81t-78.5 -58t-100.5 -22.5q-105 0 -164 56l-41 -44h-54l70 74q-39 58 -39 148q0 53 18 110.5t51.5 109.5t89 86t121.5 34q104 0 163 -56l41 44h54l-70 -74q40 -58 40 -148zM505 325q0 64 -21 106l-324 -345q43 -46 125 -46
q43 0 80.5 19.5t62.5 50t43 69t26 76t8 70.5zM117 225q0 -63 21 -106l323 345q-43 46 -124 46q-53 0 -97 -29t-69.5 -73.5t-39.5 -92.5t-14 -90z" />
    <glyph glyph-name="G.alt1" horiz-adv-x="711" 
d="M355 -12q-127 0 -207.5 79.5t-80.5 209.5q0 176 113 288.5t268 112.5q82 0 147.5 -36.5t101.5 -100.5l-51 -26q-30 54 -85.5 82.5t-121.5 28.5q-120 0 -215 -97.5t-95 -249.5q0 -109 64.5 -174t170.5 -65q110 0 182 66.5t104 173.5h-303l11 51h363q-33 -162 -126 -252.5
t-240 -90.5z" />
    <glyph glyph-name="a.alt1" horiz-adv-x="521" 
d="M390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52
t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27z" />
    <glyph glyph-name="f.alt1" horiz-adv-x="265" 
d="M96 0h-52l97 437h-80l10 46h80l9 44q33 150 150 150q44 0 76 -21l-22 -38q-19 14 -53 14q-40 0 -63 -26.5t-35 -78.5l-10 -44h98l-10 -46h-98z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="256" 
d="M140 -12q-44 0 -70 19.5t-26 54.5q0 15 3 32l127 573h52l-125 -565q-3 -15 -3 -25q0 -20 13.5 -31t36.5 -11q27 0 51 18l10 -42q-31 -23 -69 -23z" />
    <glyph glyph-name="y.alt1" horiz-adv-x="540" 
d="M181 -196q-75 0 -124.5 19.5t-83.5 62.5l37 37q46 -74 171 -74q138 0 172 156l16 71q-92 -83 -173 -83q-61 0 -100.5 28.5t-39.5 87.5q0 21 5 43l73 331h52l-72 -323q-4 -19 -4 -37q0 -83 106 -83q43 0 87 24.5t77 59.5l80 359h52l-105 -476q-46 -203 -226 -203z" />
    <glyph glyph-name="G.smcp.alt1" horiz-adv-x="647" 
d="M325 -12q-120 0 -196 69t-76 176q0 139 96.5 234t226.5 95q143 0 226 -112l-47 -29q-27 43 -75.5 67.5t-102.5 24.5q-105 0 -185.5 -78.5t-80.5 -195.5q0 -90 61.5 -146.5t151.5 -56.5q81 0 145 53.5t83 139.5h-234l10 47h287q-11 -120 -93.5 -204t-196.5 -84z" />
    <glyph glyph-name="Gbreve.alt1" horiz-adv-x="745" 
d="M674 796q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75zM355 -12q-127 0 -207.5 79.5t-80.5 209.5q0 176 113 288.5t268 112.5q82 0 147.5 -36.5t101.5 -100.5l-51 -26q-30 54 -85.5 82.5t-121.5 28.5q-120 0 -215 -97.5t-95 -249.5
q0 -109 64.5 -174t170.5 -65q110 0 182 66.5t104 173.5h-303l11 51h363q-33 -162 -126 -252.5t-240 -90.5z" />
    <glyph glyph-name="Gcommaaccent.alt1" horiz-adv-x="745" 
d="M342 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM355 -12q-127 0 -207.5 79.5t-80.5 209.5q0 176 113 288.5t268 112.5q82 0 147.5 -36.5t101.5 -100.5l-51 -26
q-30 54 -85.5 82.5t-121.5 28.5q-120 0 -215 -97.5t-95 -249.5q0 -109 64.5 -174t170.5 -65q110 0 182 66.5t104 173.5h-303l11 51h363q-33 -162 -126 -252.5t-240 -90.5z" />
    <glyph glyph-name="Gcircumflex.alt1" horiz-adv-x="745" 
d="M613 723h-38l-62 113l-109 -113h-41l129 144h54zM355 -12q-127 0 -207.5 79.5t-80.5 209.5q0 176 113 288.5t268 112.5q82 0 147.5 -36.5t101.5 -100.5l-51 -26q-30 54 -85.5 82.5t-121.5 28.5q-120 0 -215 -97.5t-95 -249.5q0 -109 64.5 -174t170.5 -65q110 0 182 66.5
t104 173.5h-303l11 51h363q-33 -162 -126 -252.5t-240 -90.5z" />
    <glyph glyph-name="Gdotaccent.alt1" horiz-adv-x="745" 
d="M536 770q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24zM355 -12q-127 0 -207.5 79.5t-80.5 209.5q0 176 113 288.5t268 112.5q82 0 147.5 -36.5t101.5 -100.5l-51 -26q-30 54 -85.5 82.5t-121.5 28.5
q-120 0 -215 -97.5t-95 -249.5q0 -109 64.5 -174t170.5 -65q110 0 182 66.5t104 173.5h-303l11 51h363q-33 -162 -126 -252.5t-240 -90.5z" />
    <glyph glyph-name="aacute.alt1" horiz-adv-x="521" 
d="M517 700l-204 -144h-46l186 144h64zM390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29
q83 0 145 64l23 105q-20 34 -61 52t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27z" />
    <glyph glyph-name="abreve.alt1" horiz-adv-x="521" 
d="M390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52
t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM509 629q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="acircumflex.alt1" horiz-adv-x="521" 
d="M449 556h-38l-62 113l-109 -113h-41l129 144h54zM390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5
q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27z" />
    <glyph glyph-name="adieresis.alt1" horiz-adv-x="521" 
d="M473 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM269 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM390 0h-53l12 54
q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52t-92 18
q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27z" />
    <glyph glyph-name="agrave.alt1" horiz-adv-x="521" 
d="M390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52
t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM379 556h-44l-140 144h62z" />
    <glyph glyph-name="amacron.alt1" horiz-adv-x="521" 
d="M510 577h-362l8 37h362zM386 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM209 29q83 0 145 64
l23 105q-20 34 -61 52t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27z" />
    <glyph glyph-name="aogonek.alt1" horiz-adv-x="521" 
d="M390 0h-1q-105 -46 -105 -110q0 -43 47 -43q36 0 59 43l26 -18q-36 -58 -90 -58q-34 0 -55.5 18.5t-21.5 51.5q0 75 89 123l11 47q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5
q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27z" />
    <glyph glyph-name="aring.alt1" horiz-adv-x="521" 
d="M335 548q-38 0 -62.5 24.5t-24.5 62.5q0 42 31.5 73.5t73.5 31.5q38 0 62.5 -24.5t24.5 -62.5q0 -43 -31 -74t-74 -31zM336 580q29 0 50.5 20.5t21.5 49.5q0 26 -15.5 42t-40.5 16q-29 0 -50 -21t-21 -49q0 -26 15 -42t40 -16zM390 0h-53l12 54q-69 -66 -153 -66
q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52t-92 18q-61 0 -100 -38.5t-39 -96.5
q0 -50 34.5 -77t89.5 -27z" />
    <glyph glyph-name="atilde.alt1" horiz-adv-x="521" 
d="M390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52
t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM394 554q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EA1.alt1" horiz-adv-x="521" 
d="M390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52
t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM207 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EA3.alt1" horiz-adv-x="521" 
d="M390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52
t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM288 591l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EA5.alt1" horiz-adv-x="521" 
d="M448 556h-38l-62 113l-109 -113h-41l129 144h54zM672 770l-204 -144h-46l186 144h64zM390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69
q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27z" />
    <glyph glyph-name="uni1EAD.alt1" horiz-adv-x="521" 
d="M448 556h-38l-62 113l-109 -113h-41l129 144h54zM390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5
q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM207 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EA7.alt1" horiz-adv-x="521" 
d="M448 556h-38l-62 113l-109 -113h-41l129 144h54zM390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5
q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM252 626h-44l-140 144h62z" />
    <glyph glyph-name="uni1EA9.alt1" horiz-adv-x="521" 
d="M447 556h-38l-62 113l-109 -113h-41l129 144h54zM390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5
q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM372 731l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EAF.alt1" horiz-adv-x="521" 
d="M505 761l-204 -144h-46l186 144h64zM390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29
q83 0 145 64l23 105q-20 34 -61 52t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM504 607q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="uni1EAB.alt1" horiz-adv-x="521" 
d="M441 523h-38l-62 113l-109 -113h-41l129 144h54zM390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5
q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM422 682q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37
q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EB1.alt1" horiz-adv-x="521" 
d="M390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52
t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM404 617h-44l-140 144h62zM505 612q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="uni1EB3.alt1" horiz-adv-x="521" 
d="M390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52
t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM308 680l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 32 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32zM505 612q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z
" />
    <glyph glyph-name="uni1EB5.alt1" horiz-adv-x="521" 
d="M505 612q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75zM390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69
q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM417 660q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35
t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EB7.alt1" horiz-adv-x="521" 
d="M390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52
t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27zM208 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24zM509 629q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="901" 
d="M846 225h-385q-2 -6 -2 -27q0 -72 40.5 -118.5t124.5 -46.5q74 0 134 52l21 -38q-72 -59 -155 -59q-77 0 -129 34t-71 99q-27 -61 -83 -97t-124 -36q-77 0 -130.5 36t-53.5 104q0 84 59 132.5t134 48.5q57 0 106.5 -20.5t77.5 -55.5l13 61q7 30 7 60q0 48 -39.5 72
t-99.5 24q-89 0 -159 -62l-22 38q78 69 188 69q75 0 121.5 -29.5t49.5 -93.5q28 55 80 89t115 34q88 0 138.5 -56.5t50.5 -152.5q0 -33 -7 -61zM469 266h334q1 7 1 22q0 75 -38 118.5t-110 43.5q-70 0 -119 -55.5t-68 -128.5zM396 169l6 29q-17 32 -64.5 51t-97.5 19
q-62 0 -106.5 -38.5t-44.5 -94.5q0 -48 38.5 -75t94.5 -27q60 0 110.5 39.5t63.5 96.5z" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="901" 
d="M846 225h-385q-2 -6 -2 -27q0 -72 40.5 -118.5t124.5 -46.5q74 0 134 52l21 -38q-72 -59 -155 -59q-77 0 -129 34t-71 99q-27 -61 -83 -97t-124 -36q-77 0 -130.5 36t-53.5 104q0 84 59 132.5t134 48.5q57 0 106.5 -20.5t77.5 -55.5l13 61q7 30 7 60q0 48 -39.5 72
t-99.5 24q-89 0 -159 -62l-22 38q78 69 188 69q75 0 121.5 -29.5t49.5 -93.5q28 55 80 89t115 34q88 0 138.5 -56.5t50.5 -152.5q0 -33 -7 -61zM469 266h334q1 7 1 22q0 75 -38 118.5t-110 43.5q-70 0 -119 -55.5t-68 -128.5zM396 169l6 29q-17 32 -64.5 51t-97.5 19
q-62 0 -106.5 -38.5t-44.5 -94.5q0 -48 38.5 -75t94.5 -27q60 0 110.5 39.5t63.5 96.5zM682 700l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="fl.alt1" horiz-adv-x="521" 
d="M96 0h-52l97 437h-80l10 46h80l9 44q33 150 150 150q44 0 76 -21l-22 -38q-19 14 -53 14q-40 0 -63 -26.5t-35 -78.5l-10 -44h98l-10 -46h-98zM405 -12q-44 0 -70 19.5t-26 54.5q0 15 3 32l127 573h52l-125 -565q-3 -15 -3 -25q0 -20 13.5 -31t36.5 -11q27 0 51 18
l10 -42q-31 -23 -69 -23z" />
    <glyph glyph-name="f_f_l.alt1" horiz-adv-x="785" 
d="M361 0h-52l97 437h-80l10 46h80l9 44q33 150 150 150q45 0 76 -21l-22 -38q-19 14 -53 14q-40 0 -63 -26.5t-35 -78.5l-10 -44h98l-10 -46h-98zM670 -12q-44 0 -70 19.5t-26 54.5q0 15 3 32l127 573h52l-125 -565q-3 -15 -3 -25q0 -20 13.5 -31t36.5 -11q27 0 51 18
l10 -42q-31 -23 -69 -23zM97 0h-53l97 437h-80l10 46h80l9 44q35 150 151 150q58 0 95 -37l-30 -33q-23 25 -65 25q-40 0 -63 -26t-35 -79l-10 -44h99l-11 -46h-98z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="256" 
d="M405 871l-204 -144h-46l186 144h64zM140 -12q-44 0 -70 19.5t-26 54.5q0 15 3 32l127 573h52l-125 -565q-3 -15 -3 -25q0 -20 13.5 -31t36.5 -11q27 0 51 18l10 -42q-31 -23 -69 -23z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="256" 
d="M70 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM140 -12q-44 0 -70 19.5t-26 54.5q0 15 3 32l127 573h52l-125 -565q-3 -15 -3 -25q0 -20 13.5 -31t36.5 -11q27 0 51 18
l10 -42q-31 -23 -69 -23z" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="269" 
d="M367 635q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM140 -12q-44 0 -70 19.5t-26 54.5q0 15 3 32l127 573h52l-125 -565q-3 -15 -3 -25q0 -20 13.5 -31t36.5 -11q27 0 51 18
l10 -42q-31 -23 -69 -23z" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="273" 
d="M140 -12q-44 0 -70 19.5t-26 54.5q0 15 3 32l127 573h52l-125 -565q-3 -15 -3 -25q0 -20 13.5 -31t36.5 -11q27 0 51 18l10 -42q-31 -23 -69 -23zM240 201q-17 0 -29 11.5t-12 28.5q0 20 14.5 33.5t34.5 13.5q17 0 28.5 -11.5t11.5 -28.5q0 -19 -14 -33t-34 -14z" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="287" 
d="M9 238l11 46l114 58l72 325h52l-66 -295l115 58l-10 -46l-115 -58l-49 -224q-3 -15 -3 -25q0 -20 13.5 -31t36.5 -11q27 0 51 18l10 -42q-31 -23 -69 -23q-44 0 -70 19.5t-26 54.5q0 15 3 32l45 202z" />
    <glyph glyph-name="yacute.alt1" horiz-adv-x="540" 
d="M532 700l-204 -144h-46l186 144h64zM181 -196q-75 0 -124.5 19.5t-83.5 62.5l37 37q46 -74 171 -74q138 0 172 156l16 71q-92 -83 -173 -83q-61 0 -100.5 28.5t-39.5 87.5q0 21 5 43l73 331h52l-72 -323q-4 -19 -4 -37q0 -83 106 -83q43 0 87 24.5t77 59.5l80 359h52
l-105 -476q-46 -203 -226 -203z" />
    <glyph glyph-name="ycircumflex.alt1" horiz-adv-x="540" 
d="M463 556h-38l-62 113l-109 -113h-41l129 144h54zM181 -196q-75 0 -124.5 19.5t-83.5 62.5l37 37q46 -74 171 -74q138 0 172 156l16 71q-92 -83 -173 -83q-61 0 -100.5 28.5t-39.5 87.5q0 21 5 43l73 331h52l-72 -323q-4 -19 -4 -37q0 -83 106 -83q43 0 87 24.5t77 59.5
l80 359h52l-105 -476q-46 -203 -226 -203z" />
    <glyph glyph-name="ydieresis.alt1" horiz-adv-x="540" 
d="M487 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM283 605q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM181 -196q-75 0 -124.5 19.5
t-83.5 62.5l37 37q46 -74 171 -74q138 0 172 156l16 71q-92 -83 -173 -83q-61 0 -100.5 28.5t-39.5 87.5q0 21 5 43l73 331h52l-72 -323q-4 -19 -4 -37q0 -83 106 -83q43 0 87 24.5t77 59.5l80 359h52l-105 -476q-46 -203 -226 -203z" />
    <glyph glyph-name="ygrave.alt1" horiz-adv-x="540" 
d="M181 -196q-75 0 -124.5 19.5t-83.5 62.5l37 37q46 -74 171 -74q138 0 172 156l16 71q-92 -83 -173 -83q-61 0 -100.5 28.5t-39.5 87.5q0 21 5 43l73 331h52l-72 -323q-4 -19 -4 -37q0 -83 106 -83q43 0 87 24.5t77 59.5l80 359h52l-105 -476q-46 -203 -226 -203zM400 591
h-44l-140 144h62z" />
    <glyph glyph-name="uni1EA0.smcp" horiz-adv-x="562" 
d="M316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM233 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EA2.smcp" horiz-adv-x="562" 
d="M316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM325 644l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 32 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EA4.smcp" horiz-adv-x="562" 
d="M484 606h-38l-62 113l-109 -113h-41l129 144h54zM709 820l-204 -144h-46l186 144h64zM316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263z" />
    <glyph glyph-name="uni1EAC.smcp" horiz-adv-x="562" 
d="M484 606h-38l-62 113l-109 -113h-41l129 144h54zM316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM232 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EA6.smcp" horiz-adv-x="562" 
d="M484 606h-38l-62 113l-109 -113h-41l129 144h54zM316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM288 676h-44l-140 144h62z" />
    <glyph glyph-name="uni1EA8.smcp" horiz-adv-x="562" 
d="M485 606h-38l-62 113l-109 -113h-41l129 144h54zM316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM409 781l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EAE.smcp" horiz-adv-x="562" 
d="M543 811l-204 -144h-46l186 144h64zM316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM541 666q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="uni1EAA.smcp" horiz-adv-x="562" 
d="M479 573h-38l-62 113l-109 -113h-41l129 144h54zM316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM458 732q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35
t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EB0.smcp" horiz-adv-x="562" 
d="M316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM434 667h-44l-140 144h62zM542 666q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="uni1EB2.smcp" horiz-adv-x="562" 
d="M316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM344 730l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 32 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32zM542 666q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5
l33 25q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="uni1EB4.smcp" horiz-adv-x="562" 
d="M542 666q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q38 -75 130 -75q93 0 163 75zM316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM452 707q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97
t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EB6.smcp" horiz-adv-x="562" 
d="M316 550h64l116 -550h-60l-29 133h-299l-85 -133h-65zM400 182l-63 308l-200 -308h263zM232 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24zM544 679q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25
q38 -75 130 -75q93 0 163 75z" />
    <glyph glyph-name="uni1E02.smcp" horiz-adv-x="541" 
d="M283 0h-256l121 550h213q68 0 113.5 -31.5t45.5 -92.5q0 -53 -33.5 -94.5t-91.5 -52.5q34 -13 56.5 -45t22.5 -69q0 -73 -51 -119t-140 -46zM92 49h193q56 0 93.5 33t37.5 84q0 41 -27.5 66.5t-75.5 25.5h-175zM327 307h17q54 0 85.5 33.5t31.5 78.5q0 41 -31 61.5
t-80 20.5h-157l-45 -194h179zM404 653q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E0A.smcp" horiz-adv-x="611" 
d="M224 0h-197l121 550h166q113 0 184.5 -69.5t71.5 -168.5q0 -57 -22 -111.5t-63 -100t-109 -73t-152 -27.5zM226 49h8q128 0 203 78.5t75 182.5q0 84 -56.5 137.5t-149.5 53.5h-114l-100 -452h134zM440 653q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5
q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EB8.smcp" horiz-adv-x="505" 
d="M395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313zM218 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EBA.smcp" horiz-adv-x="505" 
d="M395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313zM311 644l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 32 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EBC.smcp" horiz-adv-x="505" 
d="M395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313zM415 604q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EBE.smcp" horiz-adv-x="505" 
d="M470 606h-38l-62 113l-109 -113h-41l129 144h54zM694 820l-204 -144h-46l186 144h64zM395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313z" />
    <glyph glyph-name="uni1EC0.smcp" horiz-adv-x="505" 
d="M470 606h-38l-62 113l-109 -113h-41l129 144h54zM395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313zM274 676h-44l-140 144h62z" />
    <glyph glyph-name="uni1EC2.smcp" horiz-adv-x="505" 
d="M470 606h-38l-62 113l-109 -113h-41l129 144h54zM395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313zM394 781l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EC4.smcp" horiz-adv-x="505" 
d="M463 573h-38l-62 113l-109 -113h-41l129 144h54zM395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313zM443 732q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16
q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EC6.smcp" horiz-adv-x="505" 
d="M470 606h-38l-62 113l-109 -113h-41l129 144h54zM395 0h-368l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308l-46 -209h313zM218 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E1E.smcp" horiz-adv-x="484" 
d="M393 653q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24zM82 0h-55l121 550h368l-11 -49h-312l-44 -194h308l-11 -49h-308z" />
    <glyph glyph-name="Gbreve.smcp.alt1" horiz-adv-x="647" 
d="M325 -12q-120 0 -196 69t-76 176q0 139 96.5 234t226.5 95q143 0 226 -112l-47 -29q-27 43 -75.5 67.5t-102.5 24.5q-105 0 -185.5 -78.5t-80.5 -195.5q0 -90 61.5 -146.5t151.5 -56.5q81 0 145 53.5t83 139.5h-234l10 47h287q-11 -120 -93.5 -204t-196.5 -84zM611 679
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l33 25q37 -75 130 -75t163 75z" />
    <glyph glyph-name="Gcommaaccent.smcp.alt1" horiz-adv-x="647" 
d="M305 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM325 -12q-120 0 -196 69t-76 176q0 139 96.5 234t226.5 95q143 0 226 -112l-47 -29q-27 43 -75.5 67.5t-102.5 24.5
q-105 0 -185.5 -78.5t-80.5 -195.5q0 -90 61.5 -146.5t151.5 -56.5q81 0 145 53.5t83 139.5h-234l10 47h287q-11 -120 -93.5 -204t-196.5 -84z" />
    <glyph glyph-name="Gcircumflex.smcp.alt1" horiz-adv-x="647" 
d="M550 606h-38l-62 113l-109 -113h-41l129 144h54zM325 -12q-120 0 -196 69t-76 176q0 139 96.5 234t226.5 95q143 0 226 -112l-47 -29q-27 43 -75.5 67.5t-102.5 24.5q-105 0 -185.5 -78.5t-80.5 -195.5q0 -90 61.5 -146.5t151.5 -56.5q81 0 145 53.5t83 139.5h-234l10 47
h287q-11 -120 -93.5 -204t-196.5 -84z" />
    <glyph glyph-name="Gdotaccent.smcp.alt1" horiz-adv-x="647" 
d="M325 -12q-120 0 -196 69t-76 176q0 139 96.5 234t226.5 95q143 0 226 -112l-47 -29q-27 43 -75.5 67.5t-102.5 24.5q-105 0 -185.5 -78.5t-80.5 -195.5q0 -90 61.5 -146.5t151.5 -56.5q81 0 145 53.5t83 139.5h-234l10 47h287q-11 -120 -93.5 -204t-196.5 -84zM473 653
q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E22.smcp" horiz-adv-x="629" 
d="M494 0h-54l57 259h-358l-57 -259h-55l121 550h55l-54 -243h358l54 243h54zM441 653q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EC8.smcp" horiz-adv-x="216" 
d="M82 0h-55l121 550h55zM154 644l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 32 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1ECA.smcp" horiz-adv-x="216" 
d="M82 0h-55l121 550h55zM60 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1ECC.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM286 -136
q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1ECE.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM379 644l-24 13
q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 32 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1ED0.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM537 606h-38l-62 113
l-109 -113h-41l129 144h54zM761 820l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="uni1ED2.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM537 606h-38l-62 113
l-109 -113h-41l129 144h54zM341 676h-44l-140 144h62z" />
    <glyph glyph-name="uni1ED4.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM538 606h-38l-62 113
l-109 -113h-41l129 144h54zM462 781l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 31 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1ED6.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM530 573h-38l-62 113
l-109 -113h-41l129 144h54zM512 732q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1ED8.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q120 0 189.5 -69.5t69.5 -180.5q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM538 606h-38l-62 113
l-109 -113h-41l129 144h54zM286 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="Ohorn.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q146 0 216 -102q50 31 63 74h-6q-13 0 -21.5 9.5t-8.5 22.5q0 18 12 30t29 12q16 0 26.5 -11t10.5 -31q0 -37 -26.5 -74t-62.5 -58q27 -54 27 -122q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5
t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57z" />
    <glyph glyph-name="uni1EDA.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q146 0 216 -102q50 31 63 74h-6q-13 0 -21.5 9.5t-8.5 22.5q0 18 12 30t29 12q16 0 26.5 -11t10.5 -31q0 -37 -26.5 -74t-62.5 -58q27 -54 27 -122q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5
t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM606 750l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="uni1EDC.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q146 0 216 -102q50 31 63 74h-6q-13 0 -21.5 9.5t-8.5 22.5q0 18 12 30t29 12q16 0 26.5 -11t10.5 -31q0 -37 -26.5 -74t-62.5 -58q27 -54 27 -122q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5
t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM468 606h-44l-140 144h62z" />
    <glyph glyph-name="uni1EDE.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q146 0 216 -102q50 31 63 74h-6q-13 0 -21.5 9.5t-8.5 22.5q0 18 12 30t29 12q16 0 26.5 -11t10.5 -31q0 -37 -26.5 -74t-62.5 -58q27 -54 27 -122q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5
t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM379 644l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 32 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EE0.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q146 0 216 -102q50 31 63 74h-6q-13 0 -21.5 9.5t-8.5 22.5q0 18 12 30t29 12q16 0 26.5 -11t10.5 -31q0 -37 -26.5 -74t-62.5 -58q27 -54 27 -122q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5
t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM483 604q-25 0 -41.5 16t-23 35.5t-19.5 35.5t-33 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97
t-71.5 -37z" />
    <glyph glyph-name="uni1EE2.smcp" horiz-adv-x="666" 
d="M312 -12q-119 0 -189 69.5t-70 180.5q0 130 90 227t222 97q146 0 216 -102q50 31 63 74h-6q-13 0 -21.5 9.5t-8.5 22.5q0 18 12 30t29 12q16 0 26.5 -11t10.5 -31q0 -37 -26.5 -74t-62.5 -58q27 -54 27 -122q0 -130 -90 -227t-222 -97zM313 37q111 0 182.5 81.5
t71.5 191.5q0 89 -54.5 146t-148.5 57q-110 0 -182 -81.5t-72 -191.5q0 -89 54.5 -146t148.5 -57zM286 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1E56.smcp" horiz-adv-x="509" 
d="M82 0h-55l121 550h195q76 0 121.5 -38.5t45.5 -100.5q0 -77 -54.5 -132t-144.5 -55h-180zM142 273h172q63 0 100.5 39t37.5 97q0 43 -31 67.5t-86 24.5h-142zM403 653q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z
" />
    <glyph glyph-name="uni1E60.smcp" horiz-adv-x="499" 
d="M236 -12q-155 0 -226 97l41 37q27 -39 77 -62t110 -23q74 0 108 33.5t34 80.5q0 35 -30 59.5t-72.5 40t-85.5 33t-73 49t-30 75.5q0 64 59 109t138 45q65 0 117.5 -24.5t81.5 -64.5l-38 -36q-23 35 -68 55.5t-94 20.5q-56 0 -96.5 -30t-40.5 -74q0 -31 30 -53.5
t72.5 -37.5t85.5 -33t73 -51t30 -81q0 -67 -51 -116t-152 -49zM383 653q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni021A.smcp" horiz-adv-x="465" 
d="M192 -122q0 -38 -28.5 -77.5t-67.5 -59.5l-22 24q27 13 47.5 35.5t26.5 45.5h-7q-12 0 -21.5 10t-9.5 23q0 18 13 30.5t30 12.5t28 -11.5t11 -32.5zM206 0h-55l110 501h-176l11 49h408l-11 -49h-177z" />
    <glyph glyph-name="uni1E6A.smcp" horiz-adv-x="465" 
d="M206 0h-55l110 501h-176l11 49h408l-11 -49h-177zM360 653q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="uni1EE4.smcp" horiz-adv-x="613" 
d="M287 -12q-104 0 -162 46.5t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-24 -111 -79.5 -166.5t-157.5 -55.5zM260 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5
q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24z" />
    <glyph glyph-name="Uhorn.smcp" horiz-adv-x="613" 
d="M287 -12q-104 0 -162.5 46.5t-58.5 129.5q0 17 6 45l76 341h55l-75 -337q-4 -28 -4 -41q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-13 -58q77 41 89 90h-6q-12 0 -21 9.5t-9 22.5q0 18 12 30t29 12q38 0 38 -42q0 -48 -39 -90.5t-101 -67.5l-54 -246
q-24 -111 -79.5 -166.5t-157.5 -55.5z" />
    <glyph glyph-name="uni1EE6.smcp" horiz-adv-x="613" 
d="M287 -12q-104 0 -162 46.5t-58 129.5q0 22 5 45l76 341h55l-75 -338q-4 -28 -4 -40q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-75 -340q-24 -111 -79.5 -166.5t-157.5 -55.5zM353 644l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36
q36 32 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32z" />
    <glyph glyph-name="uni1EE8.smcp" horiz-adv-x="613" 
d="M581 750l-204 -144h-46l186 144h64zM287 -12q-104 0 -162.5 46.5t-58.5 129.5q0 17 6 45l76 341h55l-75 -337q-4 -28 -4 -41q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-13 -58q77 41 89 90h-6q-12 0 -21 9.5t-9 22.5q0 18 12 30t29 12q38 0 38 -42
q0 -48 -39 -90.5t-101 -67.5l-54 -246q-24 -111 -79.5 -166.5t-157.5 -55.5z" />
    <glyph glyph-name="uni1EEA.smcp" horiz-adv-x="613" 
d="M441 606h-44l-140 144h62zM287 -12q-104 0 -162.5 46.5t-58.5 129.5q0 17 6 45l76 341h55l-75 -337q-4 -28 -4 -41q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-13 -58q77 41 89 90h-6q-12 0 -21 9.5t-9 22.5q0 18 12 30t29 12q38 0 38 -42
q0 -48 -39 -90.5t-101 -67.5l-54 -246q-24 -111 -79.5 -166.5t-157.5 -55.5z" />
    <glyph glyph-name="uni1EEC.smcp" horiz-adv-x="613" 
d="M353 644l-24 13q24 46 71 46q26 0 45 -15.5t19 -42.5q0 -37 -31 -65h-36q36 32 36 63q0 16 -10 24.5t-24 8.5q-29 0 -46 -32zM287 -12q-104 0 -162.5 46.5t-58.5 129.5q0 17 6 45l76 341h55l-75 -337q-4 -28 -4 -41q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5
l75 338h55l-13 -58q77 41 89 90h-6q-12 0 -21 9.5t-9 22.5q0 18 12 30t29 12q38 0 38 -42q0 -48 -39 -90.5t-101 -67.5l-54 -246q-24 -111 -79.5 -166.5t-157.5 -55.5z" />
    <glyph glyph-name="uni1EEE.smcp" horiz-adv-x="613" 
d="M287 -12q-104 0 -162.5 46.5t-58.5 129.5q0 17 6 45l76 341h55l-75 -337q-4 -28 -4 -41q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55l-13 -58q77 41 89 90h-6q-12 0 -21 9.5t-9 22.5q0 18 12 30t29 12q38 0 38 -42q0 -48 -39 -90.5t-101 -67.5l-54 -246
q-24 -111 -79.5 -166.5t-157.5 -55.5zM458 604q-25 0 -41.5 16t-23 35.5t-20 35.5t-32.5 16q-46 0 -69 -97h-36q12 60 40.5 97t71.5 37q25 0 41.5 -16t23.5 -35t20 -35t32 -16q46 0 69 96h37q-13 -60 -41.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EF0.smcp" horiz-adv-x="613" 
d="M260 -136q0 -18 -12.5 -30t-29.5 -12q-15 0 -24.5 9.5t-9.5 23.5q0 17 12.5 30t29.5 13q15 0 24.5 -10t9.5 -24zM287 -12q-104 0 -162.5 46.5t-58.5 129.5q0 17 6 45l76 341h55l-75 -337q-4 -28 -4 -41q0 -65 43.5 -100t118.5 -35q76 0 120 43.5t63 131.5l75 338h55
l-13 -58q77 41 89 90h-6q-12 0 -21 9.5t-9 22.5q0 18 12 30t29 12q38 0 38 -42q0 -48 -39 -90.5t-101 -67.5l-54 -246q-24 -111 -79.5 -166.5t-157.5 -55.5z" />
    <glyph glyph-name="Wacute.smcp" horiz-adv-x="749" 
d="M515 0h-58l-35 463l-238 -463h-58l-44 550h59l32 -471l243 471h49l34 -472l241 472h61zM647 750l-204 -144h-46l186 144h64z" />
    <glyph glyph-name="Wdieresis.smcp" horiz-adv-x="749" 
d="M515 0h-58l-35 463l-238 -463h-58l-44 550h59l32 -471l243 471h49l34 -472l241 472h61zM603 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5zM399 655q0 -17 -12.5 -29t-29.5 -12q-14 0 -23.5 9.5
t-9.5 23.5q0 16 12.5 28.5t29.5 12.5q14 0 23.5 -9.5t9.5 -23.5z" />
    <glyph glyph-name="Wgrave.smcp" horiz-adv-x="749" 
d="M515 0h-58l-35 463l-238 -463h-58l-44 550h59l32 -471l243 471h49l34 -472l241 472h61zM508 606h-44l-140 144h62z" />
    <glyph glyph-name="Ygrave.smcp" horiz-adv-x="539" 
d="M245 0h-55l52 235l-163 315h64l135 -266l249 266h67l-298 -315zM406 606h-44l-140 144h62z" />
    <glyph glyph-name="afii10065.alt1" horiz-adv-x="521" 
d="M390 0h-53l12 54q-69 -66 -153 -66q-68 0 -115.5 36t-47.5 104q0 83 52.5 132t128.5 49q112 0 174 -76l21 92q4 18 4 33q0 41 -36 66.5t-93 25.5q-88 0 -152 -62l-22 38q81 69 180 69q76 0 126 -33.5t50 -97.5q0 -21 -6 -43zM213 29q83 0 145 64l23 105q-20 34 -61 52
t-92 18q-61 0 -100 -38.5t-39 -96.5q0 -50 34.5 -77t89.5 -27z" />
    <hkern u1="K" u2="a" k="19" />
    <hkern u1="L" u2="a" k="17" />
    <hkern u1="P" u2="a" k="20" />
    <hkern u1="R" u2="a" k="20" />
    <hkern u1="T" u2="a" k="103" />
    <hkern u1="V" u2="a" k="62" />
    <hkern u1="W" u2="a" k="40" />
    <hkern u1="Y" u2="a" k="126" />
    <hkern u1="a" u2="&#x2122;" k="3" />
    <hkern u1="a" u2="&#x201d;" k="3" />
    <hkern u1="a" u2="&#x201c;" k="3" />
    <hkern u1="a" u2="&#x2019;" k="3" />
    <hkern u1="a" u2="&#x2018;" k="3" />
    <hkern u1="a" u2="&#x1ef2;" k="110" />
    <hkern u1="a" u2="&#x1e84;" k="42" />
    <hkern u1="a" u2="&#x1e82;" k="42" />
    <hkern u1="a" u2="&#x1e80;" k="42" />
    <hkern u1="a" u2="&#x1e6a;" k="114" />
    <hkern u1="a" u2="&#x42a;" k="114" />
    <hkern u1="a" u2="&#x427;" k="114" />
    <hkern u1="a" u2="&#x422;" k="114" />
    <hkern u1="a" u2="&#x40b;" k="114" />
    <hkern u1="a" u2="&#x402;" k="114" />
    <hkern u1="a" u2="&#x3ab;" k="110" />
    <hkern u1="a" u2="&#x3a5;" k="110" />
    <hkern u1="a" u2="&#x3a4;" k="114" />
    <hkern u1="a" u2="&#x38e;" k="110" />
    <hkern u1="a" u2="&#x2bc;" k="3" />
    <hkern u1="a" u2="&#x21a;" k="114" />
    <hkern u1="a" u2="&#x178;" k="110" />
    <hkern u1="a" u2="&#x176;" k="110" />
    <hkern u1="a" u2="&#x174;" k="42" />
    <hkern u1="a" u2="&#x166;" k="114" />
    <hkern u1="a" u2="&#x164;" k="114" />
    <hkern u1="a" u2="&#x162;" k="114" />
    <hkern u1="a" u2="&#xdd;" k="110" />
    <hkern u1="a" u2="&#xae;" k="3" />
    <hkern u1="a" u2="Y" k="110" />
    <hkern u1="a" u2="W" k="42" />
    <hkern u1="a" u2="V" k="63" />
    <hkern u1="a" u2="T" k="114" />
    <hkern u1="a" u2="&#x3f;" k="33" />
    <hkern u1="a" u2="&#x27;" k="3" />
    <hkern u1="a" u2="&#x22;" k="3" />
    <hkern u1="&#xdd;" u2="a" k="126" />
    <hkern u1="&#x136;" u2="a" k="19" />
    <hkern u1="&#x139;" u2="a" k="17" />
    <hkern u1="&#x13b;" u2="a" k="17" />
    <hkern u1="&#x141;" u2="a" k="17" />
    <hkern u1="&#x154;" u2="a" k="20" />
    <hkern u1="&#x156;" u2="a" k="20" />
    <hkern u1="&#x158;" u2="a" k="20" />
    <hkern u1="&#x162;" u2="a" k="103" />
    <hkern u1="&#x164;" u2="a" k="103" />
    <hkern u1="&#x166;" u2="a" k="103" />
    <hkern u1="&#x174;" u2="a" k="40" />
    <hkern u1="&#x176;" u2="a" k="126" />
    <hkern u1="&#x178;" u2="a" k="126" />
    <hkern u1="&#x21a;" u2="a" k="103" />
    <hkern u1="&#x38e;" u2="a" k="126" />
    <hkern u1="&#x393;" u2="a" k="103" />
    <hkern u1="&#x39a;" u2="a" k="19" />
    <hkern u1="&#x3a1;" u2="a" k="20" />
    <hkern u1="&#x3a4;" u2="a" k="103" />
    <hkern u1="&#x3a5;" u2="a" k="126" />
    <hkern u1="&#x3ab;" u2="a" k="126" />
    <hkern u1="&#x403;" u2="a" k="103" />
    <hkern u1="&#x40c;" u2="a" k="19" />
    <hkern u1="&#x40e;" u2="a" k="62" />
    <hkern u1="&#x413;" u2="a" k="103" />
    <hkern u1="&#x416;" u2="a" k="19" />
    <hkern u1="&#x41a;" u2="a" k="19" />
    <hkern u1="&#x420;" u2="a" k="20" />
    <hkern u1="&#x422;" u2="a" k="103" />
    <hkern u1="&#x423;" u2="a" k="62" />
    <hkern u1="&#x490;" u2="a" k="103" />
    <hkern u1="&#x1e56;" u2="a" k="20" />
    <hkern u1="&#x1e6a;" u2="a" k="103" />
    <hkern u1="&#x1e80;" u2="a" k="40" />
    <hkern u1="&#x1e82;" u2="a" k="40" />
    <hkern u1="&#x1e84;" u2="a" k="40" />
    <hkern u1="&#x1ef2;" u2="a" k="126" />
    <hkern u1="&#x2215;" u2="&#xf659;" k="50" />
    <hkern u1="&#x2215;" u2="&#xf656;" k="-31" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="dagger"
	k="99" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V.smcp"
	k="39" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="19" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="daggerdbl"
	k="39" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="question"
	k="79" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="99" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="J,Jcircumflex,afii10057"
	k="-5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="asterisk"
	k="106" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="19" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="33" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="79" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="58" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V"
	k="67" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="59" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="11" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="19" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="v,gamma,nu"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="bullet.case"
	k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="49" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="dagger"
	k="79" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V.smcp"
	k="39" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="29" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="daggerdbl"
	k="20" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question"
	k="59" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="80" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="51" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="25" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="asterisk"
	k="67" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="5" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="110" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="55" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V"
	k="67" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="83" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="19" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="59" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question.smcp"
	k="42" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="bullet"
	k="19" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-3" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="19" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="V"
	k="12" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Jcircumflex.smcp,J.smcp"
	k="19" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="question"
	k="42" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="68" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="V"
	k="20" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="22" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="question"
	k="2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="-2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="V"
	k="11" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="23" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="bullet.case"
	k="2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="2" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V.smcp"
	k="3" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="question"
	k="22" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="3" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="2" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="3" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="86" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V"
	k="16" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="48" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="5" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-9" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="X.smcp"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="question"
	k="13" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="J,Jcircumflex,afii10057"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="39" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="31" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="V"
	k="32" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="53" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Jcircumflex.smcp,J.smcp"
	k="13" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="27" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="19" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="29" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V.smcp"
	k="29" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="question"
	k="43" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="37" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="39" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="38" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="86" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V"
	k="50" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="73" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="39" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="12" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="9" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="19" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X.smcp"
	k="22" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="26" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="57" />
    <hkern g1="Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,OE.smcp,E.smcp,AE.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="17" />
    <hkern g1="F,uni1E1E"
	g2="J,Jcircumflex,afii10057"
	k="55" />
    <hkern g1="F,uni1E1E"
	g2="Jcircumflex.smcp,J.smcp"
	k="53" />
    <hkern g1="F,uni1E1E"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="35" />
    <hkern g1="F,uni1E1E"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="33" />
    <hkern g1="F,uni1E1E"
	g2="X.smcp"
	k="17" />
    <hkern g1="F,uni1E1E"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="F,uni1E1E"
	g2="ampersand"
	k="3" />
    <hkern g1="F,uni1E1E"
	g2="ampersand.smcp"
	k="22" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="J,Jcircumflex,afii10057"
	k="33" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="17" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="43" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="22" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="22" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="12" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="22" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="ampersand.smcp"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="question"
	k="3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="7" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="V"
	k="22" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="25" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="2" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="question"
	k="32" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="2" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="2" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="67" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="13" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="question"
	k="2" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="70" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="2" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="V"
	k="20" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="32" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="V.smcp"
	k="42" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="59" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="52" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="57" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="26" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="23" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="35" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="bullet.case"
	k="77" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="70" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="X.smcp"
	k="13" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="ampersand.smcp"
	k="2" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="37" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="x,afii10072,afii10087"
	k="23" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="42" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="19" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="43" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="19" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="V"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="43" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="bullet"
	k="49" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="39" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="dagger"
	k="130" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V.smcp"
	k="52" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="46" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="daggerdbl"
	k="106" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="question"
	k="117" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="116" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="79" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="49" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="37" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="asterisk"
	k="177" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="29" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="63" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="122" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="27" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="83" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="17" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="32" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="39" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="v,gamma,nu"
	k="59" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="59" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="bullet.case"
	k="124" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="79" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="ampersand"
	k="9" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="26" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="dagger"
	k="100" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V.smcp"
	k="94" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="54" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="daggerdbl"
	k="51" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="question"
	k="86" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="110" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="89" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="60" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="64" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="asterisk"
	k="119" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="109" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="52" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V"
	k="107" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="130" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="26" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="100" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="question.smcp"
	k="59" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="bullet"
	k="51" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="51" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="J,Jcircumflex,afii10057"
	k="105" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="2" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="V"
	k="2" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="20" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Jcircumflex.smcp,J.smcp"
	k="93" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="79" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="97" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="5" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand"
	k="23" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand.smcp"
	k="33" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="hyphen,periodcentered,endash,emdash"
	k="37" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="12" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="J,Jcircumflex,afii10057"
	k="51" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="60" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="42" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="79" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="49" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="64" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="39" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="19" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="2" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="V"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="16" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="2" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="bullet.case"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand"
	k="1" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand.smcp"
	k="12" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="3" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="V.smcp"
	k="9" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="question"
	k="39" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="9" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="63" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="59" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="V.smcp"
	k="2" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="2" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="13" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="V"
	k="11" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="27" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="13" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="19" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="2" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="v,gamma,nu"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="9" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="x,afii10072,afii10087"
	k="12" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="V.smcp"
	k="10" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="question"
	k="30" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="12" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="1" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="91" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="1" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="V"
	k="22" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="33" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="20" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="-10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="V.smcp"
	k="43" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="83" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="34" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="43" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="18" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="J,Jcircumflex,afii10057"
	k="82" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-9" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="68" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="36" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="43" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="93" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="68" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="v,gamma,nu"
	k="68" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="68" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="bullet.case"
	k="91" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Jcircumflex.smcp,J.smcp"
	k="109" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="61" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="90" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="59" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="X.smcp"
	k="43" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="92" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand.smcp"
	k="96" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="123" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="x,afii10072,afii10087"
	k="78" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="96" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="70" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="51" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="93" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="19" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="J,Jcircumflex,afii10057"
	k="76" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="34" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="V"
	k="2" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="bullet"
	k="65" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="72" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-1" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="32" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="37" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand"
	k="39" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand.smcp"
	k="32" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="51" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="J,Jcircumflex,afii10057"
	k="6" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="Jcircumflex.smcp,J.smcp"
	k="6" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="13" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="question"
	k="5" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="68" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="V"
	k="20" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="22" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-9" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="1" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="17" />
    <hkern g1="V,afii10062,afii10037"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="39" />
    <hkern g1="V,afii10062,afii10037"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="2" />
    <hkern g1="V,afii10062,afii10037"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="9" />
    <hkern g1="V,afii10062,afii10037"
	g2="J,Jcircumflex,afii10057"
	k="92" />
    <hkern g1="V,afii10062,afii10037"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-11" />
    <hkern g1="V,afii10062,afii10037"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="22" />
    <hkern g1="V,afii10062,afii10037"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="36" />
    <hkern g1="V,afii10062,afii10037"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="12" />
    <hkern g1="V,afii10062,afii10037"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="3" />
    <hkern g1="V,afii10062,afii10037"
	g2="v,gamma,nu"
	k="12" />
    <hkern g1="V,afii10062,afii10037"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="3" />
    <hkern g1="V,afii10062,afii10037"
	g2="bullet.case"
	k="39" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="30" />
    <hkern g1="V,afii10062,afii10037"
	g2="Jcircumflex.smcp,J.smcp"
	k="90" />
    <hkern g1="V,afii10062,afii10037"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="13" />
    <hkern g1="V,afii10062,afii10037"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="47" />
    <hkern g1="V,afii10062,afii10037"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="47" />
    <hkern g1="V,afii10062,afii10037"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand"
	k="6" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand.smcp"
	k="49" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen,periodcentered,endash,emdash"
	k="52" />
    <hkern g1="V,afii10062,afii10037"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="60" />
    <hkern g1="V,afii10062,afii10037"
	g2="x,afii10072,afii10087"
	k="22" />
    <hkern g1="V,afii10062,afii10037"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="35" />
    <hkern g1="V,afii10062,afii10037"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="2" />
    <hkern g1="V,afii10062,afii10037"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="36" />
    <hkern g1="V.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="9" />
    <hkern g1="V.smcp"
	g2="J,Jcircumflex,afii10057"
	k="57" />
    <hkern g1="V.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="43" />
    <hkern g1="V.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="2" />
    <hkern g1="V.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="55" />
    <hkern g1="V.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-10" />
    <hkern g1="V.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="21" />
    <hkern g1="V.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="19" />
    <hkern g1="V.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="62" />
    <hkern g1="V.smcp"
	g2="ampersand"
	k="13" />
    <hkern g1="V.smcp"
	g2="ampersand.smcp"
	k="-7" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="9" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex,afii10057"
	k="29" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v,gamma,nu"
	k="2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="14" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="1" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Jcircumflex.smcp,J.smcp"
	k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="39" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="69" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand.smcp"
	k="3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="32" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x,afii10072,afii10087"
	k="22" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="30" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="18" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="J,Jcircumflex,afii10057"
	k="22" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="43" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="2" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="35" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-1" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="15" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="52" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="ampersand"
	k="2" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="ampersand.smcp"
	k="-6" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="V.smcp"
	k="1" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="2" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="27" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="J,Jcircumflex,afii10057"
	k="112" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="6" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="23" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="bullet.case"
	k="79" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="99" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Jcircumflex.smcp,J.smcp"
	k="96" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="46" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="52" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="ampersand"
	k="41" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="ampersand.smcp"
	k="11" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="129" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="x,afii10072,afii10087"
	k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="colon,semicolon"
	k="55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="32" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="90" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="19" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="J,Jcircumflex,afii10057"
	k="49" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="43" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="3" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="99" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-17" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="22" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="42" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="70" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand"
	k="35" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand.smcp"
	k="35" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="43" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="19" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="bullet.case"
	k="64" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="67" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="51" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="V"
	k="2" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="6" />
    <hkern g1="ampersand"
	g2="V.smcp"
	k="66" />
    <hkern g1="ampersand"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="26" />
    <hkern g1="ampersand"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="93" />
    <hkern g1="ampersand"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="20" />
    <hkern g1="ampersand"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="96" />
    <hkern g1="ampersand"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="64" />
    <hkern g1="ampersand"
	g2="V"
	k="69" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="115" />
    <hkern g1="ampersand"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="96" />
    <hkern g1="ampersand"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="20" />
    <hkern g1="ampersand.smcp"
	g2="V.smcp"
	k="56" />
    <hkern g1="ampersand.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="6" />
    <hkern g1="ampersand.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="86" />
    <hkern g1="ampersand.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="30" />
    <hkern g1="ampersand.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="110" />
    <hkern g1="ampersand.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="6" />
    <hkern g1="ampersand.smcp"
	g2="V"
	k="80" />
    <hkern g1="ampersand.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="14" />
    <hkern g1="ampersand.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="87" />
    <hkern g1="asterisk"
	g2="J,Jcircumflex,afii10057"
	k="113" />
    <hkern g1="asterisk"
	g2="Jcircumflex.smcp,J.smcp"
	k="102" />
    <hkern g1="asterisk"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="67" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="106" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="question"
	k="43" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="123" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="V"
	k="60" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="127" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="9" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="x,afii10072,afii10087"
	k="39" />
    <hkern g1="bullet"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="71" />
    <hkern g1="bullet"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="1" />
    <hkern g1="bullet.case"
	g2="J,Jcircumflex,afii10057"
	k="80" />
    <hkern g1="bullet.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="91" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="26" />
    <hkern g1="bullet.case"
	g2="V"
	k="59" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="99" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="29" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="64" />
    <hkern g1="bullet.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="60" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="question"
	k="22" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="2" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="86" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="22" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="V"
	k="33" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="62" />
    <hkern g1="colon,semicolon"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="55" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="question"
	k="33" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="2" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="asterisk"
	k="19" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="106" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="V"
	k="52" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="110" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="9" />
    <hkern g1="eth"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="9" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="question"
	k="-59" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="-69" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="asterisk"
	k="-69" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-22" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-62" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="V"
	k="-62" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="-53" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="42" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand"
	k="2" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand.smcp"
	k="2" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="exclam,B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Imacron,Iogonek,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Racute,Rcommaaccent,Rcaron"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="parenright,bracketright,braceright,parenright.case,bracketright.case,braceright.case"
	k="-77" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="question"
	k="22" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="93" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="V"
	k="36" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="j,jcircumflex,afii10105"
	k="-43" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="J,Jcircumflex,afii10057"
	k="69" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="1" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="30" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="99" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="49" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="67" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="79" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="51" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="3" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="32" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="52" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="105" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="43" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="42" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="80" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="22" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="V"
	k="22" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="63" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="bullet"
	k="29" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="29" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="1" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V.smcp"
	k="62" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="26" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="73" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="60" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="52" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="30" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="92" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="69" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V"
	k="89" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="17" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="22" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="22" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="47" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="v,gamma,nu"
	k="69" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="59" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="9" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="j,jcircumflex,afii10105"
	k="-40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="seven"
	k="63" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one"
	k="69" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="zero,six"
	k="36" />
    <hkern g1="questiondown"
	g2="j,jcircumflex,afii10105"
	k="-160" />
    <hkern g1="questiondown.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="107" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="62" />
    <hkern g1="questiondown.case"
	g2="V"
	k="72" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="92" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="2" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="J,Jcircumflex,afii10057"
	k="86" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="107" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="73" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="ampersand"
	k="-9" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="54" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="V"
	k="22" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="50" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="82" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="5" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="question"
	k="42" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="94" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="35" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="V"
	k="42" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="92" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="2" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="61" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="12" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="V"
	k="23" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="32" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="question"
	k="22" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="93" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="V"
	k="36" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="v,gamma,nu,chi"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="68" />
    <hkern g1="v,gamma,nu,chi"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="2" />
    <hkern g1="v,gamma,nu,chi"
	g2="V"
	k="12" />
    <hkern g1="v,gamma,nu,chi"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="v,gamma,nu,chi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="v,gamma,nu,chi"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="69" />
    <hkern g1="v,gamma,nu,chi"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="39" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="68" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="3" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="19" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="47" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="47" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="question"
	k="2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="68" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="V"
	k="3" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="59" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="39" />
    <hkern g1="X,Chi,afii10039"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="57" />
    <hkern g1="X,Chi,afii10039"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="47" />
    <hkern g1="X,Chi,afii10039"
	g2="v,gamma,nu"
	k="39" />
    <hkern g1="X,Chi,afii10039"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="39" />
    <hkern g1="X,Chi,afii10039"
	g2="bullet.case"
	k="60" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="79" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen,periodcentered,endash,emdash"
	k="42" />
    <hkern g1="X.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="22" />
    <hkern g1="X.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="43" />
    <hkern g1="X.smcp"
	g2="ampersand"
	k="20" />
    <hkern g1="zero,nine"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="36" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="99" />
    <hkern g1="x,afii10087"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="78" />
    <hkern g1="x,afii10087"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="22" />
    <hkern g1="x,afii10087"
	g2="V"
	k="22" />
    <hkern g1="x,afii10087"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="72" />
    <hkern g1="x,afii10087"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="39" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.case,bracketleft.case,braceleft.case"
	g2="j,jcircumflex,afii10105"
	k="-110" />
    <hkern g1="one.numr"
	g2="fraction"
	k="-43" />
    <hkern g1="seven.numr"
	g2="fraction"
	k="50" />
    <hkern g1="fraction"
	g2="one.dnom"
	k="-31" />
    <hkern g1="fraction"
	g2="four.dnom"
	k="50" />
  </font>
</defs></svg>
