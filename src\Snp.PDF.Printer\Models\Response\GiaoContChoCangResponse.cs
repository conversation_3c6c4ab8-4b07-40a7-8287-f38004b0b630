﻿using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Internal;
using SNP.PDF.Printer.Models.Base;
using SNP.PDF.Printer.Models.ViewModels;
using SNP.PDF.Printer.Resources;

namespace SNP.PDF.Printer.Models.Response
{
    public class GiaoContChoCangResponse : BaseResponse<string>
    {
        public GiaoContChoCangResponse(string url) : base(url)
        {
        }

        public GiaoContChoCangResponse(string url, GiaoContChoCangVm model) : this(url)
        {
            if(string.IsNullOrEmpty(model.LogoUrl))
            {
                Error missingLogoType = new Error("400100", "Logo site {0} không tồn tại", model.LogoType.ToUpper());
                Update(missingLogoType);
            }
        }

        public GiaoContChoCangResponse(string url, List<GiaoContChoCangVm> model) : this(url)
        {
            if(model != null
               && model.Any())
            {
                List<Error> errors = new List<Error>();
                foreach (var data in model)
                {
                    if(string.IsNullOrEmpty(data.LogoUrl))
                    {
                        errors.Add(new Error("400100", "Logo site {0} không tồn tại", data.LogoType.ToUpper()));
                    }
                }

                if(errors.Any())
                {
                    Update(errors);
                }
            }
        }
    }
}
