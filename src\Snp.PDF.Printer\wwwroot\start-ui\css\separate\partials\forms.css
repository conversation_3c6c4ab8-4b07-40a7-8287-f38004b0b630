@charset "UTF-8";
/* ==========================================================================
   HTML5 display definitions
   ========================================================================== */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
  display: block;
}
audio,
canvas,
video {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
[hidden] {
  display: none;
}
/* ==========================================================================
   Base
   ========================================================================== */
/**
 * 1. Correct text resizing oddly in IE 6/7 when body `font-size` is set using
 *    `em` units.
 * 2. Prevent iOS text size adjust after orientation change, without disabling
 *    user zoom.
 */
html {
  font-size: 100%;
  /* 1 */
  -ms-text-size-adjust: 100%;
  /* 2 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}
html,
button,
input,
select,
textarea {
  font-family: sans-serif;
}
body {
  margin: 0;
}
*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
/* ==========================================================================
   Links
   ========================================================================== */
*:focus {
  outline: none;
}
*:active,
*:hover {
  outline: 0;
}
/* ==========================================================================
   Typography
   ========================================================================== */
abbr[title] {
  border-bottom: 1px dotted;
}
b,
strong {
  font-weight: bold;
}
blockquote {
  margin: 1em 40px;
}
dfn {
  font-style: italic;
}
hr {
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  height: 0;
}
mark {
  background: #ff0;
  color: #000;
}
pre {
  margin: 0px;
}
/**
 * Correct font family set oddly in IE 6, Safari 4/5, and Chrome.
 */
code,
kbd,
pre,
samp {
  font-family: monospace, serif;
  _font-family: 'courier new', monospace;
  font-size: 1em;
}
/**
 * Improve readability of pre-formatted text in all browsers.
 */
pre {
  white-space: pre;
  white-space: pre-wrap;
  word-wrap: break-word;
}
/**
 * Address CSS quotes not supported in IE 6/7.
 */
q {
  quotes: none;
}
/**
 * Address `quotes` property not supported in Safari 4.
 */
q:before,
q:after {
  content: '';
  content: none;
}
/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%;
}
/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
/* ==========================================================================
   Lists
   ========================================================================== */
/**
 * Address margins set differently in IE 6/7.
 */
dl,
menu,
ol,
ul {
  margin: 0;
}
dd {
  margin: 0 0 0 40px;
}
/**
 * Correct list images handled incorrectly in IE 7.
 */
nav ul,
nav ol {
  list-style: none;
  list-style-image: none;
}
/* ==========================================================================
   Embedded content
   ========================================================================== */
/**
 * 1. Remove border when inside `a` element in IE 6/7/8/9 and Firefox 3.
 * 2. Improve image quality when scaled in IE 7.
 */
img {
  border: 0;
  /* 1 */
  -ms-interpolation-mode: bicubic;
  /* 2 */
}
/**
 * Correct overflow displayed oddly in IE 9.
 */
svg:not(:root) {
  overflow: hidden;
}
/* ==========================================================================
   Figures
   ========================================================================== */
/**
 * Address margin not present in IE 6/7/8/9, Safari 5, and Opera 11.
 */
figure {
  margin: 0;
}
/* ==========================================================================
   Forms
   ========================================================================== */
/**
 * Correct margin displayed oddly in IE 6/7.
 */
form {
  margin: 0;
}
/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
/**
 * 1. Correct color not being inherited in IE 6/7/8/9.
 * 2. Correct text not wrapping in Firefox 3.
 * 3. Correct alignment displayed oddly in IE 6/7.
 */
legend {
  border: 0;
  /* 1 */
  padding: 0;
  white-space: normal;
  /* 2 */
  *margin-left: -7px;
  /* 3 */
}
/**
 * 1. Correct font size not being inherited in all browsers.
 * 2. Address margins set differently in IE 6/7, Firefox 3+, Safari 5,
 *    and Chrome.
 * 3. Improve appearance and consistency in all browsers.
 */
button,
input,
select,
textarea {
  font-size: 100%;
  /* 1 */
  margin: 0;
  /* 2 */
  vertical-align: baseline;
  /* 3 */
  *vertical-align: middle;
  /* 3 */
}
/**
 * Address Firefox 3+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
button,
input {
  line-height: normal;
}
/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Chrome, Safari 5+, and IE 6+.
 * Correct `select` style inheritance in Firefox 4+ and Opera.
 */
button,
select {
  text-transform: none;
}
/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 * 4. Remove inner spacing in IE 7 without affecting normal text inputs.
 *    Known issue: inner spacing remains in IE 6.
 */
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
  cursor: pointer;
  /* 3 */
  *overflow: visible;
  /* 4 */
}
/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default;
}
/**
 * 1. Address box sizing set to content-box in IE 8/9.
 * 2. Remove excess padding in IE 8/9.
 * 3. Remove excess padding in IE 7.
 *    Known issue: excess padding remains in IE 6.
 */
input[type="checkbox"],
input[type="radio"] {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
  *height: 13px;
  /* 3 */
  *width: 13px;
  /* 3 */
}
/**
 * 1. Address `appearance` set to `searchfield` in Safari 5 and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari 5 and Chrome
 *    (include `-moz` to future-proof).
 */
input[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  /* 2 */
  box-sizing: content-box;
}
/**
 * Remove inner padding and search cancel button in Safari 5 and Chrome
 * on OS X.
 */
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
/**
 * Remove inner padding and border in Firefox 3+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
/**
 * 1. Remove default vertical scrollbar in IE 6/7/8/9.
 * 2. Improve readability and alignment in all browsers.
 */
textarea {
  overflow: auto;
  /* 1 */
  vertical-align: top;
  /* 2 */
}
/* ==========================================================================
   Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}
/* ==========================================================================
   Opinionated defaults
   ========================================================================== */
html {
  font-size: 1em;
  line-height: 1.4;
}
* {
  padding: 0px;
  margin: 0px;
}
img {
  border: none;
}
ul {
  list-style: none;
}
hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
  margin: 1em 0;
  padding: 0;
}
textarea {
  resize: vertical;
}
fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}
audio,
canvas,
img,
video {
  vertical-align: middle;
}
.browsehappy {
  margin: 0.2em 0;
  background: #ccc;
  color: #000;
  padding: 0.2em 0;
}
::-moz-selection {
  background: #b3d4fc;
  text-shadow: none;
}
::selection {
  background: #b3d4fc;
  text-shadow: none;
}
/* ==========================================================================
   Mixins
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
@font-face {
  font-family: 'Glyphicons Halflings';
  src: url('../fonts/glyphicons-halflings-regular.eot');
  src: url('../fonts/glyphicons-halflings-regular.eot?#iefix') format('embedded-opentype'), url('../fonts/glyphicons-halflings-regular.woff2') format('woff2'), url('../fonts/glyphicons-halflings-regular.woff') format('woff'), url('../fonts/glyphicons-halflings-regular.ttf') format('truetype'), url('../fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular') format('svg');
}
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.glyphicon-asterisk:before {
  content: "\002a";
}
.glyphicon-plus:before {
  content: "\002b";
}
.glyphicon-euro:before,
.glyphicon-eur:before {
  content: "\20ac";
}
.glyphicon-minus:before {
  content: "\2212";
}
.glyphicon-cloud:before {
  content: "\2601";
}
.glyphicon-envelope:before {
  content: "\2709";
}
.glyphicon-pencil:before {
  content: "\270f";
}
.glyphicon-glass:before {
  content: "\e001";
}
.glyphicon-music:before {
  content: "\e002";
}
.glyphicon-search:before {
  content: "\e003";
}
.glyphicon-heart:before {
  content: "\e005";
}
.glyphicon-star:before {
  content: "\e006";
}
.glyphicon-star-empty:before {
  content: "\e007";
}
.glyphicon-user:before {
  content: "\e008";
}
.glyphicon-film:before {
  content: "\e009";
}
.glyphicon-th-large:before {
  content: "\e010";
}
.glyphicon-th:before {
  content: "\e011";
}
.glyphicon-th-list:before {
  content: "\e012";
}
.glyphicon-ok:before {
  content: "\e013";
}
.glyphicon-remove:before {
  content: "\e014";
}
.glyphicon-zoom-in:before {
  content: "\e015";
}
.glyphicon-zoom-out:before {
  content: "\e016";
}
.glyphicon-off:before {
  content: "\e017";
}
.glyphicon-signal:before {
  content: "\e018";
}
.glyphicon-cog:before {
  content: "\e019";
}
.glyphicon-trash:before {
  content: "\e020";
}
.glyphicon-home:before {
  content: "\e021";
}
.glyphicon-file:before {
  content: "\e022";
}
.glyphicon-time:before {
  content: "\e023";
}
.glyphicon-road:before {
  content: "\e024";
}
.glyphicon-download-alt:before {
  content: "\e025";
}
.glyphicon-download:before {
  content: "\e026";
}
.glyphicon-upload:before {
  content: "\e027";
}
.glyphicon-inbox:before {
  content: "\e028";
}
.glyphicon-play-circle:before {
  content: "\e029";
}
.glyphicon-repeat:before {
  content: "\e030";
}
.glyphicon-refresh:before {
  content: "\e031";
}
.glyphicon-list-alt:before {
  content: "\e032";
}
.glyphicon-lock:before {
  content: "\e033";
}
.glyphicon-flag:before {
  content: "\e034";
}
.glyphicon-headphones:before {
  content: "\e035";
}
.glyphicon-volume-off:before {
  content: "\e036";
}
.glyphicon-volume-down:before {
  content: "\e037";
}
.glyphicon-volume-up:before {
  content: "\e038";
}
.glyphicon-qrcode:before {
  content: "\e039";
}
.glyphicon-barcode:before {
  content: "\e040";
}
.glyphicon-tag:before {
  content: "\e041";
}
.glyphicon-tags:before {
  content: "\e042";
}
.glyphicon-book:before {
  content: "\e043";
}
.glyphicon-bookmark:before {
  content: "\e044";
}
.glyphicon-print:before {
  content: "\e045";
}
.glyphicon-camera:before {
  content: "\e046";
}
.glyphicon-font:before {
  content: "\e047";
}
.glyphicon-bold:before {
  content: "\e048";
}
.glyphicon-italic:before {
  content: "\e049";
}
.glyphicon-text-height:before {
  content: "\e050";
}
.glyphicon-text-width:before {
  content: "\e051";
}
.glyphicon-align-left:before {
  content: "\e052";
}
.glyphicon-align-center:before {
  content: "\e053";
}
.glyphicon-align-right:before {
  content: "\e054";
}
.glyphicon-align-justify:before {
  content: "\e055";
}
.glyphicon-list:before {
  content: "\e056";
}
.glyphicon-indent-left:before {
  content: "\e057";
}
.glyphicon-indent-right:before {
  content: "\e058";
}
.glyphicon-facetime-video:before {
  content: "\e059";
}
.glyphicon-picture:before {
  content: "\e060";
}
.glyphicon-map-marker:before {
  content: "\e062";
}
.glyphicon-adjust:before {
  content: "\e063";
}
.glyphicon-tint:before {
  content: "\e064";
}
.glyphicon-edit:before {
  content: "\e065";
}
.glyphicon-share:before {
  content: "\e066";
}
.glyphicon-check:before {
  content: "\e067";
}
.glyphicon-move:before {
  content: "\e068";
}
.glyphicon-step-backward:before {
  content: "\e069";
}
.glyphicon-fast-backward:before {
  content: "\e070";
}
.glyphicon-backward:before {
  content: "\e071";
}
.glyphicon-play:before {
  content: "\e072";
}
.glyphicon-pause:before {
  content: "\e073";
}
.glyphicon-stop:before {
  content: "\e074";
}
.glyphicon-forward:before {
  content: "\e075";
}
.glyphicon-fast-forward:before {
  content: "\e076";
}
.glyphicon-step-forward:before {
  content: "\e077";
}
.glyphicon-eject:before {
  content: "\e078";
}
.glyphicon-chevron-left:before {
  content: "\e079";
}
.glyphicon-chevron-right:before {
  content: "\e080";
}
.glyphicon-plus-sign:before {
  content: "\e081";
}
.glyphicon-minus-sign:before {
  content: "\e082";
}
.glyphicon-remove-sign:before {
  content: "\e083";
}
.glyphicon-ok-sign:before {
  content: "\e084";
}
.glyphicon-question-sign:before {
  content: "\e085";
}
.glyphicon-info-sign:before {
  content: "\e086";
}
.glyphicon-screenshot:before {
  content: "\e087";
}
.glyphicon-remove-circle:before {
  content: "\e088";
}
.glyphicon-ok-circle:before {
  content: "\e089";
}
.glyphicon-ban-circle:before {
  content: "\e090";
}
.glyphicon-arrow-left:before {
  content: "\e091";
}
.glyphicon-arrow-right:before {
  content: "\e092";
}
.glyphicon-arrow-up:before {
  content: "\e093";
}
.glyphicon-arrow-down:before {
  content: "\e094";
}
.glyphicon-share-alt:before {
  content: "\e095";
}
.glyphicon-resize-full:before {
  content: "\e096";
}
.glyphicon-resize-small:before {
  content: "\e097";
}
.glyphicon-exclamation-sign:before {
  content: "\e101";
}
.glyphicon-gift:before {
  content: "\e102";
}
.glyphicon-leaf:before {
  content: "\e103";
}
.glyphicon-fire:before {
  content: "\e104";
}
.glyphicon-eye-open:before {
  content: "\e105";
}
.glyphicon-eye-close:before {
  content: "\e106";
}
.glyphicon-warning-sign:before {
  content: "\e107";
}
.glyphicon-plane:before {
  content: "\e108";
}
.glyphicon-calendar:before {
  content: "\e109";
}
.glyphicon-random:before {
  content: "\e110";
}
.glyphicon-comment:before {
  content: "\e111";
}
.glyphicon-magnet:before {
  content: "\e112";
}
.glyphicon-chevron-up:before {
  content: "\e113";
}
.glyphicon-chevron-down:before {
  content: "\e114";
}
.glyphicon-retweet:before {
  content: "\e115";
}
.glyphicon-shopping-cart:before {
  content: "\e116";
}
.glyphicon-folder-close:before {
  content: "\e117";
}
.glyphicon-folder-open:before {
  content: "\e118";
}
.glyphicon-resize-vertical:before {
  content: "\e119";
}
.glyphicon-resize-horizontal:before {
  content: "\e120";
}
.glyphicon-hdd:before {
  content: "\e121";
}
.glyphicon-bullhorn:before {
  content: "\e122";
}
.glyphicon-bell:before {
  content: "\e123";
}
.glyphicon-certificate:before {
  content: "\e124";
}
.glyphicon-thumbs-up:before {
  content: "\e125";
}
.glyphicon-thumbs-down:before {
  content: "\e126";
}
.glyphicon-hand-right:before {
  content: "\e127";
}
.glyphicon-hand-left:before {
  content: "\e128";
}
.glyphicon-hand-up:before {
  content: "\e129";
}
.glyphicon-hand-down:before {
  content: "\e130";
}
.glyphicon-circle-arrow-right:before {
  content: "\e131";
}
.glyphicon-circle-arrow-left:before {
  content: "\e132";
}
.glyphicon-circle-arrow-up:before {
  content: "\e133";
}
.glyphicon-circle-arrow-down:before {
  content: "\e134";
}
.glyphicon-globe:before {
  content: "\e135";
}
.glyphicon-wrench:before {
  content: "\e136";
}
.glyphicon-tasks:before {
  content: "\e137";
}
.glyphicon-filter:before {
  content: "\e138";
}
.glyphicon-briefcase:before {
  content: "\e139";
}
.glyphicon-fullscreen:before {
  content: "\e140";
}
.glyphicon-dashboard:before {
  content: "\e141";
}
.glyphicon-paperclip:before {
  content: "\e142";
}
.glyphicon-heart-empty:before {
  content: "\e143";
}
.glyphicon-link:before {
  content: "\e144";
}
.glyphicon-phone:before {
  content: "\e145";
}
.glyphicon-pushpin:before {
  content: "\e146";
}
.glyphicon-usd:before {
  content: "\e148";
}
.glyphicon-gbp:before {
  content: "\e149";
}
.glyphicon-sort:before {
  content: "\e150";
}
.glyphicon-sort-by-alphabet:before {
  content: "\e151";
}
.glyphicon-sort-by-alphabet-alt:before {
  content: "\e152";
}
.glyphicon-sort-by-order:before {
  content: "\e153";
}
.glyphicon-sort-by-order-alt:before {
  content: "\e154";
}
.glyphicon-sort-by-attributes:before {
  content: "\e155";
}
.glyphicon-sort-by-attributes-alt:before {
  content: "\e156";
}
.glyphicon-unchecked:before {
  content: "\e157";
}
.glyphicon-expand:before {
  content: "\e158";
}
.glyphicon-collapse-down:before {
  content: "\e159";
}
.glyphicon-collapse-up:before {
  content: "\e160";
}
.glyphicon-log-in:before {
  content: "\e161";
}
.glyphicon-flash:before {
  content: "\e162";
}
.glyphicon-log-out:before {
  content: "\e163";
}
.glyphicon-new-window:before {
  content: "\e164";
}
.glyphicon-record:before {
  content: "\e165";
}
.glyphicon-save:before {
  content: "\e166";
}
.glyphicon-open:before {
  content: "\e167";
}
.glyphicon-saved:before {
  content: "\e168";
}
.glyphicon-import:before {
  content: "\e169";
}
.glyphicon-export:before {
  content: "\e170";
}
.glyphicon-send:before {
  content: "\e171";
}
.glyphicon-floppy-disk:before {
  content: "\e172";
}
.glyphicon-floppy-saved:before {
  content: "\e173";
}
.glyphicon-floppy-remove:before {
  content: "\e174";
}
.glyphicon-floppy-save:before {
  content: "\e175";
}
.glyphicon-floppy-open:before {
  content: "\e176";
}
.glyphicon-credit-card:before {
  content: "\e177";
}
.glyphicon-transfer:before {
  content: "\e178";
}
.glyphicon-cutlery:before {
  content: "\e179";
}
.glyphicon-header:before {
  content: "\e180";
}
.glyphicon-compressed:before {
  content: "\e181";
}
.glyphicon-earphone:before {
  content: "\e182";
}
.glyphicon-phone-alt:before {
  content: "\e183";
}
.glyphicon-tower:before {
  content: "\e184";
}
.glyphicon-stats:before {
  content: "\e185";
}
.glyphicon-sd-video:before {
  content: "\e186";
}
.glyphicon-hd-video:before {
  content: "\e187";
}
.glyphicon-subtitles:before {
  content: "\e188";
}
.glyphicon-sound-stereo:before {
  content: "\e189";
}
.glyphicon-sound-dolby:before {
  content: "\e190";
}
.glyphicon-sound-5-1:before {
  content: "\e191";
}
.glyphicon-sound-6-1:before {
  content: "\e192";
}
.glyphicon-sound-7-1:before {
  content: "\e193";
}
.glyphicon-copyright-mark:before {
  content: "\e194";
}
.glyphicon-registration-mark:before {
  content: "\e195";
}
.glyphicon-cloud-download:before {
  content: "\e197";
}
.glyphicon-cloud-upload:before {
  content: "\e198";
}
.glyphicon-tree-conifer:before {
  content: "\e199";
}
.glyphicon-tree-deciduous:before {
  content: "\e200";
}
.glyphicon-cd:before {
  content: "\e201";
}
.glyphicon-save-file:before {
  content: "\e202";
}
.glyphicon-open-file:before {
  content: "\e203";
}
.glyphicon-level-up:before {
  content: "\e204";
}
.glyphicon-copy:before {
  content: "\e205";
}
.glyphicon-paste:before {
  content: "\e206";
}
.glyphicon-alert:before {
  content: "\e209";
}
.glyphicon-equalizer:before {
  content: "\e210";
}
.glyphicon-king:before {
  content: "\e211";
}
.glyphicon-queen:before {
  content: "\e212";
}
.glyphicon-pawn:before {
  content: "\e213";
}
.glyphicon-bishop:before {
  content: "\e214";
}
.glyphicon-knight:before {
  content: "\e215";
}
.glyphicon-baby-formula:before {
  content: "\e216";
}
.glyphicon-tent:before {
  content: "\26fa";
}
.glyphicon-blackboard:before {
  content: "\e218";
}
.glyphicon-bed:before {
  content: "\e219";
}
.glyphicon-apple:before {
  content: "\f8ff";
}
.glyphicon-erase:before {
  content: "\e221";
}
.glyphicon-hourglass:before {
  content: "\231b";
}
.glyphicon-lamp:before {
  content: "\e223";
}
.glyphicon-duplicate:before {
  content: "\e224";
}
.glyphicon-piggy-bank:before {
  content: "\e225";
}
.glyphicon-scissors:before {
  content: "\e226";
}
.glyphicon-bitcoin:before {
  content: "\e227";
}
.glyphicon-btc:before {
  content: "\e227";
}
.glyphicon-xbt:before {
  content: "\e227";
}
.glyphicon-yen:before {
  content: "\00a5";
}
.glyphicon-jpy:before {
  content: "\00a5";
}
.glyphicon-ruble:before {
  content: "\20bd";
}
.glyphicon-rub:before {
  content: "\20bd";
}
.glyphicon-scale:before {
  content: "\e230";
}
.glyphicon-ice-lolly:before {
  content: "\e231";
}
.glyphicon-ice-lolly-tasted:before {
  content: "\e232";
}
.glyphicon-education:before {
  content: "\e233";
}
.glyphicon-option-horizontal:before {
  content: "\e234";
}
.glyphicon-option-vertical:before {
  content: "\e235";
}
.glyphicon-menu-hamburger:before {
  content: "\e236";
}
.glyphicon-modal-window:before {
  content: "\e237";
}
.glyphicon-oil:before {
  content: "\e238";
}
.glyphicon-grain:before {
  content: "\e239";
}
.glyphicon-sunglasses:before {
  content: "\e240";
}
.glyphicon-text-size:before {
  content: "\e241";
}
.glyphicon-text-color:before {
  content: "\e242";
}
.glyphicon-text-background:before {
  content: "\e243";
}
.glyphicon-object-align-top:before {
  content: "\e244";
}
.glyphicon-object-align-bottom:before {
  content: "\e245";
}
.glyphicon-object-align-horizontal:before {
  content: "\e246";
}
.glyphicon-object-align-left:before {
  content: "\e247";
}
.glyphicon-object-align-vertical:before {
  content: "\e248";
}
.glyphicon-object-align-right:before {
  content: "\e249";
}
.glyphicon-triangle-right:before {
  content: "\e250";
}
.glyphicon-triangle-left:before {
  content: "\e251";
}
.glyphicon-triangle-bottom:before {
  content: "\e252";
}
.glyphicon-triangle-top:before {
  content: "\e253";
}
.glyphicon-console:before {
  content: "\e254";
}
.glyphicon-superscript:before {
  content: "\e255";
}
.glyphicon-subscript:before {
  content: "\e256";
}
.glyphicon-menu-left:before {
  content: "\e257";
}
.glyphicon-menu-right:before {
  content: "\e258";
}
.glyphicon-menu-down:before {
  content: "\e259";
}
.glyphicon-menu-up:before {
  content: "\e260";
}
@font-face {
  font-family: "startui";
  src: url("../fonts/startui.eot");
  src: url("../fonts/startui.eot?#iefix") format("embedded-opentype"), url("../fonts/startui.woff") format("woff"), url("../fonts/startui.ttf") format("truetype"), url("../fonts/startui.svg#startui") format("svg");
  font-weight: normal;
  font-style: normal;
}
[data-icon]:before {
  content: attr(data-icon);
  font-family: "startui" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: inherit;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
  position: relative;
  top: -0.15em;
}
[class^="font-icon-"]:before,
[class*=" font-icon-"]:before {
  font-family: "startui" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: inherit;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
  position: relative;
  top: -0.15em;
}
.font-icon-alarm:before {
  content: "\62";
}
.font-icon-build:before {
  content: "\63";
}
.font-icon-burger:before {
  content: "\64";
}
.font-icon-calend:before {
  content: "\65";
}
.font-icon-cart:before {
  content: "\66";
}
.font-icon-cogwheel:before {
  content: "\67";
}
.font-icon-comments:before {
  content: "\68";
}
.font-icon-contacts:before {
  content: "\69";
}
.font-icon-dashboard:before {
  content: "\6a";
}
.font-icon-dots:before {
  content: "\6b";
}
.font-icon-expand:before {
  content: "\6c";
}
.font-icon-filter:before {
  content: "\6d";
}
.font-icon-help:before {
  content: "\6e";
}
.font-icon-home:before {
  content: "\6f";
}
.font-icon-page:before {
  content: "\70";
}
.font-icon-pencil:before {
  content: "\71";
}
.font-icon-re:before {
  content: "\72";
}
.font-icon-rollup:before {
  content: "\73";
}
.font-icon-search:before {
  content: "\74";
}
.font-icon-share:before {
  content: "\75";
}
.font-icon-star:before {
  content: "\76";
}
.font-icon-users:before {
  content: "\77";
}
.font-icon-wallet:before {
  content: "\78";
}
.font-icon-minus:before {
  content: "\79";
}
.font-icon-pencil-thin:before {
  content: "\7a";
}
.font-icon-close:before {
  content: "\41";
}
.font-icon-speed:before {
  content: "\61";
}
.font-icon-menu-addl:before {
  content: "\42";
}
.font-icon-search:before {
  content: "\74";
}
.font-icon-edit:before {
  content: "\43";
}
.font-icon-chart:before {
  content: "\44";
}
.font-icon-zigzag:before {
  content: "\45";
}
.font-icon-tablet:before {
  content: "\46";
}
.font-icon-widget:before {
  content: "\47";
}
.font-icon-map:before {
  content: "\48";
}
.font-icon-chart-2:before {
  content: "\49";
}
.font-icon-doc:before {
  content: "\4a";
}
.font-icon-question:before {
  content: "\4b";
}
.font-icon-user:before {
  content: "\4c";
}
.font-icon-notebook:before {
  content: "\4d";
}
.font-icon-mail:before {
  content: "\4e";
}
.font-icon-close-2:before {
  content: "\4f";
}
.font-icon-pen:before {
  content: "\50";
}
.font-icon-arrow-down:before {
  content: "\51";
}
.font-icon-arrow-left:before {
  content: "\52";
}
.font-icon-arrow-right:before {
  content: "\53";
}
.font-icon-arrow-top:before {
  content: "\54";
}
.font-icon-check-circle:before {
  content: "\55";
}
.font-icon-cam-photo:before {
  content: "\56";
}
.font-icon-cam-video:before {
  content: "\57";
}
.font-icon-sound:before {
  content: "\58";
}
.font-icon-earth:before {
  content: "\59";
}
.font-icon-mail-2:before {
  content: "\5a";
}
.font-icon-upload:before {
  content: "\30";
}
.font-icon-dropbox:before {
  content: "\31";
}
.font-icon-google-drive:before {
  content: "\32";
}
.font-icon-yandex-disk:before {
  content: "\33";
}
.font-icon-box:before {
  content: "\34";
}
.font-icon-arrow-square-down:before {
  content: "\35";
}
.font-icon-refresh:before {
  content: "\36";
}
.font-icon-list-square:before {
  content: "\37";
}
.font-icon-list-rotate:before {
  content: "\38";
}
.font-icon-download:before {
  content: "\39";
}
.font-icon-heart:before {
  content: "\21";
}
.font-icon-check-bird:before {
  content: "\22";
}
.font-icon-clock:before {
  content: "\23";
}
.font-icon-trash:before {
  content: "\24";
}
.font-icon-circle-lined-error:before {
  content: "\25";
}
.font-icon-circle-lined-i:before {
  content: "\26";
}
.font-icon-circle-lined-smile:before {
  content: "\27";
}
.font-icon-circle-lined-success:before {
  content: "\28";
}
.font-icon-one-drive:before {
  content: "\29";
}
.font-icon-cloud-upload-2:before {
  content: "\2a";
}
.font-icon-plus:before {
  content: "\2b";
}
.font-icon-minus-1:before {
  content: "\2c";
}
.font-icon-arrow-square-up:before {
  content: "\2d";
}
.font-icon-revers:before {
  content: "\2e";
}
.font-icon-import:before {
  content: "\2f";
}
.font-icon-award:before {
  content: "\3a";
}
.font-icon-case:before {
  content: "\3b";
}
.font-icon-earth-bordered:before {
  content: "\3c";
}
.font-icon-comment:before {
  content: "\3d";
}
.font-icon-eye:before {
  content: "\3e";
}
.font-icon-fb-fill:before {
  content: "\3f";
}
.font-icon-in-fill:before {
  content: "\40";
}
.font-icon-lamp:before {
  content: "\5b";
}
.font-icon-picture:before {
  content: "\5d";
}
.font-icon-pdf-fill:before {
  content: "\5e";
}
.font-icon-notebook-bird:before {
  content: "\5f";
}
.font-icon-quote:before {
  content: "\60";
}
.font-icon-vk-fill:before {
  content: "\7b";
}
.font-icon-video-fill:before {
  content: "\7c";
}
.font-icon-tw-fill:before {
  content: "\7d";
}
.font-icon-answer:before {
  content: "\7e";
}
.font-icon-archive:before {
  content: "\5c";
}
.font-icon-case-2:before {
  content: "\e000";
}
.font-icon-clip:before {
  content: "\e001";
}
.font-icon-cloud:before {
  content: "\e002";
}
.font-icon-comments-2:before {
  content: "\e003";
}
.font-icon-del:before {
  content: "\e004";
}
.font-icon-event:before {
  content: "\e005";
}
.font-icon-download-3:before {
  content: "\e006";
}
.font-icon-download-2:before {
  content: "\e007";
}
.font-icon-dots-vert-square:before {
  content: "\e008";
}
.font-icon-fire:before {
  content: "\e009";
}
.font-icon-folder:before {
  content: "\e00a";
}
.font-icon-lock:before {
  content: "\e00b";
}
.font-icon-ok:before {
  content: "\e00c";
}
.font-icon-picture-2:before {
  content: "\e00d";
}
.font-icon-pin:before {
  content: "\e00e";
}
.font-icon-refresh-2:before {
  content: "\e00f";
}
.font-icon-view-cascade:before {
  content: "\e010";
}
.font-icon-users-group:before {
  content: "\e011";
}
.font-icon-upload-2:before {
  content: "\e012";
}
.font-icon-view-grid:before {
  content: "\e013";
}
.font-icon-view-rows:before {
  content: "\e014";
}
.font-icon-warning:before {
  content: "\e015";
}
.font-icon-facebook:before {
  content: "\e016";
}
.font-icon-instagram:before {
  content: "\e017";
}
.font-icon-google-plus:before {
  content: "\e018";
}
.font-icon-linkedin:before {
  content: "\e019";
}
.font-icon-twitter:before {
  content: "\e01a";
}
.font-icon-phone:before {
  content: "\e01b";
}
.font-icon-gp-fill:before {
  content: "\e01c";
}
.font-icon-ok-fill:before {
  content: "\e01d";
}
.font-icon-editor-align-center:before {
  content: "\e01e";
}
.font-icon-editor-align-justify:before {
  content: "\e01f";
}
.font-icon-editor-align-left:before {
  content: "\e020";
}
.font-icon-editor-align-right:before {
  content: "\e021";
}
.font-icon-editor-bold:before {
  content: "\e022";
}
.font-icon-editor-code:before {
  content: "\e023";
}
.font-icon-editor-eraser:before {
  content: "\e024";
}
.font-icon-editor-font:before {
  content: "\e025";
}
.font-icon-editor-fullscreen:before {
  content: "\e026";
}
.font-icon-editor-help:before {
  content: "\e027";
}
.font-icon-editor-img:before {
  content: "\e028";
}
.font-icon-editor-link:before {
  content: "\e029";
}
.font-icon-editor-list:before {
  content: "\e02a";
}
.font-icon-editor-magic:before {
  content: "\e02b";
}
.font-icon-editor-numeric-list:before {
  content: "\e02c";
}
.font-icon-editor-table:before {
  content: "\e02d";
}
.font-icon-editor-underline:before {
  content: "\e02e";
}
.font-icon-editor-video:before {
  content: "\e02f";
}
.font-icon-alarm-2:before {
  content: "\e030";
}
.font-icon-alarm-rotate:before {
  content: "\e031";
}
.font-icon-binoculars:before {
  content: "\e032";
}
.font-icon-cart-2:before {
  content: "\e033";
}
.font-icon-card:before {
  content: "\e034";
}
.font-icon-bookmark:before {
  content: "\e035";
}
.font-icon-chart-3:before {
  content: "\e036";
}
.font-icon-chart-4:before {
  content: "\e037";
}
.font-icon-check-square:before {
  content: "\e038";
}
.font-icon-del-circle:before {
  content: "\e039";
}
.font-icon-comp:before {
  content: "\e03a";
}
.font-icon-cloud-download:before {
  content: "\e03b";
}
.font-icon-downloaded:before {
  content: "\e03c";
}
.font-icon-link:before {
  content: "\e03d";
}
.font-icon-i-circle:before {
  content: "\e03e";
}
.font-icon-notebook-lines:before {
  content: "\e03f";
}
.font-icon-pdf:before {
  content: "\e040";
}
.font-icon-pen-square:before {
  content: "\e041";
}
.font-icon-play-prev:before {
  content: "\e042";
}
.font-icon-play-next:before {
  content: "\e043";
}
.font-icon-play-circle:before {
  content: "\e044";
}
.font-icon-play:before {
  content: "\e045";
}
.font-icon-pin-2:before {
  content: "\e046";
}
.font-icon-server:before {
  content: "\e047";
}
.font-icon-warning-circle:before {
  content: "\e048";
}
.font-icon-users-two:before {
  content: "\e049";
}
.font-icon-weather-cloud:before {
  content: "\e04a";
}
.font-icon-weather-cloud-circles:before {
  content: "\e04b";
}
.font-icon-weather-cloud-drops-lightning:before {
  content: "\e04c";
}
.font-icon-weather-cloud-moon:before {
  content: "\e04d";
}
.font-icon-weather-cloud-one-circle:before {
  content: "\e04e";
}
.font-icon-weather-cloud-one-drop:before {
  content: "\e04f";
}
.font-icon-weather-cloud-rain-snow:before {
  content: "\e050";
}
.font-icon-weather-cloud-sun:before {
  content: "\e051";
}
.font-icon-weather-cloud-two-circles:before {
  content: "\e052";
}
.font-icon-weather-cloud-two-drops:before {
  content: "\e053";
}
.font-icon-weather-cloud-two-snow:before {
  content: "\e054";
}
.font-icon-weather-clouds:before {
  content: "\e055";
}
.font-icon-weather-clound-lightning:before {
  content: "\e056";
}
.font-icon-weather-sun:before {
  content: "\e057";
}
.font-icon-weather-snowflake:before {
  content: "\e058";
}
.font-icon-weather-snow:before {
  content: "\e059";
}
.font-icon-weather-rain:before {
  content: "\e05a";
}
.font-icon-weather-one-snow:before {
  content: "\e05b";
}
.font-icon-weather-moon-small-cloud:before {
  content: "\e05c";
}
.font-icon-weather-moon-cloud-rain:before {
  content: "\e05d";
}
.font-icon-weather-moon-cloud:before {
  content: "\e05e";
}
.font-icon-weather-moon:before {
  content: "\e05f";
}
.font-icon-weather-lightning:before {
  content: "\e060";
}
.font-icon-weather-house-water:before {
  content: "\e061";
}
.font-icon-weather-funnel:before {
  content: "\e062";
}
.font-icon-weather-drop:before {
  content: "\e063";
}
.font-icon-weather-sun-cloud:before {
  content: "\e064";
}
.font-icon-weather-sun-clouds:before {
  content: "\e065";
}
.font-icon-weather-sun-rain:before {
  content: "\e066";
}
.font-icon-weather-thermometer:before {
  content: "\e067";
}
.font-icon-weather-umbrella:before {
  content: "\e068";
}
.font-icon-weather-waves:before {
  content: "\e069";
}
.font-icon-wp:before {
  content: "\e06a";
}
.font-icon-player-full-screen:before {
  content: "\e06b";
}
.font-icon-player-next:before {
  content: "\e06c";
}
.font-icon-player-settings:before {
  content: "\e06d";
}
.font-icon-player-sound:before {
  content: "\e06e";
}
.font-icon-player-subtitres:before {
  content: "\e06f";
}
.font-icon-player-wide-screen:before {
  content: "\e070";
}
.font-icon-case-3:before {
  content: "\e071";
}
.font-icon-github:before {
  content: "\e072";
}
.font-icon-learn:before {
  content: "\e073";
}
.font-icon-play-next-square:before {
  content: "\e074";
}
.font-icon-play-prev-square:before {
  content: "\e075";
}
.font-icon-play-square:before {
  content: "\e076";
}
.font-icon-picture-double:before {
  content: "\e077";
}
.font-icon-snippet:before {
  content: "\e078";
}
.font-icon-post:before {
  content: "\e079";
}
.font-icon-plus-1:before {
  content: "\e07a";
}
/* ==========================================================================
   Forms
   ========================================================================== */
input[placeholder],
[placeholder],
*[placeholder] {
  color: #8e9fa7;
}
::-webkit-input-placeholder {
  color: #8e9fa7 !important;
}
::-moz-placeholder {
  color: #8e9fa7 !important;
  opacity: 1 !important;
}
:-moz-placeholder {
  color: #8e9fa7 !important;
  opacity: 1 !important;
}
::-moz-placeholder {
  color: #8e9fa7 !important;
}
:-ms-input-placeholder {
  color: #8e9fa7 !important;
}
label {
  margin: 0;
  display: block;
}
label.error {
  color: #fa424a;
}
.form-label {
  display: block;
  margin-bottom: 6px;
  font-size: 1rem;
}
.form-label .font-icon {
  margin: 0 4px 0 0;
  color: #adb7be;
}
.form-control {
  border: solid 1px rgba(197, 214, 222, 0.7);
  -webkit-box-shadow: none;
          box-shadow: none;
  font-size: 1rem;
  color: #343434 !important;
}
.form-control:focus {
  border-color: #c5d6de;
}
.form-control.form-control-error {
  background-color: #fff6f6;
  border-color: #fa424a;
  color: #fa424a !important;
}
.form-control:disabled {
  background-color: #eceff4;
}
.form-control.form-control-rounded {
  -webkit-border-radius: 25rem;
          border-radius: 25rem;
}
.form-control.form-control-sm {
  padding-top: .225rem;
  padding-bottom: .225rem;
}
.form-control.form-control-success {
  border-color: #46c35f;
  background-position-y: 49%;
}
.form-control.form-control-danger {
  border-color: #fa424a;
  background-position-y: 49%;
}
.form-control.form-control-warning {
  border-color: #f29824;
  background-position-y: 49%;
}
.form-control.form-control-blue-fill {
  border-color: #00a8ff;
  background-color: #e4f6fe;
}
.form-control.form-control-green-fill {
  border-color: #46c35f;
  background-color: #edf9ee;
}
.form-control.form-control-red-fill {
  border-color: #fa424a;
  background-color: #feecec;
}
.form-control.form-control-purple-fill {
  border-color: #ac6bec;
  background-color: #eee2fc;
}
.form-control.form-control-orange-fill {
  border-color: #f29824;
  background-color: #fdf4e6;
}
textarea.form-control {
  resize: none;
  height: auto;
}
.input-group-addon {
  background-color: #f6f8fa;
  border-color: rgba(197, 214, 222, 0.7);
  font-size: 1rem;
  font-weight: 600;
  color: #6c7a86;
}
.error-list,
.text-muted {
  font-size: 0.875rem /*14*/;
  color: #6c7a86;
  padding: 4px 0 0;
  display: block;
}
.form-group-radios {
  position: relative;
}
.form-group-radios .form-label,
.form-group-radios .radio {
  margin-bottom: 15px;
}
.form-group-error *:not(a),
.error *:not(a) {
  color: #fa424a;
}
.form-group-error .form-control,
.error .form-control {
  border-color: #fa424a;
}
.form-group-error .form-control-wrapper .form-control,
.error .form-control-wrapper .form-control {
  background-color: #fff6f6;
}
.form-group-error.form-group-checkbox .checkbox label,
.error.form-group-checkbox .checkbox label {
  color: #343434;
}
.form-control-wrapper {
  position: relative;
}
.form-control-wrapper.form-control-icon-left .font-icon,
.form-control-wrapper.form-control-icon-right .font-icon,
.form-control-wrapper.form-control-icon-left .glyphicon,
.form-control-wrapper.form-control-icon-right .glyphicon,
.form-control-wrapper.form-control-icon-left .fa,
.form-control-wrapper.form-control-icon-right .fa {
  width: 38px;
  height: 38px;
  line-height: 40px;
  text-align: center;
  position: absolute;
  top: 0;
  color: rgba(145, 159, 169, 0.7);
}
.form-control-wrapper.form-control-icon-left .form-control-lg + .font-icon,
.form-control-wrapper.form-control-icon-right .form-control-lg + .font-icon,
.form-control-wrapper.form-control-icon-left .form-control-lg + .glyphicon,
.form-control-wrapper.form-control-icon-right .form-control-lg + .glyphicon,
.form-control-wrapper.form-control-icon-left .form-control-lg + .fa,
.form-control-wrapper.form-control-icon-right .form-control-lg + .fa {
  height: 48px;
  line-height: 48px;
}
.form-control-wrapper.form-control-icon-left .form-control-sm + .font-icon,
.form-control-wrapper.form-control-icon-right .form-control-sm + .font-icon,
.form-control-wrapper.form-control-icon-left .form-control-sm + .glyphicon,
.form-control-wrapper.form-control-icon-right .form-control-sm + .glyphicon,
.form-control-wrapper.form-control-icon-left .form-control-sm + .fa,
.form-control-wrapper.form-control-icon-right .form-control-sm + .fa {
  height: 34px;
  line-height: 34px;
}
.form-control-wrapper.form-control-icon-left .form-control {
  padding-left: 36px;
}
.form-control-wrapper.form-control-icon-left .font-icon,
.form-control-wrapper.form-control-icon-left .glyphicon,
.form-control-wrapper.form-control-icon-left .fa {
  left: 0;
}
.form-control-wrapper.form-control-icon-right .form-control {
  padding-right: 36px;
}
.form-control-wrapper.form-control-icon-right .font-icon,
.form-control-wrapper.form-control-icon-right .glyphicon,
.form-control-wrapper.form-control-icon-right .fa {
  right: 0;
}
.form-tooltip-error {
  background: #f95858;
  color: #fff !important;
  padding: 5px 8px;
  -webkit-border-radius: 3px;
          border-radius: 3px;
  position: absolute;
  right: 0;
  bottom: 100%;
  margin-bottom: 8px;
  max-width: 230px;
  font-size: 0.875rem /*14*/;
}
.form-tooltip-error:before {
  content: '';
  display: block;
  position: absolute;
  left: 50%;
  bottom: -4px;
  margin-left: -5px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 5px 0 5px;
  border-color: #f95858 transparent transparent transparent;
}
.form-tooltip-error * {
  color: #fff !important;
}
.form-group-checkbox .form-tooltip-error {
  right: 100%;
  bottom: 0;
  margin-bottom: 0;
  margin-right: 10px;
  min-width: 150px;
}
.form-group-checkbox .form-tooltip-error:before {
  -webkit-transform: rotate(-90deg);
      -ms-transform: rotate(-90deg);
       -o-transform: rotate(-90deg);
          transform: rotate(-90deg);
  margin: 0 !important;
  left: auto;
  right: -7px;
  bottom: 9px;
}
.form-label .form-tooltip-error {
  position: relative;
  display: inline-block;
  margin: -4px 0 -4px 8px;
}
.form-label .form-tooltip-error:before {
  -webkit-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
       -o-transform: rotate(90deg);
          transform: rotate(90deg);
  bottom: 50%;
  margin: 0 0 -2px !important;
  left: -7px;
}
.form-error-text-block {
  background-color: #fff6f6;
  border: dashed 1px #fa424a;
  padding: 12px 14px 3px;
  color: #fa424a;
  margin: 0 0 1rem;
  font-size: 1rem;
}
.form-error-text-block li {
  margin: 0 0 10px;
  position: relative;
  padding: 0 0 0 12px;
}
.form-error-text-block li:before {
  content: '';
  display: block;
  width: 5px;
  height: 5px;
  -webkit-border-radius: 50%;
          border-radius: 50%;
  background: #fa424a;
  position: absolute;
  left: 0;
  top: .5em;
  margin-top: -1px;
}
.has-success .form-label {
  color: #46c35f;
}
.has-danger .form-label {
  color: #fa424a;
}
.has-warning .form-label {
  color: #f29824;
}
/* ==========================================================================
   Datepicker
   ========================================================================== */
.input-group.date .input-group-addon {
  background-color: #dbe4ea;
  border: none;
  border-color: #dbe4ea;
  color: rgba(108, 122, 134, 0.7);
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  padding: 0 0.75rem;
}
.input-group.date .input-group-addon .font-icon {
  vertical-align: middle;
  font-size: 1rem /*18/16*/;
  position: relative;
  top: .08rem;
}
.input-group.date .bootstrap-datetimepicker-widget + .input-group-addon,
.input-group.date .input-group-addon:hover {
  background-color: #00a8ff;
  border: none;
  border-color: #00a8ff;
  color: #fff;
}
.input-group.clockpicker .input-group-addon .font-icon {
  vertical-align: middle;
  font-size: 1rem /*18/16*/;
  position: relative;
  top: 0;
}
.bootstrap-datetimepicker-widget {
  font-size: .8125rem;
}
.bootstrap-datetimepicker-widget table thead tr:first-child th:hover {
  background: none;
  color: #00a8ff !important;
}
.bootstrap-datetimepicker-widget table thead tr:first-child th.prev,
.bootstrap-datetimepicker-widget table thead tr:first-child th.next {
  color: #adb7be;
}
.bootstrap-datetimepicker-widget table th,
.bootstrap-datetimepicker-widget table td.day {
  width: 32px;
  height: 28px;
  line-height: 28px;
}
.bootstrap-datetimepicker-widget table th.dow {
  font-weight: 600;
}
.bootstrap-datetimepicker-widget table th.picker-switch {
  width: 165px;
}
.bootstrap-datetimepicker-widget table td.day:hover {
  background: none;
  color: #00a8ff;
}
.bootstrap-datetimepicker-widget table td.active,
.bootstrap-datetimepicker-widget table td.active:hover {
  background-color: #00a8ff;
  color: #fff;
}
.bootstrap-datetimepicker-widget table td span.active {
  background: #00a8ff;
}
.bootstrap-datetimepicker-widget table td span:hover,
.bootstrap-datetimepicker-widget table td.minute:hover {
  background-color: #dbe4ea;
}
.bootstrap-datetimepicker-widget table td span.timepicker-hour {
  background: none !important;
}
.bootstrap-datetimepicker-widget a[data-action].btn {
  padding: 0;
  background: none;
  color: #adb7be;
  border: none;
  -webkit-transition: none;
  -o-transition: none;
  transition: none;
}
.bootstrap-datetimepicker-widget a[data-action].btn:hover {
  color: #6c7a86;
}
.bootstrap-datetimepicker-widget .accordion-toggle span {
  color: rgba(108, 122, 134, 0.7);
  background: #dbe4ea;
}
.bootstrap-datetimepicker-widget .accordion-toggle span:hover {
  color: #fff;
  background-color: #00a8ff;
}
.bootstrap-datetimepicker-widget.dropdown-menu:before,
.bootstrap-datetimepicker-widget.dropdown-menu:after {
  display: none;
}
.bootstrap-datetimepicker-widget .glyphicon-calendar,
.bootstrap-datetimepicker-widget .glyphicon-time {
  font-family: "startui";
  vertical-align: middle;
}
.bootstrap-datetimepicker-widget .glyphicon-calendar:before,
.bootstrap-datetimepicker-widget .glyphicon-time:before {
  vertical-align: middle;
  position: relative;
  top: 0;
}
.bootstrap-datetimepicker-widget .glyphicon-calendar:before {
  content: "\65";
}
.bootstrap-datetimepicker-widget .glyphicon-time:before {
  content: "\23";
}
.bootstrap-datetimepicker-widget .glyphicon-chevron-down:before,
.bootstrap-datetimepicker-widget .glyphicon-chevron-up:before {
  position: relative;
  top: 1px;
}
/* ==========================================================================
   Checkbox & Radio
   ========================================================================== */
.checkbox,
.radio,
.checkbox-slide,
.checkbox-toggle,
.checkbox-bird,
.checkbox-detailed {
  position: relative;
  margin-bottom: .75rem;
}
.checkbox input,
.radio input,
.checkbox-slide input,
.checkbox-toggle input,
.checkbox-bird input,
.checkbox-detailed input {
  position: absolute;
  visibility: hidden;
}
.checkbox input + label,
.radio input + label,
.checkbox-slide input + label,
.checkbox-toggle input + label,
.checkbox-bird input + label,
.checkbox-detailed input + label {
  position: relative;
  display: inline-block;
  font-size: 1rem;
  min-height: 18px;
  line-height: 18px;
  cursor: pointer;
}
.checkbox input:disabled + label,
.radio input:disabled + label,
.checkbox-slide input:disabled + label,
.checkbox-toggle input:disabled + label,
.checkbox-bird input:disabled + label,
.checkbox-detailed input:disabled + label {
  cursor: not-allowed;
  color: #8e9fa7;
}
.checkbox,
.radio {
  cursor: default;
}
.checkbox input + label,
.radio input + label {
  z-index: 2;
  padding: 0 0 0 24px;
}
.checkbox input + label:before,
.radio input + label:before,
.checkbox input + label:after,
.radio input + label:after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
}
.checkbox input + label:before,
.radio input + label:before {
  width: 16px;
  height: 16px;
  background: #fff;
  border: solid 1px #c5d6de;
}
.checkbox input + label:hover:before,
.radio input + label:hover:before {
  background-color: #d9f2ff;
  border-color: #00a8ff;
}
.checkbox input[type="checkbox"] + label:after,
.radio input[type="checkbox"] + label:after {
  display: inline-block;
  width: 16px;
  height: 16px;
  font-family: "startui" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: inherit;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
  position: relative;
  top: -0.15em;
  position: absolute;
  text-align: center;
  top: 0;
  font-size: 11px;
  line-height: 16px;
}
.checkbox input[type="checkbox"] + label:before,
.radio input[type="checkbox"] + label:before {
  -webkit-border-radius: 2px;
          border-radius: 2px;
}
.checkbox input[type="radio"] + label:before,
.radio input[type="radio"] + label:before {
  -webkit-border-radius: 50%;
          border-radius: 50%;
}
.checkbox input:checked + label:before,
.radio input:checked + label:before {
  border-color: #00a8ff;
}
.checkbox input[type="checkbox"]:checked + label:after,
.radio input[type="checkbox"]:checked + label:after {
  content: "\22";
}
.checkbox input[type="radio"]:checked + label:after,
.radio input[type="radio"]:checked + label:after {
  width: 8px;
  height: 8px;
  background: #343434;
  -webkit-border-radius: 50%;
          border-radius: 50%;
  left: 4px;
  top: 4px;
}
.checkbox input:disabled + label:before,
.radio input:disabled + label:before {
  background-color: #eceff4;
  border-color: #dbe4eb;
}
.checkbox input[type="checkbox"]:disabled + label:after,
.radio input[type="checkbox"]:disabled + label:after {
  color: rgba(52, 52, 52, 0.4);
}
.checkbox input[type="radio"]:disabled + label:after,
.radio input[type="radio"]:disabled + label:after {
  background-color: rgba(52, 52, 52, 0.4);
}
.checkbox.checkbox-only,
.radio.checkbox-only {
  padding: 0;
  width: 16px;
  height: 16px;
  margin: 0;
}
.checkbox-slide input + label {
  padding: 0 0 0 52px;
}
.checkbox-slide input + label:before,
.checkbox-slide input + label:after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
}
.checkbox-slide input + label:before {
  left: 1px;
  top: 7px;
  width: 42px;
  height: 4px;
  -webkit-border-radius: 25rem;
          border-radius: 25rem;
  background: #929faa;
  -webkit-transition: background .4s ease;
  -o-transition: background .4s ease;
  transition: background .4s ease;
}
.checkbox-slide input + label:after {
  width: 20px;
  height: 20px;
  top: -1px;
  border: solid 1px #c5d6de;
  -webkit-border-radius: 50%;
          border-radius: 50%;
  -webkit-transition: left .4s ease;
  -o-transition: left .4s ease;
  transition: left .4s ease;
  background: #ffffff;
  background: -webkit-linear-gradient(top, #ffffff 0%, #e4f6ff 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#e4f6ff));
  background: -o-linear-gradient(top, #ffffff 0%, #e4f6ff 100%);
  background: linear-gradient(to bottom, #ffffff 0%, #e4f6ff 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e4f6ff', GradientType=0);
}
.checkbox-slide input + label:hover:after {
  border-color: #00a8ff;
}
.checkbox-slide input:checked + label:before {
  background-color: #343434;
}
.checkbox-slide input:checked + label:after {
  left: 24px;
}
.checkbox-slide input:disabled + label:before {
  background-color: #c6d6df;
}
.checkbox-slide input:disabled + label:after {
  background: #fcfefe;
  border-color: #c5d6de;
  background: -webkit-linear-gradient(top, #fcfefe 0%, #edf2f5 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(#fcfefe), to(#edf2f5));
  background: -o-linear-gradient(top, #fcfefe 0%, #edf2f5 100%);
  background: linear-gradient(to bottom, #fcfefe 0%, #edf2f5 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fcfefe', endColorstr='#edf2f5', GradientType=0);
}
.checkbox-toggle input + label {
  padding: 0 0 0 52px;
}
.checkbox-toggle input + label:before,
.checkbox-toggle input + label:after {
  content: '';
  display: block;
  -webkit-border-radius: 25rem;
          border-radius: 25rem;
  position: absolute;
  left: 0;
  top: -1px;
  height: 20px;
}
.checkbox-toggle input + label:before {
  width: 43px;
  background: #929faa;
  -webkit-transition: background .4s ease;
  -o-transition: background .4s ease;
  transition: background .4s ease;
}
.checkbox-toggle input + label:after {
  width: 20px;
  border: solid 1px #929faa;
  -webkit-transition: left .4s ease;
  -o-transition: left .4s ease;
  transition: left .4s ease;
  background: #ffffff;
  background: -webkit-linear-gradient(top, #ffffff 0%, #eef4f7 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#eef4f7));
  background: -o-linear-gradient(top, #ffffff 0%, #eef4f7 100%);
  background: linear-gradient(to bottom, #ffffff 0%, #eef4f7 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eef4f7', GradientType=0);
}
.checkbox-toggle input + label:hover:after {
  border-color: #00a8ff;
  background: #ffffff;
  background: -webkit-linear-gradient(top, #ffffff 0%, #e9f8ff 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#e9f8ff));
  background: -o-linear-gradient(top, #ffffff 0%, #e9f8ff 100%);
  background: linear-gradient(to bottom, #ffffff 0%, #e9f8ff 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e9f8ff', GradientType=0);
}
.checkbox-toggle input:checked + label:before {
  background-color: #00a8ff;
}
.checkbox-toggle input:checked + label:after {
  left: 23px;
  border-color: #00a8ff;
  background: #ffffff;
  background: -webkit-linear-gradient(top, #ffffff 0%, #e9f8ff 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#e9f8ff));
  background: -o-linear-gradient(top, #ffffff 0%, #e9f8ff 100%);
  background: linear-gradient(to bottom, #ffffff 0%, #e9f8ff 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e9f8ff', GradientType=0);
}
.checkbox-toggle input:disabled + label:before {
  background-color: #dbe4eb;
}
.checkbox-toggle input:disabled + label:after {
  border-color: #dbe4eb;
  background: #ffffff;
  background: -webkit-linear-gradient(top, #ffffff 0%, #eef4f7 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#eef4f7));
  background: -o-linear-gradient(top, #ffffff 0%, #eef4f7 100%);
  background: linear-gradient(to bottom, #ffffff 0%, #eef4f7 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eef4f7', GradientType=0);
}
.checkbox-toggle.-large {
  margin-top: 15px;
}
.checkbox-toggle.-large input + label {
  padding: 0 0 0 69px;
  position: relative;
}
.checkbox-toggle.-large input + label:before,
.checkbox-toggle.-large input + label:after {
  -webkit-border-radius: 35rem;
          border-radius: 35rem;
  height: 30px;
  position: absolute;
  top: -6px;
}
.checkbox-toggle.-large input + label:before {
  width: 60px;
}
.checkbox-toggle.-large input + label:after {
  width: 30px;
}
.checkbox-toggle.-large input:checked + label:after {
  left: 30px;
}
.checkbox-toggle.-extra-large {
  margin-top: 27px;
}
.checkbox-toggle.-extra-large input + label {
  padding: 0 0 0 89px;
  position: relative;
}
.checkbox-toggle.-extra-large input + label:before,
.checkbox-toggle.-extra-large input + label:after {
  -webkit-border-radius: 45rem;
          border-radius: 45rem;
  height: 40px;
  position: absolute;
  top: -12px;
}
.checkbox-toggle.-extra-large input + label:before {
  width: 80px;
}
.checkbox-toggle.-extra-large input + label:after {
  width: 40px;
}
.checkbox-toggle.-extra-large input:checked + label:after {
  left: 40px;
}
.checkbox-bird input + label {
  display: block;
  min-width: 20px;
  height: 20px;
  line-height: 20px;
  padding: 0 0 0 28px;
  position: relative;
}
.checkbox-bird input + label:before {
  font-family: "startui" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: inherit;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
  position: relative;
  top: -0.15em;
  content: "\55";
  color: rgba(173, 183, 190, 0.7);
  font-size: 1.125rem /*18/16*/;
  position: absolute;
  left: 0;
  top: 0;
  display: block;
  width: 20px;
  height: 20px;
}
.checkbox-bird input:checked + label:before {
  color: #00a8ff;
}
.checkbox-bird.grey input:checked + label:before {
  color: #6b7a85;
}
.checkbox-bird.green input:checked + label:before {
  color: #46c35f;
}
.checkbox-bird.purple input:checked + label:before {
  color: #ac6bec;
}
.checkbox-bird.orange input:checked + label:before {
  color: #f29824;
}
.checkbox-bird.red input:checked + label:before {
  color: #fa424a;
}
.checkbox-detailed {
  display: inline-block;
  vertical-align: top;
  margin: 0 12px 12px 0;
}
.checkbox-detailed input + label {
  width: 200px;
  height: 84px;
  border: solid 1px #d8e2e7;
  -webkit-border-radius: 5px;
          border-radius: 5px;
  padding: 0 10px 0 50px;
  line-height: 1.4;
}
.checkbox-detailed input + label:before {
  content: '';
  display: block;
  width: 12px;
  height: 12px;
  border: solid 1px #c5d6de;
  position: absolute;
  left: 19px;
  top: 50%;
  margin-top: -6px;
  -webkit-border-radius: 50%;
          border-radius: 50%;
}
.checkbox-detailed input:checked + label {
  background-color: #e4f6fe;
  border-color: #00a8ff;
}
.checkbox-detailed input:checked + label:before {
  border-color: #00a8ff;
  background-color: #00a8ff;
}
.checkbox-detailed .checkbox-detailed-tbl {
  display: table;
  width: 100%;
  height: 100%;
}
.checkbox-detailed .checkbox-detailed-cell {
  display: table-cell;
  vertical-align: middle;
  padding: 5px 0;
}
.checkbox-detailed .checkbox-detailed-title {
  font-weight: 600;
  display: block;
}
/* ==========================================================================
   Select
   ========================================================================== */
.btn-group.bootstrap-select .font-icon {
  display: inline-block;
  color: #929faa;
  margin: -2px 8px -2px 0;
  font-size: 16px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  vertical-align: middle;
  position: relative;
  top: -0.05em;
}
.btn-group.bootstrap-select .user-item {
  position: relative;
  display: inline-block;
  vertical-align: top;
  min-height: 20px;
  padding: 0 0 0 21px;
}
.btn-group.bootstrap-select .user-item img {
  display: block;
  width: 20px;
  height: 20px;
  position: absolute;
  left: -5px;
  top: 0;
  -webkit-border-radius: 50%;
          border-radius: 50%;
}
.btn-group.bootstrap-select > .btn.dropdown-toggle {
  outline: none !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  background: #fff !important;
  font-weight: 400;
  color: #343434 !important;
  border-color: #d8e2e7 !important;
  padding-left: 0;
  vertical-align: middle;
  font-size: 1rem;
  text-align: left;
  padding-top: 0;
  padding-bottom: 0;
}
.btn-group.bootstrap-select > .btn.dropdown-toggle:after {
  display: none;
}
.btn-group.bootstrap-select > .btn.dropdown-toggle .filter-option {
  display: inline-block;
  position: relative;
  white-space: nowrap;
  padding-left: 1rem;
  overflow: hidden;
  -o-text-overflow: ellipsis;
     text-overflow: ellipsis;
  max-width: 100%;
  padding-top: .375rem;
  padding-bottom: .375rem;
  margin: 0;
}
.btn-group.bootstrap-select > .btn.dropdown-toggle .bs-caret {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 23px;
  text-align: center;
  background: #dbe4ea;
}
.btn-group.bootstrap-select > .btn.dropdown-toggle .bs-caret .caret {
  display: none !important;
}
.btn-group.bootstrap-select > .btn.dropdown-toggle .bs-caret:before,
.btn-group.bootstrap-select > .btn.dropdown-toggle .bs-caret:after {
  content: '';
  display: block;
  width: 0;
  height: 0;
  border-style: solid;
  position: absolute;
  left: 50%;
  margin-left: -3px;
  top: 50%;
}
.btn-group.bootstrap-select > .btn.dropdown-toggle .bs-caret:before {
  border-width: 0 3px 5px 3px;
  border-color: transparent transparent #6c7a86 transparent;
  margin-top: -6px;
}
.btn-group.bootstrap-select > .btn.dropdown-toggle .bs-caret:after {
  border-width: 5px 3px 0 3px;
  border-color: #6c7a86 transparent transparent transparent;
  margin-top: 1px;
}
.btn-group.bootstrap-select > .btn.dropdown-toggle .user-item img {
  top: 50%;
  margin-top: -10px;
}
.btn-group.bootstrap-select .dropdown-menu {
  border-top: none;
  -webkit-border-top-right-radius: 0;
          border-top-right-radius: 0;
  -webkit-border-top-left-radius: 0;
          border-top-left-radius: 0;
  margin-top: -1px;
  font-size: 1rem;
  padding: 0;
}
.btn-group.bootstrap-select .dropdown-menu a {
  display: block;
  padding: 5px 1rem;
  color: #343434;
  position: static;
  border: none;
  font-size: 1rem;
  font-weight: 400;
}
.btn-group.bootstrap-select .dropdown-menu a:hover {
  color: #00a8ff;
}
.btn-group.bootstrap-select .dropdown-menu a:hover .font-icon {
  color: #00a8ff;
}
.btn-group.bootstrap-select .dropdown-menu .dropdown-header {
  font-size: 1rem;
  text-transform: none;
  color: #343434;
  font-weight: 600;
  padding-left: 1rem;
  padding-right: 1rem;
}
.btn-group.bootstrap-select.open > .btn.dropdown-toggle {
  -webkit-border-bottom-left-radius: 0;
          border-bottom-left-radius: 0;
  -webkit-border-bottom-right-radius: 0;
          border-bottom-right-radius: 0;
  border-bottom-color: #fff !important;
}
.btn-group.bootstrap-select.open > .btn.dropdown-toggle .bs-caret {
  background: none;
}
.btn-group.bootstrap-select.open.dropup > .btn.dropdown-toggle {
  -webkit-border-radius: 0 0 3px 3px;
          border-radius: 0 0 3px 3px;
  border-top-color: #fff !important;
  border-bottom-color: #d8e2e7 !important;
}
.btn-group.bootstrap-select.open.dropup .dropdown-menu {
  -webkit-border-radius: .25rem .25rem 0 0;
          border-radius: .25rem .25rem 0 0;
  border-top: solid 1px #d8e2e7;
  border-bottom: none;
  margin-bottom: -1px;
}
.btn-group.bootstrap-select.disabled {
  opacity: .65;
}
.btn-group.bootstrap-select.disabled > .btn.dropdown-toggle.disabled {
  opacity: 1;
  background-color: #dbe4ea !important;
}
.btn-group.bootstrap-select.disabled > .btn.dropdown-toggle.disabled .filter-option {
  color: #6c7a86;
}
.btn-group.bootstrap-select.bootstrap-select-arrow > .btn.dropdown-toggle .bs-caret {
  background: none;
}
.btn-group.bootstrap-select.bootstrap-select-arrow > .btn.dropdown-toggle .bs-caret:after {
  display: none;
}
.btn-group.bootstrap-select.bootstrap-select-arrow > .btn.dropdown-toggle .bs-caret:before {
  width: auto;
  height: auto;
  border: none;
  left: 0;
  margin-left: 0;
  top: 50% !important;
  margin-top: -8px;
  font-family: "startui" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: inherit;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
  position: relative;
  top: -0.15em;
  content: "\51";
  color: #6c7a86;
  font-size: 0.6875rem /*11/16*/;
  text-align: left;
}
.select2,
.select2-icon,
.select2-photo,
.select2-arrow {
  width: 100% !important;
}
.select2-dropdown {
  border-color: #d8e2e7;
  font-size: 1rem;
}
.select2-dropdown.select2-dropdown--above {
  border-bottom: none;
  -webkit-border-bottom-left-radius: 0;
          border-bottom-left-radius: 0;
  -webkit-border-bottom-right-radius: 0;
          border-bottom-right-radius: 0;
}
.select2-results__option {
  padding: 5px 1rem;
  background: none !important;
}
.select2-results__option:hover {
  color: #00a8ff;
}
.select2-results__option[role="group"]:hover {
  color: #343434;
}
.select2-results__option[role="group"] .select2-results__option {
  padding-left: 2.25rem;
}
.select2-results__option[aria-selected="true"] {
  color: #00a8ff;
}
.select2-container--default .select2-selection--single,
.select2-container--arrow .select2-selection--single,
.select2-container--white .select2-selection--single {
  border: none;
  -webkit-border-radius: 0;
          border-radius: 0;
  height: auto;
  background: none;
}
.select2-container--default .select2-selection--single .select2-selection__rendered,
.select2-container--arrow .select2-selection--single .select2-selection__rendered,
.select2-container--white .select2-selection--single .select2-selection__rendered {
  border: solid 1px #d8e2e7;
  -webkit-border-radius: .25rem;
          border-radius: .25rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #343434;
  padding: .375rem 25px .375rem 1rem;
  min-height: 38px;
  background: #fff;
}
.select2-container--default .select2-selection--single .select2-selection__rendered .user-item img,
.select2-container--arrow .select2-selection--single .select2-selection__rendered .user-item img,
.select2-container--white .select2-selection--single .select2-selection__rendered .user-item img {
  top: 50%;
  margin-top: -10px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow,
.select2-container--arrow .select2-selection--single .select2-selection__arrow,
.select2-container--white .select2-selection--single .select2-selection__arrow {
  position: absolute;
  height: 100%;
  top: 0;
  right: 0;
  width: 23px;
  text-align: center;
  background: #dbe4ea;
  -webkit-border-radius: 0 .25rem .25rem 0;
          border-radius: 0 .25rem .25rem 0;
}
.select2-container--default .select2-selection--single .select2-selection__arrow:before,
.select2-container--arrow .select2-selection--single .select2-selection__arrow:before,
.select2-container--white .select2-selection--single .select2-selection__arrow:before,
.select2-container--default .select2-selection--single .select2-selection__arrow:after,
.select2-container--arrow .select2-selection--single .select2-selection__arrow:after,
.select2-container--white .select2-selection--single .select2-selection__arrow:after {
  content: '';
  display: block;
  width: 0;
  height: 0;
  border-style: solid;
  position: absolute;
  left: 50%;
  margin-left: -3px;
  top: 50%;
}
.select2-container--default .select2-selection--single .select2-selection__arrow:before,
.select2-container--arrow .select2-selection--single .select2-selection__arrow:before,
.select2-container--white .select2-selection--single .select2-selection__arrow:before {
  border-width: 0 3px 5px 3px;
  border-color: transparent transparent #6c7a86 transparent;
  margin-top: -6px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow:after,
.select2-container--arrow .select2-selection--single .select2-selection__arrow:after,
.select2-container--white .select2-selection--single .select2-selection__arrow:after {
  border-width: 5px 3px 0 3px;
  border-color: #6c7a86 transparent transparent transparent;
  margin-top: 1px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow b,
.select2-container--arrow .select2-selection--single .select2-selection__arrow b,
.select2-container--white .select2-selection--single .select2-selection__arrow b {
  display: none;
}
.select2-container--default .select2-results__group,
.select2-container--arrow .select2-results__group,
.select2-container--white .select2-results__group {
  font-weight: 600;
  padding: 5px 1rem;
}
.select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-container--arrow .select2-results__option--highlighted[aria-selected],
.select2-container--white .select2-results__option--highlighted[aria-selected] {
  background: none;
  color: #00a8ff;
}
.select2-container--default .select2-results > .select2-results__options,
.select2-container--arrow .select2-results > .select2-results__options,
.select2-container--white .select2-results > .select2-results__options {
  max-height: 250px;
}
.select2-container--default .select2-search--inline .select2-search__field,
.select2-container--arrow .select2-search--inline .select2-search__field,
.select2-container--white .select2-search--inline .select2-search__field {
  font-size: 1rem;
  padding-left: 11px;
}
.select2-container--default .font-icon,
.select2-container--arrow .font-icon,
.select2-container--white .font-icon {
  display: inline-block;
  color: #929faa;
  margin: -2px 8px -2px 0;
  font-size: 1rem;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  vertical-align: middle;
  position: relative;
  top: -0.05em;
}
.select2-container--default .user-item,
.select2-container--arrow .user-item,
.select2-container--white .user-item {
  position: relative;
  display: inline-block;
  vertical-align: top;
  min-height: 20px;
  padding: 0 0 0 21px;
}
.select2-container--default .user-item img,
.select2-container--arrow .user-item img,
.select2-container--white .user-item img {
  display: block;
  width: 20px;
  height: 20px;
  position: absolute;
  left: -5px;
  top: 0;
  -webkit-border-radius: 50%;
          border-radius: 50%;
}
.select2-container--default.select2-container--open .select2-selection--single .select2-selection__rendered,
.select2-container--arrow.select2-container--open .select2-selection--single .select2-selection__rendered,
.select2-container--white.select2-container--open .select2-selection--single .select2-selection__rendered {
  border-bottom-color: #fff;
  -webkit-border-bottom-right-radius: 0;
          border-bottom-right-radius: 0;
  -webkit-border-bottom-left-radius: 0;
          border-bottom-left-radius: 0;
}
.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow,
.select2-container--arrow.select2-container--open .select2-selection--single .select2-selection__arrow,
.select2-container--white.select2-container--open .select2-selection--single .select2-selection__arrow {
  background: none;
}
.select2-container--default.select2-container--open.select2-container--above .select2-selection--single .select2-selection__rendered,
.select2-container--arrow.select2-container--open.select2-container--above .select2-selection--single .select2-selection__rendered,
.select2-container--white.select2-container--open.select2-container--above .select2-selection--single .select2-selection__rendered {
  -webkit-border-radius: 0 0 .25rem .25rem;
          border-radius: 0 0 .25rem .25rem;
  border-top-color: #fff;
  border-bottom-color: #d8e2e7;
}
.select2-container--default.select2-container--disabled,
.select2-container--arrow.select2-container--disabled,
.select2-container--white.select2-container--disabled {
  opacity: .65;
}
.select2-container--default.select2-container--disabled .select2-selection--single,
.select2-container--arrow.select2-container--disabled .select2-selection--single,
.select2-container--white.select2-container--disabled .select2-selection--single {
  cursor: not-allowed;
  background: none;
}
.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__rendered,
.select2-container--arrow.select2-container--disabled .select2-selection--single .select2-selection__rendered,
.select2-container--white.select2-container--disabled .select2-selection--single .select2-selection__rendered {
  background-color: #dbe4ea;
  color: #6c7a86;
}
.select2-container--default.select2-container--disabled .user-item img,
.select2-container--arrow.select2-container--disabled .user-item img,
.select2-container--white.select2-container--disabled .user-item img {
  opacity: .7;
}
.select2-container--default .select2-selection--multiple,
.select2-container--arrow .select2-selection--multiple,
.select2-container--white .select2-selection--multiple {
  border-color: #d8e2e7;
  min-height: 38px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice,
.select2-container--arrow .select2-selection--multiple .select2-selection__choice,
.select2-container--white .select2-selection--multiple .select2-selection__choice {
  color: #fff;
  background: #919fa9;
  border: none;
  font-weight: 600;
  font-size: 1rem;
  padding: 0 2rem 0 .5rem;
  height: 26px;
  line-height: 26px;
  position: relative;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove,
.select2-container--arrow .select2-selection--multiple .select2-selection__choice__remove,
.select2-container--white .select2-selection--multiple .select2-selection__choice__remove {
  position: absolute;
  right: 0;
  top: 1px;
  color: #fff !important;
  width: 1.5rem;
  text-align: center;
  font-size: 2rem;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-container--arrow .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-container--white .select2-selection--multiple .select2-selection__choice__remove:hover {
  opacity: .7;
}
.select2-container--default.select2-container--focus .select2-selection--multiple,
.select2-container--arrow.select2-container--focus .select2-selection--multiple,
.select2-container--white.select2-container--focus .select2-selection--multiple {
  border-color: #c5d6de;
}
.select2-container--arrow .select2-selection--single .select2-selection__arrow {
  background: none;
}
.select2-container--arrow .select2-selection--single .select2-selection__arrow:after {
  display: none;
}
.select2-container--arrow .select2-selection--single .select2-selection__arrow:before {
  width: auto;
  height: auto;
  border: none;
  left: 0;
  margin-left: 0;
  top: 50% !important;
  margin-top: -8px;
  font-family: "startui" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: inherit;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
  position: relative;
  top: -0.15em;
  content: "\51";
  color: #6c7a86;
  font-size: 0.6875rem /*11/16*/;
  text-align: left;
}
.select2-container--white .select2-selection--single .select2-selection__arrow {
  background: none;
}
/* ==========================================================================
   Upload
   ========================================================================== */
.uploading-list-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}
.uploading-list .uploading-list-item {
  zoom: 1;
  padding: .9rem 0;
  border-top: solid 1px #d8e2e7;
}
.uploading-list .uploading-list-item:before,
.uploading-list .uploading-list-item:after {
  content: " ";
  display: table;
}
.uploading-list .uploading-list-item:after {
  clear: both;
}
.uploading-list .uploading-list-item:first-child {
  border-top: none;
}
.uploading-list .uploading-list-item-wrapper {
  position: relative;
  padding: 0 20px 0 0;
  margin: 0 0 6px;
}
.uploading-list .uploading-list-item-name,
.uploading-list .uploading-list-item-size {
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  line-height: 1rem;
}
.uploading-list .uploading-list-item-name {
  padding-right: 15px;
  overflow: hidden;
  max-width: 100%;
  -o-text-overflow: ellipsis;
     text-overflow: ellipsis;
}
.uploading-list .uploading-list-item-name .font-icon {
  color: #adb7be;
  margin: 0 5px 0 0;
  vertical-align: middle;
  font-size: 1rem;
}
.uploading-list .uploading-list-item-size {
  color: #919fa9;
}
.uploading-list .uploading-list-item-close {
  border: none;
  background: none;
  color: #dbe4ea;
  font-size: .75rem;
  position: absolute;
  right: 0;
  top: 2px;
}
.uploading-list .uploading-list-item-close:hover {
  color: #00a8ff;
}
.uploading-list .uploading-list-item-progress,
.uploading-list .uploading-list-item-speed {
  font-size: .875rem;
}
.uploading-list .uploading-list-item-progress {
  float: left;
}
.uploading-list .uploading-list-item-speed {
  float: right;
}
.uploading-list .progress {
  margin-bottom: .4rem;
}
.uploading-list .progress {
  background-color: #f6f8fa;
  height: 5px;
  -webkit-border-radius: 0;
          border-radius: 0;
  color: #00a8ff;
}
.uploading-list .progress[value] {
  color: #00a8ff;
}
.uploading-list .progress[value]::-webkit-progress-value {
  background-color: #00a8ff;
}
.uploading-list .progress-bar {
  -webkit-border-radius: 0;
          border-radius: 0;
  background-color: #00a8ff;
}
.drop-zone {
  width: 205px;
  height: 205px;
  border: dashed 2px #adb7be;
  text-align: center;
  padding: 25px 0 0;
}
.drop-zone.dragover {
  border-color: #00a8ff;
}
.drop-zone .font-icon {
  line-height: 60px;
  color: #919fa9;
  font-size: 2.75rem;
}
.drop-zone .drop-zone-caption {
  font-size: 1rem;
  font-weight: 600;
  color: #919fa9;
  margin: 0 0 1rem;
}
.btn-file {
  position: relative;
  overflow: hidden;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}
.btn-file input[type="file"] {
  position: absolute;
  left: 0;
  top: 0;
  height: 200px;
  width: 1000px;
  opacity: 0;
  cursor: pointer;
}
.uploading-container {
  zoom: 1;
  padding: 15px 25px 15px 15px;
}
.uploading-container:before,
.uploading-container:after {
  content: " ";
  display: table;
}
.uploading-container:after {
  clear: both;
}
.uploading-container .uploading-container-left {
  float: left;
  position: relative;
  z-index: 5;
  width: 220px;
}
.uploading-container .uploading-container-right {
  float: right;
  width: 100%;
  margin-left: -220px;
}
.uploading-container .uploading-container-right-in {
  margin-left: 220px;
}
@media (max-width: 544px) {
  .uploading-container .uploading-container-left,
  .uploading-container .uploading-container-right {
    float: none;
    width: auto;
    margin: 0;
  }
  .uploading-container .uploading-container-right-in {
    margin: 0;
  }
  .uploading-container .drop-zone {
    width: auto;
    margin: 0 0 1rem;
  }
}
/* ==========================================================================
   Typehead
   ========================================================================== */
.typeahead-button button .font-icon-search {
  vertical-align: middle;
}
.typeahead-filter button {
  -webkit-border-radius: 0;
          border-radius: 0;
}
.typeahead-list {
  padding: 4px 0 3px;
}
.typeahead-list > li.typeahead-group.active > a,
.typeahead-list > li.typeahead-group > a,
.typeahead-list > li.typeahead-group > a:focus,
.typeahead-list > li.typeahead-group > a:hover {
  background-color: #f6f8fa;
}
.typeahead-item .row {
  margin: 0;
}
.typeahead-item .avatar {
  margin-right: 10px;
}
.typeahead-item .username {
  margin-right: 5px;
}
.typeahead-item small {
  font-size: .8125rem;
}
.typeahead-dropdown > li > a,
.typeahead-list > li > a {
  padding: 3px .75rem;
  font-size: .9375rem;
}
var.result-container {
  font-size: .9375rem;
}
