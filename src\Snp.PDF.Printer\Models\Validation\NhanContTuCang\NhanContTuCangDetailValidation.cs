﻿using FluentValidation;
using SNP.PDF.Printer.Resources;

namespace SNP.PDF.Printer.Models.Validation
{
    public class NhanContTuCangDetailValidation : AbstractValidator<NhanContTuCangDetail>
    {
        public NhanContTuCangDetailValidation()
        {
            RuleFor(c => c.SoCont)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.SoContIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.ISO)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.ISOIsNullOrEmpty.ErrorCode);
        }
    }
}
