/* ==========================================================================
   Mixins
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   Profile
   ========================================================================== */
.profile-header-photo {
  height: 245px;
  margin: -27px -15px 20px;
  background: #232936 no-repeat 50% 50%;
  background-size: cover;
  position: relative;
  color: #fff;
}
.profile-header-photo .profile-header-photo-in {
  display: table;
  width: 100%;
  height: 100%;
  border-collapse: collapse;
}
.profile-header-photo .profile-header-photo-in > .tbl-cell {
  vertical-align: bottom;
}
.profile-header-photo .change-cover {
  position: absolute;
  right: 30px;
  top: 20px;
  text-decoration: none;
  color: #fff;
  border: none;
  background: rgba(0, 0, 0, 0.4);
  font-size: .875rem;
  padding: 6px 8px;
  -webkit-border-radius: 3px;
          border-radius: 3px;
  overflow: hidden;
}
.profile-header-photo .change-cover:hover {
  color: #00a8ff;
}
.profile-header-photo .change-cover input[type="file"] {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  font-size: 100rem;
  opacity: 0;
  cursor: pointer;
}
.profile-header-photo .change-cover .font-icon {
  font-size: 1rem;
  margin: 0 4px 0 0;
  position: relative;
  top: 1px;
}
.profile-header-photo .info-block {
  padding: 0 15px;
}
.profile-header-photo .info-tbl p {
  margin: 0;
}
.profile-header-photo .info-tbl .tbl-cell {
  vertical-align: bottom;
  padding: 15px 0;
}
.profile-header-photo .info-tbl .tbl-cell-stat {
  width: 15%;
  text-align: right;
}
.profile-header-photo .info-tbl .title {
  font-size: 1.5rem;
  font-weight: 600;
}
.profile-header-photo .info-tbl .inline-block {
  display: inline-block;
  text-align: center;
}
.profile-header-photo.gradient .info-block {
  background: -webkit-linear-gradient(top, rgba(125, 185, 232, 0) 0%, #000000 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(125, 185, 232, 0)), to(#000000));
  background: -o-linear-gradient(top, rgba(125, 185, 232, 0) 0%, #000000 100%);
  background: linear-gradient(to bottom, rgba(125, 185, 232, 0) 0%, #000000 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#007db9e8', endColorstr='#000000', GradientType=0);
}
.profile-header-photo.opacity .info-block {
  background: rgba(0, 0, 0, 0.4);
}
/* ==========================================================================
   Profile side
   ========================================================================== */
.profile-side {
  margin: -245px 0 20px;
  position: relative;
}
.profile-side p {
  margin: 0;
}
.profile-side .box-typical {
  margin: 0 0 15px;
}
.profile-side .box-typical-inner {
  padding: 12px 15px;
}
.profile-side .box-typical-inner .line-with-icon:last-child {
  margin-bottom: 0;
}
.profile-side .progress-compact-style {
  margin: 0 0 15px;
}
.profile-side .progress-compact-style:last-child {
  margin-bottom: 0;
}
.profile-side-user {
  text-align: center;
  padding: 28px 15px 15px;
}
.profile-side-user .avatar-preview {
  display: block;
  border: none;
  background: none;
  position: relative;
  margin: 0 auto 20px;
  color: #fff;
  text-align: center;
  overflow: hidden;
  cursor: pointer;
}
.profile-side-user .avatar-preview input[type="file"] {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  font-size: 100rem;
  opacity: 0;
  cursor: pointer;
}
.profile-side-user .avatar-preview .update {
  display: block;
  opacity: 0;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  -webkit-border-radius: 50%;
          border-radius: 50%;
  background: rgba(0, 0, 0, 0.4);
  font-size: .875rem;
  padding: 35% 0 0;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.profile-side-user .avatar-preview .font-icon {
  display: block;
  font-size: 1rem;
}
.profile-side-user .avatar-preview:hover .update {
  opacity: 1;
}
.profile-side-user .btn,
.profile-side-user .btn-group {
  width: 100%;
  max-width: 180px;
  margin: 0 5px;
}
.profile-side-user .btn-group {
  margin-top: 10px;
  margin-bottom: 10px;
}
.profile-side-user .dropdown-menu {
  min-width: 100%;
}
.profile-side-user .bottom-txt {
  color: #6c7a86;
  padding: 6px 0 0;
}
.profile-side-stat {
  text-align: center;
}
.profile-side-stat .tbl-cell {
  vertical-align: middle;
  border-left: solid 1px #d8e2e7;
  padding: 12px 10px;
}
.profile-side-stat .tbl-cell:first-child {
  border-left: none;
}
.profile-side-stat .number {
  display: block;
  font-weight: 600;
  font-size: 1rem;
}
.line-with-icon,
p.line-with-icon {
  position: relative;
  padding: 0 0 0 1.75em;
  margin: 0 0 1em;
}
.line-with-icon .font-icon,
p.line-with-icon .font-icon {
  color: #adb7be;
  position: absolute;
  left: 0;
  top: 0;
  font-size: 1rem;
}
.line-with-icon a,
p.line-with-icon a {
  text-decoration: none;
  color: #0082c6;
  border-bottom: solid 1px rgba(0, 130, 198, 0.3);
}
.line-with-icon a:hover,
p.line-with-icon a:hover {
  border-bottom-color: transparent;
}
@media (max-width: 1056px) {
  .profile-side {
    margin-top: 0;
  }
}
/* ==========================================================================
   Profile tabs
   ========================================================================== */
.profile-tabs .tab-pane .box-typical {
  margin: 0 0 15px;
}
.profile-tabs .tab-pane .box-typical:first-child {
  border-top: none;
  -webkit-border-top-left-radius: 0;
          border-top-left-radius: 0;
  -webkit-border-top-right-radius: 0;
          border-top-right-radius: 0;
  padding-top: 15px;
}
.profile-tabs .write-something {
  background: none;
  border: none;
  -webkit-border-radius: 0;
          border-radius: 0;
  padding: 15px;
  color: #343434 !important;
  width: 100%;
  border-top: solid 1px #d8e2e7;
}
.profile-post .profile-post-header {
  border-bottom: solid 1px #d8e2e7;
  padding: 12px 40px 12px 15px;
  position: relative;
}
.profile-post .profile-post-header .user-card-row {
  font-size: 1rem;
  line-height: 20px;
}
.profile-post .profile-post-header .shared {
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -10px;
  text-decoration: none;
  color: #919fa9;
  border: none;
}
.profile-post .profile-post-header .shared:hover {
  color: #00a8ff;
}
.profile-post .profile-post-content {
  padding: 15px;
}
.profile-post .profile-post-content .cstm-video-player {
  max-width: 425px;
  width: 100%;
  margin: 15px 0 0;
}
.profile-post .profile-post-content .profile-post-content-note {
  color: #919fa9;
  margin: 0 0 .5em;
}
.profile-post .profile-post-content .profile-post-content-note a {
  text-decoration: none;
  color: #919fa9;
  border-bottom: solid 1px rgba(145, 159, 169, 0.3);
}
.profile-post .profile-post-content .profile-post-content-note a:hover {
  border-bottom-color: transparent;
}
@media (min-width: 768px) {
  .profile-post .profile-post-content .minimalistic-player {
    width: 50%;
    margin: 15px 0 5px;
  }
}
.profile-post .comment-rows-container {
  border-bottom: none;
}
.profile-post .profile-post-meta .meta-item {
  margin: 0 20px 0 0;
  text-decoration: none;
  color: #919fa9;
  border: none;
}
.profile-post .profile-post-meta .meta-item:hover {
  color: #00a8ff;
}
.profile-post .profile-post-meta .meta-item .font-icon {
  margin: 0 4px 0 0;
  position: relative;
}
.profile-post .profile-post-meta .meta-item .font-icon.font-icon-comment {
  top: 1px;
}
.profile-post .profile-post-gall-fluid {
  zoom: 1;
  margin: 15px -10px -15px -15px;
}
.profile-post .profile-post-gall-fluid:before,
.profile-post .profile-post-gall-fluid:after {
  content: " ";
  display: table;
}
.profile-post .profile-post-gall-fluid:after {
  clear: both;
}
.profile-post .profile-post-gall-fluid .col {
  padding: 0 0 5px 5px;
}
.profile-post .profile-post-gall-fluid img {
  display: block;
  width: 100%;
}
.profile-post .profile-post-gall {
  zoom: 1;
  margin: 15px -5px 0 0;
}
.profile-post .profile-post-gall:before,
.profile-post .profile-post-gall:after {
  content: " ";
  display: table;
}
.profile-post .profile-post-gall:after {
  clear: both;
}
.profile-post .profile-post-gall .col {
  float: left;
  width: 195px;
  padding: 0 5px 5px 0;
}
.profile-post .profile-post-gall .col:first-child {
  width: 390px;
}
.profile-post .profile-post-gall img {
  display: block;
  width: 100%;
}
.profile-post .profile-post-gall-grid[data-columns]::before {
  content: '3 .column.size-1of3';
}
.profile-post .profile-post-gall-grid .column {
  float: left;
}
.profile-post .profile-post-gall-grid .size-1of4 {
  width: 25%;
}
.profile-post .profile-post-gall-grid .size-1of3 {
  width: 33.333%;
}
.profile-post .profile-post-gall-grid .size-1of2 {
  width: 50%;
}
@media screen and (max-width: 520px) {
  .profile-post .profile-post-gall-grid[data-columns]::before {
    content: '1 .column';
  }
}
@media screen and (min-width: 521px) and (max-width: 767px) {
  .profile-post .profile-post-gall-grid[data-columns]::before {
    content: '2 .column.size-1of2';
  }
}
@media screen and (min-width: 768px) and (max-width: 1300px) {
  .profile-post .profile-post-gall-grid[data-columns]::before {
    content: '3 .column.size-1of3';
  }
}
@media screen and (min-width: 1301px) {
  .profile-post .profile-post-gall-grid[data-columns]::before {
    content: '4 .column.size-1of4';
  }
}
.profile-post .profile-post-card {
  margin: 15px 0 0;
}
.profile-post .profile-post-card p {
  margin: 0;
}
.profile-post .profile-post-card .tbl-cell-photo {
  width: 115px;
  padding-right: 15px;
}
.profile-post .profile-post-card .tbl-cell-photo img {
  display: block;
  width: 100%;
}
.profile-post .profile-post-card .title {
  font-size: 1rem;
  margin: 0 0 5px;
}
.profile-post .profile-post-card .title a {
  text-decoration: none;
  color: #0082c6;
  border-bottom: solid 1px rgba(0, 130, 198, 0.3);
}
.profile-post .profile-post-card .title a:hover {
  border-bottom-color: transparent;
}
.profile-settings .form-label {
  font-weight: 400;
  padding: 8px 0 0;
}
.profile-settings .profile-settings-btns .btn {
  margin: 0 10px 0 0;
}
.profile-settings .profile-settings-btns .btn:last-child {
  margin-right: 0;
}
.range-settings {
  position: relative;
  padding: 0 0 0 0;
  margin: 8px 0 0;
}
.range-settings .irs {
  height: 10px;
}
.range-settings .irs-line,
.range-settings .irs-bar,
.range-settings .irs-bar-edge {
  top: 6px;
}
.range-settings .irs-slider {
  top: 1px;
}
.range-settings .range-settings-percent {
  position: absolute;
  right: -45px;
  top: 0;
  color: #919fa9;
}
.range-settings .range-setting-actions {
  position: absolute;
  right: -102px;
  top: 0;
}
.range-settings .range-setting-actions button {
  border: none;
  background: none;
  color: #adb7be;
  margin: 0 0 0 8px;
}
.range-settings .range-setting-actions button:hover {
  color: #00a8ff;
}
.range-settings .range-setting-actions button.del:hover {
  color: #fa424a;
}
@media (max-width: 1199px) {
  .range-settings {
    padding-right: 102px;
  }
  .range-settings .range-settings-percent {
    right: 57px;
  }
  .range-settings .range-setting-actions {
    right: 0;
  }
}
