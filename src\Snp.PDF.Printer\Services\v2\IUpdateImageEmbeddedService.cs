﻿using Snp.PDF.Printer.Models.v2;
using System;

namespace Snp.PDF.Printer.Services.v2
{
    public interface IUpdateImageUrlService : IDisposable
    {
        /// <summary>
        /// Cập nhật đường dẫn đến các files ảnh cần được nhúng vào file Html
        /// </summary>
        /// <param name="data"></param>
        /// <param name="billType"></param>
        /// <returns></returns>
        dynamic UpdateImageEmbedded(dynamic data, string billType);

        /// <summary>
        /// Update logo, each pdf file only have one logo file
        /// </summary>
        /// <param name="data">dynamic data</param>
        /// <param name="siteId">Site Id</param>
        /// <returns></returns>
        dynamic UpdateLogo(dynamic data, string siteId);

        /// <summary>
        /// Update barcode, each pdf file only have one barcode 
        /// </summary>
        /// <param name="data"></param>
        /// <param name="meta"></param>
        /// <returns></returns>
        dynamic UpdateBarcode(dynamic data, MetaInfomation meta);
    }

}
