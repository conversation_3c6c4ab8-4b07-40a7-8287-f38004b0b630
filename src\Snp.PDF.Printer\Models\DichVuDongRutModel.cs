﻿using System.Collections.Generic;
using SNP.PDF.Printer.Models.Base;

namespace SNP.PDF.Printer.Models
{
    public class DichVuDongRutModel
    {
        /// <summary>
        ///     Logo công ty, lấy theo bảng cấu hình thông tin Site (AM_SITE)
        /// </summary>
        public string LogoType { get; set; }

        /// <summary>
        ///     Tên công ty, lấy theo bảng cấu hình thông tin Site (AM_SITE)
        /// </summary>
        public string TenCongTy { get; set; }

        /// <summary>
        ///     Tên phương án của phiếu EIR được chọn
        /// </summary>
        public string TenPhuongAn { get; set; }

        /// <summary>
        ///     Là Id của phiếu EIR được chọn
        ///     Cột EIR_ID trong màn hình T61
        /// </summary>
        public string SoPhieuEIR { get; set; }

        /// <summary>
        ///     Là ngày tạo phiếu EIR.
        ///     Format: dd/MM/yyyy
        ///     Cột Create Date trong màn hình T61
        /// </summary>
        public string NgayTaoEIR { get; set; }

        /// <summary>
        ///     Là ngày tạo phiếu EIR, format: dd/MM/yyyy hh:mm:sss
        ///     Cột CreateDate trong màn hình T61
        /// </summary>
        public string NgayGioTaoEIR { get; set; }

        /// <summary>
        ///     Là tên khách hàng tương ứng phiếu EIR được chọn
        ///     Cột CustName trong màn hình T61
        /// </summary>
        public string TenKhachHang { get; set; }

        /// <summary>
        ///     Là tên tàu nhập/xuất + mã chuyến tương ứng phiếu EIR được chọn
        ///     Cấu trúc tên tàu / mã chuyến
        ///     Trường tàu nhập khẩu hoặc tàu xuất khẩu trong màn hình C01 tương ứng Item key
        /// </summary>
        public string TenTauMaChuyen { get; set; }

        /// <summary>
        ///     Là cảng đích tương ứng phiếu EIR được chọn
        ///     Trường FinalPort trong màn hình T61
        /// </summary>
        public string CangDich { get; set; }

        /// <summary>
        ///     Là cảng chuyển tải tương ứng phiếu EIR được chọn
        ///     Cột LL dích Port trong màn hình T61
        /// </summary>
        public string CangChuyenTai { get; set; }

        /// <summary>
        ///     Thông tin hư hỏng của container
        ///     Lấy theo cấu trúc: Trách nhiệm h/ h:Mô tả hưMô tả vị trí h/ hkích thước h/ h lấy trong màn hình CP7. Mỗi hư hỏng
        ///     cách nhau bởi dấu -
        /// </summary>
        public string GhiChuHuHong { get; set; }

        /// <summary>
        ///     Thông tin này chỉ có đối với các phương án đóng rút
        ///     Trường Tr. Lượng hàng (ton) trong màn hình C36/C37 tương ứng Item key
        /// </summary>
        public string TrongLuongHang { get; set; }

        /// <summary>
        ///     Là tên đội công nhân của cont trên phiếu EIR
        ///     Trường Đội CN màn hình C36/C37 (lấy tên)
        /// </summary>
        public string TenDoiBocXep { get; set; }

        /// <summary>
        ///     Là tên phương thức đóng rút hàng của cont trên phiếu EIR
        ///     Trường Phương thức Đ.hàng/ Phương thức R.hàng màn hình C36/C37 (lấy tên)
        /// </summary>
        public string TenPhuongThucRutHang { get; set; }

        /// <summary>
        ///     Là số phiếu thu thuộc quyển hóa đơn nội bộ của cont trên phiếu EIR
        /// </summary>
        public string SoHoaDonNoiBo { get; set; }

        /// <summary>
        ///     Là người yêu cầu đóng rút hàng của cont trên phiếu EIR
        ///     Trường Người y/c màn hình CPF
        /// </summary>
        public string NguoiYeuCau { get; set; }

        /// <summary>
        ///     Là số điện thoại yêu cầu đóng rút hàng của cont trên phiếu EIR
        ///     Trường Số ĐT màn hình CPF
        /// </summary>
        public string SoDienThoai { get; set; }

        /// <summary>
        ///     Là thông tin tên đầy đủ của khu vực
        /// </summary>
        public string TenSite { get; set; }

        /// <summary>
        ///     Là thông tin Slogan của site
        /// </summary>
        public string Slogan { get; set; }

        public List<DichVuDongRutDetail> Detail { get; set; }
    }

    public class DichVuDongRutDetail : BaseChiTietPhieuEir
    {
        /// <summary>
        ///     Là Hãng tàu của Cont trên phiếu EIR
        ///     Trường Chủ KT trong màn hình C01 tương ứng Item key
        /// </summary>
        public string HangTau { get; set; }

        /// <summary>
        ///     Là thông tin về trọng lượng Max Gross của cont trên phiếu EIR
        ///     Trường Max gross (ton) trong màn hình C01 tương ứng Item key, chỉ thể hiện số
        /// </summary>
        public string MaxGross { get; set; }

        /// <summary>
        ///     Là thông tin về trọng lượng VGM của cont do khách hàng khai báo hoặc cảng nhập
        ///     Trường VGM (ton) trong màn hình C01 tương ứng Item key, chỉ thể hiện số
        /// </summary>
        public string VgmCustomer { get; set; }

        /// <summary>
        ///     Thông tin này chỉ có đối với các phương án đóng rút
        ///     Trường Khu vực trong màn hình C01 tương ứng Item key
        /// </summary>
        public string KhuVuc { get; set; }
    }
}
