/*
 * CSS Styles that are needed by jScrollPane for it to operate correctly.
 *
 * Include this stylesheet in your site or copy and paste the styles below into your stylesheet - jScrollPane
 * may not operate correctly without them.
 */
.jspContainer {
  overflow: hidden;
  position: relative;
  width: 100% !important;
}
.jspPane {
  position: absolute;
  width: 100% !important;
}
.jspVerticalBar {
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 100%;
  background: none;
}
.jspHorizontalBar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: none;
}
.jspCap {
  display: none;
}
.jspHorizontalBar .jspCap {
  float: left;
}
.jspTrack {
  background: none;
  position: relative;
}
.jspDrag {
  background: #d3dee6;
  border: solid 1px #fff;
  -webkit-border-radius: 2px;
          border-radius: 2px;
  position: relative;
  top: 0;
  left: 0;
  cursor: pointer;
}
.jspHorizontalBar .jspTrack,
.jspHorizontalBar .jspDrag {
  float: left;
  height: 100%;
}
.jspArrow {
  background: #50506d;
  text-indent: -20000px;
  display: block;
  cursor: pointer;
  padding: 0;
  margin: 0;
}
.jspArrow.jspDisabled {
  cursor: default;
  background: #80808d;
}
.jspVerticalBar .jspArrow {
  height: 8px;
}
.jspHorizontalBar .jspArrow {
  width: 8px;
  float: left;
  height: 100%;
}
.jspVerticalBar .jspArrow:focus {
  outline: none;
}
.jspCorner {
  background: #eeeef4;
  float: left;
  height: 100%;
}
/* Yuk! CSS Hack for IE6 3 pixel bug :( */
* html .jspCorner {
  margin: 0 -3px 0 0;
}
