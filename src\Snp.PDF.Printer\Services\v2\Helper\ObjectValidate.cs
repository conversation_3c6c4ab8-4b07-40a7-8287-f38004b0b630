﻿using SNP.PDF.Printer.Resources;
using System;

namespace Snp.PDF.Printer.Services.v2
{
    public class ObjectValidate : IObjectValue
    {
        #region IDispose pattern
        // Flag: Has Dispose already been called?
        bool disposed = false;

        // Public implementation of Dispose pattern callable by consumers.
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        // Protected implementation of Dispose pattern.
        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;

            if (disposing)
            {
                // Free any other managed objects here.
                //
            }

            // Free any unmanaged objects here.
            //
            disposed = true;
        }

        ~ObjectValidate()
        {
            Dispose(false);
        }
        #endregion

        public bool CheckValidArray(dynamic data, out int count, out Error error)
        {
            try
            {
                error = null;
                count = data.Count;
                return true;
            }
            catch (Exception)
            {
                error = Resource.Instance.ObjectNotTypeArray;
                // data not typeof array
                count = 0;
                return false;
            }
        }

        public bool TryGetValue(dynamic item, string propertyName, out string value, out Error error)
        {
            try
            {
                error = null;
                value = item[propertyName].Value.ToString();
                return true;
            }
            catch (Exception)
            {
                error = Resource.Instance.NotGetValueFromPropertyName;
                value = string.Empty;
                return false;
            }
        }
    }
}
