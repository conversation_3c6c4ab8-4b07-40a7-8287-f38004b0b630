﻿using Snp.PDF.Printer.Services.v2.Validate;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Text.RegularExpressions;

namespace Snp.PDF.Printer.Services.v2.Utils
{
    public static class Utilities
    {
        /// <summary>
        /// CompareNumber
        /// </summary>
        /// <param name="value1">string for number1</param>
        /// <param name="value2">string for number2</param>
        /// <param name="value">value equals 0: number1 is the same as number2,value greater than 0: number1 is later than number2,value less than 0: number1 is earlier than number2</param>
        /// <returns>true: value1 and value2 is typeof number, false: value1 or value2 invalid typeof number</returns>
        public static bool CompareNumber(this string value1, string value2, out RequireTypes value)
        {
            value = RequireTypes.None;

            double d1 = 0;
            double d2 = 0;
            if (double.TryParse(value1, out d1))
            {
                if (double.TryParse(value2, out d2))
                {
                    if (d1 > d2)
                    {
                        value = RequireTypes.GreaterThan;
                    }
                    else if (d1 < d2)
                    {
                        value = RequireTypes.LessThan;
                    }
                    else
                    {
                        value = RequireTypes.Equals;
                    }
                    return true;
                }
            }
            return false;// the value1 or value2 invalid typeof number
        }
        /// <summary>
        /// CompareTime
        /// </summary>
        /// <param name="value1">string for DateTime1</param>
        /// <param name="value2">string for DateTime2</param>
        /// <param name="value">value equals 0: DateTime1 is the same as DateTime2,value greater than 0: DateTime1 is later than DateTime2,value less than 0: DateTime1 is earlier than DateTime2</param>
        /// <returns>true: value1 and value2 is typeof DateTime, false: value1 or value2 invalid typeof DateTime</returns>
        public static bool CompareTime(this string value1, string value2, out RequireTypes value)
        {
            if (CompareTimeWithFormat(value1, value2, FormatDateTime.ddMMyyyyhhmmss, out value))
                return true;

            if (CompareTimeWithFormat(value1, value2, FormatDateTime.ddMMyyyy, out value))
                return true;

            return false;
        }


        /// <summary>
        /// CompareTimeWithFormat
        /// </summary>
        /// <param name="value1">string for DateTime1</param>
        /// <param name="value2">string for DateTime2</param>
        /// <param name="format">string format time</param>
        /// <param name="value">value equals 0: DateTime1 is the same as DateTime2,value greater than 0: DateTime1 is later than DateTime2,value less than 0: DateTime1 is earlier than DateTime2</param>
        /// <returns>true: value1 and value2 is typeof DateTime, false: value1 or value2 invalid typeof DateTime</returns>
        private static bool CompareTimeWithFormat(string value1, string value2, string format, out RequireTypes value)
        {
            value = RequireTypes.None;
            DateTime dt1;
            DateTime dt2;
            if (DateTime.TryParseExact(value1, format, CultureInfo.CurrentCulture, DateTimeStyles.NoCurrentDateDefault, out dt1))
            {
                if (DateTime.TryParseExact(value2, format, CultureInfo.CurrentCulture, DateTimeStyles.NoCurrentDateDefault, out dt2))
                {
                    int valueCompare = DateTime.Compare(dt1, dt2);
                    if (valueCompare > 0)
                    {
                        value = RequireTypes.GreaterThan;
                    }
                    else if (valueCompare < 0)
                    {
                        value = RequireTypes.LessThan;
                    }
                    else
                    {
                        value = RequireTypes.Equals;
                    }
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// DeserializeObjectFromFileConfig
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="filename"></param>
        /// <param name="exResult"></param>
        /// <returns></returns>
        public static T DeserializeObjectFromFileConfig<T>(string filename, out Exception exResult)
        {
            try
            {
                exResult = null;
                if (!File.Exists(filename)) return default(T);

                return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(File.ReadAllText(filename));
            }
            catch (Exception ex)
            {
                exResult = ex;
                return default(T);
            }
        }
        /// <summary>
        /// Get value object from property name
        /// </summary>
        /// <param name="src"></param>
        /// <param name="propName"></param>
        /// <returns></returns>
        public static object GetPropValue(this object src, string propName)
        {
            // the first: check property container on properties of src
            bool flag = false;
            var properties = src.GetType().GetProperties();
            for (int i = properties.Length - 1; i >= 0; i--)
            {
                if (properties[i].Name.Equals(propName))
                {
                    flag = true;
                    break;
                }
            }

            // the second: check flag = false then return null (not found property on src) else get value of property propName
            if (!flag) return null;

            return src.GetType().GetProperty(propName).GetValue(src, null);
        }

        /// <summary>
        /// Get all value from pattern
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        public static IEnumerable<string> Matches(this string input, string pattern )
        {
            var regex = new Regex(pattern);
            var matches = regex.Matches(input);
            foreach (Match item in matches)
            {
                yield return item.Value;
            }
        }

        /// <summary>
        /// Convert url to local path
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static string ToLocalPath(this string url)
        {
            // get filename,
            // get foldername
            var token = url.Split('/');
            if (token.Length <= 2) return string.Empty;
            
            return StaticPath.Instance.GetPathPdf(token[token.Length - 2], token[token.Length - 1]);
        }
    }
}
