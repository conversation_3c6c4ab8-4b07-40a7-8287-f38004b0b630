/* ==========================================================================
   Mixins
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   Faq
   ========================================================================== */
.faq-item {
  position: relative;
  padding: 0 0 0 60px;
}
.faq-item-circle {
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 34px;
  font-weight: 700;
  color: #919fa9;
  position: absolute;
  left: 0;
  top: 0;
  background: #ecf2f5;
  -webkit-border-radius: 50%;
          border-radius: 50%;
  border: solid 3px #d8e2e7;
}
/* ==========================================================================
   Faq search
   ========================================================================== */
.faq-page-header-search {
  -webkit-border-radius: .25rem .25rem 0 0;
          border-radius: .25rem .25rem 0 0;
  margin: -1px -1px 0 -1px;
  padding: 35px 30px;
  background: #1873d2 url('/img/faq-search.jpg') no-repeat 50% 50%;
  background-size: cover;
}
.faq-page-header-search .search {
  position: relative;
}
.faq-page-header-search .form-control {
  border: none;
  padding-right: 44px;
}
.faq-page-header-search .find {
  display: block;
  width: 44px;
  height: 36px;
  position: absolute;
  right: 0;
  top: 0;
  text-align: center;
  border: none;
  background: none;
  color: #adb7be;
}
.faq-page-header-search .find:hover {
  color: #00a8ff;
}
.faq-page-header-search .find .font-icon {
  position: relative;
  top: 1px;
}
.faq-page-cats {
  padding: 45px 30px 0;
  border-bottom: solid 1px #d8e2e7;
}
.faq-page-cats .faq-page-cat {
  text-align: center;
  margin: 0 0 40px;
}
.faq-page-cats .faq-page-cat-icon {
  margin: 0 0 25px;
}
.faq-page-cats .faq-page-cat-icon img {
  height: 80px;
}
.faq-page-cats .faq-page-cat-title {
  font-size: 1.25rem /*20/16*/;
  margin: 0 0 .5rem;
}
.faq-page-cats .faq-page-cat-title a {
  color: #343434;
}
.faq-page-cats .faq-page-cat-title a:hover {
  color: #00a8ff;
}
.faq-page-cats .faq-page-cat-txt {
  color: #919fa9;
}
.faq-page-questions {
  padding: 25px 30px 30px;
}
.faq-page-questions > h1,
.faq-page-questions > h2,
.faq-page-questions > h3,
.faq-page-questions > h4 {
  text-align: center;
  margin-bottom: 25px;
}
.faq-page-questions .faq-page-quest {
  margin: 0 0 22px;
}
.faq-page-questions .faq-page-quest-title {
  font-weight: 600;
  margin: 0 0 5px;
}
.faq-page-questions .faq-page-quest-title a {
  text-decoration: none;
  color: #0082c6;
  border: none;
}
.faq-page-questions .faq-page-quest-title a:hover {
  color: #fa424a;
}
/* ==========================================================================
   Add customers
   ========================================================================== */
.add-customers-screen {
  text-align: center;
  display: table;
  width: 100%;
  height: 100%;
}
.add-customers-screen .add-customers-screen-in {
  display: table-cell;
  vertical-align: middle;
  padding: 15px;
}
.add-customers-screen .add-customers-screen-user {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  border: solid 1px #c5d6de;
  text-align: center;
  line-height: 78px;
  -webkit-border-radius: 5px;
          border-radius: 5px;
  color: #adb7be;
  font-size: 2.5rem;
}
.add-customers-screen .add-customers-screen-user .font-icon {
  line-height: inherit;
  vertical-align: middle;
}
.add-customers-screen .lead {
  font-size: 1.5rem;
}
.add-customers-screen .btn {
  margin: 1rem 0 0;
}
/* ==========================================================================
   Documentation
   ========================================================================== */
.documentation-meta {
  padding: 0 0 14px;
  border-bottom: solid 1px #c5d6de;
}
.documentation-meta p {
  margin: 10px 0;
}
.documentation-meta .inline {
  display: inline;
  margin: 0 30px 0 0;
}
.documentation-meta a {
  color: #343434;
}
.documentation-meta a:hover {
  color: #00a8ff;
}
.documentation-header {
  margin: 0 0 .75rem;
}
.documentation-header h1,
.documentation-header h2,
.documentation-header h3,
.documentation-header h4,
.documentation-header h5,
.documentation-header h6,
.documentation-header .lead {
  margin: 0 0 .5rem;
}
.documentation-header-by {
  color: #919fa9;
}
.documentation-header-by a {
  color: #343434;
}
.documentation-header-by a:hover {
  color: #00a8ff;
}
.documentation-header-by .avatar-preview {
  margin: 0 5px;
  position: relative;
  top: -1px;
}
/* ==========================================================================
   Icons page
   ========================================================================== */
.bs-glyphicons {
  zoom: 1;
  margin: 1rem 0 0;
}
.bs-glyphicons:before,
.bs-glyphicons:after {
  content: " ";
  display: table;
}
.bs-glyphicons:after {
  clear: both;
}
.bs-glyphicons .bs-glyphicons-list {
  zoom: 1;
}
.bs-glyphicons .bs-glyphicons-list:before,
.bs-glyphicons .bs-glyphicons-list:after {
  content: " ";
  display: table;
}
.bs-glyphicons .bs-glyphicons-list:after {
  clear: both;
}
.bs-glyphicons li {
  float: left;
  width: 25%;
  height: 115px;
  padding: 10px;
  font-size: 10px;
  line-height: 1.4;
  text-align: center;
  background-color: #f9f9f9;
  border: 1px solid #fff;
}
.bs-glyphicons li:hover {
  color: #fff;
  background-color: #00a8ff;
}
.bs-glyphicons li:hover .glyphicon,
.bs-glyphicons li:hover .font-icon {
  color: #fff;
}
.bs-glyphicons .glyphicon,
.bs-glyphicons .font-icon {
  margin-top: 5px;
  margin-bottom: 10px;
  font-size: 24px;
  color: #adb7be;
}
.bs-glyphicons .glyphicon-class {
  display: block;
  text-align: center;
  word-wrap: break-word;
}
@media (min-width: 768px) {
  .bs-glyphicons li {
    width: 12.5%;
    font-size: 14px;
  }
}
.flag-icon-list .flag-wrapper {
  width: 100%;
  display: inline-block;
  position: relative;
  -webkit-box-shadow: 0 0 2px black;
          box-shadow: 0 0 2px black;
  overflow: hidden;
  margin: 10px 0;
}
.flag-icon-list .flag-wrapper:after {
  padding-top: 75%;
  display: block;
  content: '';
}
.flag-icon-list .flag {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
}
.flag-icon-list .flag-icon-background {
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
}
.fontawesome-icon-list > div {
  margin-bottom: 10px;
}
.fontawesome-icon-list a {
  text-decoration: none;
  color: #343434;
  border: none;
}
.fontawesome-icon-list a:hover {
  color: #0082c6;
}
.fontawesome-icon-list .fa {
  color: #adb7be;
}
