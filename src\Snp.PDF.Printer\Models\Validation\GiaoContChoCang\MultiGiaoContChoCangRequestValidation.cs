﻿using FluentValidation;
using SNP.PDF.Printer.Models.Request;
using SNP.PDF.Printer.Resources;

namespace SNP.PDF.Printer.Models.Validation
{
    public class MultiGiaoContChoCangRequestValidation : AbstractValidator<MultiGiaoContChoCangRequest>
    {
        public MultiGiaoContChoCangRequestValidation()
        {
            RuleFor(c => c.Data)
                    .Must(AppUtil.IsEmptyArray)
                    .WithMessage(Resource.Instance.ArrayDetailIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.Data)
                    .ForEach(c => c.SetValidator(new GiaoContChoCangValidation()));
        }
    }
}
