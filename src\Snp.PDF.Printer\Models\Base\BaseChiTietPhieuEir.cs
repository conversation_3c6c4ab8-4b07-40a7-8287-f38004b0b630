﻿namespace SNP.PDF.Printer.Models.Base
{
    public class BaseChiTietPhieuEir
    {
        /// <summary>
        ///     Là số Cont trên phiếu EIR
        ///     Cột CtnrNo trong màn hình T61
        /// </summary>
        public string SoCont { get; set; }

        /// <summary>
        ///     Là số ISO của Cont trên phiếu EIR
        ///     Cột ISO trong màn hình T61
        /// </summary>
        public string ISO { get; set; }

        /// <summary>
        ///     Là chủ KT của Cont trên phiếu EIR
        ///     Trưởng chủ KT trong màn hình C01 tương ứng Item key
        /// </summary>
        public string ChuKT { get; set; }

        /// <summary>
        ///     Là số seal trên phiếu đăng ký
        ///     Trường số seal trong màn hình C01 tương ứng Item key
        /// </summary>
        public string SoSeal { get; set; }

        /// <summary>
        ///     Là trạng thái (F/E) của Cont trên phiếu EIR
        ///     Cột FEL trong màn hình T61
        /// </summary>
        public string TtCont { get; set; }

        /// <summary>
        ///     Là trọng lượng toàn bộ (hàng và vỏ) của cont trên phiếu EIR
        ///     Trường Tr.lượng tổng (ton) trong màn hình C01 tương ứng Item key
        /// </summary>
        public string TongTL { get; set; }

        /// <summary>
        ///     Là số IMDG của Cont trên phiếu EIR
        ///     Trường IMDG Class trong màn hình CP2 tương ứng Item key, thể hiển tất cả IMDG cách nhau bằng dấu phẩy (,)
        /// </summary>
        public string SoIMDG { get; set; }

        /// <summary>
        ///     Là thông tin quá khổ của Cont trên phiếu EIR
        ///     Check box OW/OH/OL tương ứng trong màn hình CP3
        /// </summary>
        public string OwOhOl { get; set; }
    }
}
