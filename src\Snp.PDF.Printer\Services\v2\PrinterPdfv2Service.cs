﻿using Microsoft.Extensions.Configuration;
using Snp.PDF.Printer.Models.v2;
using Snp.PDF.Printer.Services.v2.Helper;
using Snp.PDF.Printer.Services.v2.Utils;
using SNP.PDF.Printer.Enums;
using SNP.PDF.Printer.Helper;
using SNP.PDF.Printer.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Snp.PDF.Printer.Services.v2
{
    public class PrinterPdfv2Service : BasePrinterService, IPrinterPdfv2Service
    {
        #region IDispose pattern

        // Flag: Has Dispose already been called?
        private bool disposed = false;

        // Public implementation of Dispose pattern callable by consumers.
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        // Protected implementation of Dispose pattern.
        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;

            if (disposing)
            {
                // Free any other managed objects here.
                //
            }

            // Free any unmanaged objects here.
            //
            disposed = true;
        }

        ~PrinterPdfv2Service()
        {
            Dispose(false);
        }

        #endregion IDispose pattern

        #region Defines

        private readonly IConvertHtml2PdfService _convertHtml2PdfServices;
        private readonly IUpdateImageUrlService _updateImageEmbeddedService;
        private readonly IConfiguration _config;

        #endregion Defines

        #region C'tor

        /// <summary>
        /// C'tor
        /// </summary>
        /// <param name="convertHtml2PdfServices"></param>
        /// <param name="validateModel"></param>
        /// <param name="updateImageEmbeddedService"></param>
        public PrinterPdfv2Service(IConvertHtml2PdfService convertHtml2PdfServices,
            IValidateModel validateModel,
            IUpdateImageUrlService updateImageEmbeddedService,
            IConfiguration config)
            : base(validateModel)
        {
            _convertHtml2PdfServices = convertHtml2PdfServices;
            _updateImageEmbeddedService = updateImageEmbeddedService;
            _config = config;
        }

        #endregion C'tor

        /// <summary>
        /// PrinterPdf
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public async Task<PrinterPdfResponse> PrinterPdf(PrinterPdfRequest req)
        {
            try
            {
                var data = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(req.JsonData);

                // Update images embedded to html
                data = _updateImageEmbeddedService.UpdateImageEmbedded(data, req.BillType);

                var siteShowLogo = _config.GetValue<string>("SiteApplyShowLogo");

                if (siteShowLogo.Contains(req.SiteId))
                {
                    data = _updateImageEmbeddedService.UpdateLogo(data, req.SiteId);
                }

                data = _updateImageEmbeddedService.UpdateBarcode(data, req as MetaInfomation);

                Error error = CheckValidateSingleFile(data, req.BillType);
                if (error != null)
                {
                    return await Failure(error);
                }

                // convert html to pdf
                IEnumerable<byte> fileData = await _convertHtml2PdfServices.ConvertAsync(data, req.BillType, req.FileName, req.SiteId);

                var fileName = $"{req.FileName}.{FileType.pdf}";
                var folder = DateTime.Now.ToString(FormatDateTime.FormatFolderName);
                var destPath = StaticPath.Instance.CreateDestinationFilePdf(folder, fileName);

                // write file
                fileData.WriteFile(destPath, out Exception exResult);
                if (exResult != null)
                    return await Exception(exResult);

                var pathPdf = StaticPath.Instance.GetUrlFilePdf(folder, fileName);
                return await Task.FromResult(new PrinterPdfResponse(pathPdf));
            }
            catch (Exception ex)
            {
                return await Exception(ex);
            }
        }

        /// <summary>
        /// Zip
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public async Task<PrinterPdfResponse> ZipPrinterPdf(ZipPrinterPdfRequest req)
        {
            try
            {
                var printerPdfs = req.PrinterPdfs.ToArray();
                var pathPdfs = new string[printerPdfs.Length];

                PrinterPdfResponse result = null;
                var flagBreak = false;

                for (int i = 0; i < printerPdfs.Length; i++)
                {
                    result = await PrinterPdf(printerPdfs[i]);

                    if (result.IsError)
                    {
                        flagBreak = true;
                        break;
                    }
                    pathPdfs[i] = result.Data.ToLocalPath();
                }

                if (flagBreak)
                    return await Failure(result);

                var fileZipName = string.IsNullOrEmpty(req.FileName) ? $"{Identity.Next()}.{FileType.zip}" : $"{req.FileName}.{FileType.zip}";

                var folder = DateTime.Now.ToString(FormatDateTime.FormatFolderName);
                ZipFileHelper.CreateZipFile(StaticPath.Instance.CreateDestinationFileZip(folder, fileZipName), pathPdfs, out Exception ex);

                var pathZip = StaticPath.Instance.GetUrlZipFile(folder, fileZipName);
                return await Task.FromResult(new PrinterPdfResponse(pathZip));
            }
            catch (Exception ex)
            {
                return await Exception(ex);
            }
        }
    }
}