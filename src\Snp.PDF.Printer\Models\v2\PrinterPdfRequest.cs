﻿namespace Snp.PDF.Printer.Models.v2
{
    public class PrinterPdfRequest : MetaInfomation
    {
        /// <summary>
        /// Data format json
        /// </summary>
        public string JsonData { get; set; }
      
        public PrinterPdfRequest() : base()
        {

        }
    }
    public class MetaInfomation
    {
        /// <summary>
        /// SiteId
        /// <para>Dựa trên SiteId -> logo tương <PERSON>ng</para>
        /// </summary>
        public string SiteId { get; set; }

        /// <summary>
        /// Tên file người dùng muốn rename
        /// </summary>
        public string FileName { get; set; }
        /// <summary>
        /// Loại phiếu
        /// <para>GIAO_CONT_CHO_CANG: giao container hàng cho cảng</para>
        /// <para>NHAN_CONT_TU_CANG: nhận container hàng cho cảng</para>
        /// <para>DICH_VU_DONG_RUT: dịch vị đóng rút</para>
        /// </summary>
        public string BillType { get; set; }
        public MetaInfomation()
        {

        }

    }
}
