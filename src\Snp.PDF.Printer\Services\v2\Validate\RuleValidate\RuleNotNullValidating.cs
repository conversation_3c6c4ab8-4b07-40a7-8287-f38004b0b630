﻿using System;
using SNP.PDF.Printer.Resources;

namespace Snp.PDF.Printer.Services.v2.Validate.RuleValidate
{
    public class RuleNotNullValidating : BaseRuleValidate, IRuleValidating
    {
        #region IDispose pattern
        // Flag: Has Dispose already been called?
        bool disposed = false;

        // Public implementation of Dispose pattern callable by consumers.
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        // Protected implementation of Dispose pattern.
        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;

            if (disposing)
            {
                // Free any other managed objects here.
                //
            }

            // Free any unmanaged objects here.
            //
            disposed = true;
        }

        ~RuleNotNullValidating()
        {
            Dispose(false);
        }
        #endregion
        
        public Error Validating(string value, string valueCompare = "", string propertyName = "", string message = "")
        {
            if (value is null)
                return Invalid(value, propertyName, message);
            return null;
        }
    }
}
