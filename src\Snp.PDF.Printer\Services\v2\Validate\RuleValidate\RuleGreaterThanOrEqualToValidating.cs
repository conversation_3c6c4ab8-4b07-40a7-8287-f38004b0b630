﻿using Snp.PDF.Printer.Services.v2.Utils;
using SNP.PDF.Printer.Resources;
using System;

namespace Snp.PDF.Printer.Services.v2.Validate.RuleValidate
{
    public class RuleGreaterThanOrEqualToValidating : BaseRuleValidate, IRuleValidating
    {
        #region IDispose pattern
        // Flag: Has Dispose already been called?
        bool disposed = false;

        // Public implementation of Dispose pattern callable by consumers.
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        // Protected implementation of Dispose pattern.
        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;

            if (disposing)
            {
                // Free any other managed objects here.
                //
            }

            // Free any unmanaged objects here.
            //
            disposed = true;
        }

        ~RuleGreaterThanOrEqualToValidating()
        {
            Dispose(false);
        }
        #endregion

        public Error Validating(string value, string valueCompare = "", string propertyName = "", string message = "")
        {
            RequireTypes type;
            //check value is datatime
            if (value.CompareTime(valueCompare, out type))
            {
                if (type == RequireTypes.GreaterThan || type == RequireTypes.Equals)
                    return Invalid(value, propertyName, message);
            }

            // value invalid typeof DateTime
            // check value is number
            if (value.CompareNumber(valueCompare, out type))
            {
                if (type == RequireTypes.GreaterThan || type == RequireTypes.Equals) 
                     return Invalid(value, propertyName, message);
            }

            return Invalid(value, propertyName, message);// value not compare 
        }
    }
}
