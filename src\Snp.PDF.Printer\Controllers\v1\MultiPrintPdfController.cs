﻿using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using SNP.PDF.Printer.Enums;
using SNP.PDF.Printer.Models.Entities;
using SNP.PDF.Printer.Models.Request;
using SNP.PDF.Printer.Models.Response;
using SNP.PDF.Printer.Models.ViewModels;
using SNP.PDF.Printer.Services;

namespace SNP.PDF.Printer.Controllers
{
    [ApiVersion("1.0")]
    [ApiController]
    public class MultiPrintPdfController : ControllerBase
    {
        private readonly IPhieu _phieuService;
        private readonly IMapper _mapper;

        public MultiPrintPdfController(IPhieu phieuService, IMapper mapper)
        {
            _phieuService = phieuService;
            _mapper = mapper;
        }

        /// <summary>
        ///     in nhiều phiếu in giao cont cho cảng
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [MapToApiVersion("1.0")]
        [Route("api/v1/multi/giaocontchocang")]
        [HttpPost]
        public async Task<IActionResult> Print(MultiGiaoContChoCangRequest request)
        {
            var data = _mapper.Map<List<GiaoContChoCangVm>>(request.Data);
            data.ForEach(c => c.Update());
            var url = await _phieuService.CreateMultiplePhieu(data, new MetaEntity(PrintType.GiaoContChoCang));
            return Ok(new GiaoContChoCangResponse(url, data));
        }

        /// <summary>
        ///     in nhiều phiếu in nhận cont từ cảng
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [MapToApiVersion("1.0")]
        [Route("api/v1/multi/nhanconttucang")]
        [HttpPost]
        public async Task<IActionResult> Print(MultiNhanContTuCangRequest request)
        {
            var data = _mapper.Map<List<NhanContTuCangVm>>(request.Data);
            data.ForEach(c => c.Update());
            var url = await _phieuService.CreateMultiplePhieu(data, new MetaEntity(PrintType.NhanContTuCang));
            return Ok(new NhanContTuCangResponse(url, data));
        }

        /// <summary>
        ///     in nhiều phiếu in dịch vụ đóng rút
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [MapToApiVersion("1.0")]
        [Route("api/v1/multi/dichvudongrut")]
        [HttpPost]
        public async Task<IActionResult> Print(MultiDichVuDongRutRequest request)
        {
            var data = _mapper.Map<List<DichVuDongRutVm>>(request.Data);
            data.ForEach(c => c.Update());
            var url = await _phieuService.CreateMultiplePhieu(data, new MetaEntity(PrintType.DichVuDongRut));
            return Ok(new DichVuDongRutResponse(url, data));
        }
    }
}
