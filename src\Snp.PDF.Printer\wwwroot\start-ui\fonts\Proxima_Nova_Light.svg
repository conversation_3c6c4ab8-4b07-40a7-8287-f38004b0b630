<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20150824 at Sat Jan  9 01:08:00 2016
 By Everything Fonts
Copyright (c) <PERSON>, 2005. All rights reserved.
</metadata>
<defs>
<font id="Proxima_Nova_Light" horiz-adv-x="571" >
  <font-face 
    font-family="Proxima_Nova_Light"
    font-weight="300"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 6 3 0 0 2 0 4"
    ascent="790"
    descent="-210"
    x-height="483"
    cap-height="667"
    bbox="-171 -265 1060 907"
    underline-thickness="20"
    underline-position="-153"
    unicode-range="U+000D-FB04"
  />
<missing-glyph horiz-adv-x="517" 
d="M434 -90h-351v842h351v-842zM402 -60v782h-288v-782h288z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="743" 
d="M415 0h-52v437h-80v46h80v44q0 71 35 110.5t96 39.5q39 0 65 -15l-16 -41q-19 11 -44 11q-84 0 -84 -105v-44h98v-46h-98v-437zM637 559q-16 0 -28 12t-12 28t12 27.5t28 11.5q17 0 28.5 -11.5t11.5 -27.5q0 -17 -11.5 -28.5t-28.5 -11.5zM532 -196q-56 0 -99 37l23 41
q31 -33 71 -33q38 0 61 22t23 67v545h52v-545q0 -66 -34 -100t-97 -34zM150 0h-52v437h-80v46h80v44q0 70 35 110t96 40q53 0 92 -34l-25 -36q-27 25 -62 25q-84 0 -84 -105v-44h98v-46h-98v-437z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="478" 
d="M150 0h-52v437h-80v46h80v44q0 71 35 110.5t96 39.5q39 0 65 -15l-16 -41q-19 11 -44 11q-84 0 -84 -105v-44h98v-46h-98v-437zM372 559q-16 0 -28 12t-12 28t12 27.5t28 11.5q17 0 28.5 -11.5t11.5 -27.5q0 -17 -12 -28.5t-28 -11.5zM267 -196q-56 0 -99 37l23 41
q31 -33 71 -33q38 0 61 22t23 67v545h52v-545q0 -66 -34 -100t-97 -34z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="530" 
d="M150 0h-52v437h-80v46h80v44q0 70 35 110t96 40q53 0 92 -34l-25 -36q-27 25 -62 25q-84 0 -84 -105v-44h98v-46h-98v-437zM415 0h-52v437h-80v46h80v44q0 70 35 110t96 40q53 0 92 -34l-25 -36q-27 25 -62 25q-84 0 -84 -105v-44h98v-46h-98v-437z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="478" 
d="M150 0h-52v437h-80v46h80v44q0 71 35 110.5t96 39.5q39 0 65 -15l-16 -41q-19 11 -44 11q-84 0 -84 -105v-44h98v-46h-98v-437zM372 559q-16 0 -28 12t-12 28t12 27.5t28 11.5q17 0 28.5 -11.5t11.5 -27.5q0 -17 -12 -28.5t-28 -11.5zM398 0h-52v483h52v-483z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="478" 
d="M150 0h-52v437h-80v46h80v44q0 71 35 110.5t96 39.5q39 0 65 -15l-16 -41q-19 11 -44 11q-84 0 -84 -105v-44h98v-46h-98v-437zM398 0h-52v667h52v-667z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="743" 
d="M415 0h-52v437h-80v46h80v44q0 71 35 110.5t96 39.5q39 0 65 -15l-16 -41q-19 11 -44 11q-84 0 -84 -105v-44h98v-46h-98v-437zM637 559q-16 0 -28 12t-12 28t12 27.5t28 11.5q17 0 28.5 -11.5t11.5 -27.5q0 -17 -11.5 -28.5t-28.5 -11.5zM663 0h-52v483h52v-483zM150 0
h-52v437h-80v46h80v44q0 70 35 110t96 40q53 0 92 -34l-25 -36q-27 25 -62 25q-84 0 -84 -105v-44h98v-46h-98v-437z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="743" 
d="M415 0h-52v437h-80v46h80v44q0 71 35 110.5t96 39.5q39 0 65 -15l-16 -41q-19 11 -44 11q-84 0 -84 -105v-44h98v-46h-98v-437zM663 0h-52v667h52v-667zM150 0h-52v437h-80v46h80v44q0 70 35 110t96 40q53 0 92 -34l-25 -36q-27 25 -62 25q-84 0 -84 -105v-44h98v-46h-98
v-437z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="835" 
d="M150 0h-52v437h-80v46h80v44q0 71 35 110.5t96 39.5q39 0 65 -15l-16 -41q-19 11 -44 11q-84 0 -84 -105v-44h98v-46h-98v-437zM398 121q22 -36 66 -61t93 -25q79 0 124 58t45 148q0 91 -45 149t-124 58q-48 0 -92.5 -26t-66.5 -62v-239zM398 0h-52v667h52v-262
q64 90 168 90q97 0 156.5 -69.5t59.5 -184.5t-59.5 -184t-156.5 -69q-52 0 -96.5 25t-71.5 64v-77z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="805" 
d="M150 0h-52v437h-80v46h80v44q0 71 35 110.5t96 39.5q39 0 65 -15l-16 -41q-19 11 -44 11q-84 0 -84 -105v-44h98v-46h-98v-437zM724 0h-52v329q0 66 -30 92.5t-87 26.5q-45 0 -89 -24t-68 -59v-365h-52v667h52v-257q29 34 77 59.5t97 25.5q152 0 152 -155v-340z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="770" 
d="M150 0h-52v437h-80v46h80v44q0 71 35 110.5t96 39.5q39 0 65 -15l-16 -41q-19 11 -44 11q-84 0 -84 -105v-44h98v-46h-98v-437zM751 0h-70l-193 230l-90 -85v-145h-52v667h52v-460l285 276h69l-225 -218z" />
    <glyph glyph-name=".notdef" horiz-adv-x="517" 
d="M434 -90h-351v842h351v-842zM402 -60v782h-288v-782h288z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="CR" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="259" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="219" 
d="M130 174h-41l-13 493h67zM109 -9q-18 0 -30.5 13t-12.5 31q0 17 12.5 30t30.5 13t31.5 -13t13.5 -30q0 -18 -13.5 -31t-31.5 -13z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="317" 
d="M109 427h-30q-21 201 -21 214q0 15 10 25.5t26 10.5q15 0 25.5 -10.5t10.5 -25.5zM238 427h-30q-21 201 -21 214q0 15 10.5 25.5t25.5 10.5t25.5 -10.5t10.5 -25.5z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="579" 
d="M284 0h-48l64 186h-118l-63 -186h-47l62 186h-110l13 41h112l71 213h-113l12 41h115l61 186h47l-61 -186h117l61 186h48l-63 -186h111l-11 -41h-113l-72 -213h115l-12 -41h-116zM312 227l72 213h-117l-71 -213h116z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="584" 
d="M273 -100v89q-145 7 -225 105l37 43q75 -88 188 -96v280q-49 14 -76.5 24.5t-61.5 31t-50 51t-16 72.5q0 76 58 124.5t146 52.5v91h46v-91q120 -10 195 -93l-38 -42q-59 71 -157 82v-252q47 -13 77.5 -25.5t64.5 -34.5t51 -55.5t17 -79.5q0 -71 -50.5 -126t-159.5 -62
v-89h-46zM469 173q0 55 -38.5 84t-111.5 51v-267q79 6 114.5 45t35.5 87zM129 503q0 -48 37 -73t107 -45v240q-63 -3 -103.5 -37t-40.5 -85z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="720" 
d="M190 350q-69 0 -112.5 46t-43.5 116q0 71 44 118t112 47q69 0 113 -47t44 -118q0 -70 -44 -116t-113 -46zM173 0h-45l426 667h46zM530 -12q-69 0 -113 46.5t-44 116.5q0 71 44 118t113 47t113 -47t44 -118q0 -70 -44 -116.5t-113 -46.5zM190 388q48 0 79.5 36.5
t31.5 87.5q0 53 -31 89.5t-80 36.5q-48 0 -79 -36.5t-31 -89.5q0 -51 31 -87.5t79 -36.5zM530 27q49 0 79.5 36t30.5 88q0 53 -30.5 89.5t-79.5 36.5t-80 -36.5t-31 -89.5q0 -52 31 -88t80 -36z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="639" 
d="M602 0h-78q-38 33 -85 80q-82 -92 -196 -92q-87 0 -144 48t-57 134q0 72 39 117.5t110 84.5q-55 87 -55 153q0 65 46 108.5t113 43.5q63 0 101.5 -33.5t38.5 -92.5q0 -29 -11 -54.5t-23.5 -41.5t-41.5 -36.5t-44.5 -29t-52.5 -28.5q29 -40 86 -105q63 -71 89 -98
q49 73 76 162l48 -20q-47 -118 -88 -178q48 -50 129 -122zM247 34q87 0 158 81q-74 76 -100 107q-59 69 -89 112q-55 -32 -85.5 -69t-30.5 -92q0 -65 43 -102t104 -37zM192 524q0 -54 46 -127q37 19 56 31t43.5 31t35 41t10.5 49q0 40 -24.5 62t-62.5 22q-43 0 -73.5 -31
t-30.5 -78z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="187" 
d="M109 427h-30q-21 201 -21 214q0 15 10 25.5t26 10.5q15 0 25.5 -10.5t10.5 -25.5z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="227" 
d="M210 -176l-31 -23q-133 201 -133 442t133 442l31 -22q-53 -109 -79.5 -204t-26.5 -216t26.5 -216t79.5 -203z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="227" 
d="M48 -199l-31 23q53 107 79.5 202.5t26.5 216.5q0 122 -26.5 217t-79.5 203l31 22q134 -203 134 -442t-134 -442z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="335" 
d="M186 399h-37l5 117l-98 -64l-19 33l104 53l-104 53l19 33l98 -64l-5 117h37l-6 -117l99 64l18 -33l-103 -53l103 -53l-18 -33l-99 64z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="496" 
d="M467 316h-196v-217h-45v217h-197v42h197v211h45v-211h196v-42z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="219" 
d="M159 14q0 -40 -20.5 -77.5t-51.5 -59.5l-29 25q24 16 40 42.5t18 49.5q-7 -1 -10 -1q-18 0 -29.5 12t-11.5 30q0 17 12.5 30t30.5 13q21 0 36 -17t15 -47z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="300" 
d="M270 218h-240v48h240v-48z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="219" 
d="M109 -10q-18 0 -31 13.5t-13 31.5q0 17 13 30t31 13t31.5 -13t13.5 -30q0 -18 -13.5 -31.5t-31.5 -13.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="282" 
d="M45 -20h-45l237 707h45z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="608" 
d="M305 -12q-64 0 -114 31t-77.5 82.5t-41.5 110t-14 121.5t14 121.5t41.5 109.5t77 82t114.5 31q64 0 113 -31t77 -82t42 -109.5t14 -121.5q0 -62 -14 -121t-42 -110.5t-77 -82.5t-113 -31zM305 40q50 0 87.5 26t58 69.5t30 93t9.5 104.5t-9.5 104.5t-30 93t-58 69
t-87.5 25.5q-51 0 -88.5 -25.5t-58 -69t-30.5 -93t-10 -104.5t10 -104.5t30.5 -93t58 -69.5t88.5 -26z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="308" 
d="M225 0h-57v589l-107 -114l-35 37l147 155h52v-667z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="582" 
d="M496 0h-440v47q80 65 128 105t102 91t82.5 88.5t47.5 78.5t19 79q0 67 -45.5 101.5t-106.5 34.5q-60 0 -107.5 -23.5t-75.5 -63.5l-39 36q36 48 94 75.5t128 27.5q83 0 146.5 -48t63.5 -140q0 -53 -25 -106t-79 -111.5t-105.5 -104t-138.5 -115.5h351v-52z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="544" 
d="M259 -12q-78 0 -137 31.5t-90 79.5l39 34q65 -93 187 -93q77 0 122.5 38t45.5 104q0 68 -49.5 101.5t-129.5 33.5q-52 0 -62 -1v54q10 -1 62 -1q72 0 120.5 32t48.5 95q0 60 -46 94.5t-114 34.5q-103 0 -179 -85l-36 36q83 101 218 101q91 0 153 -47t62 -129
q0 -47 -25 -83t-55 -51.5t-63 -21.5q54 -6 103.5 -49t49.5 -117q0 -84 -60.5 -137.5t-164.5 -53.5z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="541" 
d="M408 0h-57v182h-316v51l296 434h77v-433h98v-52h-98v-182zM351 234v376l-257 -376h257z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="582" 
d="M299 -12q-149 0 -225 109l38 38q67 -95 187 -95q72 0 119.5 46t47.5 115q0 75 -46.5 119t-119.5 44q-91 0 -162 -65l-43 18v350h385v-52h-328v-261q64 62 159 62q89 0 151 -56.5t62 -156.5q0 -97 -65 -156t-160 -59z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="584" 
d="M303 -12q-52 0 -94 19t-70 51t-46.5 76.5t-26.5 93.5t-8 105q0 53 8.5 101.5t28.5 93.5t48.5 77.5t72.5 52t97 19.5q112 0 180 -85l-34 -42q-55 75 -146 75q-52 0 -91.5 -25.5t-61.5 -68.5t-33 -93t-11 -106q0 -21 1 -31q25 41 79 76t116 35q95 0 155 -54.5t60 -156.5
q0 -87 -61 -150t-163 -63zM301 40q79 0 123.5 49.5t44.5 110.5q0 79 -46 120t-121 41q-54 0 -103.5 -31.5t-78.5 -82.5q4 -51 22 -95t59 -78t100 -34z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="502" 
d="M185 0h-63l280 615h-369v52h437v-40z" />
    <glyph glyph-name="eight" unicode="8" 
d="M286 -12q-98 0 -162.5 49.5t-64.5 129.5q0 65 48 111.5t118 65.5q-66 18 -110.5 58.5t-44.5 102.5q0 83 64 127.5t152 44.5q86 0 151 -44.5t65 -127.5q0 -62 -44.5 -102.5t-110.5 -58.5q70 -19 117.5 -65t47.5 -112q0 -80 -64.5 -129.5t-161.5 -49.5zM286 368
q22 4 43 11.5t50 22t47 40t18 57.5q0 58 -45 92t-113 34t-113 -34t-45 -92q0 -32 18 -57.5t47 -40.5t50 -22t43 -11zM286 40q68 0 118.5 36.5t50.5 94.5q0 36 -20 65t-50 45.5t-55 25.5t-44 11q-19 -2 -44.5 -11t-55 -25.5t-49.5 -45.5t-20 -65q0 -59 49.5 -95t119.5 -36z
" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="584" 
d="M271 -11q-113 0 -179 85l33 41q55 -74 146 -74q53 0 92.5 25.5t61.5 69t32.5 93t10.5 105.5v31q-26 -41 -80.5 -76.5t-115.5 -35.5q-95 0 -155 55t-60 157q0 87 61 150t163 63q52 0 94 -19t70 -51t46.5 -76.5t26.5 -93.5t8 -105q0 -53 -8.5 -101.5t-28.5 -93.5
t-48.5 -77.5t-72.5 -52t-97 -19.5zM282 305q54 0 103.5 31.5t78.5 82.5q-4 51 -22 95t-59 78t-100 34q-79 0 -123.5 -49.5t-44.5 -110.5q0 -79 46 -120t121 -41z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="219" 
d="M109 403q-18 0 -31 13t-13 30q0 18 13 31t31 13t31.5 -13t13.5 -31q0 -17 -13.5 -30t-31.5 -13zM109 -10q-18 0 -31 13.5t-13 31.5q0 17 13 30t31 13t31.5 -13t13.5 -30q0 -18 -13.5 -31.5t-31.5 -13.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="219" 
d="M109 403q-18 0 -31 13t-13 30q0 18 13 31t31 13t31.5 -13t13.5 -31q0 -17 -13.5 -30t-31.5 -13zM159 14q0 -40 -20.5 -77t-51.5 -59l-29 25q24 16 40 42.5t18 49.5q-7 -1 -10 -1q-18 0 -29.5 12t-11.5 30q0 17 12.5 30t30.5 13q21 0 36 -17.5t15 -47.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="496" 
d="M467 90l-438 226v37l438 227v-49l-386 -197l386 -195v-49z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="496" 
d="M467 411h-438v42h438v-42zM467 216h-438v41h438v-41z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="496" 
d="M467 316l-438 -226v49l386 195l-386 197v49l438 -227v-37z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="464" 
d="M214 176q-41 34 -41 79q0 36 20 65t48.5 49t57.5 39.5t49 47t20 61.5q0 46 -35.5 77t-98.5 31q-109 0 -176 -90l-36 37q77 105 215 105q87 0 139 -44.5t52 -109.5q0 -42 -21 -76t-51 -56t-60 -41.5t-51 -44t-21 -53.5t30 -51zM223 -10q-18 0 -31 13.5t-13 31.5
q0 17 13 30t31 13t31 -13t13 -30q0 -18 -13 -31.5t-31 -13.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="783" 
d="M352 -70q-136 0 -226.5 90.5t-90.5 223.5q0 106 55.5 197.5t147 144t194.5 52.5q139 0 227 -92.5t88 -229.5q0 -108 -48 -169.5t-114 -61.5q-39 0 -63 25t-25 60l-1 6q-28 -39 -72 -65t-92 -26q-69 0 -111 43.5t-42 116.5q0 104 73.5 179t165.5 75q47 0 81 -23t49 -59
l13 65h49l-59 -282q-2 -12 -2 -19q0 -25 14.5 -40t35.5 -15q40 0 78 46.5t38 144.5q0 125 -80 207t-206 82q-145 0 -253.5 -109.5t-108.5 -251.5q0 -121 82 -202.5t207 -81.5q99 0 187 57l17 -25q-98 -63 -208 -63zM342 126q95 0 164 101l32 148q-11 32 -40 57.5t-74 25.5
q-79 0 -136 -64.5t-57 -145.5q0 -54 29.5 -88t81.5 -34z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="614" 
d="M363 0h-280v667h274q84 0 134 -46t50 -124q0 -60 -34.5 -101.5t-82.5 -51.5q53 -8 91.5 -56.5t38.5 -107.5q0 -83 -51 -131.5t-140 -48.5zM350 368q62 0 96.5 34.5t34.5 88.5q0 53 -34.5 88.5t-96.5 35.5h-210v-247h210zM354 52q66 0 103 35.5t37 96.5q0 55 -37 93.5
t-103 38.5h-214v-264h214z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="672" 
d="M396 -12q-144 0 -241 96.5t-97 248.5t97 248.5t241 96.5q148 0 243 -118l-48 -31q-32 44 -84 70.5t-111 26.5q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -210.5t198.5 -82.5q59 0 111 26.5t84 70.5l48 -30q-96 -119 -243 -119z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="692" 
d="M296 0h-213v667h213q151 0 244 -96.5t93 -237.5q0 -142 -92.5 -237.5t-244.5 -95.5zM296 52q128 0 202.5 80t74.5 201t-74 201.5t-203 80.5h-156v-563h156z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="564" 
d="M506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="542" 
d="M140 0h-57v667h423v-52h-366v-247h359v-52h-359v-316z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="711" 
d="M396 -13q-143 0 -240.5 97t-97.5 249t97.5 248.5t240.5 96.5q152 0 251 -114l-43 -31q-36 43 -90.5 68t-117.5 25q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -211t198.5 -83q61 0 112 24t84 57v160h-252v51h309v-233q-99 -111 -253 -111z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="703" 
d="M621 0h-58v317h-423v-317h-57v667h57v-298h423v298h58v-667z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="470" 
d="M185 -12q-108 0 -171 82l35 43q56 -73 133 -73q67 0 107 42.5t40 110.5v474h58v-474q0 -100 -57 -152.5t-145 -52.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="586" 
d="M562 0h-73l-275 314l-74 -81v-233h-57v667h57v-365l322 365h73l-284 -318z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="504" 
d="M463 0h-380v667h57v-615h323v-52z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="790" 
d="M707 0h-58v589l-243 -589h-22l-244 589v-589h-57v667h85l227 -550l226 550h86v-667z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="699" 
d="M616 0h-56l-420 573v-573h-57v667h58l418 -565v565h57v-667z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="P" unicode="P" 
d="M140 0h-57v667h250q93 0 147 -56.5t54 -138.5t-54.5 -138.5t-146.5 -56.5h-193v-277zM327 329q66 0 106.5 40t40.5 103t-40.5 103t-106.5 40h-187v-286h187z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247q0 -153 -95 -251l70 -73l-39 -36l-71 73q-80 -58 -189 -58zM382 40q86 0 151 46l-103 107l40 37l103 -108q73 84 73 211q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211
q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="594" 
d="M543 0h-69l-187 276h-147v-276h-57v667h249q88 0 145.5 -53t57.5 -142q0 -87 -53.5 -138t-131.5 -54zM327 328q66 0 107 40.5t41 103.5t-41 103t-107 40h-187v-287h187z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="581" 
d="M290 -12q-157 0 -244 106l37 43q83 -97 208 -97q93 0 134.5 40.5t41.5 92.5q0 40 -22 67.5t-57.5 42.5t-78 27t-85 25.5t-78 32t-57.5 52t-22 80.5q0 80 63 129t156 49q143 0 226 -94l-38 -42q-70 84 -191 84q-67 0 -111.5 -34.5t-44.5 -88.5q0 -34 22 -58.5t57.5 -38
t78 -25t85 -26t78 -34.5t57.5 -56.5t22 -87.5q0 -76 -57 -132.5t-180 -56.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="564" 
d="M311 0h-58v615h-218v52h494v-52h-218v-615z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="686" 
d="M343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-411q0 -126 -66.5 -197t-193.5 -71z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="647" 
d="M359 0h-71l-273 667h66l242 -603l243 603h66z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="867" 
d="M658 0h-64l-160 573l-160 -573h-64l-191 667h64l161 -587l164 587h52l163 -587l161 587h64z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="645" 
d="M626 0h-71l-233 301l-232 -301h-72l269 342l-254 325h73l216 -283l217 283h72l-253 -324z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="618" 
d="M338 0h-58v285l-265 382h69l225 -328l225 328h69l-265 -382v-285z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="581" 
d="M531 0h-481v50l402 565h-402v52h474v-49l-402 -566h409v-52z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="228" 
d="M211 -190h-174v868h174v-43h-130v-782h130v-43z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="282" 
d="M237 -20l-237 707h45l237 -707h-45z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="228" 
d="M191 -190h-174v43h130v782h-130v43h174v-868z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="428" 
d="M409 333h-48l-147 293l-147 -293h-48l174 334h42z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="564" 
d="M567 -83h-570v43h570v-43z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="216" 
d="M216 556h-44l-172 144h62z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5z" />
    <glyph glyph-name="b" unicode="b" 
d="M133 121q22 -36 66 -61t93 -25q79 0 124 58t45 148q0 91 -45 149t-124 58q-48 0 -92.5 -26t-66.5 -62v-239zM133 0h-52v667h52v-262q64 90 168 90q97 0 156.5 -69.5t59.5 -184.5t-59.5 -184t-156.5 -69q-52 0 -96.5 25t-71.5 64v-77z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="494" 
d="M288 -12q-105 0 -170 73t-65 181t65 180.5t170 72.5q106 0 171 -82l-36 -32q-49 67 -132 67t-132.5 -58.5t-49.5 -147.5t49.5 -148t132.5 -59q82 0 132 68l36 -32q-65 -83 -171 -83z" />
    <glyph glyph-name="d" unicode="d" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v262h53v-667zM278 35q49 0 93 25t66 61v239q-22 37 -66 62.5t-93 25.5q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="567" 
d="M295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="265" 
d="M150 0h-52v437h-80v46h80v44q0 70 35 110t96 40q53 0 92 -34l-25 -36q-27 25 -62 25q-84 0 -84 -105v-44h98v-46h-98v-437z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="570" 
d="M272 -196q-63 0 -106 15.5t-84 57.5l30 40q31 -37 68 -52.5t92 -15.5q72 0 118.5 38.5t46.5 116.5v76q-27 -39 -71.5 -64.5t-96.5 -25.5q-97 0 -156.5 68.5t-59.5 183.5q0 114 60 183.5t156 69.5q104 0 168 -90v78h53v-478q0 -104 -60.5 -152.5t-157.5 -48.5zM278 37
q49 0 93 25.5t66 62.5v235q-22 37 -66 62.5t-93 25.5q-78 0 -123.5 -58t-45.5 -148t45.5 -147.5t123.5 -57.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="540" 
d="M459 0h-52v329q0 66 -30 92.5t-87 26.5q-45 0 -89 -24t-68 -59v-365h-52v667h52v-257q29 34 77 59.5t97 25.5q152 0 152 -155v-340z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="214" 
d="M107 559q-16 0 -28 12t-12 28t12 27.5t28 11.5q17 0 28.5 -11.5t11.5 -27.5q0 -17 -11.5 -28.5t-28.5 -11.5zM133 0h-52v483h52v-483z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="214" 
d="M107 559q-16 0 -28 12t-12 28t12 27.5t28 11.5q17 0 28.5 -11.5t11.5 -27.5q0 -17 -11.5 -28.5t-28.5 -11.5zM2 -196q-56 0 -99 37l23 41q31 -33 71 -33q38 0 61 22t23 67v545h52v-545q0 -66 -34 -100t-97 -34z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="505" 
d="M486 0h-70l-193 230l-90 -85v-145h-52v667h52v-460l285 276h69l-225 -218z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="214" 
d="M133 0h-52v667h52v-667z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="789" 
d="M708 0h-52v338q0 110 -97 110q-38 0 -77.5 -24t-60.5 -58v-366h-53v338q0 110 -97 110q-37 0 -76 -24.5t-62 -58.5v-365h-52v483h52v-73q18 29 63.5 57t92.5 28q53 0 85 -26t42 -65q22 36 67 63.5t93 27.5q132 0 132 -147v-348z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="540" 
d="M459 0h-52v327q0 66 -30 93.5t-87 27.5q-45 0 -89 -24t-68 -59v-365h-52v483h52v-73q29 34 77 59.5t97 25.5q152 0 152 -157v-338z" />
    <glyph glyph-name="o" unicode="o" 
d="M285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="567" 
d="M301 -12q-104 0 -168 90v-262h-52v667h52v-76q27 39 71.5 63.5t96.5 24.5q97 0 156.5 -68.5t59.5 -184.5q0 -115 -59.5 -184.5t-156.5 -69.5zM292 35q79 0 124 58t45 149q0 90 -45 148t-124 58q-49 0 -93 -25t-66 -61v-239q22 -36 66.5 -62t92.5 -26z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="567" 
d="M275 35q48 0 92.5 26t66.5 62v239q-22 36 -66 61t-93 25q-79 0 -124 -58t-45 -148q0 -91 45 -149t124 -58zM265 -12q-96 0 -155.5 69.5t-59.5 184.5t59 184t156 69q53 0 97.5 -24.5t71.5 -63.5v76h52v-667h-52v262q-64 -90 -169 -90z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="317" 
d="M133 0h-52v483h52v-83q71 93 163 93v-57q-9 2 -26 2q-36 0 -78 -27t-59 -59v-352z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="459" 
d="M225 -12q-117 0 -186 76l31 39q25 -31 66 -51t90 -20q59 0 94 27.5t35 70.5q0 36 -31.5 57.5t-76 32t-89.5 23t-76.5 41.5t-31.5 77q0 57 46.5 95.5t125.5 38.5q109 0 173 -71l-29 -37q-47 64 -144 64q-55 0 -88 -25t-33 -63q0 -33 31.5 -52t76 -29.5t89.5 -23.5
t76.5 -45t31.5 -83q0 -62 -47 -102t-134 -40z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="275" 
d="M186 -12q-95 0 -95 105v344h-80v46h80v132h53v-132h98v-46h-98v-336q0 -66 50 -66q33 0 54 23l20 -40q-32 -30 -82 -30z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="540" 
d="M459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-483z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="481" 
d="M270 0h-59l-204 483h58l176 -422l174 422h59z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="719" 
d="M545 0h-50l-135 413l-136 -413h-50l-157 483h56l128 -410l136 410h45l136 -410l128 410h56z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="479" 
d="M462 0h-62l-160 213l-161 -213h-62l191 248l-180 235h62l150 -199l149 199h62l-180 -235z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="481" 
d="M49 -189l9 48q18 -8 42 -8q49 0 73 59l38 86l-204 487h58l176 -422l174 422h59l-250 -589q-38 -90 -124 -90q-28 0 -51 7z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="469" 
d="M413 0h-357v43l286 394h-286v46h353v-42l-289 -395h293v-46z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="249" 
d="M232 -190h-62q-44 0 -78 34.5t-34 88.5v220q0 30 -13.5 50.5t-38.5 20.5v40q25 0 38.5 20.5t13.5 50.5v219q0 54 34 89t78 35h62v-43h-62q-28 0 -47.5 -23t-19.5 -58v-222q0 -68 -46 -88q46 -20 46 -88v-222q0 -35 19.5 -58t47.5 -23h62v-43z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="208" 
d="M126 -20h-43v707h43v-707z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="249" 
d="M17 -190v43h62q28 0 47.5 23t19.5 58v222q0 68 46 88q-46 20 -46 88v222q0 35 -19.5 58t-47.5 23h-62v43h62q44 0 78 -35t34 -89v-219q0 -30 13.5 -50.5t38.5 -20.5v-40q-25 0 -38.5 -20.5t-13.5 -50.5v-220q0 -54 -34 -88.5t-78 -34.5h-62z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="499" 
d="M429 667l43 -5q-5 -50 -13 -88t-23 -73.5t-39.5 -54t-58.5 -18.5t-57 20t-34.5 49t-21.5 58t-25.5 49t-38.5 20q-72 0 -90 -197l-44 6q11 106 41 169t93 63q34 0 57 -20.5t34.5 -49.5t21.5 -57.5t25.5 -49t38.5 -20.5q72 0 91 199z" />
    <glyph glyph-name="nbspace" unicode="&#xa0;" horiz-adv-x="259" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="219" 
d="M76 -184l13 493h41l14 -493h-68zM66 449q0 17 12.5 30t30.5 13t31.5 -13t13.5 -30q0 -18 -13.5 -31t-31.5 -13t-30.5 13t-12.5 31z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="494" 
d="M257 -100v90q-92 11 -148 81.5t-56 170.5q0 99 56 169.5t148 81.5v72h45v-70q95 -5 157 -82l-36 -32q-47 62 -121 67v-413q73 3 121 68l36 -32q-61 -78 -157 -83v-88h-45zM109 242q0 -79 39.5 -135.5t108.5 -68.5v407q-69 -13 -108.5 -68.5t-39.5 -134.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="505" 
d="M18 267v42h125q-3 4 -20.5 22.5t-22.5 25t-18 24.5t-18 31t-9.5 32.5t-4.5 40.5q0 79 62 135.5t154 56.5q62 0 112.5 -29t75.5 -79l-46 -29q-15 35 -53 61t-85 26q-67 0 -113 -38.5t-46 -104.5q0 -29 10.5 -56.5t22 -42.5t32.5 -39.5t30 -36.5h151v-42h-126
q10 -27 10 -53q0 -81 -81 -140q25 9 54 9q34 0 79.5 -22t74.5 -22q32 0 60 13t40 28l27 -47q-49 -47 -129 -47q-47 0 -93 23.5t-86 23.5q-42 0 -118 -40l-22 48q69 29 105.5 72.5t36.5 92.5q0 29 -16 61h-155z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="588" 
d="M293 96q-88 0 -151 53l-78 -78l-34 33l79 79q-47 64 -47 150q0 88 48 150l-81 82l34 33l80 -80q61 51 150 51t151 -51l79 79l34 -33l-80 -80q48 -63 48 -151t-48 -150l80 -79l-34 -33l-78 77q-61 -52 -152 -52zM293 145q82 0 129.5 55t47.5 133q0 77 -47.5 132t-129.5 55
q-81 0 -129 -55t-48 -132q0 -78 48 -133t129 -55z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="618" 
d="M338 0h-58v126h-258v41h258v118h-258v42h229l-236 340h69l225 -328l225 328h69l-236 -340h230v-42h-259v-118h259v-41h-259v-126z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="208" 
d="M126 -20h-43v316h43v-316zM126 371h-43v316h43v-316z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="461" 
d="M406 311q0 -87 -96 -128q96 -36 96 -121q0 -67 -49.5 -105t-131.5 -38q-113 0 -186 77l31 36q58 -72 155 -72q57 0 93.5 27t36.5 73q0 37 -31.5 59t-76 32.5t-89.5 23.5t-76.5 42t-31.5 77q0 51 34 83.5t89 45.5q-60 18 -91.5 46t-31.5 76q0 55 46 93.5t126 38.5
q112 0 172 -72l-28 -33q-49 64 -143 64q-54 0 -88 -25.5t-34 -66.5q0 -34 31.5 -53t76 -29t89.5 -22.5t76.5 -44.5t31.5 -84zM354 307q0 23 -10.5 40t-31 28.5t-38.5 18t-45 13.5q-71 -19 -99.5 -45.5t-28.5 -64.5q0 -22 11.5 -38t33 -26t46 -17.5t55.5 -14.5q3 0 5 -0.5
t4.5 -1t4.5 -1.5q93 40 93 109z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="262" 
d="M270 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM66 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="778" 
d="M734 334q0 -143 -101 -244t-244 -101t-244 101.5t-101 243.5q0 143 101 244t244 101t244 -101t101 -244zM706 334q0 131 -93 224t-224 93t-224 -93t-93 -224q0 -130 93 -223.5t224 -93.5t224 93t93 224zM513 208l22 -22q-57 -66 -145 -66q-87 0 -147.5 62.5t-60.5 153.5
t60.5 152t147.5 61q90 0 145 -65l-23 -22q-19 26 -52.5 41.5t-69.5 15.5q-71 0 -123 -51.5t-52 -132.5q0 -79 52 -132t123 -53q36 0 70 16t53 42z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="375" 
d="M310 326h-42v37q-42 -45 -110 -45q-45 0 -78.5 28.5t-33.5 76.5t33.5 75.5t78.5 27.5q68 0 110 -44v61q0 33 -25 51.5t-60 18.5q-62 0 -100 -48l-22 28q51 55 127 55q54 0 88 -26t34 -79v-217zM174 348q61 0 94 42v65q-32 41 -94 41q-37 0 -61 -20.5t-24 -52.5
q0 -33 24 -54t61 -21z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="402" 
d="M372 63h-57l-160 180l160 177h57l-160 -177zM247 63h-57l-160 180l160 177h57l-160 -177z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="499" 
d="M467 453v-237h-43v195h-395v42h438z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="300" 
d="M270 218h-240v48h240v-48z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="494" 
d="M459 465q0 -88 -62 -150t-150 -62t-150 62t-62 150t62 150t150 62q89 0 150.5 -61.5t61.5 -150.5zM432 465q0 77 -54 131t-131 54t-131 -54t-54 -131q0 -76 54.5 -130.5t130.5 -54.5t130.5 54.5t54.5 130.5zM344 343h-38l-63 96h-46v-96h-30v243h99q32 0 53.5 -20.5
t21.5 -53.5q0 -35 -22.5 -53.5t-39.5 -18.5zM310 512q0 19 -13.5 32.5t-30.5 13.5h-69v-90h69q16 0 30 13t14 31z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="363" 
d="M363 577h-363v37h363v-37z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="282" 
d="M257 560q0 -48 -34 -81.5t-83 -33.5q-48 0 -81.5 33.5t-33.5 81.5t33.5 82.5t81.5 34.5q49 0 83 -34.5t34 -82.5zM218 560q0 32 -23 55t-55 23q-31 0 -53.5 -23t-22.5 -55t22 -54t54 -22t55 22.5t23 53.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="496" 
d="M467 323h-196v-218h-45v218h-197v41h197v212h45v-212h196v-41zM467 0h-438v41h438v-41z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="384" 
d="M333 421h-281v35q122 87 178.5 144.5t56.5 109.5q0 40 -27 59.5t-66 19.5q-75 0 -113 -52l-27 28q48 62 141 62q55 0 95.5 -29t40.5 -86q0 -60 -54.5 -119t-158.5 -134h215v-38z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="384" 
d="M334 529q0 -51 -39 -83t-105 -32q-49 0 -87 18t-57 47l26 28q42 -55 117 -55q47 0 73.5 20.5t26.5 57.5q0 40 -30.5 59t-79.5 19q-32 0 -38 -1v39q7 -1 38 -1q46 0 75 17.5t29 54.5q0 34 -27.5 53t-68.5 19q-65 0 -111 -50l-25 27q52 61 139 61q60 0 98.5 -28.5
t38.5 -77.5q0 -41 -27.5 -64.5t-61.5 -28.5q34 -3 65 -28.5t31 -70.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="216" 
d="M216 700l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="554" 
d="M133 44v-228h-52v667h52v-328q0 -122 118 -122q44 0 87.5 24t68.5 58v368h52v-384q0 -64 49 -64q12 0 20 2l4 -46q-12 -3 -31 -3q-81 0 -93 86q-29 -37 -72.5 -61.5t-88.5 -24.5q-84 0 -114 56z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="449" 
d="M368 -100h-38v730h-93v-730h-38v423q-71 0 -121.5 50.5t-50.5 121.5t50.5 121.5t121.5 50.5h169v-767z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="219" 
d="M154 245q0 -18 -13.5 -31t-31.5 -13t-31 13t-13 31q0 17 13 30t31 13t31.5 -13t13.5 -30z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="198" 
d="M97 -189q-63 0 -97 29l16 32q35 -28 80 -28q28 0 46.5 12t18.5 33q0 39 -41 39q-25 0 -40 -17l-28 16l31 84h38l-26 -68q15 12 37 12q29 0 47.5 -18t18.5 -48q0 -35 -29 -56.5t-72 -21.5z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="214" 
d="M154 421h-46v341l-64 -69l-27 29l97 99h40v-400z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="405" 
d="M202 318q-70 0 -114 47.5t-44 117.5t44 117.5t114 47.5q71 0 115 -47t44 -118q0 -70 -44 -117.5t-115 -47.5zM202 355q53 0 84 36.5t31 91.5q0 54 -31 90.5t-84 36.5q-52 0 -82.5 -36t-30.5 -91t30.5 -91.5t82.5 -36.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="402" 
d="M247 243l-160 -180h-57l160 180l-160 177h57zM372 243l-160 -180h-57l160 180l-160 177h57z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="742" 
d="M154 267h-46v341l-64 -69l-27 29l97 99h40v-400zM701 107h-60v-107h-44v107h-196v35l179 258h61v-256h60v-37zM597 144v215l-150 -215h150zM568 667l-427 -667h-45l426 667h46z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="781" 
d="M154 267h-46v341l-64 -69l-27 29l97 99h40v-400zM730 0h-281v35q122 87 178.5 144.5t56.5 109.5q0 40 -27 59.5t-66 19.5q-75 0 -113 -52l-27 28q48 62 141 62q55 0 95.5 -29t40.5 -86q0 -60 -54.5 -119t-158.5 -134h215v-38zM568 667l-427 -667h-45l426 667h46z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="859" 
d="M818 107h-60v-107h-44v107h-196v35l179 258h61v-256h60v-37zM714 144v215l-150 -215h150zM685 667l-427 -667h-45l426 667h46zM334 375q0 -51 -39 -83t-105 -32q-49 0 -87 18t-57 47l26 28q42 -55 117 -55q47 0 73.5 20.5t26.5 57.5q0 40 -30.5 59t-79.5 19q-32 0 -38 -1
v39q7 -1 38 -1q46 0 75 17.5t29 54.5q0 34 -27.5 53t-68.5 19q-65 0 -111 -50l-25 27q52 61 139 61q60 0 98.5 -28.5t38.5 -77.5q0 -41 -27.5 -64.5t-61.5 -28.5q34 -3 65 -28.5t31 -70.5z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="393" 
d="M250 304q42 -34 42 -79q0 -36 -20 -65t-49 -49t-58 -39.5t-49 -47t-20 -61.5q0 -46 36 -77t99 -31q61 0 101.5 23t73.5 67l36 -37q-77 -105 -215 -105q-87 0 -139 44.5t-52 109.5q0 42 21 76t51 56t60 42t51 44.5t21 53.5t-30 50zM241 492q18 0 31.5 -13t13.5 -31
q0 -17 -13.5 -30t-31.5 -13t-31 13t-13 30q0 18 13 31t31 13z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM378 723h-44l-172 144h62z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM486 867l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="647" 
d="M450 723h-40l-86 113l-85 -113h-39l97 144h54zM632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM385 721q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36
q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="647" 
d="M463 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM259 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM325 691q-40 0 -68 28t-28 68q0 39 28 67.5t68 28.5q39 0 66.5 -28.5t27.5 -67.5q0 -40 -27.5 -68t-66.5 -28zM325 723q26 0 44.5 19t18.5 45t-18.5 45t-44.5 19t-45 -19t-19 -45
t19 -45t45 -19z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="939" 
d="M879 0h-423v164h-274l-102 -164h-66l421 667h444v-52h-365v-247h358v-52h-358v-264h365v-52zM456 216v390l-245 -390h245z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="672" 
d="M390 -185q-63 0 -97 29l16 32q35 -28 80 -28q28 0 46.5 12t18.5 33q0 39 -41 39q-25 0 -40 -17l-28 16l21 58q-133 11 -220.5 106t-87.5 238q0 152 97 248.5t241 96.5q148 0 243 -118l-48 -31q-32 44 -84 70.5t-111 26.5q-119 0 -198.5 -82t-79.5 -211
q0 -128 79.5 -210.5t198.5 -82.5q59 0 111 26.5t84 70.5l48 -30q-94 -116 -236 -119l-15 -41q15 12 37 12q29 0 47.5 -18t18.5 -48q0 -35 -29 -56.5t-72 -21.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="564" 
d="M506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52zM349 723h-44l-172 144h62z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="564" 
d="M456 867l-172 -144h-44l154 144h62zM506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="564" 
d="M418 723h-40l-86 113l-85 -113h-39l97 144h54zM506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="564" 
d="M432 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM228 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667zM166 723h-44l-172 144h62z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667zM274 867l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="223" 
d="M238 723h-40l-86 113l-85 -113h-39l97 144h54zM140 0h-57v667h57v-667z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="223" 
d="M251 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM47 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM140 0h-57v667h57v-667z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="721" 
d="M326 0h-213v309h-102v44h102v314h213q151 0 244 -96.5t93 -237.5q0 -142 -92.5 -237.5t-244.5 -95.5zM326 52q128 0 202.5 80t74.5 201t-74 201.5t-203 80.5h-156v-262h179v-44h-179v-257h156z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="699" 
d="M410 721q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5zM616 0h-56l-420 573v-573h-57v667h58l418 -565v565h57v-667z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="764" 
d="M437 723h-44l-172 144h62zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="764" 
d="M544 867l-172 -144h-44l154 144h62zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="764" 
d="M509 723h-40l-86 113l-85 -113h-39l97 144h54zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z
" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5zM444 721q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5
t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="764" 
d="M520 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM316 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40
q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="496" 
d="M400 153l-152 152l-151 -152l-30 29l152 152l-152 152l30 29l151 -152l152 152l29 -29l-152 -152l152 -152z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="764" 
d="M382 -12q-93 0 -168 44l-20 -32h-51l36 56q-58 47 -89.5 119t-31.5 158q0 149 89.5 247t234.5 98q89 0 164 -43l20 32h52l-36 -56q59 -47 91.5 -119.5t32.5 -158.5q0 -149 -90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 149 -93 231l-310 -487q60 -37 139 -37z
M118 333q0 -147 91 -230l308 487q-59 36 -135 36q-120 0 -192 -82t-72 -211z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="686" 
d="M343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-411q0 -126 -66.5 -197t-193.5 -71zM398 723h-44l-172 144h62z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="686" 
d="M505 867l-172 -144h-44l154 144h62zM343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-411q0 -126 -66.5 -197t-193.5 -71z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="686" 
d="M471 723h-40l-86 113l-85 -113h-39l97 144h54zM343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-411q0 -126 -66.5 -197t-193.5 -71z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="686" 
d="M483 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM279 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-411
q0 -126 -66.5 -197t-193.5 -71z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="618" 
d="M338 0h-58v285l-265 382h69l225 -328l225 328h69l-265 -382v-285zM471 867l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" 
d="M140 0h-57v667h57v-124h193q93 0 147 -57t54 -139t-54.5 -138t-146.5 -56h-193v-153zM327 205q66 0 106.5 39.5t40.5 102.5t-41 103.5t-106 40.5h-187v-286h187z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="578" 
d="M557 131q0 -60 -46 -101.5t-134 -41.5q-65 0 -105.5 20t-75.5 57l31 38q54 -71 150 -71q62 0 95.5 28.5t33.5 69.5q0 36 -31 57.5t-75 32.5t-87.5 23.5t-74.5 41.5t-31 77q0 37 24.5 66t53.5 44t53.5 37t24.5 46q0 34 -33.5 54.5t-74.5 20.5q-51 0 -86.5 -33t-35.5 -88
v-509h-52v509q0 72 48 120t126 48q66 0 113.5 -32.5t47.5 -85.5q0 -30 -16.5 -53t-39.5 -38t-46 -29t-39.5 -33.5t-16.5 -44.5q0 -33 31 -51.5t75 -28.5t87.5 -22.5t74.5 -44.5t31 -83z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM318 556h-44l-172 144h62z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM426 700l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM390 556h-40l-86 113l-85 -113h-39l97 144h54z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM325 554q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM401 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM197 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM263 548q-40 0 -68 28t-28 68q0 39 28 67.5t68 28.5q39 0 66.5 -28.5t27.5 -67.5q0 -40 -27.5 -68t-66.5 -28zM263 580q26 0 44.5 19t18.5 45t-18.5 45t-44.5 19t-45 -19t-19 -45t19 -45t45 -19z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="897" 
d="M844 225h-379q4 -82 51 -137t123 -55q89 0 148 67l27 -34q-72 -78 -175 -78q-67 0 -118 33t-78 96q-23 -59 -75.5 -94t-119.5 -35q-84 0 -138.5 43t-54.5 118t52 117.5t127 42.5q111 0 179 -67v96q0 53 -38.5 82t-102.5 29q-97 0 -165 -71l-29 36q75 81 202 81
q63 0 111.5 -27.5t58.5 -87.5q26 51 72.5 83t107.5 32q101 0 157.5 -73t56.5 -183v-14zM465 266h326q0 71 -42 127.5t-121 56.5q-73 0 -116.5 -57t-46.5 -127zM413 184v16q-57 68 -159 68q-65 0 -104.5 -33t-39.5 -84q0 -52 39.5 -85t104.5 -33q69 0 114 45.5t45 105.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="494" 
d="M289 -187q-63 0 -97 29l16 32q35 -28 80 -28q28 0 46.5 12t18.5 33q0 39 -41 39q-25 0 -40 -17l-28 16l22 60q-96 8 -154.5 79.5t-58.5 173.5q0 108 65 180.5t170 72.5q106 0 171 -82l-36 -32q-49 67 -132 67t-132.5 -58.5t-49.5 -147.5t49.5 -148t132.5 -59q82 0 132 68
l36 -32q-61 -77 -156 -82l-16 -44q15 12 37 12q29 0 47.5 -18t18.5 -48q0 -35 -29 -56.5t-72 -21.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="567" 
d="M295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351zM341 556h-44l-172 144h62z
" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="567" 
d="M448 700l-172 -144h-44l154 144h62zM295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5
t-49.5 -127.5h351z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="567" 
d="M413 556h-40l-86 113l-85 -113h-39l97 144h54zM295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5
t-49.5 -127.5h351z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="567" 
d="M426 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM222 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137
t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="214" 
d="M133 0h-52v483h52v-483zM161 556h-44l-172 144h62z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="214" 
d="M269 700l-172 -144h-44l154 144h62zM133 0h-52v483h52v-483z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="214" 
d="M232 556h-40l-86 113l-85 -113h-39l97 144h54zM133 0h-52v483h52v-483z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="214" 
d="M246 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM42 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM133 0h-52v483h52v-483z" />
    <glyph glyph-name="eth" unicode="&#xf0;" 
d="M130 510l-14 34l121 53q-19 13 -81 53l30 44q62 -38 108 -72l109 48l14 -33l-91 -40q192 -162 192 -353q0 -111 -64 -183.5t-169 -72.5q-103 0 -167.5 71t-64.5 179q0 107 62 178t161 71q96 0 157 -87q-58 95 -161 173zM285 35q82 0 129.5 59.5t47.5 143.5
q0 83 -47.5 142.5t-129.5 59.5q-81 0 -128.5 -59.5t-47.5 -142.5q0 -84 47.5 -143.5t128.5 -59.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="540" 
d="M459 0h-52v327q0 66 -30 93.5t-87 27.5q-45 0 -89 -24t-68 -59v-365h-52v483h52v-73q29 34 77 59.5t97 25.5q152 0 152 -157v-338zM332 554q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35
t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" 
d="M285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61zM340 556h-44l-172 144h62z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" 
d="M448 700l-172 -144h-44l154 144h62zM285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" 
d="M412 556h-40l-86 113l-85 -113h-39l97 144h54zM285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146
t128.5 -61z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" 
d="M285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61zM347 554q-25 0 -43.5 16t-27.5 35.5
t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" 
d="M425 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM221 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35
q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="511" 
d="M295 531q0 -16 -11.5 -28t-27.5 -12t-28 12t-12 28t11.5 27.5t28.5 11.5q16 0 27.5 -11.5t11.5 -27.5zM482 316h-453v42h453v-42zM295 138q0 -16 -11.5 -27.5t-27.5 -11.5t-28 11.5t-12 27.5t12 28t28 12t27.5 -12t11.5 -28z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" 
d="M118 0h-44l45 59q-66 72 -66 183q0 108 64 180.5t168 72.5q78 0 135 -43l24 31h44l-41 -53q71 -73 71 -188q0 -108 -64 -181t-169 -73q-81 0 -141 47zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -48 145l-238 -311q44 -41 109 -41zM109 242q0 -82 43 -140l237 310
q-44 36 -104 36q-81 0 -128.5 -60.5t-47.5 -145.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="540" 
d="M459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-483zM324 556h-44l-172 144h62z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="540" 
d="M432 700l-172 -144h-44l154 144h62zM459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-483z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="540" 
d="M395 556h-40l-86 113l-85 -113h-39l97 144h54zM459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-483z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="540" 
d="M411 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM207 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5
q45 0 88.5 23t68.5 57v368h52v-483z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="481" 
d="M49 -189l9 48q18 -8 42 -8q49 0 73 59l38 86l-204 487h58l176 -422l174 422h59l-250 -589q-38 -90 -124 -90q-28 0 -51 7zM403 700l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="567" 
d="M301 -12q-104 0 -168 90v-262h-52v851h52v-260q27 39 71.5 63.5t96.5 24.5q97 0 156.5 -68.5t59.5 -184.5q0 -115 -59.5 -184.5t-156.5 -69.5zM292 35q79 0 124 58t45 149q0 90 -45 148t-124 58q-49 0 -93 -25t-66 -61v-239q22 -36 66.5 -62t92.5 -26z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="481" 
d="M49 -189l9 48q18 -8 42 -8q49 0 73 59l38 86l-204 487h58l176 -422l174 422h59l-250 -589q-38 -90 -124 -90q-28 0 -51 7zM380 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM176 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11
t11 -26z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM504 744h-363v37h363v-37z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM441 577h-363v37h363v-37z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM498 796q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM435 629q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="647" 
d="M656 -109l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 58 56 101h-6l-66 164h-353l-66 -164h-66l273 667h71l273 -667q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44zM481 216l-158 390l-157 -390h315z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="521" 
d="M464 -109l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 62 64 106v52q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340
q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86t36.5 -85.5t95.5 -33.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="672" 
d="M558 867l-172 -144h-44l154 144h62zM396 -12q-144 0 -241 96.5t-97 248.5t97 248.5t241 96.5q148 0 243 -118l-48 -31q-32 44 -84 70.5t-111 26.5q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -210.5t198.5 -82.5q59 0 111 26.5t84 70.5l48 -30q-96 -119 -243 -119z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="494" 
d="M450 700l-172 -144h-44l154 144h62zM288 -12q-105 0 -170 73t-65 181t65 180.5t170 72.5q106 0 171 -82l-36 -32q-49 67 -132 67t-132.5 -58.5t-49.5 -147.5t49.5 -148t132.5 -59q82 0 132 68l36 -32q-65 -83 -171 -83z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="672" 
d="M521 723h-40l-86 113l-85 -113h-39l97 144h54zM396 -12q-144 0 -241 96.5t-97 248.5t97 248.5t241 96.5q148 0 243 -118l-48 -31q-32 44 -84 70.5t-111 26.5q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -210.5t198.5 -82.5q59 0 111 26.5t84 70.5l48 -30
q-96 -119 -243 -119z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="494" 
d="M414 556h-40l-86 113l-85 -113h-39l97 144h54zM288 -12q-105 0 -170 73t-65 181t65 180.5t170 72.5q106 0 171 -82l-36 -32q-49 67 -132 67t-132.5 -58.5t-49.5 -147.5t49.5 -148t132.5 -59q82 0 132 68l36 -32q-65 -83 -171 -83z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="672" 
d="M396 -12q-144 0 -241 96.5t-97 248.5t97 248.5t241 96.5q148 0 243 -118l-48 -31q-32 44 -84 70.5t-111 26.5q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -210.5t198.5 -82.5q59 0 111 26.5t84 70.5l48 -30q-96 -119 -243 -119zM398 728q-16 0 -27 11t-11 27
q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="494" 
d="M288 -12q-105 0 -170 73t-65 181t65 180.5t170 72.5q106 0 171 -82l-36 -32q-49 67 -132 67t-132.5 -58.5t-49.5 -147.5t49.5 -148t132.5 -59q82 0 132 68l36 -32q-65 -83 -171 -83zM291 561q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5
q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="672" 
d="M425 723h-54l-100 144h39l88 -113l83 113h40zM396 -12q-144 0 -241 96.5t-97 248.5t97 248.5t241 96.5q148 0 243 -118l-48 -31q-32 44 -84 70.5t-111 26.5q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -210.5t198.5 -82.5q59 0 111 26.5t84 70.5l48 -30
q-96 -119 -243 -119z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="494" 
d="M318 556h-54l-100 144h39l88 -113l83 113h40zM288 -12q-105 0 -170 73t-65 181t65 180.5t170 72.5q106 0 171 -82l-36 -32q-49 67 -132 67t-132.5 -58.5t-49.5 -147.5t49.5 -148t132.5 -59q82 0 132 68l36 -32q-65 -83 -171 -83z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="692" 
d="M375 723h-54l-100 144h39l88 -113l83 113h40zM296 0h-213v667h213q151 0 244 -96.5t93 -237.5q0 -142 -92.5 -237.5t-244.5 -95.5zM296 52q128 0 202.5 80t74.5 201t-74 201.5t-203 80.5h-156v-563h156z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="625" 
d="M629 620q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v262
h53v-667zM278 35q49 0 93 25t66 61v239q-22 37 -66 62.5t-93 25.5q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="721" 
d="M326 0h-213v309h-102v44h102v314h213q151 0 244 -96.5t93 -237.5q0 -142 -92.5 -237.5t-244.5 -95.5zM326 52q128 0 202.5 80t74.5 201t-74 201.5t-203 80.5h-156v-262h179v-44h-179v-257h156z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="572" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v133h-161v37h161v92h53v-92h72v-37h-72v-538zM278 35q49 0 93 25t66 61v239q-22 37 -66 62.5t-93 25.5q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z
" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="564" 
d="M506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52zM474 744h-363v37h363v-37z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="567" 
d="M295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351zM467 577h-363v37h363v-37z
" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="564" 
d="M506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52zM470 796q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="567" 
d="M295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351zM464 629q-65 -88 -175 -88
q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="564" 
d="M506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52zM300 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="567" 
d="M295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351zM288 561q-16 0 -27 11
t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="564" 
d="M506 0q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 58 56 101h-363v667h423v-52h-366v-247h359v-52h-359v-264h366v-52z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="567" 
d="M295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-48 -49 -105 -66q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 49 42 89
h-10zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="564" 
d="M325 723h-54l-100 144h39l88 -113l83 113h40zM506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="567" 
d="M316 556h-54l-100 144h39l88 -113l83 113h40zM295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5
t-49.5 -127.5h351z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="711" 
d="M526 723h-40l-86 113l-85 -113h-39l97 144h54zM396 -13q-143 0 -240.5 97t-97.5 249t97.5 248.5t240.5 96.5q152 0 251 -114l-43 -31q-36 43 -90.5 68t-117.5 25q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -211t198.5 -83q61 0 112 24t84 57v160h-252v51h309v-233
q-99 -111 -253 -111z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="570" 
d="M413 556h-40l-86 113l-85 -113h-39l97 144h54zM272 -196q-63 0 -106 15.5t-84 57.5l30 40q31 -37 68 -52.5t92 -15.5q72 0 118.5 38.5t46.5 116.5v76q-27 -39 -71.5 -64.5t-96.5 -25.5q-97 0 -156.5 68.5t-59.5 183.5q0 114 60 183.5t156 69.5q104 0 168 -90v78h53v-478
q0 -104 -60.5 -152.5t-157.5 -48.5zM278 37q49 0 93 25.5t66 62.5v235q-22 37 -66 62.5t-93 25.5q-78 0 -123.5 -58t-45.5 -148t45.5 -147.5t123.5 -57.5z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="711" 
d="M396 -13q-143 0 -240.5 97t-97.5 249t97.5 248.5t240.5 96.5q152 0 251 -114l-43 -31q-36 43 -90.5 68t-117.5 25q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -211t198.5 -83q61 0 112 24t84 57v160h-252v51h309v-233q-99 -111 -253 -111zM574 796q-65 -88 -175 -88
q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="570" 
d="M272 -196q-63 0 -106 15.5t-84 57.5l30 40q31 -37 68 -52.5t92 -15.5q72 0 118.5 38.5t46.5 116.5v76q-27 -39 -71.5 -64.5t-96.5 -25.5q-97 0 -156.5 68.5t-59.5 183.5q0 114 60 183.5t156 69.5q104 0 168 -90v78h53v-478q0 -104 -60.5 -152.5t-157.5 -48.5zM278 37
q49 0 93 25.5t66 62.5v235q-22 37 -66 62.5t-93 25.5q-78 0 -123.5 -58t-45.5 -148t45.5 -147.5t123.5 -57.5zM461 629q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="711" 
d="M396 -13q-143 0 -240.5 97t-97.5 249t97.5 248.5t240.5 96.5q152 0 251 -114l-43 -31q-36 43 -90.5 68t-117.5 25q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -211t198.5 -83q61 0 112 24t84 57v160h-252v51h309v-233q-99 -111 -253 -111zM399 728q-16 0 -27 11t-11 27
q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="570" 
d="M272 -196q-63 0 -106 15.5t-84 57.5l30 40q31 -37 68 -52.5t92 -15.5q72 0 118.5 38.5t46.5 116.5v76q-27 -39 -71.5 -64.5t-96.5 -25.5q-97 0 -156.5 68.5t-59.5 183.5q0 114 60 183.5t156 69.5q104 0 168 -90v78h53v-478q0 -104 -60.5 -152.5t-157.5 -48.5zM278 37
q49 0 93 25.5t66 62.5v235q-22 37 -66 62.5t-93 25.5q-78 0 -123.5 -58t-45.5 -148t45.5 -147.5t123.5 -57.5zM285 561q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="711" 
d="M444 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM396 -13q-143 0 -240.5 97t-97.5 249t97.5 248.5t240.5 96.5q152 0 251 -114l-43 -31q-36 43 -90.5 68
t-117.5 25q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -211t198.5 -83q61 0 112 24t84 57v160h-252v51h309v-233q-99 -111 -253 -111z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="570" 
d="M240 610q0 42 19 79.5t45 55.5l29 -22q-23 -17 -39 -41.5t-17 -46.5l2 1h3q15 0 27 -12t12 -27q0 -16 -11.5 -28t-28.5 -12t-29 14.5t-12 38.5zM272 -196q-63 0 -106 15.5t-84 57.5l30 40q31 -37 68 -52.5t92 -15.5q72 0 118.5 38.5t46.5 116.5v76q-27 -39 -71.5 -64.5
t-96.5 -25.5q-97 0 -156.5 68.5t-59.5 183.5q0 114 60 183.5t156 69.5q104 0 168 -90v78h53v-478q0 -104 -60.5 -152.5t-157.5 -48.5zM278 37q49 0 93 25.5t66 62.5v235q-22 37 -66 62.5t-93 25.5q-78 0 -123.5 -58t-45.5 -148t45.5 -147.5t123.5 -57.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="703" 
d="M474 723h-40l-86 113l-85 -113h-39l97 144h54zM621 0h-58v317h-423v-317h-57v667h57v-298h423v298h58v-667z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="540" 
d="M398 723h-40l-86 113l-85 -113h-39l97 144h54zM459 0h-52v329q0 66 -30 92.5t-87 26.5q-45 0 -89 -24t-68 -59v-365h-52v667h52v-257q29 34 77 59.5t97 25.5q152 0 152 -155v-340z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="724" 
d="M632 0h-58v317h-423v-317h-57v500h-83v37h83v130h57v-130h423v130h58v-130h81v-37h-81v-500zM151 369h423v131h-423v-131z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="541" 
d="M296 538h-162v-128q29 34 77 59.5t97 25.5q152 0 152 -155v-340h-52v329q0 66 -30 92.5t-87 26.5q-45 0 -89 -24t-68 -59v-365h-52v538h-72v37h72v92h52v-92h162v-37z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667zM173 721q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="214" 
d="M133 0h-52v483h52v-483zM168 554q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667zM294 744h-363v37h363v-37z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="214" 
d="M133 0h-52v483h52v-483zM289 577h-363v37h363v-37z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667zM288 796q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="214" 
d="M133 0h-52v483h52v-483zM283 629q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="223" 
d="M164 -109l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 60 59 103v665h57v-667q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="214" 
d="M157 -109l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 62 64 106v478h52v-483q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44zM107 559q-16 0 -28 12t-12 28t12 27.5t28 11.5q17 0 28.5 -11.5t11.5 -27.5q0 -17 -11.5 -28.5t-28.5 -11.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667zM113 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="214" 
d="M133 0h-52v483h52v-483z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="693" 
d="M140 0h-57v667h57v-667zM408 -12q-108 0 -171 82l35 43q56 -73 133 -73q67 0 107 42.5t40 110.5v474h58v-474q0 -100 -57 -152.5t-145 -52.5z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="427" 
d="M107 559q-16 0 -28 12t-12 28t12 27.5t28 11.5q17 0 28.5 -11.5t11.5 -27.5q0 -17 -11.5 -28.5t-28.5 -11.5zM133 0h-52v483h52v-483zM321 559q-16 0 -28 12t-12 28t12 27.5t28 11.5q17 0 28.5 -11.5t11.5 -27.5q0 -17 -11.5 -28.5t-28.5 -11.5zM216 -196q-56 0 -99 37
l23 41q31 -33 71 -33q38 0 61 22t23 67v545h52v-545q0 -66 -34 -100t-97 -34z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="470" 
d="M483 723h-40l-86 113l-85 -113h-39l97 144h54zM185 -12q-108 0 -171 82l35 43q56 -73 133 -73q67 0 107 42.5t40 110.5v474h58v-474q0 -100 -57 -152.5t-145 -52.5z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="214" 
d="M233 556h-40l-86 113l-85 -113h-39l97 144h54zM2 -196q-56 0 -99 37l23 41q31 -33 71 -33q38 0 61 22t23 67v545h52v-545q0 -66 -34 -100t-97 -34z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="589" 
d="M345 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM562 0h-73l-275 314l-74 -81v-233h-57v667h57v-365l322 365h73l-284 -318z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="505" 
d="M304 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM486 0h-70l-193 230l-90 -85v-145h-52v667h52v-460l285 276h69l-225 -218z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="505" 
d="M486 0h-70l-193 230l-90 -85v-145h-52v483h52v-276l285 276h69l-225 -218z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="504" 
d="M435 867l-172 -144h-44l154 144h62zM463 0h-380v667h57v-615h323v-52z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="214" 
d="M269 867l-172 -144h-44l154 144h62zM133 0h-52v667h52v-667z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="504" 
d="M316 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM463 0h-380v667h57v-615h323v-52z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="214" 
d="M158 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM133 0h-52v667h52v-667z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="504" 
d="M327 620q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM463 0h-380v667h57v-615h323v-52z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="264" 
d="M286 620q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM133 0h-52v667h52v-667z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="504" 
d="M463 0h-380v667h57v-615h323v-52zM402 342q0 -18 -13.5 -31t-31.5 -13t-31 13t-13 31q0 17 13 30t31 13t31.5 -13t13.5 -30z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="273" 
d="M133 0h-52v667h52v-667zM288 245q0 -18 -13.5 -31t-31.5 -13t-31 13t-13 31q0 17 13 30t31 13t31.5 -13t13.5 -30z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="526" 
d="M11 233v53l94 54v327h57v-294l105 62v-53l-105 -62v-268h323v-52h-380v287z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="277" 
d="M11 238v46l102 58v325h52v-295l101 58v-46l-101 -58v-326h-52v296z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="699" 
d="M526 867l-172 -144h-44l154 144h62zM616 0h-56l-420 573v-573h-57v667h58l418 -565v565h57v-667z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="540" 
d="M432 700l-172 -144h-44l154 144h62zM459 0h-52v327q0 66 -30 93.5t-87 27.5q-45 0 -89 -24t-68 -59v-365h-52v483h52v-73q29 34 77 59.5t97 25.5q152 0 152 -157v-338z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="699" 
d="M401 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM616 0h-56l-420 573v-573h-57v667h58l418 -565v565h57v-667z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="540" 
d="M320 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM459 0h-52v327q0 66 -30 93.5t-87 27.5q-45 0 -89 -24t-68 -59v-365h-52v483h52v-73q29 34 77 59.5t97 25.5
q152 0 152 -157v-338z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="699" 
d="M378 723h-54l-100 144h39l88 -113l83 113h40zM616 0h-56l-420 573v-573h-57v667h58l418 -565v565h57v-667z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="540" 
d="M299 556h-54l-100 144h39l88 -113l83 113h40zM459 0h-52v327q0 66 -30 93.5t-87 27.5q-45 0 -89 -24t-68 -59v-365h-52v483h52v-73q29 34 77 59.5t97 25.5q152 0 152 -157v-338z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="540" 
d="M155 675q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM459 0h-52v327q0 66 -30 93.5t-87 27.5q-45 0 -89 -24t-68 -59v-365h-52v483h52v-73q29 34 77 59.5t97 25.5
q152 0 152 -157v-338z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="699" 
d="M414 -196q-108 0 -171 82l35 43q54 -73 134 -73q66 0 105.5 39.5t41.5 105.5l-419 572v-573h-57v667h58l418 -565v565h57v-661q0 -99 -57 -150.5t-145 -51.5z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="540" 
d="M459 338v-400q0 -66 -34 -100t-97 -34q-57 0 -98 37l22 43q33 -33 71 -33t61 21.5t23 65.5v389q0 66 -30 93.5t-87 27.5q-45 0 -89 -24t-68 -59v-365h-52v483h52v-73q29 34 77 59.5t97 25.5q152 0 152 -157z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="764" 
d="M563 744h-363v37h363v-37zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" 
d="M467 577h-363v37h363v-37zM285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5zM556 796q-65 -88 -175 -88q-109 0 -176 88l29 25
q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" 
d="M285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61zM462 629q-65 -88 -175 -88q-109 0 -176 88
l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="764" 
d="M430 867l-115 -144h-37l97 144h55zM569 867l-115 -144h-37l97 144h55zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211
q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" 
d="M333 700l-115 -144h-37l97 144h55zM472 700l-115 -144h-37l97 144h55zM285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5
t-47.5 -145.5t47.5 -146t128.5 -61z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1115" 
d="M1055 0h-423v139q-37 -73 -105.5 -112t-147.5 -39q-143 0 -232 98t-89 247t89 246.5t232 97.5q79 0 147.5 -39t105.5 -110v139h423v-52h-365v-247h358v-52h-358v-264h365v-52zM632 221v225q-31 88 -96.5 133.5t-152.5 45.5q-121 0 -193 -81.5t-72 -210.5t72.5 -211
t192.5 -82q86 0 151.5 46.5t97.5 134.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="975" 
d="M922 225h-403q3 -82 54 -137t134 -55q95 0 159 67l27 -34q-76 -78 -189 -78q-89 0 -142 46.5t-72 104.5q-10 -25 -23.5 -47.5t-37.5 -48t-61.5 -40.5t-82.5 -15q-104 0 -168 73t-64 181t64 180.5t168 72.5q46 0 83.5 -15.5t61.5 -40.5t38 -48t22 -47q19 55 70.5 103
t133.5 48q106 0 167 -73.5t61 -182.5v-14zM518 266h351q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5zM462 242q0 86 -48 146t-129 60q-80 0 -128 -60t-48 -146t48 -147t128 -61q81 0 129 60.5t48 147.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="594" 
d="M466 867l-172 -144h-44l154 144h62zM543 0h-69l-187 276h-147v-276h-57v667h249q88 0 145.5 -53t57.5 -142q0 -87 -53.5 -138t-131.5 -54zM327 328q66 0 107 40.5t41 103.5t-41 103t-107 40h-187v-287h187z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="317" 
d="M351 700l-172 -144h-44l154 144h62zM133 0h-52v483h52v-83q71 93 163 93v-57q-9 2 -26 2q-36 0 -78 -27t-59 -59v-352z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="594" 
d="M349 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM543 0h-69l-187 276h-147v-276h-57v667h249q88 0 145.5 -53t57.5 -142q0 -87 -53.5 -138t-131.5 -54zM327 328
q66 0 107 40.5t41 103.5t-41 103t-107 40h-187v-287h187z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="317" 
d="M210 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM133 0h-52v483h52v-83q71 93 163 93v-57q-9 2 -26 2q-36 0 -78 -27t-59 -59v-352z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="594" 
d="M326 723h-54l-100 144h39l88 -113l83 113h40zM543 0h-69l-187 276h-147v-276h-57v667h249q88 0 145.5 -53t57.5 -142q0 -87 -53.5 -138t-131.5 -54zM327 328q66 0 107 40.5t41 103.5t-41 103t-107 40h-187v-287h187z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="317" 
d="M215 556h-54l-100 144h39l88 -113l83 113h40zM133 0h-52v483h52v-83q71 93 163 93v-57q-9 2 -26 2q-36 0 -78 -27t-59 -59v-352z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="581" 
d="M449 867l-172 -144h-44l154 144h62zM290 -12q-157 0 -244 106l37 43q83 -97 208 -97q93 0 134.5 40.5t41.5 92.5q0 40 -22 67.5t-57.5 42.5t-78 27t-85 25.5t-78 32t-57.5 52t-22 80.5q0 80 63 129t156 49q143 0 226 -94l-38 -42q-70 84 -191 84q-67 0 -111.5 -34.5
t-44.5 -88.5q0 -34 22 -58.5t57.5 -38t78 -25t85 -26t78 -34.5t57.5 -56.5t22 -87.5q0 -76 -57 -132.5t-180 -56.5z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="459" 
d="M386 700l-172 -144h-44l154 144h62zM225 -12q-117 0 -186 76l31 39q25 -31 66 -51t90 -20q59 0 94 27.5t35 70.5q0 36 -31.5 57.5t-76 32t-89.5 23t-76.5 41.5t-31.5 77q0 57 46.5 95.5t125.5 38.5q109 0 173 -71l-29 -37q-47 64 -144 64q-55 0 -88 -25t-33 -63
q0 -33 31.5 -52t76 -29.5t89.5 -23.5t76.5 -45t31.5 -83q0 -62 -47 -102t-134 -40z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="581" 
d="M408 723h-40l-86 113l-85 -113h-39l97 144h54zM290 -12q-157 0 -244 106l37 43q83 -97 208 -97q93 0 134.5 40.5t41.5 92.5q0 40 -22 67.5t-57.5 42.5t-78 27t-85 25.5t-78 32t-57.5 52t-22 80.5q0 80 63 129t156 49q143 0 226 -94l-38 -42q-70 84 -191 84
q-67 0 -111.5 -34.5t-44.5 -88.5q0 -34 22 -58.5t57.5 -38t78 -25t85 -26t78 -34.5t57.5 -56.5t22 -87.5q0 -76 -57 -132.5t-180 -56.5z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="459" 
d="M350 556h-40l-86 113l-85 -113h-39l97 144h54zM225 -12q-117 0 -186 76l31 39q25 -31 66 -51t90 -20q59 0 94 27.5t35 70.5q0 36 -31.5 57.5t-76 32t-89.5 23t-76.5 41.5t-31.5 77q0 57 46.5 95.5t125.5 38.5q109 0 173 -71l-29 -37q-47 64 -144 64q-55 0 -88 -25
t-33 -63q0 -33 31.5 -52t76 -29.5t89.5 -23.5t76.5 -45t31.5 -83q0 -62 -47 -102t-134 -40z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="581" 
d="M286 -189q-63 0 -97 29l16 32q35 -28 80 -28q28 0 46.5 12t18.5 33q0 39 -41 39q-25 0 -40 -17l-28 16l23 62q-138 9 -218 105l37 43q83 -97 208 -97q93 0 134.5 40.5t41.5 92.5q0 40 -22 67.5t-57.5 42.5t-78 27t-85 25.5t-78 32t-57.5 52t-22 80.5q0 80 63 129t156 49
q143 0 226 -94l-38 -42q-70 84 -191 84q-67 0 -111.5 -34.5t-44.5 -88.5q0 -34 22 -58.5t57.5 -38t78 -25t85 -26t78 -34.5t57.5 -56.5t22 -87.5q0 -74 -54.5 -130t-171.5 -59l-17 -45q15 12 37 12q29 0 47.5 -18t18.5 -48q0 -35 -29 -56.5t-72 -21.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="459" 
d="M224 -189q-63 0 -97 29l16 32q35 -28 80 -28q28 0 46.5 12t18.5 33q0 39 -41 39q-25 0 -40 -17l-28 16l22 62q-100 6 -162 75l31 39q25 -31 66 -51t90 -20q59 0 94 27.5t35 70.5q0 36 -31.5 57.5t-76 32t-89.5 23t-76.5 41.5t-31.5 77q0 57 46.5 95.5t125.5 38.5
q109 0 173 -71l-29 -37q-47 64 -144 64q-55 0 -88 -25t-33 -63q0 -33 31.5 -52t76 -29.5t89.5 -23.5t76.5 -45t31.5 -83q0 -59 -43.5 -98t-123.5 -43l-17 -46q15 12 37 12q29 0 47.5 -18t18.5 -48q0 -35 -29 -56.5t-72 -21.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="581" 
d="M312 723h-54l-100 144h39l88 -113l83 113h40zM290 -12q-157 0 -244 106l37 43q83 -97 208 -97q93 0 134.5 40.5t41.5 92.5q0 40 -22 67.5t-57.5 42.5t-78 27t-85 25.5t-78 32t-57.5 52t-22 80.5q0 80 63 129t156 49q143 0 226 -94l-38 -42q-70 84 -191 84
q-67 0 -111.5 -34.5t-44.5 -88.5q0 -34 22 -58.5t57.5 -38t78 -25t85 -26t78 -34.5t57.5 -56.5t22 -87.5q0 -76 -57 -132.5t-180 -56.5z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="459" 
d="M254 556h-54l-100 144h39l88 -113l83 113h40zM225 -12q-117 0 -186 76l31 39q25 -31 66 -51t90 -20q59 0 94 27.5t35 70.5q0 36 -31.5 57.5t-76 32t-89.5 23t-76.5 41.5t-31.5 77q0 57 46.5 95.5t125.5 38.5q109 0 173 -71l-29 -37q-47 64 -144 64q-55 0 -88 -25t-33 -63
q0 -33 31.5 -52t76 -29.5t89.5 -23.5t76.5 -45t31.5 -83q0 -62 -47 -102t-134 -40z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="564" 
d="M333 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM311 0h-58v615h-218v52h494v-52h-218v-615z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="275" 
d="M189 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM186 -12q-95 0 -95 105v344h-80v46h80v132h53v-132h98v-46h-98v-336q0 -66 50 -66q33 0 54 23l20 -40
q-32 -30 -82 -30z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="564" 
d="M311 723h-54l-100 144h39l88 -113l83 113h40zM311 0h-58v615h-218v52h494v-52h-218v-615z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="285" 
d="M293 675q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM186 -12q-95 0 -95 105v344h-80v46h80v132h53v-132h98v-46h-98v-336q0 -66 50 -66q33 0 54 23l20 -40
q-32 -30 -82 -30z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="564" 
d="M311 0h-58v308h-158v37h158v270h-218v52h494v-52h-218v-270h158v-37h-158v-308z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="275" 
d="M186 -12q-95 0 -95 105v142h-80v37h80v165h-80v46h80v132h53v-132h98v-46h-98v-165h70v-37h-70v-134q0 -66 50 -66q33 0 54 23l20 -40q-32 -30 -82 -30z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="686" 
d="M343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-411q0 -126 -66.5 -197t-193.5 -71zM407 721q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38
q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="540" 
d="M459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-483zM331 554q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38
q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="686" 
d="M343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-411q0 -126 -66.5 -197t-193.5 -71zM525 744h-363v37h363v-37z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="540" 
d="M459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-483zM451 577h-363v37h363v-37z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="686" 
d="M343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-411q0 -126 -66.5 -197t-193.5 -71zM522 796q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="540" 
d="M459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-483zM446 629q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="686" 
d="M344 715q-40 0 -68 28t-28 68q0 39 28 67.5t68 28.5q39 0 66.5 -28.5t27.5 -67.5q0 -40 -27.5 -68t-66.5 -28zM344 747q26 0 44.5 19t18.5 45t-18.5 45t-44.5 19t-45 -19t-19 -45t19 -45t45 -19zM343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5
t149.5 -57.5q98 0 150 57t52 160v410h58v-411q0 -126 -66.5 -197t-193.5 -71z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="540" 
d="M270 548q-40 0 -68 28t-28 68q0 39 28 67.5t68 28.5q39 0 66.5 -28.5t27.5 -67.5q0 -40 -27.5 -68t-66.5 -28zM270 580q26 0 44.5 19t18.5 45t-18.5 45t-44.5 19t-45 -19t-19 -45t19 -45t45 -19zM459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5
v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-483z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="686" 
d="M343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-411q0 -126 -66.5 -197t-193.5 -71zM391 867l-115 -144h-37l97 144h55zM530 867l-115 -144h-37l97 144h55z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="540" 
d="M459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-483zM317 700l-115 -144h-37l97 144h55zM456 700l-115 -144h-37l97 144h55z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="686" 
d="M343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-411q0 -101 -43.5 -167.5t-126.5 -89.5q-77 -38 -77 -98q0 -54 48 -54q41 0 55 44l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 50 43 90
q-7 -1 -19 -1z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="540" 
d="M459 0q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 62 64 106v66q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-483z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="867" 
d="M559 723h-40l-86 113l-85 -113h-39l97 144h54zM658 0h-64l-160 573l-160 -573h-64l-191 667h64l161 -587l164 587h52l163 -587l161 587h64z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="719" 
d="M485 556h-40l-86 113l-85 -113h-39l97 144h54zM545 0h-50l-135 413l-136 -413h-50l-157 483h56l128 -410l136 410h45l136 -410l128 410h56z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="618" 
d="M435 723h-40l-86 113l-85 -113h-39l97 144h54zM338 0h-58v285l-265 382h69l225 -328l225 328h69l-265 -382v-285z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="481" 
d="M49 -189l9 48q18 -8 42 -8q49 0 73 59l38 86l-204 487h58l176 -422l174 422h59l-250 -589q-38 -90 -124 -90q-28 0 -51 7zM366 556h-40l-86 113l-85 -113h-39l97 144h54z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="618" 
d="M451 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM247 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM338 0h-58v285l-265 382h69l225 -328l225 328h69l-265 -382v-285z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="581" 
d="M453 867l-172 -144h-44l154 144h62zM531 0h-481v50l402 565h-402v52h474v-49l-402 -566h409v-52z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="469" 
d="M396 700l-172 -144h-44l154 144h62zM413 0h-357v43l286 394h-286v46h353v-42l-289 -395h293v-46z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="581" 
d="M531 0h-481v50l402 565h-402v52h474v-49l-402 -566h409v-52zM290 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="469" 
d="M413 0h-357v43l286 394h-286v46h353v-42l-289 -395h293v-46zM234 561q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="581" 
d="M318 723h-54l-100 144h39l88 -113l83 113h40zM531 0h-481v50l402 565h-402v52h474v-49l-402 -566h409v-52z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="469" 
d="M257 556h-54l-100 144h39l88 -113l83 113h40zM413 0h-357v43l286 394h-286v46h353v-42l-289 -395h293v-46z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="265" 
d="M150 0h-52v437h-80v46h80v44q0 70 35 110t96 40q53 0 92 -34l-25 -38q-27 25 -62 25q-41 0 -62.5 -27t-21.5 -76v-527z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="422" 
d="M91 -161h-58l93 421h-74v49h84l49 221q16 68 59.5 107.5t104.5 39.5q53 0 85 -33l-30 -42q-20 23 -51 23q-41 0 -70.5 -27.5t-40.5 -74.5l-48 -214h151v-49h-161z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q152 0 245 -112q43 38 46 82h-4q-4 -1 -6 -1q-15 0 -25 10.5t-10 26.5q0 15 10.5 26t26.5 11q19 0 32 -15t13 -42q0 -37 -18 -70t-45 -54q59 -88 59 -207q0 -149 -90 -247t-234 -98zM382 40q118 0 191 82.5
t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" 
d="M517 530q0 -69 -55 -117q56 -70 56 -171q0 -108 -64 -181t-169 -73q-104 0 -168 73t-64 181t64 180.5t168 72.5q91 0 154 -58q38 36 41 77h-4q-4 -1 -6 -1q-15 0 -25 10.5t-10 26.5q0 15 10.5 26t26.5 11q19 0 32 -15t13 -42zM285 35q82 0 129.5 60.5t47.5 146.5
q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="686" 
d="M721 729q0 -50 -32.5 -92t-85.5 -61v-320q0 -126 -66.5 -197t-193.5 -71q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-58q34 16 57 45t24 59q-1 -1 -9 -1q-16 0 -26 11t-10 27q0 15 10.5 26t26.5 11
q19 0 32 -15.5t13 -42.5z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" horiz-adv-x="540" 
d="M459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-59q59 38 61 93q-6 -1 -9 -1q-16 0 -26 10.5t-10 26.5q0 15 11 26t27 11q19 0 31.5 -15t12.5 -42q0 -45 -26.5 -83.5
t-71.5 -61.5v-388z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="939" 
d="M648 867l-172 -144h-44l154 144h62zM879 0h-423v164h-274l-102 -164h-66l421 667h444v-52h-365v-247h358v-52h-358v-264h365v-52zM456 216v390l-245 -390h245z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="897" 
d="M577 700l-172 -144h-44l154 144h62zM844 225h-379q4 -82 51 -137t123 -55q89 0 148 67l27 -34q-72 -78 -175 -78q-67 0 -118 33t-78 96q-23 -59 -75.5 -94t-119.5 -35q-84 0 -138.5 43t-54.5 118t52 117.5t127 42.5q111 0 179 -67v96q0 53 -38.5 82t-102.5 29
q-97 0 -165 -71l-29 36q75 81 202 81q63 0 111.5 -27.5t58.5 -87.5q26 51 72.5 83t107.5 32q101 0 157.5 -73t56.5 -183v-14zM465 266h326q0 71 -42 127.5t-121 56.5q-73 0 -116.5 -57t-46.5 -127zM413 184v16q-57 68 -159 68q-65 0 -104.5 -33t-39.5 -84q0 -52 39.5 -85
t104.5 -33q69 0 114 45.5t45 105.5z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="764" 
d="M544 867l-172 -144h-44l154 144h62zM382 -12q-93 0 -168 44l-20 -32h-51l36 56q-58 47 -89.5 119t-31.5 158q0 149 89.5 247t234.5 98q89 0 164 -43l20 32h52l-36 -56q59 -47 91.5 -119.5t32.5 -158.5q0 -149 -90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5
q0 149 -93 231l-310 -487q60 -37 139 -37zM118 333q0 -147 91 -230l308 487q-59 36 -135 36q-120 0 -192 -82t-72 -211z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" 
d="M448 700l-172 -144h-44l154 144h62zM118 0h-44l45 59q-66 72 -66 183q0 108 64 180.5t168 72.5q78 0 135 -43l24 31h44l-41 -53q71 -73 71 -188q0 -108 -64 -181t-169 -73q-81 0 -141 47zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -48 145l-238 -311q44 -41 109 -41z
M109 242q0 -82 43 -140l237 310q-44 36 -104 36q-81 0 -128.5 -60.5t-47.5 -145.5z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="581" 
d="M337 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM290 -12q-157 0 -244 106l37 43q83 -97 208 -97q93 0 134.5 40.5t41.5 92.5q0 40 -22 67.5t-57.5 42.5t-78 27
t-85 25.5t-78 32t-57.5 52t-22 80.5q0 80 63 129t156 49q143 0 226 -94l-38 -42q-70 84 -191 84q-67 0 -111.5 -34.5t-44.5 -88.5q0 -34 22 -58.5t57.5 -38t78 -25t85 -26t78 -34.5t57.5 -56.5t22 -87.5q0 -76 -57 -132.5t-180 -56.5z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="459" 
d="M282 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM225 -12q-117 0 -186 76l31 39q25 -31 66 -51t90 -20q59 0 94 27.5t35 70.5q0 36 -31.5 57.5t-76 32t-89.5 23
t-76.5 41.5t-31.5 77q0 57 46.5 95.5t125.5 38.5q109 0 173 -71l-29 -37q-47 64 -144 64q-55 0 -88 -25t-33 -63q0 -33 31.5 -52t76 -29.5t89.5 -23.5t76.5 -45t31.5 -83q0 -62 -47 -102t-134 -40z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="564" 
d="M333 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM311 0h-58v615h-218v52h494v-52h-218v-615z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="275" 
d="M189 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM186 -12q-95 0 -95 105v344h-80v46h80v132h53v-132h98v-46h-98v-336q0 -66 50 -66q33 0 54 23l20 -40
q-32 -30 -82 -30z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="214" 
d="M2 -196q-56 0 -99 37l23 41q31 -33 71 -33q38 0 61 22t23 67v545h52v-545q0 -66 -34 -100t-97 -34z" />
    <glyph glyph-name="hsuperior" unicode="&#x2b0;" horiz-adv-x="392" 
d="M328 326h-43v208q0 43 -19.5 60.5t-56.5 17.5q-29 0 -57.5 -15t-44.5 -36v-235h-42v434h42v-167q19 22 51 38.5t66 16.5q104 0 104 -108v-214z" />
    <glyph glyph-name="uni02BC" unicode="&#x2bc;" horiz-adv-x="219" 
d="M159 613q0 -40 -20.5 -77.5t-51.5 -58.5l-29 24q24 16 40 42.5t18 50.5q0 -2 -10 -2q-18 0 -29.5 12t-11.5 30q0 17 12.5 30t30.5 13q21 0 36 -17t15 -47z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="250" 
d="M250 556h-40l-86 113l-85 -113h-39l97 144h54z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="250" 
d="M154 556h-54l-100 144h39l88 -113l83 113h40z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="363" 
d="M363 591h-363v38h363v-38z" />
    <glyph glyph-name="uni02CB" unicode="&#x2cb;" horiz-adv-x="216" 
d="M216 556h-44l-172 144h62z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="351" 
d="M351 629q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="85" 
d="M43 561q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="190" 
d="M96 548q-40 0 -68 28t-28 68q0 39 28 67.5t68 28.5q39 0 66.5 -28.5t27.5 -67.5q0 -40 -27.5 -68t-66.5 -28zM96 580q26 0 44.5 19t18.5 45t-18.5 45t-44.5 19t-45 -19t-19 -45t19 -45t45 -19z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="171" 
d="M140 -109l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 36 24.5 68.5t63.5 52.5l28 -20q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="307" 
d="M215 554q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="291" 
d="M152 700l-115 -144h-37l97 144h55zM291 700l-115 -144h-37l97 144h55z" />
    <glyph glyph-name="hookabovecomb" unicode="&#x309;" horiz-adv-x="171" 
d="M42 728l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni037E" unicode="&#x37e;" horiz-adv-x="219" 
d="M109 403q-18 0 -31 13t-13 30q0 18 13 31t31 13t31.5 -13t13.5 -31q0 -17 -13.5 -30t-31.5 -13zM159 14q0 -40 -20.5 -77t-51.5 -59l-29 25q24 16 40 42.5t18 49.5q-7 -1 -10 -1q-18 0 -29.5 12t-11.5 30q0 17 12.5 30t30.5 13q21 0 36 -17.5t15 -47.5z" />
    <glyph glyph-name="tonos" unicode="&#x384;" horiz-adv-x="187" 
d="M126 570h-30l18 195h64z" />
    <glyph glyph-name="dieresistonos" unicode="&#x385;" horiz-adv-x="540" 
d="M273 570h-30l18 195h64zM435 601q0 -15 -11 -26t-27 -11q-15 0 -26 11t-11 26t11 26t26 11q16 0 27 -11t11 -26zM179 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26z" />
    <glyph glyph-name="Alphatonos" unicode="&#x386;" horiz-adv-x="647" 
d="M190 570h-30l18 195h64zM632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315z" />
    <glyph glyph-name="Epsilontonos" unicode="&#x388;" horiz-adv-x="572" 
d="M19 570h-30l18 195h64zM514 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52z" />
    <glyph glyph-name="Etatonos" unicode="&#x389;" horiz-adv-x="712" 
d="M19 570h-30l18 195h64zM629 0h-58v317h-423v-317h-57v667h57v-298h423v298h58v-667z" />
    <glyph glyph-name="Iotatonos" unicode="&#x38a;" horiz-adv-x="232" 
d="M19 570h-30l18 195h64zM148 0h-57v667h57v-667z" />
    <glyph glyph-name="Omicrontonos" unicode="&#x38c;" horiz-adv-x="764" 
d="M61 570h-30l18 195h64zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="Upsilontonos" unicode="&#x38e;" horiz-adv-x="665" 
d="M19 570h-30l18 195h64zM384 0h-58v285l-265 382h69l225 -328l225 328h69l-265 -382v-285z" />
    <glyph glyph-name="Omegatonos" unicode="&#x38f;" horiz-adv-x="766" 
d="M61 570h-30l18 195h64zM79 52h141q-160 94 -160 289q0 146 90 241.5t234 95.5q145 0 234.5 -95.5t89.5 -241.5q0 -195 -159 -289h142v-52h-228v52q74 18 129.5 92.5t55.5 188.5q0 127 -71 209.5t-193 82.5t-193 -82.5t-71 -209.5q0 -114 56 -188t130 -93v-52h-227v52z
" />
    <glyph glyph-name="iotadieresistonos" unicode="&#x390;" horiz-adv-x="228" 
d="M117 570h-30l18 195h64zM279 601q0 -15 -11 -26t-27 -11q-15 0 -26 11t-11 26t11 26t26 11q16 0 27 -11t11 -26zM23 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM175 -12q-94 0 -94 105v390h52v-384q0 -64 49 -64q12 0 20 2l4 -46
q-12 -3 -31 -3z" />
    <glyph glyph-name="Alpha" unicode="&#x391;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315z" />
    <glyph glyph-name="Beta" unicode="&#x392;" horiz-adv-x="614" 
d="M363 0h-280v667h274q84 0 134 -46t50 -124q0 -60 -34.5 -101.5t-82.5 -51.5q53 -8 91.5 -56.5t38.5 -107.5q0 -83 -51 -131.5t-140 -48.5zM350 368q62 0 96.5 34.5t34.5 88.5q0 53 -34.5 88.5t-96.5 35.5h-210v-247h210zM354 52q66 0 103 35.5t37 96.5q0 55 -37 93.5
t-103 38.5h-214v-264h214z" />
    <glyph glyph-name="Gamma" unicode="&#x393;" horiz-adv-x="542" 
d="M140 0h-57v667h423v-52h-366v-615z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="647" 
d="M632 0h-617l273 667h71zM549 52l-226 554l-225 -554h451z" />
    <glyph glyph-name="Epsilon" unicode="&#x395;" horiz-adv-x="564" 
d="M506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52z" />
    <glyph glyph-name="Zeta" unicode="&#x396;" horiz-adv-x="581" 
d="M531 0h-481v50l402 565h-402v52h474v-49l-402 -566h409v-52z" />
    <glyph glyph-name="Eta" unicode="&#x397;" horiz-adv-x="703" 
d="M621 0h-58v317h-423v-317h-57v667h57v-298h423v298h58v-667z" />
    <glyph glyph-name="Theta" unicode="&#x398;" horiz-adv-x="764" 
d="M583 316h-402v52h402v-52zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="Iota" unicode="&#x399;" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667z" />
    <glyph glyph-name="Kappa" unicode="&#x39a;" horiz-adv-x="586" 
d="M562 0h-73l-275 314l-74 -81v-233h-57v667h57v-365l322 365h73l-284 -318z" />
    <glyph glyph-name="Lambda" unicode="&#x39b;" horiz-adv-x="647" 
d="M632 0h-66l-243 606l-242 -606h-66l273 667h71z" />
    <glyph glyph-name="Mu" unicode="&#x39c;" horiz-adv-x="790" 
d="M707 0h-58v589l-243 -589h-22l-244 589v-589h-57v667h85l227 -550l226 550h86v-667z" />
    <glyph glyph-name="Nu" unicode="&#x39d;" horiz-adv-x="699" 
d="M616 0h-56l-420 573v-573h-57v667h58l418 -565v565h57v-667z" />
    <glyph glyph-name="Xi" unicode="&#x39e;" horiz-adv-x="612" 
d="M553 615h-494v52h494v-52zM553 0h-495v52h495v-52zM547 316h-481v52h481v-52z" />
    <glyph glyph-name="Omicron" unicode="&#x39f;" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="Pi" unicode="&#x3a0;" horiz-adv-x="703" 
d="M621 0h-58v615h-423v-615h-57v667h538v-667z" />
    <glyph glyph-name="Rho" unicode="&#x3a1;" 
d="M140 0h-57v667h250q93 0 147 -56.5t54 -138.5t-54.5 -138.5t-146.5 -56.5h-193v-277zM327 329q66 0 106.5 40t40.5 103t-40.5 103t-106.5 40h-187v-286h187z" />
    <glyph glyph-name="Sigma" unicode="&#x3a3;" 
d="M129 615l242 -271l-245 -292h387v-52h-455v52l243 291l-242 272v52h454v-52h-384z" />
    <glyph glyph-name="Tau" unicode="&#x3a4;" horiz-adv-x="564" 
d="M311 0h-58v615h-218v52h494v-52h-218v-615z" />
    <glyph glyph-name="Upsilon" unicode="&#x3a5;" horiz-adv-x="618" 
d="M338 0h-58v285l-265 382h69l225 -328l225 328h69l-265 -382v-285z" />
    <glyph glyph-name="Phi" unicode="&#x3a6;" horiz-adv-x="785" 
d="M421 0h-57v65q-140 7 -223 81t-83 187q0 112 83 186.5t223 81.5v66h57v-66q140 -7 223 -81.5t83 -186.5q0 -113 -83 -187t-223 -81v-65zM118 333q0 -91 66 -150t180 -66v432q-114 -7 -180 -66t-66 -150zM666 333q0 91 -66 150t-179 66v-432q112 7 178.5 66t66.5 150z" />
    <glyph glyph-name="Chi" unicode="&#x3a7;" horiz-adv-x="645" 
d="M626 0h-71l-233 301l-232 -301h-72l269 342l-254 325h73l216 -283l217 283h72l-253 -324z" />
    <glyph glyph-name="Psi" unicode="&#x3a8;" horiz-adv-x="766" 
d="M412 0h-58v125q-132 7 -201.5 77.5t-69.5 189.5v275h58v-274q0 -97 54.5 -153t158.5 -63v490h58v-490q103 7 158 63t55 153v274h58v-275q0 -119 -69.5 -189.5t-201.5 -77.5v-125z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="764" 
d="M77 52h141q-160 94 -160 289q0 146 90 241.5t234 95.5q145 0 234.5 -95.5t89.5 -241.5q0 -195 -159 -289h142v-52h-228v52q74 18 129.5 92.5t55.5 188.5q0 127 -71 209.5t-193 82.5t-193 -82.5t-71 -209.5q0 -114 56 -188t130 -93v-52h-227v52z" />
    <glyph glyph-name="Iotadieresis" unicode="&#x3aa;" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667zM251 785q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM47 785q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26z" />
    <glyph glyph-name="Upsilondieresis" unicode="&#x3ab;" horiz-adv-x="618" 
d="M338 0h-58v285l-265 382h69l225 -328l225 328h69l-265 -382v-285zM448 785q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM244 785q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26z" />
    <glyph glyph-name="alphatonos" unicode="&#x3ac;" horiz-adv-x="585" 
d="M298 570h-30l18 195h64zM559 37l4 -46q-12 -3 -31 -3q-85 0 -93 91q-26 -40 -71.5 -65.5t-98.5 -25.5q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-384q0 -29 13 -46.5t36 -17.5q12 0 20 2zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26
q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z" />
    <glyph glyph-name="epsilontonos" unicode="&#x3ad;" horiz-adv-x="519" 
d="M265 570h-30l18 195h64zM270 -12q-101 0 -159 39t-58 101q0 55 45 84.5t98 33.5q-49 6 -91 37t-42 83q0 60 61 94.5t156 34.5q120 0 195 -77l-29 -32q-61 64 -167 64q-69 0 -114.5 -24.5t-45.5 -63.5q0 -46 46 -70.5t115 -24.5h112v-44h-112q-170 0 -170 -92
q0 -45 44 -71.5t116 -26.5q120 0 187 69l28 -34q-78 -80 -215 -80z" />
    <glyph glyph-name="etatonos" unicode="&#x3ae;" horiz-adv-x="540" 
d="M276 570h-30l18 195h64zM459 -184h-52v511q0 66 -30 93.5t-87 27.5q-45 0 -89 -24t-68 -59v-365h-52v483h52v-73q29 34 77 59.5t97 25.5q152 0 152 -157v-522z" />
    <glyph glyph-name="iotatonos" unicode="&#x3af;" horiz-adv-x="228" 
d="M120 570h-30l18 195h64zM175 -12q-94 0 -94 105v390h52v-384q0 -64 49 -64q12 0 20 2l4 -46q-12 -3 -31 -3z" />
    <glyph glyph-name="upsilondieresistonos" unicode="&#x3b0;" horiz-adv-x="555" 
d="M280 570h-30l18 195h64zM442 601q0 -15 -11 -26t-27 -11q-15 0 -26 11t-11 26t11 26t26 11q16 0 27 -11t11 -26zM186 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM270 -12q-92 0 -140.5 59t-48.5 162v274h52v-273q0 -82 36 -128.5t101 -46.5
q82 0 129 61t47 150q0 136 -79 222l45 27q90 -106 90 -249q0 -111 -64 -184.5t-168 -73.5z" />
    <glyph glyph-name="alpha" unicode="&#x3b1;" horiz-adv-x="585" 
d="M559 37l4 -46q-12 -3 -31 -3q-85 0 -93 91q-26 -40 -71.5 -65.5t-98.5 -25.5q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-384q0 -29 13 -46.5t36 -17.5q12 0 20 2zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5
t-45.5 -148.5t45.5 -148t123.5 -58z" />
    <glyph glyph-name="beta" unicode="&#x3b2;" 
d="M133 458v-337q23 -36 67.5 -61t94.5 -25q79 0 122.5 39t43.5 107q0 69 -49 104t-125 35h-33v46h32q69 0 114.5 33.5t45.5 97.5q0 60 -42 96.5t-103 36.5q-72 0 -120 -44.5t-48 -127.5zM133 77v-261h-52v642q0 107 64 163t156 56q85 0 143 -47t58 -129q0 -69 -42.5 -108
t-95.5 -48q54 -6 103.5 -49.5t49.5 -120.5q0 -84 -59 -135.5t-151 -51.5q-56 0 -101.5 24.5t-72.5 64.5z" />
    <glyph glyph-name="gamma" unicode="&#x3b3;" horiz-adv-x="481" 
d="M267 -184h-53v147q0 148 -61 288.5t-156 231.5h64q71 -69 126.5 -188.5t66.5 -224.5q63 85 103 196t40 217h52q0 -115 -49 -243t-133 -236v-188z" />
    <glyph glyph-name="delta" unicode="&#x3b4;" 
d="M287 -12q-104 0 -169 67t-65 159q0 77 41.5 135t110.5 85q-45 19 -68.5 44t-23.5 67q0 58 46 96t126 38q108 0 172 -71l-29 -37q-47 64 -143 64q-56 0 -88.5 -25t-32.5 -63q0 -34 36.5 -55.5t88.5 -37t104 -38t88.5 -74t36.5 -129.5q0 -92 -64 -158.5t-167 -66.5z
M466 213q0 38 -12.5 69t-28.5 49t-47.5 35.5t-51 25t-58.5 20.5q-77 -23 -118 -75.5t-41 -122.5q0 -69 48.5 -124t128.5 -55q81 0 130.5 55t49.5 123z" />
    <glyph glyph-name="epsilon" unicode="&#x3b5;" horiz-adv-x="519" 
d="M270 -12q-101 0 -159 39t-58 101q0 55 45 84.5t98 33.5q-49 6 -91 37t-42 83q0 60 61 94.5t156 34.5q120 0 195 -77l-29 -32q-61 64 -167 64q-69 0 -114.5 -24.5t-45.5 -63.5q0 -46 46 -70.5t115 -24.5h112v-44h-112q-170 0 -170 -92q0 -45 44 -71.5t116 -26.5
q120 0 187 69l28 -34q-78 -80 -215 -80z" />
    <glyph glyph-name="zeta" unicode="&#x3b6;" horiz-adv-x="491" 
d="M391 -53q0 23 -15.5 32t-51.5 9q-122 0 -196.5 60.5t-74.5 163.5q0 229 321 409h-305v46h375v-42q-165 -97 -250 -196.5t-85 -214.5q0 -84 60 -131.5t160 -47.5q62 0 89.5 -18.5t27.5 -62.5q0 -45 -39 -111h-58q42 69 42 104z" />
    <glyph glyph-name="eta" unicode="&#x3b7;" horiz-adv-x="540" 
d="M459 -184h-52v511q0 66 -30 93.5t-87 27.5q-45 0 -89 -24t-68 -59v-365h-52v483h52v-73q29 34 77 59.5t97 25.5q152 0 152 -157v-522z" />
    <glyph glyph-name="theta" unicode="&#x3b8;" 
d="M285 -12q-113 0 -172.5 97t-59.5 249q0 99 24.5 175.5t78 123t129.5 46.5q115 0 174 -96t59 -249q0 -99 -24 -175.5t-78 -123.5t-131 -47zM285 35q59 0 99 38.5t57.5 98.5t19.5 140h-352q4 -124 46.5 -200.5t129.5 -76.5zM285 632q-59 0 -99 -38.5t-57 -97.5t-20 -137
h352q-3 121 -46 197t-130 76z" />
    <glyph glyph-name="iota" unicode="&#x3b9;" horiz-adv-x="228" 
d="M175 -12q-94 0 -94 105v390h52v-384q0 -64 49 -64q12 0 20 2l4 -46q-12 -3 -31 -3z" />
    <glyph glyph-name="kappa" unicode="&#x3ba;" horiz-adv-x="505" 
d="M486 0h-70l-193 230l-90 -85v-145h-52v483h52v-276l285 276h69l-225 -218z" />
    <glyph glyph-name="lambda" unicode="&#x3bb;" horiz-adv-x="481" 
d="M58 624l-9 48q23 7 51 7q86 -2 124 -90l250 -589h-59l-174 422l-176 -422h-58l204 487l-38 86q-24 59 -73 59q-24 0 -42 -8z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="554" 
d="M133 44v-228h-52v667h52v-328q0 -122 118 -122q44 0 87.5 24t68.5 58v368h52v-384q0 -64 49 -64q12 0 20 2l4 -46q-12 -3 -31 -3q-81 0 -93 86q-29 -37 -72.5 -61.5t-88.5 -24.5q-84 0 -114 56z" />
    <glyph glyph-name="nu" unicode="&#x3bd;" horiz-adv-x="481" 
d="M264 0h-53l-204 483h58l178 -429q71 96 112.5 209t41.5 220h52q0 -116 -50 -245.5t-135 -237.5z" />
    <glyph glyph-name="xi" unicode="&#x3be;" horiz-adv-x="491" 
d="M324 -12q-129 0 -200 49t-71 138q0 73 46.5 115t98.5 49q-50 9 -92 43.5t-42 94.5q0 45 32.5 85.5t80.5 58.5h-106v46h363v-46h-187q-54 -13 -90.5 -52t-36.5 -83q0 -57 48 -88t119 -31h140v-46h-140q-78 0 -128 -35.5t-50 -105.5q0 -68 57.5 -106.5t162.5 -38.5
q62 0 89.5 -18.5t27.5 -62.5q0 -45 -39 -111h-58q42 69 42 104q0 23 -15.5 32t-51.5 9z" />
    <glyph glyph-name="omicron" unicode="&#x3bf;" 
d="M285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="561" 
d="M470 0h-53v437h-273v-437h-53v437h-80v46h539v-46h-80v-437z" />
    <glyph glyph-name="rho" unicode="&#x3c1;" horiz-adv-x="567" 
d="M301 -12q-104 0 -168 90v-262h-52v426q0 108 59.5 180.5t157.5 72.5t158.5 -72.5t60.5 -180.5q0 -115 -59.5 -184.5t-156.5 -69.5zM292 35q79 0 124 58t45 149q0 85 -44 145.5t-120 60.5t-120 -60.5t-44 -145.5v-119q22 -36 66.5 -62t92.5 -26z" />
    <glyph glyph-name="sigma1" unicode="&#x3c2;" horiz-adv-x="479" 
d="M322 -12q-124 0 -196.5 67.5t-72.5 186.5q0 108 65 180.5t170 72.5q106 0 171 -82l-36 -32q-49 67 -132 67t-132.5 -58.5t-49.5 -147.5q0 -101 59 -154t157 -53q62 0 90.5 -18.5t28.5 -62.5q0 -42 -39 -111h-54q42 69 42 104q0 23 -17.5 32t-53.5 9z" />
    <glyph glyph-name="sigma" unicode="&#x3c3;" 
d="M515 230q0 -102 -63 -172t-167 -70t-168 73t-64 181q0 103 67 172t182 69h241v-47h-125q97 -76 97 -206zM462 235q0 70 -31 123t-82 78h-47q-91 0 -142 -57.5t-51 -136.5q0 -85 47.5 -146t128.5 -61t129 59.5t48 140.5z" />
    <glyph glyph-name="tau" unicode="&#x3c4;" horiz-adv-x="463" 
d="M299 -12q-94 0 -94 105v344h-176v46h405v-46h-177v-336q0 -66 50 -66q35 0 54 23l20 -40q-32 -30 -82 -30z" />
    <glyph glyph-name="upsilon" unicode="&#x3c5;" horiz-adv-x="555" 
d="M270 -12q-92 0 -140.5 59t-48.5 162v274h52v-273q0 -82 36 -128.5t101 -46.5q82 0 129 61t47 150q0 136 -79 222l45 27q90 -106 90 -249q0 -111 -64 -184.5t-168 -73.5z" />
    <glyph glyph-name="phi" unicode="&#x3c6;" horiz-adv-x="722" 
d="M668 242q0 -109 -71.5 -178t-209.5 -75v-173h-52v173q-139 7 -210.5 77.5t-71.5 187.5q0 81 49 142.5t127 98.5l23 -41q-143 -72 -143 -200q0 -93 54.5 -152.5t171.5 -66.5v460h26q151 0 229 -70t78 -183zM612 242q0 87 -56 144t-169 61v-412q115 6 170 63.5t55 143.5z
" />
    <glyph glyph-name="chi" unicode="&#x3c7;" horiz-adv-x="479" 
d="M462 -184h-59l-163 294l-164 -294h-59l191 343l-180 324h59l153 -275l152 275h59l-180 -324z" />
    <glyph glyph-name="psi" unicode="&#x3c8;" horiz-adv-x="722" 
d="M387 -184h-52v173q-124 5 -189 64.5t-65 161.5v268h52v-268q0 -80 52.5 -127t149.5 -53v632h52v-632q96 6 149 53t53 127v268h52v-268q0 -102 -65 -161t-189 -65v-173z" />
    <glyph glyph-name="omega" unicode="&#x3c9;" horiz-adv-x="789" 
d="M735 242q0 -108 -49.5 -181t-130.5 -73q-65 0 -105.5 45t-55.5 106q-15 -61 -55.5 -106t-105.5 -45q-81 0 -130.5 73t-49.5 181q0 177 95 253l37 -34q-76 -67 -76 -219q0 -85 35 -146t95 -61t94.5 60.5t34.5 146.5v123h53v-123q0 -86 34.5 -146.5t94.5 -60.5t94.5 60.5
t34.5 146.5q0 153 -75 219l37 34q94 -75 94 -253z" />
    <glyph glyph-name="iotadieresis" unicode="&#x3ca;" horiz-adv-x="228" 
d="M253 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM49 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM175 -12q-94 0 -94 105v390h52v-384q0 -64 49 -64q12 0 20 2l4 -46q-12 -3 -31 -3z" />
    <glyph glyph-name="upsilondieresis" unicode="&#x3cb;" horiz-adv-x="555" 
d="M416 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM212 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM270 -12q-92 0 -140.5 59t-48.5 162v274h52v-273q0 -82 36 -128.5t101 -46.5q82 0 129 61t47 150q0 136 -79 222
l45 27q90 -106 90 -249q0 -111 -64 -184.5t-168 -73.5z" />
    <glyph glyph-name="omicrontonos" unicode="&#x3cc;" 
d="M291 570h-30l18 195h64zM285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61z" />
    <glyph glyph-name="upsilontonos" unicode="&#x3cd;" horiz-adv-x="555" 
d="M284 570h-30l18 195h64zM270 -12q-92 0 -140.5 59t-48.5 162v274h52v-273q0 -82 36 -128.5t101 -46.5q82 0 129 61t47 150q0 136 -79 222l45 27q90 -106 90 -249q0 -111 -64 -184.5t-168 -73.5z" />
    <glyph glyph-name="omegatonos" unicode="&#x3ce;" horiz-adv-x="789" 
d="M400 570h-30l18 195h64zM735 242q0 -108 -49.5 -181t-130.5 -73q-65 0 -105.5 45t-55.5 106q-15 -61 -55.5 -106t-105.5 -45q-81 0 -130.5 73t-49.5 181q0 177 95 253l37 -34q-76 -67 -76 -219q0 -85 35 -146t95 -61t94.5 60.5t34.5 146.5v123h53v-123q0 -86 34.5 -146.5
t94.5 -60.5t94.5 60.5t34.5 146.5q0 153 -75 219l37 34q94 -75 94 -253z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="564" 
d="M434 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM230 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="773" 
d="M499 -12v52q68 0 107.5 44t39.5 121v6q0 86 -43.5 130t-127.5 44q-82 0 -164 -21v-364h-58v615h-218v52h494v-52h-218v-200q86 22 167 22q111 0 168.5 -61t57.5 -165v-6q0 -108 -58 -162.5t-147 -54.5z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="542" 
d="M451 867l-172 -144h-44l154 144h62zM140 0h-57v667h423v-52h-366v-615z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="672" 
d="M396 -12q-144 0 -241 96.5t-97 248.5t97 248.5t241 96.5q148 0 243 -118l-48 -31q-32 44 -84 70.5t-111 26.5q-110 0 -187.5 -71.5t-88.5 -186.5h374v-52h-376q6 -121 84.5 -198.5t193.5 -77.5q59 0 111 26.5t84 70.5l48 -30q-96 -119 -243 -119z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="581" 
d="M290 -12q-157 0 -244 106l37 43q83 -97 208 -97q93 0 134.5 40.5t41.5 92.5q0 40 -22 67.5t-57.5 42.5t-78 27t-85 25.5t-78 32t-57.5 52t-22 80.5q0 80 63 129t156 49q143 0 226 -94l-38 -42q-70 84 -191 84q-67 0 -111.5 -34.5t-44.5 -88.5q0 -34 22 -58.5t57.5 -38
t78 -25t85 -26t78 -34.5t57.5 -56.5t22 -87.5q0 -76 -57 -132.5t-180 -56.5z" />
    <glyph glyph-name="afii10055" unicode="&#x406;" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667zM251 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM47 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="470" 
d="M185 -12q-108 0 -171 82l35 43q56 -73 133 -73q67 0 107 42.5t40 110.5v474h58v-474q0 -100 -57 -152.5t-145 -52.5z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="1051" 
d="M35 -12v52q46 0 75 22.5t53 89t40 186.5l43 329h375v-277h192q92 0 147 -56.5t55 -138.5t-54.5 -138.5t-147.5 -56.5h-250v615h-266l-36 -282q-11 -88 -28 -150.5t-36 -100t-46 -59t-53.5 -28.5t-62.5 -7zM807 338h-186v-286h186q66 0 106.5 40t40.5 103t-40.5 103
t-106.5 40z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="1051" 
d="M83 0v667h57v-298h423v298h58v-298h203q87 0 139 -53.5t52 -131.5t-51.5 -131t-139.5 -53h-261v317h-423v-317h-57zM621 52h198q60 0 97.5 37t37.5 95q0 59 -37.5 96t-97.5 37h-198v-265z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="781" 
d="M646 0v218q0 81 -44 124t-127 43q-82 0 -164 -21v-364h-58v615h-218v52h494v-52h-218v-200q86 22 167 22q111 0 168.5 -59.5t57.5 -159.5v-218h-58z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="586" 
d="M459 867l-172 -144h-44l154 144h62zM562 0h-73l-275 314l-74 -81v-233h-57v667h57v-365l322 365h73l-284 -318z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="618" 
d="M141 -12q-83 0 -126 59l31 43q40 -50 93 -50q37 0 60 21t54 79l23 43l-261 484h66l228 -431l228 431h66l-300 -554q-35 -65 -70 -95t-92 -30zM485 796q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="703" 
d="M83 0v667h57v-615h423v615h58v-667h-240v-127h-58v127h-240z" />
    <glyph glyph-name="afii10017" unicode="&#x410;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" 
d="M83 667h416v-52h-359v-225h193q92 0 146.5 -56.5t54.5 -138.5t-54 -138.5t-147 -56.5h-250v667zM327 338h-187v-286h187q66 0 106.5 40t40.5 103t-40.5 103t-106.5 40z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="614" 
d="M363 0h-280v667h274q84 0 134 -46t50 -124q0 -60 -34.5 -101.5t-82.5 -51.5q53 -8 91.5 -56.5t38.5 -107.5q0 -83 -51 -131.5t-140 -48.5zM350 368q62 0 96.5 34.5t34.5 88.5q0 53 -34.5 88.5t-96.5 35.5h-210v-247h210zM354 52q66 0 103 35.5t37 96.5q0 55 -37 93.5
t-103 38.5h-214v-264h214z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="542" 
d="M140 0h-57v667h423v-52h-366v-615z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="717" 
d="M35 -123v172q47 1 75.5 21t52.5 84t40 184l43 329h375v-615h59v-175h-57v123h-530v-123h-58zM261 333q-29 -225 -114 -281h416v563h-266z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="564" 
d="M506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="892" 
d="M97 0h-73l311 349l-284 318h73l293 -333v333h58v-333l292 333h74l-284 -318l311 -349h-73l-275 314l-45 -50v-264h-58v264l-45 50z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="573" 
d="M274 -12q-82 0 -145.5 31.5t-96.5 79.5l39 35q30 -42 83.5 -68t119.5 -26q83 0 131.5 38t48.5 104q0 68 -50 101.5t-132 33.5h-138v52h138q74 0 122.5 32t48.5 95q0 60 -49 94.5t-121 34.5q-112 0 -192 -85l-35 36q38 45 97.5 73t132.5 28q95 0 161 -47.5t66 -128.5
q0 -47 -25 -83t-55 -51.5t-63 -21.5q54 -6 103.5 -49t49.5 -117q0 -84 -65 -137.5t-174 -53.5z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="699" 
d="M139 0h-56v667h57v-565l418 565h58v-667h-57v573z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="699" 
d="M139 0h-56v667h57v-565l418 565h58v-667h-57v573zM525 796q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="586" 
d="M562 0h-73l-275 314l-74 -81v-233h-57v667h57v-365l322 365h73l-284 -318z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="703" 
d="M35 -12v52q46 0 75 22.5t53 89t40 186.5l43 329h375v-667h-58v615h-266l-36 -282q-11 -88 -28 -150.5t-36 -100t-46 -59t-53.5 -28.5t-62.5 -7z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="790" 
d="M707 0h-58v589l-243 -589h-22l-244 589v-589h-57v667h85l227 -550l226 550h86v-667z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="703" 
d="M621 0h-58v317h-423v-317h-57v667h57v-298h423v298h58v-667z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="703" 
d="M621 0h-58v615h-423v-615h-57v667h538v-667z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" 
d="M140 0h-57v667h250q93 0 147 -56.5t54 -138.5t-54.5 -138.5t-146.5 -56.5h-193v-277zM327 329q66 0 106.5 40t40.5 103t-40.5 103t-106.5 40h-187v-286h187z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="672" 
d="M396 -12q-144 0 -241 96.5t-97 248.5t97 248.5t241 96.5q148 0 243 -118l-48 -31q-32 44 -84 70.5t-111 26.5q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -210.5t198.5 -82.5q59 0 111 26.5t84 70.5l48 -30q-96 -119 -243 -119z" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="564" 
d="M311 0h-58v615h-218v52h494v-52h-218v-615z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="618" 
d="M141 -12q-83 0 -126 59l31 43q40 -50 93 -50q37 0 60 21t54 79l23 43l-261 484h66l228 -431l228 431h66l-300 -554q-35 -65 -70 -95t-92 -30z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="785" 
d="M421 0h-57v65q-140 7 -223 81t-83 187q0 112 83 186.5t223 81.5v66h57v-66q140 -7 223 -81.5t83 -186.5q0 -113 -83 -187t-223 -81v-65zM118 333q0 -91 66 -150t180 -66v432q-114 -7 -180 -66t-66 -150zM666 333q0 91 -66 150t-179 66v-432q112 7 178.5 66t66.5 150z" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="645" 
d="M626 0h-71l-233 301l-232 -301h-72l269 342l-254 325h73l216 -283l217 283h72l-253 -324z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="717" 
d="M623 -123v123h-540v667h57v-615h423v615h58v-615h59v-175h-57z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="595" 
d="M119 667v-171q0 -81 44.5 -124t127.5 -43q82 0 164 21v317h57v-667h-57v299q-86 -22 -168 -22q-110 0 -167.5 59.5t-57.5 159.5v171h57z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="962" 
d="M822 667h57v-667h-796v667h57v-615h312v615h58v-615h312v615z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="984" 
d="M831 667h57v-615h60v-175h-57v123h-799v667h58v-615h311v615h58v-615h312v615z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="741" 
d="M503 0h-250v615h-218v52h276v-277h192q92 0 147 -56.5t55 -138.5t-54.5 -138.5t-147.5 -56.5zM497 338h-186v-286h186q66 0 106.5 40t40.5 103t-40.5 103t-106.5 40z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="754" 
d="M83 667h57v-277h193q92 0 146.5 -56.5t54.5 -138.5t-54 -138.5t-147 -56.5h-250v667zM327 338h-187v-286h187q66 0 106.5 40t40.5 103t-40.5 103t-106.5 40zM671 0h-57v667h57v-667z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" 
d="M83 667h57v-277h193q92 0 146.5 -56.5t54.5 -138.5t-54 -138.5t-147 -56.5h-250v667zM327 338h-187v-286h187q66 0 106.5 40t40.5 103t-40.5 103t-106.5 40z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="672" 
d="M276 -12q-146 0 -244 119l48 30q33 -44 85 -70.5t111 -26.5q114 0 192.5 77.5t83.5 198.5h-374v52h373q-11 115 -88.5 186.5t-186.5 71.5q-59 0 -111 -26.5t-85 -70.5l-47 31q93 118 243 118q143 0 240.5 -97t97.5 -248t-97.5 -248t-240.5 -97z" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="974" 
d="M592 -12q-141 0 -230 93t-94 236h-128v-317h-57v667h57v-298h129q11 135 99 222t224 87q144 0 234 -98t90 -247t-90 -247t-234 -98zM592 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82t-191.5 -82t-72.5 -211q0 -128 72.5 -210.5t191.5 -82.5z" />
    <glyph glyph-name="afii10049" unicode="&#x42f;" horiz-adv-x="594" 
d="M120 0h-69l193 280q-79 3 -132 54t-53 138q0 89 57.5 142t145.5 53h249v-667h-58v276h-146zM267 328h186v287h-186q-66 0 -107 -40t-41 -103t41 -103.5t107 -40.5z" />
    <glyph glyph-name="afii10065" unicode="&#x430;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="577" 
d="M285 35q82 0 129.5 61t47.5 147q0 84 -47.5 144.5t-129.5 60.5q-80 0 -128 -60.5t-48 -144.5q0 -86 47.5 -147t128.5 -61zM285 -12q-105 0 -168 76.5t-63 199.5q0 172 62.5 252.5t196.5 97.5q80 13 110 25t30 28h49q0 -24 -14.5 -41.5t-44.5 -28.5t-54 -16.5t-64 -11.5
q-79 -12 -126 -40t-65.5 -62t-30.5 -89q65 117 189 117q100 0 162.5 -72.5t62.5 -179.5q0 -109 -63.5 -182t-168.5 -73z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="541" 
d="M333 0h-252v483h248q60 0 95.5 -33.5t35.5 -90.5q0 -41 -22.5 -70.5t-54.5 -39.5q36 -10 61 -44t25 -74q0 -61 -36.5 -96t-99.5 -35zM322 47q43 0 67.5 23.5t24.5 64.5q0 37 -24.5 64t-67.5 27h-189v-179h189zM320 272q41 0 63.5 23t22.5 59q0 38 -22.5 60.5t-63.5 22.5
h-187v-165h187z" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="425" 
d="M398 483v-46h-265v-437h-52v483h317z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="553" 
d="M18 -123v166q38 5 62 47t39 152l32 241h308v-436h58v-170h-52v123h-394v-123h-53zM198 437l-27 -201q-21 -151 -75 -189h311v390h-209z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="567" 
d="M295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="695" 
d="M89 0h-70l224 265l-225 218h70l233 -228v228h53v-229l234 229h69l-225 -218l224 -265h-70l-192 230l-40 -38v-192h-53v193l-39 37z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="519" 
d="M249 -12q-137 0 -215 80l27 34q67 -69 188 -69q72 0 116 26.5t44 71.5q0 92 -170 92h-112v44h112q69 0 115 24.5t46 70.5q0 39 -45.5 63.5t-114.5 24.5q-106 0 -167 -64l-30 32q77 77 195 77q95 0 156 -34.5t61 -94.5q0 -52 -41.5 -83t-90.5 -37q52 -4 97 -33.5t45 -84.5
q0 -62 -58 -101t-158 -39z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="540" 
d="M131 0h-50v483h52v-402l275 402h51v-483h-52v408z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="540" 
d="M131 0h-50v483h52v-402l275 402h51v-483h-52v408zM445 629q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="505" 
d="M486 0h-70l-193 230l-90 -85v-145h-52v483h52v-276l285 276h69l-225 -218z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="540" 
d="M18 -12v45q37 0 61.5 48t39.5 161l32 241h308v-483h-52v437h-209l-27 -201q-18 -135 -54 -191.5t-99 -56.5z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="618" 
d="M537 0h-53v412l-165 -412h-19l-167 412v-412h-52v483h70l158 -392l156 392h72v-483z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" horiz-adv-x="540" 
d="M133 0h-52v483h52v-210h274v210h52v-483h-52v227h-274v-227z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" 
d="M285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" horiz-adv-x="540" 
d="M459 0h-52v437h-274v-437h-52v483h378v-483z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" horiz-adv-x="567" 
d="M301 -12q-104 0 -168 90v-262h-52v667h52v-76q27 39 71.5 63.5t96.5 24.5q97 0 156.5 -68.5t59.5 -184.5q0 -115 -59.5 -184.5t-156.5 -69.5zM292 35q79 0 124 58t45 149q0 90 -45 148t-124 58q-49 0 -93 -25t-66 -61v-239q22 -36 66.5 -62t92.5 -26z" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="494" 
d="M288 -12q-105 0 -170 73t-65 181t65 180.5t170 72.5q106 0 171 -82l-36 -32q-49 67 -132 67t-132.5 -58.5t-49.5 -147.5t49.5 -148t132.5 -59q82 0 132 68l36 -32q-65 -83 -171 -83z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="391" 
d="M221 0h-52v437h-142v46h337v-46h-143v-437z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="481" 
d="M49 -189l9 48q18 -8 42 -8q49 0 73 59l38 86l-204 487h58l176 -422l174 422h59l-250 -589q-38 -90 -124 -90q-28 0 -51 7z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="772" 
d="M228 495q81 0 132 -90v262h52v-260q21 39 56.5 63.5t75.5 24.5q76 0 123 -69t47 -184t-47 -184.5t-123 -69.5q-81 0 -132 90v-262h-52v261q-21 -39 -56.5 -64t-75.5 -25q-76 0 -123 69t-47 184t47 184.5t123 69.5zM239 448q-60 0 -94 -58t-34 -149t34 -148.5t94 -57.5
q37 0 70.5 25t50.5 62v238q-17 37 -50.5 62.5t-70.5 25.5zM533 35q60 0 94 58t34 149t-34 148.5t-94 57.5q-37 0 -70.5 -25t-50.5 -61v-239q17 -37 50.5 -62.5t70.5 -25.5z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="479" 
d="M462 0h-62l-160 213l-161 -213h-62l191 248l-180 235h62l150 -199l149 199h62l-180 -235z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="553" 
d="M465 -123v123h-384v483h52v-436h274v436h52v-436h58v-170h-52z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="535" 
d="M76 483h52v-140q0 -73 31.5 -101t98.5 -28q85 0 144 33v236h52v-483h-52v204q-70 -36 -154 -36q-90 0 -131 37.5t-41 125.5v152z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="777" 
d="M644 483h52v-483h-615v483h52v-436h229v436h52v-436h230v436z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="793" 
d="M644 483h52v-436h61v-170h-52v123h-624v483h52v-436h229v436h52v-436h230v436z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="597" 
d="M409 0h-240v437h-142v46h194v-197h188q69 0 108 -41t39 -102t-39 -102t-108 -41zM405 240h-184v-193h184q46 0 71 26.5t25 69.5q0 44 -25 70.5t-71 26.5z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="684" 
d="M81 483h52v-197h188q69 0 108 -41t39 -102t-39 -102t-108 -41h-240v483zM317 240h-184v-193h184q46 0 71 26.5t25 69.5q0 44 -25 70.5t-71 26.5zM603 0h-53v483h53v-483z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="509" 
d="M81 483h52v-197h188q69 0 108 -41t39 -102t-39 -102t-108 -41h-240v483zM317 240h-184v-193h184q46 0 71 26.5t25 69.5q0 44 -25 70.5t-71 26.5z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="494" 
d="M206 -12q-106 0 -171 83l35 31q52 -69 133 -69q79 0 127.5 52.5t53.5 136.5h-261v47h261q-8 81 -56 131t-125 50q-82 0 -133 -68l-35 31q65 82 171 82q105 0 170 -72.5t65 -180.5t-65 -181t-170 -73z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="750" 
d="M464 -12q-100 0 -163.5 68t-68.5 171h-99v-227h-52v483h52v-210h100q10 97 72.5 159.5t158.5 62.5q105 0 168.5 -72.5t63.5 -180.5q0 -109 -63.5 -181.5t-168.5 -72.5zM464 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5
q0 -86 47.5 -146.5t128.5 -60.5z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" horiz-adv-x="537" 
d="M128 0h-62l135 198q-59 4 -95 42.5t-36 99.5q0 62 39.5 102.5t107.5 40.5h240v-483h-53v197h-147zM221 243h183v194h-183q-46 0 -71.5 -26.5t-25.5 -70.5t25 -70.5t72 -26.5z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="567" 
d="M425 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM221 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137
t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="541" 
d="M296 538h-162v-128q29 34 77 59.5t97 25.5q152 0 152 -155v-402q0 -66 -34 -100t-97 -34q-56 0 -99 37l23 41q33 -33 71 -33t61 22t23 67v391q0 66 -30 92.5t-87 26.5q-45 0 -89 -24t-68 -59v-365h-52v538h-72v38h72v91h52v-91h162v-38z" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="425" 
d="M401 700l-172 -144h-44l154 144h62zM398 483v-46h-265v-437h-52v483h317z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="494" 
d="M288 -12q-105 0 -170 73t-65 181t65 180.5t170 72.5q106 0 171 -82l-35 -31q-50 68 -133 68q-76 0 -124.5 -50t-56.5 -131h261v-47h-262q6 -84 55 -136.5t127 -52.5q83 0 133 69l35 -31q-65 -83 -171 -83z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="459" 
d="M225 -12q-117 0 -186 76l31 39q25 -31 66 -51t90 -20q59 0 94 27.5t35 70.5q0 36 -31.5 57.5t-76 32t-89.5 23t-76.5 41.5t-31.5 77q0 57 46.5 95.5t125.5 38.5q109 0 173 -71l-29 -37q-47 64 -144 64q-55 0 -88 -25t-33 -63q0 -33 31.5 -52t76 -29.5t89.5 -23.5
t76.5 -45t31.5 -83q0 -62 -47 -102t-134 -40z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="214" 
d="M107 559q-16 0 -28 12t-12 28t12 27.5t28 11.5q17 0 28.5 -11.5t11.5 -27.5q0 -17 -11.5 -28.5t-28.5 -11.5zM133 0h-52v483h52v-483z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="214" 
d="M246 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM42 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM133 0h-52v483h52v-483z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="214" 
d="M107 559q-16 0 -28 12t-12 28t12 27.5t28 11.5q17 0 28.5 -11.5t11.5 -27.5q0 -17 -11.5 -28.5t-28.5 -11.5zM2 -196q-56 0 -99 37l23 41q31 -33 71 -33q38 0 61 22t23 67v545h52v-545q0 -66 -34 -100t-97 -34z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="836" 
d="M18 -12v45q37 0 61.5 48t39.5 161l32 241h308v-197h188q69 0 108 -41t39 -102t-39 -102t-108 -41h-240v437h-209l-27 -201q-18 -135 -54 -191.5t-99 -56.5zM643 240h-184v-193h184q46 0 71 26.5t25 69.5q0 44 -24.5 70.5t-71.5 26.5z" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="836" 
d="M133 0h-52v483h52v-210h274v210h52v-210h196q66 0 102.5 -39.5t36.5 -97.5t-37 -97t-102 -39h-248v227h-274v-227zM650 227h-191v-180h191q43 0 66 24.5t23 64.5q0 41 -22.5 66t-66.5 25z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="541" 
d="M296 538h-162v-128q29 34 77 59.5t97 25.5q152 0 152 -155v-340h-52v329q0 66 -30 92.5t-87 26.5q-45 0 -89 -24t-68 -59v-365h-52v538h-72v37h72v92h52v-92h162v-37z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" horiz-adv-x="505" 
d="M415 700l-172 -144h-44l154 144h62zM486 0h-70l-193 230l-90 -85v-145h-52v483h52v-276l285 276h69l-225 -218z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="481" 
d="M49 -189l9 48q18 -8 42 -8q49 0 73 59l38 86l-204 487h58l176 -422l174 422h59l-250 -589q-38 -90 -124 -90q-28 0 -51 7zM416 629q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="540" 
d="M244 -126v126h-163v483h52v-436h274v436h52v-483h-163v-126h-52z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="542" 
d="M140 615v-615h-57v667h365v124h58v-176h-366z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="425" 
d="M133 437v-437h-52v483h264v126h53v-172h-265z" />
    <glyph glyph-name="uni1E02" unicode="&#x1e02;" horiz-adv-x="614" 
d="M363 0h-280v667h274q84 0 134 -46t50 -124q0 -60 -34.5 -101.5t-82.5 -51.5q53 -8 91.5 -56.5t38.5 -107.5q0 -83 -51 -131.5t-140 -48.5zM350 368q62 0 96.5 34.5t34.5 88.5q0 53 -34.5 88.5t-96.5 35.5h-210v-247h210zM354 52q66 0 103 35.5t37 96.5q0 55 -37 93.5
t-103 38.5h-214v-264h214zM308 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E03" unicode="&#x1e03;" 
d="M133 121q22 -36 66 -61t93 -25q79 0 124 58t45 148q0 91 -45 149t-124 58q-48 0 -92.5 -26t-66.5 -62v-239zM133 0h-52v667h52v-262q64 90 168 90q97 0 156.5 -69.5t59.5 -184.5t-59.5 -184t-156.5 -69q-52 0 -96.5 25t-71.5 64v-77zM293 728q-16 0 -27 11t-11 27
q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E0A" unicode="&#x1e0a;" horiz-adv-x="692" 
d="M296 0h-213v667h213q151 0 244 -96.5t93 -237.5q0 -142 -92.5 -237.5t-244.5 -95.5zM296 52q128 0 202.5 80t74.5 201t-74 201.5t-203 80.5h-156v-563h156zM346 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E0B" unicode="&#x1e0b;" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v262h53v-667zM278 35q49 0 93 25t66 61v239q-22 37 -66 62.5t-93 25.5q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58zM280 728q-16 0 -27 11t-11 27
q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E1E" unicode="&#x1e1e;" horiz-adv-x="542" 
d="M295 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11zM140 0h-57v667h423v-52h-366v-247h359v-52h-359v-316z" />
    <glyph glyph-name="uni1E1F" unicode="&#x1e1f;" horiz-adv-x="265" 
d="M230 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11zM150 0h-52v437h-80v46h80v44q0 70 35 110t96 40q53 0 92 -34l-25 -36q-27 25 -62 25q-84 0 -84 -105v-44h98v-46h-98v-437z" />
    <glyph glyph-name="uni1E22" unicode="&#x1e22;" horiz-adv-x="703" 
d="M621 0h-58v317h-423v-317h-57v667h57v-298h423v298h58v-667zM352 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E23" unicode="&#x1e23;" horiz-adv-x="540" 
d="M459 0h-52v329q0 66 -30 92.5t-87 26.5q-45 0 -89 -24t-68 -59v-365h-52v667h52v-257q29 34 77 59.5t97 25.5q152 0 152 -155v-340zM271 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E56" unicode="&#x1e56;" 
d="M140 0h-57v667h250q93 0 147 -56.5t54 -138.5t-54.5 -138.5t-146.5 -56.5h-193v-277zM327 329q66 0 106.5 40t40.5 103t-40.5 103t-106.5 40h-187v-286h187zM286 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E57" unicode="&#x1e57;" horiz-adv-x="567" 
d="M301 -12q-104 0 -168 90v-262h-52v667h52v-76q27 39 71.5 63.5t96.5 24.5q97 0 156.5 -68.5t59.5 -184.5q0 -115 -59.5 -184.5t-156.5 -69.5zM292 35q79 0 124 58t45 149q0 90 -45 148t-124 58q-49 0 -93 -25t-66 -61v-239q22 -36 66.5 -62t92.5 -26zM291 561
q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E60" unicode="&#x1e60;" horiz-adv-x="581" 
d="M290 -12q-157 0 -244 106l37 43q83 -97 208 -97q93 0 134.5 40.5t41.5 92.5q0 40 -22 67.5t-57.5 42.5t-78 27t-85 25.5t-78 32t-57.5 52t-22 80.5q0 80 63 129t156 49q143 0 226 -94l-38 -42q-70 84 -191 84q-67 0 -111.5 -34.5t-44.5 -88.5q0 -34 22 -58.5t57.5 -38
t78 -25t85 -26t78 -34.5t57.5 -56.5t22 -87.5q0 -76 -57 -132.5t-180 -56.5zM291 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E61" unicode="&#x1e61;" horiz-adv-x="459" 
d="M225 -12q-117 0 -186 76l31 39q25 -31 66 -51t90 -20q59 0 94 27.5t35 70.5q0 36 -31.5 57.5t-76 32t-89.5 23t-76.5 41.5t-31.5 77q0 57 46.5 95.5t125.5 38.5q109 0 173 -71l-29 -37q-47 64 -144 64q-55 0 -88 -25t-33 -63q0 -33 31.5 -52t76 -29.5t89.5 -23.5
t76.5 -45t31.5 -83q0 -62 -47 -102t-134 -40zM230 561q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E6A" unicode="&#x1e6a;" horiz-adv-x="564" 
d="M311 0h-58v615h-218v52h494v-52h-218v-615zM282 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E6B" unicode="&#x1e6b;" horiz-adv-x="275" 
d="M186 -12q-95 0 -95 105v344h-80v46h80v132h53v-132h98v-46h-98v-336q0 -66 50 -66q33 0 54 23l20 -40q-32 -30 -82 -30zM125 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="867" 
d="M658 0h-64l-160 573l-160 -573h-64l-191 667h64l161 -587l164 587h52l163 -587l161 587h64zM488 723h-44l-172 144h62z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="719" 
d="M545 0h-50l-135 413l-136 -413h-50l-157 483h56l128 -410l136 410h45l136 -410l128 410h56zM414 556h-44l-172 144h62z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="867" 
d="M596 867l-172 -144h-44l154 144h62zM658 0h-64l-160 573l-160 -573h-64l-191 667h64l161 -587l164 587h52l163 -587l161 587h64z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="719" 
d="M522 700l-172 -144h-44l154 144h62zM545 0h-50l-135 413l-136 -413h-50l-157 483h56l128 -410l136 410h45l136 -410l128 410h56z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="867" 
d="M573 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM369 768q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM658 0h-64l-160 573l-160 -573h-64l-191 667h64l161 -587l164 587h52l163 -587l161 587h64z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="719" 
d="M498 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM294 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM545 0h-50l-135 413l-136 -413h-50l-157 483h56l128 -410l136 410h45l136 -410l128 410h56z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM324 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM262 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM280 758l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM217 588l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="647" 
d="M450 682h-40l-86 85l-85 -85h-39l97 108h54zM632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM634 845l-172 -108h-44l154 108h62z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM390 556h-40l-86 113l-85 -113h-39l97 144h54zM567 770l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="647" 
d="M450 682h-40l-86 85l-85 -85h-39l97 108h54zM632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM230 737h-44l-172 108h62z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM390 556h-40l-86 113l-85 -113h-39l97 144h54zM176 626h-44l-172 144h62z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="647" 
d="M450 682h-40l-86 85l-85 -85h-39l97 108h54zM632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM410 790l-26 6q16 40 70 40q29 0 48.5 -13t19.5 -38q0 -19 -20 -41h-34q25 19 25 41q0 31 -39 31q-32 0 -44 -26z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM390 556h-40l-86 113l-85 -113h-39l97 144h54zM305 728l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="647" 
d="M450 678h-40l-86 56l-85 -56h-39l97 72h54zM632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM384 757q-40 0 -71 25.5t-55 25.5q-54 0 -54 -48h-35q0 29 24.5 48t68.5 19q40 0 70.5 -25.5t54.5 -25.5q53 0 53 48h36q0 -30 -24 -48.5
t-68 -18.5z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM390 523h-40l-86 113l-85 -113h-39l97 144h54zM321 682q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5
t-68 -37.5z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM449 723h-40l-86 113l-85 -113h-39l97 144h54zM324 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM385 556h-40l-86 113l-85 -113h-39l97 144h54zM262 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM484 841l-172 -103h-44l154 103h62zM498 769q-66 -82 -175 -82t-176 82l29 23q53 -69 147 -69q92 0 147 69z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM426 761l-172 -144h-44l154 144h62zM438 612q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM381 738h-44l-172 103h62zM498 766q-66 -82 -175 -82t-176 82l29 23q53 -69 147 -69q92 0 147 69z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM315 617h-44l-172 144h62zM438 616q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM280 786l-26 5q17 39 70 39q29 0 48.5 -12t19.5 -37q0 -19 -20 -39h-34q25 18 25 39q0 30 -39 30q-32 0 -44 -25zM498 768q-66 -82 -175 -82t-176 82l29 23q53 -69 147 -69
q92 0 147 69z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM219 675l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35zM438 616q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM499 736q-66 -56 -175 -56q-108 0 -176 56l29 16q55 -48 147 -48q91 0 147 48zM384 757q-40 0 -71 25.5t-55 25.5q-54 0 -54 -48h-35q0 29 24.5 48t68.5 19q40 0 70.5 -25.5
t54.5 -25.5q53 0 53 48h36q0 -30 -24 -48.5t-68 -18.5z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM438 616q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75zM321 657q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24
t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="647" 
d="M632 0h-66l-66 164h-353l-66 -164h-66l273 667h71zM481 216l-158 390l-157 -390h315zM324 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11zM498 796q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z
" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="521" 
d="M440 0h-52v57q-63 -69 -164 -69q-67 0 -118 43.5t-51 117.5q0 75 50.5 117.5t118.5 42.5q102 0 164 -69v98q0 52 -37 81.5t-93 29.5q-89 0 -151 -71l-29 36q75 81 186 81q78 0 127 -39t49 -116v-340zM242 29q95 0 146 66v106q-52 67 -146 67q-59 0 -95.5 -34t-36.5 -86
t36.5 -85.5t95.5 -33.5zM262 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11zM435 629q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="564" 
d="M506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52zM300 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="567" 
d="M295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351zM288 -178q-16 0 -27 11
t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="564" 
d="M506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52zM242 758l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="567" 
d="M295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351zM242 588l-26 8q17 54 70 54
q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="564" 
d="M506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52zM357 721q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z
" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="567" 
d="M295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351zM346 554q-25 0 -43.5 16
t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="564" 
d="M424 682h-40l-86 85l-85 -85h-39l97 108h54zM608 845l-172 -108h-44l154 108h62zM506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="567" 
d="M411 556h-40l-86 113l-85 -113h-39l97 144h54zM588 770l-172 -144h-44l154 144h62zM295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266
q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="564" 
d="M422 682h-40l-86 85l-85 -85h-39l97 108h54zM506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52zM202 737h-44l-172 108h62z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="567" 
d="M410 556h-40l-86 113l-85 -113h-39l97 144h54zM295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5
t-49.5 -127.5h351zM196 626h-44l-172 144h62z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="564" 
d="M425 682h-40l-86 85l-85 -85h-39l97 108h54zM506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52zM385 790l-26 6q16 40 70 40q29 0 48.5 -13t19.5 -38q0 -19 -20 -41h-34q25 19 25 41q0 31 -39 31q-32 0 -44 -26z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="567" 
d="M409 556h-40l-86 113l-85 -113h-39l97 144h54zM295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5
t-49.5 -127.5h351zM324 728l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="564" 
d="M424 678h-40l-86 56l-85 -56h-39l97 72h54zM506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52zM358 757q-40 0 -71 25.5t-55 25.5q-54 0 -54 -48h-35q0 29 24.5 48t68.5 19q40 0 70.5 -25.5t54.5 -25.5q53 0 53 48h36q0 -30 -24 -48.5t-68 -18.5z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="567" 
d="M408 523h-40l-86 113l-85 -113h-39l97 144h54zM295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5
t-49.5 -127.5h351zM339 682q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="564" 
d="M418 723h-40l-86 113l-85 -113h-39l97 144h54zM506 0h-423v667h423v-52h-366v-247h359v-52h-359v-264h366v-52zM300 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="567" 
d="M412 556h-40l-86 113l-85 -113h-39l97 144h54zM295 -12q-106 0 -174 71t-68 183q0 106 66.5 179.5t166.5 73.5q106 0 167 -73.5t61 -182.5v-14h-404q4 -82 55 -137t134 -55q96 0 158 67l27 -34q-75 -78 -189 -78zM461 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5
t-49.5 -127.5h351zM288 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667zM69 758l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="214" 
d="M133 0h-52v483h52v-483zM64 588l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="223" 
d="M140 0h-57v667h57v-667zM113 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="214" 
d="M107 559q-16 0 -28 12t-12 28t12 27.5t28 11.5q17 0 28.5 -11.5t11.5 -27.5q0 -17 -11.5 -28.5t-28.5 -11.5zM133 0h-52v483h52v-483zM108 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5zM384 -178q-16 0 -27 11t-11 27q0 15 11 26.5
t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" 
d="M285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61zM286 -178q-16 0 -27 11t-11 27
q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5zM339 758l-26 8q17 54 70 54q29 0 48.5 -17
t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" 
d="M285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61zM243 588l-26 8q17 54 70 54q29 0 48.5 -17
t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="764" 
d="M507 682h-40l-86 85l-85 -85h-39l97 108h54zM691 845l-172 -108h-44l154 108h62zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82
t-72 -211q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" 
d="M412 556h-40l-86 113l-85 -113h-39l97 144h54zM589 770l-172 -144h-44l154 144h62zM285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5
q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="764" 
d="M507 682h-40l-86 85l-85 -85h-39l97 108h54zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z
M286 737h-44l-172 108h62z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" 
d="M411 556h-40l-86 113l-85 -113h-39l97 144h54zM285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146
t128.5 -61zM197 626h-44l-172 144h62z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="764" 
d="M506 682h-40l-86 85l-85 -85h-39l97 108h54zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z
M467 790l-26 6q16 40 70 40q29 0 48.5 -13t19.5 -38q0 -19 -20 -41h-34q25 19 25 41q0 31 -39 31q-32 0 -44 -26z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" 
d="M411 556h-40l-86 113l-85 -113h-39l97 144h54zM285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146
t128.5 -61zM326 728l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="764" 
d="M507 678h-40l-86 56l-85 -56h-39l97 72h54zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z
M442 757q-40 0 -71 25.5t-55 25.5q-54 0 -54 -48h-35q0 29 24.5 48t68.5 19q40 0 70.5 -25.5t54.5 -25.5q53 0 53 48h36q0 -30 -24 -48.5t-68 -18.5z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" 
d="M409 523h-40l-86 113l-85 -113h-39l97 144h54zM285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146
t128.5 -61zM340 682q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="764" 
d="M509 723h-40l-86 113l-85 -113h-39l97 144h54zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q144 0 234 -98t90 -247t-90 -247t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z
M386 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" 
d="M410 556h-40l-86 113l-85 -113h-39l97 144h54zM285 -12q-104 0 -168 73t-64 181t64 180.5t168 72.5q105 0 169 -72.5t64 -180.5t-64 -181t-169 -73zM285 35q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146
t128.5 -61zM286 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="764" 
d="M544 867l-172 -144h-44l154 144h62zM382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q152 0 245 -112q43 38 46 82h-4q-4 -1 -6 -1q-15 0 -25 10.5t-10 26.5q0 15 10.5 26t26.5 11q19 0 32 -15t13 -42q0 -37 -18 -70t-45 -54q59 -88 59 -207q0 -149 -90 -247
t-234 -98zM382 40q118 0 191 82.5t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" 
d="M448 700l-172 -144h-44l154 144h62zM517 530q0 -69 -55 -117q56 -70 56 -171q0 -108 -64 -181t-169 -73q-104 0 -168 73t-64 181t64 180.5t168 72.5q91 0 154 -58q38 36 41 77h-4q-4 -1 -6 -1q-15 0 -25 10.5t-10 26.5q0 15 10.5 26t26.5 11q19 0 32 -15t13 -42zM285 35
q82 0 129.5 60.5t47.5 146.5q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q152 0 245 -112q43 38 46 82h-4q-4 -1 -6 -1q-15 0 -25 10.5t-10 26.5q0 15 10.5 26t26.5 11q19 0 32 -15t13 -42q0 -37 -18 -70t-45 -54q59 -88 59 -207q0 -149 -90 -247t-234 -98zM382 40q118 0 191 82.5
t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5zM437 723h-44l-172 144h62z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" 
d="M517 530q0 -69 -55 -117q56 -70 56 -171q0 -108 -64 -181t-169 -73q-104 0 -168 73t-64 181t64 180.5t168 72.5q91 0 154 -58q38 36 41 77h-4q-4 -1 -6 -1q-15 0 -25 10.5t-10 26.5q0 15 10.5 26t26.5 11q19 0 32 -15t13 -42zM285 35q82 0 129.5 60.5t47.5 146.5
q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61zM340 556h-44l-172 144h62z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q152 0 245 -112q43 38 46 82h-4q-4 -1 -6 -1q-15 0 -25 10.5t-10 26.5q0 15 10.5 26t26.5 11q19 0 32 -15t13 -42q0 -37 -18 -70t-45 -54q59 -88 59 -207q0 -149 -90 -247t-234 -98zM382 40q118 0 191 82.5
t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5zM339 758l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" 
d="M517 530q0 -69 -55 -117q56 -70 56 -171q0 -108 -64 -181t-169 -73q-104 0 -168 73t-64 181t64 180.5t168 72.5q91 0 154 -58q38 36 41 77h-4q-4 -1 -6 -1q-15 0 -25 10.5t-10 26.5q0 15 10.5 26t26.5 11q19 0 32 -15t13 -42zM285 35q82 0 129.5 60.5t47.5 146.5
q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61zM243 588l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q152 0 245 -112q43 38 46 82h-4q-4 -1 -6 -1q-15 0 -25 10.5t-10 26.5q0 15 10.5 26t26.5 11q19 0 32 -15t13 -42q0 -37 -18 -70t-45 -54q59 -88 59 -207q0 -149 -90 -247t-234 -98zM382 40q118 0 191 82.5
t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5zM444 721q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36
q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" 
d="M517 530q0 -69 -55 -117q56 -70 56 -171q0 -108 -64 -181t-169 -73q-104 0 -168 73t-64 181t64 180.5t168 72.5q91 0 154 -58q38 36 41 77h-4q-4 -1 -6 -1q-15 0 -25 10.5t-10 26.5q0 15 10.5 26t26.5 11q19 0 32 -15t13 -42zM285 35q82 0 129.5 60.5t47.5 146.5
q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61zM347 554q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36
q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="764" 
d="M382 -12q-145 0 -234.5 98t-89.5 247t89.5 247t234.5 98q152 0 245 -112q43 38 46 82h-4q-4 -1 -6 -1q-15 0 -25 10.5t-10 26.5q0 15 10.5 26t26.5 11q19 0 32 -15t13 -42q0 -37 -18 -70t-45 -54q59 -88 59 -207q0 -149 -90 -247t-234 -98zM382 40q118 0 191 82.5
t73 210.5q0 129 -72.5 211t-191.5 82q-120 0 -192 -82t-72 -211q0 -128 72 -210.5t192 -82.5zM384 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" 
d="M517 530q0 -69 -55 -117q56 -70 56 -171q0 -108 -64 -181t-169 -73q-104 0 -168 73t-64 181t64 180.5t168 72.5q91 0 154 -58q38 36 41 77h-4q-4 -1 -6 -1q-15 0 -25 10.5t-10 26.5q0 15 10.5 26t26.5 11q19 0 32 -15t13 -42zM285 35q82 0 129.5 60.5t47.5 146.5
q0 85 -47.5 145.5t-129.5 60.5q-81 0 -128.5 -60.5t-47.5 -145.5t47.5 -146t128.5 -61zM286 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="686" 
d="M343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-411q0 -126 -66.5 -197t-193.5 -71zM348 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="540" 
d="M459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-483zM273 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="686" 
d="M343 -12q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-411q0 -126 -66.5 -197t-193.5 -71zM301 758l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" horiz-adv-x="540" 
d="M459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-483zM227 588l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="686" 
d="M505 867l-172 -144h-44l154 144h62zM721 729q0 -50 -32.5 -92t-85.5 -61v-320q0 -126 -66.5 -197t-193.5 -71q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-58q34 16 57 45t24 59q-1 -1 -9 -1q-16 0 -26 11
t-10 27q0 15 10.5 26t26.5 11q19 0 32 -15.5t13 -42.5z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" horiz-adv-x="540" 
d="M432 700l-172 -144h-44l154 144h62zM459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-59q59 38 61 93q-6 -1 -9 -1q-16 0 -26 10.5t-10 26.5q0 15 11 26t27 11q19 0 31.5 -15
t12.5 -42q0 -45 -26.5 -83.5t-71.5 -61.5v-388z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="686" 
d="M398 723h-44l-172 144h62zM721 729q0 -50 -32.5 -92t-85.5 -61v-320q0 -126 -66.5 -197t-193.5 -71q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-58q34 16 57 45t24 59q-1 -1 -9 -1q-16 0 -26 11t-10 27
q0 15 10.5 26t26.5 11q19 0 32 -15.5t13 -42.5z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" horiz-adv-x="540" 
d="M324 556h-44l-172 144h62zM459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-59q59 38 61 93q-6 -1 -9 -1q-16 0 -26 10.5t-10 26.5q0 15 11 26t27 11q19 0 31.5 -15t12.5 -42
q0 -45 -26.5 -83.5t-71.5 -61.5v-388z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="686" 
d="M301 758l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35zM721 729q0 -50 -32.5 -92t-85.5 -61v-320q0 -126 -66.5 -197t-193.5 -71q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5
q98 0 150 57t52 160v410h58v-58q34 16 57 45t24 59q-1 -1 -9 -1q-16 0 -26 11t-10 27q0 15 10.5 26t26.5 11q19 0 32 -15.5t13 -42.5z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" horiz-adv-x="540" 
d="M227 588l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35zM459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-59
q59 38 61 93q-6 -1 -9 -1q-16 0 -26 10.5t-10 26.5q0 15 11 26t27 11q19 0 31.5 -15t12.5 -42q0 -45 -26.5 -83.5t-71.5 -61.5v-388z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="686" 
d="M721 729q0 -50 -32.5 -92t-85.5 -61v-320q0 -126 -66.5 -197t-193.5 -71q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57t52 160v410h58v-58q34 16 57 45t24 59q-1 -1 -9 -1q-16 0 -26 11t-10 27q0 15 10.5 26t26.5 11
q19 0 32 -15.5t13 -42.5zM405 721q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" horiz-adv-x="540" 
d="M459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-59q59 38 61 93q-6 -1 -9 -1q-16 0 -26 10.5t-10 26.5q0 15 11 26t27 11q19 0 31.5 -15t12.5 -42q0 -45 -26.5 -83.5
t-71.5 -61.5v-388zM331 554q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="686" 
d="M348 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11zM721 729q0 -50 -32.5 -92t-85.5 -61v-320q0 -126 -66.5 -197t-193.5 -71q-126 0 -193 71.5t-67 196.5v411h58v-410q0 -102 52.5 -159.5t149.5 -57.5q98 0 150 57
t52 160v410h58v-58q34 16 57 45t24 59q-1 -1 -9 -1q-16 0 -26 11t-10 27q0 15 10.5 26t26.5 11q19 0 32 -15.5t13 -42.5z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" horiz-adv-x="540" 
d="M273 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11zM459 0h-52v71q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37.5t-38 117.5v340h52v-328q0 -67 30 -93.5t87 -26.5q45 0 88.5 23t68.5 57v368h52v-59q59 38 61 93
q-6 -1 -9 -1q-16 0 -26 10.5t-10 26.5q0 15 11 26t27 11q19 0 31.5 -15t12.5 -42q0 -45 -26.5 -83.5t-71.5 -61.5v-388z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="618" 
d="M338 0h-58v285l-265 382h69l225 -328l225 328h69l-265 -382v-285zM364 723h-44l-172 144h62z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="481" 
d="M49 -189l9 48q18 -8 42 -8q49 0 73 59l38 86l-204 487h58l176 -422l174 422h59l-250 -589q-38 -90 -124 -90q-28 0 -51 7zM295 556h-44l-172 144h62z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="52" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="593" 
d="M563 218h-533v48h533v-48z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="833" 
d="M803 218h-773v48h773v-48z" />
    <glyph glyph-name="afii00208" unicode="&#x2015;" horiz-adv-x="593" 
d="M563 218h-533v48h533v-48z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="219" 
d="M51 541q0 40 20.5 77t51.5 59l29 -24q-24 -16 -40 -42.5t-18 -50.5q0 2 10 2q18 0 29.5 -12t11.5 -30q0 -17 -12.5 -30t-30.5 -13q-21 0 -36 17t-15 47z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="219" 
d="M159 613q0 -40 -20.5 -77.5t-51.5 -58.5l-29 24q24 16 40 42.5t18 50.5q0 -2 -10 -2q-18 0 -29.5 12t-11.5 30q0 17 12.5 30t30.5 13q21 0 36 -17t15 -47z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="219" 
d="M159 14q0 -40 -20.5 -77.5t-51.5 -59.5l-29 25q24 16 40 42.5t18 49.5q-7 -1 -10 -1q-18 0 -29.5 12t-11.5 30q0 17 12.5 30t30.5 13q21 0 36 -17t15 -47z" />
    <glyph glyph-name="quotereversed" unicode="&#x201b;" horiz-adv-x="219" 
d="M60 613q0 30 15 47t36 17q18 0 30.5 -13t12.5 -30q0 -18 -11.5 -30t-29.5 -12q-9 0 -11 2q2 -24 18.5 -50.5t40.5 -42.5l-29 -24q-31 21 -51.5 58.5t-20.5 77.5z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="360" 
d="M60 541q0 40 20 77t51 59l30 -24q-24 -17 -41 -43t-18 -50q2 2 11 2q17 0 28.5 -12.5t11.5 -29.5q0 -18 -12.5 -30.5t-30.5 -12.5q-21 0 -35.5 17t-14.5 47zM202 541q0 40 20 77t51 59l30 -24q-24 -17 -41 -43t-18 -50q2 2 11 2q17 0 28.5 -12.5t11.5 -29.5
q0 -18 -12.5 -30.5t-30.5 -12.5q-21 0 -35.5 17t-14.5 47z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="360" 
d="M300 613q0 -41 -20 -78t-51 -58l-30 24q24 17 41 43t18 50q-2 -2 -11 -2q-17 0 -28.5 12t-11.5 30t12.5 30.5t30.5 12.5q21 0 35.5 -17t14.5 -47zM159 613q0 -40 -20.5 -77.5t-51.5 -58.5l-29 24q24 16 40 42.5t18 50.5q0 -2 -10 -2q-18 0 -29.5 12t-11.5 30
q0 17 12.5 30t30.5 13q21 0 36 -17t15 -47z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="361" 
d="M159 14q0 -40 -20.5 -77.5t-51.5 -59.5l-29 25q24 16 40 42.5t18 49.5q-7 -1 -10 -1q-18 0 -29.5 12t-11.5 30q0 17 12.5 30t30.5 13q21 0 36 -17t15 -47zM300 14q0 -40 -20 -77.5t-51 -59.5l-30 25q24 16 40.5 42.5t18.5 49.5h-4q-5 -1 -7 -1q-17 0 -28.5 12t-11.5 30
t12.5 30.5t30.5 12.5q21 0 35.5 -17t14.5 -47z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="257" 
d="M233 535l-89 4l4 -212h-39l4 212l-89 -4v35l89 -3l-4 110h39l-4 -110l89 3v-35z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="257" 
d="M233 570v-35l-89 4v-221l89 4v-35l-89 3l4 -110h-39l4 110l-89 -3v35l89 -4v221l-89 -4v35l89 -3l-4 110h39l-4 -110z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="358" 
d="M283 242q0 -42 -30 -72t-73 -30t-73 30t-30 72q0 43 30 73t73 30t73 -30t30 -73z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="657" 
d="M153 35q0 -18 -13 -31t-31 -13t-31 13t-13 31q0 17 13 30t31 13t31 -13t13 -30zM372 35q0 -18 -13 -31t-31 -13t-31 13t-13 31q0 17 13 30t31 13t31 -13t13 -30zM591 35q0 -18 -13 -31t-31 -13t-30.5 13t-12.5 31q0 17 12.5 30t30.5 13t31 -13t13 -30z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1067" 
d="M347 512q0 -70 -44 -116t-113 -46t-112.5 46t-43.5 116q0 71 44 118t112 47q69 0 113 -47t44 -118zM600 667l-427 -667h-45l426 667h46zM687 151q0 -70 -44 -116.5t-113 -46.5t-113 46.5t-44 116.5q0 71 44 118t113 47t113 -47t44 -118zM301 512q0 54 -31 91t-80 37
q-48 0 -79 -37t-31 -91q0 -53 31 -89.5t79 -36.5q49 0 80 36.5t31 89.5zM640 151q0 54 -30.5 90.5t-79.5 36.5t-80 -36.5t-31 -90.5q0 -53 31 -89.5t80 -36.5t79.5 36.5t30.5 89.5zM1034 151q0 -70 -44 -116.5t-113 -46.5t-113 46.5t-44 116.5q0 71 44 118t113 47t113 -47
t44 -118zM987 151q0 54 -30.5 90.5t-79.5 36.5t-80 -36.5t-31 -90.5q0 -53 31 -89.5t80 -36.5t79.5 36.5t30.5 89.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="277" 
d="M247 63h-57l-160 180l160 177h57l-160 -177z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="277" 
d="M247 243l-160 -180h-57l160 180l-160 177h57z" />
    <glyph glyph-name="uni203E" unicode="&#x203e;" horiz-adv-x="363" 
d="M363 577h-363v37h363v-37z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="130" 
d="M301 667l-427 -667h-45l426 667h46z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="384" 
d="M349 621q0 -51 -15 -96.5t-52 -78t-90 -32.5q-78 0 -117.5 62.5t-39.5 144.5q0 83 39.5 144.5t117.5 61.5t117.5 -62t39.5 -144zM302 621q0 71 -26.5 119.5t-83.5 48.5t-84 -49t-27 -119q0 -71 27 -120t84 -49t83.5 49t26.5 120z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="384" 
d="M343 528h-60v-107h-44v107h-196v35l179 258h61v-256h60v-37zM239 565v215l-150 -215h150z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="384" 
d="M335 544q0 -59 -41 -94.5t-103 -35.5q-98 0 -143 65l27 30q42 -57 116 -57q44 0 72 26t28 65q0 42 -27.5 67.5t-71.5 25.5q-58 0 -99 -40l-33 13v212h248v-38h-203v-149q37 37 99 37q55 0 93 -33t38 -94z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="384" 
d="M342 543q0 -53 -39.5 -91t-103.5 -38q-80 0 -118.5 58.5t-38.5 148.5q0 87 41.5 146.5t123.5 59.5q68 0 113 -48l-23 -32q-37 42 -90 42q-60 0 -90 -47.5t-31 -115.5q0 -13 1 -19q16 24 49 44.5t71 20.5q59 0 97 -33.5t38 -95.5zM297 542q0 45 -27.5 69t-72.5 24
q-31 0 -60.5 -17.5t-47.5 -46.5q4 -49 31 -84t78 -35q46 0 72.5 28t26.5 62z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="359" 
d="M315 790l-174 -369h-49l173 362h-230v38h280v-31z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="384" 
d="M338 522q0 -49 -42 -78.5t-104 -29.5t-104 29t-42 79q0 39 30 66.5t74 38.5q-41 10 -69 34.5t-28 60.5q0 52 41.5 78.5t97.5 26.5t97.5 -26.5t41.5 -78.5q0 -37 -27.5 -61t-68.5 -34q43 -11 73 -38.5t30 -66.5zM287 718q0 34 -27 53t-68 19t-68 -19t-27 -53q0 -53 95 -75
q95 21 95 75zM293 525q0 39 -35.5 60t-65.5 25q-17 -2 -37.5 -9.5t-42.5 -28t-22 -47.5q0 -34 30 -54.5t72 -20.5t71.5 20.5t29.5 54.5z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="384" 
d="M42 698q0 53 39 91t104 38q80 0 118.5 -58.5t38.5 -148.5q0 -87 -41.5 -146.5t-122.5 -59.5q-69 0 -114 48l23 32q34 -42 91 -42q59 0 88.5 47.5t30.5 115.5v19q-17 -24 -50 -44.5t-69 -20.5q-59 0 -97.5 33.5t-38.5 95.5zM86 699q0 -45 28 -69t72 -24q32 0 62 18t47 47
q-4 48 -31 83t-78 35q-46 0 -73 -28t-27 -62z" />
    <glyph glyph-name="parenleftsuperior" unicode="&#x207d;" horiz-adv-x="148" 
d="M138 368l-25 -12q-86 116 -86 265q0 148 86 265l25 -12q-65 -128 -65 -253q0 -126 65 -253z" />
    <glyph glyph-name="parenrightsuperior" unicode="&#x207e;" horiz-adv-x="148" 
d="M136 621q0 -149 -85 -265l-26 12q65 127 65 253q0 125 -65 253l26 12q85 -116 85 -265z" />
    <glyph glyph-name="nsuperior" unicode="&#x207f;" horiz-adv-x="393" 
d="M328 326h-43v207q0 79 -76 79q-29 0 -57.5 -15t-43.5 -36v-235h-43v314h43v-47q19 22 50.5 38.5t64.5 16.5q58 0 81.5 -27.5t23.5 -80.5v-214z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="384" 
d="M349 54q0 -51 -15 -96.5t-52 -78t-90 -32.5q-78 0 -117.5 62.5t-39.5 144.5q0 83 39.5 144.5t117.5 61.5t117.5 -62t39.5 -144zM302 54q0 71 -26.5 119.5t-83.5 48.5t-84 -49t-27 -119q0 -71 27 -120t84 -49t83.5 49t26.5 120z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="214" 
d="M154 -146h-46v341l-64 -69l-27 29l97 99h40v-400z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="384" 
d="M333 -146h-281v35q122 87 178.5 144.5t56.5 109.5q0 40 -27 59.5t-66 19.5q-75 0 -113 -52l-27 28q48 62 141 62q55 0 95.5 -29t40.5 -86q0 -60 -54.5 -119t-158.5 -134h215v-38z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="384" 
d="M334 -38q0 -51 -39 -83t-105 -32q-49 0 -87 18t-57 47l26 28q42 -55 117 -55q47 0 73.5 20.5t26.5 57.5q0 40 -30.5 59t-79.5 19q-32 0 -38 -1v39q7 -1 38 -1q46 0 75 17.5t29 54.5q0 34 -27.5 53t-68.5 19q-65 0 -111 -50l-25 27q52 61 139 61q60 0 98.5 -28.5
t38.5 -77.5q0 -41 -27.5 -64.5t-61.5 -28.5q34 -3 65 -28.5t31 -70.5z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="384" 
d="M343 -39h-60v-107h-44v107h-196v35l179 258h61v-256h60v-37zM239 -2v215l-150 -215h150z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="384" 
d="M335 -23q0 -59 -41 -94.5t-103 -35.5q-98 0 -143 65l27 30q42 -57 116 -57q44 0 72 26t28 65q0 42 -27.5 67.5t-71.5 25.5q-58 0 -99 -40l-33 13v212h248v-38h-203v-149q37 37 99 37q55 0 93 -33t38 -94z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="384" 
d="M342 -24q0 -53 -39.5 -91t-103.5 -38q-80 0 -118.5 58.5t-38.5 148.5q0 87 41.5 146.5t123.5 59.5q68 0 113 -48l-23 -32q-37 42 -90 42q-60 0 -90 -47.5t-31 -115.5q0 -13 1 -19q16 24 49 44.5t71 20.5q59 0 97 -33.5t38 -95.5zM297 -25q0 45 -27.5 69t-72.5 24
q-31 0 -60.5 -17.5t-47.5 -46.5q4 -49 31 -84t78 -35q46 0 72.5 28t26.5 62z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="359" 
d="M315 223l-174 -369h-49l173 362h-230v38h280v-31z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="384" 
d="M338 -45q0 -49 -42 -78.5t-104 -29.5t-104 29t-42 79q0 39 30 66.5t74 38.5q-41 10 -69 34.5t-28 60.5q0 52 41.5 78.5t97.5 26.5t97.5 -26.5t41.5 -78.5q0 -37 -27.5 -61t-68.5 -34q43 -11 73 -38.5t30 -66.5zM287 151q0 34 -27 53t-68 19t-68 -19t-27 -53q0 -53 95 -75
q95 21 95 75zM293 -42q0 39 -35.5 60t-65.5 25q-17 -2 -37.5 -9.5t-42.5 -28t-22 -47.5q0 -34 30 -54.5t72 -20.5t71.5 20.5t29.5 54.5z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="384" 
d="M42 131q0 53 39 91t104 38q80 0 118.5 -58.5t38.5 -148.5q0 -87 -41.5 -146.5t-122.5 -59.5q-69 0 -114 48l23 32q34 -42 91 -42q59 0 88.5 47.5t30.5 115.5v19q-17 -24 -50 -44.5t-69 -20.5q-59 0 -97.5 33.5t-38.5 95.5zM86 132q0 -45 28 -69t72 -24q32 0 62 18t47 47
q-4 48 -31 83t-78 35q-46 0 -73 -28t-27 -62z" />
    <glyph glyph-name="parenleftinferior" unicode="&#x208d;" horiz-adv-x="148" 
d="M138 -199l-25 -12q-86 116 -86 265q0 148 86 265l25 -12q-65 -128 -65 -253q0 -126 65 -253z" />
    <glyph glyph-name="parenrightinferior" unicode="&#x208e;" horiz-adv-x="148" 
d="M136 54q0 -149 -85 -265l-26 12q65 127 65 253q0 125 -65 253l26 12q85 -116 85 -265z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="674" 
d="M398 -12q-5 0 -14.5 0.5t-13.5 0.5l-29 -89h-40l30 94q-51 11 -84 27l-40 -121h-41l47 139q-71 45 -112 121.5t-41 172.5q0 152 97 248.5t241 96.5q11 0 29 -2l31 92h40l-33 -97q47 -11 84 -32l43 129h40l-51 -150q31 -23 60 -58l-48 -31q-13 18 -30 33l-175 -522h10
q59 0 111 26.5t84 70.5l48 -30q-96 -119 -243 -119zM120 333q0 -76 29.5 -138t82.5 -101l178 531q-2 0 -6 0.5t-6 0.5q-119 0 -198.5 -82t-79.5 -211zM265 72q38 -20 84 -28l182 543q-37 23 -82 34z" />
    <glyph glyph-name="frenchfranc" unicode="&#x20a3;" horiz-adv-x="553" 
d="M151 0h-57v149h-83v37h83v481h423v-52h-366v-247h359v-52h-359v-130h223v-37h-223v-149z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="505" 
d="M18 325v39h76q-44 57 -44 121q0 79 62 135.5t154 56.5q62 0 112.5 -29t75.5 -79l-46 -29q-15 35 -53 61t-85 26q-67 0 -113 -38.5t-46 -104.5q0 -64 49 -120h197v-39h-164q35 -42 44 -77h120v-38h-116q-2 -79 -81 -136q25 9 54 9q34 0 79.5 -22t74.5 -22q32 0 60 13
t40 28l27 -47q-49 -47 -129 -47q-47 0 -93 23.5t-86 23.5q-42 0 -118 -40l-22 48q69 29 105.5 72.5t36.5 92.5l-1 2v2h-170v38h163q-11 31 -53 77h-110z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="722" 
d="M627 0h-56l-180 246h-240v-246h-57v246h-83v37h83v107h-83v38h83v239h58l177 -239h241v239h57v-239h83v-38h-83v-107h83v-37h-83v-246zM151 390v-107h213l-79 107h-134zM357 390l79 -107h134v107h-213zM151 573v-145h106zM463 246l107 -144v144h-107z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="624" 
d="M620 451h-75q-8 -75 -61.5 -124.5t-139.5 -49.5h-192v-277h-58v451h-83v41h83v175h250q87 0 140.5 -50t60.5 -125h75v-41zM152 615v-123h332q-7 55 -46 89t-100 34h-186zM338 329q60 0 99.5 33.5t46.5 88.5h-332v-122h186z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="996" 
d="M761 -12q-117 0 -186 76l31 39q25 -31 66 -51t90 -20q59 0 94 27.5t35 70.5q0 36 -31.5 57.5t-76.5 32t-89.5 23t-76 41.5t-31.5 77q0 57 46.5 95.5t125.5 38.5q109 0 173 -71l-29 -37q-47 64 -144 64q-55 0 -88 -25t-33 -63q0 -33 31.5 -52t76.5 -29.5t89.5 -23.5
t76 -45t31.5 -83q0 -62 -47 -102t-134 -40zM543 0h-69l-187 276h-147v-276h-57v667h249q88 0 145.5 -53t57.5 -142q0 -87 -53.5 -138t-131.5 -54zM327 328q66 0 107 40.5t41 103.5t-41 103t-107 40h-187v-287h187z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="867" 
d="M658 0h-64l-69 246h-183l-68 -246h-64l-71 246h-115v38h104l-30 106h-74v38h63l-68 239h64l65 -239h193l67 239h52l66 -239h192l66 239h64l-68 -239h64v-38h-75l-30 -106h105v-38h-116zM159 390l29 -106h113l29 106h-171zM537 390l29 -106h113l29 106h-171zM382 390
l-29 -106h161l-29 106h-103zM198 246l46 -166l46 166h-92zM577 246l46 -166l45 166h-91zM434 573l-41 -145h81z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="810" 
d="M509 0h-231v393h52v-345h179q168 0 168 164v338h53v-340q0 -210 -221 -210zM312 550q220 0 220 -210v-184h-52v182q0 164 -168 164h-179v-502h-52v550h231z" />
    <glyph glyph-name="dong" unicode="&#x20ab;" horiz-adv-x="574" 
d="M482 -131h-363v37h363v-37zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v133h-161v37h161v92h53v-92h72v-37h-72v-538zM278 35q49 0 93 25t66 61v239q-22 37 -66 62.5t-93 25.5q-78 0 -123.5 -58.5
t-45.5 -148.5t45.5 -148t123.5 -58z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="690" 
d="M417 -12q-121 0 -210.5 69t-117.5 185h-54v41h47q-3 24 -3 50q0 27 3 52h-47v41h55q28 114 117.5 183t209.5 69q148 0 243 -118l-48 -31q-32 44 -84 70.5t-111 26.5q-95 0 -167.5 -54.5t-98.5 -145.5h326v-41h-335q-3 -25 -3 -52q0 -26 3 -50h335v-41h-326
q26 -92 98 -147t168 -55q59 0 111 26.5t84 70.5l48 -30q-96 -119 -243 -119z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="454" 
d="M133 296v-198q0 -30 12.5 -48t37.5 -18q33 0 54 23l20 -37q-30 -30 -82 -30q-94 0 -94 105v420q0 69 49 116.5t125 47.5q70 0 116 -33.5t46 -84.5q0 -42 -28 -75t-81 -69zM133 353l154 103q41 28 60 50.5t19 50.5q0 35 -33.5 57t-77.5 22q-51 0 -86.5 -34.5t-35.5 -89.5
v-159z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="1104" 
d="M901 350q-70 0 -114 47.5t-44 117.5t44 117.5t114 47.5q71 0 115 -47t44 -118q0 -70 -44 -117.5t-115 -47.5zM901 387q53 0 84 36.5t31 91.5q0 54 -31 90.5t-84 36.5q-52 0 -82.5 -36t-30.5 -91t30.5 -91.5t82.5 -36.5zM616 0h-56l-420 573v-573h-57v667h58l418 -565v565
h57v-667z" />
    <glyph glyph-name="published" unicode="&#x2117;" horiz-adv-x="778" 
d="M734 334q0 -142 -101 -243.5t-244 -101.5t-244 101.5t-101 243.5t101 243.5t244 101.5t244 -101.5t101 -243.5zM706 334q0 130 -93 223.5t-224 93.5t-224 -93.5t-93 -223.5t93 -223.5t224 -93.5t224 93.5t93 223.5zM547 421q0 -52 -36 -83t-89 -31h-112v-176h-33v405h145
q53 0 89 -30.5t36 -84.5zM514 421q0 38 -25.5 60.5t-66.5 22.5h-112v-165h112q41 0 66.5 22t25.5 60z" />
    <glyph glyph-name="servicemark" unicode="&#x2120;" horiz-adv-x="476" 
d="M439 447h-28v182l-75 -182h-8l-75 182v-182h-28v220h43l64 -160l64 160h43v-220zM188 506q0 -31 -22.5 -49t-64.5 -18q-55 0 -86 36l18 21q28 -32 66 -32q56 0 56 39q0 19 -21.5 30t-46.5 15t-46.5 19t-21.5 42q0 28 22.5 46t57.5 18q55 0 82 -32l-18 -19q-22 27 -63 27
q-21 0 -34.5 -10t-13.5 -26q0 -17 21 -27t47 -14.5t47 -20.5t21 -45z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="454" 
d="M419 447h-28v182l-75 -182h-8l-75 182v-182h-28v220h43l64 -160l64 160h43v-220zM169 641h-62v-194h-28v194h-62v26h152v-26z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="764" 
d="M77 52h141q-160 94 -160 289q0 146 90 241.5t234 95.5q145 0 234.5 -95.5t89.5 -241.5q0 -195 -159 -289h142v-52h-228v52q74 18 129.5 92.5t55.5 188.5q0 127 -71 209.5t-193 82.5t-193 -82.5t-71 -209.5q0 -114 56 -188t130 -93v-52h-227v52z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="842" 
d="M811 324h-632q-5 0 -5 -5v-190q0 -14 9 -23q98 -103 240 -103q151 0 250 115h57q-54 -63 -135 -99.5t-174 -36.5q-161 0 -275.5 103t-114.5 248t114.5 248t275.5 103t275.5 -103t114.5 -248v-9zM668 349v191q0 14 -10 24q-97 99 -235 99q-140 0 -239 -102
q-10 -10 -10 -24v-188q0 -6 5 -6h485q4 0 4 6z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="781" 
d="M154 267h-46v341l-64 -69l-27 29l97 99h40v-400zM568 667l-427 -667h-45l426 667h46zM731 108q0 -51 -39 -83t-105 -32q-49 0 -87 18t-57 47l26 28q42 -55 117 -55q47 0 73.5 20.5t26.5 57.5q0 40 -30.5 59t-79.5 19q-32 0 -38 -1v39q7 -1 38 -1q46 0 75 17.5t29 54.5
q0 34 -27.5 53t-68.5 19q-65 0 -111 -50l-25 27q52 61 139 61q60 0 98.5 -28.5t38.5 -77.5q0 -41 -27.5 -64.5t-61.5 -28.5q34 -3 65 -28.5t31 -70.5z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="898" 
d="M333 267h-281v35q122 87 178.5 144.5t56.5 109.5q0 40 -27 59.5t-66 19.5q-75 0 -113 -52l-27 28q48 62 141 62q55 0 95.5 -29t40.5 -86q0 -60 -54.5 -119t-158.5 -134h215v-38zM685 667l-427 -667h-45l426 667h46zM848 108q0 -51 -39 -83t-105 -32q-49 0 -87 18t-57 47
l26 28q42 -55 117 -55q47 0 73.5 20.5t26.5 57.5q0 40 -30.5 59t-79.5 19q-32 0 -38 -1v39q7 -1 38 -1q46 0 75 17.5t29 54.5q0 34 -27.5 53t-68.5 19q-65 0 -111 -50l-25 27q52 61 139 61q60 0 98.5 -28.5t38.5 -77.5q0 -41 -27.5 -64.5t-61.5 -28.5q34 -3 65 -28.5
t31 -70.5z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="781" 
d="M154 267h-46v341l-64 -69l-27 29l97 99h40v-400zM735 101q0 -49 -42 -78.5t-104 -29.5t-104 29t-42 79q0 39 30 66.5t74 38.5q-41 10 -69 34.5t-28 60.5q0 52 41.5 78.5t97.5 26.5t97.5 -26.5t41.5 -78.5q0 -37 -27.5 -61t-68.5 -34q43 -11 73 -38.5t30 -66.5zM684 297
q0 34 -27 53t-68 19t-68 -19t-27 -53q0 -53 95 -75q95 21 95 75zM690 104q0 39 -35.5 60t-65.5 25q-17 -2 -37.5 -9.5t-42.5 -28t-22 -47.5q0 -34 30 -54.5t72 -20.5t71.5 20.5t29.5 54.5zM568 667l-427 -667h-45l426 667h46z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="898" 
d="M852 101q0 -49 -42 -78.5t-104 -29.5t-104 29t-42 79q0 39 30 66.5t74 38.5q-41 10 -69 34.5t-28 60.5q0 52 41.5 78.5t97.5 26.5t97.5 -26.5t41.5 -78.5q0 -37 -27.5 -61t-68.5 -34q43 -11 73 -38.5t30 -66.5zM801 297q0 34 -27 53t-68 19t-68 -19t-27 -53q0 -53 95 -75
q95 21 95 75zM807 104q0 39 -35.5 60t-65.5 25q-17 -2 -37.5 -9.5t-42.5 -28t-22 -47.5q0 -34 30 -54.5t72 -20.5t71.5 20.5t29.5 54.5zM685 667l-427 -667h-45l426 667h46zM334 375q0 -51 -39 -83t-105 -32q-49 0 -87 18t-57 47l26 28q42 -55 117 -55q47 0 73.5 20.5
t26.5 57.5q0 40 -30.5 59t-79.5 19q-32 0 -38 -1v39q7 -1 38 -1q46 0 75 17.5t29 54.5q0 34 -27.5 53t-68.5 19q-65 0 -111 -50l-25 27q52 61 139 61q60 0 98.5 -28.5t38.5 -77.5q0 -41 -27.5 -64.5t-61.5 -28.5q34 -3 65 -28.5t31 -70.5z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="896" 
d="M850 101q0 -49 -42 -78.5t-104 -29.5t-104 29t-42 79q0 39 30 66.5t74 38.5q-41 10 -69 34.5t-28 60.5q0 52 41.5 78.5t97.5 26.5t97.5 -26.5t41.5 -78.5q0 -37 -27.5 -61t-68.5 -34q43 -11 73 -38.5t30 -66.5zM799 297q0 34 -27 53t-68 19t-68 -19t-27 -53q0 -53 95 -75
q95 21 95 75zM805 104q0 39 -35.5 60t-65.5 25q-17 -2 -37.5 -9.5t-42.5 -28t-22 -47.5q0 -34 30 -54.5t72 -20.5t71.5 20.5t29.5 54.5zM335 390q0 -59 -41 -94.5t-103 -35.5q-98 0 -143 65l27 30q42 -57 116 -57q44 0 72 26t28 65q0 42 -27.5 67.5t-71.5 25.5
q-58 0 -99 -40l-33 13v212h248v-38h-203v-149q37 37 99 37q55 0 93 -33t38 -94zM683 667l-427 -667h-45l426 667h46z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="811" 
d="M765 101q0 -49 -42 -78.5t-104 -29.5t-104 29t-42 79q0 39 30 66.5t74 38.5q-41 10 -69 34.5t-28 60.5q0 52 41.5 78.5t97.5 26.5t97.5 -26.5t41.5 -78.5q0 -37 -27.5 -61t-68.5 -34q43 -11 73 -38.5t30 -66.5zM714 297q0 34 -27 53t-68 19t-68 -19t-27 -53q0 -53 95 -75
q95 21 95 75zM720 104q0 39 -35.5 60t-65.5 25q-17 -2 -37.5 -9.5t-42.5 -28t-22 -47.5q0 -34 30 -54.5t72 -20.5t71.5 20.5t29.5 54.5zM315 636l-174 -369h-49l173 362h-230v38h280v-31zM598 667l-427 -667h-45l426 667h46z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="582" 
d="M538 211h-269l12 -130l-281 205l281 204l-12 -129h269v-150z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="582" 
d="M216 17v269l-130 -12l205 281l204 -281l-129 12v-269h-150z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="582" 
d="M584 286l-281 -205l12 130h-268v150h268l-12 129z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="582" 
d="M366 555v-269l130 12l-205 -281l-204 281l129 -12v269h150z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M285 -12q-104 0 -168 71.5t-64 178.5q0 106 62.5 177.5t160.5 71.5q96 0 155 -84q-31 67 -100 131t-162 108l38 37q130 -65 220.5 -178.5t90.5 -255.5q0 -112 -64 -184.5t-169 -72.5zM285 35q82 0 129.5 59t47.5 144q0 84 -47.5 143t-129.5 59q-81 0 -128.5 -59
t-47.5 -143t47.5 -143.5t128.5 -59.5z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="647" 
d="M632 0h-617l273 667h71zM549 52l-226 554l-225 -554h451z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="673" 
d="M527 -90h-58v705h-266v-705h-57v705h-111v52h603v-52h-111v-705z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M129 615l242 -316l-245 -337h387v-52h-455v52l243 336l-242 317v52h454v-52h-384z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="496" 
d="M467 316h-438v42h438v-42z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="130" 
d="M301 667l-427 -667h-45l426 667h46z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="219" 
d="M154 245q0 -18 -13.5 -31t-31.5 -13t-31 13t-13 31q0 17 13 30t31 13t31.5 -13t13.5 -30z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="743" 
d="M436 0h-46l-138 338l-165 -67l-15 39l203 81l139 -335l250 611h44z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="641" 
d="M178 175q-72 0 -110.5 48t-38.5 112q0 65 38.5 112.5t110.5 47.5q48 0 84 -32t58 -82q22 50 58.5 82t84.5 32q73 0 111 -47.5t38 -112.5q0 -64 -38 -112t-111 -48q-48 0 -84.5 32t-58.5 82q-22 -50 -58 -82t-84 -32zM461 217q50 0 78.5 33t28.5 85q0 53 -28.5 86
t-78.5 33q-74 0 -121 -119q20 -50 50.5 -84t70.5 -34zM181 217q74 0 120 118q-47 119 -120 119q-51 0 -79.5 -33t-28.5 -86q0 -52 28.5 -85t79.5 -33z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="337" 
d="M79 -100h-62v43h62q28 0 47.5 23t19.5 58v620q0 54 34 89t78 35h62v-43h-62q-28 0 -47.5 -23t-19.5 -58v-620q0 -55 -34 -89.5t-78 -34.5z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="496" 
d="M29 216v47q47 -35 110 -35q47 0 104 30.5t114 30.5q64 0 110 -28v-47q-47 34 -110 34q-48 0 -105 -30.5t-113 -30.5q-61 0 -110 29zM29 407v47q45 -34 110 -34q47 0 104 30.5t114 30.5q62 0 110 -28v-48q-47 35 -110 35q-48 0 -105 -30.5t-113 -30.5q-63 0 -110 28z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="496" 
d="M118 93h-46l78 123h-121v41h148l98 154h-246v42h272l77 121h47l-78 -121h120v-42h-146l-99 -154h245v-41h-271z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="496" 
d="M467 95l-438 225v38l438 227v-50l-386 -196l386 -195v-49zM467 0h-438v41h438v-41z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="496" 
d="M467 320l-438 -225v49l386 195l-386 196v50l438 -227v-38zM466 0h-438v41h438v-41z" />
    <glyph glyph-name="triangleright" unicode="&#x22b2;" horiz-adv-x="587" 
d="M589 286l-535 -309v618z" />
    <glyph glyph-name="triangleleft" unicode="&#x22b3;" horiz-adv-x="587" 
d="M535 595v-618l-535 309z" />
    <glyph glyph-name="dotmath" unicode="&#x22c5;" horiz-adv-x="219" 
d="M154 245q0 -18 -13.5 -31t-31.5 -13t-31 13t-13 31q0 17 13 30t31 13t31.5 -13t13.5 -30z" />
    <glyph glyph-name="uni2318" unicode="&#x2318;" horiz-adv-x="667" 
d="M230 154v77h-78q-32 0 -53.5 -22.5t-21.5 -54.5q0 -33 22 -56t54 -23t54.5 23.5t22.5 55.5zM230 435v79q0 32 -22 54.5t-54 22.5t-54.5 -22.5t-22.5 -54.5t22.5 -55.5t54.5 -23.5h76zM265 265h137v137h-137v-137zM590 154q0 32 -21.5 54.5t-53.5 22.5h-78v-77
q0 -32 22.5 -55.5t54.5 -23.5t54 23t22 56zM590 514q0 32 -22 54.5t-54 22.5t-54.5 -22.5t-22.5 -54.5v-79h78q32 0 53.5 23t21.5 56zM625 154q0 -46 -32.5 -79t-78.5 -33t-79 33t-33 79v77h-137v-77q0 -46 -33 -79t-79 -33t-78.5 33t-32.5 79t32.5 78.5t78.5 32.5h77v137
h-76q-46 0 -79 33t-33 79t33 78.5t79 32.5t78.5 -32.5t32.5 -78.5v-79h137v79q0 46 33 78.5t79 32.5t78.5 -32.5t32.5 -78.5t-32.5 -79t-78.5 -33h-77v-137h77q46 0 78.5 -32.5t32.5 -78.5z" />
    <glyph glyph-name="blacksquare" unicode="&#x25a0;" horiz-adv-x="661" 
d="M618 0h-571v572h571v-572z" />
    <glyph glyph-name="triagup" unicode="&#x25b2;" horiz-adv-x="616" 
d="M617 29l-619 1l309 535z" />
    <glyph glyph-name="triagdn" unicode="&#x25bc;" horiz-adv-x="616" 
d="M617 510l-310 -535l-309 535h619z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="375" 
d="M198 0h-21l-158 334l158 333h21l158 -333zM188 41l132 293l-133 292l-132 -292z" />
    <glyph glyph-name="circlesolid" unicode="&#x25cf;" horiz-adv-x="695" 
d="M644 285q0 -123 -87 -210t-210 -87t-210 87t-87 210t87 210t210 87t210 -87t87 -210z" />
    <glyph glyph-name="blackstar" unicode="&#x2605;" horiz-adv-x="759" 
d="M759 421l-235 -171l90 -275l-235 170l-234 -170l89 275l-234 171h290l89 276l90 -276h290z" />
    <glyph glyph-name="ballotbox" unicode="&#x2610;" horiz-adv-x="661" 
d="M616 0h-571v572h571v-572zM571 41v490h-482v-490h482z" />
    <glyph glyph-name="boxchecked" unicode="&#x2611;" horiz-adv-x="661" 
d="M671 698l-69 -126h14v-572h-571v572h508l81 150zM571 41v473l-229 -423l-202 246l33 31l163 -201l195 364h-442v-490h482z" />
    <glyph glyph-name="diamond" unicode="&#x2666;" horiz-adv-x="501" 
d="M501 285l-251 -250l-250 250l250 251z" />
    <glyph glyph-name="checkmark" unicode="&#x2713;" horiz-adv-x="531" 
d="M531 698l-329 -607l-202 246l33 31l163 -201l298 555z" />
    <glyph glyph-name="perthousand.onum" unicode="&#xf628;" horiz-adv-x="866" 
d="M285 425q0 -58 -36.5 -96.5t-93.5 -38.5t-93 38.5t-36 96.5t36.5 97.5t92.5 39.5q57 0 93.5 -39.5t36.5 -97.5zM556 124q0 -58 -36.5 -97t-92.5 -39t-92.5 39t-36.5 97q0 59 36 98t93 39t93 -39t36 -98zM505 550l-383 -550h-44l382 550h45zM244 425q0 44 -24.5 73
t-64.5 29q-39 0 -63.5 -29.5t-24.5 -72.5t25 -72t63 -29q40 0 64.5 29t24.5 72zM515 124q0 44 -24.5 73t-63.5 29t-63.5 -29t-24.5 -73q0 -42 24.5 -71.5t63.5 -29.5t63.5 29.5t24.5 71.5zM840 124q0 -58 -36.5 -97t-92.5 -39t-92.5 40t-36.5 96q0 59 36 98t93 39t93 -39
t36 -98zM800 124q0 44 -24.5 73t-64.5 29q-39 0 -63.5 -29t-24.5 -73q0 -42 24.5 -71.5t63.5 -29.5q40 0 64.5 29.5t24.5 71.5z" />
    <glyph glyph-name="uniF637" unicode="&#xf637;" horiz-adv-x="392" 
d="M328 326h-43v208q0 43 -19.5 60.5t-56.5 17.5q-29 0 -57.5 -15t-44.5 -36v-235h-42v434h42v-167q19 22 51 38.5t66 16.5q104 0 104 -108v-214z" />
    <glyph glyph-name="zero.tzero" unicode="&#xf638;" horiz-adv-x="588" 
d="M295 -12q-90 0 -150 60l-29 -48h-51l50 83q-67 97 -67 250q0 63 14 121.5t41.5 109.5t77 82t114.5 31q86 0 147 -58l30 48h51l-51 -83q69 -98 69 -251q0 -62 -14 -121t-42 -110.5t-77 -82.5t-113 -31zM295 40q50 0 87.5 26t58 69.5t30 93t9.5 104.5q0 118 -40 198
l-266 -435q46 -56 121 -56zM108 333q0 -120 40 -197l265 435q-46 54 -118 54q-51 0 -88.5 -25.5t-58 -69t-30.5 -93t-10 -104.5z" />
    <glyph glyph-name="zero.tnum" unicode="&#xf639;" horiz-adv-x="588" 
d="M295 -12q-64 0 -114 31t-77.5 82.5t-41.5 110t-14 121.5t14 121.5t41.5 109.5t77 82t114.5 31q64 0 113 -31t77 -82t42 -109.5t14 -121.5q0 -62 -14 -121t-42 -110.5t-77 -82.5t-113 -31zM295 40q50 0 87.5 26t58 69.5t30 93t9.5 104.5t-9.5 104.5t-30 93t-58 69
t-87.5 25.5q-51 0 -88.5 -25.5t-58 -69t-30.5 -93t-10 -104.5t10 -104.5t30.5 -93t58 -69.5t88.5 -26z" />
    <glyph glyph-name="two.tnum" unicode="&#xf63a;" horiz-adv-x="588" 
d="M509 0h-440v47q80 65 128 105t102 91t82.5 88.5t47.5 78.5t19 79q0 67 -45.5 101.5t-106.5 34.5q-60 0 -107.5 -23.5t-75.5 -63.5l-39 36q36 48 94 75.5t128 27.5q83 0 146.5 -48t63.5 -140q0 -53 -25 -106t-79 -111.5t-105.5 -104t-138.5 -115.5h351v-52z" />
    <glyph glyph-name="three.tnum" unicode="&#xf63b;" horiz-adv-x="588" 
d="M294 -12q-78 0 -137 31.5t-90 79.5l39 34q65 -93 187 -93q77 0 122.5 38t45.5 104q0 68 -49.5 101.5t-129.5 33.5q-52 0 -62 -1v54q10 -1 62 -1q72 0 120.5 32t48.5 95q0 60 -46 94.5t-114 34.5q-103 0 -179 -85l-36 36q83 101 218 101q91 0 153 -47t62 -129
q0 -47 -25 -83t-55 -51.5t-63 -21.5q54 -6 103.5 -49t49.5 -117q0 -84 -60.5 -137.5t-164.5 -53.5z" />
    <glyph glyph-name="four.tnum" unicode="&#xf63c;" horiz-adv-x="588" 
d="M430 0h-57v182h-316v51l296 434h77v-433h98v-52h-98v-182zM373 234v376l-257 -376h257z" />
    <glyph glyph-name="five.tnum" unicode="&#xf63d;" horiz-adv-x="588" 
d="M302 -12q-149 0 -225 109l38 38q67 -95 187 -95q72 0 119.5 46t47.5 115q0 75 -46.5 119t-119.5 44q-91 0 -162 -65l-43 18v350h385v-52h-328v-261q64 62 159 62q89 0 151 -56.5t62 -156.5q0 -97 -65 -156t-160 -59z" />
    <glyph glyph-name="six.tnum" unicode="&#xf63e;" horiz-adv-x="588" 
d="M309 -12q-52 0 -94 19t-70 51t-46.5 76.5t-26.5 93.5t-8 105q0 53 8.5 101.5t28.5 93.5t48.5 77.5t72.5 52t97 19.5q112 0 180 -85l-34 -42q-55 75 -146 75q-52 0 -91.5 -25.5t-61.5 -68.5t-33 -93t-11 -106q0 -21 1 -31q25 41 79 76t116 35q95 0 155 -54.5t60 -156.5
q0 -87 -61 -150t-163 -63zM307 40q79 0 123.5 49.5t44.5 110.5q0 79 -46 120t-121 41q-54 0 -103.5 -31.5t-78.5 -82.5q4 -51 22 -95t59 -78t100 -34z" />
    <glyph glyph-name="seven.tnum" unicode="&#xf63f;" horiz-adv-x="588" 
d="M228 0h-63l280 615h-369v52h437v-40z" />
    <glyph glyph-name="eight.tnum" unicode="&#xf640;" horiz-adv-x="588" 
d="M294 -12q-98 0 -162.5 49.5t-64.5 129.5q0 65 48 111.5t118 65.5q-66 18 -110.5 58.5t-44.5 102.5q0 83 64 127.5t152 44.5q86 0 151 -44.5t65 -127.5q0 -62 -44.5 -102.5t-110.5 -58.5q70 -19 117.5 -65t47.5 -112q0 -80 -64.5 -129.5t-161.5 -49.5zM294 368
q22 4 43 11.5t50 22t47 40t18 57.5q0 58 -45 92t-113 34t-113 -34t-45 -92q0 -32 18 -57.5t47 -40.5t50 -22t43 -11zM294 40q68 0 118.5 36.5t50.5 94.5q0 36 -20 65t-50 45.5t-55 25.5t-44 11q-19 -2 -44.5 -11t-55 -25.5t-49.5 -45.5t-20 -65q0 -59 49.5 -95t119.5 -36z
" />
    <glyph glyph-name="nine.tnum" unicode="&#xf641;" horiz-adv-x="588" 
d="M269 -11q-113 0 -179 85l33 41q55 -74 146 -74q53 0 92.5 25.5t61.5 69t32.5 93t10.5 105.5v31q-26 -41 -80.5 -76.5t-115.5 -35.5q-95 0 -155 55t-60 157q0 87 61 150t163 63q52 0 94 -19t70 -51t46.5 -76.5t26.5 -93.5t8 -105q0 -53 -8.5 -101.5t-28.5 -93.5
t-48.5 -77.5t-72.5 -52t-97 -19.5zM280 305q54 0 103.5 31.5t78.5 82.5q-4 51 -22 95t-59 78t-100 34q-79 0 -123.5 -49.5t-44.5 -110.5q0 -79 46 -120t121 -41z" />
    <glyph glyph-name="percent.onum" unicode="&#xf642;" horiz-adv-x="582" 
d="M285 425q0 -58 -36.5 -96.5t-93.5 -38.5t-93 38.5t-36 96.5t36.5 97.5t92.5 39.5q57 0 93.5 -39.5t36.5 -97.5zM556 124q0 -58 -36.5 -97t-92.5 -39t-92.5 39t-36.5 97q0 59 36 98t93 39t93 -39t36 -98zM505 550l-384 -550h-44l383 550h45zM244 425q0 44 -24.5 73
t-64.5 29q-39 0 -63.5 -29.5t-24.5 -72.5t25 -72t63 -29q40 0 64.5 29t24.5 72zM515 124q0 44 -24.5 73t-63.5 29t-63.5 -29t-24.5 -73q0 -42 24.5 -71.5t63.5 -29.5t63.5 29.5t24.5 71.5z" />
    <glyph glyph-name="zero.tonum" unicode="&#xf643;" horiz-adv-x="588" 
d="M541 275q0 -74 -24.5 -137t-82 -106.5t-139.5 -43.5q-83 0 -140.5 43.5t-82 106.5t-24.5 137q0 55 14.5 104.5t43.5 91.5t77.5 66.5t111.5 24.5q82 0 139.5 -43.5t82 -106.5t24.5 -137zM480 275q0 98 -46 166.5t-139 68.5q-94 0 -140.5 -69t-46.5 -166t46.5 -166
t140.5 -69q93 0 139 69t46 166z" />
    <glyph glyph-name="one.tonum" unicode="&#xf644;" horiz-adv-x="588" 
d="M372 0h-57v472l-107 -114l-35 37l147 155h52v-550z" />
    <glyph glyph-name="two.tonum" unicode="&#xf645;" horiz-adv-x="588" 
d="M496 0h-415v48q183 80 275.5 163.5t92.5 165.5q0 58 -44 95.5t-107 37.5q-60 0 -111 -23.5t-80 -63.5l-38 36q34 48 96.5 75.5t133.5 27.5q87 0 147 -49.5t60 -129.5q0 -93 -87.5 -181t-228.5 -150h306v-52z" />
    <glyph glyph-name="three.tonum" unicode="&#xf646;" horiz-adv-x="588" 
d="M294 -134q-78 0 -137 31.5t-90 79.5l39 34q65 -93 187 -93q77 0 122.5 38t45.5 104q0 68 -49.5 101.5t-129.5 33.5q-52 0 -62 -1v54q10 -1 62 -1q72 0 120.5 32t48.5 95q0 60 -46 94.5t-114 34.5q-103 0 -179 -85l-36 36q83 101 218 101q91 0 153 -47t62 -129
q0 -47 -25 -83t-55 -51.5t-63 -21.5q54 -6 103.5 -49t49.5 -117q0 -84 -60.5 -137.5t-164.5 -53.5z" />
    <glyph glyph-name="four.tonum" unicode="&#xf647;" horiz-adv-x="588" 
d="M528 66h-98v-183h-57v183h-316v52l296 432h77v-432h98v-52zM373 118v375l-257 -375h257z" />
    <glyph glyph-name="five.tonum" unicode="&#xf648;" horiz-adv-x="588" 
d="M302 -129q-149 0 -225 109l38 38q67 -95 187 -95q72 0 119.5 46t47.5 115q0 75 -46.5 119t-119.5 44q-91 0 -162 -65l-43 18v350h385v-52h-328v-261q64 62 159 62q89 0 151 -56.5t62 -156.5q0 -97 -65 -156t-160 -59z" />
    <glyph glyph-name="six.tonum" unicode="&#xf649;" horiz-adv-x="588" 
d="M309 -12q-52 0 -94 19t-70 51t-46.5 76.5t-26.5 93.5t-8 105q0 53 8.5 101.5t28.5 93.5t48.5 77.5t72.5 52t97 19.5q112 0 180 -85l-34 -42q-55 75 -146 75q-52 0 -91.5 -25.5t-61.5 -68.5t-33 -93t-11 -106q0 -21 1 -31q25 41 79 76t116 35q95 0 155 -54.5t60 -156.5
q0 -87 -61 -150t-163 -63zM307 40q79 0 123.5 49.5t44.5 110.5q0 79 -46 120t-121 41q-54 0 -103.5 -31.5t-78.5 -82.5q4 -51 22 -95t59 -78t100 -34z" />
    <glyph glyph-name="seven.tonum" unicode="&#xf64a;" horiz-adv-x="588" 
d="M228 -117h-63l280 615h-369v52h437v-40z" />
    <glyph glyph-name="eight.tonum" unicode="&#xf64b;" horiz-adv-x="588" 
d="M294 -12q-98 0 -162.5 49.5t-64.5 129.5q0 65 48 111.5t118 65.5q-66 18 -110.5 58.5t-44.5 102.5q0 83 64 127.5t152 44.5q86 0 151 -44.5t65 -127.5q0 -62 -44.5 -102.5t-110.5 -58.5q70 -19 117.5 -65t47.5 -112q0 -80 -64.5 -129.5t-161.5 -49.5zM294 368
q22 4 43 11.5t50 22t47 40t18 57.5q0 58 -45 92t-113 34t-113 -34t-45 -92q0 -32 18 -57.5t47 -40.5t50 -22t43 -11zM294 40q68 0 118.5 36.5t50.5 94.5q0 36 -20 65t-50 45.5t-55 25.5t-44 11q-19 -2 -44.5 -11t-55 -25.5t-49.5 -45.5t-20 -65q0 -59 49.5 -95t119.5 -36z
" />
    <glyph glyph-name="nine.tonum" unicode="&#xf64c;" horiz-adv-x="588" 
d="M269 -127q-113 0 -179 85l33 41q55 -74 146 -74q53 0 92.5 25.5t61.5 69t32.5 93t10.5 105.5v31q-26 -41 -80.5 -76.5t-115.5 -35.5q-95 0 -155 55t-60 157q0 87 61 150t163 63q52 0 94 -19t70 -51t46.5 -76.5t26.5 -93.5t8 -105q0 -53 -8.5 -101.5t-28.5 -93.5
t-48.5 -77.5t-72.5 -52t-97 -19.5zM280 189q54 0 103.5 31.5t78.5 82.5q-4 51 -22 95t-59 78t-100 34q-79 0 -123.5 -49.5t-44.5 -110.5q0 -79 46 -120t121 -41z" />
    <glyph glyph-name="colonmonetary.onum" unicode="&#xf64d;" horiz-adv-x="581" 
d="M143 -53l30 92q-56 38 -88 99.5t-32 137.5q0 125 83.5 205.5t207.5 80.5h6l13 37h36l-14 -40q32 -4 64 -17l19 57h37l-25 -73q41 -24 75 -66l-43 -27q-22 28 -48 45l-147 -440q9 -1 27 -1q51 0 97 21.5t71 58.5l44 -27q-83 -102 -212 -102q-22 0 -43 3l-15 -44h-36l16 51
q-33 7 -62 22l-24 -73h-37zM111 276q0 -115 81 -185l142 422q-96 -4 -159.5 -70.5t-63.5 -166.5zM221 70q31 -18 61 -25l151 450q-33 13 -64 16z" />
    <glyph glyph-name="Euro.onum" unicode="&#xf64e;" horiz-adv-x="610" 
d="M415 318h-272q-3 -21 -3 -42q0 -26 4 -46h271v-36h-262q23 -72 82.5 -114.5t137.5 -42.5q51 0 97 21.5t71 58.5l44 -27q-83 -102 -212 -102q-101 0 -178 56t-102 150h-58v36h50q-3 23 -3 46q0 28 2 42h-49v38h57q25 94 102 150t179 56q130 0 211 -102l-43 -27
q-28 37 -72.5 58.5t-95.5 21.5q-78 0 -138 -43t-83 -114h263v-38z" />
    <glyph glyph-name="florin.onum" unicode="&#xf64f;" horiz-adv-x="354" 
d="M84 -119h-58l76 340h-54v41h62l34 150q33 149 141 149q48 0 79 -30l-28 -41q-15 19 -45 19q-67 0 -89 -102l-33 -145h116v-41h-125z" />
    <glyph glyph-name="numbersign.onum" unicode="&#xf650;" horiz-adv-x="488" 
d="M464 390l-11 -37h-92l-51 -152h93l-12 -39h-94l-54 -162h-45l55 162h-97l-53 -162h-45l54 162h-88l12 39h89l51 152h-90l11 37h92l54 160h45l-55 -160h96l55 160h45l-55 -160h90zM315 353h-94l-52 -152h96z" />
    <glyph glyph-name="sterling.onum" unicode="&#xf651;" horiz-adv-x="415" 
d="M14 223v39h83q-18 25 -28 41t-20 43.5t-10 55.5q0 67 50.5 113t126.5 46q50 0 93 -24t63 -63l-41 -24q-33 68 -112 68q-52 0 -89.5 -32t-37.5 -84q0 -13 3.5 -27.5t6 -23t12.5 -25t12.5 -20.5t16 -23.5t14.5 -20.5h137v-39h-112q16 -34 16 -59q0 -66 -58 -101q9 3 22 3
q31 0 73 -15.5t63 -15.5q24 0 47.5 9.5t34.5 22.5l27 -40q-41 -39 -111 -39q-37 0 -73.5 17.5t-70.5 17.5t-96 -32l-21 43q114 49 114 123q0 29 -23 66h-112z" />
    <glyph glyph-name="yen.onum" unicode="&#xf652;" horiz-adv-x="539" 
d="M299 0h-55v116h-221v38h221v80h-221v39h193l-204 277h66l194 -266l190 266h65l-201 -277h189v-39h-216v-80h216v-38h-216v-116z" />
    <glyph glyph-name="cent.onum" unicode="&#xf654;" horiz-adv-x="581" 
d="M320 -96v85q-116 8 -191.5 87.5t-75.5 199.5q0 118 75.5 197.5t191.5 87.5v56h45v-56q116 -7 190 -101l-43 -27q-51 69 -147 79v-474q45 4 84.5 25t62.5 54l44 -27q-76 -94 -191 -101v-85h-45zM111 276q0 -97 59 -163.5t150 -74.5v474q-91 -8 -150 -73.5t-59 -162.5z" />
    <glyph glyph-name="zero.dnom" unicode="&#xf655;" horiz-adv-x="384" 
d="M349 200q0 -51 -15 -96.5t-52 -78t-90 -32.5q-78 0 -117.5 62.5t-39.5 144.5q0 83 39.5 144.5t117.5 61.5t117.5 -62t39.5 -144zM302 200q0 71 -26.5 119.5t-83.5 48.5t-84 -49t-27 -119q0 -71 27 -120t84 -49t83.5 49t26.5 120z" />
    <glyph glyph-name="one.dnom" unicode="&#xf656;" horiz-adv-x="214" 
d="M154 0h-46v341l-64 -69l-27 29l97 99h40v-400z" />
    <glyph glyph-name="two.dnom" unicode="&#xf657;" horiz-adv-x="384" 
d="M333 0h-281v35q122 87 178.5 144.5t56.5 109.5q0 40 -27 59.5t-66 19.5q-75 0 -113 -52l-27 28q48 62 141 62q55 0 95.5 -29t40.5 -86q0 -60 -54.5 -119t-158.5 -134h215v-38z" />
    <glyph glyph-name="three.dnom" unicode="&#xf658;" horiz-adv-x="384" 
d="M334 108q0 -51 -39 -83t-105 -32q-49 0 -87 18t-57 47l26 28q42 -55 117 -55q47 0 73.5 20.5t26.5 57.5q0 40 -30.5 59t-79.5 19q-32 0 -38 -1v39q7 -1 38 -1q46 0 75 17.5t29 54.5q0 34 -27.5 53t-68.5 19q-65 0 -111 -50l-25 27q52 61 139 61q60 0 98.5 -28.5
t38.5 -77.5q0 -41 -27.5 -64.5t-61.5 -28.5q34 -3 65 -28.5t31 -70.5z" />
    <glyph glyph-name="four.dnom" unicode="&#xf659;" horiz-adv-x="384" 
d="M343 107h-60v-107h-44v107h-196v35l179 258h61v-256h60v-37zM239 144v215l-150 -215h150z" />
    <glyph glyph-name="five.dnom" unicode="&#xf65a;" horiz-adv-x="384" 
d="M335 123q0 -59 -41 -94.5t-103 -35.5q-98 0 -143 65l27 30q42 -57 116 -57q44 0 72 26t28 65q0 42 -27.5 67.5t-71.5 25.5q-58 0 -99 -40l-33 13v212h248v-38h-203v-149q37 37 99 37q55 0 93 -33t38 -94z" />
    <glyph glyph-name="six.dnom" unicode="&#xf65b;" horiz-adv-x="384" 
d="M342 122q0 -53 -39.5 -91t-103.5 -38q-80 0 -118.5 58.5t-38.5 148.5q0 87 41.5 146.5t123.5 59.5q68 0 113 -48l-23 -32q-37 42 -90 42q-60 0 -90 -47.5t-31 -115.5q0 -13 1 -19q16 24 49 44.5t71 20.5q59 0 97 -33.5t38 -95.5zM297 121q0 45 -27.5 69t-72.5 24
q-31 0 -60.5 -17.5t-47.5 -46.5q4 -49 31 -84t78 -35q46 0 72.5 28t26.5 62z" />
    <glyph glyph-name="seven.dnom" unicode="&#xf65c;" horiz-adv-x="359" 
d="M315 369l-174 -369h-49l173 362h-230v38h280v-31z" />
    <glyph glyph-name="eight.dnom" unicode="&#xf65d;" horiz-adv-x="384" 
d="M338 101q0 -49 -42 -78.5t-104 -29.5t-104 29t-42 79q0 39 30 66.5t74 38.5q-41 10 -69 34.5t-28 60.5q0 52 41.5 78.5t97.5 26.5t97.5 -26.5t41.5 -78.5q0 -37 -27.5 -61t-68.5 -34q43 -11 73 -38.5t30 -66.5zM287 297q0 34 -27 53t-68 19t-68 -19t-27 -53q0 -53 95 -75
q95 21 95 75zM293 104q0 39 -35.5 60t-65.5 25q-17 -2 -37.5 -9.5t-42.5 -28t-22 -47.5q0 -34 30 -54.5t72 -20.5t71.5 20.5t29.5 54.5z" />
    <glyph glyph-name="nine.dnom" unicode="&#xf65e;" horiz-adv-x="384" 
d="M42 277q0 53 39 91t104 38q80 0 118.5 -58.5t38.5 -148.5q0 -87 -41.5 -146.5t-122.5 -59.5q-69 0 -114 48l23 32q34 -42 91 -42q59 0 88.5 47.5t30.5 115.5v19q-17 -24 -50 -44.5t-69 -20.5q-59 0 -97.5 33.5t-38.5 95.5zM86 278q0 -45 28 -69t72 -24q32 0 62 18t47 47
q-4 48 -31 83t-78 35q-46 0 -73 -28t-27 -62z" />
    <glyph glyph-name="zero.numr" unicode="&#xf661;" horiz-adv-x="384" 
d="M349 467q0 -51 -15 -96.5t-52 -78t-90 -32.5q-78 0 -117.5 62.5t-39.5 144.5q0 83 39.5 144.5t117.5 61.5t117.5 -62t39.5 -144zM302 467q0 71 -26.5 119.5t-83.5 48.5t-84 -49t-27 -119q0 -71 27 -120t84 -49t83.5 49t26.5 120z" />
    <glyph glyph-name="one.numr" unicode="&#xf662;" horiz-adv-x="214" 
d="M154 267h-46v341l-64 -69l-27 29l97 99h40v-400z" />
    <glyph glyph-name="two.numr" unicode="&#xf663;" horiz-adv-x="384" 
d="M333 267h-281v35q122 87 178.5 144.5t56.5 109.5q0 40 -27 59.5t-66 19.5q-75 0 -113 -52l-27 28q48 62 141 62q55 0 95.5 -29t40.5 -86q0 -60 -54.5 -119t-158.5 -134h215v-38z" />
    <glyph glyph-name="three.numr" unicode="&#xf664;" horiz-adv-x="384" 
d="M334 375q0 -51 -39 -83t-105 -32q-49 0 -87 18t-57 47l26 28q42 -55 117 -55q47 0 73.5 20.5t26.5 57.5q0 40 -30.5 59t-79.5 19q-32 0 -38 -1v39q7 -1 38 -1q46 0 75 17.5t29 54.5q0 34 -27.5 53t-68.5 19q-65 0 -111 -50l-25 27q52 61 139 61q60 0 98.5 -28.5
t38.5 -77.5q0 -41 -27.5 -64.5t-61.5 -28.5q34 -3 65 -28.5t31 -70.5z" />
    <glyph glyph-name="four.numr" unicode="&#xf665;" horiz-adv-x="384" 
d="M343 374h-60v-107h-44v107h-196v35l179 258h61v-256h60v-37zM239 411v215l-150 -215h150z" />
    <glyph glyph-name="five.numr" unicode="&#xf666;" horiz-adv-x="384" 
d="M335 390q0 -59 -41 -94.5t-103 -35.5q-98 0 -143 65l27 30q42 -57 116 -57q44 0 72 26t28 65q0 42 -27.5 67.5t-71.5 25.5q-58 0 -99 -40l-33 13v212h248v-38h-203v-149q37 37 99 37q55 0 93 -33t38 -94z" />
    <glyph glyph-name="six.numr" unicode="&#xf667;" horiz-adv-x="384" 
d="M342 389q0 -53 -39.5 -91t-103.5 -38q-80 0 -118.5 58.5t-38.5 148.5q0 87 41.5 146.5t123.5 59.5q68 0 113 -48l-23 -32q-37 42 -90 42q-60 0 -90 -47.5t-31 -115.5q0 -13 1 -19q16 24 49 44.5t71 20.5q59 0 97 -33.5t38 -95.5zM297 388q0 45 -27.5 69t-72.5 24
q-31 0 -60.5 -17.5t-47.5 -46.5q4 -49 31 -84t78 -35q46 0 72.5 28t26.5 62z" />
    <glyph glyph-name="seven.numr" unicode="&#xf668;" horiz-adv-x="359" 
d="M315 636l-174 -369h-49l173 362h-230v38h280v-31z" />
    <glyph glyph-name="eight.numr" unicode="&#xf669;" horiz-adv-x="384" 
d="M338 368q0 -49 -42 -78.5t-104 -29.5t-104 29t-42 79q0 39 30 66.5t74 38.5q-41 10 -69 34.5t-28 60.5q0 52 41.5 78.5t97.5 26.5t97.5 -26.5t41.5 -78.5q0 -37 -27.5 -61t-68.5 -34q43 -11 73 -38.5t30 -66.5zM287 564q0 34 -27 53t-68 19t-68 -19t-27 -53q0 -53 95 -75
q95 21 95 75zM293 371q0 39 -35.5 60t-65.5 25q-17 -2 -37.5 -9.5t-42.5 -28t-22 -47.5q0 -34 30 -54.5t72 -20.5t71.5 20.5t29.5 54.5z" />
    <glyph glyph-name="nine.numr" unicode="&#xf66a;" horiz-adv-x="384" 
d="M42 544q0 53 39 91t104 38q80 0 118.5 -58.5t38.5 -148.5q0 -87 -41.5 -146.5t-122.5 -59.5q-69 0 -114 48l23 32q34 -42 91 -42q59 0 88.5 47.5t30.5 115.5v19q-17 -24 -50 -44.5t-69 -20.5q-59 0 -97.5 33.5t-38.5 95.5zM86 545q0 -45 28 -69t72 -24q32 0 62 18t47 47
q-4 48 -31 83t-78 35q-46 0 -73 -28t-27 -62z" />
    <glyph glyph-name="Abreve.smcp" unicode="&#xf66d;" horiz-adv-x="562" 
d="M249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM457 679q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="Amacron.smcp" unicode="&#xf66e;" horiz-adv-x="562" 
d="M463 627h-363v37h363v-37zM249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263z" />
    <glyph glyph-name="Aogonek.smcp" unicode="&#xf66f;" horiz-adv-x="562" 
d="M249 550h64l237 -550q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 58 56 101h-2l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263z" />
    <glyph glyph-name="AEacute.smcp" unicode="&#xf670;" horiz-adv-x="811" 
d="M577 750l-172 -144h-44l154 144h62zM755 0h-369v133h-227l-85 -133h-62l355 550h388v-49h-314v-194h308v-49h-308v-209h314v-49zM386 182v308l-198 -308h198z" />
    <glyph glyph-name="Cacute.smcp" unicode="&#xf671;" horiz-adv-x="581" 
d="M506 750l-172 -144h-44l154 144h62zM344 -12q-124 0 -207.5 81t-83.5 207q0 125 83.5 205.5t207.5 80.5q130 0 211 -102l-43 -27q-28 37 -72.5 58.5t-95.5 21.5q-99 0 -166 -67t-67 -170t67 -171t166 -68q51 0 97 21.5t71 58.5l44 -27q-83 -102 -212 -102z" />
    <glyph glyph-name="Ccaron.smcp" unicode="&#xf672;" horiz-adv-x="581" 
d="M376 606h-54l-100 144h39l88 -113l83 113h40zM344 -12q-124 0 -207.5 81t-83.5 207q0 125 83.5 205.5t207.5 80.5q130 0 211 -102l-43 -27q-28 37 -72.5 58.5t-95.5 21.5q-99 0 -166 -67t-67 -170t67 -171t166 -68q51 0 97 21.5t71 58.5l44 -27q-83 -102 -212 -102z" />
    <glyph glyph-name="Ccircumflex.smcp" unicode="&#xf673;" horiz-adv-x="581" 
d="M472 606h-40l-86 113l-85 -113h-39l97 144h54zM344 -12q-124 0 -207.5 81t-83.5 207q0 125 83.5 205.5t207.5 80.5q130 0 211 -102l-43 -27q-28 37 -72.5 58.5t-95.5 21.5q-99 0 -166 -67t-67 -170t67 -171t166 -68q51 0 97 21.5t71 58.5l44 -27q-83 -102 -212 -102z" />
    <glyph glyph-name="Cdotaccent.smcp" unicode="&#xf674;" horiz-adv-x="581" 
d="M344 -12q-124 0 -207.5 81t-83.5 207q0 125 83.5 205.5t207.5 80.5q130 0 211 -102l-43 -27q-28 37 -72.5 58.5t-95.5 21.5q-99 0 -166 -67t-67 -170t67 -171t166 -68q51 0 97 21.5t71 58.5l44 -27q-83 -102 -212 -102zM347 611q-16 0 -27 11t-11 27q0 15 11 26.5
t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="Dcaron.smcp" unicode="&#xf675;" horiz-adv-x="611" 
d="M321 606h-54l-100 144h39l88 -113l83 113h40zM252 0h-185v550h185q132 0 211.5 -79.5t79.5 -196.5t-79.5 -195.5t-211.5 -78.5zM252 49q108 0 170.5 64t62.5 161q0 98 -62 162.5t-171 64.5h-130v-452h130z" />
    <glyph glyph-name="Dcroat.smcp" unicode="&#xf676;" horiz-adv-x="622" 
d="M264 0h-185v261h-68v39h68v250h185q132 0 211.5 -79.5t79.5 -196.5t-79.5 -195.5t-211.5 -78.5zM276 261h-142v-212h130q108 0 170.5 64t62.5 161q0 98 -62 162.5t-171 64.5h-130v-201h142v-39z" />
    <glyph glyph-name="Ebreve.smcp" unicode="&#xf677;" horiz-adv-x="505" 
d="M449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49zM438 679q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="Ecaron.smcp" unicode="&#xf678;" horiz-adv-x="505" 
d="M295 606h-54l-100 144h39l88 -113l83 113h40zM449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49z" />
    <glyph glyph-name="Edotaccent.smcp" unicode="&#xf679;" horiz-adv-x="505" 
d="M449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49zM269 611q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="Emacron.smcp" unicode="&#xf67a;" horiz-adv-x="505" 
d="M449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49zM444 627h-363v37h363v-37z" />
    <glyph glyph-name="Eng.smcp" unicode="&#xf67b;" horiz-adv-x="625" 
d="M366 -196q-92 0 -148 71l31 39q47 -61 115 -61q57 0 91 34.5t34 89.5v24l-353 463v-464h-55v550h55l353 -457v457h55v-576q0 -83 -49.5 -126.5t-128.5 -43.5z" />
    <glyph glyph-name="Eogonek.smcp" unicode="&#xf67c;" horiz-adv-x="505" 
d="M473 -109l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 58 56 101h-308v550h368v-49h-313v-194h307v-49h-307v-209h313v-49q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44z" />
    <glyph glyph-name="Gbreve.smcp" unicode="&#xf67d;" horiz-adv-x="630" 
d="M345 -12q-124 0 -208 80.5t-84 206.5t84 206.5t208 80.5q129 0 216 -94l-40 -32q-70 77 -176 77q-100 0 -167 -67t-67 -171t67 -171.5t167 -67.5q94 0 163 65v128h-186v48h241v-196q-86 -93 -218 -93zM521 679q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75
q92 0 147 75z" />
    <glyph glyph-name="Gcircumflex.smcp" unicode="&#xf67e;" horiz-adv-x="630" 
d="M345 -12q-124 0 -208 80.5t-84 206.5t84 206.5t208 80.5q129 0 216 -94l-40 -32q-70 77 -176 77q-100 0 -167 -67t-67 -171t67 -171.5t167 -67.5q94 0 163 65v128h-186v48h241v-196q-86 -93 -218 -93zM470 606h-40l-86 113l-85 -113h-39l97 144h54z" />
    <glyph glyph-name="Gcommaaccent.smcp" unicode="&#xf67f;" horiz-adv-x="630" 
d="M345 -12q-124 0 -208 80.5t-84 206.5t84 206.5t208 80.5q129 0 216 -94l-40 -32q-70 77 -176 77q-100 0 -167 -67t-67 -171t67 -171.5t167 -67.5q94 0 163 65v128h-186v48h241v-196q-86 -93 -218 -93zM392 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47
q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43z" />
    <glyph glyph-name="Gdotaccent.smcp" unicode="&#xf680;" horiz-adv-x="630" 
d="M345 -12q-124 0 -208 80.5t-84 206.5t84 206.5t208 80.5q129 0 216 -94l-40 -32q-70 77 -176 77q-100 0 -167 -67t-67 -171t67 -171.5t167 -67.5q94 0 163 65v128h-186v48h241v-196q-86 -93 -218 -93zM348 611q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5
t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="Hcircumflex.smcp" unicode="&#xf681;" horiz-adv-x="629" 
d="M438 606h-40l-86 113l-85 -113h-39l97 144h54zM548 0h-54v259h-358v-259h-55v550h55v-243h358v243h54v-550z" />
    <glyph glyph-name="Hbar.smcp" unicode="&#xf682;" horiz-adv-x="650" 
d="M559 0h-54v259h-358v-259h-55v411h-81v37h81v102h55v-102h358v102h54v-102h79v-37h-79v-411zM147 307h358v104h-358v-104z" />
    <glyph glyph-name="Ibreve.smcp" unicode="&#xf683;" horiz-adv-x="216" 
d="M136 0h-55v550h55v-550zM281 679q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="IJ.smcp" unicode="&#xf684;" horiz-adv-x="633" 
d="M374 -12q-92 0 -148 71l31 39q47 -61 114 -61q58 0 92 34t34 89v390h55v-392q0 -83 -50 -126.5t-128 -43.5zM136 0h-55v550h55v-550z" />
    <glyph glyph-name="Imacron.smcp" unicode="&#xf685;" horiz-adv-x="216" 
d="M291 627h-363v37h363v-37zM136 0h-55v550h55v-550z" />
    <glyph glyph-name="Iogonek.smcp" unicode="&#xf686;" horiz-adv-x="216" 
d="M136 0q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 61 61 104v547h55v-550z" />
    <glyph glyph-name="Itilde.smcp" unicode="&#xf687;" horiz-adv-x="216" 
d="M136 0h-55v550h55v-550zM170 604q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="Jcircumflex.smcp" unicode="&#xf688;" horiz-adv-x="417" 
d="M433 606h-40l-86 113l-85 -113h-39l97 144h54zM158 -12q-92 0 -148 71l31 39q47 -61 114 -61q58 0 92 34t34 89v390h55v-392q0 -83 -50 -126.5t-128 -43.5z" />
    <glyph glyph-name="Kcommaaccent.smcp" unicode="&#xf689;" horiz-adv-x="521" 
d="M311 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM501 0h-70l-235 256l-60 -65v-191h-55v550h55v-294l272 294h70l-247 -262z" />
    <glyph glyph-name="Lacute.smcp" unicode="&#xf68a;" horiz-adv-x="454" 
d="M413 0h-332v550h55v-501h277v-49zM409 750l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="Lcaron.smcp" unicode="&#xf68b;" horiz-adv-x="454" 
d="M309 500q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM413 0h-332v550h55v-501h277v-49z" />
    <glyph glyph-name="Lcommaaccent.smcp" unicode="&#xf68c;" horiz-adv-x="454" 
d="M292 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM413 0h-332v550h55v-501h277v-49z" />
    <glyph glyph-name="Ldot.smcp" unicode="&#xf68d;" horiz-adv-x="460" 
d="M413 0h-332v550h55v-501h277v-49zM387 290q0 -18 -13.5 -31t-31.5 -13t-31 13t-13 31q0 17 13 30t31 13t31.5 -13t13.5 -30z" />
    <glyph glyph-name="Nacute.smcp" unicode="&#xf68e;" horiz-adv-x="625" 
d="M474 750l-172 -144h-44l154 144h62zM544 0h-54l-354 464v-464h-55v550h55l353 -457v457h55v-550z" />
    <glyph glyph-name="Ncaron.smcp" unicode="&#xf68f;" horiz-adv-x="625" 
d="M342 606h-54l-100 144h39l88 -113l83 113h40zM544 0h-54l-354 464v-464h-55v550h55l353 -457v457h55v-550z" />
    <glyph glyph-name="Ncommaaccent.smcp" unicode="&#xf690;" horiz-adv-x="625" 
d="M355 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM544 0h-54l-354 464v-464h-55v550h55l353 -457v457h55v-550z" />
    <glyph glyph-name="Obreve.smcp" unicode="&#xf691;" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM510 679q-65 -88 -175 -88
q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="Ohungarumlaut.smcp" unicode="&#xf692;" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM384 750l-115 -144h-37l97 144
h55zM523 750l-115 -144h-37l97 144h55z" />
    <glyph glyph-name="Omacron.smcp" unicode="&#xf693;" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM515 627h-363v37h363v-37z" />
    <glyph glyph-name="Oslashacute.smcp" unicode="&#xf694;" horiz-adv-x="666" 
d="M333 -12q-87 0 -153 41l-21 -29h-51l39 54q-94 83 -94 221q0 123 77.5 205t202.5 82q82 0 149 -40l20 28h51l-38 -52q97 -83 97 -223q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 112 -70 180l-275 -384q53 -34 123 -34zM111 275
q0 -110 67 -177l274 382q-53 33 -119 33q-101 0 -161.5 -67t-60.5 -171zM495 750l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="Racute.smcp" unicode="&#xf695;" horiz-adv-x="533" 
d="M435 750l-172 -144h-44l154 144h62zM484 0h-68l-158 224h-122v-224h-55v550h218q82 0 129 -45.5t47 -116.5q0 -70 -43 -114t-115 -47zM295 273q60 0 91.5 31.5t31.5 83.5q0 51 -32.5 82t-90.5 31h-159v-228h159z" />
    <glyph glyph-name="Rcaron.smcp" unicode="&#xf696;" horiz-adv-x="533" 
d="M288 606h-54l-100 144h39l88 -113l83 113h40zM484 0h-68l-158 224h-122v-224h-55v550h218q82 0 129 -45.5t47 -116.5q0 -70 -43 -114t-115 -47zM295 273q60 0 91.5 31.5t31.5 83.5q0 51 -32.5 82t-90.5 31h-159v-228h159z" />
    <glyph glyph-name="Rcommaaccent.smcp" unicode="&#xf697;" horiz-adv-x="533" 
d="M310 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM484 0h-68l-158 224h-122v-224h-55v550h218q82 0 129 -45.5t47 -116.5q0 -70 -43 -114t-115 -47zM295 273
q60 0 91.5 31.5t31.5 83.5q0 51 -32.5 82t-90.5 31h-159v-228h159z" />
    <glyph glyph-name="Sacute.smcp" unicode="&#xf698;" horiz-adv-x="499" 
d="M410 750l-172 -144h-44l154 144h62zM250 -12q-137 0 -211 88l34 39q29 -33 76 -56t102 -23q76 0 112.5 31.5t36.5 78.5q0 44 -35.5 69.5t-86 37.5t-101 26t-86 46.5t-35.5 86.5q0 66 55 108t135 42q123 0 197 -79l-32 -38q-59 69 -165 69q-56 0 -94.5 -29t-38.5 -72
q0 -32 25.5 -53t64 -30.5t83 -23t83 -28.5t64 -48.5t25.5 -82.5q0 -70 -52.5 -114.5t-155.5 -44.5z" />
    <glyph glyph-name="Scedilla.smcp" unicode="&#xf699;" horiz-adv-x="499" 
d="M247 -189q-63 0 -97 29l16 32q35 -28 80 -28q28 0 46.5 12t18.5 33q0 39 -41 39q-25 0 -40 -17l-28 16l23 62q-121 9 -186 87l34 39q29 -33 76 -56t102 -23q76 0 112.5 31.5t36.5 78.5q0 44 -35.5 69.5t-86 37.5t-101 26t-86 46.5t-35.5 86.5q0 66 55 108t135 42
q123 0 197 -79l-32 -38q-59 69 -165 69q-56 0 -94.5 -29t-38.5 -72q0 -32 25.5 -53t64 -30.5t83 -23t83 -28.5t64 -48.5t25.5 -82.5q0 -68 -49.5 -112t-146.5 -47l-17 -45q15 12 37 12q29 0 47.5 -18t18.5 -48q0 -35 -29 -56.5t-72 -21.5z" />
    <glyph glyph-name="Scircumflex.smcp" unicode="&#xf69a;" horiz-adv-x="499" 
d="M376 606h-40l-86 113l-85 -113h-39l97 144h54zM250 -12q-137 0 -211 88l34 39q29 -33 76 -56t102 -23q76 0 112.5 31.5t36.5 78.5q0 44 -35.5 69.5t-86 37.5t-101 26t-86 46.5t-35.5 86.5q0 66 55 108t135 42q123 0 197 -79l-32 -38q-59 69 -165 69q-56 0 -94.5 -29
t-38.5 -72q0 -32 25.5 -53t64 -30.5t83 -23t83 -28.5t64 -48.5t25.5 -82.5q0 -70 -52.5 -114.5t-155.5 -44.5z" />
    <glyph glyph-name="Scommaaccent.smcp" unicode="&#xf69b;" horiz-adv-x="499" 
d="M299 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM250 -12q-137 0 -211 88l34 39q29 -33 76 -56t102 -23q76 0 112.5 31.5t36.5 78.5q0 44 -35.5 69.5t-86 37.5
t-101 26t-86 46.5t-35.5 86.5q0 66 55 108t135 42q123 0 197 -79l-32 -38q-59 69 -165 69q-56 0 -94.5 -29t-38.5 -72q0 -32 25.5 -53t64 -30.5t83 -23t83 -28.5t64 -48.5t25.5 -82.5q0 -70 -52.5 -114.5t-155.5 -44.5z" />
    <glyph glyph-name="Tbar.smcp" unicode="&#xf69c;" horiz-adv-x="465" 
d="M260 0h-55v256h-122v37h122v208h-176v49h408v-49h-177v-208h124v-37h-124v-256z" />
    <glyph glyph-name="Tcaron.smcp" unicode="&#xf69d;" horiz-adv-x="465" 
d="M262 606h-54l-100 144h39l88 -113l83 113h40zM260 0h-55v501h-176v49h408v-49h-177v-501z" />
    <glyph glyph-name="Tcommaaccent.smcp" unicode="&#xf69e;" horiz-adv-x="465" 
d="M279 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM260 0h-55v501h-176v49h408v-49h-177v-501z" />
    <glyph glyph-name="Ubreve.smcp" unicode="&#xf69f;" horiz-adv-x="613" 
d="M306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340q0 -104 -58 -163t-168 -59zM480 679q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="Uhungarumlaut.smcp" unicode="&#xf6a0;" horiz-adv-x="613" 
d="M306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340q0 -104 -58 -163t-168 -59zM354 750l-115 -144h-37l97 144h55zM493 750l-115 -144h-37l97 144h55z" />
    <glyph glyph-name="Umacron.smcp" unicode="&#xf6a1;" horiz-adv-x="613" 
d="M306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340q0 -104 -58 -163t-168 -59zM490 627h-363v37h363v-37z" />
    <glyph glyph-name="Uogonek.smcp" unicode="&#xf6a2;" horiz-adv-x="613" 
d="M418 -109l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 49 42 89h-14q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340q0 -81 -35 -135t-103 -75q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44z" />
    <glyph glyph-name="Uring.smcp" unicode="&#xf6a3;" horiz-adv-x="613" 
d="M306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340q0 -104 -58 -163t-168 -59zM309 574q-40 0 -68 28t-28 68q0 39 28 67.5t68 28.5q39 0 66.5 -28.5t27.5 -67.5q0 -40 -27.5 -68t-66.5 -28zM309 606
q26 0 44.5 19t18.5 45t-18.5 45t-44.5 19t-45 -19t-19 -45t19 -45t45 -19z" />
    <glyph glyph-name="Utilde.smcp" unicode="&#xf6a4;" horiz-adv-x="613" 
d="M306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340q0 -104 -58 -163t-168 -59zM368 604q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35
t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="Wcircumflex.smcp" unicode="&#xf6a6;" horiz-adv-x="749" 
d="M500 606h-40l-86 113l-85 -113h-39l97 144h54zM569 0h-58l-137 463l-136 -463h-58l-165 550h60l135 -471l140 471h49l139 -472l136 472h60z" />
    <glyph glyph-name="Ycircumflex.smcp" unicode="&#xf6a9;" horiz-adv-x="539" 
d="M397 606h-40l-86 113l-85 -113h-39l97 144h54zM299 0h-55v235l-232 315h66l194 -266l190 266h65l-228 -315v-235z" />
    <glyph glyph-name="Zacute.smcp" unicode="&#xf6ab;" horiz-adv-x="513" 
d="M419 750l-172 -144h-44l154 144h62zM472 0h-431v43l353 458h-353v49h424v-44l-352 -457h359v-49z" />
    <glyph glyph-name="Zdotaccent.smcp" unicode="&#xf6ac;" horiz-adv-x="513" 
d="M472 0h-431v43l353 458h-353v49h424v-44l-352 -457h359v-49zM260 611q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="Idotaccent.smcp" unicode="&#xf6ad;" horiz-adv-x="216" 
d="M136 0h-55v550h55v-550zM108 611q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="parenleft.case" unicode="&#xf6ae;" horiz-adv-x="227" 
d="M210 -86l-31 -23q-133 201 -133 442t133 442l31 -22q-53 -109 -79.5 -204t-26.5 -216t26.5 -216t79.5 -203z" />
    <glyph glyph-name="parenright.case" unicode="&#xf6af;" horiz-adv-x="227" 
d="M48 -109l-31 23q53 107 79.5 202.5t26.5 216.5q0 122 -26.5 217t-79.5 203l31 22q134 -203 134 -442t-134 -442z" />
    <glyph glyph-name="bracketleft.case" unicode="&#xf6b0;" horiz-adv-x="228" 
d="M211 -100h-174v868h174v-43h-130v-782h130v-43z" />
    <glyph glyph-name="bracketright.case" unicode="&#xf6b1;" horiz-adv-x="228" 
d="M191 -100h-174v43h130v782h-130v43h174v-868z" />
    <glyph glyph-name="braceleft.case" unicode="&#xf6b2;" horiz-adv-x="249" 
d="M232 -100h-62q-44 0 -78 34.5t-34 88.5v220q0 30 -13.5 50.5t-38.5 20.5v40q25 0 38.5 20.5t13.5 50.5v219q0 54 34 89t78 35h62v-43h-62q-28 0 -47.5 -23t-19.5 -58v-222q0 -68 -46 -88q46 -20 46 -88v-222q0 -35 19.5 -58t47.5 -23h62v-43z" />
    <glyph glyph-name="braceright.case" unicode="&#xf6b3;" horiz-adv-x="249" 
d="M17 -100v43h62q28 0 47.5 23t19.5 58v222q0 68 46 88q-46 20 -46 88v222q0 35 -19.5 58t-47.5 23h-62v43h62q44 0 78 -35t34 -89v-219q0 -30 13.5 -50.5t38.5 -20.5v-40q-25 0 -38.5 -20.5t-13.5 -50.5v-220q0 -54 -34 -88.5t-78 -34.5h-62z" />
    <glyph glyph-name="exclamdown.case" unicode="&#xf6b4;" horiz-adv-x="219" 
d="M76 0l13 493h41l14 -493h-68zM66 633q0 17 12.5 30t30.5 13t31.5 -13t13.5 -30q0 -18 -13.5 -31t-31.5 -13t-30.5 13t-12.5 31z" />
    <glyph glyph-name="questiondown.case" unicode="&#xf6b5;" horiz-adv-x="464" 
d="M250 488q42 -34 42 -79q0 -36 -20 -65t-49 -49t-58 -39.5t-49 -47t-20 -61.5q0 -46 36 -77t99 -31q61 0 101.5 23t73.5 67l36 -37q-77 -105 -215 -105q-87 0 -139 44.5t-52 109.5q0 42 21 76t51 56t60 42t51 44.5t21 53.5t-30 50zM241 676q18 0 31.5 -13t13.5 -31
q0 -17 -13.5 -30t-31.5 -13t-31 13t-13 30q0 18 13 31t31 13z" />
    <glyph glyph-name="guillemotleft.case" unicode="&#xf6b6;" horiz-adv-x="402" 
d="M372 162h-57l-160 180l160 177h57l-160 -177zM247 162h-57l-160 180l160 177h57l-160 -177z" />
    <glyph glyph-name="guillemotright.case" unicode="&#xf6b7;" horiz-adv-x="402" 
d="M247 342l-160 -180h-57l160 180l-160 177h57zM372 342l-160 -180h-57l160 180l-160 177h57z" />
    <glyph glyph-name="guilsinglleft.case" unicode="&#xf6b8;" horiz-adv-x="277" 
d="M247 162h-57l-160 180l160 177h57l-160 -177z" />
    <glyph glyph-name="guilsinglright.case" unicode="&#xf6b9;" horiz-adv-x="277" 
d="M247 342l-160 -180h-57l160 180l-160 177h57z" />
    <glyph glyph-name="hyphen.case" unicode="&#xf6ba;" horiz-adv-x="300" 
d="M270 317h-240v48h240v-48z" />
    <glyph glyph-name="endash.case" unicode="&#xf6bb;" horiz-adv-x="593" 
d="M563 317h-533v48h533v-48z" />
    <glyph glyph-name="emdash.case" unicode="&#xf6bc;" horiz-adv-x="833" 
d="M803 317h-773v48h773v-48z" />
    <glyph glyph-name="periodcentered.case" unicode="&#xf6bd;" horiz-adv-x="219" 
d="M154 343q0 -18 -13.5 -31t-31.5 -13t-31 13t-13 31q0 17 13 30t31 13t31.5 -13t13.5 -30z" />
    <glyph glyph-name="bullet.case" unicode="&#xf6be;" horiz-adv-x="358" 
d="M283 341q0 -42 -30 -72t-73 -30t-73 30t-30 72q0 43 30 73t73 30t73 -30t30 -73z" />
    <glyph glyph-name="cent.case" unicode="&#xf6bf;" horiz-adv-x="494" 
d="M257 -6v90q-92 11 -148 81.5t-56 170.5q0 99 56 169.5t148 81.5v72h45v-70q95 -5 157 -82l-36 -32q-47 62 -121 67v-413q73 3 121 68l36 -32q-62 -79 -157 -83v-88h-45zM109 336q0 -79 39.5 -135.5t108.5 -68.5v407q-69 -13 -108.5 -68.5t-39.5 -134.5z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="219" 
d="M155 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43z" />
    <glyph glyph-name="one.tnum" unicode="&#xf6dc;" horiz-adv-x="588" 
d="M372 0h-57v589l-107 -114l-35 37l147 155h52v-667z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="1103" 
d="M837 -12q-104 0 -168 90v-262h-52v667h52v-76q27 39 71.5 63.5t96.5 24.5q97 0 156.5 -68.5t59.5 -184.5q0 -115 -59.5 -184.5t-156.5 -69.5zM828 35q79 0 124 58t45 149q0 90 -45 148t-124 58q-49 0 -93 -25t-66 -61v-239q22 -36 66.5 -62t92.5 -26zM543 0h-69l-187 276
h-147v-276h-57v667h249q88 0 145.5 -53t57.5 -142q0 -87 -53.5 -138t-131.5 -54zM327 328q66 0 107 40.5t41 103.5t-41 103t-107 40h-187v-287h187z" />
    <glyph glyph-name="centinferior" unicode="&#xf6df;" horiz-adv-x="305" 
d="M160 -151v54q-58 7 -93 49t-35 102t35 102t93 49v43h32v-43q58 -4 98 -52l-29 -22q-27 37 -69 40v-235q41 3 69 41l29 -23q-40 -47 -98 -51v-54h-32zM74 54q0 -45 23.5 -77.5t62.5 -39.5v233q-40 -7 -63 -39t-23 -77z" />
    <glyph glyph-name="centsuperior" unicode="&#xf6e0;" horiz-adv-x="305" 
d="M160 416v54q-58 7 -93 49t-35 102t35 102t93 49v43h32v-43q58 -4 98 -52l-29 -22q-27 37 -69 40v-235q41 3 69 41l29 -23q-40 -47 -98 -51v-54h-32zM74 621q0 -45 23.5 -77.5t62.5 -39.5v233q-40 -7 -63 -39t-23 -77z" />
    <glyph glyph-name="commainferior" unicode="&#xf6e1;" horiz-adv-x="131" 
d="M94 -128q0 -29 -13 -54.5t-33 -38.5q-4 3 -11 8.5t-10 8.5q14 9 25.5 25.5t11.5 29.5q-2 -1 -3 -1q-11 0 -20.5 9t-9.5 21t9 21t22 9q14 0 23 -10.5t9 -27.5z" />
    <glyph glyph-name="commasuperior" unicode="&#xf6e2;" horiz-adv-x="131" 
d="M94 439q0 -29 -13 -54.5t-33 -38.5q-4 3 -10.5 8.5t-10.5 8.5q14 9 25.5 25.5t11.5 29.5q-2 -1 -3 -1q-11 0 -20.5 9t-9.5 21t9 21t22 9q14 0 23 -10.5t9 -27.5z" />
    <glyph glyph-name="dollarinferior" unicode="&#xf6e3;" horiz-adv-x="364" 
d="M171 -206v53q-92 4 -143 66l28 29q51 -53 115 -58v157q-25 6 -40 10.5t-35.5 13.5t-31 19.5t-19 27.5t-8.5 39q0 47 38 77t96 32v55h33v-56q79 -9 120 -58l-27 -27q-34 41 -93 49v-143q30 -7 49 -14t40.5 -20t32.5 -33.5t11 -48.5q0 -50 -34 -81t-99 -35v-54h-33z
M290 -40q0 30 -22.5 46t-63.5 27v-149q43 4 64.5 24.5t21.5 51.5zM84 154q0 -27 22 -41t65 -25v137q-38 -2 -62.5 -22t-24.5 -49z" />
    <glyph glyph-name="dollarsuperior" unicode="&#xf6e4;" horiz-adv-x="364" 
d="M171 361v53q-92 4 -143 66l28 29q51 -53 115 -58v157q-25 6 -40 10.5t-35.5 13.5t-31 19.5t-19 27.5t-8.5 39q0 47 38 77t96 32v55h33v-56q79 -9 120 -58l-27 -27q-34 41 -93 49v-143q30 -7 49 -14t40.5 -20t32.5 -33.5t11 -48.5q0 -50 -34 -81t-99 -35v-54h-33zM290 527
q0 30 -22.5 46t-63.5 27v-149q43 4 64.5 24.5t21.5 51.5zM84 721q0 -27 22 -41t65 -25v137q-38 -2 -62.5 -22t-24.5 -49z" />
    <glyph glyph-name="hypheninferior" unicode="&#xf6e5;" horiz-adv-x="184" 
d="M166 35h-148v36h148v-36z" />
    <glyph glyph-name="hyphensuperior" unicode="&#xf6e6;" horiz-adv-x="184" 
d="M166 603h-148v36h148v-36z" />
    <glyph glyph-name="periodinferior" unicode="&#xf6e7;" horiz-adv-x="136" 
d="M99 -120q0 -13 -9 -22t-22 -9q-12 0 -21.5 9t-9.5 22q0 12 9.5 21t21.5 9q13 0 22 -9t9 -21z" />
    <glyph glyph-name="periodsuperior" unicode="&#xf6e8;" horiz-adv-x="136" 
d="M99 447q0 -13 -9 -22t-22 -9q-12 0 -21.5 9t-9.5 22q0 12 9.5 21t21.5 9q13 0 22 -9t9 -21z" />
    <glyph glyph-name="asuperior" unicode="&#xf6e9;" horiz-adv-x="375" 
d="M310 326h-42v37q-42 -45 -110 -45q-45 0 -78.5 28.5t-33.5 76.5t33.5 75.5t78.5 27.5q68 0 110 -44v61q0 33 -25 51.5t-60 18.5q-62 0 -100 -48l-22 28q51 55 127 55q54 0 88 -26t34 -79v-217zM174 348q61 0 94 42v65q-32 41 -94 41q-37 0 -61 -20.5t-24 -52.5
q0 -33 24 -54t61 -21z" />
    <glyph glyph-name="bsuperior" unicode="&#xf6ea;" horiz-adv-x="408" 
d="M210 353q51 0 80.5 36.5t29.5 93.5t-29.5 93.5t-80.5 36.5q-31 0 -59.5 -15.5t-42.5 -36.5v-156q14 -22 42.5 -37t59.5 -15zM108 326h-43v434h43v-170q42 58 111 58q65 0 105 -45t40 -120q0 -76 -40 -120.5t-105 -44.5q-72 0 -111 57v-49z" />
    <glyph glyph-name="dsuperior" unicode="&#xf6eb;" horiz-adv-x="409" 
d="M344 326h-42v49q-43 -57 -113 -57q-64 0 -104.5 45t-40.5 120t40.5 120t104.5 45q70 0 112 -58v170h43v-434zM198 353q32 0 60.5 15t43.5 37v156q-15 22 -44 37t-60 15q-51 0 -80 -36.5t-29 -93.5q0 -58 29 -94t80 -36z" />
    <glyph glyph-name="esuperior" unicode="&#xf6ec;" horiz-adv-x="401" 
d="M209 318q-72 0 -118.5 46.5t-46.5 118.5q0 69 45.5 117t113.5 48q71 0 112.5 -48t41.5 -120v-11h-268q3 -49 36.5 -83t85.5 -34q62 0 105 41l20 -25q-52 -50 -127 -50zM316 502q-1 43 -31 78t-83 35q-51 0 -81 -34.5t-32 -78.5h227z" />
    <glyph glyph-name="isuperior" unicode="&#xf6ed;" horiz-adv-x="152" 
d="M76 690q-12 0 -21 9t-9 21t9 21t21 9t21 -9t9 -21t-9 -21t-21 -9zM97 326h-43v314h43v-314z" />
    <glyph glyph-name="lsuperior" unicode="&#xf6ee;" horiz-adv-x="172" 
d="M108 326h-43v434h43v-434z" />
    <glyph glyph-name="msuperior" unicode="&#xf6ef;" horiz-adv-x="561" 
d="M496 326h-42v214q0 72 -63 72q-25 0 -50.5 -15.5t-38.5 -35.5v-235h-42v214q0 72 -64 72q-24 0 -49.5 -15.5t-38.5 -35.5v-235h-43v314h43v-47q12 19 42 37t62 18q34 0 56.5 -17t29.5 -42q15 23 45 41t64 18q89 0 89 -101v-221z" />
    <glyph glyph-name="osuperior" unicode="&#xf6f0;" horiz-adv-x="405" 
d="M202 318q-70 0 -114 47.5t-44 117.5t44 117.5t114 47.5q71 0 115 -47t44 -118q0 -70 -44 -117.5t-115 -47.5zM202 355q53 0 84 36.5t31 91.5q0 54 -31 90.5t-84 36.5q-52 0 -82.5 -36t-30.5 -91t30.5 -91.5t82.5 -36.5z" />
    <glyph glyph-name="rsuperior" unicode="&#xf6f1;" horiz-adv-x="233" 
d="M108 326h-43v314h43v-52q46 59 108 59v-42l-19 1q-24 0 -51.5 -16t-37.5 -36v-228z" />
    <glyph glyph-name="ssuperior" unicode="&#xf6f2;" horiz-adv-x="327" 
d="M159 318q-79 0 -128 52l23 27q39 -47 105 -47q39 0 61 17t22 44q0 28 -32 43t-70 20t-70 26t-32 60q0 37 32 62.5t87 25.5q78 0 117 -47l-20 -26q-32 41 -97 41q-35 0 -56 -16t-21 -41t32 -38t70 -17.5t70 -27t32 -63.5t-32.5 -68t-92.5 -27z" />
    <glyph glyph-name="tsuperior" unicode="&#xf6f3;" horiz-adv-x="193" 
d="M131 318q-33 0 -51 19t-18 53v217h-53v33h53v86h43v-86h65v-33h-65v-212q0 -42 33 -42q24 0 35 15q2 -5 7 -15t7 -15q-20 -20 -56 -20z" />
    <glyph glyph-name="Lslash.smcp" unicode="&#xf6f9;" horiz-adv-x="482" 
d="M11 185v52l99 57v256h55v-224l100 59v-54l-100 -58v-224h277v-49h-332v242z" />
    <glyph glyph-name="OE.smcp" unicode="&#xf6fa;" horiz-adv-x="964" 
d="M908 0h-369v113q-65 -125 -211 -125q-123 0 -199 82t-76 205t76 205t199 82q145 0 211 -124v112h369v-49h-314v-194h308v-49h-308v-209h314v-49zM539 191v169q-24 77 -80 115t-125 38q-101 0 -162 -67t-61 -171q0 -103 61 -170.5t162 -67.5q71 0 126 38.5t79 115.5z" />
    <glyph glyph-name="Scaron.smcp" unicode="&#xf6fd;" horiz-adv-x="499" 
d="M280 606h-54l-100 144h39l88 -113l83 113h40zM250 -12q-137 0 -211 88l34 39q29 -33 76 -56t102 -23q76 0 112.5 31.5t36.5 78.5q0 44 -35.5 69.5t-86 37.5t-101 26t-86 46.5t-35.5 86.5q0 66 55 108t135 42q123 0 197 -79l-32 -38q-59 69 -165 69q-56 0 -94.5 -29
t-38.5 -72q0 -32 25.5 -53t64 -30.5t83 -23t83 -28.5t64 -48.5t25.5 -82.5q0 -70 -52.5 -114.5t-155.5 -44.5z" />
    <glyph glyph-name="Zcaron.smcp" unicode="&#xf6ff;" horiz-adv-x="513" 
d="M283 606h-54l-100 144h39l88 -113l83 113h40zM472 0h-431v43l353 458h-353v49h424v-44l-352 -457h359v-49z" />
    <glyph glyph-name="exclam.smcp" unicode="&#xf721;" horiz-adv-x="219" 
d="M142 550l-13 -401h-39l-13 401h65zM154 35q0 -18 -13 -31.5t-31 -13.5t-31 13.5t-13 31.5q0 17 13 30t31 13t31 -13t13 -30z" />
    <glyph glyph-name="dollar.onum" unicode="&#xf724;" horiz-adv-x="499" 
d="M229 -74v63q-122 7 -190 87l34 39q26 -30 67 -52t89 -26v228q-41 10 -65 19t-52.5 25t-42 42t-13.5 61q0 62 49.5 103t123.5 46v67h45v-67q103 -8 169 -78l-32 -38q-50 59 -137 67v-203q41 -11 68 -20.5t56.5 -28t44.5 -46.5t15 -67q0 -66 -46 -109.5t-138 -48.5v-63h-45
zM400 146q0 44 -32.5 67.5t-93.5 40.5v-217q63 5 94.5 35.5t31.5 73.5zM113 413q0 -37 29 -56.5t87 -35.5v192q-51 -5 -83.5 -32.5t-32.5 -67.5z" />
    <glyph glyph-name="ampersand.smcp" unicode="&#xf726;" horiz-adv-x="552" 
d="M525 0h-72q-32 27 -74 67q-74 -79 -172 -79q-78 0 -127.5 42t-49.5 113q0 59 34 97t94 68q-46 67 -46 125q0 56 40.5 92.5t98.5 36.5q53 0 88.5 -28t35.5 -80q0 -30 -11.5 -53.5t-37 -43.5t-44.5 -30.5t-56 -29.5q29 -37 74 -82q12 -13 76 -80q40 56 64 134l45 -18
q-33 -87 -76 -147q33 -32 116 -104zM210 30q74 0 136 67q-38 37 -84 85q-45 48 -80 92q-47 -26 -73 -56t-26 -73q0 -50 35.5 -82.5t91.5 -32.5zM161 432q0 -42 40 -102q43 20 65 33t42 37t20 53q0 33 -20.5 51t-55.5 18q-39 0 -65 -26t-26 -64z" />
    <glyph glyph-name="zero.onum" unicode="&#xf730;" horiz-adv-x="608" 
d="M551 275q0 -74 -24.5 -137t-82 -106.5t-139.5 -43.5q-83 0 -140.5 43.5t-82 106.5t-24.5 137q0 55 14.5 104.5t43.5 91.5t77.5 66.5t111.5 24.5q82 0 139.5 -43.5t82 -106.5t24.5 -137zM490 275q0 98 -46 166.5t-139 68.5q-94 0 -140.5 -69t-46.5 -166t46.5 -166
t140.5 -69q93 0 139 69t46 166z" />
    <glyph glyph-name="one.onum" unicode="&#xf731;" horiz-adv-x="308" 
d="M225 0h-57v472l-107 -114l-35 37l147 155h52v-550z" />
    <glyph glyph-name="two.onum" unicode="&#xf732;" horiz-adv-x="576" 
d="M479 0h-415v48q183 80 275.5 163.5t92.5 165.5q0 58 -44 95.5t-107 37.5q-60 0 -111 -23.5t-80 -63.5l-38 36q34 48 96.5 75.5t133.5 27.5q87 0 147 -49.5t60 -129.5q0 -93 -87.5 -181t-228.5 -150h306v-52z" />
    <glyph glyph-name="three.onum" unicode="&#xf733;" horiz-adv-x="542" 
d="M257 -128q-78 0 -137 31.5t-90 79.5l39 34q65 -93 187 -93q77 0 122.5 38t45.5 104q0 68 -49.5 101.5t-129.5 33.5q-52 0 -62 -1v54q10 -1 62 -1q72 0 120.5 32t48.5 95q0 60 -46 94.5t-114 34.5q-103 0 -179 -85l-36 36q83 101 218 101q91 0 153 -47t62 -129
q0 -47 -25 -83t-55 -51.5t-63 -21.5q54 -6 103.5 -49t49.5 -117q0 -84 -60.5 -137.5t-164.5 -53.5z" />
    <glyph glyph-name="four.onum" unicode="&#xf734;" horiz-adv-x="541" 
d="M506 66h-98v-183h-57v183h-316v52l296 432h77v-432h98v-52zM351 118v375l-257 -375h257z" />
    <glyph glyph-name="five.onum" unicode="&#xf735;" horiz-adv-x="582" 
d="M299 -129q-149 0 -225 109l38 38q67 -95 187 -95q72 0 119.5 46t47.5 115q0 75 -46.5 119t-119.5 44q-91 0 -162 -65l-43 18v350h385v-52h-328v-261q64 62 159 62q89 0 151 -56.5t62 -156.5q0 -97 -65 -156t-160 -59z" />
    <glyph glyph-name="six.onum" unicode="&#xf736;" horiz-adv-x="584" 
d="M303 -12q-52 0 -94 19t-70 51t-46.5 76.5t-26.5 93.5t-8 105q0 53 8.5 101.5t28.5 93.5t48.5 77.5t72.5 52t97 19.5q112 0 180 -85l-34 -42q-55 75 -146 75q-52 0 -91.5 -25.5t-61.5 -68.5t-33 -93t-11 -106q0 -21 1 -31q25 41 79 76t116 35q95 0 155 -54.5t60 -156.5
q0 -87 -61 -150t-163 -63zM301 40q79 0 123.5 49.5t44.5 110.5q0 79 -46 120t-121 41q-54 0 -103.5 -31.5t-78.5 -82.5q4 -51 22 -95t59 -78t100 -34z" />
    <glyph glyph-name="seven.onum" unicode="&#xf737;" horiz-adv-x="502" 
d="M185 -117h-63l280 615h-369v52h437v-40z" />
    <glyph glyph-name="eight.onum" unicode="&#xf738;" 
d="M286 -12q-98 0 -162.5 49.5t-64.5 129.5q0 65 48 111.5t118 65.5q-66 18 -110.5 58.5t-44.5 102.5q0 83 64 127.5t152 44.5q86 0 151 -44.5t65 -127.5q0 -62 -44.5 -102.5t-110.5 -58.5q70 -19 117.5 -65t47.5 -112q0 -80 -64.5 -129.5t-161.5 -49.5zM286 368
q22 4 43 11.5t50 22t47 40t18 57.5q0 58 -45 92t-113 34t-113 -34t-45 -92q0 -32 18 -57.5t47 -40.5t50 -22t43 -11zM286 40q68 0 118.5 36.5t50.5 94.5q0 36 -20 65t-50 45.5t-55 25.5t-44 11q-19 -2 -44.5 -11t-55 -25.5t-49.5 -45.5t-20 -65q0 -59 49.5 -95t119.5 -36z
" />
    <glyph glyph-name="nine.onum" unicode="&#xf739;" horiz-adv-x="584" 
d="M271 -128q-113 0 -179 85l33 41q55 -74 146 -74q53 0 92.5 25.5t61.5 69t32.5 93t10.5 105.5v31q-26 -41 -80.5 -76.5t-115.5 -35.5q-95 0 -155 55t-60 157q0 87 61 150t163 63q52 0 94 -19t70 -51t46.5 -76.5t26.5 -93.5t8 -105q0 -53 -8.5 -101.5t-28.5 -93.5
t-48.5 -77.5t-72.5 -52t-97 -19.5zM282 188q54 0 103.5 31.5t78.5 82.5q-4 51 -22 95t-59 78t-100 34q-79 0 -123.5 -49.5t-44.5 -110.5q0 -79 46 -120t121 -41z" />
    <glyph glyph-name="question.smcp" unicode="&#xf73f;" horiz-adv-x="397" 
d="M369 433q0 -30 -13 -54.5t-33 -40.5t-42.5 -31.5t-42.5 -27.5t-33 -29t-13 -36q0 -25 29 -43l-39 -23q-39 30 -39 67q0 29 18 53t43 40.5t50.5 32.5t43.5 37.5t18 47.5q0 39 -32 63.5t-86 24.5q-96 0 -155 -76l-31 33q68 90 189 90q76 0 122 -37t46 -91zM233 35
q0 -18 -13 -31.5t-31 -13.5t-31 13.5t-13 31.5q0 17 13 30t31 13t31 -13t13 -30z" />
    <glyph glyph-name="A.smcp" unicode="&#xf761;" horiz-adv-x="562" 
d="M249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263z" />
    <glyph glyph-name="B.smcp" unicode="&#xf762;" horiz-adv-x="541" 
d="M324 0h-243v550h238q74 0 117.5 -38t43.5 -103q0 -47 -27.5 -81t-66.5 -44q44 -11 75 -50t31 -85q0 -68 -45 -108.5t-123 -40.5zM318 49q54 0 85 28t31 76q0 43 -31 74t-85 31h-182v-209h182zM314 307q52 0 80.5 27t28.5 70q0 44 -28.5 70.5t-80.5 26.5h-178v-194h178z
" />
    <glyph glyph-name="C.smcp" unicode="&#xf763;" horiz-adv-x="581" 
d="M344 -12q-124 0 -207.5 81t-83.5 207q0 125 83.5 205.5t207.5 80.5q130 0 211 -102l-43 -27q-28 37 -72.5 58.5t-95.5 21.5q-99 0 -166 -67t-67 -170t67 -171t166 -68q51 0 97 21.5t71 58.5l44 -27q-83 -102 -212 -102z" />
    <glyph glyph-name="D.smcp" unicode="&#xf764;" horiz-adv-x="611" 
d="M266 0h-185v550h185q132 0 211.5 -79.5t79.5 -196.5t-79.5 -195.5t-211.5 -78.5zM266 49q108 0 170.5 64t62.5 161q0 98 -62 162.5t-171 64.5h-130v-452h130z" />
    <glyph glyph-name="E.smcp" unicode="&#xf765;" horiz-adv-x="505" 
d="M449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49z" />
    <glyph glyph-name="F.smcp" unicode="&#xf766;" horiz-adv-x="484" 
d="M136 0h-55v550h368v-49h-313v-194h307v-49h-307v-258z" />
    <glyph glyph-name="G.smcp" unicode="&#xf767;" horiz-adv-x="630" 
d="M345 -12q-124 0 -208 80.5t-84 206.5t84 206.5t208 80.5q129 0 216 -94l-40 -32q-70 77 -176 77q-100 0 -167 -67t-67 -171t67 -171.5t167 -67.5q94 0 163 65v128h-186v48h241v-196q-86 -93 -218 -93z" />
    <glyph glyph-name="H.smcp" unicode="&#xf768;" horiz-adv-x="629" 
d="M548 0h-54v259h-358v-259h-55v550h55v-243h358v243h54v-550z" />
    <glyph glyph-name="I.smcp" unicode="&#xf769;" horiz-adv-x="216" 
d="M136 0h-55v550h55v-550z" />
    <glyph glyph-name="J.smcp" unicode="&#xf76a;" horiz-adv-x="417" 
d="M158 -12q-92 0 -148 71l31 39q47 -61 114 -61q58 0 92 34t34 89v390h55v-392q0 -83 -50 -126.5t-128 -43.5z" />
    <glyph glyph-name="K.smcp" unicode="&#xf76b;" horiz-adv-x="521" 
d="M501 0h-70l-235 256l-60 -65v-191h-55v550h55v-294l272 294h70l-247 -262z" />
    <glyph glyph-name="L.smcp" unicode="&#xf76c;" horiz-adv-x="454" 
d="M413 0h-332v550h55v-501h277v-49z" />
    <glyph glyph-name="M.smcp" unicode="&#xf76d;" horiz-adv-x="703" 
d="M622 0h-55v469l-204 -469h-23l-204 469v-469h-55v550h76l194 -446l194 446h77v-550z" />
    <glyph glyph-name="N.smcp" unicode="&#xf76e;" horiz-adv-x="625" 
d="M544 0h-54l-354 464v-464h-55v550h55l353 -457v457h55v-550z" />
    <glyph glyph-name="O.smcp" unicode="&#xf76f;" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5z" />
    <glyph glyph-name="P.smcp" unicode="&#xf770;" horiz-adv-x="509" 
d="M136 0h-55v550h218q84 0 129.5 -46.5t45.5 -115.5q0 -70 -45.5 -117t-129.5 -47h-163v-224zM295 273q59 0 90.5 31.5t31.5 83.5q0 50 -32 81.5t-90 31.5h-159v-228h159z" />
    <glyph glyph-name="Q.smcp" unicode="&#xf771;" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -129 -83 -210l56 -60l-35 -32l-58 61q-68 -46 -159 -46zM333 37q71 0 124 36l-84 90l35 33l85 -92q62 67 62 171t-61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5
t161 -67.5z" />
    <glyph glyph-name="R.smcp" unicode="&#xf772;" horiz-adv-x="533" 
d="M484 0h-68l-158 224h-122v-224h-55v550h218q82 0 129 -45.5t47 -116.5q0 -70 -43 -114t-115 -47zM295 273q60 0 91.5 31.5t31.5 83.5q0 51 -32.5 82t-90.5 31h-159v-228h159z" />
    <glyph glyph-name="S.smcp" unicode="&#xf773;" horiz-adv-x="499" 
d="M250 -12q-137 0 -211 88l34 39q29 -33 76 -56t102 -23q76 0 112.5 31.5t36.5 78.5q0 44 -35.5 69.5t-86 37.5t-101 26t-86 46.5t-35.5 86.5q0 66 55 108t135 42q123 0 197 -79l-32 -38q-59 69 -165 69q-56 0 -94.5 -29t-38.5 -72q0 -32 25.5 -53t64 -30.5t83 -23
t83 -28.5t64 -48.5t25.5 -82.5q0 -70 -52.5 -114.5t-155.5 -44.5z" />
    <glyph glyph-name="T.smcp" unicode="&#xf774;" horiz-adv-x="465" 
d="M260 0h-55v501h-176v49h408v-49h-177v-501z" />
    <glyph glyph-name="U.smcp" unicode="&#xf775;" horiz-adv-x="613" 
d="M306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340q0 -104 -58 -163t-168 -59z" />
    <glyph glyph-name="V.smcp" unicode="&#xf776;" horiz-adv-x="562" 
d="M313 0h-64l-237 550h63l206 -488l208 488h61z" />
    <glyph glyph-name="W.smcp" unicode="&#xf777;" horiz-adv-x="749" 
d="M569 0h-58l-137 463l-136 -463h-58l-165 550h60l135 -471l140 471h49l139 -472l136 472h60z" />
    <glyph glyph-name="X.smcp" unicode="&#xf778;" horiz-adv-x="560" 
d="M546 0h-68l-198 244l-198 -244h-68l232 282l-219 268h68l185 -229l185 229h68l-219 -267z" />
    <glyph glyph-name="Y.smcp" unicode="&#xf779;" horiz-adv-x="539" 
d="M299 0h-55v235l-232 315h66l194 -266l190 266h65l-228 -315v-235z" />
    <glyph glyph-name="Z.smcp" unicode="&#xf77a;" horiz-adv-x="513" 
d="M472 0h-431v43l353 458h-353v49h424v-44l-352 -457h359v-49z" />
    <glyph glyph-name="Agrave.smcp" unicode="&#xf7e0;" horiz-adv-x="562" 
d="M249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM336 606h-44l-172 144h62z" />
    <glyph glyph-name="Aacute.smcp" unicode="&#xf7e1;" horiz-adv-x="562" 
d="M443 750l-172 -144h-44l154 144h62zM249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263z" />
    <glyph glyph-name="Acircumflex.smcp" unicode="&#xf7e2;" horiz-adv-x="562" 
d="M408 606h-40l-86 113l-85 -113h-39l97 144h54zM249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263z" />
    <glyph glyph-name="Atilde.smcp" unicode="&#xf7e3;" horiz-adv-x="562" 
d="M249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM343 604q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36
q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="Adieresis.smcp" unicode="&#xf7e4;" horiz-adv-x="562" 
d="M420 654q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM216 654q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263z" />
    <glyph glyph-name="Aring.smcp" unicode="&#xf7e5;" horiz-adv-x="562" 
d="M282 574q-40 0 -68 28t-28 68q0 39 28 67.5t68 28.5q39 0 66.5 -28.5t27.5 -67.5q0 -40 -27.5 -68t-66.5 -28zM282 606q26 0 44.5 19t18.5 45t-18.5 45t-44.5 19t-45 -19t-19 -45t19 -45t45 -19zM249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308
l-132 -308h263z" />
    <glyph glyph-name="AE.smcp" unicode="&#xf7e6;" horiz-adv-x="811" 
d="M755 0h-369v133h-227l-85 -133h-62l355 550h388v-49h-314v-194h308v-49h-308v-209h314v-49zM386 182v308l-198 -308h198z" />
    <glyph glyph-name="Ccedilla.smcp" unicode="&#xf7e7;" horiz-adv-x="581" 
d="M345 -189q-63 0 -97 29l16 32q35 -28 80 -28q28 0 46.5 12t18.5 33q0 39 -41 39q-25 0 -40 -17l-28 16l23 62q-117 8 -193.5 87.5t-76.5 199.5q0 125 83.5 205.5t207.5 80.5q130 0 211 -102l-43 -27q-28 37 -72.5 58.5t-95.5 21.5q-99 0 -166 -67t-67 -170t67 -171
t166 -68q51 0 97 21.5t71 58.5l44 -27q-78 -95 -196 -101l-17 -46q15 12 37 12q29 0 47.5 -18t18.5 -48q0 -35 -29 -56.5t-72 -21.5z" />
    <glyph glyph-name="Egrave.smcp" unicode="&#xf7e8;" horiz-adv-x="505" 
d="M449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49zM319 606h-44l-172 144h62z" />
    <glyph glyph-name="Eacute.smcp" unicode="&#xf7e9;" horiz-adv-x="505" 
d="M427 750l-172 -144h-44l154 144h62zM449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49z" />
    <glyph glyph-name="Ecircumflex.smcp" unicode="&#xf7ea;" horiz-adv-x="505" 
d="M393 606h-40l-86 113l-85 -113h-39l97 144h54zM449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49z" />
    <glyph glyph-name="Edieresis.smcp" unicode="&#xf7eb;" horiz-adv-x="505" 
d="M403 651q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM199 651q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49z" />
    <glyph glyph-name="Igrave.smcp" unicode="&#xf7ec;" horiz-adv-x="216" 
d="M136 0h-55v550h55v-550zM162 606h-44l-172 144h62z" />
    <glyph glyph-name="Iacute.smcp" unicode="&#xf7ed;" horiz-adv-x="216" 
d="M270 750l-172 -144h-44l154 144h62zM136 0h-55v550h55v-550z" />
    <glyph glyph-name="Icircumflex.smcp" unicode="&#xf7ee;" horiz-adv-x="216" 
d="M234 606h-40l-86 113l-85 -113h-39l97 144h54zM136 0h-55v550h55v-550z" />
    <glyph glyph-name="Idieresis.smcp" unicode="&#xf7ef;" horiz-adv-x="216" 
d="M248 651q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM44 651q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM136 0h-55v550h55v-550z" />
    <glyph glyph-name="Eth.smcp" unicode="&#xf7f0;" horiz-adv-x="622" 
d="M262 0h-185v261h-68v39h68v250h185q132 0 211.5 -79.5t79.5 -196.5t-79.5 -195.5t-211.5 -78.5zM274 261h-142v-212h130q108 0 170.5 64t62.5 161q0 98 -62 162.5t-171 64.5h-130v-201h142v-39z" />
    <glyph glyph-name="Ntilde.smcp" unicode="&#xf7f1;" horiz-adv-x="625" 
d="M544 0h-54l-354 464v-464h-55v550h55l353 -457v457h55v-550zM373 604q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="Ograve.smcp" unicode="&#xf7f2;" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM388 606h-44l-172 144h62z" />
    <glyph glyph-name="Oacute.smcp" unicode="&#xf7f3;" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM495 750l-172 -144h-44l154 144
h62z" />
    <glyph glyph-name="Ocircumflex.smcp" unicode="&#xf7f4;" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM459 606h-40l-86 113l-85 -113
h-39l97 144h54z" />
    <glyph glyph-name="Otilde.smcp" unicode="&#xf7f5;" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM397 604q-25 0 -43.5 16
t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="Odieresis.smcp" unicode="&#xf7f6;" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM473 651q0 -15 -11 -26t-26 -11
t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM269 651q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26z" />
    <glyph glyph-name="Oslash.smcp" unicode="&#xf7f8;" horiz-adv-x="666" 
d="M333 -12q-87 0 -153 41l-21 -29h-51l39 54q-94 83 -94 221q0 123 77.5 205t202.5 82q82 0 149 -40l20 28h51l-38 -52q97 -83 97 -223q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 112 -70 180l-275 -384q53 -34 123 -34zM111 275
q0 -110 67 -177l274 382q-53 33 -119 33q-101 0 -161.5 -67t-60.5 -171z" />
    <glyph glyph-name="Ugrave.smcp" unicode="&#xf7f9;" horiz-adv-x="613" 
d="M306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340q0 -104 -58 -163t-168 -59zM361 606h-44l-172 144h62z" />
    <glyph glyph-name="Uacute.smcp" unicode="&#xf7fa;" horiz-adv-x="613" 
d="M468 750l-172 -144h-44l154 144h62zM306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340q0 -104 -58 -163t-168 -59z" />
    <glyph glyph-name="Ucircumflex.smcp" unicode="&#xf7fb;" horiz-adv-x="613" 
d="M432 606h-40l-86 113l-85 -113h-39l97 144h54zM306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340q0 -104 -58 -163t-168 -59z" />
    <glyph glyph-name="Udieresis.smcp" unicode="&#xf7fc;" horiz-adv-x="613" 
d="M445 651q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM241 651q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340
q0 -104 -58 -163t-168 -59z" />
    <glyph glyph-name="Yacute.smcp" unicode="&#xf7fd;" horiz-adv-x="539" 
d="M434 750l-172 -144h-44l154 144h62zM299 0h-55v235l-232 315h66l194 -266l190 266h65l-228 -315v-235z" />
    <glyph glyph-name="Thorn.smcp" unicode="&#xf7fe;" horiz-adv-x="509" 
d="M136 0h-55v550h55v-97h163q84 0 129.5 -47t45.5 -116q0 -70 -45.5 -116.5t-129.5 -46.5h-163v-127zM295 176q59 0 90.5 31.5t31.5 82.5t-31.5 82.5t-90.5 31.5h-159v-228h159z" />
    <glyph glyph-name="Ydieresis.smcp" unicode="&#xf7ff;" horiz-adv-x="539" 
d="M411 651q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM207 651q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM299 0h-55v235l-232 315h66l194 -266l190 266h65l-228 -315v-235z" />
    <glyph glyph-name="uniF8FF" unicode="&#xf8ff;" horiz-adv-x="667" 
d="M230 154v77h-78q-32 0 -53.5 -22.5t-21.5 -54.5q0 -33 22 -56t54 -23t54.5 23.5t22.5 55.5zM230 435v79q0 32 -22 54.5t-54 22.5t-54.5 -22.5t-22.5 -54.5t22.5 -55.5t54.5 -23.5h76zM265 265h137v137h-137v-137zM590 154q0 32 -21.5 54.5t-53.5 22.5h-78v-77
q0 -32 22.5 -55.5t54.5 -23.5t54 23t22 56zM590 514q0 32 -22 54.5t-54 22.5t-54.5 -22.5t-22.5 -54.5v-79h78q32 0 53.5 23t21.5 56zM625 154q0 -46 -32.5 -79t-78.5 -33t-79 33t-33 79v77h-137v-77q0 -46 -33 -79t-79 -33t-78.5 33t-32.5 79t32.5 78.5t78.5 32.5h77v137
h-76q-46 0 -79 33t-33 79t33 78.5t79 32.5t78.5 -32.5t32.5 -78.5v-79h137v79q0 46 33 78.5t79 32.5t78.5 -32.5t32.5 -78.5t-32.5 -79t-78.5 -33h-77v-137h77q46 0 78.5 -32.5t32.5 -78.5z" />
    <glyph glyph-name="zero.pzero" horiz-adv-x="608" 
d="M305 -12q-90 0 -150 60l-29 -48h-51l50 83q-67 97 -67 250q0 63 14 121.5t41.5 109.5t77 82t114.5 31q86 0 147 -58l30 48h51l-51 -83q69 -98 69 -251q0 -62 -14 -121t-42 -110.5t-77 -82.5t-113 -31zM305 40q50 0 87.5 26t58 69.5t30 93t9.5 104.5q0 118 -40 198
l-266 -435q46 -56 121 -56zM118 333q0 -120 40 -197l265 435q-46 54 -118 54q-51 0 -88.5 -25.5t-58 -69t-30.5 -93t-10 -104.5z" />
    <glyph glyph-name="zero.tonumzero" horiz-adv-x="588" 
d="M541 275q0 -74 -24.5 -137t-82 -106.5t-139.5 -43.5q-89 0 -150 50l-27 -38h-52l49 68q-67 80 -67 207q0 55 14.5 104.5t43.5 91.5t77.5 66.5t111.5 24.5q83 0 142 -45l23 33h51l-44 -61q74 -80 74 -214zM480 275q0 103 -47 167l-256 -359q46 -43 118 -43q93 0 139 69
t46 166zM108 275q0 -97 41 -159l256 357q-45 37 -110 37q-94 0 -140.5 -69t-46.5 -166z" />
    <glyph glyph-name="zero.onumzero" horiz-adv-x="608" 
d="M552 275q0 -74 -24.5 -137t-82 -106.5t-139.5 -43.5q-89 0 -150 50l-27 -38h-52l49 68q-67 80 -67 207q0 55 14.5 104.5t43.5 91.5t77.5 66.5t111.5 24.5q83 0 142 -45l23 33h51l-44 -61q74 -80 74 -214zM491 275q0 103 -47 167l-256 -359q46 -43 118 -43q93 0 139 69
t46 166zM119 275q0 -97 41 -159l256 357q-45 37 -110 37q-94 0 -140.5 -69t-46.5 -166z" />
    <glyph glyph-name="G.alt1" horiz-adv-x="719" 
d="M393 -13q-141 0 -238 97t-97 249t97.5 248.5t240.5 96.5q152 0 251 -114l-43 -31q-36 43 -90.5 68t-117.5 25q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -211t198.5 -83q110 0 172.5 68.5t73.5 172.5h-302v51h364q0 -153 -82 -248.5t-229 -95.5z" />
    <glyph glyph-name="a.alt1" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z" />
    <glyph glyph-name="f.alt1" horiz-adv-x="265" 
d="M150 0h-52v437h-80v46h80v44q0 71 35 110.5t96 39.5q39 0 65 -15l-16 -41q-19 11 -44 11q-84 0 -84 -105v-44h98v-46h-98v-437z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="256" 
d="M175 -12q-94 0 -94 105v574h52v-566q0 -66 50 -66q33 0 54 23l20 -40q-32 -30 -82 -30z" />
    <glyph glyph-name="y.alt1" horiz-adv-x="540" 
d="M253 -196q-109 0 -178 73l30 40q54 -68 148 -68q72 0 113 40.5t41 114.5v72q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37t-38 118v335h52v-324q0 -66 30 -92.5t87 -26.5q45 0 88.5 23t68.5 57v363h52v-479q0 -100 -59.5 -150t-146.5 -50z" />
    <glyph glyph-name="G.smcp.alt1" horiz-adv-x="647" 
d="M343 -12q-123 0 -206.5 80.5t-83.5 206.5t84 206.5t208 80.5q129 0 216 -94l-40 -32q-70 77 -176 77q-100 0 -167 -67t-67 -171t67 -171.5t167 -67.5q81 0 138 52t67 141h-228v48h287q0 -128 -73 -208.5t-193 -80.5z" />
    <glyph glyph-name="Gbreve.alt1" horiz-adv-x="745" 
d="M574 796q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75zM393 -13q-141 0 -238 97t-97 249t97.5 248.5t240.5 96.5q152 0 251 -114l-43 -31q-36 43 -90.5 68t-117.5 25q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -211t198.5 -83q110 0 172.5 68.5
t73.5 172.5h-302v51h364q0 -153 -82 -248.5t-229 -95.5z" />
    <glyph glyph-name="Gcommaaccent.alt1" horiz-adv-x="745" 
d="M444 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM393 -13q-141 0 -238 97t-97 249t97.5 248.5t240.5 96.5q152 0 251 -114l-43 -31q-36 43 -90.5 68t-117.5 25
q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -211t198.5 -83q110 0 172.5 68.5t73.5 172.5h-302v51h364q0 -153 -82 -248.5t-229 -95.5z" />
    <glyph glyph-name="Gcircumflex.alt1" horiz-adv-x="745" 
d="M521 723h-40l-86 113l-85 -113h-39l97 144h54zM393 -13q-141 0 -238 97t-97 249t97.5 248.5t240.5 96.5q152 0 251 -114l-43 -31q-36 43 -90.5 68t-117.5 25q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -211t198.5 -83q110 0 172.5 68.5t73.5 172.5h-302v51h364
q0 -153 -82 -248.5t-229 -95.5z" />
    <glyph glyph-name="Gdotaccent.alt1" horiz-adv-x="745" 
d="M394 728q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11zM393 -13q-141 0 -238 97t-97 249t97.5 248.5t240.5 96.5q152 0 251 -114l-43 -31q-36 43 -90.5 68t-117.5 25q-119 0 -198.5 -82t-79.5 -211q0 -128 79.5 -211
t198.5 -83q110 0 172.5 68.5t73.5 172.5h-302v51h364q0 -153 -82 -248.5t-229 -95.5z" />
    <glyph glyph-name="aacute.alt1" 
d="M440 700l-172 -144h-44l154 144h62zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z
" />
    <glyph glyph-name="abreve.alt1" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58zM465 629q-65 -88 -175 -88
q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="acircumflex.alt1" 
d="M416 556h-40l-86 113l-85 -113h-39l97 144h54zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148
t123.5 -58z" />
    <glyph glyph-name="adieresis.alt1" 
d="M429 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM225 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53
v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z" />
    <glyph glyph-name="agrave.alt1" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58zM333 556h-44l-172 144h62z" />
    <glyph glyph-name="amacron.alt1" 
d="M474 577h-363v37h363v-37zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z" />
    <glyph glyph-name="aogonek.alt1" 
d="M490 0q-79 -38 -79 -99q0 -54 48 -54q41 0 55 44l31 -16q-26 -61 -86 -61q-37 0 -61 21.5t-24 63.5q0 61 63 106v72q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62
t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z" />
    <glyph glyph-name="aring.alt1" 
d="M288 548q-40 0 -68 28t-28 68q0 39 28 67.5t68 28.5q39 0 66.5 -28.5t27.5 -67.5q0 -40 -27.5 -68t-66.5 -28zM288 580q26 0 44.5 19t18.5 45t-18.5 45t-44.5 19t-45 -19t-19 -45t19 -45t45 -19zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184
q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z" />
    <glyph glyph-name="atilde.alt1" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58zM351 554q-25 0 -43.5 16t-27.5 35.5
t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EA1.alt1" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58zM288 -178q-16 0 -27 11t-11 27
q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EA3.alt1" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58zM249 588l-26 8q17 54 70 54
q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EA5.alt1" 
d="M416 556h-40l-86 113l-85 -113h-39l97 144h54zM593 770l-172 -144h-44l154 144h62zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26
q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z" />
    <glyph glyph-name="uni1EAD.alt1" 
d="M417 556h-40l-86 113l-85 -113h-39l97 144h54zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148
t123.5 -58zM293 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EA7.alt1" 
d="M416 556h-40l-86 113l-85 -113h-39l97 144h54zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148
t123.5 -58zM202 626h-44l-172 144h62z" />
    <glyph glyph-name="uni1EA9.alt1" 
d="M416 556h-40l-86 113l-85 -113h-39l97 144h54zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148
t123.5 -58zM331 728l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EAF.alt1" 
d="M448 761l-172 -144h-44l154 144h62zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z
M461 612q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="uni1EAB.alt1" 
d="M416 523h-40l-86 113l-85 -113h-39l97 144h54zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148
t123.5 -58zM347 682q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EB1.alt1" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58zM338 617h-44l-172 144h62zM461 616
q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="uni1EB3.alt1" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58zM242 675l-26 8q17 54 70 54
q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35zM461 616q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="uni1EB5.alt1" 
d="M461 616q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75zM490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26
q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58zM345 657q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EB7.alt1" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58zM293 -178q-16 0 -27 11t-11 27
q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11zM461 629q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="947" 
d="M278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58zM490 108v-108h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-110q27 56 74 89t110 33
q103 0 161.5 -73.5t58.5 -182.5v-14h-404q3 -81 54.5 -136.5t133.5 -55.5q94 0 159 67l27 -34q-75 -78 -180 -78q-137 0 -194 120zM841 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351z" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="947" 
d="M278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58zM490 108v-108h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-110q27 56 74 89t110 33
q103 0 161.5 -73.5t58.5 -182.5v-14h-404q3 -81 54.5 -136.5t133.5 -55.5q94 0 159 67l27 -34q-75 -78 -180 -78q-137 0 -194 120zM841 266q-1 70 -46.5 127t-129.5 57q-79 0 -125.5 -56.5t-49.5 -127.5h351zM627 700l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="fl.alt1" horiz-adv-x="521" 
d="M150 0h-52v437h-80v46h80v44q0 71 35 110.5t96 39.5q39 0 65 -15l-16 -41q-19 11 -44 11q-84 0 -84 -105v-44h98v-46h-98v-437zM440 -12q-94 0 -94 105v574h52v-566q0 -66 50 -66q33 0 54 23l20 -40q-32 -30 -82 -30z" />
    <glyph glyph-name="f_f_l.alt1" horiz-adv-x="785" 
d="M415 0h-52v437h-80v46h80v44q0 71 35 110.5t96 39.5q39 0 65 -15l-16 -41q-19 11 -44 11q-84 0 -84 -105v-44h98v-46h-98v-437zM705 -12q-94 0 -94 105v574h52v-566q0 -66 50 -66q33 0 54 23l20 -40q-32 -30 -82 -30zM150 0h-52v437h-80v46h80v44q0 70 35 110t96 40
q53 0 92 -34l-25 -36q-27 25 -62 25q-84 0 -84 -105v-44h98v-46h-98v-437z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="256" 
d="M269 867l-172 -144h-44l154 144h62zM175 -12q-94 0 -94 105v574h52v-566q0 -66 50 -66q33 0 54 23l20 -40q-32 -30 -82 -30z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="256" 
d="M175 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM175 -12q-94 0 -94 105v574h52v-566q0 -66 50 -66q33 0 54 23l20 -40q-32 -30 -82 -30z" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="268" 
d="M286 620q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM175 -12q-94 0 -94 105v574h52v-566q0 -66 50 -66q33 0 54 23l20 -40q-32 -30 -82 -30z" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="273" 
d="M175 -12q-94 0 -94 105v574h52v-566q0 -66 50 -66q33 0 54 23l20 -40q-32 -30 -82 -30zM288 245q0 -18 -13.5 -31t-31.5 -13t-31 13t-13 31q0 17 13 30t31 13t31.5 -13t13.5 -30z" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="287" 
d="M11 238v46l102 58v325h52v-295l101 58v-46l-101 -58v-225q0 -66 50 -66q33 0 54 23l20 -40q-32 -30 -82 -30q-94 0 -94 105v203z" />
    <glyph glyph-name="yacute.alt1" horiz-adv-x="540" 
d="M432 700l-172 -144h-44l154 144h62zM253 -196q-109 0 -178 73l30 40q54 -68 148 -68q72 0 113 40.5t41 114.5v72q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37t-38 118v335h52v-324q0 -66 30 -92.5t87 -26.5q45 0 88.5 23t68.5 57v363h52v-479q0 -100 -59.5 -150
t-146.5 -50z" />
    <glyph glyph-name="ycircumflex.alt1" horiz-adv-x="540" 
d="M396 556h-40l-86 113l-85 -113h-39l97 144h54zM253 -196q-109 0 -178 73l30 40q54 -68 148 -68q72 0 113 40.5t41 114.5v72q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37t-38 118v335h52v-324q0 -66 30 -92.5t87 -26.5q45 0 88.5 23t68.5 57v363h52v-479
q0 -100 -59.5 -150t-146.5 -50z" />
    <glyph glyph-name="ydieresis.alt1" horiz-adv-x="540" 
d="M406 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM202 601q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM253 -196q-109 0 -178 73l30 40q54 -68 148 -68q72 0 113 40.5t41 114.5v72q-33 -36 -79 -59.5t-95 -23.5
q-76 0 -114 37t-38 118v335h52v-324q0 -66 30 -92.5t87 -26.5q45 0 88.5 23t68.5 57v363h52v-479q0 -100 -59.5 -150t-146.5 -50z" />
    <glyph glyph-name="ygrave.alt1" horiz-adv-x="540" 
d="M253 -196q-109 0 -178 73l30 40q54 -68 148 -68q72 0 113 40.5t41 114.5v72q-33 -36 -79 -59.5t-95 -23.5q-76 0 -114 37t-38 118v335h52v-324q0 -66 30 -92.5t87 -26.5q45 0 88.5 23t68.5 57v363h52v-479q0 -100 -59.5 -150t-146.5 -50zM324 556h-44l-172 144h62z" />
    <glyph glyph-name="uni1EA0.smcp" horiz-adv-x="562" 
d="M249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM282 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EA2.smcp" horiz-adv-x="562" 
d="M249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM238 641l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EA4.smcp" horiz-adv-x="562" 
d="M408 606h-40l-86 113l-85 -113h-39l97 144h54zM584 820l-172 -144h-44l154 144h62zM249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263z" />
    <glyph glyph-name="uni1EAC.smcp" horiz-adv-x="562" 
d="M407 606h-40l-86 113l-85 -113h-39l97 144h54zM249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM281 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EA6.smcp" horiz-adv-x="562" 
d="M408 606h-40l-86 113l-85 -113h-39l97 144h54zM249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM194 676h-44l-172 144h62z" />
    <glyph glyph-name="uni1EA8.smcp" horiz-adv-x="562" 
d="M407 606h-40l-86 113l-85 -113h-39l97 144h54zM249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM322 778l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EAE.smcp" horiz-adv-x="562" 
d="M445 813l-172 -144h-44l154 144h62zM249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM457 664q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="uni1EAA.smcp" horiz-adv-x="562" 
d="M407 573h-40l-86 113l-85 -113h-39l97 144h54zM249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM343 732q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35
t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EB0.smcp" horiz-adv-x="562" 
d="M249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM337 669h-44l-172 144h62zM457 664q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="uni1EB2.smcp" horiz-adv-x="562" 
d="M249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM239 723l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35zM458 664q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75
q92 0 147 75z" />
    <glyph glyph-name="uni1EB4.smcp" horiz-adv-x="562" 
d="M458 664q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75q92 0 147 75zM249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM341 705q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96
t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EB6.smcp" horiz-adv-x="562" 
d="M249 550h64l237 -550h-62l-58 133h-299l-56 -133h-63zM412 182l-131 308l-132 -308h263zM282 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11zM457 679q-65 -88 -175 -88q-109 0 -176 88l29 25q54 -75 147 -75
q92 0 147 75z" />
    <glyph glyph-name="uni1E02.smcp" horiz-adv-x="541" 
d="M324 0h-243v550h238q74 0 117.5 -38t43.5 -103q0 -47 -27.5 -81t-66.5 -44q44 -11 75 -50t31 -85q0 -68 -45 -108.5t-123 -40.5zM318 49q54 0 85 28t31 76q0 43 -31 74t-85 31h-182v-209h182zM314 307q52 0 80.5 27t28.5 70q0 44 -28.5 70.5t-80.5 26.5h-178v-194h178z
M278 611q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E0A.smcp" horiz-adv-x="611" 
d="M266 0h-185v550h185q132 0 211.5 -79.5t79.5 -196.5t-79.5 -195.5t-211.5 -78.5zM266 49q108 0 170.5 64t62.5 161q0 98 -62 162.5t-171 64.5h-130v-452h130zM306 611q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EB8.smcp" horiz-adv-x="505" 
d="M449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49zM268 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EBA.smcp" horiz-adv-x="505" 
d="M449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49zM225 641l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EBC.smcp" horiz-adv-x="505" 
d="M449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49zM329 604q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z
" />
    <glyph glyph-name="uni1EBE.smcp" horiz-adv-x="505" 
d="M389 606h-40l-86 113l-85 -113h-39l97 144h54zM566 820l-172 -144h-44l154 144h62zM449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49z" />
    <glyph glyph-name="uni1EC0.smcp" horiz-adv-x="505" 
d="M389 606h-40l-86 113l-85 -113h-39l97 144h54zM449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49zM176 676h-44l-172 144h62z" />
    <glyph glyph-name="uni1EC2.smcp" horiz-adv-x="505" 
d="M391 606h-40l-86 113l-85 -113h-39l97 144h54zM449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49zM306 778l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EC4.smcp" horiz-adv-x="505" 
d="M389 573h-40l-86 113l-85 -113h-39l97 144h54zM449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49zM325 732q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16
q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EC6.smcp" horiz-adv-x="505" 
d="M393 606h-40l-86 113l-85 -113h-39l97 144h54zM449 0h-368v550h368v-49h-313v-194h307v-49h-307v-209h313v-49zM268 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E1E.smcp" horiz-adv-x="484" 
d="M272 611q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11zM136 0h-55v550h368v-49h-313v-194h307v-49h-307v-258z" />
    <glyph glyph-name="Gbreve.smcp.alt1" horiz-adv-x="647" 
d="M343 -12q-123 0 -206.5 80.5t-83.5 206.5t84 206.5t208 80.5q129 0 216 -94l-40 -32q-70 77 -176 77q-100 0 -167 -67t-67 -171t67 -171.5t167 -67.5q81 0 138 52t67 141h-228v48h287q0 -128 -73 -208.5t-193 -80.5zM521 679q-65 -88 -175 -88q-109 0 -176 88l29 25
q54 -75 147 -75q92 0 147 75z" />
    <glyph glyph-name="Gcommaaccent.smcp.alt1" horiz-adv-x="647" 
d="M392 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM343 -12q-123 0 -206.5 80.5t-83.5 206.5t84 206.5t208 80.5q129 0 216 -94l-40 -32q-70 77 -176 77
q-100 0 -167 -67t-67 -171t67 -171.5t167 -67.5q81 0 138 52t67 141h-228v48h287q0 -128 -73 -208.5t-193 -80.5z" />
    <glyph glyph-name="Gcircumflex.smcp.alt1" horiz-adv-x="647" 
d="M473 606h-40l-86 113l-85 -113h-39l97 144h54zM343 -12q-123 0 -206.5 80.5t-83.5 206.5t84 206.5t208 80.5q129 0 216 -94l-40 -32q-70 77 -176 77q-100 0 -167 -67t-67 -171t67 -171.5t167 -67.5q81 0 138 52t67 141h-228v48h287q0 -128 -73 -208.5t-193 -80.5z" />
    <glyph glyph-name="Gdotaccent.smcp.alt1" horiz-adv-x="647" 
d="M343 -12q-123 0 -206.5 80.5t-83.5 206.5t84 206.5t208 80.5q129 0 216 -94l-40 -32q-70 77 -176 77q-100 0 -167 -67t-67 -171t67 -171.5t167 -67.5q81 0 138 52t67 141h-228v48h287q0 -128 -73 -208.5t-193 -80.5zM348 611q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5
q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E22.smcp" horiz-adv-x="629" 
d="M548 0h-54v259h-358v-259h-55v550h55v-243h358v243h54v-550zM315 611q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EC8.smcp" horiz-adv-x="216" 
d="M136 0h-55v550h55v-550zM66 641l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1ECA.smcp" horiz-adv-x="216" 
d="M136 0h-55v550h55v-550zM110 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1ECC.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM335 -178q-16 0 -27 11t-11 27
q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1ECE.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM291 646l-26 8q17 54 70 54
q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1ED0.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM459 606h-40l-86 113l-85 -113
h-39l97 144h54zM635 820l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="uni1ED2.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM459 606h-40l-86 113l-85 -113
h-39l97 144h54zM245 676h-44l-172 144h62z" />
    <glyph glyph-name="uni1ED4.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM459 606h-40l-86 113l-85 -113
h-39l97 144h54zM374 778l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1ED6.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM460 573h-40l-86 113l-85 -113
h-39l97 144h54zM396 732q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1ED8.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82t202 -82t77 -205q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM460 606h-40l-86 113l-85 -113
h-39l97 144h54zM337 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="Ohorn.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82q144 0 223 -108q38 36 41 77l-4 -1h-6q-16 0 -25.5 10.5t-9.5 26.5q0 15 10.5 26t26.5 11q19 0 31.5 -15t12.5 -42q0 -72 -59 -120q38 -67 38 -152q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5
t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5z" />
    <glyph glyph-name="uni1EDA.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82q144 0 223 -108q38 36 41 77l-4 -1h-6q-16 0 -25.5 10.5t-9.5 26.5q0 15 10.5 26t26.5 11q19 0 31.5 -15t12.5 -42q0 -72 -59 -120q38 -67 38 -152q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5
t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM495 750l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="uni1EDC.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82q144 0 223 -108q38 36 41 77l-4 -1h-6q-16 0 -25.5 10.5t-9.5 26.5q0 15 10.5 26t26.5 11q19 0 31.5 -15t12.5 -42q0 -72 -59 -120q38 -67 38 -152q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5
t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM388 606h-44l-172 144h62z" />
    <glyph glyph-name="uni1EDE.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82q144 0 223 -108q38 36 41 77l-4 -1h-6q-16 0 -25.5 10.5t-9.5 26.5q0 15 10.5 26t26.5 11q19 0 31.5 -15t12.5 -42q0 -72 -59 -120q38 -67 38 -152q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5
t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM291 646l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EE0.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82q144 0 223 -108q38 36 41 77l-4 -1h-6q-16 0 -25.5 10.5t-9.5 26.5q0 15 10.5 26t26.5 11q19 0 31.5 -15t12.5 -42q0 -72 -59 -120q38 -67 38 -152q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5
t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM396 604q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72
h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EE2.smcp" horiz-adv-x="666" 
d="M333 -12q-125 0 -202.5 82t-77.5 205t77.5 205t202.5 82q144 0 223 -108q38 36 41 77l-4 -1h-6q-16 0 -25.5 10.5t-9.5 26.5q0 15 10.5 26t26.5 11q19 0 31.5 -15t12.5 -42q0 -72 -59 -120q38 -67 38 -152q0 -122 -77.5 -204.5t-201.5 -82.5zM333 37q99 0 160.5 67.5
t61.5 170.5q0 104 -61.5 171t-160.5 67q-101 0 -161.5 -67t-60.5 -171q0 -103 61 -170.5t161 -67.5zM335 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1E56.smcp" horiz-adv-x="509" 
d="M136 0h-55v550h218q84 0 129.5 -46.5t45.5 -115.5q0 -70 -45.5 -117t-129.5 -47h-163v-224zM295 273q59 0 90.5 31.5t31.5 83.5q0 50 -32 81.5t-90 31.5h-159v-228h159zM274 611q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11
z" />
    <glyph glyph-name="uni1E60.smcp" horiz-adv-x="499" 
d="M250 -12q-137 0 -211 88l34 39q29 -33 76 -56t102 -23q76 0 112.5 31.5t36.5 78.5q0 44 -35.5 69.5t-86 37.5t-101 26t-86 46.5t-35.5 86.5q0 66 55 108t135 42q123 0 197 -79l-32 -38q-59 69 -165 69q-56 0 -94.5 -29t-38.5 -72q0 -32 25.5 -53t64 -30.5t83 -23
t83 -28.5t64 -48.5t25.5 -82.5q0 -70 -52.5 -114.5t-155.5 -44.5zM256 611q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni021A.smcp" horiz-adv-x="465" 
d="M279 -137q0 -38 -19 -72.5t-48 -55.5l-27 22q23 17 38.5 42t16.5 47q-7 -1 -10 -1q-16 0 -26 10.5t-10 27.5q0 16 11 27.5t27 11.5q20 0 33.5 -16t13.5 -43zM260 0h-55v501h-176v49h408v-49h-177v-501z" />
    <glyph glyph-name="uni1E6A.smcp" horiz-adv-x="465" 
d="M260 0h-55v501h-176v49h408v-49h-177v-501zM233 611q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="uni1EE4.smcp" horiz-adv-x="613" 
d="M306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340q0 -104 -58 -163t-168 -59zM307 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11z" />
    <glyph glyph-name="Uhorn.smcp" horiz-adv-x="613" 
d="M306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-55q32 16 54 44.5t23 57.5l-4 -1h-5q-16 0 -26 10.5t-10 26.5q0 15 10.5 26.5t26.5 11.5q19 0 32 -15.5t13 -42.5q0 -49 -31 -90t-83 -61v-252q0 -104 -58 -163
t-168 -59z" />
    <glyph glyph-name="uni1EE6.smcp" horiz-adv-x="613" 
d="M306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-340q0 -104 -58 -163t-168 -59zM263 641l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35z" />
    <glyph glyph-name="uni1EE8.smcp" horiz-adv-x="613" 
d="M468 750l-172 -144h-44l154 144h62zM306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-55q32 16 54 44.5t23 57.5l-4 -1h-5q-16 0 -26 10.5t-10 26.5q0 15 10.5 26.5t26.5 11.5q19 0 32 -15.5t13 -42.5
q0 -49 -31 -90t-83 -61v-252q0 -104 -58 -163t-168 -59z" />
    <glyph glyph-name="uni1EEA.smcp" horiz-adv-x="613" 
d="M361 606h-44l-172 144h62zM306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-55q32 16 54 44.5t23 57.5l-4 -1h-5q-16 0 -26 10.5t-10 26.5q0 15 10.5 26.5t26.5 11.5q19 0 32 -15.5t13 -42.5q0 -49 -31 -90
t-83 -61v-252q0 -104 -58 -163t-168 -59z" />
    <glyph glyph-name="uni1EEC.smcp" horiz-adv-x="613" 
d="M263 641l-26 8q17 54 70 54q29 0 48.5 -17t19.5 -51q0 -27 -20 -55h-34q25 25 25 54q0 42 -39 42q-32 0 -44 -35zM306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-55q32 16 54 44.5t23 57.5l-4 -1h-5
q-16 0 -26 10.5t-10 26.5q0 15 10.5 26.5t26.5 11.5q19 0 32 -15.5t13 -42.5q0 -49 -31 -90t-83 -61v-252q0 -104 -58 -163t-168 -59z" />
    <glyph glyph-name="uni1EEE.smcp" horiz-adv-x="613" 
d="M306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-55q32 16 54 44.5t23 57.5l-4 -1h-5q-16 0 -26 10.5t-10 26.5q0 15 10.5 26.5t26.5 11.5q19 0 32 -15.5t13 -42.5q0 -49 -31 -90t-83 -61v-252q0 -104 -58 -163
t-168 -59zM368 604q-25 0 -43.5 16t-27.5 35.5t-23.5 35.5t-31.5 16q-24 0 -39 -24t-15 -73h-35q0 58 24.5 96t68.5 38q25 0 43.5 -16t27 -35t23 -35t31.5 -16q24 0 38.5 24t14.5 72h36q0 -59 -24 -96.5t-68 -37.5z" />
    <glyph glyph-name="uni1EF0.smcp" horiz-adv-x="613" 
d="M307 -178q-16 0 -27 11t-11 27q0 15 11 26.5t27 11.5q15 0 26 -11.5t11 -26.5q0 -16 -11 -27t-26 -11zM306 -12q-110 0 -167.5 59t-57.5 162v341h55v-338q0 -83 44 -129t126 -46t126.5 46.5t44.5 128.5v338h55v-55q32 16 54 44.5t23 57.5l-4 -1h-5q-16 0 -26 10.5
t-10 26.5q0 15 10.5 26.5t26.5 11.5q19 0 32 -15.5t13 -42.5q0 -49 -31 -90t-83 -61v-252q0 -104 -58 -163t-168 -59z" />
    <glyph glyph-name="Wacute.smcp" horiz-adv-x="749" 
d="M569 0h-58l-137 463l-136 -463h-58l-165 550h60l135 -471l140 471h49l139 -472l136 472h60zM536 750l-172 -144h-44l154 144h62z" />
    <glyph glyph-name="Wdieresis.smcp" horiz-adv-x="749" 
d="M569 0h-58l-137 463l-136 -463h-58l-165 550h60l135 -471l140 471h49l139 -472l136 472h60zM513 651q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26zM309 651q0 -15 -11 -26t-26 -11t-26 11t-11 26t11 26t26 11t26 -11t11 -26z" />
    <glyph glyph-name="Wgrave.smcp" horiz-adv-x="749" 
d="M569 0h-58l-137 463l-136 -463h-58l-165 550h60l135 -471l140 471h49l139 -472l136 472h60zM429 606h-44l-172 144h62z" />
    <glyph glyph-name="Ygrave.smcp" horiz-adv-x="539" 
d="M299 0h-55v235l-232 315h66l194 -266l190 266h65l-228 -315v-235zM326 606h-44l-172 144h62z" />
    <glyph glyph-name="afii10065.alt1" 
d="M490 0h-53v77q-27 -39 -71.5 -64t-96.5 -25q-97 0 -156.5 69t-59.5 184q0 114 60 184t156 70q104 0 168 -90v78h53v-483zM278 35q49 0 93 25t66 61v239q-22 36 -66 62t-93 26q-78 0 -123.5 -58.5t-45.5 -148.5t45.5 -148t123.5 -58z" />
    <hkern u1="&#x2215;" u2="&#xf659;" k="50" />
    <hkern u1="&#x2215;" u2="&#xf656;" k="-31" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="dagger"
	k="99" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V.smcp"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="19" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="daggerdbl"
	k="39" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="question"
	k="79" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="99" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="39" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="39" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="J,Jcircumflex,afii10057"
	k="-5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="asterisk"
	k="106" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="17" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="23" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="69" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V"
	k="57" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="62" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="11" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="19" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="v,gamma,nu"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="bullet.case"
	k="39" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="49" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="dagger"
	k="79" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V.smcp"
	k="30" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="19" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="daggerdbl"
	k="20" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question"
	k="42" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="80" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="42" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="20" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="asterisk"
	k="67" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="5" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V"
	k="57" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="65" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="40" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question.smcp"
	k="42" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="bullet"
	k="10" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-3" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="19" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="V"
	k="12" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Jcircumflex.smcp,J.smcp"
	k="19" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="question"
	k="42" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="68" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="V"
	k="20" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="22" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="question"
	k="2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="V"
	k="1" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="13" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="bullet.case"
	k="2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="2" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="question"
	k="5" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="3" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="2" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="83" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="12" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V"
	k="5" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="35" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="2" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="X.smcp"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="question"
	k="13" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="J,Jcircumflex,afii10057"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="29" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="V"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Jcircumflex.smcp,J.smcp"
	k="13" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="39" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="19" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="29" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V.smcp"
	k="20" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="question"
	k="26" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="29" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="86" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V"
	k="50" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="73" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="12" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="19" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="19" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X.smcp"
	k="22" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="26" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="57" />
    <hkern g1="Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,OE.smcp,E.smcp,AE.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="17" />
    <hkern g1="F,uni1E1E"
	g2="J,Jcircumflex,afii10057"
	k="55" />
    <hkern g1="F,uni1E1E"
	g2="Jcircumflex.smcp,J.smcp"
	k="53" />
    <hkern g1="F,uni1E1E"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="35" />
    <hkern g1="F,uni1E1E"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="33" />
    <hkern g1="F,uni1E1E"
	g2="X.smcp"
	k="17" />
    <hkern g1="F,uni1E1E"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="F,uni1E1E"
	g2="ampersand"
	k="3" />
    <hkern g1="F,uni1E1E"
	g2="ampersand.smcp"
	k="22" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="J,Jcircumflex,afii10057"
	k="33" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="17" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="43" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="22" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="22" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="12" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="22" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="ampersand.smcp"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="question"
	k="3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="V"
	k="3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="2" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="question"
	k="15" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="2" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="47" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="17" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="2" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="question"
	k="2" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="77" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="2" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="V"
	k="20" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="32" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="V.smcp"
	k="42" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="59" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="52" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="57" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="26" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="23" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="35" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="bullet.case"
	k="77" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="70" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="X.smcp"
	k="13" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="ampersand.smcp"
	k="2" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="19" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="37" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="x,afii10072,afii10087"
	k="23" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="42" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="19" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="43" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="19" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="V"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="13" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="35" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="bullet"
	k="49" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="39" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="dagger"
	k="130" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V.smcp"
	k="52" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="46" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="daggerdbl"
	k="106" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="question"
	k="117" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="116" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="79" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="49" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="37" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="asterisk"
	k="177" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="29" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="63" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="122" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="27" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="83" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="17" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="32" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="39" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="v,gamma,nu"
	k="59" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="59" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="bullet.case"
	k="124" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="79" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="ampersand"
	k="9" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="17" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="26" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V.smcp"
	k="77" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="26" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="110" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="89" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="60" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="37" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="asterisk"
	k="119" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="109" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="43" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V"
	k="99" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="113" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="100" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="J,Jcircumflex,afii10057"
	k="105" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="1" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Jcircumflex.smcp,J.smcp"
	k="93" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="79" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="97" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="5" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand"
	k="23" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand.smcp"
	k="33" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="hyphen,periodcentered,endash,emdash"
	k="37" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="20" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="12" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="79" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="49" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="64" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="19" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="2" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="V"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="2" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="bullet.case"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand"
	k="1" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand.smcp"
	k="12" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="V.smcp"
	k="1" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="2" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="13" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="19" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="2" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="v,gamma,nu"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="x,afii10072,afii10087"
	k="12" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="80" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="V"
	k="13" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="V.smcp"
	k="43" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="86" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="34" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="43" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="29" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="J,Jcircumflex,afii10057"
	k="82" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="2" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="68" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="36" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="43" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="93" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="68" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="v,gamma,nu"
	k="68" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="68" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="bullet.case"
	k="91" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Jcircumflex.smcp,J.smcp"
	k="109" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="100" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="69" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="X.smcp"
	k="43" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="92" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand"
	k="59" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand.smcp"
	k="99" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="73" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="103" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="123" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="x,afii10072,afii10087"
	k="78" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="96" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="70" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="51" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="93" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="30" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="34" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="V"
	k="2" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="72" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="42" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="39" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand"
	k="47" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand.smcp"
	k="49" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="J,Jcircumflex,afii10057"
	k="6" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="Jcircumflex.smcp,J.smcp"
	k="6" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="23" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="68" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="V"
	k="20" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="17" />
    <hkern g1="V,afii10062,afii10037"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="50" />
    <hkern g1="V,afii10062,afii10037"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="2" />
    <hkern g1="V,afii10062,afii10037"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="J,Jcircumflex,afii10057"
	k="92" />
    <hkern g1="V,afii10062,afii10037"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="22" />
    <hkern g1="V,afii10062,afii10037"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="36" />
    <hkern g1="V,afii10062,afii10037"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="12" />
    <hkern g1="V,afii10062,afii10037"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="3" />
    <hkern g1="V,afii10062,afii10037"
	g2="v,gamma,nu"
	k="12" />
    <hkern g1="V,afii10062,afii10037"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="3" />
    <hkern g1="V,afii10062,afii10037"
	g2="bullet.case"
	k="49" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="30" />
    <hkern g1="V,afii10062,afii10037"
	g2="Jcircumflex.smcp,J.smcp"
	k="99" />
    <hkern g1="V,afii10062,afii10037"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="57" />
    <hkern g1="V,afii10062,afii10037"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="57" />
    <hkern g1="V,afii10062,afii10037"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand"
	k="25" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand.smcp"
	k="52" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen,periodcentered,endash,emdash"
	k="52" />
    <hkern g1="V,afii10062,afii10037"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="22" />
    <hkern g1="V,afii10062,afii10037"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="62" />
    <hkern g1="V,afii10062,afii10037"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="60" />
    <hkern g1="V,afii10062,afii10037"
	g2="x,afii10072,afii10087"
	k="22" />
    <hkern g1="V,afii10062,afii10037"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="35" />
    <hkern g1="V,afii10062,afii10037"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="36" />
    <hkern g1="V.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="V.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="43" />
    <hkern g1="V.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="72" />
    <hkern g1="V.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="30" />
    <hkern g1="V.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="V.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="51" />
    <hkern g1="V.smcp"
	g2="ampersand"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex,afii10057"
	k="29" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v,gamma,nu"
	k="2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="1" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Jcircumflex.smcp,J.smcp"
	k="43" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="49" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="69" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="32" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x,afii10072,afii10087"
	k="22" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="30" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="29" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="43" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="35" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="20" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="49" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="ampersand"
	k="12" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="73" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="J,Jcircumflex,afii10057"
	k="112" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="23" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="bullet.case"
	k="89" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="99" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Jcircumflex.smcp,J.smcp"
	k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="62" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="ampersand"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="126" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="129" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="x,afii10072,afii10087"
	k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="colon,semicolon"
	k="55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="32" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="90" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="30" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="43" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="99" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="40" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="50" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="70" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand"
	k="42" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand.smcp"
	k="52" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="19" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="bullet.case"
	k="64" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="67" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="51" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="question"
	k="33" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="3" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="114" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="42" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="V"
	k="63" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="110" />
    <hkern g1="ampersand"
	g2="V.smcp"
	k="40" />
    <hkern g1="ampersand"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="67" />
    <hkern g1="ampersand"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="10" />
    <hkern g1="ampersand"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="77" />
    <hkern g1="ampersand"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="54" />
    <hkern g1="ampersand"
	g2="V"
	k="50" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="96" />
    <hkern g1="ampersand"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="67" />
    <hkern g1="ampersand.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="60" />
    <hkern g1="ampersand.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="106" />
    <hkern g1="ampersand.smcp"
	g2="V"
	k="77" />
    <hkern g1="ampersand.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="67" />
    <hkern g1="asterisk"
	g2="J,Jcircumflex,afii10057"
	k="113" />
    <hkern g1="asterisk"
	g2="Jcircumflex.smcp,J.smcp"
	k="102" />
    <hkern g1="asterisk"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="67" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="106" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="question"
	k="43" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="123" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="V"
	k="60" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="127" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="9" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="x,afii10072,afii10087"
	k="39" />
    <hkern g1="bullet"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="bullet.case"
	g2="J,Jcircumflex,afii10057"
	k="80" />
    <hkern g1="bullet.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="91" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="bullet.case"
	g2="V"
	k="49" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="89" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="39" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="64" />
    <hkern g1="bullet.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="60" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="question"
	k="22" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="2" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="86" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="22" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="V"
	k="33" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="62" />
    <hkern g1="colon,semicolon"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="55" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="question"
	k="33" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="2" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="asterisk"
	k="19" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="106" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="V"
	k="52" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="110" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="9" />
    <hkern g1="eth"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="9" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="question"
	k="-59" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="-69" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="asterisk"
	k="-69" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-22" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-62" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="V"
	k="-62" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="-53" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="42" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand"
	k="2" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand.smcp"
	k="2" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="exclam,B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Imacron,Iogonek,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Racute,Rcommaaccent,Rcaron"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="parenright,bracketright,braceright,parenright.case,bracketright.case,braceright.case"
	k="-77" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="question"
	k="22" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="93" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="V"
	k="36" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="j,jcircumflex,afii10105"
	k="-43" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="J,Jcircumflex,afii10057"
	k="69" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="1" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="30" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="99" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="49" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="67" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="79" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="32" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="52" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="105" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="42" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="80" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="22" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="V"
	k="22" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="63" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="bullet"
	k="29" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="29" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="1" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V.smcp"
	k="51" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="26" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="73" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="60" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="49" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="30" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="92" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="69" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V"
	k="89" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="17" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="22" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="22" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="47" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="v,gamma,nu"
	k="69" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="59" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="9" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="j,jcircumflex,afii10105"
	k="-40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="seven"
	k="63" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one"
	k="69" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="zero,six"
	k="36" />
    <hkern g1="questiondown"
	g2="j,jcircumflex,afii10105"
	k="-160" />
    <hkern g1="questiondown.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="107" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="62" />
    <hkern g1="questiondown.case"
	g2="V"
	k="72" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="92" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="2" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="J,Jcircumflex,afii10057"
	k="86" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="107" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="73" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="54" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="V"
	k="22" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="50" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="82" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="5" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="question"
	k="42" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="94" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="35" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="V"
	k="42" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="92" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="2" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="61" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="12" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="V"
	k="23" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="32" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="question"
	k="22" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="93" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="V"
	k="36" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="v,gamma,nu,chi"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="68" />
    <hkern g1="v,gamma,nu,chi"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="2" />
    <hkern g1="v,gamma,nu,chi"
	g2="V"
	k="12" />
    <hkern g1="v,gamma,nu,chi"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="v,gamma,nu,chi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="v,gamma,nu,chi"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="69" />
    <hkern g1="v,gamma,nu,chi"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="39" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="68" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="3" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="19" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="47" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="47" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="question"
	k="2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="68" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="V"
	k="3" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="59" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="39" />
    <hkern g1="X,Chi,afii10039"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="57" />
    <hkern g1="X,Chi,afii10039"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="47" />
    <hkern g1="X,Chi,afii10039"
	g2="v,gamma,nu"
	k="39" />
    <hkern g1="X,Chi,afii10039"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="39" />
    <hkern g1="X,Chi,afii10039"
	g2="bullet.case"
	k="60" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="79" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen,periodcentered,endash,emdash"
	k="42" />
    <hkern g1="X.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="22" />
    <hkern g1="X.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="43" />
    <hkern g1="X.smcp"
	g2="ampersand"
	k="20" />
    <hkern g1="zero,nine"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="36" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="99" />
    <hkern g1="x,afii10087"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="78" />
    <hkern g1="x,afii10087"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="22" />
    <hkern g1="x,afii10087"
	g2="V"
	k="22" />
    <hkern g1="x,afii10087"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="72" />
    <hkern g1="x,afii10087"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="39" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.case,bracketleft.case,braceleft.case"
	g2="j,jcircumflex,afii10105"
	k="-110" />
    <hkern g1="one.numr"
	g2="fraction"
	k="-43" />
    <hkern g1="seven.numr"
	g2="fraction"
	k="50" />
    <hkern g1="fraction"
	g2="one.dnom"
	k="-31" />
    <hkern g1="fraction"
	g2="four.dnom"
	k="50" />
  </font>
</defs></svg>
