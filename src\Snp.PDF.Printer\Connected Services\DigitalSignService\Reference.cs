﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     //
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DigitalSignService
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SignResult", Namespace="http://schemas.datacontract.org/2004/07/Com.TCIS.SNPDigitalSign.Bll")]
    public partial class SignResult : object
    {
        
        private string MessageField;
        
        private int ReturnCodeField;
        
        private string SignedStrField;
        
        private string TransactionIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Message
        {
            get
            {
                return this.MessageField;
            }
            set
            {
                this.MessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ReturnCode
        {
            get
            {
                return this.ReturnCodeField;
            }
            set
            {
                this.ReturnCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SignedStr
        {
            get
            {
                return this.SignedStrField;
            }
            set
            {
                this.SignedStrField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TransactionId
        {
            get
            {
                return this.TransactionIdField;
            }
            set
            {
                this.TransactionIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="VerifyResult", Namespace="http://schemas.datacontract.org/2004/07/Com.TCIS.SNPDigitalSign.Bll")]
    public partial class VerifyResult : object
    {
        
        private string MessageField;
        
        private int ReturnCodeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Message
        {
            get
            {
                return this.MessageField;
            }
            set
            {
                this.MessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ReturnCode
        {
            get
            {
                return this.ReturnCodeField;
            }
            set
            {
                this.ReturnCodeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="FileCertResult", Namespace="http://schemas.datacontract.org/2004/07/Com.TCIS.SNPDigitalSign.Bll")]
    public partial class FileCertResult : object
    {
        
        private string FileCertField;
        
        private string MessageField;
        
        private int ReturnCodeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FileCert
        {
            get
            {
                return this.FileCertField;
            }
            set
            {
                this.FileCertField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Message
        {
            get
            {
                return this.MessageField;
            }
            set
            {
                this.MessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ReturnCode
        {
            get
            {
                return this.ReturnCodeField;
            }
            set
            {
                this.ReturnCodeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Position", Namespace="http://schemas.datacontract.org/2004/07/Com.TCIS.SNPDigitalSign.ValueObjects.Dtos" +
        "")]
    public partial class Position : object
    {
        
        private int HeightField;
        
        private int WidthField;
        
        private int XField;
        
        private int YField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Height
        {
            get
            {
                return this.HeightField;
            }
            set
            {
                this.HeightField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Width
        {
            get
            {
                return this.WidthField;
            }
            set
            {
                this.WidthField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int X
        {
            get
            {
                return this.XField;
            }
            set
            {
                this.XField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Y
        {
            get
            {
                return this.YField;
            }
            set
            {
                this.YField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://tcis.com/services", ConfigurationName="DigitalSignService.ISnpDigitalSignBE")]
    public interface ISnpDigitalSignBE
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tcis.com/services/ISnpDigitalSignBE/SignBase64", ReplyAction="http://tcis.com/services/ISnpDigitalSignBE/SignBase64Response")]
        System.Threading.Tasks.Task<DigitalSignService.SignResult> SignBase64Async(string accessToken, string inputStr);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tcis.com/services/ISnpDigitalSignBE/Verify", ReplyAction="http://tcis.com/services/ISnpDigitalSignBE/VerifyResponse")]
        System.Threading.Tasks.Task<DigitalSignService.VerifyResult> VerifyAsync(string appName, string dataType, string accessToken, string origStr, string signedStr, string fileCert);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tcis.com/services/ISnpDigitalSignBE/GetFileCertBase64", ReplyAction="http://tcis.com/services/ISnpDigitalSignBE/GetFileCertBase64Response")]
        System.Threading.Tasks.Task<DigitalSignService.FileCertResult> GetFileCertBase64Async(string accessToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tcis.com/services/ISnpDigitalSignBE/Sign", ReplyAction="http://tcis.com/services/ISnpDigitalSignBE/SignResponse")]
        System.Threading.Tasks.Task<DigitalSignService.SignResult> SignAsync(string appName, string dataType, string accessToken, string inputStr);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tcis.com/services/ISnpDigitalSignBE/GetFileCert", ReplyAction="http://tcis.com/services/ISnpDigitalSignBE/GetFileCertResponse")]
        System.Threading.Tasks.Task<DigitalSignService.FileCertResult> GetFileCertAsync(string appName, string dataType, string accessToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tcis.com/services/ISnpDigitalSignBE/CheckDigitalSignExpiredWarning", ReplyAction="http://tcis.com/services/ISnpDigitalSignBE/CheckDigitalSignExpiredWarningResponse" +
            "")]
        System.Threading.Tasks.Task<string> CheckDigitalSignExpiredWarningAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tcis.com/services/ISnpDigitalSignBE/ESignPdf", ReplyAction="http://tcis.com/services/ISnpDigitalSignBE/ESignPdfResponse")]
        System.Threading.Tasks.Task<DigitalSignService.ESignResponse> ESignPdfAsync(DigitalSignService.ESignRequest request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ESignRequest", WrapperNamespace="http://tcis.com/services", IsWrapped=true)]
    public partial class ESignRequest
    {
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public string AccessToken;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public int Alignment;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public string AppName;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public string CaPath;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public string DataType;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public string Eir;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public float ImageScale;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public string Location;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public int Page;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public string Password;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public DigitalSignService.Position Position;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public string Reason;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public System.DateTime SignDate;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public bool VisibleImage;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tcis.com/services", Order=0)]
        public byte[] Data;
        
        public ESignRequest()
        {
        }
        
        public ESignRequest(string AccessToken, int Alignment, string AppName, string CaPath, string DataType, string Eir, float ImageScale, string Location, int Page, string Password, DigitalSignService.Position Position, string Reason, System.DateTime SignDate, bool VisibleImage, byte[] Data)
        {
            this.AccessToken = AccessToken;
            this.Alignment = Alignment;
            this.AppName = AppName;
            this.CaPath = CaPath;
            this.DataType = DataType;
            this.Eir = Eir;
            this.ImageScale = ImageScale;
            this.Location = Location;
            this.Page = Page;
            this.Password = Password;
            this.Position = Position;
            this.Reason = Reason;
            this.SignDate = SignDate;
            this.VisibleImage = VisibleImage;
            this.Data = Data;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ESignResponse", WrapperNamespace="http://tcis.com/services", IsWrapped=true)]
    public partial class ESignResponse
    {
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public string Message;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public int ReturnCode;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tcis.com/services")]
        public string TransactionId;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tcis.com/services", Order=0)]
        public byte[] Data;
        
        public ESignResponse()
        {
        }
        
        public ESignResponse(string Message, int ReturnCode, string TransactionId, byte[] Data)
        {
            this.Message = Message;
            this.ReturnCode = ReturnCode;
            this.TransactionId = TransactionId;
            this.Data = Data;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    public interface ISnpDigitalSignBEChannel : DigitalSignService.ISnpDigitalSignBE, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    public partial class SnpDigitalSignBEClient : System.ServiceModel.ClientBase<DigitalSignService.ISnpDigitalSignBE>, DigitalSignService.ISnpDigitalSignBE
    {
        
    /// <summary>
    /// Implement this partial method to configure the service endpoint.
    /// </summary>
    /// <param name="serviceEndpoint">The endpoint to configure</param>
    /// <param name="clientCredentials">The client credentials</param>
    static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public SnpDigitalSignBEClient() : 
                base(SnpDigitalSignBEClient.GetDefaultBinding(), SnpDigitalSignBEClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.SnpDigitalSignBEBinding.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SnpDigitalSignBEClient(EndpointConfiguration endpointConfiguration) : 
                base(SnpDigitalSignBEClient.GetBindingForEndpoint(endpointConfiguration), SnpDigitalSignBEClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SnpDigitalSignBEClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(SnpDigitalSignBEClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SnpDigitalSignBEClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(SnpDigitalSignBEClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SnpDigitalSignBEClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<DigitalSignService.SignResult> SignBase64Async(string accessToken, string inputStr)
        {
            return base.Channel.SignBase64Async(accessToken, inputStr);
        }
        
        public System.Threading.Tasks.Task<DigitalSignService.VerifyResult> VerifyAsync(string appName, string dataType, string accessToken, string origStr, string signedStr, string fileCert)
        {
            return base.Channel.VerifyAsync(appName, dataType, accessToken, origStr, signedStr, fileCert);
        }
        
        public System.Threading.Tasks.Task<DigitalSignService.FileCertResult> GetFileCertBase64Async(string accessToken)
        {
            return base.Channel.GetFileCertBase64Async(accessToken);
        }
        
        public System.Threading.Tasks.Task<DigitalSignService.SignResult> SignAsync(string appName, string dataType, string accessToken, string inputStr)
        {
            return base.Channel.SignAsync(appName, dataType, accessToken, inputStr);
        }
        
        public System.Threading.Tasks.Task<DigitalSignService.FileCertResult> GetFileCertAsync(string appName, string dataType, string accessToken)
        {
            return base.Channel.GetFileCertAsync(appName, dataType, accessToken);
        }
        
        public System.Threading.Tasks.Task<string> CheckDigitalSignExpiredWarningAsync()
        {
            return base.Channel.CheckDigitalSignExpiredWarningAsync();
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<DigitalSignService.ESignResponse> DigitalSignService.ISnpDigitalSignBE.ESignPdfAsync(DigitalSignService.ESignRequest request)
        {
            return base.Channel.ESignPdfAsync(request);
        }
        
        public System.Threading.Tasks.Task<DigitalSignService.ESignResponse> ESignPdfAsync(string AccessToken, int Alignment, string AppName, string CaPath, string DataType, string Eir, float ImageScale, string Location, int Page, string Password, DigitalSignService.Position Position, string Reason, System.DateTime SignDate, bool VisibleImage, byte[] Data)
        {
            DigitalSignService.ESignRequest inValue = new DigitalSignService.ESignRequest();
            inValue.AccessToken = AccessToken;
            inValue.Alignment = Alignment;
            inValue.AppName = AppName;
            inValue.CaPath = CaPath;
            inValue.DataType = DataType;
            inValue.Eir = Eir;
            inValue.ImageScale = ImageScale;
            inValue.Location = Location;
            inValue.Page = Page;
            inValue.Password = Password;
            inValue.Position = Position;
            inValue.Reason = Reason;
            inValue.SignDate = SignDate;
            inValue.VisibleImage = VisibleImage;
            inValue.Data = Data;
            return ((DigitalSignService.ISnpDigitalSignBE)(this)).ESignPdfAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.SnpDigitalSignBEBinding))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.SnpDigitalSignBEBinding))
            {
                return new System.ServiceModel.EndpointAddress("http://localhost:2301/SnpDigitalSignServices.svc");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return SnpDigitalSignBEClient.GetBindingForEndpoint(EndpointConfiguration.SnpDigitalSignBEBinding);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return SnpDigitalSignBEClient.GetEndpointAddress(EndpointConfiguration.SnpDigitalSignBEBinding);
        }
        
        public enum EndpointConfiguration
        {
            
            SnpDigitalSignBEBinding,
        }
    }
}
