﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace AnonymousGateway
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_SITE", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_SITE : AnonymousGateway.AuditableEntityOflong
    {
        
        private AnonymousGateway.AM_ACTIVITY_LOG[] AM_ACTIVITY_LOGField;
        
        private System.Nullable<int> CREATED_BYField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private string REMARKField;
        
        private string SITE_IDField;
        
        private string SITE_NAMEField;
        
        private bool STATUSField;
        
        private System.Nullable<int> UPDATED_BYField;
        
        private System.Nullable<System.DateTime> UPDATED_DATEField;
        
        private bool USED_LOGINField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_ACTIVITY_LOG[] AM_ACTIVITY_LOG
        {
            get
            {
                return this.AM_ACTIVITY_LOGField;
            }
            set
            {
                this.AM_ACTIVITY_LOGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string REMARK
        {
            get
            {
                return this.REMARKField;
            }
            set
            {
                this.REMARKField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_NAME
        {
            get
            {
                return this.SITE_NAMEField;
            }
            set
            {
                this.SITE_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool STATUS
        {
            get
            {
                return this.STATUSField;
            }
            set
            {
                this.STATUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> UPDATED_BY
        {
            get
            {
                return this.UPDATED_BYField;
            }
            set
            {
                this.UPDATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UPDATED_DATE
        {
            get
            {
                return this.UPDATED_DATEField;
            }
            set
            {
                this.UPDATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool USED_LOGIN
        {
            get
            {
                return this.USED_LOGINField;
            }
            set
            {
                this.USED_LOGINField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BaseEntity", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_ACTIVITY_LOG))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.CFG_SITE_PARAMS))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_INVOICE_PATTERN))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_INVOICE_SERIAL))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.EntityOflong))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AuditableEntityOflong))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_SITE))]
    public partial class BaseEntity : object
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_ACTIVITY_LOG", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_ACTIVITY_LOG : AnonymousGateway.BaseEntity
    {
        
        private string ACTIVITY_TYPEField;
        
        private AnonymousGateway.AM_SITE AM_SITEField;
        
        private AnonymousGateway.AM_USER AM_USERField;
        
        private string COMMENTField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private int IDField;
        
        private string SITE_IDField;
        
        private System.Nullable<int> USER_IDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ACTIVITY_TYPE
        {
            get
            {
                return this.ACTIVITY_TYPEField;
            }
            set
            {
                this.ACTIVITY_TYPEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_SITE AM_SITE
        {
            get
            {
                return this.AM_SITEField;
            }
            set
            {
                this.AM_SITEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_USER AM_USER
        {
            get
            {
                return this.AM_USERField;
            }
            set
            {
                this.AM_USERField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string COMMENT
        {
            get
            {
                return this.COMMENTField;
            }
            set
            {
                this.COMMENTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> USER_ID
        {
            get
            {
                return this.USER_IDField;
            }
            set
            {
                this.USER_IDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="CFG_SITE_PARAMS", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class CFG_SITE_PARAMS : AnonymousGateway.BaseEntity
    {
        
        private string FEATURE_IDField;
        
        private int IDField;
        
        private string INVOICE_TYPEField;
        
        private string PARAM_NAMEField;
        
        private string PARTNER_IDField;
        
        private string SITE_IDField;
        
        private string VALUEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FEATURE_ID
        {
            get
            {
                return this.FEATURE_IDField;
            }
            set
            {
                this.FEATURE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string INVOICE_TYPE
        {
            get
            {
                return this.INVOICE_TYPEField;
            }
            set
            {
                this.INVOICE_TYPEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PARAM_NAME
        {
            get
            {
                return this.PARAM_NAMEField;
            }
            set
            {
                this.PARAM_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PARTNER_ID
        {
            get
            {
                return this.PARTNER_IDField;
            }
            set
            {
                this.PARTNER_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VALUE
        {
            get
            {
                return this.VALUEField;
            }
            set
            {
                this.VALUEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_INVOICE_PATTERN", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_INVOICE_PATTERN : AnonymousGateway.BaseEntity
    {
        
        private System.Nullable<bool> ACTIVEField;
        
        private System.Nullable<int> CREATED_BYField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private System.Nullable<int> EDITED_BYField;
        
        private System.Nullable<System.DateTime> EDITED_DATEField;
        
        private int IDField;
        
        private string PATTERNField;
        
        private string SITE_IDField;
        
        private System.Nullable<int> UPDATED_BYField;
        
        private System.Nullable<System.DateTime> UPDATED_DATEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> ACTIVE
        {
            get
            {
                return this.ACTIVEField;
            }
            set
            {
                this.ACTIVEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> EDITED_BY
        {
            get
            {
                return this.EDITED_BYField;
            }
            set
            {
                this.EDITED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> EDITED_DATE
        {
            get
            {
                return this.EDITED_DATEField;
            }
            set
            {
                this.EDITED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PATTERN
        {
            get
            {
                return this.PATTERNField;
            }
            set
            {
                this.PATTERNField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> UPDATED_BY
        {
            get
            {
                return this.UPDATED_BYField;
            }
            set
            {
                this.UPDATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UPDATED_DATE
        {
            get
            {
                return this.UPDATED_DATEField;
            }
            set
            {
                this.UPDATED_DATEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_INVOICE_SERIAL", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_INVOICE_SERIAL : AnonymousGateway.BaseEntity
    {
        
        private System.Nullable<bool> ACTIVEField;
        
        private System.Nullable<int> CREATED_BYField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private int IDField;
        
        private System.Nullable<int> INVOICE_NUMBERField;
        
        private AnonymousGateway.Message[] MessagesField;
        
        private int PATTERN_IDField;
        
        private string SERIALField;
        
        private string SITE_IDField;
        
        private System.Nullable<int> UPDATED_BYField;
        
        private System.Nullable<System.DateTime> UPDATED_DATEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> ACTIVE
        {
            get
            {
                return this.ACTIVEField;
            }
            set
            {
                this.ACTIVEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> INVOICE_NUMBER
        {
            get
            {
                return this.INVOICE_NUMBERField;
            }
            set
            {
                this.INVOICE_NUMBERField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.Message[] Messages
        {
            get
            {
                return this.MessagesField;
            }
            set
            {
                this.MessagesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PATTERN_ID
        {
            get
            {
                return this.PATTERN_IDField;
            }
            set
            {
                this.PATTERN_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SERIAL
        {
            get
            {
                return this.SERIALField;
            }
            set
            {
                this.SERIALField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> UPDATED_BY
        {
            get
            {
                return this.UPDATED_BYField;
            }
            set
            {
                this.UPDATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UPDATED_DATE
        {
            get
            {
                return this.UPDATED_DATEField;
            }
            set
            {
                this.UPDATED_DATEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EntityOflong", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AuditableEntityOflong))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_SITE))]
    public partial class EntityOflong : AnonymousGateway.BaseEntity
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AuditableEntityOflong", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_USER))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_ACCOUNT_TYPE))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_USER_AGENT))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_FEATURE))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_GROUP_MENU))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_GROUP_OPER_METHOD))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_USER_LINER))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_MENU))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_ROLE))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_USER_SITE_ROLE))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AM_SITE))]
    public partial class AuditableEntityOflong : AnonymousGateway.EntityOflong
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_USER", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_USER : AnonymousGateway.AuditableEntityOflong
    {
        
        private System.Nullable<int> ACCOUNT_TYPEField;
        
        private string ADDRESSField;
        
        private System.Nullable<int> AGENT_IDField;
        
        private AnonymousGateway.AM_ACTIVITY_LOG[] AM_ACTIVITY_LOGField;
        
        private AnonymousGateway.AM_ACCOUNT_TYPE AccountTypeField;
        
        private System.Nullable<int> CREATED_BYField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private AnonymousGateway.AM_USER_AGENT CurrentAgentField;
        
        private AnonymousGateway.AM_FEATURE[] CurrentFeaturesField;
        
        private AnonymousGateway.AM_GROUP_MENU[] CurrentGroupMenusField;
        
        private AnonymousGateway.AM_GROUP_OPER_METHOD[] CurrentGroupOperMethodsField;
        
        private AnonymousGateway.AM_USER_LINER CurrentLinerField;
        
        private AnonymousGateway.AM_MENU[] CurrentMenusField;
        
        private AnonymousGateway.AM_OPER_METHOD_DTO[] CurrentOperMethodsField;
        
        private AnonymousGateway.AM_ROLE CurrentRoleField;
        
        private AnonymousGateway.AM_SITE CurrentSiteField;
        
        private string DEPOTField;
        
        private string DESCRIPTIONField;
        
        private bool DISCOUNT_ON_INVOICEField;
        
        private System.Nullable<System.DateTime> DOBField;
        
        private string DOMAIN_IP_ADDRESSField;
        
        private string DOMAIN_NAMEField;
        
        private string EMAILField;
        
        private string EPORT_POST_PAID_FLAGField;
        
        private System.Nullable<bool> FULL_INFO_REQField;
        
        private string FULL_NAMEField;
        
        private string GENDERField;
        
        private System.Nullable<bool> IS_APPROVEDField;
        
        private System.Nullable<bool> IS_CONTRACT_ON_CONTAINER_SERVICEField;
        
        private System.Nullable<bool> IS_CONTRACT_ON_EMPTY_CONTAINER_DELIVERYField;
        
        private System.Nullable<bool> IS_CONTRACT_ON_FULL_CONTAINER_DELIVERYField;
        
        private System.Nullable<bool> IS_LOGIN_WITH_IDCARDField;
        
        private bool IS_MASTER_ACCOUNTField;
        
        private int IdField;
        
        private System.Nullable<System.DateTime> LAST_CHANGE_PWD_TSField;
        
        private System.Nullable<System.DateTime> LAST_LOGON_TSField;
        
        private System.Nullable<int> MASTER_ACCOUNTField;
        
        private string NEW_PASSWORDField;
        
        private string OTP_CODEField;
        
        private string OTP_CONFIRMEDField;
        
        private System.Nullable<System.DateTime> OTP_EXPIREField;
        
        private string PASSWORDField;
        
        private string PHONE_NOField;
        
        private string POST_PAID_FLGField;
        
        private bool REGISTER_VESSEL_EXPORT_FLAGField;
        
        private string SITE_CODEField;
        
        private string SITE_MAPPED_ROLEField;
        
        private bool STATUSField;
        
        private AnonymousGateway.AM_USER_SITE_ROLE[] SiteRoleField;
        
        private string[] SitesField;
        
        private string TAX_FILE_NOField;
        
        private System.Nullable<bool> TRANSPORTATION_AGENT_FLGField;
        
        private string TRUCK_NOField;
        
        private System.Nullable<int> UPDATED_BYField;
        
        private System.Nullable<System.DateTime> UPDATED_DATEField;
        
        private int USER_IDField;
        
        private string USER_NAMEField;
        
        private string USER_TYPEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> ACCOUNT_TYPE
        {
            get
            {
                return this.ACCOUNT_TYPEField;
            }
            set
            {
                this.ACCOUNT_TYPEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ADDRESS
        {
            get
            {
                return this.ADDRESSField;
            }
            set
            {
                this.ADDRESSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> AGENT_ID
        {
            get
            {
                return this.AGENT_IDField;
            }
            set
            {
                this.AGENT_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_ACTIVITY_LOG[] AM_ACTIVITY_LOG
        {
            get
            {
                return this.AM_ACTIVITY_LOGField;
            }
            set
            {
                this.AM_ACTIVITY_LOGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_ACCOUNT_TYPE AccountType
        {
            get
            {
                return this.AccountTypeField;
            }
            set
            {
                this.AccountTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_USER_AGENT CurrentAgent
        {
            get
            {
                return this.CurrentAgentField;
            }
            set
            {
                this.CurrentAgentField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_FEATURE[] CurrentFeatures
        {
            get
            {
                return this.CurrentFeaturesField;
            }
            set
            {
                this.CurrentFeaturesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_GROUP_MENU[] CurrentGroupMenus
        {
            get
            {
                return this.CurrentGroupMenusField;
            }
            set
            {
                this.CurrentGroupMenusField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_GROUP_OPER_METHOD[] CurrentGroupOperMethods
        {
            get
            {
                return this.CurrentGroupOperMethodsField;
            }
            set
            {
                this.CurrentGroupOperMethodsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_USER_LINER CurrentLiner
        {
            get
            {
                return this.CurrentLinerField;
            }
            set
            {
                this.CurrentLinerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_MENU[] CurrentMenus
        {
            get
            {
                return this.CurrentMenusField;
            }
            set
            {
                this.CurrentMenusField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_OPER_METHOD_DTO[] CurrentOperMethods
        {
            get
            {
                return this.CurrentOperMethodsField;
            }
            set
            {
                this.CurrentOperMethodsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_ROLE CurrentRole
        {
            get
            {
                return this.CurrentRoleField;
            }
            set
            {
                this.CurrentRoleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_SITE CurrentSite
        {
            get
            {
                return this.CurrentSiteField;
            }
            set
            {
                this.CurrentSiteField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DEPOT
        {
            get
            {
                return this.DEPOTField;
            }
            set
            {
                this.DEPOTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DESCRIPTION
        {
            get
            {
                return this.DESCRIPTIONField;
            }
            set
            {
                this.DESCRIPTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DISCOUNT_ON_INVOICE
        {
            get
            {
                return this.DISCOUNT_ON_INVOICEField;
            }
            set
            {
                this.DISCOUNT_ON_INVOICEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DOB
        {
            get
            {
                return this.DOBField;
            }
            set
            {
                this.DOBField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DOMAIN_IP_ADDRESS
        {
            get
            {
                return this.DOMAIN_IP_ADDRESSField;
            }
            set
            {
                this.DOMAIN_IP_ADDRESSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DOMAIN_NAME
        {
            get
            {
                return this.DOMAIN_NAMEField;
            }
            set
            {
                this.DOMAIN_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EMAIL
        {
            get
            {
                return this.EMAILField;
            }
            set
            {
                this.EMAILField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EPORT_POST_PAID_FLAG
        {
            get
            {
                return this.EPORT_POST_PAID_FLAGField;
            }
            set
            {
                this.EPORT_POST_PAID_FLAGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> FULL_INFO_REQ
        {
            get
            {
                return this.FULL_INFO_REQField;
            }
            set
            {
                this.FULL_INFO_REQField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FULL_NAME
        {
            get
            {
                return this.FULL_NAMEField;
            }
            set
            {
                this.FULL_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GENDER
        {
            get
            {
                return this.GENDERField;
            }
            set
            {
                this.GENDERField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IS_APPROVED
        {
            get
            {
                return this.IS_APPROVEDField;
            }
            set
            {
                this.IS_APPROVEDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IS_CONTRACT_ON_CONTAINER_SERVICE
        {
            get
            {
                return this.IS_CONTRACT_ON_CONTAINER_SERVICEField;
            }
            set
            {
                this.IS_CONTRACT_ON_CONTAINER_SERVICEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IS_CONTRACT_ON_EMPTY_CONTAINER_DELIVERY
        {
            get
            {
                return this.IS_CONTRACT_ON_EMPTY_CONTAINER_DELIVERYField;
            }
            set
            {
                this.IS_CONTRACT_ON_EMPTY_CONTAINER_DELIVERYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IS_CONTRACT_ON_FULL_CONTAINER_DELIVERY
        {
            get
            {
                return this.IS_CONTRACT_ON_FULL_CONTAINER_DELIVERYField;
            }
            set
            {
                this.IS_CONTRACT_ON_FULL_CONTAINER_DELIVERYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IS_LOGIN_WITH_IDCARD
        {
            get
            {
                return this.IS_LOGIN_WITH_IDCARDField;
            }
            set
            {
                this.IS_LOGIN_WITH_IDCARDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IS_MASTER_ACCOUNT
        {
            get
            {
                return this.IS_MASTER_ACCOUNTField;
            }
            set
            {
                this.IS_MASTER_ACCOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> LAST_CHANGE_PWD_TS
        {
            get
            {
                return this.LAST_CHANGE_PWD_TSField;
            }
            set
            {
                this.LAST_CHANGE_PWD_TSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> LAST_LOGON_TS
        {
            get
            {
                return this.LAST_LOGON_TSField;
            }
            set
            {
                this.LAST_LOGON_TSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> MASTER_ACCOUNT
        {
            get
            {
                return this.MASTER_ACCOUNTField;
            }
            set
            {
                this.MASTER_ACCOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NEW_PASSWORD
        {
            get
            {
                return this.NEW_PASSWORDField;
            }
            set
            {
                this.NEW_PASSWORDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OTP_CODE
        {
            get
            {
                return this.OTP_CODEField;
            }
            set
            {
                this.OTP_CODEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OTP_CONFIRMED
        {
            get
            {
                return this.OTP_CONFIRMEDField;
            }
            set
            {
                this.OTP_CONFIRMEDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OTP_EXPIRE
        {
            get
            {
                return this.OTP_EXPIREField;
            }
            set
            {
                this.OTP_EXPIREField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PASSWORD
        {
            get
            {
                return this.PASSWORDField;
            }
            set
            {
                this.PASSWORDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PHONE_NO
        {
            get
            {
                return this.PHONE_NOField;
            }
            set
            {
                this.PHONE_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string POST_PAID_FLG
        {
            get
            {
                return this.POST_PAID_FLGField;
            }
            set
            {
                this.POST_PAID_FLGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool REGISTER_VESSEL_EXPORT_FLAG
        {
            get
            {
                return this.REGISTER_VESSEL_EXPORT_FLAGField;
            }
            set
            {
                this.REGISTER_VESSEL_EXPORT_FLAGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_CODE
        {
            get
            {
                return this.SITE_CODEField;
            }
            set
            {
                this.SITE_CODEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_MAPPED_ROLE
        {
            get
            {
                return this.SITE_MAPPED_ROLEField;
            }
            set
            {
                this.SITE_MAPPED_ROLEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool STATUS
        {
            get
            {
                return this.STATUSField;
            }
            set
            {
                this.STATUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_USER_SITE_ROLE[] SiteRole
        {
            get
            {
                return this.SiteRoleField;
            }
            set
            {
                this.SiteRoleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string[] Sites
        {
            get
            {
                return this.SitesField;
            }
            set
            {
                this.SitesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TAX_FILE_NO
        {
            get
            {
                return this.TAX_FILE_NOField;
            }
            set
            {
                this.TAX_FILE_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> TRANSPORTATION_AGENT_FLG
        {
            get
            {
                return this.TRANSPORTATION_AGENT_FLGField;
            }
            set
            {
                this.TRANSPORTATION_AGENT_FLGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TRUCK_NO
        {
            get
            {
                return this.TRUCK_NOField;
            }
            set
            {
                this.TRUCK_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> UPDATED_BY
        {
            get
            {
                return this.UPDATED_BYField;
            }
            set
            {
                this.UPDATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UPDATED_DATE
        {
            get
            {
                return this.UPDATED_DATEField;
            }
            set
            {
                this.UPDATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int USER_ID
        {
            get
            {
                return this.USER_IDField;
            }
            set
            {
                this.USER_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string USER_NAME
        {
            get
            {
                return this.USER_NAMEField;
            }
            set
            {
                this.USER_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string USER_TYPE
        {
            get
            {
                return this.USER_TYPEField;
            }
            set
            {
                this.USER_TYPEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_ACCOUNT_TYPE", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_ACCOUNT_TYPE : AnonymousGateway.AuditableEntityOflong
    {
        
        private string DESCRIPTIONField;
        
        private int IDField;
        
        private bool IS_DEFAULTField;
        
        private string NAMEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DESCRIPTION
        {
            get
            {
                return this.DESCRIPTIONField;
            }
            set
            {
                this.DESCRIPTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IS_DEFAULT
        {
            get
            {
                return this.IS_DEFAULTField;
            }
            set
            {
                this.IS_DEFAULTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NAME
        {
            get
            {
                return this.NAMEField;
            }
            set
            {
                this.NAMEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_USER_AGENT", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_USER_AGENT : AnonymousGateway.AuditableEntityOflong
    {
        
        private string AGENTField;
        
        private System.Nullable<int> CREATED_BYField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private int IDField;
        
        private string ROLE_NAMEField;
        
        private string SITE_IDField;
        
        private System.Nullable<int> UPDATED_BYField;
        
        private System.Nullable<System.DateTime> UPDATED_DATEField;
        
        private int USER_IDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AGENT
        {
            get
            {
                return this.AGENTField;
            }
            set
            {
                this.AGENTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ROLE_NAME
        {
            get
            {
                return this.ROLE_NAMEField;
            }
            set
            {
                this.ROLE_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> UPDATED_BY
        {
            get
            {
                return this.UPDATED_BYField;
            }
            set
            {
                this.UPDATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UPDATED_DATE
        {
            get
            {
                return this.UPDATED_DATEField;
            }
            set
            {
                this.UPDATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int USER_ID
        {
            get
            {
                return this.USER_IDField;
            }
            set
            {
                this.USER_IDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_FEATURE", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_FEATURE : AnonymousGateway.AuditableEntityOflong
    {
        
        private string DESCRIPTIONField;
        
        private int IDField;
        
        private bool IS_COMMONField;
        
        private System.Nullable<int> MENU_IDField;
        
        private string NAMEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DESCRIPTION
        {
            get
            {
                return this.DESCRIPTIONField;
            }
            set
            {
                this.DESCRIPTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IS_COMMON
        {
            get
            {
                return this.IS_COMMONField;
            }
            set
            {
                this.IS_COMMONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> MENU_ID
        {
            get
            {
                return this.MENU_IDField;
            }
            set
            {
                this.MENU_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NAME
        {
            get
            {
                return this.NAMEField;
            }
            set
            {
                this.NAMEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_GROUP_MENU", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_GROUP_MENU : AnonymousGateway.AuditableEntityOflong
    {
        
        private string ICONField;
        
        private int IDField;
        
        private string NAMEField;
        
        private int SORTField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ICON
        {
            get
            {
                return this.ICONField;
            }
            set
            {
                this.ICONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NAME
        {
            get
            {
                return this.NAMEField;
            }
            set
            {
                this.NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SORT
        {
            get
            {
                return this.SORTField;
            }
            set
            {
                this.SORTField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_GROUP_OPER_METHOD", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_GROUP_OPER_METHOD : AnonymousGateway.AuditableEntityOflong
    {
        
        private System.Nullable<bool> ACTIVEField;
        
        private string DESCRIPTIONField;
        
        private int IDField;
        
        private string NAMEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> ACTIVE
        {
            get
            {
                return this.ACTIVEField;
            }
            set
            {
                this.ACTIVEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DESCRIPTION
        {
            get
            {
                return this.DESCRIPTIONField;
            }
            set
            {
                this.DESCRIPTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NAME
        {
            get
            {
                return this.NAMEField;
            }
            set
            {
                this.NAMEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_USER_LINER", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_USER_LINER : AnonymousGateway.AuditableEntityOflong
    {
        
        private System.Nullable<int> CREATED_BYField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private int IDField;
        
        private string LINE_OPERField;
        
        private string SITE_IDField;
        
        private System.Nullable<int> UPDATED_BYField;
        
        private System.Nullable<System.DateTime> UPDATED_DATEField;
        
        private int USER_IDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LINE_OPER
        {
            get
            {
                return this.LINE_OPERField;
            }
            set
            {
                this.LINE_OPERField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> UPDATED_BY
        {
            get
            {
                return this.UPDATED_BYField;
            }
            set
            {
                this.UPDATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UPDATED_DATE
        {
            get
            {
                return this.UPDATED_DATEField;
            }
            set
            {
                this.UPDATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int USER_ID
        {
            get
            {
                return this.USER_IDField;
            }
            set
            {
                this.USER_IDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_MENU", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_MENU : AnonymousGateway.AuditableEntityOflong
    {
        
        private int GROUP_MENU_IDField;
        
        private string ICONField;
        
        private int IDField;
        
        private System.Nullable<bool> IS_DEFAULTField;
        
        private AnonymousGateway.Message[] MessagesField;
        
        private string NAMEField;
        
        private int SORTField;
        
        private string URLField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int GROUP_MENU_ID
        {
            get
            {
                return this.GROUP_MENU_IDField;
            }
            set
            {
                this.GROUP_MENU_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ICON
        {
            get
            {
                return this.ICONField;
            }
            set
            {
                this.ICONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IS_DEFAULT
        {
            get
            {
                return this.IS_DEFAULTField;
            }
            set
            {
                this.IS_DEFAULTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.Message[] Messages
        {
            get
            {
                return this.MessagesField;
            }
            set
            {
                this.MessagesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NAME
        {
            get
            {
                return this.NAMEField;
            }
            set
            {
                this.NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SORT
        {
            get
            {
                return this.SORTField;
            }
            set
            {
                this.SORTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string URL
        {
            get
            {
                return this.URLField;
            }
            set
            {
                this.URLField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_ROLE", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_ROLE : AnonymousGateway.AuditableEntityOflong
    {
        
        private System.Nullable<bool> ACTIVEField;
        
        private System.Nullable<int> CREATED_BYField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private string DESCRIPTIONField;
        
        private int IDField;
        
        private string NAMEField;
        
        private System.Nullable<bool> STATIC_ROLEField;
        
        private System.Nullable<int> UPDATED_BYField;
        
        private System.Nullable<System.DateTime> UPDATED_DATEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> ACTIVE
        {
            get
            {
                return this.ACTIVEField;
            }
            set
            {
                this.ACTIVEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DESCRIPTION
        {
            get
            {
                return this.DESCRIPTIONField;
            }
            set
            {
                this.DESCRIPTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NAME
        {
            get
            {
                return this.NAMEField;
            }
            set
            {
                this.NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> STATIC_ROLE
        {
            get
            {
                return this.STATIC_ROLEField;
            }
            set
            {
                this.STATIC_ROLEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> UPDATED_BY
        {
            get
            {
                return this.UPDATED_BYField;
            }
            set
            {
                this.UPDATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UPDATED_DATE
        {
            get
            {
                return this.UPDATED_DATEField;
            }
            set
            {
                this.UPDATED_DATEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_USER_SITE_ROLE", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_USER_SITE_ROLE : AnonymousGateway.AuditableEntityOflong
    {
        
        private System.Nullable<int> ROLE_IDField;
        
        private string SITE_IDField;
        
        private int USER_IDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> ROLE_ID
        {
            get
            {
                return this.ROLE_IDField;
            }
            set
            {
                this.ROLE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int USER_ID
        {
            get
            {
                return this.USER_IDField;
            }
            set
            {
                this.USER_IDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_OPER_METHOD_DTO", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_OPER_METHOD_DTO : object
    {
        
        private AnonymousGateway.AM_DISCOUNT_SETTING_DTO[] AM_DISCOUNT_SETTINGSField;
        
        private int GROUP_OPER_METHOD_IDField;
        
        private int IDField;
        
        private string OPER_METHODField;
        
        private string OPER_METHOD_NAMEField;
        
        private string OPER_TYPEField;
        
        private int TRANSPORT_TYPE_IDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_DISCOUNT_SETTING_DTO[] AM_DISCOUNT_SETTINGS
        {
            get
            {
                return this.AM_DISCOUNT_SETTINGSField;
            }
            set
            {
                this.AM_DISCOUNT_SETTINGSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int GROUP_OPER_METHOD_ID
        {
            get
            {
                return this.GROUP_OPER_METHOD_IDField;
            }
            set
            {
                this.GROUP_OPER_METHOD_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OPER_METHOD
        {
            get
            {
                return this.OPER_METHODField;
            }
            set
            {
                this.OPER_METHODField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OPER_METHOD_NAME
        {
            get
            {
                return this.OPER_METHOD_NAMEField;
            }
            set
            {
                this.OPER_METHOD_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OPER_TYPE
        {
            get
            {
                return this.OPER_TYPEField;
            }
            set
            {
                this.OPER_TYPEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TRANSPORT_TYPE_ID
        {
            get
            {
                return this.TRANSPORT_TYPE_IDField;
            }
            set
            {
                this.TRANSPORT_TYPE_IDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Message", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Common")]
    public partial class Message : object
    {
        
        private string ErrorCodeField;
        
        private string MessageContentField;
        
        private string[] MessageParamsField;
        
        private string MessageTypeField;
        
        private string QuestionCodeField;
        
        private int ReturnParamField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorCode
        {
            get
            {
                return this.ErrorCodeField;
            }
            set
            {
                this.ErrorCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MessageContent
        {
            get
            {
                return this.MessageContentField;
            }
            set
            {
                this.MessageContentField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string[] MessageParams
        {
            get
            {
                return this.MessageParamsField;
            }
            set
            {
                this.MessageParamsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MessageType
        {
            get
            {
                return this.MessageTypeField;
            }
            set
            {
                this.MessageTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string QuestionCode
        {
            get
            {
                return this.QuestionCodeField;
            }
            set
            {
                this.QuestionCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ReturnParam
        {
            get
            {
                return this.ReturnParamField;
            }
            set
            {
                this.ReturnParamField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_DISCOUNT_SETTING_DTO", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_DISCOUNT_SETTING_DTO : object
    {
        
        private AnonymousGateway.AM_CATEGORY_DTO AM_CATEGORYField;
        
        private AnonymousGateway.AM_DISCOUNT_UNIT_DTO AM_DISCOUNT_UNITField;
        
        private AnonymousGateway.AM_OPER_METHOD_DTO AM_OPER_METHODField;
        
        private int CATEGORY_IDField;
        
        private string CODEField;
        
        private int CREATED_BYField;
        
        private System.DateTime CREATED_DATEField;
        
        private string DESCRIPTIONField;
        
        private decimal DISCOUNTField;
        
        private int DISCOUNT_UNIT_IDField;
        
        private System.Nullable<System.DateTime> EFFECTED_DATEField;
        
        private System.Nullable<System.DateTime> EXPIRED_DATEField;
        
        private int FE_IDField;
        
        private int IDField;
        
        private string NOTEField;
        
        private int OPER_METHOD_IDField;
        
        private string SITE_IDField;
        
        private int UPDATED_BYField;
        
        private System.DateTime UPDATED_DATEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_CATEGORY_DTO AM_CATEGORY
        {
            get
            {
                return this.AM_CATEGORYField;
            }
            set
            {
                this.AM_CATEGORYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_DISCOUNT_UNIT_DTO AM_DISCOUNT_UNIT
        {
            get
            {
                return this.AM_DISCOUNT_UNITField;
            }
            set
            {
                this.AM_DISCOUNT_UNITField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_OPER_METHOD_DTO AM_OPER_METHOD
        {
            get
            {
                return this.AM_OPER_METHODField;
            }
            set
            {
                this.AM_OPER_METHODField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int CATEGORY_ID
        {
            get
            {
                return this.CATEGORY_IDField;
            }
            set
            {
                this.CATEGORY_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CODE
        {
            get
            {
                return this.CODEField;
            }
            set
            {
                this.CODEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DESCRIPTION
        {
            get
            {
                return this.DESCRIPTIONField;
            }
            set
            {
                this.DESCRIPTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal DISCOUNT
        {
            get
            {
                return this.DISCOUNTField;
            }
            set
            {
                this.DISCOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DISCOUNT_UNIT_ID
        {
            get
            {
                return this.DISCOUNT_UNIT_IDField;
            }
            set
            {
                this.DISCOUNT_UNIT_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> EFFECTED_DATE
        {
            get
            {
                return this.EFFECTED_DATEField;
            }
            set
            {
                this.EFFECTED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> EXPIRED_DATE
        {
            get
            {
                return this.EXPIRED_DATEField;
            }
            set
            {
                this.EXPIRED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FE_ID
        {
            get
            {
                return this.FE_IDField;
            }
            set
            {
                this.FE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NOTE
        {
            get
            {
                return this.NOTEField;
            }
            set
            {
                this.NOTEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OPER_METHOD_ID
        {
            get
            {
                return this.OPER_METHOD_IDField;
            }
            set
            {
                this.OPER_METHOD_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UPDATED_BY
        {
            get
            {
                return this.UPDATED_BYField;
            }
            set
            {
                this.UPDATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime UPDATED_DATE
        {
            get
            {
                return this.UPDATED_DATEField;
            }
            set
            {
                this.UPDATED_DATEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_CATEGORY_DTO", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_CATEGORY_DTO : object
    {
        
        private System.Nullable<int> CREATED_BYField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private string DESCRIPTIONField;
        
        private int IDField;
        
        private string NAMEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DESCRIPTION
        {
            get
            {
                return this.DESCRIPTIONField;
            }
            set
            {
                this.DESCRIPTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NAME
        {
            get
            {
                return this.NAMEField;
            }
            set
            {
                this.NAMEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AM_DISCOUNT_UNIT_DTO", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class AM_DISCOUNT_UNIT_DTO : object
    {
        
        private AnonymousGateway.AM_DISCOUNT_SETTING_DTO[] AM_DISCOUNT_SETTINGSField;
        
        private string DESCRIPTIONField;
        
        private int IDField;
        
        private string NAMEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_DISCOUNT_SETTING_DTO[] AM_DISCOUNT_SETTINGS
        {
            get
            {
                return this.AM_DISCOUNT_SETTINGSField;
            }
            set
            {
                this.AM_DISCOUNT_SETTINGSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DESCRIPTION
        {
            get
            {
                return this.DESCRIPTIONField;
            }
            set
            {
                this.DESCRIPTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NAME
        {
            get
            {
                return this.NAMEField;
            }
            set
            {
                this.NAMEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="VE_CONTAINER_INFO", Namespace="http://schemas.datacontract.org/2004/07/Snp.Tos.Entity")]
    public partial class VE_CONTAINER_INFO : AnonymousGateway.BaseEntity1
    {
        
        private string BILL_BOOKField;
        
        private string CATEGORYField;
        
        private string CONTAINERNOField;
        
        private string CUSTField;
        
        private string CUSTOM_CLEARANCE_STATUSField;
        
        private System.Nullable<System.DateTime> CUST_APPROVAL_DATEField;
        
        private System.DateTime EVENT_TIMEField;
        
        private string EVENT_TYPEField;
        
        private string FELField;
        
        private System.Nullable<decimal> GATE_GROSS_WTField;
        
        private System.Nullable<decimal> GATE_WTField;
        
        private System.Nullable<decimal> GROSSField;
        
        private string HAZField;
        
        private string IMDG_PATHField;
        
        private string IM_EXPField;
        
        private string IN_YARDField;
        
        private string ISOField;
        
        private int ITEM_KEYField;
        
        private string ITEM_SEAL_NOField;
        
        private string LINE_OPERField;
        
        private string LOAD_TO_VESSELField;
        
        private string LOCATIONField;
        
        private decimal MANIFEST_WTField;
        
        private string NOTEField;
        
        private string POD_DESTINATIONField;
        
        private string SITEField;
        
        private string STACKField;
        
        private decimal TARE_WTField;
        
        private string TEMPField;
        
        private System.Nullable<System.DateTime> TRANS_INField;
        
        private System.Nullable<System.DateTime> TRANS_OUTField;
        
        private string TRUCK_VESSELField;
        
        private string VGMField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BILL_BOOK
        {
            get
            {
                return this.BILL_BOOKField;
            }
            set
            {
                this.BILL_BOOKField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CATEGORY
        {
            get
            {
                return this.CATEGORYField;
            }
            set
            {
                this.CATEGORYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CONTAINERNO
        {
            get
            {
                return this.CONTAINERNOField;
            }
            set
            {
                this.CONTAINERNOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CUST
        {
            get
            {
                return this.CUSTField;
            }
            set
            {
                this.CUSTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CUSTOM_CLEARANCE_STATUS
        {
            get
            {
                return this.CUSTOM_CLEARANCE_STATUSField;
            }
            set
            {
                this.CUSTOM_CLEARANCE_STATUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CUST_APPROVAL_DATE
        {
            get
            {
                return this.CUST_APPROVAL_DATEField;
            }
            set
            {
                this.CUST_APPROVAL_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EVENT_TIME
        {
            get
            {
                return this.EVENT_TIMEField;
            }
            set
            {
                this.EVENT_TIMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EVENT_TYPE
        {
            get
            {
                return this.EVENT_TYPEField;
            }
            set
            {
                this.EVENT_TYPEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FEL
        {
            get
            {
                return this.FELField;
            }
            set
            {
                this.FELField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> GATE_GROSS_WT
        {
            get
            {
                return this.GATE_GROSS_WTField;
            }
            set
            {
                this.GATE_GROSS_WTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> GATE_WT
        {
            get
            {
                return this.GATE_WTField;
            }
            set
            {
                this.GATE_WTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> GROSS
        {
            get
            {
                return this.GROSSField;
            }
            set
            {
                this.GROSSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HAZ
        {
            get
            {
                return this.HAZField;
            }
            set
            {
                this.HAZField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IMDG_PATH
        {
            get
            {
                return this.IMDG_PATHField;
            }
            set
            {
                this.IMDG_PATHField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IM_EXP
        {
            get
            {
                return this.IM_EXPField;
            }
            set
            {
                this.IM_EXPField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IN_YARD
        {
            get
            {
                return this.IN_YARDField;
            }
            set
            {
                this.IN_YARDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ISO
        {
            get
            {
                return this.ISOField;
            }
            set
            {
                this.ISOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ITEM_KEY
        {
            get
            {
                return this.ITEM_KEYField;
            }
            set
            {
                this.ITEM_KEYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ITEM_SEAL_NO
        {
            get
            {
                return this.ITEM_SEAL_NOField;
            }
            set
            {
                this.ITEM_SEAL_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LINE_OPER
        {
            get
            {
                return this.LINE_OPERField;
            }
            set
            {
                this.LINE_OPERField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LOAD_TO_VESSEL
        {
            get
            {
                return this.LOAD_TO_VESSELField;
            }
            set
            {
                this.LOAD_TO_VESSELField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LOCATION
        {
            get
            {
                return this.LOCATIONField;
            }
            set
            {
                this.LOCATIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal MANIFEST_WT
        {
            get
            {
                return this.MANIFEST_WTField;
            }
            set
            {
                this.MANIFEST_WTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NOTE
        {
            get
            {
                return this.NOTEField;
            }
            set
            {
                this.NOTEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string POD_DESTINATION
        {
            get
            {
                return this.POD_DESTINATIONField;
            }
            set
            {
                this.POD_DESTINATIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE
        {
            get
            {
                return this.SITEField;
            }
            set
            {
                this.SITEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string STACK
        {
            get
            {
                return this.STACKField;
            }
            set
            {
                this.STACKField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal TARE_WT
        {
            get
            {
                return this.TARE_WTField;
            }
            set
            {
                this.TARE_WTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TEMP
        {
            get
            {
                return this.TEMPField;
            }
            set
            {
                this.TEMPField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> TRANS_IN
        {
            get
            {
                return this.TRANS_INField;
            }
            set
            {
                this.TRANS_INField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> TRANS_OUT
        {
            get
            {
                return this.TRANS_OUTField;
            }
            set
            {
                this.TRANS_OUTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TRUCK_VESSEL
        {
            get
            {
                return this.TRUCK_VESSELField;
            }
            set
            {
                this.TRUCK_VESSELField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VGM
        {
            get
            {
                return this.VGMField;
            }
            set
            {
                this.VGMField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BaseEntity", Namespace="http://schemas.datacontract.org/2004/07/Snp.Tos.Entity")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.EntityOflong1))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AuditableEntityOflong1))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.SYS_CODES))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.VE_CONTAINER_INFO))]
    public partial class BaseEntity1 : object
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EntityOflong", Namespace="http://schemas.datacontract.org/2004/07/Snp.Tos.Entity")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.AuditableEntityOflong1))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.SYS_CODES))]
    public partial class EntityOflong1 : AnonymousGateway.BaseEntity1
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AuditableEntityOflong", Namespace="http://schemas.datacontract.org/2004/07/Snp.Tos.Entity")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(AnonymousGateway.SYS_CODES))]
    public partial class AuditableEntityOflong1 : AnonymousGateway.EntityOflong1
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SYS_CODES", Namespace="http://schemas.datacontract.org/2004/07/Snp.Tos.Entity")]
    public partial class SYS_CODES : AnonymousGateway.AuditableEntityOflong1
    {
        
        private string CODE_REFField;
        
        private string CODE_TPField;
        
        private string DESCRField;
        
        private string LOCAL_DESCRField;
        
        private System.DateTime UPD_TSField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CODE_REF
        {
            get
            {
                return this.CODE_REFField;
            }
            set
            {
                this.CODE_REFField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CODE_TP
        {
            get
            {
                return this.CODE_TPField;
            }
            set
            {
                this.CODE_TPField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DESCR
        {
            get
            {
                return this.DESCRField;
            }
            set
            {
                this.DESCRField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LOCAL_DESCR
        {
            get
            {
                return this.LOCAL_DESCRField;
            }
            set
            {
                this.LOCAL_DESCRField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime UPD_TS
        {
            get
            {
                return this.UPD_TSField;
            }
            set
            {
                this.UPD_TSField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EirDetail", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity.DTO")]
    public partial class EirDetail : object
    {
        
        private string AgentField;
        
        private string AreaField;
        
        private string ArrCarField;
        
        private string BatNoField;
        
        private string BayField;
        
        private string BlockField;
        
        private string BookBillField;
        
        private double CargoField;
        
        private string ChassisNoField;
        
        private string ContainerOwnerField;
        
        private string CreateByField;
        
        private System.DateTime CrtTsField;
        
        private string CustomerNameField;
        
        private string DamageStringField;
        
        private string DepCarField;
        
        private int DetentionField;
        
        private string DetentionNotesField;
        
        private System.DateTime DetentionTsField;
        
        private System.DateTime EirCompletedTsField;
        
        private string EirIdField;
        
        private string EirRemarkField;
        
        private string FelField;
        
        private string FpodField;
        
        private string FpodNameField;
        
        private string GateNoField;
        
        private string GateOutFlgField;
        
        private double GrossField;
        
        private string HasEdoField;
        
        private string HistFlgField;
        
        private string ImoField;
        
        private string InvoiceNoField;
        
        private string IsOogField;
        
        private string IsoField;
        
        private string ItemKeyField;
        
        private string ItemNoField;
        
        private string ItemRemarksField;
        
        private string KeyIntfField;
        
        private string LineOperField;
        
        private System.DateTime LinerExpiryTsField;
        
        private string LlPodField;
        
        private string LlPodNameField;
        
        private double MaxGrossField;
        
        private double OogHeightField;
        
        private double OogLengthField;
        
        private double OogWidthField;
        
        private string OperationMethodField;
        
        private string OperationNameField;
        
        private string OwnerField;
        
        private string PhoneNoField;
        
        private string PlaceOfDeliveryField;
        
        private string PlaceOfDeliveryNameField;
        
        private string PlaceOfEmptyRetField;
        
        private string PlaceOfReceiveField;
        
        private string PlaceOfReceiveNameField;
        
        private string PublishFlgField;
        
        private string RDField;
        
        private string ReeferStringField;
        
        private string RemarksField;
        
        private string RequesterField;
        
        private string RowField;
        
        private string SealNoField;
        
        private string SiteIdField;
        
        private string SpecialHandlingDescField;
        
        private string StopField;
        
        private AnonymousGateway.StripStuffTruckDto[] StripStuffTrucksField;
        
        private string StuffStripMethodField;
        
        private double TareField;
        
        private string TierField;
        
        private string TruckIdField;
        
        private string UnnoField;
        
        private string VersionField;
        
        private double VgmField;
        
        private double VgmCustomerField;
        
        private double VgmLopField;
        
        private string WeightRemarkField;
        
        private string WeightViolationField;
        
        private string WorkerTeamField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Agent
        {
            get
            {
                return this.AgentField;
            }
            set
            {
                this.AgentField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Area
        {
            get
            {
                return this.AreaField;
            }
            set
            {
                this.AreaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ArrCar
        {
            get
            {
                return this.ArrCarField;
            }
            set
            {
                this.ArrCarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BatNo
        {
            get
            {
                return this.BatNoField;
            }
            set
            {
                this.BatNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Bay
        {
            get
            {
                return this.BayField;
            }
            set
            {
                this.BayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Block
        {
            get
            {
                return this.BlockField;
            }
            set
            {
                this.BlockField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BookBill
        {
            get
            {
                return this.BookBillField;
            }
            set
            {
                this.BookBillField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Cargo
        {
            get
            {
                return this.CargoField;
            }
            set
            {
                this.CargoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ChassisNo
        {
            get
            {
                return this.ChassisNoField;
            }
            set
            {
                this.ChassisNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ContainerOwner
        {
            get
            {
                return this.ContainerOwnerField;
            }
            set
            {
                this.ContainerOwnerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CreateBy
        {
            get
            {
                return this.CreateByField;
            }
            set
            {
                this.CreateByField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CrtTs
        {
            get
            {
                return this.CrtTsField;
            }
            set
            {
                this.CrtTsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CustomerName
        {
            get
            {
                return this.CustomerNameField;
            }
            set
            {
                this.CustomerNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DamageString
        {
            get
            {
                return this.DamageStringField;
            }
            set
            {
                this.DamageStringField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DepCar
        {
            get
            {
                return this.DepCarField;
            }
            set
            {
                this.DepCarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Detention
        {
            get
            {
                return this.DetentionField;
            }
            set
            {
                this.DetentionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DetentionNotes
        {
            get
            {
                return this.DetentionNotesField;
            }
            set
            {
                this.DetentionNotesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime DetentionTs
        {
            get
            {
                return this.DetentionTsField;
            }
            set
            {
                this.DetentionTsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EirCompletedTs
        {
            get
            {
                return this.EirCompletedTsField;
            }
            set
            {
                this.EirCompletedTsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EirId
        {
            get
            {
                return this.EirIdField;
            }
            set
            {
                this.EirIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EirRemark
        {
            get
            {
                return this.EirRemarkField;
            }
            set
            {
                this.EirRemarkField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Fel
        {
            get
            {
                return this.FelField;
            }
            set
            {
                this.FelField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Fpod
        {
            get
            {
                return this.FpodField;
            }
            set
            {
                this.FpodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FpodName
        {
            get
            {
                return this.FpodNameField;
            }
            set
            {
                this.FpodNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GateNo
        {
            get
            {
                return this.GateNoField;
            }
            set
            {
                this.GateNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GateOutFlg
        {
            get
            {
                return this.GateOutFlgField;
            }
            set
            {
                this.GateOutFlgField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Gross
        {
            get
            {
                return this.GrossField;
            }
            set
            {
                this.GrossField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HasEdo
        {
            get
            {
                return this.HasEdoField;
            }
            set
            {
                this.HasEdoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HistFlg
        {
            get
            {
                return this.HistFlgField;
            }
            set
            {
                this.HistFlgField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Imo
        {
            get
            {
                return this.ImoField;
            }
            set
            {
                this.ImoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string InvoiceNo
        {
            get
            {
                return this.InvoiceNoField;
            }
            set
            {
                this.InvoiceNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IsOog
        {
            get
            {
                return this.IsOogField;
            }
            set
            {
                this.IsOogField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Iso
        {
            get
            {
                return this.IsoField;
            }
            set
            {
                this.IsoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ItemKey
        {
            get
            {
                return this.ItemKeyField;
            }
            set
            {
                this.ItemKeyField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ItemNo
        {
            get
            {
                return this.ItemNoField;
            }
            set
            {
                this.ItemNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ItemRemarks
        {
            get
            {
                return this.ItemRemarksField;
            }
            set
            {
                this.ItemRemarksField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KeyIntf
        {
            get
            {
                return this.KeyIntfField;
            }
            set
            {
                this.KeyIntfField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LineOper
        {
            get
            {
                return this.LineOperField;
            }
            set
            {
                this.LineOperField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime LinerExpiryTs
        {
            get
            {
                return this.LinerExpiryTsField;
            }
            set
            {
                this.LinerExpiryTsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LlPod
        {
            get
            {
                return this.LlPodField;
            }
            set
            {
                this.LlPodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LlPodName
        {
            get
            {
                return this.LlPodNameField;
            }
            set
            {
                this.LlPodNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double MaxGross
        {
            get
            {
                return this.MaxGrossField;
            }
            set
            {
                this.MaxGrossField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double OogHeight
        {
            get
            {
                return this.OogHeightField;
            }
            set
            {
                this.OogHeightField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double OogLength
        {
            get
            {
                return this.OogLengthField;
            }
            set
            {
                this.OogLengthField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double OogWidth
        {
            get
            {
                return this.OogWidthField;
            }
            set
            {
                this.OogWidthField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OperationMethod
        {
            get
            {
                return this.OperationMethodField;
            }
            set
            {
                this.OperationMethodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OperationName
        {
            get
            {
                return this.OperationNameField;
            }
            set
            {
                this.OperationNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Owner
        {
            get
            {
                return this.OwnerField;
            }
            set
            {
                this.OwnerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PhoneNo
        {
            get
            {
                return this.PhoneNoField;
            }
            set
            {
                this.PhoneNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PlaceOfDelivery
        {
            get
            {
                return this.PlaceOfDeliveryField;
            }
            set
            {
                this.PlaceOfDeliveryField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PlaceOfDeliveryName
        {
            get
            {
                return this.PlaceOfDeliveryNameField;
            }
            set
            {
                this.PlaceOfDeliveryNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PlaceOfEmptyRet
        {
            get
            {
                return this.PlaceOfEmptyRetField;
            }
            set
            {
                this.PlaceOfEmptyRetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PlaceOfReceive
        {
            get
            {
                return this.PlaceOfReceiveField;
            }
            set
            {
                this.PlaceOfReceiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PlaceOfReceiveName
        {
            get
            {
                return this.PlaceOfReceiveNameField;
            }
            set
            {
                this.PlaceOfReceiveNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PublishFlg
        {
            get
            {
                return this.PublishFlgField;
            }
            set
            {
                this.PublishFlgField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string RD
        {
            get
            {
                return this.RDField;
            }
            set
            {
                this.RDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ReeferString
        {
            get
            {
                return this.ReeferStringField;
            }
            set
            {
                this.ReeferStringField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Remarks
        {
            get
            {
                return this.RemarksField;
            }
            set
            {
                this.RemarksField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Requester
        {
            get
            {
                return this.RequesterField;
            }
            set
            {
                this.RequesterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Row
        {
            get
            {
                return this.RowField;
            }
            set
            {
                this.RowField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SealNo
        {
            get
            {
                return this.SealNoField;
            }
            set
            {
                this.SealNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiteId
        {
            get
            {
                return this.SiteIdField;
            }
            set
            {
                this.SiteIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SpecialHandlingDesc
        {
            get
            {
                return this.SpecialHandlingDescField;
            }
            set
            {
                this.SpecialHandlingDescField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Stop
        {
            get
            {
                return this.StopField;
            }
            set
            {
                this.StopField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.StripStuffTruckDto[] StripStuffTrucks
        {
            get
            {
                return this.StripStuffTrucksField;
            }
            set
            {
                this.StripStuffTrucksField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StuffStripMethod
        {
            get
            {
                return this.StuffStripMethodField;
            }
            set
            {
                this.StuffStripMethodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tare
        {
            get
            {
                return this.TareField;
            }
            set
            {
                this.TareField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tier
        {
            get
            {
                return this.TierField;
            }
            set
            {
                this.TierField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TruckId
        {
            get
            {
                return this.TruckIdField;
            }
            set
            {
                this.TruckIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Unno
        {
            get
            {
                return this.UnnoField;
            }
            set
            {
                this.UnnoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Version
        {
            get
            {
                return this.VersionField;
            }
            set
            {
                this.VersionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Vgm
        {
            get
            {
                return this.VgmField;
            }
            set
            {
                this.VgmField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double VgmCustomer
        {
            get
            {
                return this.VgmCustomerField;
            }
            set
            {
                this.VgmCustomerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double VgmLop
        {
            get
            {
                return this.VgmLopField;
            }
            set
            {
                this.VgmLopField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string WeightRemark
        {
            get
            {
                return this.WeightRemarkField;
            }
            set
            {
                this.WeightRemarkField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string WeightViolation
        {
            get
            {
                return this.WeightViolationField;
            }
            set
            {
                this.WeightViolationField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string WorkerTeam
        {
            get
            {
                return this.WorkerTeamField;
            }
            set
            {
                this.WorkerTeamField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="StripStuffTruckDto", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity.DTO.Eir")]
    public partial class StripStuffTruckDto : object
    {
        
        private System.Nullable<double> CargoField;
        
        private string CargoDisplayField;
        
        private string CargoUnitField;
        
        private string EirIdField;
        
        private System.Nullable<int> ItemKeyField;
        
        private System.Nullable<int> NumCargoField;
        
        private string NumCargoDisplayField;
        
        private System.DateTime StripStuffTsField;
        
        private string TruckIdField;
        
        private System.DateTime UpdTsField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<double> Cargo
        {
            get
            {
                return this.CargoField;
            }
            set
            {
                this.CargoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CargoDisplay
        {
            get
            {
                return this.CargoDisplayField;
            }
            set
            {
                this.CargoDisplayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CargoUnit
        {
            get
            {
                return this.CargoUnitField;
            }
            set
            {
                this.CargoUnitField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EirId
        {
            get
            {
                return this.EirIdField;
            }
            set
            {
                this.EirIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> ItemKey
        {
            get
            {
                return this.ItemKeyField;
            }
            set
            {
                this.ItemKeyField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> NumCargo
        {
            get
            {
                return this.NumCargoField;
            }
            set
            {
                this.NumCargoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NumCargoDisplay
        {
            get
            {
                return this.NumCargoDisplayField;
            }
            set
            {
                this.NumCargoDisplayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime StripStuffTs
        {
            get
            {
                return this.StripStuffTsField;
            }
            set
            {
                this.StripStuffTsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TruckId
        {
            get
            {
                return this.TruckIdField;
            }
            set
            {
                this.TruckIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime UpdTs
        {
            get
            {
                return this.UpdTsField;
            }
            set
            {
                this.UpdTsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="VE_VESSEL_INFO", Namespace="http://schemas.datacontract.org/2004/07/Snp.Tos.Entity")]
    public partial class VE_VESSEL_INFO : object
    {
        
        private string ACTUAL_BERTH_TIMEField;
        
        private string ACTUAL_DEPATURE_TIMEField;
        
        private string AGENTField;
        
        private string CLOSING_TIMEField;
        
        private string CLOSING_TIME_ICDField;
        
        private string HAZ_OPEN_TSField;
        
        private string IN_GATEField;
        
        private string IN_OUT_VOYAGEField;
        
        private string OOG_OPEN_TSField;
        
        private string OPEN_TSField;
        
        private string REEFER_OPEN_TSField;
        
        private string REMARKSField;
        
        private string SITE_IDField;
        
        private string VESSELNAMEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ACTUAL_BERTH_TIME
        {
            get
            {
                return this.ACTUAL_BERTH_TIMEField;
            }
            set
            {
                this.ACTUAL_BERTH_TIMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ACTUAL_DEPATURE_TIME
        {
            get
            {
                return this.ACTUAL_DEPATURE_TIMEField;
            }
            set
            {
                this.ACTUAL_DEPATURE_TIMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AGENT
        {
            get
            {
                return this.AGENTField;
            }
            set
            {
                this.AGENTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CLOSING_TIME
        {
            get
            {
                return this.CLOSING_TIMEField;
            }
            set
            {
                this.CLOSING_TIMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CLOSING_TIME_ICD
        {
            get
            {
                return this.CLOSING_TIME_ICDField;
            }
            set
            {
                this.CLOSING_TIME_ICDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HAZ_OPEN_TS
        {
            get
            {
                return this.HAZ_OPEN_TSField;
            }
            set
            {
                this.HAZ_OPEN_TSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IN_GATE
        {
            get
            {
                return this.IN_GATEField;
            }
            set
            {
                this.IN_GATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IN_OUT_VOYAGE
        {
            get
            {
                return this.IN_OUT_VOYAGEField;
            }
            set
            {
                this.IN_OUT_VOYAGEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OOG_OPEN_TS
        {
            get
            {
                return this.OOG_OPEN_TSField;
            }
            set
            {
                this.OOG_OPEN_TSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OPEN_TS
        {
            get
            {
                return this.OPEN_TSField;
            }
            set
            {
                this.OPEN_TSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string REEFER_OPEN_TS
        {
            get
            {
                return this.REEFER_OPEN_TSField;
            }
            set
            {
                this.REEFER_OPEN_TSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string REMARKS
        {
            get
            {
                return this.REMARKSField;
            }
            set
            {
                this.REMARKSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VESSELNAME
        {
            get
            {
                return this.VESSELNAMEField;
            }
            set
            {
                this.VESSELNAMEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DownloadInvoiceSearchModel", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity.Model")]
    public partial class DownloadInvoiceSearchModel : object
    {
        
        private string FKEYField;
        
        private string INVOICE_NOField;
        
        private string KeyFactorSearchField;
        
        private string ORDER_DETAIL_NOField;
        
        private string PATTERNField;
        
        private AnonymousGateway.AM_INVOICE_PATTERN[] PatternsField;
        
        private AnonymousGateway.INVOICE_DTO[] ResultField;
        
        private string SERIALField;
        
        private string SITE_IDField;
        
        private AnonymousGateway.AM_SITE[] SitesActiveField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FKEY
        {
            get
            {
                return this.FKEYField;
            }
            set
            {
                this.FKEYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string INVOICE_NO
        {
            get
            {
                return this.INVOICE_NOField;
            }
            set
            {
                this.INVOICE_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KeyFactorSearch
        {
            get
            {
                return this.KeyFactorSearchField;
            }
            set
            {
                this.KeyFactorSearchField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ORDER_DETAIL_NO
        {
            get
            {
                return this.ORDER_DETAIL_NOField;
            }
            set
            {
                this.ORDER_DETAIL_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PATTERN
        {
            get
            {
                return this.PATTERNField;
            }
            set
            {
                this.PATTERNField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_INVOICE_PATTERN[] Patterns
        {
            get
            {
                return this.PatternsField;
            }
            set
            {
                this.PatternsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.INVOICE_DTO[] Result
        {
            get
            {
                return this.ResultField;
            }
            set
            {
                this.ResultField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SERIAL
        {
            get
            {
                return this.SERIALField;
            }
            set
            {
                this.SERIALField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.AM_SITE[] SitesActive
        {
            get
            {
                return this.SitesActiveField;
            }
            set
            {
                this.SitesActiveField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="INVOICE_DTO", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity.DTO")]
    public partial class INVOICE_DTO : object
    {
        
        private decimal BILLED_AMOUNTField;
        
        private decimal CALCULATED_AMOUNTField;
        
        private AnonymousGateway.CHARGE_DTO[] CHARGEField;
        
        private System.Nullable<int> CREATED_BYField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private string CURRENCYField;
        
        private decimal CURRENCY_RATEField;
        
        private string CUST_REG_NOField;
        
        private decimal DISCOUNT_AMOUNTField;
        
        private System.Nullable<bool> DISCOUNT_ON_INVField;
        
        private System.Nullable<int> FORWARDERField;
        
        private string FULL_NAMEField;
        
        private string HEADERField;
        
        private System.Nullable<System.DateTime> INVOICE_DATEField;
        
        private AnonymousGateway.INVOICE_DETAILS_DTO[] INVOICE_DETAILSField;
        
        private string INVOICE_FKEYField;
        
        private int INVOICE_IDField;
        
        private string INVOICE_NOField;
        
        private System.Nullable<int> INVOICE_STATUSField;
        
        private string ORG_INVOICE_FKEYField;
        
        private string PATTERNField;
        
        private System.Nullable<int> PATTERN_IDField;
        
        private string PAYMENT_METHODField;
        
        private int QUANTITYField;
        
        private string REMARKField;
        
        private string SERIALField;
        
        private string SITE_IDField;
        
        private decimal TAX_AMOUNTField;
        
        private decimal TAX_RATEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal BILLED_AMOUNT
        {
            get
            {
                return this.BILLED_AMOUNTField;
            }
            set
            {
                this.BILLED_AMOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal CALCULATED_AMOUNT
        {
            get
            {
                return this.CALCULATED_AMOUNTField;
            }
            set
            {
                this.CALCULATED_AMOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.CHARGE_DTO[] CHARGE
        {
            get
            {
                return this.CHARGEField;
            }
            set
            {
                this.CHARGEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CURRENCY
        {
            get
            {
                return this.CURRENCYField;
            }
            set
            {
                this.CURRENCYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal CURRENCY_RATE
        {
            get
            {
                return this.CURRENCY_RATEField;
            }
            set
            {
                this.CURRENCY_RATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CUST_REG_NO
        {
            get
            {
                return this.CUST_REG_NOField;
            }
            set
            {
                this.CUST_REG_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal DISCOUNT_AMOUNT
        {
            get
            {
                return this.DISCOUNT_AMOUNTField;
            }
            set
            {
                this.DISCOUNT_AMOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> DISCOUNT_ON_INV
        {
            get
            {
                return this.DISCOUNT_ON_INVField;
            }
            set
            {
                this.DISCOUNT_ON_INVField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> FORWARDER
        {
            get
            {
                return this.FORWARDERField;
            }
            set
            {
                this.FORWARDERField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FULL_NAME
        {
            get
            {
                return this.FULL_NAMEField;
            }
            set
            {
                this.FULL_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HEADER
        {
            get
            {
                return this.HEADERField;
            }
            set
            {
                this.HEADERField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> INVOICE_DATE
        {
            get
            {
                return this.INVOICE_DATEField;
            }
            set
            {
                this.INVOICE_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.INVOICE_DETAILS_DTO[] INVOICE_DETAILS
        {
            get
            {
                return this.INVOICE_DETAILSField;
            }
            set
            {
                this.INVOICE_DETAILSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string INVOICE_FKEY
        {
            get
            {
                return this.INVOICE_FKEYField;
            }
            set
            {
                this.INVOICE_FKEYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int INVOICE_ID
        {
            get
            {
                return this.INVOICE_IDField;
            }
            set
            {
                this.INVOICE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string INVOICE_NO
        {
            get
            {
                return this.INVOICE_NOField;
            }
            set
            {
                this.INVOICE_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> INVOICE_STATUS
        {
            get
            {
                return this.INVOICE_STATUSField;
            }
            set
            {
                this.INVOICE_STATUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ORG_INVOICE_FKEY
        {
            get
            {
                return this.ORG_INVOICE_FKEYField;
            }
            set
            {
                this.ORG_INVOICE_FKEYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PATTERN
        {
            get
            {
                return this.PATTERNField;
            }
            set
            {
                this.PATTERNField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> PATTERN_ID
        {
            get
            {
                return this.PATTERN_IDField;
            }
            set
            {
                this.PATTERN_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PAYMENT_METHOD
        {
            get
            {
                return this.PAYMENT_METHODField;
            }
            set
            {
                this.PAYMENT_METHODField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int QUANTITY
        {
            get
            {
                return this.QUANTITYField;
            }
            set
            {
                this.QUANTITYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string REMARK
        {
            get
            {
                return this.REMARKField;
            }
            set
            {
                this.REMARKField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SERIAL
        {
            get
            {
                return this.SERIALField;
            }
            set
            {
                this.SERIALField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal TAX_AMOUNT
        {
            get
            {
                return this.TAX_AMOUNTField;
            }
            set
            {
                this.TAX_AMOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal TAX_RATE
        {
            get
            {
                return this.TAX_RATEField;
            }
            set
            {
                this.TAX_RATEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="CHARGE_DTO", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity.DTO")]
    public partial class CHARGE_DTO : object
    {
        
        private decimal AMOUNT_AFTER_DISCOUNTField;
        
        private decimal AMOUNT_DISCOUNT_BEFORE_TAXField;
        
        private decimal AMOUNT_DISCOUNT_PAYMENT_AFTER_TAXField;
        
        private decimal BILLED_AMOUNTField;
        
        private string BILLED_TOField;
        
        private decimal CACULATED_AMOUNTField;
        
        private string CHARGE_CODEField;
        
        private string CHARGE_FEATUREField;
        
        private int CHARGE_IDField;
        
        private decimal CK_PAYMENTField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private string CURRENCYField;
        
        private decimal CURRENCY_RATEField;
        
        private string ContainerNoField;
        
        private bool DELETED_FLGField;
        
        private string DESCRIPTIONField;
        
        private decimal DISCOUNT_AMOUNTField;
        
        private System.Nullable<int> DISCOUNT_FORField;
        
        private double DISCOUNT_PERCENTField;
        
        private decimal DISCOUNT_RATEField;
        
        private string DISCOUNT_SETTINGField;
        
        private System.DateTime EXECUTE_DATEField;
        
        private System.Nullable<bool> HAS_EDOField;
        
        private AnonymousGateway.INVOICE_DTO[] INVOICEField;
        
        private string INVOICE_FKEYField;
        
        private string OPER_METHODField;
        
        private AnonymousGateway.ORDER_DETAIL_DTO ORDER_DETAILField;
        
        private System.Nullable<int> ORDER_DETAIL_CREATED_BYField;
        
        private System.Nullable<int> ORDER_DETAIL_IDField;
        
        private string ORDER_DETAIL_NOField;
        
        private string PAYMENT_STATUSField;
        
        private decimal RATEField;
        
        private decimal RATE_AFTER_DISCOUNTField;
        
        private decimal RATE_BEFORE_TAXField;
        
        private string RATE_IDField;
        
        private string RECEIPT_NOField;
        
        private bool REFUND_FLGField;
        
        private string SITE_IDField;
        
        private decimal TAX_RATEField;
        
        private AnonymousGateway.TRANSACTION_DTO[] TRANSACTIONField;
        
        private string TRANSACTION_CODEField;
        
        private decimal UNITS_CHARGEDField;
        
        private string UNIT_CODEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal AMOUNT_AFTER_DISCOUNT
        {
            get
            {
                return this.AMOUNT_AFTER_DISCOUNTField;
            }
            set
            {
                this.AMOUNT_AFTER_DISCOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal AMOUNT_DISCOUNT_BEFORE_TAX
        {
            get
            {
                return this.AMOUNT_DISCOUNT_BEFORE_TAXField;
            }
            set
            {
                this.AMOUNT_DISCOUNT_BEFORE_TAXField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal AMOUNT_DISCOUNT_PAYMENT_AFTER_TAX
        {
            get
            {
                return this.AMOUNT_DISCOUNT_PAYMENT_AFTER_TAXField;
            }
            set
            {
                this.AMOUNT_DISCOUNT_PAYMENT_AFTER_TAXField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal BILLED_AMOUNT
        {
            get
            {
                return this.BILLED_AMOUNTField;
            }
            set
            {
                this.BILLED_AMOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BILLED_TO
        {
            get
            {
                return this.BILLED_TOField;
            }
            set
            {
                this.BILLED_TOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal CACULATED_AMOUNT
        {
            get
            {
                return this.CACULATED_AMOUNTField;
            }
            set
            {
                this.CACULATED_AMOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CHARGE_CODE
        {
            get
            {
                return this.CHARGE_CODEField;
            }
            set
            {
                this.CHARGE_CODEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CHARGE_FEATURE
        {
            get
            {
                return this.CHARGE_FEATUREField;
            }
            set
            {
                this.CHARGE_FEATUREField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int CHARGE_ID
        {
            get
            {
                return this.CHARGE_IDField;
            }
            set
            {
                this.CHARGE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal CK_PAYMENT
        {
            get
            {
                return this.CK_PAYMENTField;
            }
            set
            {
                this.CK_PAYMENTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CURRENCY
        {
            get
            {
                return this.CURRENCYField;
            }
            set
            {
                this.CURRENCYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal CURRENCY_RATE
        {
            get
            {
                return this.CURRENCY_RATEField;
            }
            set
            {
                this.CURRENCY_RATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ContainerNo
        {
            get
            {
                return this.ContainerNoField;
            }
            set
            {
                this.ContainerNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DELETED_FLG
        {
            get
            {
                return this.DELETED_FLGField;
            }
            set
            {
                this.DELETED_FLGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DESCRIPTION
        {
            get
            {
                return this.DESCRIPTIONField;
            }
            set
            {
                this.DESCRIPTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal DISCOUNT_AMOUNT
        {
            get
            {
                return this.DISCOUNT_AMOUNTField;
            }
            set
            {
                this.DISCOUNT_AMOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> DISCOUNT_FOR
        {
            get
            {
                return this.DISCOUNT_FORField;
            }
            set
            {
                this.DISCOUNT_FORField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double DISCOUNT_PERCENT
        {
            get
            {
                return this.DISCOUNT_PERCENTField;
            }
            set
            {
                this.DISCOUNT_PERCENTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal DISCOUNT_RATE
        {
            get
            {
                return this.DISCOUNT_RATEField;
            }
            set
            {
                this.DISCOUNT_RATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DISCOUNT_SETTING
        {
            get
            {
                return this.DISCOUNT_SETTINGField;
            }
            set
            {
                this.DISCOUNT_SETTINGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EXECUTE_DATE
        {
            get
            {
                return this.EXECUTE_DATEField;
            }
            set
            {
                this.EXECUTE_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> HAS_EDO
        {
            get
            {
                return this.HAS_EDOField;
            }
            set
            {
                this.HAS_EDOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.INVOICE_DTO[] INVOICE
        {
            get
            {
                return this.INVOICEField;
            }
            set
            {
                this.INVOICEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string INVOICE_FKEY
        {
            get
            {
                return this.INVOICE_FKEYField;
            }
            set
            {
                this.INVOICE_FKEYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OPER_METHOD
        {
            get
            {
                return this.OPER_METHODField;
            }
            set
            {
                this.OPER_METHODField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.ORDER_DETAIL_DTO ORDER_DETAIL
        {
            get
            {
                return this.ORDER_DETAILField;
            }
            set
            {
                this.ORDER_DETAILField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> ORDER_DETAIL_CREATED_BY
        {
            get
            {
                return this.ORDER_DETAIL_CREATED_BYField;
            }
            set
            {
                this.ORDER_DETAIL_CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> ORDER_DETAIL_ID
        {
            get
            {
                return this.ORDER_DETAIL_IDField;
            }
            set
            {
                this.ORDER_DETAIL_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ORDER_DETAIL_NO
        {
            get
            {
                return this.ORDER_DETAIL_NOField;
            }
            set
            {
                this.ORDER_DETAIL_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PAYMENT_STATUS
        {
            get
            {
                return this.PAYMENT_STATUSField;
            }
            set
            {
                this.PAYMENT_STATUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal RATE
        {
            get
            {
                return this.RATEField;
            }
            set
            {
                this.RATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal RATE_AFTER_DISCOUNT
        {
            get
            {
                return this.RATE_AFTER_DISCOUNTField;
            }
            set
            {
                this.RATE_AFTER_DISCOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal RATE_BEFORE_TAX
        {
            get
            {
                return this.RATE_BEFORE_TAXField;
            }
            set
            {
                this.RATE_BEFORE_TAXField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string RATE_ID
        {
            get
            {
                return this.RATE_IDField;
            }
            set
            {
                this.RATE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string RECEIPT_NO
        {
            get
            {
                return this.RECEIPT_NOField;
            }
            set
            {
                this.RECEIPT_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool REFUND_FLG
        {
            get
            {
                return this.REFUND_FLGField;
            }
            set
            {
                this.REFUND_FLGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal TAX_RATE
        {
            get
            {
                return this.TAX_RATEField;
            }
            set
            {
                this.TAX_RATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.TRANSACTION_DTO[] TRANSACTION
        {
            get
            {
                return this.TRANSACTIONField;
            }
            set
            {
                this.TRANSACTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TRANSACTION_CODE
        {
            get
            {
                return this.TRANSACTION_CODEField;
            }
            set
            {
                this.TRANSACTION_CODEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal UNITS_CHARGED
        {
            get
            {
                return this.UNITS_CHARGEDField;
            }
            set
            {
                this.UNITS_CHARGEDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UNIT_CODE
        {
            get
            {
                return this.UNIT_CODEField;
            }
            set
            {
                this.UNIT_CODEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="INVOICE_DETAILS_DTO", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity.DTO")]
    public partial class INVOICE_DETAILS_DTO : object
    {
        
        private decimal AMOUNTField;
        
        private decimal CALCULATE_AMOUNTField;
        
        private string CHARGE_CDField;
        
        private System.Nullable<int> CREATED_BYField;
        
        private System.DateTime CREATED_DATEField;
        
        private string DESCRField;
        
        private decimal DISCOUNTField;
        
        private int INVOICE_DETAIL_IDField;
        
        private System.Nullable<int> INVOICE_IDField;
        
        private int QTYField;
        
        private decimal RATE_AFTER_TAXField;
        
        private decimal RATE_BEFORE_TAXField;
        
        private int SEQField;
        
        private string SITE_IDField;
        
        private decimal TaxRateField;
        
        private string UNIT_CODEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal AMOUNT
        {
            get
            {
                return this.AMOUNTField;
            }
            set
            {
                this.AMOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal CALCULATE_AMOUNT
        {
            get
            {
                return this.CALCULATE_AMOUNTField;
            }
            set
            {
                this.CALCULATE_AMOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CHARGE_CD
        {
            get
            {
                return this.CHARGE_CDField;
            }
            set
            {
                this.CHARGE_CDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DESCR
        {
            get
            {
                return this.DESCRField;
            }
            set
            {
                this.DESCRField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal DISCOUNT
        {
            get
            {
                return this.DISCOUNTField;
            }
            set
            {
                this.DISCOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int INVOICE_DETAIL_ID
        {
            get
            {
                return this.INVOICE_DETAIL_IDField;
            }
            set
            {
                this.INVOICE_DETAIL_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> INVOICE_ID
        {
            get
            {
                return this.INVOICE_IDField;
            }
            set
            {
                this.INVOICE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int QTY
        {
            get
            {
                return this.QTYField;
            }
            set
            {
                this.QTYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal RATE_AFTER_TAX
        {
            get
            {
                return this.RATE_AFTER_TAXField;
            }
            set
            {
                this.RATE_AFTER_TAXField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal RATE_BEFORE_TAX
        {
            get
            {
                return this.RATE_BEFORE_TAXField;
            }
            set
            {
                this.RATE_BEFORE_TAXField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SEQ
        {
            get
            {
                return this.SEQField;
            }
            set
            {
                this.SEQField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal TaxRate
        {
            get
            {
                return this.TaxRateField;
            }
            set
            {
                this.TaxRateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UNIT_CODE
        {
            get
            {
                return this.UNIT_CODEField;
            }
            set
            {
                this.UNIT_CODEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ORDER_DETAIL_DTO", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity")]
    public partial class ORDER_DETAIL_DTO : object
    {
        
        private string AGENTField;
        
        private string AGENT_NAMEField;
        
        private string APPROVAL_CODEField;
        
        private System.Nullable<System.DateTime> APPROVAL_CODE_EXPIRYField;
        
        private string APPROVED_BYField;
        
        private System.Nullable<System.DateTime> APPROVED_DATEField;
        
        private System.Nullable<bool> APPROVED_DOC_FLGField;
        
        private string APPROVED_SYSTEMField;
        
        private System.Nullable<System.DateTime> ARRIVE_ESTField;
        
        private string BILL_OF_LADINGField;
        
        private string BOOKING_NOField;
        
        private decimal CARGO_WEIGHTField;
        
        private string CERTIFIED_PLACEField;
        
        private string CHASIS_NOField;
        
        private System.DateTime CLOSING_TIMEField;
        
        private string COMPANY_NAME_FUMIGATIONField;
        
        private string CONSIGNEEField;
        
        private System.Nullable<int> CREATED_BYField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private int CUSTOMS_CHECKEDField;
        
        private int CUSTOMS_REQField;
        
        private int CUSTOMS_REQ_100Field;
        
        private string CUSTOMS_STATUSField;
        
        private string CUST_REG_NO_FUMIGATIONField;
        
        private string CUST_TAXFILENOField;
        
        private string CreatedByField;
        
        private string CustPhoneField;
        
        private string CustRegNoField;
        
        private System.Nullable<bool> DELETED_FLGField;
        
        private System.Nullable<System.DateTime> DELIVERED_DATEField;
        
        private System.Nullable<System.DateTime> DEPART_ESTField;
        
        private string DEPOT_CODE_DELIVERYField;
        
        private string DEPOT_CODE_RECEIPTField;
        
        private System.Nullable<int> DETENTIONField;
        
        private bool DOMESTIC_FLGField;
        
        private string EMAILField;
        
        private string EMERGENCY_PHONEField;
        
        private System.Nullable<System.DateTime> EXECUTE_ESTField;
        
        private string EXECUTIVE_PLANField;
        
        private string FPODField;
        
        private string FPOD_NAMEField;
        
        private string FULL_EMPTYField;
        
        private string GATE_NAMEField;
        
        private decimal GROSS_WEIGHTField;
        
        private System.Nullable<bool> HAS_EDOField;
        
        private string HAZADOUSField;
        
        private string HAZARDOUS_CONTENTField;
        
        private System.Nullable<int> HUMIDITYField;
        
        private string IDENTITY_NOField;
        
        private string IN_VOYAGEField;
        
        private string ISO_CODEField;
        
        private string ITEM_NOField;
        
        private System.DateTime LINER_EXPIRY_DATEField;
        
        private string LINE_OPERField;
        
        private string LINE_OPER_NAMEField;
        
        private string LINE_OPER_STATUSField;
        
        private string LLPODField;
        
        private string LLPOD_NAMEField;
        
        private decimal MAX_GROSSField;
        
        private string NO_PLUGIN_REQUIREDField;
        
        private string OPER_METHODField;
        
        private int ORDER_DETAIL_IDField;
        
        private string ORDER_DETAIL_NOField;
        
        private int ORDER_IDField;
        
        private string OUT_VOYAGEField;
        
        private string OVER_SIZEField;
        
        private string PACKAGE_TYPEField;
        
        private System.Nullable<int> PAID_BY_USERField;
        
        private string PAYMENT_STATUSField;
        
        private string PHONE_NUMBERField;
        
        private string PHONE_NUMBER_FUMIGATIONField;
        
        private string PLACE_OF_DELIVERYField;
        
        private string PLACE_OF_RECEIPTField;
        
        private string POST_PAID_FLGField;
        
        private System.Nullable<System.DateTime> POWER_OFF_DATEField;
        
        private System.Nullable<bool> PRINT_FLGField;
        
        private System.Nullable<int> RA_KEYField;
        
        private string RA_NO_EMPTYField;
        
        private string READY_FOR_TRANSField;
        
        private string RELEASE_NOField;
        
        private string REMARKField;
        
        private string REQ_GRADEField;
        
        private string RegisterUserField;
        
        private string SEAL_NOField;
        
        private string SECURE_CODEField;
        
        private System.Nullable<bool> SEND_SMS_FLAGField;
        
        private System.Nullable<int> SEQ_NOField;
        
        private string SERVICE_CODEField;
        
        private string SITE_IDField;
        
        private string SO_PHIEUField;
        
        private string SPECIALIZED_TRANSField;
        
        private string SPECIAL_HDLField;
        
        private string SPECIFY_ARISEField;
        
        private string STATUSField;
        
        private System.Nullable<bool> STOPPED_FLGField;
        
        private string TEMPField;
        
        private string TEMP_UNITField;
        
        private string TRUCK_NOField;
        
        private string UNNOField;
        
        private System.Nullable<int> UPDATED_BYField;
        
        private System.Nullable<System.DateTime> UPDATED_DATEField;
        
        private string VENTField;
        
        private string VENT_UNITField;
        
        private string VESSEL_CODEField;
        
        private string VESSEL_NAMEField;
        
        private string VES_IDField;
        
        private int WEIGHT_CERTIFIEDField;
        
        private string YARD_CONSOL_STATUSField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AGENT
        {
            get
            {
                return this.AGENTField;
            }
            set
            {
                this.AGENTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AGENT_NAME
        {
            get
            {
                return this.AGENT_NAMEField;
            }
            set
            {
                this.AGENT_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string APPROVAL_CODE
        {
            get
            {
                return this.APPROVAL_CODEField;
            }
            set
            {
                this.APPROVAL_CODEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> APPROVAL_CODE_EXPIRY
        {
            get
            {
                return this.APPROVAL_CODE_EXPIRYField;
            }
            set
            {
                this.APPROVAL_CODE_EXPIRYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string APPROVED_BY
        {
            get
            {
                return this.APPROVED_BYField;
            }
            set
            {
                this.APPROVED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> APPROVED_DATE
        {
            get
            {
                return this.APPROVED_DATEField;
            }
            set
            {
                this.APPROVED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> APPROVED_DOC_FLG
        {
            get
            {
                return this.APPROVED_DOC_FLGField;
            }
            set
            {
                this.APPROVED_DOC_FLGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string APPROVED_SYSTEM
        {
            get
            {
                return this.APPROVED_SYSTEMField;
            }
            set
            {
                this.APPROVED_SYSTEMField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> ARRIVE_EST
        {
            get
            {
                return this.ARRIVE_ESTField;
            }
            set
            {
                this.ARRIVE_ESTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BILL_OF_LADING
        {
            get
            {
                return this.BILL_OF_LADINGField;
            }
            set
            {
                this.BILL_OF_LADINGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BOOKING_NO
        {
            get
            {
                return this.BOOKING_NOField;
            }
            set
            {
                this.BOOKING_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal CARGO_WEIGHT
        {
            get
            {
                return this.CARGO_WEIGHTField;
            }
            set
            {
                this.CARGO_WEIGHTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CERTIFIED_PLACE
        {
            get
            {
                return this.CERTIFIED_PLACEField;
            }
            set
            {
                this.CERTIFIED_PLACEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CHASIS_NO
        {
            get
            {
                return this.CHASIS_NOField;
            }
            set
            {
                this.CHASIS_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CLOSING_TIME
        {
            get
            {
                return this.CLOSING_TIMEField;
            }
            set
            {
                this.CLOSING_TIMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string COMPANY_NAME_FUMIGATION
        {
            get
            {
                return this.COMPANY_NAME_FUMIGATIONField;
            }
            set
            {
                this.COMPANY_NAME_FUMIGATIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CONSIGNEE
        {
            get
            {
                return this.CONSIGNEEField;
            }
            set
            {
                this.CONSIGNEEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int CUSTOMS_CHECKED
        {
            get
            {
                return this.CUSTOMS_CHECKEDField;
            }
            set
            {
                this.CUSTOMS_CHECKEDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int CUSTOMS_REQ
        {
            get
            {
                return this.CUSTOMS_REQField;
            }
            set
            {
                this.CUSTOMS_REQField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int CUSTOMS_REQ_100
        {
            get
            {
                return this.CUSTOMS_REQ_100Field;
            }
            set
            {
                this.CUSTOMS_REQ_100Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CUSTOMS_STATUS
        {
            get
            {
                return this.CUSTOMS_STATUSField;
            }
            set
            {
                this.CUSTOMS_STATUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CUST_REG_NO_FUMIGATION
        {
            get
            {
                return this.CUST_REG_NO_FUMIGATIONField;
            }
            set
            {
                this.CUST_REG_NO_FUMIGATIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CUST_TAXFILENO
        {
            get
            {
                return this.CUST_TAXFILENOField;
            }
            set
            {
                this.CUST_TAXFILENOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CreatedBy
        {
            get
            {
                return this.CreatedByField;
            }
            set
            {
                this.CreatedByField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CustPhone
        {
            get
            {
                return this.CustPhoneField;
            }
            set
            {
                this.CustPhoneField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CustRegNo
        {
            get
            {
                return this.CustRegNoField;
            }
            set
            {
                this.CustRegNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> DELETED_FLG
        {
            get
            {
                return this.DELETED_FLGField;
            }
            set
            {
                this.DELETED_FLGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DELIVERED_DATE
        {
            get
            {
                return this.DELIVERED_DATEField;
            }
            set
            {
                this.DELIVERED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DEPART_EST
        {
            get
            {
                return this.DEPART_ESTField;
            }
            set
            {
                this.DEPART_ESTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DEPOT_CODE_DELIVERY
        {
            get
            {
                return this.DEPOT_CODE_DELIVERYField;
            }
            set
            {
                this.DEPOT_CODE_DELIVERYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DEPOT_CODE_RECEIPT
        {
            get
            {
                return this.DEPOT_CODE_RECEIPTField;
            }
            set
            {
                this.DEPOT_CODE_RECEIPTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> DETENTION
        {
            get
            {
                return this.DETENTIONField;
            }
            set
            {
                this.DETENTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DOMESTIC_FLG
        {
            get
            {
                return this.DOMESTIC_FLGField;
            }
            set
            {
                this.DOMESTIC_FLGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EMAIL
        {
            get
            {
                return this.EMAILField;
            }
            set
            {
                this.EMAILField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EMERGENCY_PHONE
        {
            get
            {
                return this.EMERGENCY_PHONEField;
            }
            set
            {
                this.EMERGENCY_PHONEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> EXECUTE_EST
        {
            get
            {
                return this.EXECUTE_ESTField;
            }
            set
            {
                this.EXECUTE_ESTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EXECUTIVE_PLAN
        {
            get
            {
                return this.EXECUTIVE_PLANField;
            }
            set
            {
                this.EXECUTIVE_PLANField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FPOD
        {
            get
            {
                return this.FPODField;
            }
            set
            {
                this.FPODField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FPOD_NAME
        {
            get
            {
                return this.FPOD_NAMEField;
            }
            set
            {
                this.FPOD_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FULL_EMPTY
        {
            get
            {
                return this.FULL_EMPTYField;
            }
            set
            {
                this.FULL_EMPTYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GATE_NAME
        {
            get
            {
                return this.GATE_NAMEField;
            }
            set
            {
                this.GATE_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal GROSS_WEIGHT
        {
            get
            {
                return this.GROSS_WEIGHTField;
            }
            set
            {
                this.GROSS_WEIGHTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> HAS_EDO
        {
            get
            {
                return this.HAS_EDOField;
            }
            set
            {
                this.HAS_EDOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HAZADOUS
        {
            get
            {
                return this.HAZADOUSField;
            }
            set
            {
                this.HAZADOUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HAZARDOUS_CONTENT
        {
            get
            {
                return this.HAZARDOUS_CONTENTField;
            }
            set
            {
                this.HAZARDOUS_CONTENTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> HUMIDITY
        {
            get
            {
                return this.HUMIDITYField;
            }
            set
            {
                this.HUMIDITYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IDENTITY_NO
        {
            get
            {
                return this.IDENTITY_NOField;
            }
            set
            {
                this.IDENTITY_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IN_VOYAGE
        {
            get
            {
                return this.IN_VOYAGEField;
            }
            set
            {
                this.IN_VOYAGEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ISO_CODE
        {
            get
            {
                return this.ISO_CODEField;
            }
            set
            {
                this.ISO_CODEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ITEM_NO
        {
            get
            {
                return this.ITEM_NOField;
            }
            set
            {
                this.ITEM_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime LINER_EXPIRY_DATE
        {
            get
            {
                return this.LINER_EXPIRY_DATEField;
            }
            set
            {
                this.LINER_EXPIRY_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LINE_OPER
        {
            get
            {
                return this.LINE_OPERField;
            }
            set
            {
                this.LINE_OPERField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LINE_OPER_NAME
        {
            get
            {
                return this.LINE_OPER_NAMEField;
            }
            set
            {
                this.LINE_OPER_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LINE_OPER_STATUS
        {
            get
            {
                return this.LINE_OPER_STATUSField;
            }
            set
            {
                this.LINE_OPER_STATUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LLPOD
        {
            get
            {
                return this.LLPODField;
            }
            set
            {
                this.LLPODField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LLPOD_NAME
        {
            get
            {
                return this.LLPOD_NAMEField;
            }
            set
            {
                this.LLPOD_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal MAX_GROSS
        {
            get
            {
                return this.MAX_GROSSField;
            }
            set
            {
                this.MAX_GROSSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NO_PLUGIN_REQUIRED
        {
            get
            {
                return this.NO_PLUGIN_REQUIREDField;
            }
            set
            {
                this.NO_PLUGIN_REQUIREDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OPER_METHOD
        {
            get
            {
                return this.OPER_METHODField;
            }
            set
            {
                this.OPER_METHODField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ORDER_DETAIL_ID
        {
            get
            {
                return this.ORDER_DETAIL_IDField;
            }
            set
            {
                this.ORDER_DETAIL_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ORDER_DETAIL_NO
        {
            get
            {
                return this.ORDER_DETAIL_NOField;
            }
            set
            {
                this.ORDER_DETAIL_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ORDER_ID
        {
            get
            {
                return this.ORDER_IDField;
            }
            set
            {
                this.ORDER_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OUT_VOYAGE
        {
            get
            {
                return this.OUT_VOYAGEField;
            }
            set
            {
                this.OUT_VOYAGEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OVER_SIZE
        {
            get
            {
                return this.OVER_SIZEField;
            }
            set
            {
                this.OVER_SIZEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PACKAGE_TYPE
        {
            get
            {
                return this.PACKAGE_TYPEField;
            }
            set
            {
                this.PACKAGE_TYPEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> PAID_BY_USER
        {
            get
            {
                return this.PAID_BY_USERField;
            }
            set
            {
                this.PAID_BY_USERField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PAYMENT_STATUS
        {
            get
            {
                return this.PAYMENT_STATUSField;
            }
            set
            {
                this.PAYMENT_STATUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PHONE_NUMBER
        {
            get
            {
                return this.PHONE_NUMBERField;
            }
            set
            {
                this.PHONE_NUMBERField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PHONE_NUMBER_FUMIGATION
        {
            get
            {
                return this.PHONE_NUMBER_FUMIGATIONField;
            }
            set
            {
                this.PHONE_NUMBER_FUMIGATIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PLACE_OF_DELIVERY
        {
            get
            {
                return this.PLACE_OF_DELIVERYField;
            }
            set
            {
                this.PLACE_OF_DELIVERYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PLACE_OF_RECEIPT
        {
            get
            {
                return this.PLACE_OF_RECEIPTField;
            }
            set
            {
                this.PLACE_OF_RECEIPTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string POST_PAID_FLG
        {
            get
            {
                return this.POST_PAID_FLGField;
            }
            set
            {
                this.POST_PAID_FLGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> POWER_OFF_DATE
        {
            get
            {
                return this.POWER_OFF_DATEField;
            }
            set
            {
                this.POWER_OFF_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> PRINT_FLG
        {
            get
            {
                return this.PRINT_FLGField;
            }
            set
            {
                this.PRINT_FLGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> RA_KEY
        {
            get
            {
                return this.RA_KEYField;
            }
            set
            {
                this.RA_KEYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string RA_NO_EMPTY
        {
            get
            {
                return this.RA_NO_EMPTYField;
            }
            set
            {
                this.RA_NO_EMPTYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string READY_FOR_TRANS
        {
            get
            {
                return this.READY_FOR_TRANSField;
            }
            set
            {
                this.READY_FOR_TRANSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string RELEASE_NO
        {
            get
            {
                return this.RELEASE_NOField;
            }
            set
            {
                this.RELEASE_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string REMARK
        {
            get
            {
                return this.REMARKField;
            }
            set
            {
                this.REMARKField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string REQ_GRADE
        {
            get
            {
                return this.REQ_GRADEField;
            }
            set
            {
                this.REQ_GRADEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string RegisterUser
        {
            get
            {
                return this.RegisterUserField;
            }
            set
            {
                this.RegisterUserField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SEAL_NO
        {
            get
            {
                return this.SEAL_NOField;
            }
            set
            {
                this.SEAL_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SECURE_CODE
        {
            get
            {
                return this.SECURE_CODEField;
            }
            set
            {
                this.SECURE_CODEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> SEND_SMS_FLAG
        {
            get
            {
                return this.SEND_SMS_FLAGField;
            }
            set
            {
                this.SEND_SMS_FLAGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> SEQ_NO
        {
            get
            {
                return this.SEQ_NOField;
            }
            set
            {
                this.SEQ_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SERVICE_CODE
        {
            get
            {
                return this.SERVICE_CODEField;
            }
            set
            {
                this.SERVICE_CODEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SO_PHIEU
        {
            get
            {
                return this.SO_PHIEUField;
            }
            set
            {
                this.SO_PHIEUField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SPECIALIZED_TRANS
        {
            get
            {
                return this.SPECIALIZED_TRANSField;
            }
            set
            {
                this.SPECIALIZED_TRANSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SPECIAL_HDL
        {
            get
            {
                return this.SPECIAL_HDLField;
            }
            set
            {
                this.SPECIAL_HDLField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SPECIFY_ARISE
        {
            get
            {
                return this.SPECIFY_ARISEField;
            }
            set
            {
                this.SPECIFY_ARISEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string STATUS
        {
            get
            {
                return this.STATUSField;
            }
            set
            {
                this.STATUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> STOPPED_FLG
        {
            get
            {
                return this.STOPPED_FLGField;
            }
            set
            {
                this.STOPPED_FLGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TEMP
        {
            get
            {
                return this.TEMPField;
            }
            set
            {
                this.TEMPField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TEMP_UNIT
        {
            get
            {
                return this.TEMP_UNITField;
            }
            set
            {
                this.TEMP_UNITField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TRUCK_NO
        {
            get
            {
                return this.TRUCK_NOField;
            }
            set
            {
                this.TRUCK_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UNNO
        {
            get
            {
                return this.UNNOField;
            }
            set
            {
                this.UNNOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> UPDATED_BY
        {
            get
            {
                return this.UPDATED_BYField;
            }
            set
            {
                this.UPDATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UPDATED_DATE
        {
            get
            {
                return this.UPDATED_DATEField;
            }
            set
            {
                this.UPDATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VENT
        {
            get
            {
                return this.VENTField;
            }
            set
            {
                this.VENTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VENT_UNIT
        {
            get
            {
                return this.VENT_UNITField;
            }
            set
            {
                this.VENT_UNITField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VESSEL_CODE
        {
            get
            {
                return this.VESSEL_CODEField;
            }
            set
            {
                this.VESSEL_CODEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VESSEL_NAME
        {
            get
            {
                return this.VESSEL_NAMEField;
            }
            set
            {
                this.VESSEL_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VES_ID
        {
            get
            {
                return this.VES_IDField;
            }
            set
            {
                this.VES_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int WEIGHT_CERTIFIED
        {
            get
            {
                return this.WEIGHT_CERTIFIEDField;
            }
            set
            {
                this.WEIGHT_CERTIFIEDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string YARD_CONSOL_STATUS
        {
            get
            {
                return this.YARD_CONSOL_STATUSField;
            }
            set
            {
                this.YARD_CONSOL_STATUSField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="TRANSACTION_DTO", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity.DTO")]
    public partial class TRANSACTION_DTO : object
    {
        
        private System.Nullable<decimal> BILLED_AMOUNTField;
        
        private AnonymousGateway.CHARGE_DTO[] CHARGEField;
        
        private System.DateTime CREATED_DATEField;
        
        private System.Nullable<decimal> CUSTOMER_AMOUNT_PAIDField;
        
        private bool IsValidRefundField;
        
        private AnonymousGateway.USER_DTO PAID_BYField;
        
        private System.Nullable<int> PAID_BY_USERField;
        
        private string PARTNER_IDField;
        
        private System.Nullable<int> PAYMENT_BYField;
        
        private string PAYMENT_METHODField;
        
        private string PAYMENT_REF_NOField;
        
        private string PAYMENT_STATUSField;
        
        private System.Nullable<System.DateTime> PROCESSED_DATEField;
        
        private System.Nullable<decimal> REFUND_AMOUNTField;
        
        private System.Nullable<int> REFUND_BYField;
        
        private System.Nullable<System.DateTime> REFUND_DATEField;
        
        private string REFUND_FOR_TRANSACTIONField;
        
        private string REFUND_REF_NOField;
        
        private string REFUND_STATUSField;
        
        private string REMARKField;
        
        private System.Nullable<System.DateTime> SEND_DATEField;
        
        private string SITE_IDField;
        
        private string TRANSACTION_CODEField;
        
        private int TRANSACTION_IDField;
        
        private System.Nullable<System.DateTime> UPDATED_DATEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> BILLED_AMOUNT
        {
            get
            {
                return this.BILLED_AMOUNTField;
            }
            set
            {
                this.BILLED_AMOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.CHARGE_DTO[] CHARGE
        {
            get
            {
                return this.CHARGEField;
            }
            set
            {
                this.CHARGEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> CUSTOMER_AMOUNT_PAID
        {
            get
            {
                return this.CUSTOMER_AMOUNT_PAIDField;
            }
            set
            {
                this.CUSTOMER_AMOUNT_PAIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsValidRefund
        {
            get
            {
                return this.IsValidRefundField;
            }
            set
            {
                this.IsValidRefundField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.USER_DTO PAID_BY
        {
            get
            {
                return this.PAID_BYField;
            }
            set
            {
                this.PAID_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> PAID_BY_USER
        {
            get
            {
                return this.PAID_BY_USERField;
            }
            set
            {
                this.PAID_BY_USERField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PARTNER_ID
        {
            get
            {
                return this.PARTNER_IDField;
            }
            set
            {
                this.PARTNER_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> PAYMENT_BY
        {
            get
            {
                return this.PAYMENT_BYField;
            }
            set
            {
                this.PAYMENT_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PAYMENT_METHOD
        {
            get
            {
                return this.PAYMENT_METHODField;
            }
            set
            {
                this.PAYMENT_METHODField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PAYMENT_REF_NO
        {
            get
            {
                return this.PAYMENT_REF_NOField;
            }
            set
            {
                this.PAYMENT_REF_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PAYMENT_STATUS
        {
            get
            {
                return this.PAYMENT_STATUSField;
            }
            set
            {
                this.PAYMENT_STATUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> PROCESSED_DATE
        {
            get
            {
                return this.PROCESSED_DATEField;
            }
            set
            {
                this.PROCESSED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> REFUND_AMOUNT
        {
            get
            {
                return this.REFUND_AMOUNTField;
            }
            set
            {
                this.REFUND_AMOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> REFUND_BY
        {
            get
            {
                return this.REFUND_BYField;
            }
            set
            {
                this.REFUND_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> REFUND_DATE
        {
            get
            {
                return this.REFUND_DATEField;
            }
            set
            {
                this.REFUND_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string REFUND_FOR_TRANSACTION
        {
            get
            {
                return this.REFUND_FOR_TRANSACTIONField;
            }
            set
            {
                this.REFUND_FOR_TRANSACTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string REFUND_REF_NO
        {
            get
            {
                return this.REFUND_REF_NOField;
            }
            set
            {
                this.REFUND_REF_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string REFUND_STATUS
        {
            get
            {
                return this.REFUND_STATUSField;
            }
            set
            {
                this.REFUND_STATUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string REMARK
        {
            get
            {
                return this.REMARKField;
            }
            set
            {
                this.REMARKField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SEND_DATE
        {
            get
            {
                return this.SEND_DATEField;
            }
            set
            {
                this.SEND_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_ID
        {
            get
            {
                return this.SITE_IDField;
            }
            set
            {
                this.SITE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TRANSACTION_CODE
        {
            get
            {
                return this.TRANSACTION_CODEField;
            }
            set
            {
                this.TRANSACTION_CODEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TRANSACTION_ID
        {
            get
            {
                return this.TRANSACTION_IDField;
            }
            set
            {
                this.TRANSACTION_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UPDATED_DATE
        {
            get
            {
                return this.UPDATED_DATEField;
            }
            set
            {
                this.UPDATED_DATEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="USER_DTO", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity.DTO")]
    public partial class USER_DTO : object
    {
        
        private System.Nullable<int> ACCOUNT_TYPEField;
        
        private string ADDRESSField;
        
        private System.Nullable<int> AGENT_IDField;
        
        private System.Nullable<int> CREATED_BYField;
        
        private System.Nullable<System.DateTime> CREATED_DATEField;
        
        private string DEPOTField;
        
        private string DESCRIPTIONField;
        
        private bool DISCOUNT_ON_INVOICEField;
        
        private string DOMAIN_IP_ADDRESSField;
        
        private string DOMAIN_NAMEField;
        
        private string EMAILField;
        
        private string EPORT_POST_PAID_FLAGField;
        
        private System.Nullable<bool> FULL_INFO_REQField;
        
        private string FULL_NAMEField;
        
        private System.Nullable<bool> IS_APPROVEDField;
        
        private System.Nullable<bool> IS_CONTRACT_ON_CONTAINER_SERVICEField;
        
        private System.Nullable<bool> IS_CONTRACT_ON_EMPTY_CONTAINER_DELIVERYField;
        
        private System.Nullable<bool> IS_CONTRACT_ON_FULL_CONTAINER_DELIVERYField;
        
        private bool IS_MASTER_ACCOUNTField;
        
        private System.Nullable<System.DateTime> LAST_CHANGE_PWD_TSField;
        
        private System.Nullable<System.DateTime> LAST_LOGON_TSField;
        
        private System.Nullable<int> MASTER_ACCOUNTField;
        
        private string MASTER_ACCOUNT1Field;
        
        private string PASSWORDField;
        
        private string PHONE_NOField;
        
        private string POST_PAID_FLGField;
        
        private string SITE_CODEField;
        
        private bool STATUSField;
        
        private string TAX_FILE_NOField;
        
        private System.Nullable<int> UPDATED_BYField;
        
        private System.Nullable<System.DateTime> UPDATED_DATEField;
        
        private int USER_IDField;
        
        private string USER_NAMEField;
        
        private string USER_TYPEField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> ACCOUNT_TYPE
        {
            get
            {
                return this.ACCOUNT_TYPEField;
            }
            set
            {
                this.ACCOUNT_TYPEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ADDRESS
        {
            get
            {
                return this.ADDRESSField;
            }
            set
            {
                this.ADDRESSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> AGENT_ID
        {
            get
            {
                return this.AGENT_IDField;
            }
            set
            {
                this.AGENT_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> CREATED_BY
        {
            get
            {
                return this.CREATED_BYField;
            }
            set
            {
                this.CREATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> CREATED_DATE
        {
            get
            {
                return this.CREATED_DATEField;
            }
            set
            {
                this.CREATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DEPOT
        {
            get
            {
                return this.DEPOTField;
            }
            set
            {
                this.DEPOTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DESCRIPTION
        {
            get
            {
                return this.DESCRIPTIONField;
            }
            set
            {
                this.DESCRIPTIONField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DISCOUNT_ON_INVOICE
        {
            get
            {
                return this.DISCOUNT_ON_INVOICEField;
            }
            set
            {
                this.DISCOUNT_ON_INVOICEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DOMAIN_IP_ADDRESS
        {
            get
            {
                return this.DOMAIN_IP_ADDRESSField;
            }
            set
            {
                this.DOMAIN_IP_ADDRESSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DOMAIN_NAME
        {
            get
            {
                return this.DOMAIN_NAMEField;
            }
            set
            {
                this.DOMAIN_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EMAIL
        {
            get
            {
                return this.EMAILField;
            }
            set
            {
                this.EMAILField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EPORT_POST_PAID_FLAG
        {
            get
            {
                return this.EPORT_POST_PAID_FLAGField;
            }
            set
            {
                this.EPORT_POST_PAID_FLAGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> FULL_INFO_REQ
        {
            get
            {
                return this.FULL_INFO_REQField;
            }
            set
            {
                this.FULL_INFO_REQField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FULL_NAME
        {
            get
            {
                return this.FULL_NAMEField;
            }
            set
            {
                this.FULL_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IS_APPROVED
        {
            get
            {
                return this.IS_APPROVEDField;
            }
            set
            {
                this.IS_APPROVEDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IS_CONTRACT_ON_CONTAINER_SERVICE
        {
            get
            {
                return this.IS_CONTRACT_ON_CONTAINER_SERVICEField;
            }
            set
            {
                this.IS_CONTRACT_ON_CONTAINER_SERVICEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IS_CONTRACT_ON_EMPTY_CONTAINER_DELIVERY
        {
            get
            {
                return this.IS_CONTRACT_ON_EMPTY_CONTAINER_DELIVERYField;
            }
            set
            {
                this.IS_CONTRACT_ON_EMPTY_CONTAINER_DELIVERYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IS_CONTRACT_ON_FULL_CONTAINER_DELIVERY
        {
            get
            {
                return this.IS_CONTRACT_ON_FULL_CONTAINER_DELIVERYField;
            }
            set
            {
                this.IS_CONTRACT_ON_FULL_CONTAINER_DELIVERYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IS_MASTER_ACCOUNT
        {
            get
            {
                return this.IS_MASTER_ACCOUNTField;
            }
            set
            {
                this.IS_MASTER_ACCOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> LAST_CHANGE_PWD_TS
        {
            get
            {
                return this.LAST_CHANGE_PWD_TSField;
            }
            set
            {
                this.LAST_CHANGE_PWD_TSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> LAST_LOGON_TS
        {
            get
            {
                return this.LAST_LOGON_TSField;
            }
            set
            {
                this.LAST_LOGON_TSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> MASTER_ACCOUNT
        {
            get
            {
                return this.MASTER_ACCOUNTField;
            }
            set
            {
                this.MASTER_ACCOUNTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MASTER_ACCOUNT1
        {
            get
            {
                return this.MASTER_ACCOUNT1Field;
            }
            set
            {
                this.MASTER_ACCOUNT1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PASSWORD
        {
            get
            {
                return this.PASSWORDField;
            }
            set
            {
                this.PASSWORDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PHONE_NO
        {
            get
            {
                return this.PHONE_NOField;
            }
            set
            {
                this.PHONE_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string POST_PAID_FLG
        {
            get
            {
                return this.POST_PAID_FLGField;
            }
            set
            {
                this.POST_PAID_FLGField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SITE_CODE
        {
            get
            {
                return this.SITE_CODEField;
            }
            set
            {
                this.SITE_CODEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool STATUS
        {
            get
            {
                return this.STATUSField;
            }
            set
            {
                this.STATUSField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TAX_FILE_NO
        {
            get
            {
                return this.TAX_FILE_NOField;
            }
            set
            {
                this.TAX_FILE_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> UPDATED_BY
        {
            get
            {
                return this.UPDATED_BYField;
            }
            set
            {
                this.UPDATED_BYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UPDATED_DATE
        {
            get
            {
                return this.UPDATED_DATEField;
            }
            set
            {
                this.UPDATED_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int USER_ID
        {
            get
            {
                return this.USER_IDField;
            }
            set
            {
                this.USER_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string USER_NAME
        {
            get
            {
                return this.USER_NAMEField;
            }
            set
            {
                this.USER_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string USER_TYPE
        {
            get
            {
                return this.USER_TYPEField;
            }
            set
            {
                this.USER_TYPEField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="INVOICE_CUSTOMER_DTO", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity.DTO")]
    public partial class INVOICE_CUSTOMER_DTO : object
    {
        
        private string CUST_REG_NOField;
        
        private string FULL_NAMEField;
        
        private System.Nullable<System.DateTime> INVOICE_DATEField;
        
        private string INVOICE_FKEYField;
        
        private int INVOICE_IDField;
        
        private string INVOICE_NOField;
        
        private string PATTERNField;
        
        private string SERIALField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CUST_REG_NO
        {
            get
            {
                return this.CUST_REG_NOField;
            }
            set
            {
                this.CUST_REG_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FULL_NAME
        {
            get
            {
                return this.FULL_NAMEField;
            }
            set
            {
                this.FULL_NAMEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> INVOICE_DATE
        {
            get
            {
                return this.INVOICE_DATEField;
            }
            set
            {
                this.INVOICE_DATEField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string INVOICE_FKEY
        {
            get
            {
                return this.INVOICE_FKEYField;
            }
            set
            {
                this.INVOICE_FKEYField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int INVOICE_ID
        {
            get
            {
                return this.INVOICE_IDField;
            }
            set
            {
                this.INVOICE_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string INVOICE_NO
        {
            get
            {
                return this.INVOICE_NOField;
            }
            set
            {
                this.INVOICE_NOField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PATTERN
        {
            get
            {
                return this.PATTERNField;
            }
            set
            {
                this.PATTERNField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SERIAL
        {
            get
            {
                return this.SERIALField;
            }
            set
            {
                this.SERIALField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PrintEirConfigDto", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity.DTO.Eir")]
    public partial class PrintEirConfigDto : object
    {
        
        private string ApiUrlField;
        
        private string PrintSingleField;
        
        private string PrintZipField;
        
        private string SiteField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ApiUrl
        {
            get
            {
                return this.ApiUrlField;
            }
            set
            {
                this.ApiUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PrintSingle
        {
            get
            {
                return this.PrintSingleField;
            }
            set
            {
                this.PrintSingleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PrintZip
        {
            get
            {
                return this.PrintZipField;
            }
            set
            {
                this.PrintZipField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Site
        {
            get
            {
                return this.SiteField;
            }
            set
            {
                this.SiteField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SiteFullNameDto", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Entity.DTO.Eir")]
    public partial class SiteFullNameDto : object
    {
        
        private string SiteFullNameField;
        
        private string SiteIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiteFullName
        {
            get
            {
                return this.SiteFullNameField;
            }
            set
            {
                this.SiteFullNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiteId
        {
            get
            {
                return this.SiteIdField;
            }
            set
            {
                this.SiteIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Request", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Common.SnpSign")]
    public partial class Request : object
    {
        
        private string AccessTokenField;
        
        private int AlignmentField;
        
        private string AppNameField;
        
        private string CaPathField;
        
        private byte[] DataField;
        
        private string DataTypeField;
        
        private string EirField;
        
        private float ImageScaleField;
        
        private string LocationField;
        
        private int PageField;
        
        private string PasswordField;
        
        private AnonymousGateway.Position PositionField;
        
        private string ReasonField;
        
        private System.DateTime SignDateField;
        
        private bool VisibleImageField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AccessToken
        {
            get
            {
                return this.AccessTokenField;
            }
            set
            {
                this.AccessTokenField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Alignment
        {
            get
            {
                return this.AlignmentField;
            }
            set
            {
                this.AlignmentField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AppName
        {
            get
            {
                return this.AppNameField;
            }
            set
            {
                this.AppNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CaPath
        {
            get
            {
                return this.CaPathField;
            }
            set
            {
                this.CaPathField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte[] Data
        {
            get
            {
                return this.DataField;
            }
            set
            {
                this.DataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DataType
        {
            get
            {
                return this.DataTypeField;
            }
            set
            {
                this.DataTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Eir
        {
            get
            {
                return this.EirField;
            }
            set
            {
                this.EirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public float ImageScale
        {
            get
            {
                return this.ImageScaleField;
            }
            set
            {
                this.ImageScaleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Location
        {
            get
            {
                return this.LocationField;
            }
            set
            {
                this.LocationField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Page
        {
            get
            {
                return this.PageField;
            }
            set
            {
                this.PageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Password
        {
            get
            {
                return this.PasswordField;
            }
            set
            {
                this.PasswordField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public AnonymousGateway.Position Position
        {
            get
            {
                return this.PositionField;
            }
            set
            {
                this.PositionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Reason
        {
            get
            {
                return this.ReasonField;
            }
            set
            {
                this.ReasonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime SignDate
        {
            get
            {
                return this.SignDateField;
            }
            set
            {
                this.SignDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool VisibleImage
        {
            get
            {
                return this.VisibleImageField;
            }
            set
            {
                this.VisibleImageField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Position", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Common.SnpSign")]
    public partial class Position : object
    {
        
        private int HeightField;
        
        private int WidthField;
        
        private int XField;
        
        private int YField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Height
        {
            get
            {
                return this.HeightField;
            }
            set
            {
                this.HeightField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Width
        {
            get
            {
                return this.WidthField;
            }
            set
            {
                this.WidthField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int X
        {
            get
            {
                return this.XField;
            }
            set
            {
                this.XField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Y
        {
            get
            {
                return this.YField;
            }
            set
            {
                this.YField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Response", Namespace="http://schemas.datacontract.org/2004/07/Snp.ePort.Common.SnpSign")]
    public partial class Response : object
    {
        
        private byte[] DataField;
        
        private string MessageField;
        
        private int ReturnCodeField;
        
        private string TransactionIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte[] Data
        {
            get
            {
                return this.DataField;
            }
            set
            {
                this.DataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Message
        {
            get
            {
                return this.MessageField;
            }
            set
            {
                this.MessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ReturnCode
        {
            get
            {
                return this.ReturnCodeField;
            }
            set
            {
                this.ReturnCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TransactionId
        {
            get
            {
                return this.TransactionIdField;
            }
            set
            {
                this.TransactionIdField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="AnonymousGateway.IAnonymousGateway")]
    public interface IAnonymousGateway
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetSiteActive", ReplyAction="http://tempuri.org/IAnonymousGateway/GetSiteActiveResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.AM_SITE[]> GetSiteActiveAsync(bool active, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetSiteById", ReplyAction="http://tempuri.org/IAnonymousGateway/GetSiteByIdResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.AM_SITE> GetSiteByIdAsync(string siteId, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetCfgSiteParamsByParamName", ReplyAction="http://tempuri.org/IAnonymousGateway/GetCfgSiteParamsByParamNameResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.CFG_SITE_PARAMS> GetCfgSiteParamsByParamNameAsync(string siteId, string featureId, string parnerId, string paramName, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/FindCont", ReplyAction="http://tempuri.org/IAnonymousGateway/FindContResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.VE_CONTAINER_INFO[]> FindContAsync(string siteId, System.DateTime pdFrom, System.DateTime pdTo, string pContainer, string pLastMoveFlg, string pBatch, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetSysCodes", ReplyAction="http://tempuri.org/IAnonymousGateway/GetSysCodesResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.SYS_CODES[]> GetSysCodesAsync(string siteId, string codeTp, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetEirsByDateAndItemNo", ReplyAction="http://tempuri.org/IAnonymousGateway/GetEirsByDateAndItemNoResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.EirDetail[]> GetEirsByDateAndItemNoAsync(string siteId, System.DateTime fromDate, System.DateTime toDate, string itemNo, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetEirsByLineBillBook", ReplyAction="http://tempuri.org/IAnonymousGateway/GetEirsByLineBillBookResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.EirDetail[]> GetEirsByLineBillBookAsync(string siteId, string liner, string bill, string book, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetEirsByIdsAndKeyIntfs", ReplyAction="http://tempuri.org/IAnonymousGateway/GetEirsByIdsAndKeyIntfsResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.EirDetail[]> GetEirsByIdsAndKeyIntfsAsync(string siteId, string[] eirIds, string[] keyIntfs, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetEirDetailsByIds", ReplyAction="http://tempuri.org/IAnonymousGateway/GetEirDetailsByIdsResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.EirDetail[]> GetEirDetailsByIdsAsync(string siteId, string[] eirIds, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/FindVessel", ReplyAction="http://tempuri.org/IAnonymousGateway/FindVesselResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.VE_VESSEL_INFO[]> FindVesselAsync(string siteId, string psVessel, System.DateTime pdFrom, System.DateTime pdTo, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetPatternBySiteID", ReplyAction="http://tempuri.org/IAnonymousGateway/GetPatternBySiteIDResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.AM_INVOICE_PATTERN[]> GetPatternBySiteIDAsync(string siteId, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetSerialBySiteIDAndPattern", ReplyAction="http://tempuri.org/IAnonymousGateway/GetSerialBySiteIDAndPatternResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.AM_INVOICE_SERIAL[]> GetSerialBySiteIDAndPatternAsync(string siteId, int patternId, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/SearchForDownloadInvoices", ReplyAction="http://tempuri.org/IAnonymousGateway/SearchForDownloadInvoicesResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.INVOICE_DTO[]> SearchForDownloadInvoicesAsync(string siteId, AnonymousGateway.DownloadInvoiceSearchModel searchKeys, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/DownloadEInvoices", ReplyAction="http://tempuri.org/IAnonymousGateway/DownloadEInvoicesResponse")]
        System.Threading.Tasks.Task<byte[]> DownloadEInvoicesAsync(string siteId, string eInvoiceFkey, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/DownloadEInvoicesInv", ReplyAction="http://tempuri.org/IAnonymousGateway/DownloadEInvoicesInvResponse")]
        System.Threading.Tasks.Task<byte[]> DownloadEInvoicesInvAsync(string siteId, string pattern, string serie, string invoiceNo, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetInvoiceByFKey", ReplyAction="http://tempuri.org/IAnonymousGateway/GetInvoiceByFKeyResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.INVOICE_DTO> GetInvoiceByFKeyAsync(string siteId, string fKey, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetPatternById", ReplyAction="http://tempuri.org/IAnonymousGateway/GetPatternByIdResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.AM_INVOICE_PATTERN> GetPatternByIdAsync(string siteId, int id, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetInvoicesByOrderDetailNo", ReplyAction="http://tempuri.org/IAnonymousGateway/GetInvoicesByOrderDetailNoResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.INVOICE_DTO[]> GetInvoicesByOrderDetailNoAsync(string siteId, string orderDetailNo, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetEInvoiceListBySerialAndPatternAndInvNo", ReplyAction="http://tempuri.org/IAnonymousGateway/GetEInvoiceListBySerialAndPatternAndInvNoRes" +
            "ponse")]
        System.Threading.Tasks.Task<AnonymousGateway.INVOICE_DTO[]> GetEInvoiceListBySerialAndPatternAndInvNoAsync(string siteId, string[] eInvoiceNoList, string invoiceType, string serial, string pattern, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetSloganBySite", ReplyAction="http://tempuri.org/IAnonymousGateway/GetSloganBySiteResponse")]
        System.Threading.Tasks.Task<string> GetSloganBySiteAsync(string siteId, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/SearchInvoicesCustomer", ReplyAction="http://tempuri.org/IAnonymousGateway/SearchInvoicesCustomerResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.INVOICE_CUSTOMER_DTO[]> SearchInvoicesCustomerAsync(string siteId, string invoicesNo, string serial, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/ConvertForVerify", ReplyAction="http://tempuri.org/IAnonymousGateway/ConvertForVerifyResponse")]
        System.Threading.Tasks.Task<string> ConvertForVerifyAsync(string siteId, string token, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetPrintEirConfig", ReplyAction="http://tempuri.org/IAnonymousGateway/GetPrintEirConfigResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.PrintEirConfigDto> GetPrintEirConfigAsync(string siteId, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/GetSiteFullNameConfig", ReplyAction="http://tempuri.org/IAnonymousGateway/GetSiteFullNameConfigResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.SiteFullNameDto> GetSiteFullNameConfigAsync(string siteId, string wsUsername, string wsPassword, string wsSecureKey);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAnonymousGateway/ESignPdf", ReplyAction="http://tempuri.org/IAnonymousGateway/ESignPdfResponse")]
        System.Threading.Tasks.Task<AnonymousGateway.Response> ESignPdfAsync(string siteId, AnonymousGateway.Request request, string wsUsername, string wsPassword, string wsSecureKey);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    public interface IAnonymousGatewayChannel : AnonymousGateway.IAnonymousGateway, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    public partial class AnonymousGatewayClient : System.ServiceModel.ClientBase<AnonymousGateway.IAnonymousGateway>, AnonymousGateway.IAnonymousGateway
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public AnonymousGatewayClient() : 
                base(AnonymousGatewayClient.GetDefaultBinding(), AnonymousGatewayClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.BasicHttpBinding_IAnonymousGateway.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public AnonymousGatewayClient(EndpointConfiguration endpointConfiguration) : 
                base(AnonymousGatewayClient.GetBindingForEndpoint(endpointConfiguration), AnonymousGatewayClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public AnonymousGatewayClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(AnonymousGatewayClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public AnonymousGatewayClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(AnonymousGatewayClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public AnonymousGatewayClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.AM_SITE[]> GetSiteActiveAsync(bool active, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetSiteActiveAsync(active, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.AM_SITE> GetSiteByIdAsync(string siteId, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetSiteByIdAsync(siteId, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.CFG_SITE_PARAMS> GetCfgSiteParamsByParamNameAsync(string siteId, string featureId, string parnerId, string paramName, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetCfgSiteParamsByParamNameAsync(siteId, featureId, parnerId, paramName, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.VE_CONTAINER_INFO[]> FindContAsync(string siteId, System.DateTime pdFrom, System.DateTime pdTo, string pContainer, string pLastMoveFlg, string pBatch, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.FindContAsync(siteId, pdFrom, pdTo, pContainer, pLastMoveFlg, pBatch, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.SYS_CODES[]> GetSysCodesAsync(string siteId, string codeTp, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetSysCodesAsync(siteId, codeTp, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.EirDetail[]> GetEirsByDateAndItemNoAsync(string siteId, System.DateTime fromDate, System.DateTime toDate, string itemNo, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetEirsByDateAndItemNoAsync(siteId, fromDate, toDate, itemNo, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.EirDetail[]> GetEirsByLineBillBookAsync(string siteId, string liner, string bill, string book, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetEirsByLineBillBookAsync(siteId, liner, bill, book, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.EirDetail[]> GetEirsByIdsAndKeyIntfsAsync(string siteId, string[] eirIds, string[] keyIntfs, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetEirsByIdsAndKeyIntfsAsync(siteId, eirIds, keyIntfs, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.EirDetail[]> GetEirDetailsByIdsAsync(string siteId, string[] eirIds, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetEirDetailsByIdsAsync(siteId, eirIds, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.VE_VESSEL_INFO[]> FindVesselAsync(string siteId, string psVessel, System.DateTime pdFrom, System.DateTime pdTo, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.FindVesselAsync(siteId, psVessel, pdFrom, pdTo, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.AM_INVOICE_PATTERN[]> GetPatternBySiteIDAsync(string siteId, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetPatternBySiteIDAsync(siteId, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.AM_INVOICE_SERIAL[]> GetSerialBySiteIDAndPatternAsync(string siteId, int patternId, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetSerialBySiteIDAndPatternAsync(siteId, patternId, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.INVOICE_DTO[]> SearchForDownloadInvoicesAsync(string siteId, AnonymousGateway.DownloadInvoiceSearchModel searchKeys, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.SearchForDownloadInvoicesAsync(siteId, searchKeys, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<byte[]> DownloadEInvoicesAsync(string siteId, string eInvoiceFkey, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.DownloadEInvoicesAsync(siteId, eInvoiceFkey, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<byte[]> DownloadEInvoicesInvAsync(string siteId, string pattern, string serie, string invoiceNo, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.DownloadEInvoicesInvAsync(siteId, pattern, serie, invoiceNo, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.INVOICE_DTO> GetInvoiceByFKeyAsync(string siteId, string fKey, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetInvoiceByFKeyAsync(siteId, fKey, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.AM_INVOICE_PATTERN> GetPatternByIdAsync(string siteId, int id, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetPatternByIdAsync(siteId, id, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.INVOICE_DTO[]> GetInvoicesByOrderDetailNoAsync(string siteId, string orderDetailNo, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetInvoicesByOrderDetailNoAsync(siteId, orderDetailNo, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.INVOICE_DTO[]> GetEInvoiceListBySerialAndPatternAndInvNoAsync(string siteId, string[] eInvoiceNoList, string invoiceType, string serial, string pattern, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetEInvoiceListBySerialAndPatternAndInvNoAsync(siteId, eInvoiceNoList, invoiceType, serial, pattern, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<string> GetSloganBySiteAsync(string siteId, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetSloganBySiteAsync(siteId, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.INVOICE_CUSTOMER_DTO[]> SearchInvoicesCustomerAsync(string siteId, string invoicesNo, string serial, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.SearchInvoicesCustomerAsync(siteId, invoicesNo, serial, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<string> ConvertForVerifyAsync(string siteId, string token, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.ConvertForVerifyAsync(siteId, token, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.PrintEirConfigDto> GetPrintEirConfigAsync(string siteId, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetPrintEirConfigAsync(siteId, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.SiteFullNameDto> GetSiteFullNameConfigAsync(string siteId, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.GetSiteFullNameConfigAsync(siteId, wsUsername, wsPassword, wsSecureKey);
        }
        
        public System.Threading.Tasks.Task<AnonymousGateway.Response> ESignPdfAsync(string siteId, AnonymousGateway.Request request, string wsUsername, string wsPassword, string wsSecureKey)
        {
            return base.Channel.ESignPdfAsync(siteId, request, wsUsername, wsPassword, wsSecureKey);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IAnonymousGateway))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IAnonymousGateway))
            {
                return new System.ServiceModel.EndpointAddress("http://localhost:54000/AnonymousGateway.svc");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return AnonymousGatewayClient.GetBindingForEndpoint(EndpointConfiguration.BasicHttpBinding_IAnonymousGateway);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return AnonymousGatewayClient.GetEndpointAddress(EndpointConfiguration.BasicHttpBinding_IAnonymousGateway);
        }
        
        public enum EndpointConfiguration
        {
            
            BasicHttpBinding_IAnonymousGateway,
        }
    }
}
