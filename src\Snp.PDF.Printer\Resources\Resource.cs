﻿using System;

namespace SNP.PDF.Printer.Resources
{
    /// <summary>
    ///     Resource
    ///     <para>ErrorCodes</para>
    ///     <para>==========Error format==========</para>
    ///     <para>1XX – Informational</para>
    ///     <para>2XX – Success</para>
    ///     <para>3XX – Redirection</para>
    ///     <para>4XX – Client Error</para>
    ///     <para>5XX – Server Error</para>
    ///     <para>================================</para>
    ///     <para>Thus, My error code will follow format y the same: [xxx][yyy]</para>
    /// </summary>
    public class Resource
    {
        private static readonly Lazy<Resource> _lazyInstance = new Lazy<Resource>(() => new Resource());

        private readonly string _ediSecureKey = string.Empty;

        #region Group 4xx[xxx]
        private Error _arrayIsNullOrEmpty; //400000
        private Error _logoCongTyUrlIsNullOrEmpty; //400100
        private Error _tenTauMaChuyenIsNullOrEmpty; //400110
        private Error _tongTlIsNullOrEmpty; //400120
        private Error _tenPhuongThucRutHangIsNullOrEmpty; //400130
        private Error _soBillOfLadingIsNullOrEmpty; //400140
        private Error _ghiChuViPhamTaiTrongIsNullOrEmpty; //400150
        private Error _soNgayMienIsNullOrEmpty; //400160

        private Error _tenCongTyIsNullOrEmpty; //400200
        private Error _cangDichIsNullOrEmpty; //400210
        private Error _soIMDGIsNullOrEmpty; //400220
        private Error _soHoaDonNoiBoIsNullOrEmpty; //400230
        private Error _soBookIsNullOrEmpty; //400240
        private Error _ghiChuStopCodeIsNullOrEmpty; //400250

        private Error _tenPhuongAnIsNullOrEmpty; //400300
        private Error _cangChuyenTaiIsNullOrEmpty; //400310
        private Error _oWoHoLIsNullOrEmpty; //400320
        private Error _nguoiYeuCauIsNullOrEmpty; //400330
        private Error _soUNNOIsNullOrEmpty; //400340
        private Error _ghiChuChiDinhIsNullOrEmpty; //400350

        private Error _soPhieuEIRIsNullOrEmpty; //400400
        private Error _soContIsNullOrEmpty; //400410
        private Error _maxGrosIsNullOrEmpty; //400420
        private Error _soDienThoaiIsNullOrEmpty; //400430
        private Error _nhietDoIsNullOrEmpty; //400440
        private Error _ghiChuThongTinContIsNullOrEmpty; //400450


        private Error _ngayTaoEIRIsNullOrEmpty; //400500
        private Error _isoIsNullOrEmpty; //400510
        private Error _vgmCustomerIsNullOrEmpty; //400520
        private Error _siteGiaoNhanIsNullOrEmpty; //400530
        private Error _thongGioIsNullOrEmpty; //400540
        private Error _soXeIsNullOrEmpty; //400550

        private Error _ngayTaoEIRInValid; //400600
        private Error _chuKtIsNullOrEmpty; //400610
        private Error _ghiChuHuHongIsNullOrEmpty; //400620
        private Error _sitePhieuEIRIsNullOrEmpty; //400630
        private Error _doAmIsNullOrEmpty; //400640
        private Error _soMoocIsNullOrEmpty; //400650

        private Error _ngayGioTaoEIRIsNullOrEmpty; //400700
        private Error _hangTauIsNullOrEmpty; //400710
        private Error _khuVucIsNullOrEmpty; //400720
        private Error _maVachUrlIsNullOrEmpty; //400730
        private Error _tlVoIsNullOrEmpty; //400740
        private Error _ghiChuHanLenhIsNullOrEmpty; //400750

        private Error _ngayGioTaoEIRInValid; //400800
        private Error _soSealIsNullOrEmpty; //400810
        private Error _trongLuongHangIsNullOrEmpty; //400820
        private Error _gateNoIsNullOrEmpty; //400830
        private Error _soDkEportIsNullOrEmpty; //400840
        private Error _ghiChuHanLenhInvalid; //400850

        private Error _tenKhachHangIsNullOrEmpty; //400900
        private Error _ttContIsNullOrEmpty; //400910
        private Error _tenDoiBocXepIsNullOrEmpty; //400920
        private Error _batNoIsNullOrEmpty; //400930
        private Error _ghiChuThongTinCanIsNullOrEmpty; //400940
        private Error _noiHaRongIsNullOrEmpty; //400950



        // Group error-code for v2
        private Error _objectNotTypeArray; //400111
        private Error _notGetValueFromPropertyName;//400112
        private Error _notFoundFileValidate;//400113
        private Error _notTryParseRequireType;//400114
        private Error _requireInvalid;//400115
        #endregion

        /**********************************************************/

        private Error _ediWrongSecureKey; //500100
        private Error _siteNotFound; //500200

        /// <summary>
        ///     C'tor
        /// </summary>
        public Resource()
        {
            Init();
        }

        /// <summary>
        ///     Instance resource
        /// </summary>
        public static Resource Instance
        {
            get { return _lazyInstance.Value; }
        }

        #region 400115: Require invalid
        public Error RequireInvalid { get {
                if (_requireInvalid is null)
                {
                    Init();
                }
                return _requireInvalid;
            } }
        #endregion


        #region 400114: Not try parse require type
        public Error NotTryParseRequireType
        {
            get {
                if (_notTryParseRequireType is null)
                {
                    Init();
                }
                return _notTryParseRequireType;
            }
        }
        #endregion

        #region 400113: Not found File Validate
        public Error NotFoundFileValidate
        {
            get {
                if (_notFoundFileValidate is null)
                {
                    Init();
                }
                return _notFoundFileValidate;
            }
        }
        #endregion

        #region 400112: Not get value from property name
        public Error NotGetValueFromPropertyName
        {
            get
            {
                if (_notGetValueFromPropertyName is null)
                {
                    Init();
                }
                return _notGetValueFromPropertyName;
            }
        }
        #endregion

        #region 40011: Object not type array
        public Error ObjectNotTypeArray
        {
            get
            {
                if (_objectNotTypeArray is null)
                {
                    Init();
                }
                return _objectNotTypeArray;
            }
        }
        #endregion

        #region 400000: Array Detail not null or empty

        /// <summary>
        ///     400100:ArrayDetail = null Or Empty
        /// </summary>
        public Error ArrayDetailIsNullOrEmpty
        {
            get
            {
                if (_arrayIsNullOrEmpty == null)
                {
                    Init();
                }
                return _arrayIsNullOrEmpty;
            }
        }

        #endregion

        #region 400100: LogoCongTyUrl not null or empty

        /// <summary>
        ///     400100:LogoCongTyUrl = null Or Empty
        /// </summary>
        public Error LogoCongTyUrlIsNullOrEmpty
        {
            get
            {
                if (_logoCongTyUrlIsNullOrEmpty == null)
                {
                    Init();
                }

                return _logoCongTyUrlIsNullOrEmpty;
            }
        }

        #endregion

        #region 400200: TenCongTy not null or empty

        /// <summary>
        ///     400200: TenCongTy not null or empty
        /// </summary>
        public Error TenCongTyIsNullOrEmpty
        {
            get
            {
                if (_tenCongTyIsNullOrEmpty == null)
                {
                    Init();
                }

                return _tenCongTyIsNullOrEmpty;
            }
        }

        #endregion

        #region 400300: TenPhuongAn not null or empty

        /// <summary>
        ///     400300: TenPhuongAn not null or empty
        /// </summary>
        public Error TenPhuongAnIsNullOrEmpty
        {
            get
            {
                if (_tenPhuongAnIsNullOrEmpty == null)
                {
                    Init();
                }

                return _tenPhuongAnIsNullOrEmpty;
            }
        }

        #endregion

        #region 400400: SoPhieuEIR not null or empty

        /// <summary>
        ///     400400: SoPhieuEIR not null or empty
        /// </summary>
        public Error SoPhieuEirIsNullOrEmpty
        {
            get
            {
                if (_soPhieuEIRIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soPhieuEIRIsNullOrEmpty;
            }
        }

        #endregion

        #region 400500: NgayTaoEIR not null or empty

        /// <summary>
        ///     400500: NgayTaoEIR not null or empty
        /// </summary>
        public Error NgayTaoEIRIsNullOrEmpty
        {
            get
            {
                if (_ngayTaoEIRIsNullOrEmpty == null)
                {
                    Init();
                }

                return _ngayTaoEIRIsNullOrEmpty;
            }
        }

        #endregion

        #region 400600: NgayTaoEIR invalid

        /// <summary>
        ///     400600: NgayTaoEIR invalid
        /// </summary>
        public Error NgayTaoEIRInvalid
        {
            get
            {
                if (_ngayTaoEIRInValid == null)
                {
                    Init();
                }

                return _ngayTaoEIRInValid;
            }
        }

        #endregion

        #region 400700: NgayGioTaoEIR not null or empty

        /// <summary>
        ///     400700: NgayGioTaoEIR not null or empty
        /// </summary>
        public Error NgayGioTaoEIRIsNullOrEmpty
        {
            get
            {
                if (_ngayGioTaoEIRIsNullOrEmpty == null)
                {
                    Init();
                }

                return _ngayGioTaoEIRIsNullOrEmpty;
            }
        }

        #endregion

        #region 400800: NgayGioTaoEIR invalid

        /// <summary>
        ///     400800: NgayGioTaoEIR invalid
        /// </summary>
        public Error NgayGioTaoEIRInvalid
        {
            get
            {
                if (_ngayGioTaoEIRInValid == null)
                {
                    Init();
                }

                return _ngayGioTaoEIRInValid;
            }
        }

        #endregion

        #region 400900: TenKhachHang not null or empty

        /// <summary>
        ///     400900: TenKhachHang not null or empty
        /// </summary>
        public Error TenKhachHangIsNullOrEmpty
        {
            get
            {
                if (_tenKhachHangIsNullOrEmpty == null)
                {
                    Init();
                }

                return _tenKhachHangIsNullOrEmpty;
            }
        }

        #endregion

        #region 400110: TenTauMaChuyen not null or empty

        /// <summary>
        ///     400110:TenTauMaChuyen = null Or Empty
        /// </summary>
        public Error TenTauMaChuyenIsNullOrEmpty
        {
            get
            {
                if (_tenTauMaChuyenIsNullOrEmpty == null)
                {
                    Init();
                }

                return _tenTauMaChuyenIsNullOrEmpty;
            }
        }

        #endregion

        #region 400210: CangDich not null or empty

        /// <summary>
        ///     400210: CangDich not null or empty
        /// </summary>
        public Error CangDichIsNullOrEmpty
        {
            get
            {
                if (_cangDichIsNullOrEmpty == null)
                {
                    Init();
                }

                return _cangDichIsNullOrEmpty;
            }
        }

        #endregion

        #region 400310: CangChuyenTai not null or empty

        /// <summary>
        ///     400310: CangChuyenTai not null or empty
        /// </summary>
        public Error CangChuyenTaiIsNullOrEmpty
        {
            get
            {
                if (_cangChuyenTaiIsNullOrEmpty == null)
                {
                    Init();
                }

                return _cangChuyenTaiIsNullOrEmpty;
            }
        }

        #endregion

        #region 400410: SoCont not null or empty

        /// <summary>
        ///     400410: SoCont not null or empty
        /// </summary>
        public Error SoContIsNullOrEmpty
        {
            get
            {
                if (_soContIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soContIsNullOrEmpty;
            }
        }

        #endregion

        #region 400510: ISO not null or empty

        /// <summary>
        ///     400510: ISO not null or empty
        /// </summary>
        public Error ISOIsNullOrEmpty
        {
            get
            {
                if (_isoIsNullOrEmpty == null)
                {
                    Init();
                }

                return _isoIsNullOrEmpty;
            }
        }

        #endregion

        #region 400610: ChuKT not null or empty

        /// <summary>
        ///     400610: ChuKT not null or empty
        /// </summary>
        public Error ChuKtIsNullOrEmpty
        {
            get
            {
                if (_chuKtIsNullOrEmpty == null)
                {
                    Init();
                }

                return _chuKtIsNullOrEmpty;
            }
        }

        #endregion

        #region 400710: HangTau not null or empty

        /// <summary>
        ///     400710: HangTau not null or empty
        /// </summary>
        public Error HangTauIsNullOrEmpty
        {
            get
            {
                if (_hangTauIsNullOrEmpty == null)
                {
                    Init();
                }

                return _hangTauIsNullOrEmpty;
            }
        }

        #endregion

        #region 400810: SoSeal not null or empty

        /// <summary>
        ///     400810: SoSeal not null or empty
        /// </summary>
        public Error SoSealIsNullOrEmpty
        {
            get
            {
                if (_soSealIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soSealIsNullOrEmpty;
            }
        }

        #endregion

        #region 400910: TtCont not null or empty

        /// <summary>
        ///     400910: TtCont not null or empty
        /// </summary>
        public Error TtContIsNullOrEmpty
        {
            get
            {
                if (_ttContIsNullOrEmpty == null)
                {
                    Init();
                }

                return _ttContIsNullOrEmpty;
            }
        }

        #endregion

        #region 400120: TongTL not null or empty

        /// <summary>
        ///     400120:TongTrongLuong = null Or Empty
        /// </summary>
        public Error TongTlIsNullOrEmpty
        {
            get
            {
                if (_tongTlIsNullOrEmpty == null)
                {
                    Init();
                }

                return _tongTlIsNullOrEmpty;
            }
        }

        #endregion

        #region 400220: SoIMDG not null or empty

        /// <summary>
        ///     400220: SoIMDG not null or empty
        /// </summary>
        public Error SoIMDGIsNullOrEmpty
        {
            get
            {
                if (_soIMDGIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soIMDGIsNullOrEmpty;
            }
        }

        #endregion

        #region 400320: OwOhOl not null or empty

        /// <summary>
        ///     400320: OwOhOl not null or empty
        /// </summary>
        public Error OwOhOlIsNullOrEmpty
        {
            get
            {
                if (_oWoHoLIsNullOrEmpty == null)
                {
                    Init();
                }

                return _oWoHoLIsNullOrEmpty;
            }
        }

        #endregion

        #region 400420: MaxGross not null or empty

        /// <summary>
        ///     400420: MaxGross not null or empty
        /// </summary>
        public Error MaxGrossIsNullOrEmpty
        {
            get
            {
                if (_maxGrosIsNullOrEmpty == null)
                {
                    Init();
                }

                return _maxGrosIsNullOrEmpty;
            }
        }

        #endregion

        #region 400520: VgmCustomer not null or empty

        /// <summary>
        ///     400520: VgmCustomer not null or empty
        /// </summary>
        public Error VgmCustomerIsNullOrEmpty
        {
            get
            {
                if (_vgmCustomerIsNullOrEmpty == null)
                {
                    Init();
                }

                return _vgmCustomerIsNullOrEmpty;
            }
        }

        #endregion

        #region 400620: GhiChuHuHong not null or empty

        /// <summary>
        ///     400620: GhiChuHuHong not null or empty
        /// </summary>
        public Error GhiChuHuHongIsNullOrEmpty
        {
            get
            {
                if (_ghiChuHuHongIsNullOrEmpty == null)
                {
                    Init();
                }

                return _ghiChuHuHongIsNullOrEmpty;
            }
        }

        #endregion

        #region 400720: KhuVuc not null or empty

        /// <summary>
        ///     400720: KhuVuc not null or empty
        /// </summary>
        public Error KhuVucIsNullOrEmpty
        {
            get
            {
                if (_khuVucIsNullOrEmpty == null)
                {
                    Init();
                }

                return _khuVucIsNullOrEmpty;
            }
        }

        #endregion

        #region 400820: TrongLuongHang not null or empty

        /// <summary>
        ///     400820: TrongLuongHang not null or empty
        /// </summary>
        public Error TrongLuongHangIsNullOrEmpty
        {
            get
            {
                if (_trongLuongHangIsNullOrEmpty == null)
                {
                    Init();
                }

                return _trongLuongHangIsNullOrEmpty;
            }
        }

        #endregion

        #region 400920: TenDoiBocXep not null or empty

        /// <summary>
        ///     400920: TenDoiBocXep not null or empty
        /// </summary>
        public Error TenDoiBocXepIsNullOrEmpty
        {
            get
            {
                if (_tenDoiBocXepIsNullOrEmpty == null)
                {
                    Init();
                }

                return _tenDoiBocXepIsNullOrEmpty;
            }
        }

        #endregion

        #region 400130: TenPhuongThucRutHang not null or empty

        /// <summary>
        ///     400130:TenPhuongThucRutHang = null Or Empty
        /// </summary>
        public Error TenPhuongThucRutHangIsNullOrEmpty
        {
            get
            {
                if (_tenPhuongThucRutHangIsNullOrEmpty == null)
                {
                    Init();
                }

                return _tenPhuongThucRutHangIsNullOrEmpty;
            }
        }

        #endregion

        #region 400230: SoHoaDonNoiBo not null or empty

        /// <summary>
        ///     400230: SoHoaDonNoiBo not null or empty
        /// </summary>
        public Error SoHoaDonNoiBoIsNullOrEmpty
        {
            get
            {
                if (_soHoaDonNoiBoIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soHoaDonNoiBoIsNullOrEmpty;
            }
        }

        #endregion

        #region 400330: NguoiYeuCau not null or empty

        /// <summary>
        ///     400330: NguoiYeuCau not null or empty
        /// </summary>
        public Error NguoiYeuCauIsNullOrEmpty
        {
            get
            {
                if (_nguoiYeuCauIsNullOrEmpty == null)
                {
                    Init();
                }

                return _nguoiYeuCauIsNullOrEmpty;
            }
        }

        #endregion

        #region 400430: SoDienThoai not null or empty

        /// <summary>
        ///     400430: SoDienThoai not null or empty
        /// </summary>
        public Error SoDienThoaiIsNullOrEmpty
        {
            get
            {
                if (_soDienThoaiIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soDienThoaiIsNullOrEmpty;
            }
        }

        #endregion

        #region 400530: SiteGiaoNhan not null or empty

        /// <summary>
        ///     400530: SiteGiaoNhan not null or empty
        /// </summary>
        public Error SiteGiaoNhanIsNullOrEmpty
        {
            get
            {
                if (_siteGiaoNhanIsNullOrEmpty == null)
                {
                    Init();
                }

                return _siteGiaoNhanIsNullOrEmpty;
            }
        }

        #endregion

        #region 400630: SitePhieuEIR not null or empty

        /// <summary>
        ///     400630: SitePhieuEIR not null or empty
        /// </summary>
        public Error SitePhieuEIRIsNullOrEmpty
        {
            get
            {
                if (_sitePhieuEIRIsNullOrEmpty == null)
                {
                    Init();
                }

                return _sitePhieuEIRIsNullOrEmpty;
            }
        }

        #endregion

        #region 400730: MaVachUrl not null or empty

        /// <summary>
        ///     400730: MaVachUrl not null or empty
        /// </summary>
        public Error MaVachUrlIsNullOrEmpty
        {
            get
            {
                if (_maVachUrlIsNullOrEmpty == null)
                {
                    Init();
                }

                return _maVachUrlIsNullOrEmpty;
            }
        }

        #endregion

        #region 400830: GateNo not null or empty

        /// <summary>
        ///     400830: GateNo not null or empty
        /// </summary>
        public Error GateNoIsNullOrEmpty
        {
            get
            {
                if (_gateNoIsNullOrEmpty == null)
                {
                    Init();
                }

                return _gateNoIsNullOrEmpty;
            }
        }

        #endregion

        #region 400930: BatNo not null or empty

        /// <summary>
        ///     400930: BatNo not null or empty
        /// </summary>
        public Error BatNoIsNullOrEmpty
        {
            get
            {
                if (_batNoIsNullOrEmpty == null)
                {
                    Init();
                }

                return _batNoIsNullOrEmpty;
            }
        }

        #endregion

        #region 400140: SoBillOfLading not null or empty

        /// <summary>
        ///     400140: SoBillOfLading not null or empty
        /// </summary>
        public Error SoBillOfLadingIsNullOrEmpty
        {
            get
            {
                if (_soBillOfLadingIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soBillOfLadingIsNullOrEmpty;
            }
        }

        #endregion

        #region 400240: SoBook not null or empty

        /// <summary>
        ///     400240: SoBook not null or empty
        /// </summary>
        public Error SoBookIsNullOrEmpty
        {
            get
            {
                if (_soBookIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soBookIsNullOrEmpty;
            }
        }

        #endregion

        #region 400340: SoUNNO not null or empty

        /// <summary>
        ///     400340: SoUNNO not null or empty
        /// </summary>
        public Error SoUNNOIsNullOrEmpty
        {
            get
            {
                if (_soUNNOIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soUNNOIsNullOrEmpty;
            }
        }

        #endregion

        #region 400440: NhietDo not null or empty

        /// <summary>
        ///     400440: NhietDo not null or empty
        /// </summary>
        public Error NhietDoIsNullOrEmpty
        {
            get
            {
                if (_nhietDoIsNullOrEmpty == null)
                {
                    Init();
                }

                return _nhietDoIsNullOrEmpty;
            }
        }

        #endregion

        #region 400540: ThongGio not null or empty

        /// <summary>
        ///     400540: ThongGio not null or empty
        /// </summary>
        public Error ThongGioIsNullOrEmpty
        {
            get
            {
                if (_thongGioIsNullOrEmpty == null)
                {
                    Init();
                }

                return _thongGioIsNullOrEmpty;
            }
        }

        #endregion

        #region 400640: DoAm not null or empty

        /// <summary>
        ///     400640: DoAm not null or empty
        /// </summary>
        public Error DoAmIsNullOrEmpty
        {
            get
            {
                if (_doAmIsNullOrEmpty == null)
                {
                    Init();
                }

                return _doAmIsNullOrEmpty;
            }
        }

        #endregion

        #region 400740: TlVo not null or empty

        /// <summary>
        ///     400740: TlVo not null or empty
        /// </summary>
        public Error TlVoIsNullOrEmpty
        {
            get
            {
                if (_tlVoIsNullOrEmpty == null)
                {
                    Init();
                }

                return _tlVoIsNullOrEmpty;
            }
        }

        #endregion

        #region 400840: SoDkEport not null or empty

        /// <summary>
        ///     400840: SoDkEport not null or empty
        /// </summary>
        public Error SoDkEportIsNullOrEmpty
        {
            get
            {
                if (_soDkEportIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soDkEportIsNullOrEmpty;
            }
        }

        #endregion

        #region 400940: GhiChuThongTinCan not null or empty

        /// <summary>
        ///     400940: GhiChuThongTinCan not null or empty
        /// </summary>
        public Error GhiChuThongTinCanIsNullOrEmpty
        {
            get
            {
                if (_ghiChuThongTinCanIsNullOrEmpty == null)
                {
                    Init();
                }

                return _ghiChuThongTinCanIsNullOrEmpty;
            }
        }

        #endregion

        #region 400150: GhiChuViPhamTaiTrong not null or empty

        /// <summary>
        ///     400150: GhiChuViPhamTaiTrong not null or empty
        /// </summary>
        public Error GhiChuViPhamTaiTrongIsNullOrEmpty
        {
            get
            {
                if (_ghiChuViPhamTaiTrongIsNullOrEmpty == null)
                {
                    Init();
                }

                return _ghiChuViPhamTaiTrongIsNullOrEmpty;
            }
        }

        #endregion

        #region 400250: GhiChuStopCode not null or empty

        /// <summary>
        ///     400250: GhiChuStopCode not null or empty
        /// </summary>
        public Error GhiChuStopCodeIsNullOrEmpty
        {
            get
            {
                if (_ghiChuStopCodeIsNullOrEmpty == null)
                {
                    Init();
                }

                return _ghiChuStopCodeIsNullOrEmpty;
            }
        }

        #endregion

        #region 400350: GhiChuChiDinh not null or empty

        /// <summary>
        ///     400350: GhiChuChiDinh not null or empty
        /// </summary>
        public Error GhiChuChiDinhIsNullOrEmpty
        {
            get
            {
                if (_ghiChuChiDinhIsNullOrEmpty == null)
                {
                    Init();
                }

                return _ghiChuChiDinhIsNullOrEmpty;
            }
        }

        #endregion

        #region 400450: GhiChuThongTinCont not null or empty

        /// <summary>
        ///     400450: GhiChuThongTinCont not null or empty
        /// </summary>
        public Error GhiChuThongTinContIsNullOrEmpty
        {
            get
            {
                if (_ghiChuThongTinContIsNullOrEmpty == null)
                {
                    Init();
                }

                return _ghiChuThongTinContIsNullOrEmpty;
            }
        }

        #endregion

        #region 400550: SoXe not null or empty

        /// <summary>
        ///     400550: SoXe not null or empty
        /// </summary>
        public Error SoXeIsNullOrEmpty
        {
            get
            {
                if (_soXeIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soXeIsNullOrEmpty;
            }
        }

        #endregion

        #region 400650: SoMooc not null or empty

        /// <summary>
        ///     400650: SoMooc not null or empty
        /// </summary>
        public Error SoMoocIsNullOrEmpty
        {
            get
            {
                if (_soMoocIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soMoocIsNullOrEmpty;
            }
        }

        #endregion

        #region 400750: GhiChuHanLenh not null or empty

        /// <summary>
        ///     400750: GhiChuHanLenh not null or empty
        /// </summary>
        public Error GhiChuHanLenhIsNullOrEmpty
        {
            get
            {
                if (_ghiChuHanLenhIsNullOrEmpty == null)
                {
                    Init();
                }

                return _ghiChuHanLenhIsNullOrEmpty;
            }
        }

        #endregion

        #region 400850: GhiChuHanLenh invalid

        /// <summary>
        ///     400850: GhiChuHanLenh invalid
        /// </summary>
        public Error GhiChuHanLenhInvalid
        {
            get
            {
                if (_ghiChuHanLenhInvalid == null)
                {
                    Init();
                }

                return _ghiChuHanLenhInvalid;
            }
        }

        #endregion

        #region 400950: NoiHaRong not null or empty

        /// <summary>
        ///     400950: NoiHaRong not null or empty
        /// </summary>
        public Error NoiHaRongIsNullOrEmpty
        {
            get
            {
                if (_noiHaRongIsNullOrEmpty == null)
                {
                    Init();
                }

                return _noiHaRongIsNullOrEmpty;
            }
        }

        #endregion

        #region 400160: SoNgayMien not null or empty

        /// <summary>
        ///     400160: SoNgayMien not null or empty
        /// </summary>
        public Error SoNgayMienIsNullOrEmpty
        {
            get
            {
                if (_soNgayMienIsNullOrEmpty == null)
                {
                    Init();
                }

                return _soNgayMienIsNullOrEmpty;
            }
        }

        #endregion

        #region 500100: The Edi secure key is wrong

        /// <summary>
        ///     500100: The Edi secure key is wrong
        /// </summary>
        public Error EDIWrongSecureKey
        {
            get
            {
                if (_ediWrongSecureKey == null)
                {
                    Init();
                }

                return _ediWrongSecureKey;
            }
        }

        #endregion

        #region 500200: Site not found

        /// <summary>
        ///     500200: Site not found
        /// </summary>
        public Error SiteNotFound
        {
            get
            {
                if (_siteNotFound == null)
                {
                    Init();
                }

                return _siteNotFound;
            }
        }

        #endregion

        private void Init()
        {
            // error code for v2
            if (_objectNotTypeArray is null)
            {
                _objectNotTypeArray = new Error("400111", "Object not typeof array.");
            }
            if (_notGetValueFromPropertyName is null)
            {
                _notGetValueFromPropertyName = new Error("400112", "Error while try get value by property name. Not found property name.");
            }
            if (_notFoundFileValidate is null)
            {
                _notFoundFileValidate = new Error("400113","Not found file validate object by BillType.");
            }
            if (_notTryParseRequireType is null)
            {
                _notTryParseRequireType = new Error("400114", "No TryParse RequireType {0}.");
            }
            if(_requireInvalid is null)
            {
                _requireInvalid = new Error("400115","Property name {0} = {1} invalid. {2}");
            }
            // error code for v1
            #region 400000: ArrayDetail not null or empty

            if (_arrayIsNullOrEmpty == null)
            {
                _arrayIsNullOrEmpty = new Error("400000", "Chi tiết phiếu in không được để trống");
            }

            #endregion

            #region 400100: LogoCongTy not null or empty

            if (_logoCongTyUrlIsNullOrEmpty == null)
            {
                _logoCongTyUrlIsNullOrEmpty = new Error("400100", "Logo không tồn tại");
            }

            #endregion

            #region 400200: TenCongTy not null or empty

            if (_tenCongTyIsNullOrEmpty == null)
            {
                _tenCongTyIsNullOrEmpty = new Error("400200", "Tên công ty không được để trống, lấy theo bảng cấu hình thông tin Site (AM_SITE)");
            }

            #endregion

            #region 400300: TenPhuongAn not null or empty

            if (_tenPhuongAnIsNullOrEmpty == null)
            {
                _tenPhuongAnIsNullOrEmpty = new Error("400300", "Tên phương án không được để trống");
            }

            #endregion

            #region 400400: SoPhieuEIR not null or empty

            if (_soPhieuEIRIsNullOrEmpty == null)
            {
                _soPhieuEIRIsNullOrEmpty = new Error("400400", "Số phiếu EIR không được để trống, là Id của phiếu EIR được chọn, cột EIR_ID trong màn hình T61");
            }

            #endregion

            #region 400500: NgayTaoEIR not null or empty

            if (_ngayTaoEIRIsNullOrEmpty == null)
            {
                _ngayTaoEIRIsNullOrEmpty = new Error("400500", "Ngày tạo phiếu EIR không được để trống, là ngày tạo phiếu EIR, cột CreateDate trong màn hình T61");
            }

            #endregion

            #region 400600: NgayTaoEIR invalid

            if (_ngayTaoEIRInValid == null)
            {
                _ngayTaoEIRInValid = new Error("400600", "Ngày tạo phiếu EIR không đúng format, format: dd/MM/yyyy");
            }

            #endregion

            #region 400700: NgayGioTaoEIR not null or empty

            if (_ngayGioTaoEIRIsNullOrEmpty == null)
            {
                _ngayGioTaoEIRIsNullOrEmpty = new Error("400700", "Ngày tạo phiếu EIR không được để trống, là ngày tạo phiếu EIR, cột CreateDate trong màn hình T61");
            }

            #endregion

            #region 400800: NgayGioTaoEIR invalid

            if (_ngayGioTaoEIRInValid == null)
            {
                _ngayGioTaoEIRInValid = new Error("400800", "Ngày tạo phiếu EIR không đúng format, format: dd/MM/yyyy hh:mm:ss");
            }

            #endregion

            #region 400900: TenKhachHang not null or empty

            if (_tenKhachHangIsNullOrEmpty == null)
            {
                _tenKhachHangIsNullOrEmpty = new Error("400900", "Tên khách hàng không được để trống, là tên khách hàng tương ứng phiếu EIR được chọn, cột CustName trong màn hình T61");
            }

            #endregion

            #region 400110: TenTauMaChuyen not null or empty

            if (_tenTauMaChuyenIsNullOrEmpty == null)
            {
                _tenTauMaChuyenIsNullOrEmpty = new Error("400110", "Tên tàu và mã chuyến không được để trống, là tên tàu nhập/xuất + mã chuyến tương ứng phiếu EIR được chọn, format: tên tàu / mã chuyến");
            }

            #endregion

            #region 400210: CangDich not null or empty

            if (_cangDichIsNullOrEmpty == null)
            {
                _cangDichIsNullOrEmpty = new Error("400210", "Cảng đích không được để trống, là cảng đích tương ứng phiếu EIR được chọn, trường tàu nhập/xuất khẩu trong màn hình C01 tương ứng Item key");
            }

            #endregion

            #region 400310: CangChuyenTai not null or empty

            if (_cangChuyenTaiIsNullOrEmpty == null)
            {
                _cangChuyenTaiIsNullOrEmpty = new Error("400310", "Cảng chuyển tải không được để trống, là cảng chuyển tải tương ứng phiếu EIR được chọn");
            }

            #endregion

            #region 400410: SoCont not null or empty

            if (_soContIsNullOrEmpty == null)
            {
                _soContIsNullOrEmpty = new Error("400410", "Số Cont không được để trống, là số Cont trên phiếu EIR, cột CtnrNo trong màn hình T61");
            }

            #endregion

            #region 400510: ISO not null or empty

            if (_isoIsNullOrEmpty == null)
            {
                _isoIsNullOrEmpty = new Error("400510", "Số ISO không được để trống, là số ISO của Cont trên phiếu EIR, cột ISO trong màn hình T61");
            }

            #endregion

            #region 400610: ChuKT not null or empty

            if (_chuKtIsNullOrEmpty == null)
            {
                _chuKtIsNullOrEmpty = new Error("400610", "Chủ KT không được để trống, là chủ KT của Cont trên phiếu EIR, trường chủ KT trong màn hình C01 tương ứng Item key");
            }

            #endregion

            #region 400710: HangTau not null or empty

            if (_hangTauIsNullOrEmpty == null)
            {
                _hangTauIsNullOrEmpty = new Error("400710", "Hãng tàu không được để trống, là hãng tàu của Cont trên phiếu EIR");
            }

            #endregion

            #region 400810: SoSeal not null or empty

            if (_soSealIsNullOrEmpty == null)
            {
                _soSealIsNullOrEmpty = new Error("400810", "Số Seal không được để trống, là số seal trên phiếu đăng ký, trường số seal trong màn hình C01 tương ứng Item key");
            }

            #endregion

            #region 400910: TtCont not null or empty

            if (_ttContIsNullOrEmpty == null)
            {
                _ttContIsNullOrEmpty = new Error("400910", "Trạng thái Cont không được để trống, là trạng thái của Cont trên phiếu EIR, cột FEL trong màn hình T61");
            }

            #endregion

            #region 400120: TongTL not null or empty

            if (_tongTlIsNullOrEmpty == null)
            {
                _tongTlIsNullOrEmpty = new Error("400120", "Tổng trọng lượng không được để trống, là trọng lượng toàn bộ (hàng và vỏ) của cont trên phiếu EIR, trường trọng lượng tổng (ton) trong màn hình C01 tương ứng Item key");
            }

            #endregion

            #region 400220: SoIMDG not null or empty

            if (_soIMDGIsNullOrEmpty == null)
            {
                _soIMDGIsNullOrEmpty = new Error("400220", "Số IMDG không được để trống, là số IMDG của Cont trên phiếu EIR, trường IMDG class trong màn hình Cp2 tương ứng Item key, thể hiện tất cả IMDG cách nhau bằng dấu phẩy");
            }

            #endregion

            #region 400320: OwOhOl not null or empty

            if (_oWoHoLIsNullOrEmpty == null)
            {
                _oWoHoLIsNullOrEmpty = new Error("400320", "Thông tin quá khổ không được để trống, là thông tin quá khổ của Cont trên phiếu EIR, container quá khổ chiều cao thể hiện OH, chiều rộng OW, chiều dài OL");
            }

            #endregion

            #region 400420: MaxGross not null or empty

            if (_maxGrosIsNullOrEmpty == null)
            {
                _maxGrosIsNullOrEmpty = new Error("400420", "MaxGross không được để trống, là thông tin về trọng lượng MaxGross của cont trên phiếu EIR");
            }

            #endregion

            #region 400520: VgmCustomer not null or empty

            if (_vgmCustomerIsNullOrEmpty == null)
            {
                _vgmCustomerIsNullOrEmpty = new Error("400520", "VgmCustomer không được để trống, là thông tin về trọng lượng VGM của cont do khách hàng khai báo hoặc cảng nhập, trường VGM (ton) trong màn hình C01 tương ứng Item key, chỉ thể hiện số");
            }

            #endregion

            #region 400620: GhiChuHuHong not null or empty

            if (_ghiChuHuHongIsNullOrEmpty == null)
            {
                _ghiChuHuHongIsNullOrEmpty = new Error("400620", "Ghi chú hư hỏng không được để trống, thông tin hư hỏng của Container");
            }

            #endregion

            #region 400720: KhuVuc not null or empty

            if ((_khuVucIsNullOrEmpty) == null)
            {
                _khuVucIsNullOrEmpty = new Error("400720", "Khu vực không được để trống, trường khu vực trong màn hình C01 tương ứng Item key, chỉ có đối với các phương án đóng rút");
            }

            #endregion

            #region 400820: TrongLuongHang not null or empty

            if (_trongLuongHangIsNullOrEmpty == null)
            {
                _trongLuongHangIsNullOrEmpty = new Error("400820", "Trọng lượng hàng không được để trống, trường trọng lượng hàng (ton) trong màn hình C36/C37 tương ứng Item key, chỉ có đối với các phương án đóng rút");
            }

            #endregion

            #region 400920: TenDoiBocXep not null or empty

            if (_tenDoiBocXepIsNullOrEmpty == null)
            {
                _tenDoiBocXepIsNullOrEmpty = new Error("400920", "Tên đội bốc xếp không được để trống, là tên đội công nhân của cont trên phiếu EIR, trường đội CN màn hình C36/C37 (chỉ lấy tên)");
            }

            #endregion

            #region 400130: TenPhuongThucRutHang not null or empty

            if (_tenPhuongThucRutHangIsNullOrEmpty == null)
            {
                _tenPhuongThucRutHangIsNullOrEmpty = new Error("400130", "Tên phương thức rút hàng không được để trống, là tên phương thức đóng rút hàng của cont trên phiếu EIR, trường Phương thức Đ.hàng/ Phương thức R.hàng trên màn hình C36/C37 (chỉ lấy tên)");
            }

            #endregion

            #region 400230: SoHoaDonNoiBo not null or empty

            if (_soHoaDonNoiBoIsNullOrEmpty == null)
            {
                _soHoaDonNoiBoIsNullOrEmpty = new Error("400230", "Số hóa đơn nội bộ không được để trống, là số phiếu thu thuộc quyển hóa đơn nội bộ của cont trên phiếu EIR");
            }

            #endregion

            #region 400330: NguoiYeuCau not null or empty

            if (_nguoiYeuCauIsNullOrEmpty == null)
            {
                _nguoiYeuCauIsNullOrEmpty = new Error("400330", "Người yêu cầu không được để trống, là người yêu cầu đóng rút hàng cảu cont trên phiếu EIR");
            }

            #endregion

            #region 400430: SoDienThoai not null or empty

            if (_soDienThoaiIsNullOrEmpty == null)
            {
                _soDienThoaiIsNullOrEmpty = new Error("400430", "Số điện thoại không được để trống, là số điện thoại yêu cầu đóng rút hàng của cont trên phiếu EIR, trường Số ĐT màn hình CPF");
            }

            #endregion

            #region 400530: SiteGiaoNhan not null or empty

            if (_siteGiaoNhanIsNullOrEmpty == null)
            {
                _siteGiaoNhanIsNullOrEmpty = new Error("400530", "Site giao nhận không được để trống, là site giao nhận với site phiếu EIR được chọn SITE, lấy theo bảng cấu hình thông tin Site (AM_SITE)");
            }

            #endregion

            #region 400630: SitePhieuEIR not null or empty

            if (_sitePhieuEIRIsNullOrEmpty == null)
            {
                _sitePhieuEIRIsNullOrEmpty = new Error("400630", "Site phiếu EIR không được để trống, là site phiếu EIR được chọn Site, lấy theo bảng cấu hình thông tin Site (AM_SITE)");
            }

            #endregion

            #region 400730: MaVachUrl not null or empty

            if (_maVachUrlIsNullOrEmpty == null)
            {
                _maVachUrlIsNullOrEmpty = new Error("400730", "Mã vạch url không được để trống, url mã vạch barcode");
            }

            #endregion

            #region 400830: GateNo not null or empty

            if (_gateNoIsNullOrEmpty == null)
            {
                _gateNoIsNullOrEmpty = new Error("400830", "GateNo không được để trống, mã cổng giao nhận container, cột gateno trong màn hình T61");
            }

            #endregion

            #region 400930: BatNo not null or empty

            if (_batNoIsNullOrEmpty == null)
            {
                _batNoIsNullOrEmpty = new Error("400930", "BatNo không được để trống, số bat giao nhận trong container, trường số BAT trong màn hình T01");
            }

            #endregion

            #region 400140: SoBillOfLading not null or empty

            if (_soBillOfLadingIsNullOrEmpty == null)
            {
                _soBillOfLadingIsNullOrEmpty = new Error("400140", "Số bill of lading không được để trống, là số bill tương ứng phiếu EIR được chọn, trường B/L trong màn hình C01 tương ứng Item key");
            }

            #endregion

            #region 400240: SoBook not null or empty

            if (_soBookIsNullOrEmpty == null)
            {
                _soBookIsNullOrEmpty = new Error("400240", "Số book không được để trống, là số book tương ứng phiếu EIR được chọn, trường số book trong màn hình C01 tương ứng Item key");
            }

            #endregion

            #region 400340: SoUNNO not null or empty

            if (_soUNNOIsNullOrEmpty == null)
            {
                _soUNNOIsNullOrEmpty = new Error("400340", "Số UNNO không được để trống, là số UNNO của Cont trên phiếu EIR, trường UN trong màn hình CP2 tương ứng Item key, thể hiện tất cả cách nhau bằng dấu phẩy");
            }

            #endregion

            #region 400440: NhietDo not null or empty

            if (_nhietDoIsNullOrEmpty == null)
            {
                _nhietDoIsNullOrEmpty = new Error("400440", "Nhiệt độ không được để trống, là thông tin về nhiệt độ của Cont trên phiếu EIR, trường nhiệt độ thực tế trong màn hình CP2 tương ứng Item key");
            }

            #endregion

            #region 400540: ThongGio not null or empty

            if (_thongGioIsNullOrEmpty == null)
            {
                _thongGioIsNullOrEmpty = new Error("400540", "Thông gió không được để trống, là thông tin về thông gió của Cont trên phiếu EIR, trường thông gió trong màn hình CP2 tương ứng Item key");
            }

            #endregion

            #region 400640: DoAm not null or empty

            if (_doAmIsNullOrEmpty == null)
            {
                _doAmIsNullOrEmpty = new Error("400640", "Độ ẩm không được để trống, là thông tin về độ ẩm của Cont trên phiếu EIR, trường độ ẩm trong màn hình CP2 tương ứng Item key");
            }

            #endregion

            #region 400740: TlVo not null or empty

            if (_tlVoIsNullOrEmpty == null)
            {
                _tlVoIsNullOrEmpty = new Error("400740", "Trọng lượng vỏ không được để trống, là thông tin trọng lượng vỏ, trường Tr.lượng vỏ (ton) trong màn hình C01 tương ứng Item key, chỉ thể hiện số");
            }

            #endregion

            #region 400840: SoDkEport not null or empty

            if (_soDkEportIsNullOrEmpty == null)
            {
                _soDkEportIsNullOrEmpty = new Error("400840", "Số đăng ký Eport không được để trống, là thông tin về số đăng ký eport của cont trên phiếu EIR");
            }

            #endregion

            #region 400940: GhiChuThongTinCan not null or empty

            if (_ghiChuThongTinCanIsNullOrEmpty == null)
            {
                _ghiChuThongTinCanIsNullOrEmpty = new Error("400940", "Ghi chú thông tin cân không được để trống, thể hiện 1 trong 2 giá trị Cân hoặc Không cân tương ứng với thực tế của container");
            }

            #endregion

            #region 400150: GhiChuViPhamTaiTrong not null or empty

            if (_ghiChuViPhamTaiTrongIsNullOrEmpty == null)
            {
                _ghiChuViPhamTaiTrongIsNullOrEmpty = new Error("400145", "Ghi chú vi phạm tải trọng không được để trống");
            }

            #endregion

            #region 400250: GhiChuStopCode not null or empty

            if (_ghiChuStopCodeIsNullOrEmpty == null)
            {
                _ghiChuStopCodeIsNullOrEmpty = new Error("400250", "Ghi chú Stop Code không được để trống, là danh sách mô tả thông tin stop của Cont, lấy trong hệ thống TopO, mỗi mô tả cách nhau dấu phẩy, lấy theo cấu trúc: Stop code: trường mô tả trong màn hình CP6");
            }

            #endregion

            #region 400350: GhiChuChiDinh not null or empty

            if (_ghiChuChiDinhIsNullOrEmpty == null)
            {
                _ghiChuChiDinhIsNullOrEmpty = new Error("400350", "Ghi chú chỉ định không được để trống, là danh sách mô tả chỉ định của Cont, lấy trong hệ thống TopO, mỗi mô tả cách nhau dấu phẩy, trường Ghi chú trong màn hình CP5, mỗi ghi chú cách nhau dấu phẩy");
            }

            #endregion

            #region 400450: GhiChuThongTinCont not null or empty

            if (_ghiChuThongTinContIsNullOrEmpty == null)
            {
                _ghiChuThongTinContIsNullOrEmpty = new Error("400450", "Ghi chú thông tin Cont không được để trống, là danh sách comment về Cont, lấy trong hệ thống TopO, mỗi comment cách nhau dấu phẩy");
            }

            #endregion

            #region 400550: SoXe not null or empty

            if (_soXeIsNullOrEmpty == null)
            {
                _soXeIsNullOrEmpty = new Error("400550", "Số xe không được để trống, là thông tin số xe giao nhận Cont trên phiếu EIR, trường Xe trong màn hình T01");
            }

            #endregion

            #region 400650: SoMooc not null or empty

            if (_soMoocIsNullOrEmpty == null)
            {
                _soMoocIsNullOrEmpty = new Error("400650", "Số mọoc không được để trống, là thông tin số mọoc của cont trên phiếu EIR, trường số mọoc trong màn hình T01");
            }

            #endregion

            #region 400750: GhiChuHanLenh not null or empty

            if (_ghiChuHanLenhIsNullOrEmpty == null)
            {
                _ghiChuHanLenhIsNullOrEmpty = new Error("400750", "Ghi chú hạn lệnh không được để trống, là ngày hạn lệnh lấy cont, cột Liner expiry date trong màn hình T61");
            }

            #endregion

            #region 400850: GhiChuHanLenh invalid

            if (_ghiChuHanLenhInvalid == null)
            {
                _ghiChuHanLenhInvalid = new Error("400850", "Ghi chú hạn lệnh không đúng định dạng, format: dd/MM/yyyy hh:mm:ss, là ngày hạn lệnh lấy cont, cột Liner expiry date trong màn hình T61");
            }

            #endregion

            #region 400950: NoiHaRong not null or empty

            if (_noiHaRongIsNullOrEmpty == null)
            {
                _noiHaRongIsNullOrEmpty = new Error("400950", "Nơi hạ rỗng không được để trống, là nơi hạ rỗng sau khu rút hàng tại kho khách hàng, lấy trường Nơi hạ rỗng tại E46 TOPO");
            }

            #endregion

            #region 400160: SoNgayMien not null or empty

            if (_soNgayMienIsNullOrEmpty == null)
            {
                _soNgayMienIsNullOrEmpty = new Error("400160", "Số ngày miễn không được để trống, là số ngày miễn tính từ ngày gateout cont hàng đến lúc trả rỗng tại nơi hạ rỗng, lấy số trường Số ngày miễn tại E46 TOPO");
            }

            #endregion

            #region 500100: The Edi secure key is wrong

            if (_ediWrongSecureKey == null)
            {
                _ediWrongSecureKey = new Error("500100", "The Edi secure key is wrong");
            }

            #endregion

            #region 500200: Site not found

            if (_siteNotFound == null)
            {
                _siteNotFound = new Error("500200", "Site not found");
            }

            #endregion
        }
    }
}
