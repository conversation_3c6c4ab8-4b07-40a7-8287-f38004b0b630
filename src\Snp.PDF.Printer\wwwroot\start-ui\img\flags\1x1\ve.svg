<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="512" height="512" viewBox="0 0 102.4 102.4">
  <defs>
  <g id="f" transform="translate(0 -36)">
    <g id="e">
    <g id="d">
      <path d="M0-5l-1.545 4.755 2.853.927z" id="c" fill="#fff"/>
      <use xlink:href="#c" transform="scale(-1 1)" width="180" height="120"/>
    </g>
    <use xlink:href="#d" transform="rotate(72)" width="180" height="120"/>
    </g>
    <use xlink:href="#d" transform="rotate(-72)" width="180" height="120"/>
    <use xlink:href="#e" transform="matrix(-.809 .588 -.588 -.809 0 0)" width="180" height="120"/>
  </g>
  </defs>
  <path d="M0 0h102.4v102.4h-102.4z" fill="#cf142b"/>
  <path d="M0 0h102.4v68.267h-102.4z" fill="#00247d"/>
  <path d="M0 0h102.4v34.133h-102.4z" fill="#fc0"/>
  <g transform="matrix(.853 0 0 .853 51.267 71.68)">
  <g id="b">
    <g id="a">
    <use xlink:href="#f" transform="rotate(10)" width="180" height="120"/>
    <use xlink:href="#f" transform="rotate(30)" width="180" height="120"/>
    </g>
    <use xlink:href="#a" transform="rotate(40)" width="180" height="120"/>
  </g>
  <use xlink:href="#b" transform="rotate(-80)" width="180" height="120"/>
  </g>
</svg>
