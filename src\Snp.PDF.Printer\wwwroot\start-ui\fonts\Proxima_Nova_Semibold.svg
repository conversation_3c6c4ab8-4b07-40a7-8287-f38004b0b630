<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20150824 at Sat Jan  9 01:06:17 2016
 By Everything Fonts
Copyright (c) <PERSON>, 2005. All rights reserved.
</metadata>
<defs>
<font id="Proxima_Nova_Semibold" horiz-adv-x="567" >
  <font-face 
    font-family="Proxima_Nova_Semibold"
    font-weight="600"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 6 3 0 0 2 0 4"
    ascent="790"
    descent="-210"
    x-height="483"
    cap-height="667"
    bbox="-172 -281 1119 908"
    underline-thickness="20"
    underline-position="-153"
    unicode-range="U+000D-FB04"
  />
<missing-glyph horiz-adv-x="493" 
d="M422 -90h-351v842h351v-842zM390 -60v782h-288v-782h288z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="858" 
d="M506 0h-105v391h-80v92h80v27q0 79 42 123t111 44q51 0 84 -19l-23 -78q-16 11 -42 11q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391zM737 542q-26 0 -45.5 19t-19.5 46t19.5 46t45.5 19q27 0 46 -19t19 -46t-19 -46t-46 -19zM630 -196q-40 0 -64 8t-50 27l31 78
q30 -27 67 -27q32 0 51.5 20.5t19.5 61.5v511h105v-511q0 -79 -41 -123.5t-119 -44.5zM198 0h-105v391h-80v92h80v27q0 78 41.5 122.5t111.5 44.5q75 0 116 -42l-41 -65q-23 21 -56 21q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="549" 
d="M198 0h-105v391h-80v92h80v27q0 79 42 123t111 44q51 0 84 -19l-23 -78q-16 11 -42 11q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391zM428 542q-26 0 -45.5 19t-19.5 46t19.5 46t45.5 19q27 0 46 -19t19 -46t-19 -46t-46 -19zM321 -196q-40 0 -64 8t-50 27l31 78
q30 -27 67 -27q32 0 51.5 20.5t19.5 61.5v511h105v-511q0 -79 -41 -123.5t-119 -44.5z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="617" 
d="M198 0h-105v391h-80v92h80v27q0 78 41.5 122.5t111.5 44.5q75 0 116 -42l-41 -65q-23 21 -56 21q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391zM506 0h-105v391h-80v92h80v27q0 78 41.5 122.5t111.5 44.5q75 0 116 -42l-41 -65q-23 21 -56 21q-31 0 -49 -20.5
t-18 -60.5v-27h98v-92h-98v-391z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="549" 
d="M198 0h-105v391h-80v92h80v27q0 79 42 123t111 44q51 0 84 -19l-23 -78q-16 11 -42 11q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391zM428 542q-26 0 -45.5 19t-19.5 46t19.5 46t45.5 19q27 0 46 -19t19 -46t-19 -46t-46 -19zM481 0h-105v483h105v-483z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="549" 
d="M198 0h-105v391h-80v92h80v27q0 79 42 123t111 44q51 0 84 -19l-23 -78q-16 11 -42 11q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391zM481 0h-105v667h105v-667z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="858" 
d="M506 0h-105v391h-80v92h80v27q0 79 42 123t111 44q51 0 84 -19l-23 -78q-16 11 -42 11q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391zM737 542q-26 0 -45.5 19t-19.5 46t19.5 46t45.5 19q27 0 46 -19t19 -46t-19 -46t-46 -19zM790 0h-105v483h105v-483zM198 0h-105
v391h-80v92h80v27q0 78 41.5 122.5t111.5 44.5q75 0 116 -42l-41 -65q-23 21 -56 21q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="858" 
d="M506 0h-105v391h-80v92h80v27q0 79 42 123t111 44q51 0 84 -19l-23 -78q-16 11 -42 11q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391zM790 0h-105v667h105v-667zM198 0h-105v391h-80v92h80v27q0 78 41.5 122.5t111.5 44.5q75 0 116 -42l-41 -65q-23 21 -56 21
q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="889" 
d="M198 0h-105v391h-80v92h80v27q0 79 42 123t111 44q51 0 84 -19l-23 -78q-16 11 -42 11q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391zM481 145q17 -27 52.5 -45.5t71.5 -18.5q62 0 99.5 44.5t37.5 115.5t-37.5 116t-99.5 45q-36 0 -71 -19t-53 -47v-191zM481 0h-105
v667h105v-251q59 79 155 79q94 0 154 -69.5t60 -184.5q0 -117 -60 -185t-154 -68q-95 0 -155 78v-66z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="876" 
d="M198 0h-105v391h-80v92h80v27q0 79 42 123t111 44q51 0 84 -19l-23 -78q-16 11 -42 11q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391zM808 0h-105v304q0 53 -25.5 75.5t-72.5 22.5q-37 0 -70 -19t-54 -46v-337h-105v667h105v-250q27 32 72.5 55t98.5 23
q156 0 156 -153v-342z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="835" 
d="M198 0h-105v391h-80v92h80v27q0 79 42 123t111 44q51 0 84 -19l-23 -78q-16 11 -42 11q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391zM828 0h-132l-148 199l-67 -69v-130h-105v667h105v-417l213 233h130l-201 -219z" />
    <glyph glyph-name=".notdef" horiz-adv-x="493" 
d="M422 -90h-351v842h351v-842zM390 -60v782h-288v-782h288z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="CR" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="257" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="245" 
d="M168 204h-91l-19 463h128zM122 -10q-28 0 -48.5 20.5t-20.5 48.5q0 29 20.5 49.5t48.5 20.5t49 -20.5t21 -49.5q0 -28 -21 -48.5t-49 -20.5z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="379" 
d="M131 391h-48q-35 210 -35 227q0 24 17 41.5t42 17.5q24 0 41.5 -17.5t17.5 -41.5zM296 391h-47q-35 210 -35 227q0 24 17 41.5t42 17.5q24 0 41 -17.5t17 -41.5z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="606" 
d="M322 0h-80l59 174h-102l-58 -174h-80l58 174h-100l22 69h102l59 180h-102l21 68h105l58 176h80l-58 -176h101l58 176h80l-59 -176h101l-20 -68h-104l-60 -180h105l-21 -69h-107zM323 243l60 180h-101l-59 -180h100z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="605" 
d="M268 -100v90q-149 11 -233 104l65 90q69 -74 168 -89v193q-47 12 -78 23.5t-65 33t-51.5 56t-17.5 81.5q0 77 58 131t154 62v93h80v-94q121 -13 201 -90l-67 -87q-55 54 -134 70v-172q37 -10 63 -19t57.5 -25.5t50.5 -36t32 -50.5t13 -70q0 -82 -54.5 -138.5
t-161.5 -65.5v-90h-80zM445 183q0 31 -25 50.5t-72 33.5v-173q49 8 73 33t24 56zM176 490q0 -48 92 -74v156q-43 -4 -67.5 -26.5t-24.5 -55.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="746" 
d="M193 346q-73 0 -119 46.5t-46 117.5q0 72 46 119.5t119 47.5q74 0 120.5 -47.5t46.5 -119.5q0 -71 -46.5 -117.5t-120.5 -46.5zM195 0h-66l426 667h67zM552 -12q-73 0 -119 46.5t-46 117.5q0 72 46 119.5t119 47.5t119.5 -48t46.5 -119t-46.5 -117.5t-119.5 -46.5z
M193 412q39 0 64 27.5t25 70.5q0 45 -25 72.5t-64 27.5t-63.5 -27.5t-24.5 -72.5q0 -43 25 -70.5t63 -27.5zM552 54q39 0 64 27.5t25 70.5q0 45 -25 72.5t-64 27.5t-63.5 -27.5t-24.5 -72.5q0 -43 24.5 -70.5t63.5 -27.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="644" 
d="M627 0h-137q-22 19 -59 58q-83 -70 -187 -70q-94 0 -154.5 49.5t-60.5 140.5q0 70 38.5 114.5t106.5 80.5q-47 84 -47 146q0 68 51.5 113t129.5 45q71 0 118 -37t47 -100q0 -36 -13 -65t-40.5 -51.5t-51 -36t-62.5 -32.5q29 -40 66 -82q37 -45 66 -77q44 69 66 139
l86 -39q-47 -103 -93 -165q55 -59 130 -131zM257 72q60 0 115 47q-63 68 -82 92q-42 51 -74 96q-75 -49 -75 -120q0 -53 34 -84t82 -31zM233 517q0 -43 33 -101q56 26 85 53t29 66q0 30 -19 47t-48 17q-34 0 -57 -23t-23 -59z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="214" 
d="M131 391h-48q-35 210 -35 227q0 24 17 41.5t42 17.5q24 0 41.5 -17.5t17.5 -41.5z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="276" 
d="M258 -149l-65 -50q-71 86 -111 203.5t-40 238.5q0 120 40 237.5t111 204.5l65 -49q-52 -98 -77 -189t-25 -204t25 -204t77 -188z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="276" 
d="M82 -199l-65 50q53 97 78 188t25 204t-25 204t-78 189l65 49q71 -87 111.5 -204t40.5 -238t-40.5 -238.5t-111.5 -203.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="346" 
d="M201 376h-56l5 112l-94 -61l-27 48l99 51l-99 51l27 48l94 -60l-5 112h56l-6 -112l94 60l28 -48l-99 -51l99 -51l-28 -48l-94 61z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="502" 
d="M473 303h-185v-207h-74v207h-185v68h185v200h74v-200h185v-68z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="245" 
d="M198 38q0 -50 -25.5 -95.5t-68.5 -75.5l-47 39q25 15 46 42t26 52q-9 -3 -17 -3q-26 0 -43 17.5t-17 45.5t20 48.5t48 20.5q32 0 55 -24.5t23 -66.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="300" 
d="M270 197h-240v90h240v-90z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="245" 
d="M122 -11q-28 0 -49 20.5t-21 49.5t21 49.5t49 20.5t49 -20.5t21 -49.5t-21 -49.5t-49 -20.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="315" 
d="M78 -20h-78l237 707h78z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="616" 
d="M308 -12q-67 0 -120 30t-84 80t-47 110t-16 125t16 125t47 109.5t84 79.5t120 30t120 -30t84 -79.5t47 -109.5t16 -125t-16 -125t-47 -110t-84 -80t-120 -30zM308 92q76 0 112 68t36 173q0 67 -13.5 119t-48 86.5t-86.5 34.5t-86.5 -34.5t-48 -86.5t-13.5 -119
q0 -105 36 -173t112 -68z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="380" 
d="M309 0h-117v515l-109 -113l-68 71l192 194h102v-667z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="595" 
d="M533 0h-483v92q209 158 284.5 234.5t75.5 144.5q0 49 -35.5 75.5t-85.5 26.5q-110 0 -180 -80l-68 77q43 52 108.5 79.5t137.5 27.5q102 0 172 -55t70 -151q0 -86 -72 -171t-220 -197h296v-103z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="574" 
d="M276 -12q-85 0 -150.5 30t-101.5 78l63 77q32 -37 82 -59t103 -22q65 0 102 27.5t37 74.5q0 94 -148 94q-67 0 -77 -1v105q12 -1 77 -1q62 0 99.5 22t37.5 66q0 45 -38 69.5t-97 24.5q-98 0 -172 -74l-60 73q89 105 243 105q109 0 175 -48.5t66 -131.5
q0 -62 -43.5 -102.5t-101.5 -50.5q57 -5 106 -48t49 -114q0 -86 -68.5 -140t-182.5 -54z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="580" 
d="M463 0h-117v151h-318v94l274 422h161v-413h89v-103h-89v-151zM346 254v308l-203 -308h203z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="600" 
d="M304 -12q-161 0 -248 103l67 80q70 -79 180 -79q62 0 99.5 33.5t37.5 83.5q0 54 -36.5 86.5t-97.5 32.5q-85 0 -146 -58l-83 24v373h437v-103h-320v-193q58 58 151 58q88 0 150.5 -58.5t62.5 -156.5q0 -103 -70.5 -164.5t-183.5 -61.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="601" 
d="M316 -12q-71 0 -125 27.5t-86 75.5t-48 108.5t-16 132.5q0 151 77 248t217 97q116 0 192 -74l-55 -89q-56 59 -137 59q-80 0 -128 -64t-48 -156q0 -13 1 -19q25 37 74 65t105 28q95 0 159 -56.5t64 -158.5q0 -95 -68 -159.5t-178 -64.5zM310 92q62 0 98 35.5t36 80.5
q0 58 -39 88.5t-98 30.5q-42 0 -81 -21.5t-65 -57.5q6 -64 42 -110t107 -46z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="532" 
d="M249 0h-128l252 564h-346v103h481v-81z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="595" 
d="M298 -12q-108 0 -182 48.5t-74 134.5q0 61 42.5 106t107.5 67q-62 20 -100.5 59t-38.5 99q0 44 21.5 79t57 55t78 30.5t88.5 10.5t88 -10.5t78 -30.5t57.5 -55t21.5 -79q0 -113 -140 -158q65 -22 107.5 -67t42.5 -106q0 -86 -73.5 -134.5t-181.5 -48.5zM298 390
q27 5 52 13.5t49.5 30t24.5 50.5q0 41 -35.5 65t-90.5 24q-56 0 -91.5 -24t-35.5 -65q0 -29 25 -50.5t50 -30t52 -13.5zM298 92q57 0 97 26t40 68q0 45 -46.5 72.5t-90.5 32.5q-26 -3 -55 -13.5t-56 -35t-27 -56.5q0 -42 39.5 -68t98.5 -26z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="601" 
d="M265 -11q-117 0 -191 74l54 88q56 -58 137 -58q85 0 130.5 64.5t45.5 154.5v20q-26 -37 -75 -65.5t-104 -28.5q-95 0 -159.5 57t-64.5 159q0 95 68.5 159.5t177.5 64.5q94 0 157.5 -48.5t90.5 -124t27 -172.5q0 -150 -77 -247t-217 -97zM293 339q43 0 82 21t64 57
q-5 63 -41.5 110t-107.5 47q-61 0 -97 -36t-36 -80q0 -58 39 -88.5t97 -30.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="245" 
d="M122 350q-28 0 -49 21t-21 49t21 49t49 21t49 -21t21 -49t-21 -49t-49 -21zM122 -11q-28 0 -49 20.5t-21 49.5t21 49.5t49 20.5t49 -20.5t21 -49.5t-21 -49.5t-49 -20.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="245" 
d="M122 350q-28 0 -49 21t-21 49t21 49t49 21t49 -21t21 -49t-21 -49t-49 -21zM198 41q0 -50 -25.5 -95.5t-68.5 -75.5l-47 39q25 15 46 42t26 52q-9 -3 -17 -3q-26 0 -43 17.5t-17 45.5t20 48.5t48 20.5q32 0 55 -24.5t23 -66.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="502" 
d="M473 90l-444 207v75l444 208v-81l-365 -165l365 -164v-80z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="502" 
d="M473 395h-444v68h444v-68zM473 205h-444v67h444v-67z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="502" 
d="M473 297l-444 -207v80l364 164l-364 165v81l444 -208v-75z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="458" 
d="M189 196q-36 34 -36 85q0 41 25 73.5t55.5 50.5t55.5 43t25 53q0 31 -24.5 51.5t-70.5 20.5q-82 0 -139 -70l-67 75q81 99 218 99q93 0 148 -44t55 -113q0 -41 -18.5 -73.5t-45 -53t-52.5 -38.5t-44.5 -39.5t-18.5 -45.5q0 -26 22 -45zM225 -11q-29 0 -49.5 20.5
t-20.5 49.5t20.5 49.5t49.5 20.5t49.5 -20.5t20.5 -49.5t-20.5 -49.5t-49.5 -20.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="783" 
d="M352 -70q-136 0 -226.5 90.5t-90.5 223.5q0 159 120 276.5t277 117.5q139 0 227 -90t88 -221q0 -114 -58 -178t-129 -64q-42 0 -66.5 22t-28.5 55l-1 6q-27 -37 -67 -60t-83 -23q-70 0 -112 46t-42 120q0 100 70.5 173.5t159.5 73.5q90 0 126 -71l12 56h94l-54 -255
q-2 -12 -2 -23q0 -23 11.5 -35.5t29.5 -12.5q36 0 68 43t32 127q0 122 -76.5 198t-202.5 76q-143 0 -249.5 -107t-106.5 -248q0 -120 80.5 -200t203.5 -80q97 0 184 55l20 -28q-98 -63 -208 -63zM348 160q73 0 127 76l27 126q-10 24 -33 42.5t-58 18.5q-64 0 -109 -50.5
t-45 -114.5q0 -44 24.5 -71t66.5 -27z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="648" 
d="M408 0h-337v667h328q89 0 139.5 -48.5t50.5 -121.5q0 -60 -33.5 -101.5t-82.5 -51.5q54 -8 91.5 -55.5t37.5 -108.5q0 -80 -51.5 -130t-142.5 -50zM375 391q44 0 69 24t25 62q0 39 -25 63t-69 24h-187v-173h187zM380 103q48 0 75 24.5t27 68.5q0 39 -27 65.5t-75 26.5
h-192v-185h192z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="682" 
d="M391 -12q-150 0 -250 96.5t-100 248.5t100 248.5t250 96.5q174 0 268 -150l-100 -51q-25 43 -70.5 70t-97.5 27q-99 0 -164.5 -68t-65.5 -173t65.5 -173t164.5 -68q53 0 98 27t70 70l100 -51q-96 -150 -268 -150z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="710" 
d="M319 0h-248v667h248q155 0 252 -93t97 -241q0 -147 -96.5 -240t-252.5 -93zM319 103q105 0 167 66t62 164q0 101 -60.5 166t-168.5 65h-131v-461h131z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="577" 
d="M528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="562" 
d="M188 0h-117v667h457v-103h-340v-173h333v-103h-333v-288z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="717" 
d="M391 -13q-148 0 -249 96t-101 250t101 249.5t249 95.5q172 0 269 -139l-96 -54q-28 39 -73.5 64t-99.5 25q-99 0 -164.5 -68t-65.5 -173t65.5 -173.5t164.5 -68.5q47 0 89.5 18t68.5 42v104h-199v103h316v-250q-109 -121 -275 -121z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="723" 
d="M652 0h-117v291h-347v-291h-117v667h117v-273h347v273h117v-667z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="481" 
d="M185 -12q-111 0 -177 70l54 89q51 -55 114 -55q54 0 85.5 33t31.5 88v454h117v-456q0 -110 -62 -166.5t-163 -56.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="621" 
d="M614 0h-144l-224 281l-58 -68v-213h-117v667h117v-318l261 318h145l-271 -315z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="517" 
d="M482 0h-411v667h117v-564h294v-103z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="835" 
d="M764 0h-117v495l-205 -495h-50l-204 495v-495h-117v667h165l181 -439l182 439h165v-667z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="720" 
d="M649 0h-113l-348 476v-476h-117v667h120l341 -463v463h117v-667z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="608" 
d="M188 0h-117v667h293q101 0 159.5 -60t58.5 -149q0 -88 -58.5 -148.5t-159.5 -60.5h-176v-249zM348 352q50 0 82 29t32 77t-32 77t-82 29h-160v-212h160z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5q0 -143 -89 -240l55 -62l-81 -68l-58 65q-74 -40 -168 -40zM382 92q51 0 94 20l-82 94l81 67l82 -94q46 63 46 154q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68
z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="628" 
d="M587 0h-135l-148 249h-116v-249h-117v667h293q98 0 158 -58t60 -151q0 -84 -46 -134.5t-112 -61.5zM348 352q50 0 82 29t32 77t-32 77t-82 29h-160v-212h160z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="594" 
d="M299 -12q-172 0 -271 106l65 90q86 -92 211 -92q67 0 100.5 27t33.5 64q0 38 -40 60.5t-97 35.5t-114.5 31t-97.5 61t-40 111q0 84 67.5 139.5t175.5 55.5q154 0 250 -93l-67 -87q-76 76 -193 76q-52 0 -82.5 -22.5t-30.5 -60.5q0 -25 21.5 -42t55.5 -27.5t75.5 -20.5
t83 -24.5t75.5 -35.5t55.5 -58t21.5 -88q0 -90 -65 -148t-193 -58z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="577" 
d="M347 0h-117v564h-202v103h521v-103h-202v-564z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="719" 
d="M360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-403q0 -128 -73.5 -202t-214.5 -74z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="673" 
d="M409 0h-146l-262 667h133l202 -540l202 540h133z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="903" 
d="M706 0h-125l-130 492l-129 -492h-125l-191 667h131l130 -514l138 514h93l138 -514l129 514h131z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="661" 
d="M657 0h-140l-187 261l-187 -261h-140l250 342l-235 325h140l172 -245l171 245h141l-234 -324z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="637" 
d="M377 0h-117v277l-259 390h134l184 -286l182 286h134l-258 -390v-277z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="589" 
d="M544 0h-500v95l341 469h-341v103h493v-95l-341 -469h348v-103z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="264" 
d="M247 -190h-203v868h203v-72h-126v-724h126v-72z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="315" 
d="M237 -20l-237 707h78l237 -707h-78z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="264" 
d="M220 -190h-203v72h126v724h-126v72h203v-868z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="437" 
d="M418 333h-80l-120 261l-119 -261h-80l161 334h78z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="564" 
d="M567 -112h-570v72h570v-72z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="240" 
d="M240 556h-75l-165 144h100z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="581" 
d="M173 145q17 -27 52.5 -45.5t71.5 -18.5q62 0 99.5 44.5t37.5 115.5t-37.5 116t-99.5 45q-36 0 -71 -19t-53 -47v-191zM173 0h-105v667h105v-251q59 79 155 79q94 0 154 -69.5t60 -184.5q0 -117 -60 -185t-154 -68q-95 0 -155 78v-66z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="497" 
d="M288 -12q-110 0 -180 71.5t-70 182.5q0 110 70 181.5t180 71.5q121 0 185 -86l-69 -64q-40 57 -111 57q-66 0 -106 -44.5t-40 -115.5t40 -116t106 -45q69 0 111 57l69 -64q-64 -86 -185 -86z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="581" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t155 -79v251h105v-667zM283 81q37 0 72 18t53 46v192q-18 28 -53 46.5t-72 18.5q-62 0 -99 -45t-37 -116t37 -115.5t99 -44.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="557" 
d="M291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="308" 
d="M198 0h-105v391h-80v92h80v27q0 78 41.5 122.5t111.5 44.5q75 0 116 -42l-41 -65q-23 21 -56 21q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="580" 
d="M264 -196q-123 0 -202 72l49 76q54 -62 153 -62q60 0 101.5 31.5t41.5 99.5v58q-62 -80 -155 -80q-95 0 -154.5 66t-59.5 182q0 115 59.5 181.5t154.5 66.5t155 -79v67h105v-458q0 -62 -21.5 -107.5t-58.5 -69t-78.5 -34t-89.5 -10.5zM283 92q36 0 71 19t53 46v180
q-18 27 -53 46t-71 19q-62 0 -99 -42.5t-37 -112.5t37 -112.5t99 -42.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="568" 
d="M500 0h-105v304q0 53 -25.5 75.5t-72.5 22.5q-37 0 -70 -19t-54 -46v-337h-105v667h105v-250q27 32 72.5 55t98.5 23q156 0 156 -153v-342z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="241" 
d="M120 542q-26 0 -45.5 19t-19.5 46t19.5 46t45.5 19q27 0 46 -19t19 -46t-19 -46t-46 -19zM173 0h-105v483h105v-483z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="241" 
d="M120 542q-26 0 -45.5 19t-19.5 46t19.5 46t45.5 19q27 0 46 -19t19 -46t-19 -46t-46 -19zM13 -196q-40 0 -64 8t-50 27l31 78q30 -27 67 -27q32 0 51.5 20.5t19.5 61.5v511h105v-511q0 -79 -41 -123.5t-119 -44.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="526" 
d="M520 0h-132l-148 199l-67 -69v-130h-105v667h105v-417l213 233h130l-201 -219z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="241" 
d="M173 0h-105v667h105v-667z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="833" 
d="M765 0h-105v315q0 87 -79 87q-33 0 -64 -19t-48 -45v-338h-105v315q0 87 -80 87q-32 0 -62.5 -19t-48.5 -46v-337h-105v483h105v-66q18 27 62.5 52.5t94.5 25.5q105 0 132 -89q23 36 68 62.5t96 26.5q139 0 139 -146v-349z" />
    <glyph glyph-name="n" unicode="n" 
d="M499 0h-105v302q0 100 -97 100q-38 0 -71 -19t-53 -46v-337h-105v483h105v-66q27 32 72 55t98 23q77 0 116.5 -40t39.5 -115v-340z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="573" 
d="M286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="578" 
d="M328 -12q-96 0 -155 79v-251h-105v667h105v-66q58 78 155 78q95 0 154.5 -68t59.5 -185t-59.5 -185.5t-154.5 -68.5zM297 81q62 0 99 45t37 116t-37 115.5t-99 44.5q-36 0 -71 -19t-53 -46v-191q18 -27 53 -46t71 -19z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="578" 
d="M281 81q36 0 71 19t53 46v191q-18 27 -53 46t-71 19q-62 0 -99 -44.5t-37 -115.5t37 -116t99 -45zM250 -12q-95 0 -154.5 68.5t-59.5 185.5t59.5 185t154.5 68q96 0 155 -78v66h105v-667h-105v251q-59 -79 -155 -79z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="347" 
d="M173 0h-105v483h105v-71q28 36 70 59t87 23v-104q-14 3 -33 3q-33 0 -70.5 -19.5t-53.5 -44.5v-329z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="472" 
d="M233 -12q-130 0 -207 75l48 76q27 -28 73 -48t91 -20q46 0 70.5 18t24.5 47q0 26 -30 40.5t-73 23t-86 21t-73 44.5t-30 83q0 62 51 104.5t139 42.5q111 0 188 -68l-44 -74q-23 26 -61 42t-82 16q-41 0 -65.5 -16.5t-24.5 -42.5q0 -20 21.5 -32.5t54 -18.5t70.5 -16.5
t70.5 -24.5t54 -45.5t21.5 -76.5q0 -66 -53 -108t-148 -42z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="319" 
d="M213 -12q-61 0 -92.5 32t-31.5 92v279h-80v92h80v132h105v-132h98v-92h-98v-253q0 -26 12 -41.5t34 -15.5q32 0 47 17l25 -79q-33 -31 -99 -31z" />
    <glyph glyph-name="u" unicode="u" 
d="M499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="503" 
d="M308 0h-113l-197 483h112l141 -362l141 362h113z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="754" 
d="M599 0h-110l-112 354l-112 -354h-110l-150 483h109l102 -351l115 351h92l115 -351l102 351h109z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="496" 
d="M491 0h-118l-125 178l-126 -178h-117l177 248l-167 235h118l115 -164l114 164h118l-167 -235z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="503" 
d="M47 -189l16 94q18 -8 40 -8q51 0 69 42l24 55l-198 489h112l141 -362l141 362h113l-233 -570q-43 -107 -166 -109q-33 0 -59 7z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="476" 
d="M429 0h-383v79l239 312h-239v92h379v-77l-241 -315h245v-91z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="276" 
d="M259 -190h-61q-58 0 -100 41t-42 107v187q0 29 -13.5 48.5t-38.5 19.5v62q25 0 38.5 19.5t13.5 48.5v186q0 66 42.5 107.5t99.5 41.5h61v-72h-61q-27 0 -45.5 -21.5t-18.5 -55.5v-196q0 -69 -46 -89q46 -20 46 -89v-196q0 -33 18.5 -55t45.5 -22h61v-72z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="214" 
d="M143 -20h-72v707h72v-707z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="276" 
d="M17 -190v72h61q27 0 45.5 22t18.5 55v196q0 69 46 89q-46 20 -46 89v196q0 34 -18.5 55.5t-45.5 21.5h-61v72h61q57 0 99.5 -41.5t42.5 -107.5v-186q0 -29 13.5 -48.5t38.5 -19.5v-62q-25 0 -38.5 -19.5t-13.5 -48.5v-187q0 -66 -42 -107t-100 -41h-61z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="507" 
d="M407 667l73 -9q-4 -51 -12 -89.5t-23.5 -75t-43 -56.5t-65.5 -20q-43 0 -70 28.5t-35.5 62.5t-23 62.5t-36.5 28.5q-54 0 -71 -183l-74 9q5 51 13 89.5t23.5 75t43 56.5t65.5 20q43 0 70 -28.5t35.5 -63t23 -63t36.5 -28.5q56 0 71 184z" />
    <glyph glyph-name="nbspace" unicode="&#xa0;" horiz-adv-x="257" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="245" 
d="M59 -184l18 462h91l19 -462h-128zM53 424q0 28 20.5 48.5t48.5 20.5t49 -20.5t21 -48.5t-21 -49t-49 -21t-48.5 20.5t-20.5 49.5z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="497" 
d="M246 -100v91q-93 14 -150.5 82.5t-57.5 168.5q0 98 57.5 167t150.5 83v73h78v-72q95 -12 149 -84l-69 -64q-31 43 -80 54v-315q49 11 80 54l69 -64q-54 -72 -149 -84v-90h-78zM147 242q0 -57 26.5 -98.5t72.5 -55.5v307q-46 -15 -72.5 -55.5t-26.5 -97.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="536" 
d="M21 268v70h83q-18 23 -28.5 39t-20 42.5t-9.5 54.5q0 87 71 145t164 58q158 0 219 -117l-93 -55q-13 33 -44 54.5t-69 21.5q-51 0 -86 -30t-35 -79q0 -17 3 -32t11 -30.5t13.5 -23.5t18 -25t16.5 -23h147v-70h-116q3 -14 3 -29q0 -75 -70 -120q24 8 51 8q32 0 72 -18
t66 -18q60 0 90 38l47 -93q-47 -49 -142 -49q-47 0 -98 22.5t-82 22.5q-44 0 -117 -39l-40 82q119 55 119 137q0 28 -13 56h-131z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="612" 
d="M306 89q-81 0 -143 40l-74 -75l-63 62l74 74q-42 64 -42 143q0 80 43 143l-76 76l63 62l76 -77q61 39 142 39q80 0 141 -39l76 77l63 -63l-75 -75q43 -63 43 -143q0 -81 -43 -144l75 -73l-63 -62l-76 74q-59 -39 -141 -39zM306 183q66 0 104 43.5t38 106.5q0 62 -38 105
t-104 43t-104 -43t-38 -105q0 -63 38 -106.5t104 -43.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="637" 
d="M377 0h-117v118h-237v67h237v92h-237v67h192l-214 323h134l184 -286l182 286h134l-214 -323h194v-67h-238v-92h238v-67h-238v-118z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="214" 
d="M143 -20h-72v316h72v-316zM143 371h-72v316h72v-316z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="477" 
d="M434 321q0 -87 -87 -128q87 -36 87 -122q0 -71 -55.5 -111.5t-145.5 -40.5q-121 0 -205 78l46 67q27 -29 69.5 -50t89.5 -21q45 0 72.5 18.5t27.5 51.5q0 25 -21.5 41t-54 23.5t-70.5 18.5t-70.5 24t-54 42.5t-21.5 72.5q0 48 30.5 80t74.5 46q-105 37 -105 125
q0 60 52.5 100.5t137.5 40.5q120 0 187 -69l-43 -63q-52 57 -140 57q-42 0 -67.5 -17.5t-25.5 -47.5q0 -26 30 -41t73 -23.5t86 -21.5t73 -45.5t30 -84.5zM331 303q0 34 -26 52t-70 29q-51 -13 -72 -33.5t-21 -51.5q0 -19 9 -32t29.5 -22.5t36 -14t45.5 -11.5q69 29 69 84z
" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="285" 
d="M306 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM96 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="778" 
d="M734 334q0 -143 -101 -244t-244 -101t-244 101.5t-101 243.5q0 143 101 244t244 101t244 -101t101 -244zM700 334q0 128 -91.5 219.5t-219.5 91.5t-219.5 -91.5t-91.5 -219.5t91.5 -219.5t219.5 -91.5t219.5 91.5t91.5 219.5zM512 217l29 -28q-57 -67 -148 -67
q-87 0 -147.5 61t-60.5 152t60.5 151t147.5 60q91 0 147 -66l-29 -28q-42 57 -118 57q-69 0 -117.5 -49t-48.5 -125q0 -75 49 -125.5t117 -50.5q76 0 119 58z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="382" 
d="M327 326h-77v33q-37 -41 -104 -41q-44 0 -77.5 27.5t-33.5 76.5t33.5 75.5t77.5 26.5q68 0 104 -40v46q0 27 -20.5 43t-52.5 16q-55 0 -95 -43l-31 50q56 52 138 52q61 0 99.5 -28t38.5 -91v-203zM177 367q49 0 73 33v44q-24 32 -73 32q-28 0 -46.5 -15t-18.5 -39
q0 -25 18.5 -40t46.5 -15z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="489" 
d="M459 63h-99l-160 180l160 177h99l-160 -177zM289 63h-99l-160 180l160 177h99l-160 -177z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="510" 
d="M473 463v-258h-70v190h-374v68h444z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="300" 
d="M270 197h-240v90h240v-90z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="494" 
d="M459 465q0 -88 -62 -150t-150 -62t-150 62t-62 150t62 150t150 62q89 0 150.5 -61.5t61.5 -150.5zM428 465q0 76 -53 128.5t-128 52.5q-76 0 -129 -52.5t-53 -128.5q0 -75 53 -128.5t129 -53.5q75 0 128 53.5t53 128.5zM346 343h-41l-63 96h-43v-96h-33v243h99
q32 0 55 -20.5t23 -53.5q0 -35 -22.5 -53.5t-39.5 -18.5zM309 512q0 19 -13.5 32t-30.5 13h-66v-88h66q17 0 30.5 12.5t13.5 30.5z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="363" 
d="M363 566h-363v62h363v-62z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="302" 
d="M280 547q0 -54 -38 -91.5t-92 -37.5q-53 0 -91 37.5t-38 91.5q0 53 38 91.5t91 38.5q54 0 92 -38.5t38 -91.5zM220 547q0 29 -21 49.5t-49 20.5t-48 -20.5t-20 -49.5t20 -49t48 -20q29 0 49.5 20.5t20.5 48.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="502" 
d="M473 325h-185v-208h-74v208h-185v67h185v201h74v-201h185v-67zM473 0h-444v67h444v-67z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="393" 
d="M352 421h-304v59q128 91 174.5 136t46.5 83q0 31 -21.5 47t-53.5 16q-34 0 -64 -15t-46 -36l-43 51q55 65 155 65q66 0 109.5 -32t43.5 -90q0 -51 -44.5 -101.5t-139.5 -117.5h187v-65z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="393" 
d="M354 531q0 -52 -43.5 -84.5t-115.5 -32.5q-107 0 -159 64l41 52q45 -51 115 -51q39 0 60.5 15.5t21.5 41.5q0 57 -95 57q-37 0 -43 -1v65q8 -1 42 -1q89 0 89 53q0 25 -22.5 39t-57.5 14q-62 0 -107 -46l-39 48q58 63 154 63q70 0 111 -29.5t41 -78.5q0 -37 -26.5 -61.5
t-62.5 -30.5q35 -3 65.5 -28t30.5 -68z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="240" 
d="M240 700l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="581" 
d="M173 24v-208h-105v667h105v-304q0 -99 98 -99q37 0 70 18.5t53 45.5v339h105v-352q0 -23 11.5 -36.5t31.5 -13.5q9 0 19 2l7 -89q-20 -6 -50 -6q-97 0 -119 86q-25 -38 -62.5 -62t-80.5 -24q-60 0 -83 36z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="449" 
d="M392 -100h-55v712h-83v-712h-55v423q-71 0 -121.5 50.5t-50.5 121.5t50.5 121.5t121.5 50.5h193v-767z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="245" 
d="M192 244q0 -28 -21 -48.5t-49 -20.5t-49 20.5t-21 48.5t21 49t49 21t49 -21t21 -49z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="214" 
d="M106 -194q-67 0 -106 31l22 46q36 -29 81 -29q23 0 39.5 9.5t16.5 26.5q0 30 -33 30q-21 0 -34 -15l-41 23l31 83h55l-26 -64q16 11 36 11q29 0 48 -19.5t19 -49.5q0 -37 -31 -60t-77 -23z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="264" 
d="M204 421h-81v296l-67 -69l-46 49l124 124h70v-400z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="405" 
d="M202 318q-75 0 -121.5 47.5t-46.5 117.5t46.5 117.5t121.5 47.5q77 0 123 -47.5t46 -117.5t-46 -117.5t-123 -47.5zM202 381q41 0 65 28t24 74q0 45 -24 72.5t-65 27.5t-64.5 -27.5t-23.5 -72.5q0 -46 23.5 -74t64.5 -28z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="489" 
d="M289 243l-160 -180h-99l160 180l-160 177h99zM459 243l-160 -180h-99l160 180l-160 177h99z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="806" 
d="M204 267h-81v296l-67 -69l-46 49l124 124h70v-400zM776 92h-55v-92h-80v92h-196v56l164 252h112v-244h55v-64zM641 156v175l-116 -175h116zM619 667l-427 -667h-67l426 667h68z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="841" 
d="M204 267h-81v296l-67 -69l-46 49l124 124h70v-400zM799 0h-304v59q128 91 174.5 136t46.5 83q0 31 -21.5 47t-53.5 16q-34 0 -64 -15t-46 -36l-43 51q55 65 155 65q66 0 109.5 -32t43.5 -90q0 -51 -44.5 -101.5t-139.5 -117.5h187v-65zM619 667l-427 -667h-67l426 667h68
z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="902" 
d="M873 92h-55v-92h-80v92h-196v56l164 252h112v-244h55v-64zM738 156v175l-116 -175h116zM715 667l-427 -667h-67l426 667h68zM354 377q0 -52 -43.5 -84.5t-115.5 -32.5q-107 0 -159 64l41 52q45 -51 115 -51q39 0 60.5 15.5t21.5 41.5q0 57 -95 57q-37 0 -43 -1v65
q8 -1 42 -1q89 0 89 53q0 25 -22.5 39t-57.5 14q-62 0 -107 -46l-39 48q58 63 154 63q70 0 111 -29.5t41 -78.5q0 -37 -26.5 -61.5t-62.5 -30.5q35 -3 65.5 -28t30.5 -68z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="396" 
d="M269 285q36 -35 36 -85q0 -41 -25 -73.5t-55.5 -50.5t-55.5 -43t-25 -53q0 -31 24.5 -51.5t70.5 -20.5q82 0 139 70l67 -75q-81 -99 -218 -99q-93 0 -148 44t-55 113q0 41 18.5 73.5t45 53t52.5 38.5t44.5 39.5t18.5 45.5q0 26 -22 45zM233 493q29 0 49.5 -20.5
t20.5 -49.5q0 -28 -21 -48.5t-49 -20.5t-49 20.5t-21 48.5q0 29 20.5 49.5t49.5 20.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM398 723h-75l-165 144h100z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM516 867l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="673" 
d="M480 723h-67l-77 96l-75 -96h-66l93 144h96zM671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM394 721q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58
q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="673" 
d="M500 775q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM290 775q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318
l-120 -318h240z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM337 689q-42 0 -72 30t-30 72t30 72.5t72 30.5t71.5 -30.5t29.5 -72.5t-29.5 -72t-71.5 -30zM337 739q21 0 36 15.5t15 36.5q0 22 -14.5 37t-36.5 15q-21 0 -36.5 -15t-15.5 -37
q0 -21 15.5 -36.5t36.5 -15.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="959" 
d="M912 0h-457v128h-247l-77 -128h-133l410 667h504v-103h-340v-173h333v-103h-333v-185h340v-103zM455 231v318l-191 -318h191z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="682" 
d="M385 -190q-67 0 -106 31l22 46q36 -29 81 -29q23 0 39.5 9.5t16.5 26.5q0 30 -33 30q-21 0 -34 -15l-41 23l21 58q-135 13 -222.5 107.5t-87.5 235.5q0 152 100 248.5t250 96.5q174 0 268 -150l-100 -51q-25 43 -70.5 70t-97.5 27q-99 0 -164.5 -68t-65.5 -173t65.5 -173
t164.5 -68q53 0 98 27t70 70l100 -51q-91 -144 -254 -150l-15 -37q16 11 36 11q29 0 48 -19.5t19 -49.5q0 -37 -31 -60t-77 -23z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="577" 
d="M528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103zM360 723h-75l-165 144h100z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="577" 
d="M478 867l-165 -144h-75l140 144h100zM528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="577" 
d="M442 723h-67l-77 96l-75 -96h-66l93 144h96zM528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="577" 
d="M463 775q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM253 775q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667zM190 723h-75l-165 144h100z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667zM310 867l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="259" 
d="M273 723h-67l-77 96l-75 -96h-66l93 144h96zM188 0h-117v667h117v-667z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="259" 
d="M294 775q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM84 775q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM188 0h-117v667h117v-667z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="736" 
d="M345 0h-248v285h-88v86h88v296h248q155 0 252 -93t97 -241q0 -147 -96.5 -240t-252.5 -93zM345 103q105 0 167 66t62 164q0 101 -60.5 166t-168.5 65h-131v-193h158v-86h-158v-182h131z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="720" 
d="M416 721q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36zM649 0h-113l-348 476v-476h-117v667h120l341 -463v463h117v-667z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="765" 
d="M444 723h-75l-165 144h100zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="765" 
d="M562 867l-165 -144h-75l140 144h100zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="765" 
d="M527 723h-67l-77 96l-75 -96h-66l93 144h96zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68zM440 721q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5
t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q29 0 50 -19.5t36.5 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="765" 
d="M546 775q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM336 775q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5
t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="502" 
d="M395 142l-144 144l-144 -144l-48 47l144 145l-144 144l48 48l144 -145l144 145l48 -48l-144 -144l144 -145z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="765" 
d="M382 -12q-101 0 -179 46l-25 -34h-87l55 76q-105 99 -105 257q0 150 96 247.5t245 97.5q93 0 170 -41l22 30h87l-51 -71q113 -100 113 -263q0 -150 -96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5t-60 172l-276 -383q51 -30 115 -30zM161 333q0 -98 52 -164
l275 379q-47 26 -106 26q-100 0 -160.5 -68t-60.5 -173z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="719" 
d="M360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-403q0 -128 -73.5 -202t-214.5 -74zM421 723h-75l-165 144h100z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="719" 
d="M539 867l-165 -144h-75l140 144h100zM360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-403q0 -128 -73.5 -202t-214.5 -74z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="719" 
d="M504 723h-67l-77 96l-75 -96h-66l93 144h96zM360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-403q0 -128 -73.5 -202t-214.5 -74z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="719" 
d="M525 775q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM315 775q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128
t126.5 -47t126 47t44 128v400h118v-403q0 -128 -73.5 -202t-214.5 -74z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="637" 
d="M377 0h-117v277l-259 390h134l184 -286l182 286h134l-258 -390v-277zM497 867l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="608" 
d="M188 0h-117v667h117v-113h176q101 0 159.5 -60.5t58.5 -149.5q0 -88 -58.5 -147.5t-159.5 -59.5h-176v-137zM348 240q50 0 82 29t32 76t-32 76.5t-82 29.5h-160v-211h160z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="623" 
d="M606 140q0 -66 -52.5 -109t-142.5 -43q-67 0 -109.5 20t-82.5 58l47 73q22 -29 61.5 -48t83.5 -19q45 0 69.5 19t24.5 46q0 22 -20 36t-50.5 20.5t-65.5 17.5t-65.5 24.5t-50.5 42.5t-20 71q0 36 21 65t45.5 44t45.5 33t21 35q0 27 -26.5 42.5t-60.5 15.5q-46 0 -76 -27
t-30 -72v-485h-105v485q0 81 58 136.5t155 55.5q78 0 135 -38t57 -99q0 -37 -21.5 -66t-48 -44t-48 -35t-21.5 -43q0 -20 20 -32t50.5 -17.5t65.5 -16.5t65.5 -24.5t50.5 -45t20 -76.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM328 556h-75l-165 144h100z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM446 700l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM410 556h-67l-77 96l-75 -96h-66l93 144h96z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM325 554q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM431 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM221 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM268 536q-42 0 -72 30t-30 72t30 72.5t72 30.5t71.5 -30.5t29.5 -72.5t-29.5 -72t-71.5 -30zM268 586q21 0 36 15.5t15 36.5q0 22 -14.5 37t-36.5 15q-21 0 -36.5 -15t-15.5 -37q0 -21 15.5 -36.5t36.5 -15.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="878" 
d="M842 207h-351q6 -57 43.5 -94t98.5 -37q34 0 69 14t58 39l48 -71q-35 -33 -84 -51.5t-96 -18.5q-132 0 -190 98q-29 -44 -83.5 -71t-119.5 -27q-82 0 -139 41.5t-57 117.5q0 75 51 116.5t126 41.5q110 0 170 -60v71q0 41 -34 65t-89 24q-89 0 -156 -61l-43 73
q84 78 208 78q137 0 174 -93q64 93 177 93q100 0 159.5 -73.5t59.5 -189.5v-25zM490 281h250q-2 50 -33.5 89t-91.5 39q-57 0 -89.5 -39t-35.5 -89zM386 175v7q-45 51 -128 51q-50 0 -81.5 -20.5t-31.5 -58.5t31 -58t82 -20t89.5 31.5t38.5 67.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="497" 
d="M290 -192q-67 0 -106 31l22 46q36 -29 81 -29q23 0 39.5 9.5t16.5 26.5q0 30 -33 30q-21 0 -34 -15l-41 23l22 60q-98 10 -158.5 79.5t-60.5 172.5q0 110 70 181.5t180 71.5q121 0 185 -86l-69 -64q-40 57 -111 57q-66 0 -106 -44.5t-40 -115.5t40 -116t106 -45
q69 0 111 57l69 -64q-57 -77 -162 -85l-16 -40q16 11 36 11q29 0 48 -19.5t19 -49.5q0 -37 -31 -60t-77 -23z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="557" 
d="M291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272zM345 556h-75
l-165 144h100z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="557" 
d="M463 700l-165 -144h-75l140 144h100zM291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39
q-62 0 -97 -38.5t-39 -89.5h272z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="557" 
d="M427 556h-67l-77 96l-75 -96h-66l93 144h96zM291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39
q-62 0 -97 -38.5t-39 -89.5h272z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="557" 
d="M448 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM238 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5
q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="241" 
d="M173 0h-105v483h105v-483zM181 556h-75l-165 144h100z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="241" 
d="M300 700l-165 -144h-75l140 144h100zM173 0h-105v483h105v-483z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="241" 
d="M263 556h-67l-77 96l-75 -96h-66l93 144h96zM173 0h-105v483h105v-483z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="241" 
d="M284 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM74 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM173 0h-105v483h105v-483z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="573" 
d="M111 481l-22 52l112 49q-13 9 -37 23.5t-30 18.5l57 85q64 -38 118 -79l105 46l22 -51l-78 -35q177 -155 177 -340q0 -117 -67 -189.5t-182 -72.5q-111 0 -179.5 67.5t-68.5 172.5q0 104 61.5 172t155.5 68q99 0 159 -91q-40 81 -158 168zM286 81q65 0 102.5 42.5
t37.5 104.5t-37.5 104.5t-102.5 42.5q-64 0 -101.5 -42.5t-37.5 -104.5t37.5 -104.5t101.5 -42.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" 
d="M499 0h-105v302q0 100 -97 100q-38 0 -71 -19t-53 -46v-337h-105v483h105v-66q27 32 72 55t98 23q77 0 116.5 -40t39.5 -115v-340zM342 554q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q29 0 50 -19.5t36.5 -39
t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="573" 
d="M286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM348 556h-75l-165 144h100z
" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="573" 
d="M466 700l-165 -144h-75l140 144h100zM286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5
t101.5 -46.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="573" 
d="M429 556h-67l-77 96l-75 -96h-66l93 144h96zM286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5
q0 -68 37.5 -114.5t101.5 -46.5z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="573" 
d="M286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM344 554q-19 0 -34.5 8
t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="573" 
d="M450 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM240 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180
q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="511" 
d="M310 520q0 -22 -16 -38.5t-39 -16.5q-22 0 -38 16t-16 39q0 22 15.5 38t38.5 16t39 -16t16 -38zM482 303h-453v68h453v-68zM310 151q0 -22 -16 -38t-39 -16t-38.5 15.5t-15.5 38.5t16 39t38 16q23 0 39 -16t16 -39z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="573" 
d="M118 0h-68l53 64q-65 71 -65 178t68 180t180 73q78 0 140 -39l22 27h69l-50 -61q68 -71 68 -180q0 -108 -68 -181t-181 -73q-84 0 -143 42zM286 81q65 0 102.5 46.5t37.5 114.5q0 58 -27 98l-193 -233q36 -26 80 -26zM147 242q0 -54 24 -95l191 232q-35 23 -76 23
q-64 0 -101.5 -46.5t-37.5 -113.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" 
d="M499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483zM344 556h-75l-165 144h100z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" 
d="M462 700l-165 -144h-75l140 144h100zM499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" 
d="M426 556h-67l-77 96l-75 -96h-66l93 144h96zM499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" 
d="M446 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM236 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304
q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="503" 
d="M47 -189l16 94q18 -8 40 -8q51 0 69 42l24 55l-198 489h112l141 -362l141 362h113l-233 -570q-43 -107 -166 -109q-33 0 -59 7zM430 700l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="578" 
d="M328 -12q-96 0 -155 79v-251h-105v851h105v-250q58 78 155 78q95 0 154.5 -68t59.5 -185t-59.5 -185.5t-154.5 -68.5zM297 81q62 0 99 45t37 116t-37 115.5t-99 44.5q-36 0 -71 -19t-53 -46v-191q18 -27 53 -46t71 -19z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="503" 
d="M47 -189l16 94q18 -8 40 -8q51 0 69 42l24 55l-198 489h112l141 -362l141 362h113l-233 -570q-43 -107 -166 -109q-33 0 -59 7zM415 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM205 608q0 -25 -17 -42t-41 -17
q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM517 733h-363v62h363v-62z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM449 566h-363v62h363v-62z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM511 789q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM443 622q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="673" 
d="M685 -101l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 55 48 97h-53l-49 128h-306l-49 -128h-133l262 667h146l262 -667q-73 -36 -73 -93q0 -23 11 -34.5t29 -11.5q34 0 47 38zM456 231l-120 318l-120 -318h240z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="536" 
d="M482 -101l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 55 48 97h-25v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320q-73 -36 -73 -93
q0 -23 11 -34.5t29 -11.5q34 0 47 38zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5t74 -23.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="682" 
d="M570 867l-165 -144h-75l140 144h100zM391 -12q-150 0 -250 96.5t-100 248.5t100 248.5t250 96.5q174 0 268 -150l-100 -51q-25 43 -70.5 70t-97.5 27q-99 0 -164.5 -68t-65.5 -173t65.5 -173t164.5 -68q53 0 98 27t70 70l100 -51q-96 -150 -268 -150z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="497" 
d="M466 700l-165 -144h-75l140 144h100zM288 -12q-110 0 -180 71.5t-70 182.5q0 110 70 181.5t180 71.5q121 0 185 -86l-69 -64q-40 57 -111 57q-66 0 -106 -44.5t-40 -115.5t40 -116t106 -45q69 0 111 57l69 -64q-64 -86 -185 -86z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="682" 
d="M533 723h-67l-77 96l-75 -96h-66l93 144h96zM391 -12q-150 0 -250 96.5t-100 248.5t100 248.5t250 96.5q174 0 268 -150l-100 -51q-25 43 -70.5 70t-97.5 27q-99 0 -164.5 -68t-65.5 -173t65.5 -173t164.5 -68q53 0 98 27t70 70l100 -51q-96 -150 -268 -150z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="497" 
d="M431 556h-67l-77 96l-75 -96h-66l93 144h96zM288 -12q-110 0 -180 71.5t-70 182.5q0 110 70 181.5t180 71.5q121 0 185 -86l-69 -64q-40 57 -111 57q-66 0 -106 -44.5t-40 -115.5t40 -116t106 -45q69 0 111 57l69 -64q-64 -86 -185 -86z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="682" 
d="M391 -12q-150 0 -250 96.5t-100 248.5t100 248.5t250 96.5q174 0 268 -150l-100 -51q-25 43 -70.5 70t-97.5 27q-99 0 -164.5 -68t-65.5 -173t65.5 -173t164.5 -68q53 0 98 27t70 70l100 -51q-96 -150 -268 -150zM393 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5
t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="497" 
d="M288 -12q-110 0 -180 71.5t-70 182.5q0 110 70 181.5t180 71.5q121 0 185 -86l-69 -64q-40 57 -111 57q-66 0 -106 -44.5t-40 -115.5t40 -116t106 -45q69 0 111 57l69 -64q-64 -86 -185 -86zM289 548q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5
t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="682" 
d="M440 723h-96l-96 144h66l78 -97l74 97h67zM391 -12q-150 0 -250 96.5t-100 248.5t100 248.5t250 96.5q174 0 268 -150l-100 -51q-25 43 -70.5 70t-97.5 27q-99 0 -164.5 -68t-65.5 -173t65.5 -173t164.5 -68q53 0 98 27t70 70l100 -51q-96 -150 -268 -150z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="497" 
d="M338 556h-96l-96 144h66l78 -97l74 97h67zM288 -12q-110 0 -180 71.5t-70 182.5q0 110 70 181.5t180 71.5q121 0 185 -86l-69 -64q-40 57 -111 57q-66 0 -106 -44.5t-40 -115.5t40 -116t106 -45q69 0 111 57l69 -64q-64 -86 -185 -86z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="710" 
d="M407 723h-96l-96 144h66l78 -97l74 97h67zM319 0h-248v667h248q155 0 252 -93t97 -241q0 -147 -96.5 -240t-252.5 -93zM319 103q105 0 167 66t62 164q0 101 -60.5 166t-168.5 65h-131v-461h131z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="670" 
d="M675 604q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t155 -79v251h105v-667zM283 81q37 0 72 18
t53 46v192q-18 28 -53 46.5t-72 18.5q-62 0 -99 -45t-37 -116t37 -115.5t99 -44.5z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="736" 
d="M345 0h-248v285h-88v86h88v296h248q155 0 252 -93t97 -241q0 -147 -96.5 -240t-252.5 -93zM345 103q105 0 167 66t62 164q0 101 -60.5 166t-168.5 65h-131v-193h158v-86h-158v-182h131z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="585" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t155 -79v115h-150v62h150v74h105v-74h63v-62h-63v-531zM283 81q37 0 72 18t53 46v192q-18 28 -53 46.5t-72 18.5q-62 0 -99 -45t-37 -116t37 -115.5t99 -44.5z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="577" 
d="M528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103zM481 733h-363v62h363v-62z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="557" 
d="M291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272zM466 566h-363v62
h363v-62z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="577" 
d="M528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103zM476 789q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="557" 
d="M291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272zM460 622
q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="577" 
d="M528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103zM301 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="557" 
d="M291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272zM285 548
q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="577" 
d="M528 0q-73 -36 -73 -93q0 -23 11 -34.5t29 -11.5q34 0 47 38l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 55 48 97h-377v667h457v-103h-340v-173h333v-103h-333v-185h340v-103z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="557" 
d="M395 -101l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 47 36 85q-109 1 -180 71t-71 183q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-42 -41 -106 -58q-73 -36 -73 -93q0 -23 11 -34.5
t29 -11.5q34 0 47 38zM419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="577" 
d="M350 723h-96l-96 144h66l78 -97l74 97h67zM528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="557" 
d="M333 556h-96l-96 144h66l78 -97l74 97h67zM291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39
q-62 0 -97 -38.5t-39 -89.5h272z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="717" 
d="M537 723h-67l-77 96l-75 -96h-66l93 144h96zM391 -13q-148 0 -249 96t-101 250t101 249.5t249 95.5q172 0 269 -139l-96 -54q-28 39 -73.5 64t-99.5 25q-99 0 -164.5 -68t-65.5 -173t65.5 -173.5t164.5 -68.5q47 0 89.5 18t68.5 42v104h-199v103h316v-250
q-109 -121 -275 -121z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="580" 
d="M437 556h-67l-77 96l-75 -96h-66l93 144h96zM264 -196q-123 0 -202 72l49 76q54 -62 153 -62q60 0 101.5 31.5t41.5 99.5v58q-62 -80 -155 -80q-95 0 -154.5 66t-59.5 182q0 115 59.5 181.5t154.5 66.5t155 -79v67h105v-458q0 -62 -21.5 -107.5t-58.5 -69t-78.5 -34
t-89.5 -10.5zM283 92q36 0 71 19t53 46v180q-18 27 -53 46t-71 19q-62 0 -99 -42.5t-37 -112.5t37 -112.5t99 -42.5z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="717" 
d="M391 -13q-148 0 -249 96t-101 250t101 249.5t249 95.5q172 0 269 -139l-96 -54q-28 39 -73.5 64t-99.5 25q-99 0 -164.5 -68t-65.5 -173t65.5 -173.5t164.5 -68.5q47 0 89.5 18t68.5 42v104h-199v103h316v-250q-109 -121 -275 -121zM568 789q-65 -88 -175 -88
q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="580" 
d="M264 -196q-123 0 -202 72l49 76q54 -62 153 -62q60 0 101.5 31.5t41.5 99.5v58q-62 -80 -155 -80q-95 0 -154.5 66t-59.5 182q0 115 59.5 181.5t154.5 66.5t155 -79v67h105v-458q0 -62 -21.5 -107.5t-58.5 -69t-78.5 -34t-89.5 -10.5zM283 92q36 0 71 19t53 46v180
q-18 27 -53 46t-71 19q-62 0 -99 -42.5t-37 -112.5t37 -112.5t99 -42.5zM469 622q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="717" 
d="M391 -13q-148 0 -249 96t-101 250t101 249.5t249 95.5q172 0 269 -139l-96 -54q-28 39 -73.5 64t-99.5 25q-99 0 -164.5 -68t-65.5 -173t65.5 -173.5t164.5 -68.5q47 0 89.5 18t68.5 42v104h-199v103h316v-250q-109 -121 -275 -121zM393 715q-24 0 -41.5 17t-17.5 42
q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="580" 
d="M264 -196q-123 0 -202 72l49 76q54 -62 153 -62q60 0 101.5 31.5t41.5 99.5v58q-62 -80 -155 -80q-95 0 -154.5 66t-59.5 182q0 115 59.5 181.5t154.5 66.5t155 -79v67h105v-458q0 -62 -21.5 -107.5t-58.5 -69t-78.5 -34t-89.5 -10.5zM283 92q36 0 71 19t53 46v180
q-18 27 -53 46t-71 19q-62 0 -99 -42.5t-37 -112.5t37 -112.5t99 -42.5zM294 548q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="717" 
d="M455 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM391 -13q-148 0 -249 96t-101 250t101 249.5t249 95.5q172 0 269 -139l-96 -54q-28 39 -73.5 64t-99.5 25
q-99 0 -164.5 -68t-65.5 -173t65.5 -173.5t164.5 -68.5q47 0 89.5 18t68.5 42v104h-199v103h316v-250q-109 -121 -275 -121z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="580" 
d="M236 611q0 46 20.5 85.5t54.5 63.5l39 -31q-21 -13 -39.5 -36t-20.5 -43h2q2 1 4 1q20 0 37 -15.5t17 -37.5q0 -23 -16.5 -39.5t-40.5 -16.5q-22 0 -39.5 17t-17.5 52zM264 -196q-123 0 -202 72l49 76q54 -62 153 -62q60 0 101.5 31.5t41.5 99.5v58q-62 -80 -155 -80
q-95 0 -154.5 66t-59.5 182q0 115 59.5 181.5t154.5 66.5t155 -79v67h105v-458q0 -62 -21.5 -107.5t-58.5 -69t-78.5 -34t-89.5 -10.5zM283 92q36 0 71 19t53 46v180q-18 27 -53 46t-71 19q-62 0 -99 -42.5t-37 -112.5t37 -112.5t99 -42.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="723" 
d="M502 723h-67l-77 96l-75 -96h-66l93 144h96zM652 0h-117v291h-347v-291h-117v667h117v-273h347v273h117v-667z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="568" 
d="M427 723h-67l-77 96l-75 -96h-66l93 144h96zM500 0h-105v304q0 53 -25.5 75.5t-72.5 22.5q-37 0 -70 -19t-54 -46v-337h-105v667h105v-250q27 32 72.5 55t98.5 23q156 0 156 -153v-342z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="741" 
d="M661 0h-117v291h-347v-291h-117v496h-71v62h71v109h117v-109h347v109h117v-109h70v-62h-70v-496zM197 394h347v102h-347v-102z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="571" 
d="M327 531h-151v-114q27 32 72.5 55t98.5 23q156 0 156 -153v-342h-105v304q0 53 -25.5 75.5t-72.5 22.5q-37 0 -70 -19t-54 -46v-337h-105v531h-62v62h62v74h105v-74h151v-62z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667zM188 721q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="241" 
d="M173 0h-105v483h105v-483zM178 554q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667zM312 733h-363v62h363v-62z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="241" 
d="M173 0h-105v483h105v-483zM302 566h-363v62h363v-62z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667zM306 789q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="241" 
d="M173 0h-105v483h105v-483zM296 622q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="259" 
d="M202 -101l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 55 48 97h-37v667h117v-667q-73 -36 -73 -93q0 -23 11 -34.5t29 -11.5q34 0 47 38z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="241" 
d="M187 -101l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 55 48 97h-25v483h105v-483q-73 -36 -73 -93q0 -23 11 -34.5t29 -11.5q34 0 47 38zM120 542q-26 0 -45.5 19t-19.5 46t19.5 46t45.5 19q27 0 46 -19t19 -46t-19 -46t-46 -19z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667zM131 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="241" 
d="M173 0h-105v483h105v-483z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="740" 
d="M188 0h-117v667h117v-667zM444 -12q-111 0 -177 70l54 89q51 -55 114 -55q54 0 85.5 33t31.5 88v454h117v-456q0 -110 -62 -166.5t-163 -56.5z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="482" 
d="M120 542q-26 0 -45.5 19t-19.5 46t19.5 46t45.5 19q27 0 46 -19t19 -46t-19 -46t-46 -19zM173 0h-105v483h105v-483zM361 542q-26 0 -45.5 19t-19.5 46t19.5 46t45.5 19q27 0 46 -19t19 -46t-19 -46t-46 -19zM254 -196q-40 0 -64 8t-50 27l31 78q30 -27 67 -27
q32 0 51.5 20.5t19.5 61.5v511h105v-511q0 -79 -41 -123.5t-119 -44.5z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="481" 
d="M495 723h-67l-77 96l-75 -96h-66l93 144h96zM185 -12q-111 0 -177 70l54 89q51 -55 114 -55q54 0 85.5 33t31.5 88v454h117v-456q0 -110 -62 -166.5t-163 -56.5z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="241" 
d="M265 556h-67l-77 96l-75 -96h-66l93 144h96zM13 -196q-40 0 -64 8t-50 27l31 78q30 -27 67 -27q32 0 51.5 20.5t19.5 61.5v511h105v-511q0 -79 -41 -123.5t-119 -44.5z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="630" 
d="M380 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM614 0h-144l-224 281l-58 -68v-213h-117v667h117v-318l261 318h145l-271 -315z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="526" 
d="M328 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM520 0h-132l-148 199l-67 -69v-130h-105v667h105v-417l213 233h130l-201 -219z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="526" 
d="M520 0h-132l-148 199l-67 -69v-130h-105v483h105v-233l213 233h130l-201 -219z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="517" 
d="M455 867l-165 -144h-75l140 144h100zM482 0h-411v667h117v-564h294v-103z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="241" 
d="M300 867l-165 -144h-75l140 144h100zM173 0h-105v667h105v-667z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="517" 
d="M323 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM482 0h-411v667h117v-564h294v-103z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="241" 
d="M186 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM173 0h-105v667h105v-667z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="517" 
d="M392 605q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM482 0h-411v667h117v-564h294v-103z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="327" 
d="M346 605q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM173 0h-105v667h105v-667z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="517" 
d="M482 0h-411v667h117v-564h294v-103zM455 341q0 -28 -21 -48.5t-49 -20.5t-49 20.5t-21 48.5q0 29 20.5 49.5t49.5 20.5t49.5 -20.5t20.5 -49.5z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="351" 
d="M173 0h-105v667h105v-667zM365 244q0 -28 -21 -48.5t-49 -20.5t-49 20.5t-21 48.5t20.5 49t49.5 21t49.5 -21t20.5 -49z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="533" 
d="M9 204v97l78 45v321h117v-253l85 49v-97l-85 -49v-214h294v-103h-411v249z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="299" 
d="M9 214v79l88 50v324h105v-263l87 50v-79l-87 -50v-325h-105v264z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="720" 
d="M539 867l-165 -144h-75l140 144h100zM649 0h-113l-348 476v-476h-117v667h120l341 -463v463h117v-667z" />
    <glyph glyph-name="nacute" unicode="&#x144;" 
d="M462 700l-165 -144h-75l140 144h100zM499 0h-105v302q0 100 -97 100q-38 0 -71 -19t-53 -46v-337h-105v483h105v-66q27 32 72 55t98 23q77 0 116.5 -40t39.5 -115v-340z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="720" 
d="M425 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM649 0h-113l-348 476v-476h-117v667h120l341 -463v463h117v-667z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" 
d="M344 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM499 0h-105v302q0 100 -97 100q-38 0 -71 -19t-53 -46v-337h-105v483h105v-66q27 32 72 55t98 23q77 0 116.5 -40
t39.5 -115v-340z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="720" 
d="M408 723h-96l-96 144h66l78 -97l74 97h67zM649 0h-113l-348 476v-476h-117v667h120l341 -463v463h117v-667z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" 
d="M334 556h-96l-96 144h66l78 -97l74 97h67zM499 0h-105v302q0 100 -97 100q-38 0 -71 -19t-53 -46v-337h-105v483h105v-66q27 32 72 55t98 23q77 0 116.5 -40t39.5 -115v-340z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" 
d="M169 680q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM499 0h-105v302q0 100 -97 100q-38 0 -71 -19t-53 -46v-337h-105v483h105v-66q27 32 72 55t98 23q77 0 116.5 -40
t39.5 -115v-340z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="720" 
d="M424 -196q-111 0 -178 70l55 89q49 -55 114 -55q48 0 79 27t36 72l-342 469v-476h-117v667h120l341 -463v463h117v-641q0 -110 -61.5 -166t-163.5 -56z" />
    <glyph glyph-name="eng" unicode="&#x14b;" 
d="M499 340v-368q0 -79 -40.5 -123.5t-119.5 -44.5q-39 0 -63 8t-50 27l31 85q30 -27 66 -27q33 0 52 19.5t19 55.5v330q0 100 -97 100q-38 0 -71 -19t-53 -46v-337h-105v483h105v-66q27 32 72 55t98 23q77 0 116.5 -40t39.5 -115z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="765" 
d="M563 733h-363v62h363v-62zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="573" 
d="M468 566h-363v62h363v-62zM286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5z
" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68zM557 789q-65 -88 -175 -88q-109 0 -176 88l48 40
q48 -66 128 -66t127 66z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="573" 
d="M286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM462 622q-65 -88 -175 -88
q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="765" 
d="M443 867l-115 -144h-63l90 144h88zM589 867l-115 -144h-63l90 144h88zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173
t60.5 -173t160.5 -68z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="573" 
d="M347 700l-115 -144h-63l90 144h88zM493 700l-115 -144h-63l90 144h88zM286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5
t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1093" 
d="M1045 0h-457v96q-34 -52 -93.5 -80t-123.5 -28q-145 0 -237.5 97.5t-92.5 247.5t92.5 247t237.5 97q64 0 123.5 -28t93.5 -79v97h457v-103h-340v-173h333v-103h-333v-185h340v-103zM588 227v212q-26 65 -79 99.5t-123 34.5q-103 0 -164 -67.5t-61 -172.5t61.5 -173
t163.5 -68q69 0 122.5 35t79.5 100z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="944" 
d="M908 207h-372q6 -57 46.5 -95t106.5 -38q83 0 138 53l48 -69q-75 -70 -196 -70q-129 0 -200 120q-69 -120 -193 -120q-112 0 -180 73.5t-68 180.5t68 180t180 73q123 0 194 -121q67 121 191 121q107 0 172 -73.5t65 -189.5v-25zM535 281h272q-3 50 -37.5 89t-99.5 39
q-62 0 -97 -39t-38 -89zM426 242q0 69 -38 114.5t-102 45.5q-63 0 -101 -45.5t-38 -114.5q0 -70 38 -116t101 -46q64 0 102 46t38 116z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="628" 
d="M500 867l-165 -144h-75l140 144h100zM587 0h-135l-148 249h-116v-249h-117v667h293q98 0 158 -58t60 -151q0 -84 -46 -134.5t-112 -61.5zM348 352q50 0 82 29t32 77t-32 77t-82 29h-160v-212h160z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="347" 
d="M378 700l-165 -144h-75l140 144h100zM173 0h-105v483h105v-71q28 36 70 59t87 23v-104q-14 3 -33 3q-33 0 -70.5 -19.5t-53.5 -44.5v-329z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="628" 
d="M381 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM587 0h-135l-148 249h-116v-249h-117v667h293q98 0 158 -58t60 -151q0 -84 -46 -134.5t-112 -61.5zM348 352
q50 0 82 29t32 77t-32 77t-82 29h-160v-212h160z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="347" 
d="M238 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM173 0h-105v483h105v-71q28 36 70 59t87 23v-104q-14 3 -33 3q-33 0 -70.5 -19.5t-53.5 -44.5v-329z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="628" 
d="M365 723h-96l-96 144h66l78 -97l74 97h67zM587 0h-135l-148 249h-116v-249h-117v667h293q98 0 158 -58t60 -151q0 -84 -46 -134.5t-112 -61.5zM348 352q50 0 82 29t32 77t-32 77t-82 29h-160v-212h160z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="347" 
d="M247 556h-96l-96 144h66l78 -97l74 97h67zM173 0h-105v483h105v-71q28 36 70 59t87 23v-104q-14 3 -33 3q-33 0 -70.5 -19.5t-53.5 -44.5v-329z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="594" 
d="M474 867l-165 -144h-75l140 144h100zM299 -12q-172 0 -271 106l65 90q86 -92 211 -92q67 0 100.5 27t33.5 64q0 38 -40 60.5t-97 35.5t-114.5 31t-97.5 61t-40 111q0 84 67.5 139.5t175.5 55.5q154 0 250 -93l-67 -87q-76 76 -193 76q-52 0 -82.5 -22.5t-30.5 -60.5
q0 -25 21.5 -42t55.5 -27.5t75.5 -20.5t83 -24.5t75.5 -35.5t55.5 -58t21.5 -88q0 -90 -65 -148t-193 -58z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="472" 
d="M413 700l-165 -144h-75l140 144h100zM233 -12q-130 0 -207 75l48 76q27 -28 73 -48t91 -20q46 0 70.5 18t24.5 47q0 26 -30 40.5t-73 23t-86 21t-73 44.5t-30 83q0 62 51 104.5t139 42.5q111 0 188 -68l-44 -74q-23 26 -61 42t-82 16q-41 0 -65.5 -16.5t-24.5 -42.5
q0 -20 21.5 -32.5t54 -18.5t70.5 -16.5t70.5 -24.5t54 -45.5t21.5 -76.5q0 -66 -53 -108t-148 -42z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="594" 
d="M432 723h-67l-77 96l-75 -96h-66l93 144h96zM299 -12q-172 0 -271 106l65 90q86 -92 211 -92q67 0 100.5 27t33.5 64q0 38 -40 60.5t-97 35.5t-114.5 31t-97.5 61t-40 111q0 84 67.5 139.5t175.5 55.5q154 0 250 -93l-67 -87q-76 76 -193 76q-52 0 -82.5 -22.5
t-30.5 -60.5q0 -25 21.5 -42t55.5 -27.5t75.5 -20.5t83 -24.5t75.5 -35.5t55.5 -58t21.5 -88q0 -90 -65 -148t-193 -58z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="472" 
d="M376 556h-67l-77 96l-75 -96h-66l93 144h96zM233 -12q-130 0 -207 75l48 76q27 -28 73 -48t91 -20q46 0 70.5 18t24.5 47q0 26 -30 40.5t-73 23t-86 21t-73 44.5t-30 83q0 62 51 104.5t139 42.5q111 0 188 -68l-44 -74q-23 26 -61 42t-82 16q-41 0 -65.5 -16.5
t-24.5 -42.5q0 -20 21.5 -32.5t54 -18.5t70.5 -16.5t70.5 -24.5t54 -45.5t21.5 -76.5q0 -66 -53 -108t-148 -42z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="594" 
d="M290 -194q-67 0 -106 31l22 46q36 -29 81 -29q23 0 39.5 9.5t16.5 26.5q0 30 -33 30q-21 0 -34 -15l-41 23l23 62q-144 11 -230 104l65 90q86 -92 211 -92q67 0 100.5 27t33.5 64q0 38 -40 60.5t-97 35.5t-114.5 31t-97.5 61t-40 111q0 84 67.5 139.5t175.5 55.5
q154 0 250 -93l-67 -87q-76 76 -193 76q-52 0 -82.5 -22.5t-30.5 -60.5q0 -25 21.5 -42t55.5 -27.5t75.5 -20.5t83 -24.5t75.5 -35.5t55.5 -58t21.5 -88q0 -87 -62 -144.5t-184 -61.5l-16 -41q16 11 36 11q29 0 48 -19.5t19 -49.5q0 -37 -31 -60t-77 -23z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="472" 
d="M232 -194q-67 0 -106 31l22 46q36 -29 81 -29q23 0 39.5 9.5t16.5 26.5q0 30 -33 30q-21 0 -34 -15l-41 23l23 61q-110 10 -174 74l48 76q27 -28 73 -48t91 -20q46 0 70.5 18t24.5 47q0 26 -30 40.5t-73 23t-86 21t-73 44.5t-30 83q0 62 51 104.5t139 42.5q111 0 188 -68
l-44 -74q-23 26 -61 42t-82 16q-41 0 -65.5 -16.5t-24.5 -42.5q0 -20 21.5 -32.5t54 -18.5t70.5 -16.5t70.5 -24.5t54 -45.5t21.5 -76.5q0 -62 -47.5 -103.5t-132.5 -45.5l-17 -42q16 11 36 11q29 0 48 -19.5t19 -49.5q0 -37 -31 -60t-77 -23z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="594" 
d="M339 723h-96l-96 144h66l78 -97l74 97h67zM299 -12q-172 0 -271 106l65 90q86 -92 211 -92q67 0 100.5 27t33.5 64q0 38 -40 60.5t-97 35.5t-114.5 31t-97.5 61t-40 111q0 84 67.5 139.5t175.5 55.5q154 0 250 -93l-67 -87q-76 76 -193 76q-52 0 -82.5 -22.5t-30.5 -60.5
q0 -25 21.5 -42t55.5 -27.5t75.5 -20.5t83 -24.5t75.5 -35.5t55.5 -58t21.5 -88q0 -90 -65 -148t-193 -58z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="472" 
d="M283 556h-96l-96 144h66l78 -97l74 97h67zM233 -12q-130 0 -207 75l48 76q27 -28 73 -48t91 -20q46 0 70.5 18t24.5 47q0 26 -30 40.5t-73 23t-86 21t-73 44.5t-30 83q0 62 51 104.5t139 42.5q111 0 188 -68l-44 -74q-23 26 -61 42t-82 16q-41 0 -65.5 -16.5t-24.5 -42.5
q0 -20 21.5 -32.5t54 -18.5t70.5 -16.5t70.5 -24.5t54 -45.5t21.5 -76.5q0 -66 -53 -108t-148 -42z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="577" 
d="M353 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM347 0h-117v564h-202v103h521v-103h-202v-564z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="319" 
d="M224 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM213 -12q-61 0 -92.5 32t-31.5 92v279h-80v92h80v132h105v-132h98v-92h-98v-253q0 -26 12 -41.5t34 -15.5
q32 0 47 17l25 -79q-33 -31 -99 -31z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="577" 
d="M338 723h-96l-96 144h66l78 -97l74 97h67zM347 0h-117v564h-202v103h521v-103h-202v-564z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="351" 
d="M369 680q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM213 -12q-61 0 -92.5 32t-31.5 92v279h-80v92h80v132h105v-132h98v-92h-98v-253q0 -26 12 -41.5t34 -15.5q32 0 47 17
l25 -79q-33 -31 -99 -31z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="577" 
d="M347 0h-117v276h-141v62h141v226h-202v103h521v-103h-202v-226h141v-62h-141v-276z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="319" 
d="M213 -12q-61 0 -92.5 32t-31.5 92v104h-80v62h80v113h-80v92h80v132h105v-132h98v-92h-98v-113h69v-62h-69v-78q0 -26 12 -41.5t34 -15.5q32 0 47 17l25 -79q-33 -31 -99 -31z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="719" 
d="M360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-403q0 -128 -73.5 -202t-214.5 -74zM419 721q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36
q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="utilde" unicode="&#x169;" 
d="M499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483zM341 554q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39
t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="719" 
d="M360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-403q0 -128 -73.5 -202t-214.5 -74zM542 733h-363v62h363v-62z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" 
d="M499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483zM464 566h-363v62h363v-62z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="719" 
d="M360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-403q0 -128 -73.5 -202t-214.5 -74zM537 789q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" 
d="M499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483zM459 622q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="719" 
d="M361 703q-42 0 -72 30t-30 72t30 72.5t72 30.5t71.5 -30.5t29.5 -72.5t-29.5 -72t-71.5 -30zM361 753q21 0 36 15.5t15 36.5q0 22 -14.5 37t-36.5 15q-21 0 -36.5 -15t-15.5 -37q0 -21 15.5 -36.5t36.5 -15.5zM360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400
q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-403q0 -128 -73.5 -202t-214.5 -74z" />
    <glyph glyph-name="uring" unicode="&#x16f;" 
d="M284 536q-42 0 -72 30t-30 72t30 72.5t72 30.5t71.5 -30.5t29.5 -72.5t-29.5 -72t-71.5 -30zM284 586q21 0 36 15.5t15 36.5q0 22 -14.5 37t-36.5 15q-21 0 -36.5 -15t-15.5 -37q0 -21 15.5 -36.5t36.5 -15.5zM499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105
v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="719" 
d="M360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-403q0 -128 -73.5 -202t-214.5 -74zM422 867l-115 -144h-63l90 144h88zM568 867l-115 -144h-63l90 144h88z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" 
d="M499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483zM344 700l-115 -144h-63l90 144h88zM490 700l-115 -144h-63l90 144h88z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="719" 
d="M360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-403q0 -103 -47.5 -171.5t-140.5 -92.5q-73 -36 -73 -93q0 -23 11 -34.5t29 -11.5q34 0 47 38l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 47 36 85h-8z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" 
d="M499 0q-73 -36 -73 -93q0 -23 11 -34.5t29 -11.5q34 0 47 38l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 55 48 97h-25v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="903" 
d="M594 723h-67l-77 96l-75 -96h-66l93 144h96zM706 0h-125l-130 492l-129 -492h-125l-191 667h131l130 -514l138 514h93l138 -514l129 514h131z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="754" 
d="M520 556h-67l-77 96l-75 -96h-66l93 144h96zM599 0h-110l-112 354l-112 -354h-110l-150 483h109l102 -351l115 351h92l115 -351l102 351h109z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="637" 
d="M462 723h-67l-77 96l-75 -96h-66l93 144h96zM377 0h-117v277l-259 390h134l184 -286l182 286h134l-258 -390v-277z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="503" 
d="M47 -189l16 94q18 -8 40 -8q51 0 69 42l24 55l-198 489h112l141 -362l141 362h113l-233 -570q-43 -107 -166 -109q-33 0 -59 7zM394 556h-67l-77 96l-75 -96h-66l93 144h96z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="637" 
d="M483 775q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM273 775q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM377 0h-117v277l-259 390h134l184 -286l182 286h134l-258 -390v-277z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="589" 
d="M473 867l-165 -144h-75l140 144h100zM544 0h-500v95l341 469h-341v103h493v-95l-341 -469h348v-103z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="476" 
d="M416 700l-165 -144h-75l140 144h100zM429 0h-383v79l239 312h-239v92h379v-77l-241 -315h245v-91z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="589" 
d="M544 0h-500v95l341 469h-341v103h493v-95l-341 -469h348v-103zM290 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="476" 
d="M429 0h-383v79l239 312h-239v92h379v-77l-241 -315h245v-91zM236 548q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="589" 
d="M339 723h-96l-96 144h66l78 -97l74 97h67zM544 0h-500v95l341 469h-341v103h493v-95l-341 -469h348v-103z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="476" 
d="M284 556h-96l-96 144h66l78 -97l74 97h67zM429 0h-383v79l239 312h-239v92h379v-77l-241 -315h245v-91z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="308" 
d="M198 0h-105v391h-80v92h80v27q0 78 41.5 122.5t111.5 44.5q75 0 116 -42l-41 -72q-23 21 -56 21q-31 0 -49 -20t-18 -54v-510z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="444" 
d="M134 -161h-119l90 406h-60v93h80l40 182q16 71 67.5 114t120.5 43t108 -40l-47 -85q-19 22 -48 22q-31 0 -54.5 -21t-31.5 -57l-36 -158h125v-93h-145z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5q157 0 255 -109q36 30 45 69q-4 -2 -13 -2q-19 0 -31.5 13.5t-12.5 33.5q0 21 14.5 36t35.5 15q24 0 40.5 -18.5t16.5 -50.5q0 -79 -68 -130q59 -89 59 -202q0 -150 -96 -247.5t-245 -97.5zM382 92q99 0 160 68.5
t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" horiz-adv-x="573" 
d="M541 531q0 -72 -62 -124q56 -68 56 -165q0 -108 -68 -181t-181 -73q-112 0 -180 73.5t-68 180.5t68 180t180 73q96 0 163 -57q36 31 42 67q-4 -2 -13 -2q-19 0 -31.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 40.5 -18.5t16.5 -50.5zM286 81q65 0 102.5 46.5t37.5 114.5
q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="719" 
d="M760 727q0 -47 -29.5 -89.5t-82.5 -65.5v-308q0 -128 -73.5 -202t-214.5 -74q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-48q24 14 41.5 36.5t20.5 45.5q-4 -2 -13 -2q-19 0 -31.5 13t-12.5 34q0 20 14.5 35t35.5 15
q24 0 40.5 -18.5t16.5 -50.5z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" 
d="M499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-31q31 30 36 63q-2 -2 -12 -2q-20 0 -32.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 41 -18.5t17 -50.5q0 -41 -23 -79t-64 -63v-399z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="959" 
d="M695 867l-165 -144h-75l140 144h100zM912 0h-457v128h-247l-77 -128h-133l410 667h504v-103h-340v-173h333v-103h-333v-185h340v-103zM455 231v318l-191 -318h191z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="878" 
d="M598 700l-165 -144h-75l140 144h100zM842 207h-351q6 -57 43.5 -94t98.5 -37q34 0 69 14t58 39l48 -71q-35 -33 -84 -51.5t-96 -18.5q-132 0 -190 98q-29 -44 -83.5 -71t-119.5 -27q-82 0 -139 41.5t-57 117.5q0 75 51 116.5t126 41.5q110 0 170 -60v71q0 41 -34 65
t-89 24q-89 0 -156 -61l-43 73q84 78 208 78q137 0 174 -93q64 93 177 93q100 0 159.5 -73.5t59.5 -189.5v-25zM490 281h250q-2 50 -33.5 89t-91.5 39q-57 0 -89.5 -39t-35.5 -89zM386 175v7q-45 51 -128 51q-50 0 -81.5 -20.5t-31.5 -58.5t31 -58t82 -20t89.5 31.5
t38.5 67.5z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="765" 
d="M562 867l-165 -144h-75l140 144h100zM382 -12q-101 0 -179 46l-25 -34h-87l55 76q-105 99 -105 257q0 150 96 247.5t245 97.5q93 0 170 -41l22 30h87l-51 -71q113 -100 113 -263q0 -150 -96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5t-60 172l-276 -383
q51 -30 115 -30zM161 333q0 -98 52 -164l275 379q-47 26 -106 26q-100 0 -160.5 -68t-60.5 -173z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="573" 
d="M466 700l-165 -144h-75l140 144h100zM118 0h-68l53 64q-65 71 -65 178t68 180t180 73q78 0 140 -39l22 27h69l-50 -61q68 -71 68 -180q0 -108 -68 -181t-181 -73q-84 0 -143 42zM286 81q65 0 102.5 46.5t37.5 114.5q0 58 -27 98l-193 -233q36 -26 80 -26zM147 242
q0 -54 24 -95l191 232q-35 23 -76 23q-64 0 -101.5 -46.5t-37.5 -113.5z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="594" 
d="M358 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM299 -12q-172 0 -271 106l65 90q86 -92 211 -92q67 0 100.5 27t33.5 64q0 38 -40 60.5t-97 35.5t-114.5 31t-97.5 61
t-40 111q0 84 67.5 139.5t175.5 55.5q154 0 250 -93l-67 -87q-76 76 -193 76q-52 0 -82.5 -22.5t-30.5 -60.5q0 -25 21.5 -42t55.5 -27.5t75.5 -20.5t83 -24.5t75.5 -35.5t55.5 -58t21.5 -88q0 -90 -65 -148t-193 -58z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="472" 
d="M304 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM233 -12q-130 0 -207 75l48 76q27 -28 73 -48t91 -20q46 0 70.5 18t24.5 47q0 26 -30 40.5t-73 23t-86 21t-73 44.5
t-30 83q0 62 51 104.5t139 42.5q111 0 188 -68l-44 -74q-23 26 -61 42t-82 16q-41 0 -65.5 -16.5t-24.5 -42.5q0 -20 21.5 -32.5t54 -18.5t70.5 -16.5t70.5 -24.5t54 -45.5t21.5 -76.5q0 -66 -53 -108t-148 -42z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="577" 
d="M353 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM347 0h-117v564h-202v103h521v-103h-202v-564z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="319" 
d="M224 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM213 -12q-61 0 -92.5 32t-31.5 92v279h-80v92h80v132h105v-132h98v-92h-98v-253q0 -26 12 -41.5t34 -15.5
q32 0 47 17l25 -79q-33 -31 -99 -31z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="241" 
d="M13 -196q-40 0 -64 8t-50 27l31 78q30 -27 67 -27q32 0 51.5 20.5t19.5 61.5v511h105v-511q0 -79 -41 -123.5t-119 -44.5z" />
    <glyph glyph-name="hsuperior" unicode="&#x2b0;" horiz-adv-x="405" 
d="M351 326h-78v194q0 63 -63 63q-46 0 -79 -39v-218h-77v434h77v-162q44 50 114 50q106 0 106 -105v-217z" />
    <glyph glyph-name="uni02BC" unicode="&#x2bc;" horiz-adv-x="244" 
d="M198 586q0 -50 -25.5 -95.5t-68.5 -75.5l-47 39q25 15 46 42t26 52q-9 -3 -17 -3q-26 0 -43 17.5t-17 45.5t20 48.5t48 20.5q32 0 55 -24.5t23 -66.5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="285" 
d="M285 556h-67l-77 96l-75 -96h-66l93 144h96z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="285" 
d="M192 556h-96l-96 144h66l78 -97l74 97h67z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="363" 
d="M363 591h-363v62h363v-62z" />
    <glyph glyph-name="uni02CB" unicode="&#x2cb;" horiz-adv-x="240" 
d="M240 556h-75l-165 144h100z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="351" 
d="M351 622q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="147" 
d="M74 548q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="203" 
d="M102 536q-42 0 -72 30t-30 72t30 72.5t72 30.5t71.5 -30.5t29.5 -72.5t-29.5 -72t-71.5 -30zM102 586q21 0 36 15.5t15 36.5q0 22 -14.5 37t-36.5 15q-21 0 -36.5 -15t-15.5 -37q0 -21 15.5 -36.5t36.5 -15.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="187" 
d="M142 -101l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 77 88 124l40 -27q-73 -36 -73 -93q0 -23 11 -34.5t29 -11.5q34 0 47 38z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="321" 
d="M218 554q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="324" 
d="M178 700l-115 -144h-63l90 144h88zM324 700l-115 -144h-63l90 144h88z" />
    <glyph glyph-name="hookabovecomb" unicode="&#x309;" horiz-adv-x="187" 
d="M55 722l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni037E" unicode="&#x37e;" horiz-adv-x="245" 
d="M122 350q-28 0 -49 21t-21 49t21 49t49 21t49 -21t21 -49t-21 -49t-49 -21zM198 41q0 -50 -25.5 -95.5t-68.5 -75.5l-47 39q25 15 46 42t26 52q-9 -3 -17 -3q-26 0 -43 17.5t-17 45.5t20 48.5t48 20.5q32 0 55 -24.5t23 -66.5z" />
    <glyph glyph-name="tonos" unicode="&#x384;" horiz-adv-x="214" 
d="M153 558h-48l20 207h93z" />
    <glyph glyph-name="dieresistonos" unicode="&#x385;" 
d="M292 558h-49l20 207h93zM485 608q0 -25 -17 -42t-42 -17q-24 0 -41 17t-17 42q0 24 17 41t41 17q25 0 42 -17t17 -41zM198 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41z" />
    <glyph glyph-name="Alphatonos" unicode="&#x386;" horiz-adv-x="673" 
d="M172 558h-48l20 207h93zM671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240z" />
    <glyph glyph-name="Epsilontonos" unicode="&#x388;" horiz-adv-x="605" 
d="M32 558h-48l20 207h93zM556 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103z" />
    <glyph glyph-name="Etatonos" unicode="&#x389;" horiz-adv-x="751" 
d="M32 558h-48l20 207h93zM680 0h-117v291h-347v-291h-117v667h117v-273h347v273h117v-667z" />
    <glyph glyph-name="Iotatonos" unicode="&#x38a;" horiz-adv-x="287" 
d="M32 558h-48l20 207h93zM216 0h-117v667h117v-667z" />
    <glyph glyph-name="Omicrontonos" unicode="&#x38c;" horiz-adv-x="765" 
d="M57 558h-48l20 207h93zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="Upsilontonos" unicode="&#x38e;" horiz-adv-x="710" 
d="M32 558h-48l20 207h93zM450 0h-117v277l-259 390h134l184 -286l182 286h134l-258 -390v-277z" />
    <glyph glyph-name="Omegatonos" unicode="&#x38f;" horiz-adv-x="772" 
d="M57 558h-48l20 207h93zM66 103h126q-64 41 -104 105.5t-40 149.5q0 142 95.5 231t245.5 89q151 0 246 -88.5t95 -231.5q0 -164 -142 -255h125v-103h-257v103q66 18 110 80.5t44 150.5q0 99 -57.5 169t-163.5 70t-163.5 -70t-57.5 -169q0 -88 44 -150.5t111 -80.5v-103
h-257v103z" />
    <glyph glyph-name="iotadieresistonos" unicode="&#x390;" horiz-adv-x="255" 
d="M136 558h-49l20 207h93zM329 608q0 -25 -17 -42t-42 -17q-24 0 -41 17t-17 42q0 24 17 41t41 17q25 0 42 -17t17 -41zM42 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM192 -12q-124 0 -124 124v371h105v-352q0 -23 11.5 -36.5
t31.5 -13.5q9 0 19 2l7 -89q-20 -6 -50 -6z" />
    <glyph glyph-name="Alpha" unicode="&#x391;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240z" />
    <glyph glyph-name="Beta" unicode="&#x392;" horiz-adv-x="648" 
d="M408 0h-337v667h328q89 0 139.5 -48.5t50.5 -121.5q0 -60 -33.5 -101.5t-82.5 -51.5q54 -8 91.5 -55.5t37.5 -108.5q0 -80 -51.5 -130t-142.5 -50zM375 391q44 0 69 24t25 62q0 39 -25 63t-69 24h-187v-173h187zM380 103q48 0 75 24.5t27 68.5q0 39 -27 65.5t-75 26.5
h-192v-185h192z" />
    <glyph glyph-name="Gamma" unicode="&#x393;" horiz-adv-x="562" 
d="M188 0h-117v667h457v-103h-340v-564z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="673" 
d="M671 0h-670l262 667h146zM506 103l-170 446l-169 -446h339z" />
    <glyph glyph-name="Epsilon" unicode="&#x395;" horiz-adv-x="577" 
d="M528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103z" />
    <glyph glyph-name="Zeta" unicode="&#x396;" horiz-adv-x="589" 
d="M544 0h-500v95l341 469h-341v103h493v-95l-341 -469h348v-103z" />
    <glyph glyph-name="Eta" unicode="&#x397;" horiz-adv-x="723" 
d="M652 0h-117v291h-347v-291h-117v667h117v-273h347v273h117v-667z" />
    <glyph glyph-name="Theta" unicode="&#x398;" horiz-adv-x="765" 
d="M550 288h-335v103h335v-103zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="Iota" unicode="&#x399;" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667z" />
    <glyph glyph-name="Kappa" unicode="&#x39a;" horiz-adv-x="621" 
d="M614 0h-144l-224 281l-58 -68v-213h-117v667h117v-318l261 318h145l-271 -315z" />
    <glyph glyph-name="Lambda" unicode="&#x39b;" horiz-adv-x="673" 
d="M671 0h-133l-202 549l-202 -549h-133l262 667h146z" />
    <glyph glyph-name="Mu" unicode="&#x39c;" horiz-adv-x="835" 
d="M764 0h-117v495l-205 -495h-50l-204 495v-495h-117v667h165l181 -439l182 439h165v-667z" />
    <glyph glyph-name="Nu" unicode="&#x39d;" horiz-adv-x="720" 
d="M649 0h-113l-348 476v-476h-117v667h120l341 -463v463h117v-667z" />
    <glyph glyph-name="Xi" unicode="&#x39e;" horiz-adv-x="620" 
d="M570 564h-521v103h521v-103zM570 0h-521v103h521v-103zM564 288h-508v103h508v-103z" />
    <glyph glyph-name="Omicron" unicode="&#x39f;" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="Pi" unicode="&#x3a0;" horiz-adv-x="723" 
d="M652 0h-117v564h-347v-564h-117v667h581v-667z" />
    <glyph glyph-name="Rho" unicode="&#x3a1;" horiz-adv-x="608" 
d="M188 0h-117v667h293q101 0 159.5 -60t58.5 -149q0 -88 -58.5 -148.5t-159.5 -60.5h-176v-249zM348 352q50 0 82 29t32 77t-32 77t-82 29h-160v-212h160z" />
    <glyph glyph-name="Sigma" unicode="&#x3a3;" horiz-adv-x="581" 
d="M195 564l212 -222l-217 -239h342v-103h-483v103l215 238l-214 223v103h482v-103h-337z" />
    <glyph glyph-name="Tau" unicode="&#x3a4;" horiz-adv-x="577" 
d="M347 0h-117v564h-202v103h521v-103h-202v-564z" />
    <glyph glyph-name="Upsilon" unicode="&#x3a5;" horiz-adv-x="637" 
d="M377 0h-117v277l-259 390h134l184 -286l182 286h134l-258 -390v-277z" />
    <glyph glyph-name="Phi" unicode="&#x3a6;" horiz-adv-x="834" 
d="M475 0h-117v66q-149 12 -233 82.5t-84 184.5q0 113 84 183.5t233 82.5v68h117v-67q148 -12 232.5 -83t84.5 -184q0 -114 -84.5 -184.5t-232.5 -82.5v-66zM672 333q0 66 -51 109t-146 53v-324q95 10 146 52.5t51 109.5zM161 333q0 -66 51 -109t146 -53v324
q-95 -10 -146 -53t-51 -109z" />
    <glyph glyph-name="Chi" unicode="&#x3a7;" horiz-adv-x="661" 
d="M657 0h-140l-187 261l-187 -261h-140l250 342l-235 325h140l172 -245l171 245h141l-234 -324z" />
    <glyph glyph-name="Psi" unicode="&#x3a8;" horiz-adv-x="836" 
d="M477 0h-117v126q-142 12 -215.5 84.5t-73.5 188.5v268h118v-264q0 -72 43.5 -116t127.5 -55v435h117v-435q83 11 126 55t43 116v264h119v-267q0 -117 -73 -189t-215 -85v-126z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="765" 
d="M59 103h126q-64 41 -104 105.5t-40 149.5q0 142 95.5 231t245.5 89q151 0 246 -88.5t95 -231.5q0 -164 -142 -255h125v-103h-257v103q66 18 110 80.5t44 150.5q0 99 -57.5 169t-163.5 70t-163.5 -70t-57.5 -169q0 -88 44 -150.5t111 -80.5v-103h-257v103z" />
    <glyph glyph-name="Iotadieresis" unicode="&#x3aa;" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667zM293 792q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM83 792q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41z" />
    <glyph glyph-name="Upsilondieresis" unicode="&#x3ab;" horiz-adv-x="637" 
d="M377 0h-117v277l-259 390h134l184 -286l182 286h134l-258 -390v-277zM481 792q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM271 792q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41z" />
    <glyph glyph-name="alphatonos" unicode="&#x3ac;" horiz-adv-x="595" 
d="M311 558h-48l20 207h93zM575 83l7 -89q-20 -6 -50 -6q-99 0 -118 87q-25 -39 -68 -63t-93 -24q-95 0 -155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-352q0 -23 11.5 -36.5t31.5 -13.5q9 0 19 2zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5
q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5z" />
    <glyph glyph-name="epsilontonos" unicode="&#x3ad;" horiz-adv-x="508" 
d="M267 558h-48l20 207h93zM266 -12q-105 0 -166.5 40t-61.5 103q0 52 43.5 82t94.5 34q-51 7 -89.5 38.5t-38.5 77.5q0 61 61.5 96.5t168.5 34.5q125 0 202 -79l-52 -64q-58 58 -155 58q-50 0 -83 -17t-33 -43q0 -31 31.5 -47t86.5 -16h103v-84h-103q-127 0 -127 -63
q0 -30 31.5 -47.5t86.5 -17.5q107 0 173 59l49 -67q-80 -78 -222 -78z" />
    <glyph glyph-name="etatonos" unicode="&#x3ae;" 
d="M297 558h-48l20 207h93zM499 -184h-105v486q0 100 -97 100q-38 0 -71 -19t-53 -46v-337h-105v483h105v-66q27 32 72 55t98 23q77 0 116.5 -40t39.5 -115v-524z" />
    <glyph glyph-name="iotatonos" unicode="&#x3af;" horiz-adv-x="255" 
d="M141 558h-48l20 207h93zM192 -12q-124 0 -124 124v371h105v-352q0 -23 11.5 -36.5t31.5 -13.5q9 0 19 2l7 -89q-20 -6 -50 -6z" />
    <glyph glyph-name="upsilondieresistonos" unicode="&#x3b0;" horiz-adv-x="570" 
d="M293 558h-49l20 207h93zM486 608q0 -25 -17 -42t-42 -17q-24 0 -41 17t-17 42q0 24 17 41t41 17q25 0 42 -17t17 -41zM199 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM283 -12q-105 0 -160 61.5t-55 165.5v268h105v-266
q0 -62 29 -99t81 -37q66 0 103 48.5t37 125.5q0 116 -65 196l95 44q79 -105 79 -240q0 -117 -67.5 -192t-181.5 -75z" />
    <glyph glyph-name="alpha" unicode="&#x3b1;" horiz-adv-x="595" 
d="M575 83l7 -89q-20 -6 -50 -6q-99 0 -118 87q-25 -39 -68 -63t-93 -24q-95 0 -155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-352q0 -23 11.5 -36.5t31.5 -13.5q9 0 19 2zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5
t37 -115.5t99 -44.5z" />
    <glyph glyph-name="beta" unicode="&#x3b2;" horiz-adv-x="581" 
d="M173 451v-306q20 -28 55.5 -46t76.5 -18q61 0 95 29t34 79t-37 77t-96 27h-38v93h35q54 0 87 25.5t33 72.5q0 45 -31.5 72t-79.5 27q-59 0 -96.5 -35t-37.5 -97zM173 66v-250h-105v635q0 112 71.5 169t167.5 57q101 0 160.5 -48t59.5 -132q0 -62 -41.5 -102.5
t-97.5 -50.5q57 -5 105.5 -50t48.5 -123q0 -79 -59 -131t-144 -52q-106 0 -166 78z" />
    <glyph glyph-name="gamma" unicode="&#x3b3;" horiz-adv-x="503" 
d="M304 -184h-105v119q0 155 -59.5 303t-151.5 245h120q58 -63 104 -167.5t56 -191.5q45 66 76 167t31 192h105q0 -117 -49 -248t-127 -230v-189z" />
    <glyph glyph-name="delta" unicode="&#x3b4;" horiz-adv-x="573" 
d="M292 -12q-115 0 -184.5 71t-69.5 169q0 69 34.5 120.5t90.5 78.5q-67 36 -67 105q0 62 51 104.5t139 42.5q112 0 187 -68l-44 -74q-51 58 -143 58q-41 0 -65 -16.5t-24 -42.5q0 -18 18.5 -30.5t48.5 -22t66 -19.5t72 -28t66 -43t48.5 -68.5t18.5 -100.5q0 -96 -66 -166
t-177 -70zM433 224q0 38 -13 65.5t-40 46t-50 28.5t-62 23q-59 -21 -90 -63t-31 -96q0 -58 38.5 -102.5t103.5 -44.5t104.5 44t39.5 99z" />
    <glyph glyph-name="epsilon" unicode="&#x3b5;" horiz-adv-x="508" 
d="M266 -12q-105 0 -166.5 40t-61.5 103q0 52 43.5 82t94.5 34q-51 7 -89.5 38.5t-38.5 77.5q0 61 61.5 96.5t168.5 34.5q125 0 202 -79l-52 -64q-58 58 -155 58q-50 0 -83 -17t-33 -43q0 -31 31.5 -47t86.5 -16h103v-84h-103q-127 0 -127 -63q0 -30 31.5 -47.5t86.5 -17.5
q107 0 173 59l49 -67q-80 -78 -222 -78z" />
    <glyph glyph-name="zeta" unicode="&#x3b6;" horiz-adv-x="487" 
d="M345 -40q0 28 -43 28q-120 0 -192 60t-72 163q0 109 72 202t200 162h-256v92h392v-77q-136 -76 -217.5 -171.5t-81.5 -199.5q0 -68 45 -103t123 -35q74 0 106.5 -23.5t32.5 -74.5q0 -53 -49 -140h-112q52 76 52 117z" />
    <glyph glyph-name="eta" unicode="&#x3b7;" 
d="M499 -184h-105v486q0 100 -97 100q-38 0 -71 -19t-53 -46v-337h-105v483h105v-66q27 32 72 55t98 23q77 0 116.5 -40t39.5 -115v-524z" />
    <glyph glyph-name="theta" unicode="&#x3b8;" horiz-adv-x="573" 
d="M286 -12q-79 0 -136 47.5t-84.5 125t-27.5 173.5t27.5 173t84.5 124.5t136 47.5q119 0 184 -98t65 -247t-65 -247.5t-184 -98.5zM286 81q127 0 139 208h-277q12 -208 138 -208zM286 586q-125 0 -138 -205h276q-12 205 -138 205z" />
    <glyph glyph-name="iota" unicode="&#x3b9;" horiz-adv-x="255" 
d="M192 -12q-124 0 -124 124v371h105v-352q0 -23 11.5 -36.5t31.5 -13.5q9 0 19 2l7 -89q-20 -6 -50 -6z" />
    <glyph glyph-name="kappa" unicode="&#x3ba;" horiz-adv-x="526" 
d="M520 0h-132l-148 199l-67 -69v-130h-105v483h105v-233l213 233h130l-201 -219z" />
    <glyph glyph-name="lambda" unicode="&#x3bb;" horiz-adv-x="503" 
d="M63 577l-16 94q24 8 59 8q60 -2 101.5 -26.5t64.5 -82.5l233 -570h-113l-141 362l-141 -362h-112l198 488l-24 55q-18 43 -69 43q-23 0 -40 -9z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="581" 
d="M173 24v-208h-105v667h105v-304q0 -99 98 -99q37 0 70 18.5t53 45.5v339h105v-352q0 -23 11.5 -36.5t31.5 -13.5q9 0 19 2l7 -89q-20 -6 -50 -6q-97 0 -119 86q-25 -38 -62.5 -62t-80.5 -24q-60 0 -83 36z" />
    <glyph glyph-name="nu" unicode="&#x3bd;" horiz-adv-x="503" 
d="M300 0h-105l-197 483h112l146 -378q55 80 87 182t32 196h105q0 -118 -50.5 -251t-129.5 -232z" />
    <glyph glyph-name="xi" unicode="&#x3be;" horiz-adv-x="487" 
d="M302 -12q-127 0 -195.5 48t-68.5 133q0 64 38.5 103.5t89.5 51.5q-46 8 -81.5 39t-35.5 82q0 42 28.5 78t72.5 52h-92v92h382v-92h-189q-40 -10 -66.5 -38t-26.5 -62q0 -43 37 -66.5t95 -23.5h145v-90h-145q-65 0 -104 -27t-39 -80q0 -51 44 -79t124 -28
q74 0 106.5 -23.5t32.5 -74.5q0 -53 -49 -140h-112q52 76 52 117q0 28 -43 28z" />
    <glyph glyph-name="omicron" unicode="&#x3bf;" horiz-adv-x="573" 
d="M286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="610" 
d="M521 0h-105v391h-222v-391h-105v391h-80v92h592v-92h-80v-391z" />
    <glyph glyph-name="rho" unicode="&#x3c1;" horiz-adv-x="578" 
d="M328 -12q-96 0 -155 79v-251h-105v426q0 107 64.5 180t171.5 73q108 0 173 -73t65 -180q0 -117 -59.5 -185.5t-154.5 -68.5zM297 81q62 0 99 45t37 116q0 67 -35 113.5t-95 46.5t-95 -46.5t-35 -113.5v-96q18 -27 53 -46t71 -19z" />
    <glyph glyph-name="sigma1" unicode="&#x3c2;" horiz-adv-x="488" 
d="M308 -12q-121 0 -195.5 68.5t-74.5 185.5q0 110 70 181.5t180 71.5q121 0 185 -86l-69 -64q-40 57 -111 57q-66 0 -106 -44.5t-40 -115.5q0 -78 45.5 -119.5t120.5 -41.5q145 0 145 -98q0 -53 -49 -140h-109q52 79 52 117q0 28 -44 28z" />
    <glyph glyph-name="sigma" unicode="&#x3c3;" horiz-adv-x="573" 
d="M527 219q0 -98 -64.5 -164.5t-176.5 -66.5t-180 73.5t-68 180.5q0 103 69.5 172t188.5 69h255v-93h-101q77 -63 77 -171zM426 238q0 51 -22.5 90.5t-60.5 61.5h-47q-70 0 -109.5 -43.5t-39.5 -104.5q0 -68 37.5 -114.5t101.5 -46.5q65 0 102.5 46t37.5 111z" />
    <glyph glyph-name="tau" unicode="&#x3c4;" horiz-adv-x="476" 
d="M309 -12q-61 0 -92.5 32t-31.5 92v279h-162v92h430v-92h-163v-253q0 -26 12 -41.5t34 -15.5q32 0 47 17l25 -79q-33 -31 -99 -31z" />
    <glyph glyph-name="upsilon" unicode="&#x3c5;" horiz-adv-x="570" 
d="M283 -12q-105 0 -160 61.5t-55 165.5v268h105v-266q0 -62 29 -99t81 -37q66 0 103 48.5t37 125.5q0 116 -65 196l95 44q79 -105 79 -240q0 -117 -67.5 -192t-181.5 -75z" />
    <glyph glyph-name="phi" unicode="&#x3c6;" horiz-adv-x="773" 
d="M734 242q0 -109 -74.5 -175.5t-220.5 -76.5v-174h-105v174q-147 10 -221.5 79t-74.5 186q0 83 50.5 143.5t132.5 96.5l44 -82q-57 -26 -87.5 -66.5t-30.5 -91.5q0 -154 187 -172v412h52q172 0 260 -67.5t88 -185.5zM625 242q0 68 -48.5 110.5t-137.5 46.5v-316
q95 9 140.5 51.5t45.5 107.5z" />
    <glyph glyph-name="chi" unicode="&#x3c7;" horiz-adv-x="496" 
d="M491 -184h-116l-127 245l-128 -245h-115l177 343l-167 324h116l117 -226l116 226h116l-167 -324z" />
    <glyph glyph-name="psi" unicode="&#x3c8;" horiz-adv-x="773" 
d="M439 -184h-105v174q-132 10 -199 73.5t-67 168.5v251h105v-251q0 -62 42.5 -100.5t118.5 -47.5v583h105v-583q76 9 118.5 47t42.5 101v251h105v-251q0 -105 -67.5 -168.5t-198.5 -73.5v-174z" />
    <glyph glyph-name="omega" unicode="&#x3c9;" horiz-adv-x="833" 
d="M794 242q0 -108 -55.5 -181t-147.5 -73q-66 0 -109.5 39t-65.5 102q-49 -141 -175 -141q-92 0 -147.5 73t-55.5 181q0 81 27 146.5t75 106.5l78 -61q-71 -70 -71 -192q0 -68 29 -114.5t79 -46.5q51 0 80 46.5t29 114.5v122h105v-122q0 -68 29 -114.5t79 -46.5t79 46.5
t29 114.5q0 122 -71 192l79 61q101 -87 101 -253z" />
    <glyph glyph-name="iotadieresis" unicode="&#x3ca;" horiz-adv-x="255" 
d="M291 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM81 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM192 -12q-124 0 -124 124v371h105v-352q0 -23 11.5 -36.5t31.5 -13.5
q9 0 19 2l7 -89q-20 -6 -50 -6z" />
    <glyph glyph-name="upsilondieresis" unicode="&#x3cb;" horiz-adv-x="570" 
d="M448 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM238 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM283 -12q-105 0 -160 61.5t-55 165.5v268h105v-266q0 -62 29 -99t81 -37
q66 0 103 48.5t37 125.5q0 116 -65 196l95 44q79 -105 79 -240q0 -117 -67.5 -192t-181.5 -75z" />
    <glyph glyph-name="omicrontonos" unicode="&#x3cc;" horiz-adv-x="573" 
d="M300 558h-48l20 207h93zM286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5z" />
    <glyph glyph-name="upsilontonos" unicode="&#x3cd;" horiz-adv-x="570" 
d="M299 558h-48l20 207h93zM283 -12q-105 0 -160 61.5t-55 165.5v268h105v-266q0 -62 29 -99t81 -37q66 0 103 48.5t37 125.5q0 116 -65 196l95 44q79 -105 79 -240q0 -117 -67.5 -192t-181.5 -75z" />
    <glyph glyph-name="omegatonos" unicode="&#x3ce;" horiz-adv-x="833" 
d="M430 558h-48l20 207h93zM794 242q0 -108 -55.5 -181t-147.5 -73q-66 0 -109.5 39t-65.5 102q-49 -141 -175 -141q-92 0 -147.5 73t-55.5 181q0 81 27 146.5t75 106.5l78 -61q-71 -70 -71 -192q0 -68 29 -114.5t79 -46.5q51 0 80 46.5t29 114.5v122h105v-122
q0 -68 29 -114.5t79 -46.5t79 46.5t29 114.5q0 122 -71 192l79 61q101 -87 101 -253z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="577" 
d="M462 775q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM252 775q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="764" 
d="M504 -12v104q54 0 85.5 28t31.5 74v10q0 53 -35 82t-100 29q-76 0 -139 -25v-290h-117v564h-202v103h521v-103h-202v-171q70 25 151 25q117 0 178.5 -61t61.5 -153v-10q0 -100 -66 -153t-168 -53z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="562" 
d="M460 867l-165 -144h-75l140 144h100zM188 0h-117v667h457v-103h-340v-564z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="682" 
d="M391 -12q-150 0 -250 96.5t-100 248.5t100 248.5t250 96.5q174 0 268 -150l-100 -51q-25 43 -70.5 70t-97.5 27q-84 0 -145.5 -50.5t-78.5 -132.5h354v-103h-357q14 -87 76.5 -141.5t150.5 -54.5q53 0 98 27t70 70l100 -51q-96 -150 -268 -150z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="594" 
d="M299 -12q-172 0 -271 106l65 90q86 -92 211 -92q67 0 100.5 27t33.5 64q0 38 -40 60.5t-97 35.5t-114.5 31t-97.5 61t-40 111q0 84 67.5 139.5t175.5 55.5q154 0 250 -93l-67 -87q-76 76 -193 76q-52 0 -82.5 -22.5t-30.5 -60.5q0 -25 21.5 -42t55.5 -27.5t75.5 -20.5
t83 -24.5t75.5 -35.5t55.5 -58t21.5 -88q0 -90 -65 -148t-193 -58z" />
    <glyph glyph-name="afii10055" unicode="&#x406;" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667zM293 775q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM83 775q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="481" 
d="M185 -12q-111 0 -177 70l54 89q51 -55 114 -55q54 0 85.5 33t31.5 88v454h117v-456q0 -110 -62 -166.5t-163 -56.5z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="1072" 
d="M28 -12v104q58 0 87 45.5t45 186.5l39 343h453v-249h176q101 0 159.5 -60.5t58.5 -148.5q0 -89 -58.5 -149t-159.5 -60h-293v564h-231l-28 -248q-12 -103 -33.5 -170t-55 -100.5t-70 -45.5t-89.5 -12zM812 315h-160v-212h160q50 0 82 29t32 77t-32 77t-82 29z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="1072" 
d="M71 0v667h117v-273h347v273h117v-273h189q95 0 150 -56.5t55 -140.5t-55 -140.5t-150 -56.5h-306v291h-347v-291h-117zM652 103h173q45 0 73 26t28 68t-28 68t-73 26h-173v-188z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="790" 
d="M621 0v177q0 138 -135 138q-76 0 -139 -25v-290h-117v564h-202v103h521v-103h-202v-171q70 25 151 25q119 0 179.5 -64.5t60.5 -176.5v-177h-117z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="621" 
d="M493 867l-165 -144h-75l140 144h100zM614 0h-144l-224 281l-58 -68v-213h-117v667h117v-318l261 318h145l-271 -315z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="637" 
d="M151 -12q-42 0 -81 14t-56 32l42 96q43 -38 87 -38q39 0 61.5 18.5t47.5 66.5l-251 490h133l184 -389l184 389h133l-278 -542q-35 -68 -81 -102.5t-125 -34.5zM494 789q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="723" 
d="M71 0v667h117v-564h347v564h117v-667h-232v-128h-117v128h-232z" />
    <glyph glyph-name="afii10017" unicode="&#x410;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" horiz-adv-x="608" 
d="M71 667h457v-103h-340v-146h176q101 0 159.5 -60.5t58.5 -148.5q0 -89 -58.5 -149t-159.5 -60h-293v667zM348 315h-160v-212h160q50 0 82 29t32 77t-32 77t-82 29z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="648" 
d="M408 0h-337v667h328q89 0 139.5 -48.5t50.5 -121.5q0 -60 -33.5 -101.5t-82.5 -51.5q54 -8 91.5 -55.5t37.5 -108.5q0 -80 -51.5 -130t-142.5 -50zM375 391q44 0 69 24t25 62q0 39 -25 63t-69 24h-187v-173h187zM380 103q48 0 75 24.5t27 68.5q0 39 -27 65.5t-75 26.5
h-192v-185h192z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="562" 
d="M188 0h-117v667h457v-103h-340v-564z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="732" 
d="M28 -123v215q40 4 62 20t40.5 66t29.5 146l39 343h453v-564h62v-226h-117v123h-452v-123h-117zM276 316q-17 -148 -88 -213h347v461h-231z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="577" 
d="M528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="933" 
d="M151 0h-144l291 352l-271 315h145l236 -288v288h117v-288l236 288h145l-271 -315l291 -352h-144l-224 281l-33 -39v-242h-117v242l-33 39z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="599" 
d="M288 -12q-86 0 -157.5 30.5t-106.5 77.5l63 77q32 -37 87 -59t110 -22q71 0 111.5 27.5t40.5 74.5q0 94 -161 94h-134v103h134q67 0 108.5 22t41.5 66q0 45 -42 69.5t-106 24.5q-110 0 -184 -74l-60 73q40 47 107.5 76t147.5 29q111 0 182.5 -49t71.5 -131
q0 -62 -43.5 -102.5t-101.5 -50.5q57 -5 106 -48t49 -114q0 -85 -74.5 -139.5t-189.5 -54.5z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="720" 
d="M184 0h-113v667h117v-463l341 463h120v-667h-117v476z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="720" 
d="M184 0h-113v667h117v-463l341 463h120v-667h-117v476zM535 789q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="621" 
d="M614 0h-144l-224 281l-58 -68v-213h-117v667h117v-318l261 318h145l-271 -315z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="723" 
d="M28 -12v104q58 0 87 45.5t45 186.5l39 343h453v-667h-117v564h-231l-28 -248q-12 -103 -33.5 -170t-55 -100.5t-70 -45.5t-89.5 -12z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="835" 
d="M764 0h-117v495l-205 -495h-50l-204 495v-495h-117v667h165l181 -439l182 439h165v-667z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="723" 
d="M652 0h-117v291h-347v-291h-117v667h117v-273h347v273h117v-667z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="723" 
d="M652 0h-117v564h-347v-564h-117v667h581v-667z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" horiz-adv-x="608" 
d="M188 0h-117v667h293q101 0 159.5 -60t58.5 -149q0 -88 -58.5 -148.5t-159.5 -60.5h-176v-249zM348 352q50 0 82 29t32 77t-32 77t-82 29h-160v-212h160z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="682" 
d="M391 -12q-150 0 -250 96.5t-100 248.5t100 248.5t250 96.5q174 0 268 -150l-100 -51q-25 43 -70.5 70t-97.5 27q-99 0 -164.5 -68t-65.5 -173t65.5 -173t164.5 -68q53 0 98 27t70 70l100 -51q-96 -150 -268 -150z" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="577" 
d="M347 0h-117v564h-202v103h521v-103h-202v-564z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="637" 
d="M151 -12q-42 0 -81 14t-56 32l42 96q43 -38 87 -38q39 0 61.5 18.5t47.5 66.5l-251 490h133l184 -389l184 389h133l-278 -542q-35 -68 -81 -102.5t-125 -34.5z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="834" 
d="M475 0h-117v66q-149 12 -233 82.5t-84 184.5q0 113 84 183.5t233 82.5v68h117v-67q148 -12 232.5 -83t84.5 -184q0 -114 -84.5 -184.5t-232.5 -82.5v-66zM672 333q0 66 -51 109t-146 53v-324q95 10 146 52.5t51 109.5zM161 333q0 -66 51 -109t146 -53v324
q-95 -10 -146 -53t-51 -109z" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="661" 
d="M657 0h-140l-187 261l-187 -261h-140l250 342l-235 325h140l172 -245l171 245h141l-234 -324z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="732" 
d="M597 -123v123h-526v667h117v-564h347v564h117v-564h62v-226h-117z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="631" 
d="M169 667v-177q0 -138 135 -138q76 0 139 25v290h117v-667h-117v274q-70 -25 -151 -25q-119 0 -179.5 64.5t-60.5 176.5v177h117z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="953" 
d="M765 667h117v-667h-811v667h117v-564h230v564h117v-564h230v564z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="962" 
d="M765 667h117v-564h62v-226h-117v123h-756v667h117v-564h230v564h117v-564h230v564z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="767" 
d="M523 0h-293v564h-202v103h319v-249h176q101 0 159.5 -60.5t58.5 -148.5q0 -89 -58.5 -149t-159.5 -60zM507 315h-160v-212h160q50 0 82 29t32 77t-32 77t-82 29z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="832" 
d="M71 667h117v-249h176q101 0 159.5 -60.5t58.5 -148.5q0 -89 -58.5 -149t-159.5 -60h-293v667zM348 315h-160v-212h160q50 0 82 29t32 77t-32 77t-82 29zM761 0h-117v667h117v-667z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" horiz-adv-x="608" 
d="M71 667h117v-249h176q101 0 159.5 -60.5t58.5 -148.5q0 -89 -58.5 -149t-159.5 -60h-293v667zM348 315h-160v-212h160q50 0 82 29t32 77t-32 77t-82 29z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="682" 
d="M291 -12q-172 0 -268 150l100 51q25 -43 70 -70t98 -27q88 0 150 54.5t76 141.5h-356v103h354q-17 82 -78.5 132.5t-145.5 50.5q-52 0 -97.5 -27t-70.5 -70l-100 51q94 150 268 150q150 0 250 -96.5t100 -248.5t-100 -248.5t-250 -96.5z" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="1015" 
d="M632 -12q-138 0 -231.5 84t-107.5 219h-105v-291h-117v667h117v-273h107q20 126 112 205t225 79q149 0 245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM632 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="afii10049" unicode="&#x42f;" horiz-adv-x="628" 
d="M176 0h-135l163 262q-66 11 -112 61.5t-46 134.5q0 93 60 151t158 58h293v-667h-117v249h-116zM280 352h160v212h-160q-50 0 -82 -29t-32 -77t32 -77t82 -29z" />
    <glyph glyph-name="afii10065" unicode="&#x430;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="577" 
d="M286 81q65 0 102.5 47t37.5 116q0 66 -37.5 112t-102.5 46q-64 0 -101.5 -46t-37.5 -112q0 -69 37.5 -116t101.5 -47zM286 -12q-114 0 -180.5 77.5t-66.5 198.5q0 173 59.5 258t183.5 100q75 11 103.5 21t28.5 24h103q0 -105 -206 -128q-81 -9 -124.5 -44.5t-54.5 -84.5
q29 40 75.5 62.5t98.5 22.5q100 0 164 -74t64 -178q0 -107 -67 -181t-181 -74z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="572" 
d="M364 0h-296v483h289q67 0 105 -35t38 -89q0 -42 -23 -71t-58 -39q39 -9 65 -42.5t26 -75.5q0 -58 -38.5 -94.5t-107.5 -36.5zM342 92q28 0 44 14.5t16 39.5q0 23 -16 39t-44 16h-169v-109h169zM339 293q25 0 39.5 13.5t14.5 34.5q0 23 -14.5 36.5t-39.5 13.5h-166v-98
h166z" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="452" 
d="M434 483v-92h-261v-391h-105v483h366z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="571" 
d="M18 -123v210q34 8 51.5 39.5t28.5 121.5l29 235h373v-391h53v-215h-105v123h-325v-123h-105zM220 391l-17 -155q-10 -96 -64 -144h256v299h-175z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="557" 
d="M291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="731" 
d="M138 0h-132l205 264l-201 219h130l173 -189v189h105v-189l173 189h130l-201 -219l205 -264h-132l-148 199l-27 -28v-171h-105v171l-27 28z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="508" 
d="M242 -12q-142 0 -222 78l49 67q66 -59 173 -59q55 0 86.5 17.5t31.5 47.5q0 63 -127 63h-103v84h103q55 0 86.5 16t31.5 47q0 26 -33 43t-83 17q-97 0 -155 -58l-52 64q77 79 202 79q107 1 168.5 -34.5t61.5 -96.5q0 -46 -38.5 -77.5t-89.5 -38.5q51 -4 94.5 -34
t43.5 -82q0 -63 -61.5 -103t-166.5 -40z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="568" 
d="M169 0h-101v483h105v-325l222 325h105v-483h-105v334z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="568" 
d="M169 0h-101v483h105v-325l222 325h105v-483h-105v334zM459 622q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="526" 
d="M520 0h-132l-148 199l-67 -69v-130h-105v483h105v-233l213 233h130l-201 -219z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="568" 
d="M18 -12v86q31 0 49.5 40t30.5 134l29 235h373v-483h-105v391h-175l-17 -155q-14 -130 -59 -189t-126 -59z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="677" 
d="M609 0h-105v336l-144 -336h-42l-145 336v-336h-105v483h137l134 -312l132 312h138v-483z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" horiz-adv-x="568" 
d="M173 0h-105v483h105v-190h222v190h105v-483h-105v201h-222v-201z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" horiz-adv-x="573" 
d="M286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" horiz-adv-x="568" 
d="M500 0h-105v391h-222v-391h-105v483h432v-483z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" horiz-adv-x="578" 
d="M328 -12q-96 0 -155 79v-251h-105v667h105v-66q58 78 155 78q95 0 154.5 -68t59.5 -185t-59.5 -185.5t-154.5 -68.5zM297 81q62 0 99 45t37 116t-37 115.5t-99 44.5q-36 0 -71 -19t-53 -46v-191q18 -27 53 -46t71 -19z" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="497" 
d="M288 -12q-110 0 -180 71.5t-70 182.5q0 110 70 181.5t180 71.5q121 0 185 -86l-69 -64q-40 57 -111 57q-66 0 -106 -44.5t-40 -115.5t40 -116t106 -45q69 0 111 57l69 -64q-64 -86 -185 -86z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="418" 
d="M261 0h-105v391h-138v92h382v-92h-139v-391z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="503" 
d="M47 -189l16 94q18 -8 40 -8q51 0 69 42l24 55l-198 489h112l141 -362l141 362h113l-233 -570q-43 -107 -166 -109q-33 0 -59 7z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="833" 
d="M234 495q80 0 130 -79v251h105v-250q49 78 130 78q80 0 130 -68t50 -185t-50 -185.5t-130 -68.5t-130 79v-251h-105v250q-49 -78 -130 -78q-80 0 -130 68t-50 185t50 185.5t130 68.5zM267 402q-48 0 -77.5 -45t-29.5 -116t29.5 -115.5t77.5 -44.5q28 0 55.5 19t41.5 46
v191q-14 27 -41.5 46t-55.5 19zM566 81q48 0 77.5 45t29.5 116t-29.5 115.5t-77.5 44.5q-28 0 -55.5 -19t-41.5 -46v-191q14 -27 41.5 -46t55.5 -19z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="496" 
d="M491 0h-118l-125 178l-126 -178h-117l177 248l-167 235h118l115 -164l114 164h118l-167 -235z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="571" 
d="M448 -123v123h-380v483h105v-391h222v391h105v-391h53v-215h-105z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="550" 
d="M51 483h105v-130q0 -54 27.5 -76t87.5 -22q65 0 106 18v210h105v-483h-105v188q-52 -26 -139 -26q-99 0 -143 38t-44 115v168z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="791" 
d="M618 483h105v-483h-655v483h105v-391h170v391h105v-391h170v391z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="804" 
d="M618 483h105v-391h63v-215h-105v123h-613v483h105v-391h170v391h105v-391h170v391z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="628" 
d="M432 0h-276v391h-138v92h243v-175h171q77 0 118.5 -44t41.5 -110q0 -65 -42.5 -109.5t-117.5 -44.5zM419 216h-158v-124h158q31 0 48.5 17t17.5 45t-17.5 45t-48.5 17z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="750" 
d="M68 483h105v-175h171q77 0 118.5 -44t41.5 -110q0 -65 -42.5 -109.5t-117.5 -44.5h-276v483zM331 216h-158v-124h158q31 0 48.5 17t17.5 45t-17.5 45t-48.5 17zM682 0h-105v483h105v-483z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="540" 
d="M68 483h105v-175h171q77 0 118.5 -44t41.5 -110q0 -65 -42.5 -109.5t-117.5 -44.5h-276v483zM331 216h-158v-124h158q31 0 48.5 17t17.5 45t-17.5 45t-48.5 17z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="497" 
d="M209 -12q-121 0 -185 86l65 61q45 -61 115 -61q56 0 93.5 32.5t48.5 91.5h-218v91h217q-11 56 -48.5 88t-92.5 32q-72 0 -115 -61l-65 61q64 86 185 86q110 0 180 -71.5t70 -181.5q0 -111 -70 -182.5t-180 -71.5z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="789" 
d="M502 -12q-101 0 -166.5 60t-78.5 153h-84v-201h-105v483h105v-190h85q16 89 81.5 145.5t162.5 56.5q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM502 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5
q0 -68 37.5 -114.5t101.5 -46.5z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" horiz-adv-x="561" 
d="M171 0h-120l119 180q-50 11 -81.5 50t-31.5 99q0 65 42.5 109.5t117.5 44.5h276v-483h-105v175h-111zM230 267h158v124h-158q-30 0 -48 -17.5t-18 -44.5q0 -28 17.5 -45t48.5 -17z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="557" 
d="M447 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM237 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5
q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="571" 
d="M327 531h-151v-114q27 32 72.5 55t98.5 23q156 0 156 -153v-370q0 -79 -41 -123.5t-119 -44.5q-40 0 -64 8t-50 27l31 78q30 -27 67 -27q32 0 51.5 20.5t19.5 61.5v332q0 53 -25.5 75.5t-72.5 22.5q-37 0 -70 -19t-54 -46v-337h-105v531h-62v62h62v74h105v-74h151v-62z
" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="452" 
d="M431 700l-165 -144h-75l140 144h100zM434 483v-92h-261v-391h-105v483h366z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="497" 
d="M288 -12q-110 0 -180 71.5t-70 182.5q0 110 70 181.5t180 71.5q121 0 185 -86l-65 -61q-43 61 -115 61q-55 0 -93 -32t-49 -88h218v-91h-219q10 -58 48.5 -91t94.5 -33q70 0 115 61l65 -61q-64 -86 -185 -86z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="472" 
d="M233 -12q-130 0 -207 75l48 76q27 -28 73 -48t91 -20q46 0 70.5 18t24.5 47q0 26 -30 40.5t-73 23t-86 21t-73 44.5t-30 83q0 62 51 104.5t139 42.5q111 0 188 -68l-44 -74q-23 26 -61 42t-82 16q-41 0 -65.5 -16.5t-24.5 -42.5q0 -20 21.5 -32.5t54 -18.5t70.5 -16.5
t70.5 -24.5t54 -45.5t21.5 -76.5q0 -66 -53 -108t-148 -42z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="241" 
d="M120 542q-26 0 -45.5 19t-19.5 46t19.5 46t45.5 19q27 0 46 -19t19 -46t-19 -46t-46 -19zM173 0h-105v483h105v-483z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="241" 
d="M284 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM74 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM173 0h-105v483h105v-483z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="241" 
d="M120 542q-26 0 -45.5 19t-19.5 46t19.5 46t45.5 19q27 0 46 -19t19 -46t-19 -46t-46 -19zM13 -196q-40 0 -64 8t-50 27l31 78q30 -27 67 -27q32 0 51.5 20.5t19.5 61.5v511h105v-511q0 -79 -41 -123.5t-119 -44.5z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="867" 
d="M18 -12v86q31 0 49.5 40t30.5 134l29 235h373v-175h171q77 0 118.5 -44t41.5 -110q0 -65 -42.5 -109.5t-117.5 -44.5h-276v391h-175l-17 -155q-14 -130 -59 -189t-126 -59zM658 216h-158v-124h158q31 0 48.5 17t17.5 45t-17.5 45t-48.5 17z" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="867" 
d="M173 0h-105v483h105v-190h222v190h105v-190h179q72 0 112 -42t40 -105q0 -62 -40.5 -104t-111.5 -42h-284v201h-222v-201zM666 201h-166v-109h166q27 0 42.5 15t15.5 39q0 25 -15.5 40t-42.5 15z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="571" 
d="M327 531h-151v-114q27 32 72.5 55t98.5 23q156 0 156 -153v-342h-105v304q0 53 -25.5 75.5t-72.5 22.5q-37 0 -70 -19t-54 -46v-337h-105v531h-62v62h62v74h105v-74h151v-62z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" horiz-adv-x="526" 
d="M445 700l-165 -144h-75l140 144h100zM520 0h-132l-148 199l-67 -69v-130h-105v483h105v-233l213 233h130l-201 -219z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="503" 
d="M47 -189l16 94q18 -8 40 -8q51 0 69 42l24 55l-198 489h112l141 -362l141 362h113l-233 -570q-43 -107 -166 -109q-33 0 -59 7zM427 622q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="568" 
d="M232 -127v127h-164v483h105v-391h222v391h105v-483h-163v-127h-105z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="562" 
d="M188 564v-564h-117v667h340v123h117v-226h-340z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="452" 
d="M173 391v-391h-105v483h261v127h105v-219h-261z" />
    <glyph glyph-name="uni1E02" unicode="&#x1e02;" horiz-adv-x="648" 
d="M408 0h-337v667h328q89 0 139.5 -48.5t50.5 -121.5q0 -60 -33.5 -101.5t-82.5 -51.5q54 -8 91.5 -55.5t37.5 -108.5q0 -80 -51.5 -130t-142.5 -50zM375 391q44 0 69 24t25 62q0 39 -25 63t-69 24h-187v-173h187zM380 103q48 0 75 24.5t27 68.5q0 39 -27 65.5t-75 26.5
h-192v-185h192zM324 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E03" unicode="&#x1e03;" horiz-adv-x="581" 
d="M173 145q17 -27 52.5 -45.5t71.5 -18.5q62 0 99.5 44.5t37.5 115.5t-37.5 116t-99.5 45q-36 0 -71 -19t-53 -47v-191zM173 0h-105v667h105v-251q59 79 155 79q94 0 154 -69.5t60 -184.5q0 -117 -60 -185t-154 -68q-95 0 -155 78v-66zM297 715q-24 0 -41.5 17t-17.5 42
q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E0A" unicode="&#x1e0a;" horiz-adv-x="710" 
d="M319 0h-248v667h248q155 0 252 -93t97 -241q0 -147 -96.5 -240t-252.5 -93zM319 103q105 0 167 66t62 164q0 101 -60.5 166t-168.5 65h-131v-461h131zM355 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E0B" unicode="&#x1e0b;" horiz-adv-x="581" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t155 -79v251h105v-667zM283 81q37 0 72 18t53 46v192q-18 28 -53 46.5t-72 18.5q-62 0 -99 -45t-37 -116t37 -115.5t99 -44.5zM285 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5
t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E1E" unicode="&#x1e1e;" horiz-adv-x="562" 
d="M300 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17zM188 0h-117v667h457v-103h-340v-173h333v-103h-333v-288z" />
    <glyph glyph-name="uni1E1F" unicode="&#x1e1f;" horiz-adv-x="308" 
d="M247 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17zM198 0h-105v391h-80v92h80v27q0 78 41.5 122.5t111.5 44.5q75 0 116 -42l-41 -65q-23 21 -56 21q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391z" />
    <glyph glyph-name="uni1E22" unicode="&#x1e22;" horiz-adv-x="723" 
d="M652 0h-117v291h-347v-291h-117v667h117v-273h347v273h117v-667zM361 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E23" unicode="&#x1e23;" horiz-adv-x="568" 
d="M500 0h-105v304q0 53 -25.5 75.5t-72.5 22.5q-37 0 -70 -19t-54 -46v-337h-105v667h105v-250q27 32 72.5 55t98.5 23q156 0 156 -153v-342zM284 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E56" unicode="&#x1e56;" horiz-adv-x="608" 
d="M188 0h-117v667h293q101 0 159.5 -60t58.5 -149q0 -88 -58.5 -148.5t-159.5 -60.5h-176v-249zM348 352q50 0 82 29t32 77t-32 77t-82 29h-160v-212h160zM304 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E57" unicode="&#x1e57;" horiz-adv-x="578" 
d="M328 -12q-96 0 -155 79v-251h-105v667h105v-66q58 78 155 78q95 0 154.5 -68t59.5 -185t-59.5 -185.5t-154.5 -68.5zM297 81q62 0 99 45t37 116t-37 115.5t-99 44.5q-36 0 -71 -19t-53 -46v-191q18 -27 53 -46t71 -19zM296 548q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5
t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E60" unicode="&#x1e60;" horiz-adv-x="594" 
d="M299 -12q-172 0 -271 106l65 90q86 -92 211 -92q67 0 100.5 27t33.5 64q0 38 -40 60.5t-97 35.5t-114.5 31t-97.5 61t-40 111q0 84 67.5 139.5t175.5 55.5q154 0 250 -93l-67 -87q-76 76 -193 76q-52 0 -82.5 -22.5t-30.5 -60.5q0 -25 21.5 -42t55.5 -27.5t75.5 -20.5
t83 -24.5t75.5 -35.5t55.5 -58t21.5 -88q0 -90 -65 -148t-193 -58zM297 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E61" unicode="&#x1e61;" horiz-adv-x="472" 
d="M233 -12q-130 0 -207 75l48 76q27 -28 73 -48t91 -20q46 0 70.5 18t24.5 47q0 26 -30 40.5t-73 23t-86 21t-73 44.5t-30 83q0 62 51 104.5t139 42.5q111 0 188 -68l-44 -74q-23 26 -61 42t-82 16q-41 0 -65.5 -16.5t-24.5 -42.5q0 -20 21.5 -32.5t54 -18.5t70.5 -16.5
t70.5 -24.5t54 -45.5t21.5 -76.5q0 -66 -53 -108t-148 -42zM236 548q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E6A" unicode="&#x1e6a;" horiz-adv-x="577" 
d="M347 0h-117v564h-202v103h521v-103h-202v-564zM288 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E6B" unicode="&#x1e6b;" horiz-adv-x="319" 
d="M213 -12q-61 0 -92.5 32t-31.5 92v279h-80v92h80v132h105v-132h98v-92h-98v-253q0 -26 12 -41.5t34 -15.5q32 0 47 17l25 -79q-33 -31 -99 -31zM147 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="903" 
d="M706 0h-125l-130 492l-129 -492h-125l-191 667h131l130 -514l138 514h93l138 -514l129 514h131zM512 723h-75l-165 144h100z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="754" 
d="M599 0h-110l-112 354l-112 -354h-110l-150 483h109l102 -351l115 351h92l115 -351l102 351h109zM438 556h-75l-165 144h100z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="903" 
d="M630 867l-165 -144h-75l140 144h100zM706 0h-125l-130 492l-129 -492h-125l-191 667h131l130 -514l138 514h93l138 -514l129 514h131z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="754" 
d="M556 700l-165 -144h-75l140 144h100zM599 0h-110l-112 354l-112 -354h-110l-150 483h109l102 -351l115 351h92l115 -351l102 351h109z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="903" 
d="M615 775q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM405 775q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM706 0h-125l-130 492l-129 -492h-125l-191 667h131l130 -514l138 514h93
l138 -514l129 514h131z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="754" 
d="M540 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM330 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM599 0h-110l-112 354l-112 -354h-110l-150 483h109l102 -351l115 351h92
l115 -351l102 351h109z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM337 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM268 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM299 752l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM231 582l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="673" 
d="M480 682h-67l-77 72l-75 -72h-66l93 108h96zM671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM687 844l-165 -108h-75l140 108h100z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM410 556h-67l-77 96l-75 -96h-66l93 144h96zM613 770l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="673" 
d="M480 682h-67l-77 72l-75 -72h-66l93 108h96zM671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM228 736h-75l-165 108h100z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM410 556h-67l-77 96l-75 -96h-66l93 144h96zM161 626h-75l-165 144h100z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="673" 
d="M480 682h-67l-77 72l-75 -72h-66l93 108h96zM671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM450 785l-40 9q18 42 77 42q33 0 54.5 -13t21.5 -40q0 -19 -17 -39h-48q21 17 21 38q0 26 -32 26q-27 0 -37 -23z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM410 556h-67l-77 96l-75 -96h-66l93 144h96zM328 722l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="673" 
d="M480 678h-67l-77 48l-75 -48h-66l93 72h96zM671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM393 757q-39 0 -70 19.5t-49 19.5q-42 0 -42 -36h-57q0 31 26.5 49t76.5 18q38 0 69.5 -19.5t49.5 -19.5q41 0 41 36h58
q0 -31 -26.5 -49t-76.5 -18z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM410 523h-67l-77 96l-75 -96h-66l93 144h96zM325 682q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM479 723h-67l-77 96l-75 -96h-66l93 144h96zM337 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM410 556h-67l-77 96l-75 -96h-66l93 144h96zM268 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM516 834l-165 -90h-75l140 90h100zM511 750q-66 -66 -175 -66q-108 0 -176 66l48 30q47 -49 128 -49q80 0 127 49z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM449 773l-165 -144h-75l140 144h100zM443 606q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM398 744h-75l-165 90h100zM511 746q-66 -66 -175 -66q-108 0 -176 66l48 30q47 -49 128 -49q80 0 127 49z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM324 629h-75l-165 144h100zM443 609q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM299 781l-40 8q18 35 77 35q33 0 54.5 -11t21.5 -34q0 -17 -17 -32h-48q21 14 21 31q0 22 -32 22q-27 0 -37 -19zM511 747q-66 -66 -175 -66q-108 0 -176 66l48 30q47 -49 128 -49
q80 0 127 49z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM231 687l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30zM443 609q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM512 729q-64 -51 -175 -51t-176 51l48 23q48 -38 128 -38q81 0 127 38zM393 757q-39 0 -70 19.5t-49 19.5q-42 0 -42 -36h-57q0 31 26.5 49t76.5 18q38 0 69.5 -19.5t49.5 -19.5
q41 0 41 36h58q0 -31 -26.5 -49t-76.5 -18z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM443 609q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66zM325 658q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98
t-76.5 -36z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="673" 
d="M671 0h-133l-49 128h-306l-49 -128h-133l262 667h146zM456 231l-120 318l-120 -318h240zM337 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17zM511 789q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z
" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="536" 
d="M468 0h-105v52q-56 -64 -156 -64q-66 0 -117 42.5t-51 117.5q0 77 50.5 117t117.5 40q103 0 156 -62v72q0 42 -31 66t-82 24q-81 0 -143 -61l-43 73q82 78 203 78q89 0 145 -42t56 -133v-320zM248 60q79 0 115 50v73q-36 50 -115 50q-45 0 -74 -24t-29 -63t29 -62.5
t74 -23.5zM268 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17zM441 622q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="577" 
d="M528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103zM301 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="557" 
d="M291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272zM285 -191
q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="577" 
d="M528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103zM263 752l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="557" 
d="M291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272zM247 582l-40 12
q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="577" 
d="M528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103zM357 721q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="557" 
d="M291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272zM341 554
q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="577" 
d="M447 682h-67l-77 72l-75 -72h-66l93 108h96zM654 844l-165 -108h-75l140 108h100zM528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="557" 
d="M426 556h-67l-77 96l-75 -96h-66l93 144h96zM626 770l-165 -144h-75l140 144h100zM291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70z
M419 281q-2 50 -36.5 89t-99.5 39q-62 0 -97 -38.5t-39 -89.5h272z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="577" 
d="M447 682h-67l-77 72l-75 -72h-66l93 108h96zM528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103zM195 736h-75l-165 108h100z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="557" 
d="M426 556h-67l-77 96l-75 -96h-66l93 144h96zM291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39
q-62 0 -97 -38.5t-39 -89.5h272zM177 626h-75l-165 144h100z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="577" 
d="M447 682h-67l-77 72l-75 -72h-66l93 108h96zM528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103zM417 785l-40 9q18 42 77 42q33 0 54.5 -13t21.5 -40q0 -19 -17 -39h-48q21 17 21 38q0 26 -32 26q-27 0 -37 -23z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="557" 
d="M426 556h-67l-77 96l-75 -96h-66l93 144h96zM291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39
q-62 0 -97 -38.5t-39 -89.5h272zM344 722l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="577" 
d="M447 678h-67l-77 48l-75 -48h-66l93 72h96zM528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103zM360 757q-38 0 -70 19.5t-49 19.5q-42 0 -42 -36h-57q0 31 26.5 49t76.5 18q38 0 69.5 -19.5t49.5 -19.5q41 0 41 36h58q0 -31 -26.5 -49t-76.5 -18z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="557" 
d="M426 523h-67l-77 96l-75 -96h-66l93 144h96zM291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39
q-62 0 -97 -38.5t-39 -89.5h272zM341 682q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="577" 
d="M442 723h-67l-77 96l-75 -96h-66l93 144h96zM528 0h-457v667h457v-103h-340v-173h333v-103h-333v-185h340v-103zM301 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="557" 
d="M427 556h-67l-77 96l-75 -96h-66l93 144h96zM291 -12q-110 0 -181.5 70.5t-71.5 183.5q0 106 69.5 179.5t175.5 73.5q107 0 172.5 -74t65.5 -189v-25h-373q6 -57 47 -95t107 -38q37 0 74.5 14t62.5 39l48 -69q-73 -70 -196 -70zM419 281q-2 50 -36.5 89t-99.5 39
q-62 0 -97 -38.5t-39 -89.5h272zM285 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667zM94 752l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="241" 
d="M173 0h-105v483h105v-483zM84 582l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="259" 
d="M188 0h-117v667h117v-667zM131 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="241" 
d="M120 542q-26 0 -45.5 19t-19.5 46t19.5 46t45.5 19q27 0 46 -19t19 -46t-19 -46t-46 -19zM173 0h-105v483h105v-483zM121 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68zM385 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5
t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" horiz-adv-x="573" 
d="M286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM287 -191q-24 0 -41.5 17
t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68zM344 752l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53
q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" horiz-adv-x="573" 
d="M286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM250 582l-40 12q18 56 77 56
q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="765" 
d="M521 682h-67l-77 72l-75 -72h-66l93 108h96zM728 844l-165 -108h-75l140 108h100zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68
t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" horiz-adv-x="573" 
d="M429 556h-67l-77 96l-75 -96h-66l93 144h96zM630 770l-165 -144h-75l140 144h100zM286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5
q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="765" 
d="M520 682h-67l-77 72l-75 -72h-66l93 108h96zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68zM267 736
h-75l-165 108h100z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" horiz-adv-x="573" 
d="M429 556h-67l-77 96l-75 -96h-66l93 144h96zM286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5
q0 -68 37.5 -114.5t101.5 -46.5zM180 626h-75l-165 144h100z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="765" 
d="M519 682h-67l-77 72l-75 -72h-66l93 108h96zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68zM489 785
l-40 9q18 42 77 42q33 0 54.5 -13t21.5 -40q0 -19 -17 -39h-48q21 17 21 38q0 26 -32 26q-27 0 -37 -23z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" horiz-adv-x="573" 
d="M429 556h-67l-77 96l-75 -96h-66l93 144h96zM286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5
q0 -68 37.5 -114.5t101.5 -46.5zM347 722l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="765" 
d="M524 678h-67l-77 48l-75 -48h-66l93 72h96zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68zM437 757
q-39 0 -70 19.5t-49 19.5q-42 0 -42 -36h-57q0 31 26.5 49t76.5 18q38 0 69.5 -19.5t49.5 -19.5q41 0 41 36h58q0 -31 -26.5 -49t-76.5 -18z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" horiz-adv-x="573" 
d="M429 523h-67l-77 96l-75 -96h-66l93 144h96zM286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5
q0 -68 37.5 -114.5t101.5 -46.5zM344 682q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="765" 
d="M527 723h-67l-77 96l-75 -96h-66l93 144h96zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68zM385 -191
q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" horiz-adv-x="573" 
d="M429 556h-67l-77 96l-75 -96h-66l93 144h96zM286 -12q-112 0 -180 73.5t-68 180.5t68 180t180 73q113 0 181 -73t68 -180q0 -108 -68 -181t-181 -73zM286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5
q0 -68 37.5 -114.5t101.5 -46.5zM287 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="765" 
d="M562 867l-165 -144h-75l140 144h100zM382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5q157 0 255 -109q36 30 45 69q-4 -2 -13 -2q-19 0 -31.5 13.5t-12.5 33.5q0 21 14.5 36t35.5 15q24 0 40.5 -18.5t16.5 -50.5q0 -79 -68 -130q59 -89 59 -202
q0 -150 -96 -247.5t-245 -97.5zM382 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" horiz-adv-x="573" 
d="M466 700l-165 -144h-75l140 144h100zM541 531q0 -72 -62 -124q56 -68 56 -165q0 -108 -68 -181t-181 -73q-112 0 -180 73.5t-68 180.5t68 180t180 73q96 0 163 -57q36 31 42 67q-4 -2 -13 -2q-19 0 -31.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 40.5 -18.5t16.5 -50.5z
M286 81q65 0 102.5 46.5t37.5 114.5q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5q157 0 255 -109q36 30 45 69q-4 -2 -13 -2q-19 0 -31.5 13.5t-12.5 33.5q0 21 14.5 36t35.5 15q24 0 40.5 -18.5t16.5 -50.5q0 -79 -68 -130q59 -89 59 -202q0 -150 -96 -247.5t-245 -97.5zM382 92q99 0 160 68.5
t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68zM444 723h-75l-165 144h100z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" horiz-adv-x="573" 
d="M541 531q0 -72 -62 -124q56 -68 56 -165q0 -108 -68 -181t-181 -73q-112 0 -180 73.5t-68 180.5t68 180t180 73q96 0 163 -57q36 31 42 67q-4 -2 -13 -2q-19 0 -31.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 40.5 -18.5t16.5 -50.5zM286 81q65 0 102.5 46.5t37.5 114.5
q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM348 556h-75l-165 144h100z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5q157 0 255 -109q36 30 45 69q-4 -2 -13 -2q-19 0 -31.5 13.5t-12.5 33.5q0 21 14.5 36t35.5 15q24 0 40.5 -18.5t16.5 -50.5q0 -79 -68 -130q59 -89 59 -202q0 -150 -96 -247.5t-245 -97.5zM382 92q99 0 160 68.5
t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68zM345 752l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" horiz-adv-x="573" 
d="M541 531q0 -72 -62 -124q56 -68 56 -165q0 -108 -68 -181t-181 -73q-112 0 -180 73.5t-68 180.5t68 180t180 73q96 0 163 -57q36 31 42 67q-4 -2 -13 -2q-19 0 -31.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 40.5 -18.5t16.5 -50.5zM286 81q65 0 102.5 46.5t37.5 114.5
q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM250 582l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5q157 0 255 -109q36 30 45 69q-4 -2 -13 -2q-19 0 -31.5 13.5t-12.5 33.5q0 21 14.5 36t35.5 15q24 0 40.5 -18.5t16.5 -50.5q0 -79 -68 -130q59 -89 59 -202q0 -150 -96 -247.5t-245 -97.5zM382 92q99 0 160 68.5
t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68zM440 721q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q29 0 50 -19.5t36.5 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98
t-76.5 -36z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" horiz-adv-x="573" 
d="M541 531q0 -72 -62 -124q56 -68 56 -165q0 -108 -68 -181t-181 -73q-112 0 -180 73.5t-68 180.5t68 180t180 73q96 0 163 -57q36 31 42 67q-4 -2 -13 -2q-19 0 -31.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 40.5 -18.5t16.5 -50.5zM286 81q65 0 102.5 46.5t37.5 114.5
q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM344 554q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58
q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="765" 
d="M382 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5q157 0 255 -109q36 30 45 69q-4 -2 -13 -2q-19 0 -31.5 13.5t-12.5 33.5q0 21 14.5 36t35.5 15q24 0 40.5 -18.5t16.5 -50.5q0 -79 -68 -130q59 -89 59 -202q0 -150 -96 -247.5t-245 -97.5zM382 92q99 0 160 68.5
t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68zM385 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" horiz-adv-x="573" 
d="M541 531q0 -72 -62 -124q56 -68 56 -165q0 -108 -68 -181t-181 -73q-112 0 -180 73.5t-68 180.5t68 180t180 73q96 0 163 -57q36 31 42 67q-4 -2 -13 -2q-19 0 -31.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 40.5 -18.5t16.5 -50.5zM286 81q65 0 102.5 46.5t37.5 114.5
q0 67 -37.5 113.5t-102.5 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM287 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="719" 
d="M360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-403q0 -128 -73.5 -202t-214.5 -74zM362 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" 
d="M499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483zM285 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="719" 
d="M360 -12q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-403q0 -128 -73.5 -202t-214.5 -74zM324 752l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" 
d="M499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-483zM247 582l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="719" 
d="M539 867l-165 -144h-75l140 144h100zM760 727q0 -47 -29.5 -89.5t-82.5 -65.5v-308q0 -128 -73.5 -202t-214.5 -74q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-48q24 14 41.5 36.5t20.5 45.5q-4 -2 -13 -2
q-19 0 -31.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 40.5 -18.5t16.5 -50.5z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" 
d="M462 700l-165 -144h-75l140 144h100zM499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-31q31 30 36 63q-2 -2 -12 -2q-20 0 -32.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 41 -18.5t17 -50.5
q0 -41 -23 -79t-64 -63v-399z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="719" 
d="M421 723h-75l-165 144h100zM760 727q0 -47 -29.5 -89.5t-82.5 -65.5v-308q0 -128 -73.5 -202t-214.5 -74q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-48q24 14 41.5 36.5t20.5 45.5q-4 -2 -13 -2q-19 0 -31.5 13
t-12.5 34q0 20 14.5 35t35.5 15q24 0 40.5 -18.5t16.5 -50.5z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" 
d="M344 556h-75l-165 144h100zM499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-31q31 30 36 63q-2 -2 -12 -2q-20 0 -32.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 41 -18.5t17 -50.5q0 -41 -23 -79
t-64 -63v-399z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="719" 
d="M324 752l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30zM760 727q0 -47 -29.5 -89.5t-82.5 -65.5v-308q0 -128 -73.5 -202t-214.5 -74q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47
t126 47t44 128v400h118v-48q24 14 41.5 36.5t20.5 45.5q-4 -2 -13 -2q-19 0 -31.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 40.5 -18.5t16.5 -50.5z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" 
d="M247 582l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30zM499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-31q31 30 36 63q-2 -2 -12 -2
q-20 0 -32.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 41 -18.5t17 -50.5q0 -41 -23 -79t-64 -63v-399z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="719" 
d="M760 727q0 -47 -29.5 -89.5t-82.5 -65.5v-308q0 -128 -73.5 -202t-214.5 -74q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128v400h118v-48q24 14 41.5 36.5t20.5 45.5q-4 -2 -13 -2q-19 0 -31.5 13t-12.5 34q0 20 14.5 35t35.5 15
q24 0 40.5 -18.5t16.5 -50.5zM418 721q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q29 0 50 -19.5t36.5 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" 
d="M499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-31q31 30 36 63q-2 -2 -12 -2q-20 0 -32.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 41 -18.5t17 -50.5q0 -41 -23 -79t-64 -63v-399zM341 554
q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="719" 
d="M362 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17zM760 727q0 -47 -29.5 -89.5t-82.5 -65.5v-308q0 -128 -73.5 -202t-214.5 -74q-142 0 -215.5 74t-73.5 201v404h118v-400q0 -81 44.5 -128t126.5 -47t126 47t44 128
v400h118v-48q24 14 41.5 36.5t20.5 45.5q-4 -2 -13 -2q-19 0 -31.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 40.5 -18.5t16.5 -50.5z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" 
d="M285 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17zM499 0h-105v64q-68 -76 -171 -76q-155 0 -155 153v342h105v-304q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v339h105v-31q31 30 36 63q-2 -2 -12 -2
q-20 0 -32.5 13t-12.5 34q0 20 14.5 35t35.5 15q24 0 41 -18.5t17 -50.5q0 -41 -23 -79t-64 -63v-399z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="637" 
d="M377 0h-117v277l-259 390h134l184 -286l182 286h134l-258 -390v-277zM380 723h-75l-165 144h100z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="503" 
d="M47 -189l16 94q18 -8 40 -8q51 0 69 42l24 55l-198 489h112l141 -362l141 362h113l-233 -570q-43 -107 -166 -109q-33 0 -59 7zM312 556h-75l-165 144h100z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="51" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="593" 
d="M563 197h-533v90h533v-90z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="833" 
d="M803 197h-773v90h773v-90z" />
    <glyph glyph-name="afii00208" unicode="&#x2015;" horiz-adv-x="593" 
d="M563 197h-533v90h533v-90z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="244" 
d="M42 507q0 50 25.5 95t68.5 75l47 -38q-25 -15 -46 -42t-26 -52q6 2 17 2q26 0 43 -17.5t17 -45.5t-20 -48t-48 -20q-32 0 -55 24.5t-23 66.5z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="244" 
d="M198 586q0 -50 -25.5 -95.5t-68.5 -75.5l-47 39q25 15 46 42t26 52q-9 -3 -17 -3q-26 0 -43 17.5t-17 45.5t20 48.5t48 20.5q32 0 55 -24.5t23 -66.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="245" 
d="M198 38q0 -50 -25.5 -95.5t-68.5 -75.5l-47 39q25 15 46 42t26 52q-9 -3 -17 -3q-26 0 -43 17.5t-17 45.5t20 48.5t48 20.5q32 0 55 -24.5t23 -66.5z" />
    <glyph glyph-name="quotereversed" unicode="&#x201b;" horiz-adv-x="244" 
d="M46 586q0 42 23 66.5t55 24.5q28 0 48 -20.5t20 -48.5t-17 -45.5t-43 -17.5q-8 0 -17 3q5 -25 26 -52t46 -42l-47 -39q-43 30 -68.5 75.5t-25.5 95.5z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="429" 
d="M46 507q0 50 25.5 95t67.5 75l48 -38q-25 -15 -46.5 -42t-25.5 -52q4 2 17 2q25 0 42 -18t17 -45q0 -28 -20 -48t-48 -20q-32 0 -54.5 24.5t-22.5 66.5zM231 507q0 50 25.5 95t67.5 75l48 -38q-25 -15 -46.5 -42t-25.5 -52q4 2 17 2q25 0 42 -18t17 -45q0 -28 -20 -48
t-48 -20q-32 0 -54.5 24.5t-22.5 66.5z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="429" 
d="M383 586q0 -50 -25.5 -95.5t-68.5 -75.5l-47 39q25 15 46 42t26 52q-9 -3 -17 -3q-26 0 -43 17.5t-17 45.5t20 48.5t48 20.5q32 0 55 -24.5t23 -66.5zM198 586q0 -50 -25.5 -95.5t-68.5 -75.5l-47 39q25 15 46 42t26 52q-9 -3 -17 -3q-26 0 -43 17.5t-17 45.5t20 48.5
t48 20.5q32 0 55 -24.5t23 -66.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="430" 
d="M198 38q0 -50 -25.5 -95.5t-68.5 -75.5l-47 39q25 15 46 42t26 52q-9 -3 -17 -3q-26 0 -43 17.5t-17 45.5t20 48.5t48 20.5q32 0 55 -24.5t23 -66.5zM383 38q0 -50 -25.5 -95.5t-68.5 -75.5l-47 39q25 15 46 42t26 52q-9 -3 -17 -3q-26 0 -43 17.5t-17 45.5t20 48.5
t48 20.5q32 0 55 -24.5t23 -66.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="271" 
d="M250 526l-89 4l4 -203h-60l5 203l-89 -4v54l89 -3l-5 100h60l-4 -100l89 3v-54z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="271" 
d="M250 580v-54l-89 4v-202l89 4v-54l-89 3l4 -101h-60l4 101l-88 -3v54l88 -4v202l-88 -4v54l88 -3l-4 100h60l-4 -100z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="358" 
d="M294 242q0 -47 -33.5 -80t-80.5 -33t-80.5 33t-33.5 80t33.5 80.5t80.5 33.5t80.5 -33.5t33.5 -80.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="734" 
d="M191 59q0 -28 -21 -48.5t-49 -20.5t-48.5 20.5t-20.5 48.5q0 29 20.5 49.5t48.5 20.5q29 0 49.5 -20.5t20.5 -49.5zM436 59q0 -28 -21 -48.5t-49 -20.5t-48.5 20.5t-20.5 48.5q0 29 20.5 49.5t48.5 20.5t49 -20.5t21 -49.5zM681 59q0 -28 -21 -48.5t-49 -20.5t-48.5 20.5
t-20.5 48.5q0 29 20.5 49.5t48.5 20.5q29 0 49.5 -20.5t20.5 -49.5z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1105" 
d="M360 510q0 -71 -46.5 -117.5t-120.5 -46.5q-73 0 -119 46.5t-46 117.5q0 72 46 119.5t119 47.5q74 0 120.5 -47.5t46.5 -119.5zM622 667l-427 -667h-66l426 667h67zM718 152q0 -71 -46.5 -117.5t-119.5 -46.5t-119 46.5t-46 117.5q0 72 46 119.5t119 47.5t119.5 -48
t46.5 -119zM282 510q0 48 -25 77t-64 29t-63.5 -29t-24.5 -77q0 -46 24.5 -75t63.5 -29t64 29t25 75zM641 152q0 48 -25 77t-64 29t-63.5 -29t-24.5 -77q0 -46 24.5 -75t63.5 -29t64 29t25 75zM1077 152q0 -71 -46.5 -117.5t-119.5 -46.5t-119 46.5t-46 117.5q0 72 46 119.5
t119 47.5t119.5 -48t46.5 -119zM1000 152q0 48 -25 77t-64 29t-63.5 -29t-24.5 -77q0 -46 24.5 -75t63.5 -29t64 29t25 75z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="319" 
d="M289 63h-99l-160 180l160 177h99l-160 -177z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="319" 
d="M289 243l-160 -180h-99l160 180l-160 177h99z" />
    <glyph glyph-name="uni203E" unicode="&#x203e;" horiz-adv-x="363" 
d="M363 566h-363v62h363v-62z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="150" 
d="M322 667l-427 -667h-67l426 667h68z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="393" 
d="M364 621q0 -85 -43 -146t-124 -61q-83 0 -125.5 61t-42.5 146t42.5 145.5t125.5 60.5q81 0 124 -61t43 -145zM282 621q0 141 -85 141q-45 0 -66 -38.5t-21 -102.5q0 -142 87 -142q85 0 85 142z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="393" 
d="M364 513h-55v-92h-80v92h-196v56l164 252h112v-244h55v-64zM229 577v175l-116 -175h116z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="393" 
d="M355 550q0 -61 -44.5 -98.5t-116.5 -37.5q-105 0 -156 64l45 53q41 -52 111 -52q37 0 59 20t22 49q0 31 -21.5 50.5t-57.5 19.5q-53 0 -89 -39l-57 19v223h278v-65h-197v-113q36 34 93 34q55 0 93 -33t38 -94z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="393" 
d="M361 550q0 -59 -43 -97.5t-111 -38.5q-88 0 -131.5 57.5t-43.5 148.5q0 92 49.5 149.5t138.5 57.5q70 0 120 -42l-36 -57q-36 34 -84 34q-50 0 -78.5 -36t-28.5 -87v-12q18 23 47 39t61 16q59 0 99.5 -35.5t40.5 -96.5zM280 547q0 35 -23.5 53t-57.5 18q-52 0 -85 -44
q3 -40 25 -67.5t64 -27.5q35 0 56 21t21 47z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="367" 
d="M333 765l-156 -344h-89l157 335h-216v65h304v-56z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="393" 
d="M359 524q0 -54 -47 -82t-116 -28q-68 0 -115 27.5t-47 82.5q0 36 28 64.5t70 38.5q-40 10 -65.5 34t-25.5 60q0 53 46 79.5t109 26.5t109.5 -26.5t46.5 -79.5q0 -36 -25.5 -60t-64.5 -34q41 -10 69 -38.5t28 -64.5zM272 710q0 26 -21 40t-55 14q-33 0 -54 -14t-21 -40
q0 -44 75 -57q15 2 29 6.5t30.5 18t16.5 32.5zM279 532q0 30 -29 47t-54 19q-24 -3 -53 -19.5t-29 -46.5q0 -26 23 -41t59 -15t59.5 15t23.5 41z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="393" 
d="M32 691q0 58 43 97t112 39q87 0 130.5 -58t43.5 -149q0 -92 -49.5 -149t-137.5 -57q-73 0 -120 42l36 57q33 -34 84 -34q49 0 77.5 35.5t28.5 87.5v12q-17 -23 -46 -39t-61 -16q-59 0 -100 35t-41 97zM113 694q0 -35 23.5 -53.5t57.5 -18.5q54 0 85 45q-3 39 -24.5 67
t-62.5 28q-36 0 -57.5 -21t-21.5 -47z" />
    <glyph glyph-name="parenleftsuperior" unicode="&#x207d;" horiz-adv-x="179" 
d="M169 376l-48 -20q-45 48 -71 120t-26 145q0 72 26 144t71 121l48 -20q-62 -117 -62 -245q0 -130 62 -245z" />
    <glyph glyph-name="parenrightsuperior" unicode="&#x207e;" horiz-adv-x="179" 
d="M163 621q0 -73 -26 -144.5t-71 -120.5l-47 20q62 118 62 245t-62 245l47 20q45 -49 71 -121t26 -144z" />
    <glyph glyph-name="nsuperior" unicode="&#x207f;" horiz-adv-x="406" 
d="M351 326h-77v192q0 65 -63 65q-48 0 -79 -39v-218h-78v314h78v-42q44 50 113 50q106 0 106 -105v-217z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="393" 
d="M364 54q0 -85 -43 -146t-124 -61q-83 0 -125.5 61t-42.5 146t42.5 145.5t125.5 60.5q81 0 124 -61t43 -145zM282 54q0 141 -85 141q-45 0 -66 -38.5t-21 -102.5q0 -142 87 -142q85 0 85 142z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="264" 
d="M204 -146h-81v296l-67 -69l-46 49l124 124h70v-400z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="393" 
d="M352 -146h-304v59q128 91 174.5 136t46.5 83q0 31 -21.5 47t-53.5 16q-34 0 -64 -15t-46 -36l-43 51q55 65 155 65q66 0 109.5 -32t43.5 -90q0 -51 -44.5 -101.5t-139.5 -117.5h187v-65z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="393" 
d="M354 -36q0 -52 -43.5 -84.5t-115.5 -32.5q-107 0 -159 64l41 52q45 -51 115 -51q39 0 60.5 15.5t21.5 41.5q0 57 -95 57q-37 0 -43 -1v65q8 -1 42 -1q89 0 89 53q0 25 -22.5 39t-57.5 14q-62 0 -107 -46l-39 48q58 63 154 63q70 0 111 -29.5t41 -78.5q0 -37 -26.5 -61.5
t-62.5 -30.5q35 -3 65.5 -28t30.5 -68z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="393" 
d="M364 -54h-55v-92h-80v92h-196v56l164 252h112v-244h55v-64zM229 10v175l-116 -175h116z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="393" 
d="M355 -17q0 -61 -44.5 -98.5t-116.5 -37.5q-105 0 -156 64l45 53q41 -52 111 -52q37 0 59 20t22 49q0 31 -21.5 50.5t-57.5 19.5q-53 0 -89 -39l-57 19v223h278v-65h-197v-113q36 34 93 34q55 0 93 -33t38 -94z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="393" 
d="M361 -17q0 -59 -43 -97.5t-111 -38.5q-88 0 -131.5 57.5t-43.5 148.5q0 92 49.5 149.5t138.5 57.5q70 0 120 -42l-36 -57q-36 34 -84 34q-50 0 -78.5 -36t-28.5 -87v-12q18 23 47 39t61 16q59 0 99.5 -35.5t40.5 -96.5zM280 -20q0 35 -23.5 53t-57.5 18q-52 0 -85 -44
q3 -40 25 -67.5t64 -27.5q35 0 56 21t21 47z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="367" 
d="M333 198l-156 -344h-89l157 335h-216v65h304v-56z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="393" 
d="M359 -43q0 -54 -47 -82t-116 -28q-68 0 -115 27.5t-47 82.5q0 36 28 64.5t70 38.5q-40 10 -65.5 34t-25.5 60q0 53 46 79.5t109 26.5t109.5 -26.5t46.5 -79.5q0 -36 -25.5 -60t-64.5 -34q41 -10 69 -38.5t28 -64.5zM272 143q0 26 -21 40t-55 14q-33 0 -54 -14t-21 -40
q0 -19 16.5 -32.5t30.5 -18t28 -6.5q15 2 29 6.5t30.5 18t16.5 32.5zM279 -35q0 30 -29 47t-54 19q-24 -3 -53 -19.5t-29 -46.5q0 -26 23 -41t59 -15t59.5 15t23.5 41z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="393" 
d="M32 124q0 58 43 97t112 39q87 0 130.5 -58t43.5 -149q0 -92 -49.5 -149t-137.5 -57q-73 0 -120 42l36 57q33 -34 84 -34q49 0 77.5 35.5t28.5 87.5v12q-17 -23 -46 -39t-61 -16q-59 0 -100 35t-41 97zM113 127q0 -35 23.5 -53.5t57.5 -18.5q54 0 85 45q-3 39 -24.5 67
t-62.5 28q-36 0 -57.5 -21t-21.5 -47z" />
    <glyph glyph-name="parenleftinferior" unicode="&#x208d;" horiz-adv-x="179" 
d="M169 -191l-48 -20q-45 48 -71 120t-26 145q0 72 26 144t71 121l48 -20q-62 -117 -62 -245q0 -130 62 -245z" />
    <glyph glyph-name="parenrightinferior" unicode="&#x208e;" horiz-adv-x="179" 
d="M163 54q0 -73 -26 -144.5t-71 -120.5l-47 20q62 118 62 245t-62 245l47 20q45 -49 71 -121t26 -144z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="689" 
d="M397 -12q-3 0 -7.5 0.5t-6.5 0.5l-30 -89h-63l32 95q-41 9 -69 20l-39 -115h-63l48 143q-71 45 -111.5 120.5t-40.5 169.5q0 152 100 248.5t250 96.5h13l30 90h63l-33 -98q38 -8 69 -24l40 122h63l-52 -155q40 -31 75 -85l-100 -51q-11 17 -13 19l-134 -403q47 5 86 31
t61 65l100 -51q-96 -150 -268 -150zM167 333q0 -110 70 -178l138 418q-91 -8 -149.5 -74.5t-58.5 -165.5zM287 118q33 -17 69 -23l148 446q-32 21 -67 28z" />
    <glyph glyph-name="frenchfranc" unicode="&#x20a3;" horiz-adv-x="572" 
d="M197 0h-117v121h-71v62h71v484h457v-103h-340v-173h333v-103h-333v-105h175v-62h-175v-121z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="536" 
d="M21 331v57h48q-23 39 -23 86q0 87 71 145t164 58q158 0 219 -117l-93 -55q-13 33 -44 54.5t-69 21.5q-51 0 -86 -30t-35 -79q0 -44 26 -84h183v-57h-142q20 -33 26 -60h116v-57h-115q-11 -58 -68 -95q24 8 51 8q32 0 72 -18t66 -18q60 0 90 38l47 -93q-47 -49 -142 -49
q-47 0 -98 22.5t-82 22.5q-44 0 -117 -39l-40 82q119 55 119 137v2h-144v57h129q-9 18 -40 60h-89z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="739" 
d="M658 0h-113l-171 234h-177v-234h-117v234h-71v62h71v79h-71v62h71v230h120l169 -230h172v230h117v-230h71v-62h-71v-79h71v-62h-71v-234zM197 375v-79h131l-57 79h-74zM415 375l58 -79h68v79h-126zM197 476v-39h28zM519 234l22 -30v30h-22z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="661" 
d="M657 425h-69q-11 -77 -68 -126.5t-147 -49.5h-176v-249h-117v425h-71v66h71v176h293q91 0 148 -49.5t67 -126.5h69v-66zM197 564v-73h269q-10 34 -39 53.5t-70 19.5h-160zM357 352q41 0 70 19.5t39 53.5h-269v-73h160z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="1049" 
d="M810 -12q-130 0 -207 75l48 76q27 -28 73 -48t91 -20q46 0 70.5 18t24.5 47q0 26 -30 40.5t-73 23t-86 21t-73 44.5t-30 83q0 62 51 104.5t139 42.5q111 0 188 -68l-44 -74q-23 26 -61 42t-82 16q-41 0 -65.5 -16.5t-24.5 -42.5q0 -20 21.5 -32.5t54 -18.5t70.5 -16.5
t70.5 -24.5t54 -45.5t21.5 -76.5q0 -66 -53 -108t-148 -42zM587 0h-135l-148 249h-116v-249h-117v667h293q98 0 158 -58t60 -151q0 -84 -46 -134.5t-112 -61.5zM348 352q50 0 82 29t32 77t-32 77t-82 29h-160v-212h160z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="903" 
d="M706 0h-125l-62 235h-136l-61 -235h-125l-68 235h-113v62h96l-23 78h-73v62h56l-66 230h131l58 -230h148l62 230h93l62 -230h147l58 230h131l-66 -230h57v-62h-75l-22 -78h97v-62h-114zM210 375l20 -78h75l21 78h-116zM576 375l21 -78h75l19 78h-115zM420 375l-20 -78
h102l-20 78h-62zM246 235l21 -82l22 82h-43zM614 235l22 -82l20 82h-42zM451 492l-15 -55h29z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="834" 
d="M536 0h-270v379h100v-288h170q68 0 99 33t31 100v326h100v-334q0 -107 -54 -161.5t-176 -54.5zM338 550q121 0 175.5 -55t54.5 -161v-164h-100v155q0 68 -30.5 101t-99.5 33h-170v-459h-100v550h270z" />
    <glyph glyph-name="dong" unicode="&#x20ab;" horiz-adv-x="585" 
d="M485 -159h-363v62h363v-62zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t155 -79v115h-150v62h150v74h105v-74h63v-62h-63v-531zM283 81q37 0 72 18t53 46v192q-18 28 -53 46.5t-72 18.5q-62 0 -99 -45t-37 -116t37 -115.5t99 -44.5z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="703" 
d="M413 -12q-122 0 -213.5 65.5t-122.5 176.5h-52v67h39q-1 11 -1 36q0 12 2 38h-40v67h52q32 109 123.5 174.5t212.5 65.5q174 0 268 -150l-100 -51q-25 43 -70.5 70t-97.5 27q-71 0 -127 -36.5t-82 -99.5h270v-67h-289q-2 -24 -2 -38q0 -25 2 -36h289v-67h-271
q27 -64 82.5 -101t127.5 -37q53 0 98 27t70 70l100 -51q-96 -150 -268 -150z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="504" 
d="M173 260v-131q0 -26 12 -41.5t34 -15.5q27 0 47 17l25 -70q-32 -31 -99 -31q-124 0 -124 124v383q0 78 59.5 130t153.5 52q81 0 138 -39t57 -98q0 -48 -37 -88t-102 -83zM173 359l127 85q41 27 58.5 46.5t17.5 44.5q0 30 -29 48.5t-68 18.5q-47 0 -76.5 -29.5
t-29.5 -78.5v-135z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="1125" 
d="M922 350q-75 0 -121.5 47.5t-46.5 117.5t46.5 117.5t121.5 47.5q77 0 123 -47.5t46 -117.5t-46 -117.5t-123 -47.5zM922 413q41 0 65 28t24 74q0 45 -24 72.5t-65 27.5t-64.5 -27.5t-23.5 -72.5q0 -46 23.5 -74t64.5 -28zM649 0h-113l-348 476v-476h-117v667h120
l341 -463v463h117v-667z" />
    <glyph glyph-name="published" unicode="&#x2117;" horiz-adv-x="778" 
d="M734 334q0 -142 -101 -243.5t-244 -101.5t-244 101.5t-101 243.5t101 243.5t244 101.5t244 -101.5t101 -243.5zM700 334q0 128 -92 219.5t-219 91.5q-128 0 -219.5 -91.5t-91.5 -219.5t91.5 -219.5t219.5 -91.5t219.5 91.5t91.5 219.5zM552 417q0 -52 -36.5 -84
t-89.5 -32h-112v-170h-42v405h154q52 0 89 -32.5t37 -86.5zM509 417q0 36 -24 58t-59 22h-112v-158h112q35 0 59 21.5t24 56.5z" />
    <glyph glyph-name="servicemark" unicode="&#x2120;" horiz-adv-x="476" 
d="M439 447h-28v182l-75 -182h-8l-75 182v-182h-28v220h43l64 -160l64 160h43v-220zM188 506q0 -31 -22.5 -49t-64.5 -18q-55 0 -86 36l18 21q28 -32 67 -32q55 0 55 39q0 19 -21.5 30t-46.5 15t-46.5 19.5t-21.5 42.5q0 28 22.5 45.5t57.5 17.5q55 0 82 -32l-18 -19
q-22 27 -63 27q-21 0 -34.5 -10t-13.5 -26q0 -17 21 -27t47 -14.5t47 -20.5t21 -45z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="454" 
d="M419 447h-28v182l-75 -182h-8l-75 182v-182h-28v220h43l64 -160l64 160h43v-220zM169 641h-62v-194h-28v194h-62v26h152v-26z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="765" 
d="M59 103h126q-64 41 -104 105.5t-40 149.5q0 142 95.5 231t245.5 89q151 0 246 -88.5t95 -231.5q0 -164 -142 -255h125v-103h-257v103q66 18 110 80.5t44 150.5q0 99 -57.5 169t-163.5 70t-163.5 -70t-57.5 -169q0 -88 44 -150.5t111 -80.5v-103h-257v103z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="842" 
d="M811 324h-632q-5 0 -5 -5v-190q0 -14 9 -23q98 -103 240 -103q151 0 250 115h57q-54 -63 -135 -99.5t-174 -36.5q-161 0 -275.5 103t-114.5 248t114.5 248t275.5 103t275.5 -103t114.5 -248v-9zM668 349v191q0 14 -10 24q-97 99 -235 99q-140 0 -239 -102
q-10 -10 -10 -24v-188q0 -6 5 -6h485q4 0 4 6z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="841" 
d="M204 267h-81v296l-67 -69l-46 49l124 124h70v-400zM619 667l-427 -667h-67l426 667h68zM801 110q0 -52 -43.5 -84.5t-115.5 -32.5q-107 0 -159 64l41 52q45 -51 115 -51q39 0 60.5 15.5t21.5 41.5q0 57 -95 57q-37 0 -43 -1v65q8 -1 42 -1q89 0 89 53q0 25 -22.5 39
t-57.5 14q-62 0 -107 -46l-39 48q58 63 154 63q70 0 111 -29.5t41 -78.5q0 -37 -26.5 -61.5t-62.5 -30.5q35 -3 65.5 -28t30.5 -68z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="937" 
d="M352 267h-304v59q128 91 174.5 136t46.5 83q0 31 -21.5 47t-53.5 16q-34 0 -64 -15t-46 -36l-43 51q55 65 155 65q66 0 109.5 -32t43.5 -90q0 -51 -44.5 -101.5t-139.5 -117.5h187v-65zM715 667l-427 -667h-67l426 667h68zM898 110q0 -52 -43.5 -84.5t-115.5 -32.5
q-107 0 -159 64l41 52q45 -51 115 -51q39 0 60.5 15.5t21.5 41.5q0 57 -95 57q-37 0 -43 -1v65q8 -1 42 -1q89 0 89 53q0 25 -22.5 39t-57.5 14q-62 0 -107 -46l-39 48q58 63 154 63q70 0 111 -29.5t41 -78.5q0 -37 -26.5 -61.5t-62.5 -30.5q35 -3 65.5 -28t30.5 -68z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="841" 
d="M204 267h-81v296l-67 -69l-46 49l124 124h70v-400zM806 103q0 -54 -47 -82t-116 -28q-68 0 -115 27.5t-47 82.5q0 36 28 64.5t70 38.5q-40 10 -65.5 34t-25.5 60q0 53 46 79.5t109 26.5t109.5 -26.5t46.5 -79.5q0 -36 -25.5 -60t-64.5 -34q41 -10 69 -38.5t28 -64.5z
M719 289q0 26 -21 40t-55 14q-33 0 -54 -14t-21 -40q0 -19 16.5 -32.5t30.5 -18t28 -6.5q15 2 29 6.5t30.5 18t16.5 32.5zM726 111q0 30 -29 47t-54 19q-24 -3 -53 -19.5t-29 -46.5q0 -26 23 -41t59 -15t59.5 15t23.5 41zM619 667l-427 -667h-67l426 667h68z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="937" 
d="M903 103q0 -54 -47 -82t-116 -28q-68 0 -115 27.5t-47 82.5q0 36 28 64.5t70 38.5q-40 10 -65.5 34t-25.5 60q0 53 46 79.5t109 26.5t109.5 -26.5t46.5 -79.5q0 -36 -25.5 -60t-64.5 -34q41 -10 69 -38.5t28 -64.5zM816 289q0 26 -21 40t-55 14q-33 0 -54 -14t-21 -40
q0 -19 16.5 -32.5t30.5 -18t28 -6.5q15 2 29 6.5t30.5 18t16.5 32.5zM823 111q0 30 -29 47t-54 19q-24 -3 -53 -19.5t-29 -46.5q0 -26 23 -41t59 -15t59.5 15t23.5 41zM715 667l-427 -667h-67l426 667h68zM354 377q0 -52 -43.5 -84.5t-115.5 -32.5q-107 0 -159 64l41 52
q45 -51 115 -51q39 0 60.5 15.5t21.5 41.5q0 57 -95 57q-37 0 -43 -1v65q8 -1 42 -1q89 0 89 53q0 25 -22.5 39t-57.5 14q-62 0 -107 -46l-39 48q58 63 154 63q70 0 111 -29.5t41 -78.5q0 -37 -26.5 -61.5t-62.5 -30.5q35 -3 65.5 -28t30.5 -68z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="932" 
d="M898 103q0 -54 -47 -82t-116 -28q-68 0 -115 27.5t-47 82.5q0 36 28 64.5t70 38.5q-40 10 -65.5 34t-25.5 60q0 53 46 79.5t109 26.5t109.5 -26.5t46.5 -79.5q0 -36 -25.5 -60t-64.5 -34q41 -10 69 -38.5t28 -64.5zM811 289q0 26 -21 40t-55 14q-33 0 -54 -14t-21 -40
q0 -19 16.5 -32.5t30.5 -18t28 -6.5q15 2 29 6.5t30.5 18t16.5 32.5zM818 111q0 30 -29 47t-54 19q-24 -3 -53 -19.5t-29 -46.5q0 -26 23 -41t59 -15t59.5 15t23.5 41zM355 396q0 -61 -44.5 -98.5t-116.5 -37.5q-105 0 -156 64l45 53q41 -52 111 -52q37 0 59 20t22 49
q0 31 -21.5 50.5t-57.5 19.5q-53 0 -89 -39l-57 19v223h278v-65h-197v-113q36 34 93 34q55 0 93 -33t38 -94zM710 667l-427 -667h-67l426 667h68z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="854" 
d="M820 103q0 -54 -47 -82t-116 -28q-68 0 -115 27.5t-47 82.5q0 36 28 64.5t70 38.5q-40 10 -65.5 34t-25.5 60q0 53 46 79.5t109 26.5t109.5 -26.5t46.5 -79.5q0 -36 -25.5 -60t-64.5 -34q41 -10 69 -38.5t28 -64.5zM733 289q0 26 -21 40t-55 14q-33 0 -54 -14t-21 -40
q0 -19 16.5 -32.5t30.5 -18t28 -6.5q15 2 29 6.5t30.5 18t16.5 32.5zM740 111q0 30 -29 47t-54 19q-24 -3 -53 -19.5t-29 -46.5q0 -26 23 -41t59 -15t59.5 15t23.5 41zM333 611l-156 -344h-89l157 335h-216v65h304v-56zM632 667l-427 -667h-67l426 667h68z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="582" 
d="M538 211h-269l12 -130l-281 205l281 204l-12 -129h269v-150z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="582" 
d="M216 17v269l-130 -12l205 281l204 -281l-129 12v-269h-150z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="582" 
d="M584 286l-281 -205l12 130h-268v150h268l-12 129z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="582" 
d="M366 555v-269l130 12l-205 -281l-204 281l129 -12v269h150z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="573" 
d="M286 -12q-112 0 -180 69.5t-68 170.5q0 100 62 170t155 70q102 0 154 -80q-29 69 -87.5 130t-144.5 104l82 60q113 -63 194.5 -178t81.5 -251q0 -119 -67 -192t-182 -73zM286 81q65 0 102.5 42t37.5 105t-37.5 105t-102.5 42q-64 0 -101.5 -42t-37.5 -105t37.5 -105
t101.5 -42z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="673" 
d="M671 0h-670l262 667h146zM506 103l-170 446l-169 -446h339z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="753" 
d="M619 -90h-117v654h-251v-654h-117v654h-106v103h697v-103h-106v-654z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="581" 
d="M195 564l212 -267l-217 -284h342v-103h-483v103l215 283l-214 268v103h482v-103h-337z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="502" 
d="M473 303h-444v68h444v-68z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="150" 
d="M322 667l-427 -667h-67l426 667h68z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="245" 
d="M192 244q0 -28 -21 -48.5t-49 -20.5t-49 20.5t-21 48.5t21 49t49 21t49 -21t21 -49z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="700" 
d="M414 0h-73l-120 313l-141 -53l-22 64l209 75l112 -293l214 561h79z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="675" 
d="M190 163q-79 0 -120 51.5t-41 120.5q0 70 40.5 121t120.5 51q49 0 87 -29t60 -75q23 46 61.5 75t86.5 29q80 0 120.5 -51t40.5 -121t-40.5 -121t-120.5 -51q-48 0 -86.5 29t-61.5 75q-22 -46 -60 -75t-87 -29zM477 231q44 0 70.5 28.5t26.5 75.5t-26.5 76t-70.5 29
q-36 0 -64 -30t-44 -75q16 -45 44 -74.5t64 -29.5zM199 230q69 0 108 105q-39 105 -108 105q-44 0 -71 -29t-27 -76q0 -46 27 -75.5t71 -29.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="362" 
d="M78 -100h-61v72h61q27 0 45.5 22t18.5 55v570q0 66 42.5 107.5t99.5 41.5h61v-72h-61q-27 0 -45.5 -21.5t-18.5 -55.5v-571q0 -66 -42 -107t-100 -41z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="502" 
d="M29 205v74q49 -31 111 -31q46 0 105 27t116 27q64 0 112 -25v-74q-50 32 -112 32q-46 0 -105 -27.5t-116 -27.5q-63 0 -111 25zM29 390v74q49 -31 111 -31q46 0 105 27.5t116 27.5q60 0 112 -25v-74q-49 31 -112 31q-46 0 -105 -27t-116 -27q-61 0 -111 24z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="502" 
d="M131 93h-67l71 112h-106v67h149l79 123h-228v68h271l70 111h68l-71 -111h106v-68h-149l-79 -123h228v-67h-271z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="502" 
d="M473 105l-444 207v75l444 208v-81l-365 -165l365 -164v-80zM473 0h-444v67h444v-67z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="502" 
d="M472 312l-444 -207v80l365 164l-365 165v81l444 -208v-75zM472 0h-444v67h444v-67z" />
    <glyph glyph-name="triangleright" unicode="&#x22b2;" horiz-adv-x="587" 
d="M589 286l-535 -309v618z" />
    <glyph glyph-name="triangleleft" unicode="&#x22b3;" horiz-adv-x="587" 
d="M535 595v-618l-535 309z" />
    <glyph glyph-name="dotmath" unicode="&#x22c5;" horiz-adv-x="245" 
d="M192 244q0 -28 -21 -48.5t-49 -20.5t-49 20.5t-21 48.5t21 49t49 21t49 -21t21 -49z" />
    <glyph glyph-name="uni2318" unicode="&#x2318;" horiz-adv-x="667" 
d="M222 154v69h-70q-28 0 -48 -20t-20 -49t20 -50t49 -21q28 0 48.5 21t20.5 50zM222 443v71q0 29 -20 49t-48 20q-29 0 -49.5 -20t-20.5 -49t20.5 -50t49.5 -21h68zM265 265h137v137h-137v-137zM582 154q0 29 -19.5 49t-48.5 20h-69v-69q0 -29 20 -50t49 -21q28 0 48 21
t20 50zM582 514q0 29 -20 49t-48 20q-29 0 -49 -20t-20 -49v-71h69q28 0 48 21t20 50zM625 154q0 -46 -32.5 -79t-78.5 -33t-79 33t-33 79v69h-137v-69q0 -46 -33 -79t-79 -33t-78.5 33t-32.5 79t32.5 78.5t78.5 32.5h69v137h-68q-46 0 -79 33t-33 79t33 78.5t79 32.5
t78.5 -32.5t32.5 -78.5v-71h137v71q0 46 33 78.5t79 32.5t78.5 -32.5t32.5 -78.5t-32.5 -79t-78.5 -33h-69v-137h69q46 0 78.5 -32.5t32.5 -78.5z" />
    <glyph glyph-name="blacksquare" unicode="&#x25a0;" horiz-adv-x="661" 
d="M618 0h-571v572h571v-572z" />
    <glyph glyph-name="triagup" unicode="&#x25b2;" horiz-adv-x="616" 
d="M617 29l-619 1l309 535z" />
    <glyph glyph-name="triagdn" unicode="&#x25bc;" horiz-adv-x="616" 
d="M617 510l-310 -535l-309 535h619z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="375" 
d="M198 0h-21l-158 334l158 333h21l158 -333zM188 41l132 293l-133 292l-132 -292z" />
    <glyph glyph-name="circlesolid" unicode="&#x25cf;" horiz-adv-x="695" 
d="M644 285q0 -123 -87 -210t-210 -87t-210 87t-87 210t87 210t210 87t210 -87t87 -210z" />
    <glyph glyph-name="blackstar" unicode="&#x2605;" horiz-adv-x="759" 
d="M759 421l-235 -171l90 -275l-235 170l-234 -170l89 275l-234 171h290l89 276l90 -276h290z" />
    <glyph glyph-name="ballotbox" unicode="&#x2610;" horiz-adv-x="661" 
d="M616 0h-571v572h571v-572zM542 67v437h-424v-437h424z" />
    <glyph glyph-name="boxchecked" unicode="&#x2611;" horiz-adv-x="661" 
d="M616 0h-571v572h493l88 162l59 -36l-69 -128v-570zM542 67v367l-186 -343l-202 246l53 49l139 -169l155 287h-383v-437h424z" />
    <glyph glyph-name="diamond" unicode="&#x2666;" horiz-adv-x="501" 
d="M501 285l-251 -250l-250 250l250 251z" />
    <glyph glyph-name="checkmark" unicode="&#x2713;" horiz-adv-x="531" 
d="M531 698l-329 -607l-202 246l53 49l139 -169l280 517z" />
    <glyph glyph-name="perthousand.onum" unicode="&#xf628;" horiz-adv-x="893" 
d="M292 424q0 -59 -37.5 -98t-96.5 -39t-96 39t-37 98t37 98.5t96 39.5t96.5 -39.5t37.5 -98.5zM579 126q0 -59 -37.5 -98t-96.5 -39t-96 39t-37 98t37 98.5t96 39.5t96.5 -39.5t37.5 -98.5zM525 550l-383 -550h-63l382 550h64zM226 424q0 35 -19 58t-49 23q-29 0 -48 -23
t-19 -58t19 -57.5t48 -22.5q30 0 49 22.5t19 57.5zM513 126q0 36 -18.5 58.5t-49.5 22.5q-30 0 -49 -22.5t-19 -58.5q0 -35 19 -57.5t49 -22.5t49 22.5t19 57.5zM868 126q0 -59 -37 -98t-96 -39t-96 40t-37 97q0 59 37 98.5t96 39.5t96 -39.5t37 -98.5zM803 126
q0 36 -19 58.5t-49 22.5q-31 0 -49.5 -22.5t-18.5 -58.5q0 -35 19 -57.5t49 -22.5t49 22.5t19 57.5z" />
    <glyph glyph-name="uniF637" unicode="&#xf637;" horiz-adv-x="405" 
d="M351 326h-78v194q0 63 -63 63q-46 0 -79 -39v-218h-77v434h77v-162q44 50 114 50q106 0 106 -105v-217z" />
    <glyph glyph-name="zero.tzero" unicode="&#xf638;" horiz-adv-x="612" 
d="M306 -12q-87 0 -149 49l-23 -37h-85l58 90q-68 95 -68 243q0 65 16 125t47 109.5t84 79.5t120 30q85 0 147 -48l24 38h85l-59 -91q70 -96 70 -243q0 -65 -16 -125t-47 -110t-84 -80t-120 -30zM306 92q76 0 112 68t36 173q0 79 -20 136l-218 -340q35 -37 90 -37zM158 333
q0 -80 18 -134l218 338q-36 36 -88 36t-86.5 -34.5t-48 -86.5t-13.5 -119z" />
    <glyph glyph-name="zero.tnum" unicode="&#xf639;" horiz-adv-x="612" 
d="M306 -12q-67 0 -120 30t-84 80t-47 110t-16 125t16 125t47 109.5t84 79.5t120 30t120 -30t84 -79.5t47 -109.5t16 -125t-16 -125t-47 -110t-84 -80t-120 -30zM306 92q76 0 112 68t36 173q0 67 -13.5 119t-48 86.5t-86.5 34.5t-86.5 -34.5t-48 -86.5t-13.5 -119
q0 -105 36 -173t112 -68z" />
    <glyph glyph-name="two.tnum" unicode="&#xf63a;" horiz-adv-x="612" 
d="M551 0h-483v92q209 158 284.5 234.5t75.5 144.5q0 49 -35.5 75.5t-85.5 26.5q-110 0 -180 -80l-68 77q43 52 108.5 79.5t137.5 27.5q102 0 172 -55t70 -151q0 -86 -72 -171t-220 -197h296v-103z" />
    <glyph glyph-name="three.tnum" unicode="&#xf63b;" horiz-adv-x="612" 
d="M304 -12q-85 0 -150.5 30t-101.5 78l63 77q32 -37 82 -59t103 -22q65 0 102 27.5t37 74.5q0 94 -148 94q-67 0 -77 -1v105q12 -1 77 -1q62 0 99.5 22t37.5 66q0 45 -38 69.5t-97 24.5q-98 0 -172 -74l-60 73q89 105 243 105q109 0 175 -48.5t66 -131.5
q0 -62 -43.5 -102.5t-101.5 -50.5q57 -5 106 -48t49 -114q0 -86 -68.5 -140t-182.5 -54z" />
    <glyph glyph-name="four.tnum" unicode="&#xf63c;" horiz-adv-x="612" 
d="M477 0h-117v151h-318v94l274 422h161v-413h89v-103h-89v-151zM360 254v308l-203 -308h203z" />
    <glyph glyph-name="five.tnum" unicode="&#xf63d;" horiz-adv-x="612" 
d="M308 -12q-161 0 -248 103l67 80q70 -79 180 -79q62 0 99.5 33.5t37.5 83.5q0 54 -36.5 86.5t-97.5 32.5q-85 0 -146 -58l-83 24v373h437v-103h-320v-193q58 58 151 58q88 0 150.5 -58.5t62.5 -156.5q0 -103 -70.5 -164.5t-183.5 -61.5z" />
    <glyph glyph-name="six.tnum" unicode="&#xf63e;" horiz-adv-x="612" 
d="M325 -12q-71 0 -125 27.5t-86 75.5t-48 108.5t-16 132.5q0 151 77 248t217 97q116 0 192 -74l-55 -89q-56 59 -137 59q-80 0 -128 -64t-48 -156q0 -13 1 -19q25 37 74 65t105 28q95 0 159 -56.5t64 -158.5q0 -95 -68 -159.5t-178 -64.5zM319 92q62 0 98 35.5t36 80.5
q0 58 -39 88.5t-98 30.5q-42 0 -81 -21.5t-65 -57.5q6 -64 42 -110t107 -46z" />
    <glyph glyph-name="seven.tnum" unicode="&#xf63f;" horiz-adv-x="612" 
d="M288 0h-128l252 564h-346v103h481v-81z" />
    <glyph glyph-name="eight.tnum" unicode="&#xf640;" horiz-adv-x="612" 
d="M306 -12q-108 0 -182 48.5t-74 134.5q0 61 42.5 106t107.5 67q-62 20 -100.5 59t-38.5 99q0 44 21.5 79t57 55t78 30.5t88.5 10.5t88 -10.5t78 -30.5t57.5 -55t21.5 -79q0 -113 -140 -158q65 -22 107.5 -67t42.5 -106q0 -86 -73.5 -134.5t-181.5 -48.5zM306 390
q27 5 52 13.5t49.5 30t24.5 50.5q0 41 -35.5 65t-90.5 24q-56 0 -91.5 -24t-35.5 -65q0 -29 25 -50.5t50 -30t52 -13.5zM306 92q57 0 97 26t40 68q0 45 -46.5 72.5t-90.5 32.5q-26 -3 -55 -13.5t-56 -35t-27 -56.5q0 -42 39.5 -68t98.5 -26z" />
    <glyph glyph-name="nine.tnum" unicode="&#xf641;" horiz-adv-x="612" 
d="M269 -11q-117 0 -191 74l54 88q56 -58 137 -58q85 0 130.5 64.5t45.5 154.5v20q-26 -37 -75 -65.5t-104 -28.5q-95 0 -159.5 57t-64.5 159q0 95 68.5 159.5t177.5 64.5q94 0 157.5 -48.5t90.5 -124t27 -172.5q0 -150 -77 -247t-217 -97zM297 339q43 0 82 21t64 57
q-5 63 -41.5 110t-107.5 47q-61 0 -97 -36t-36 -80q0 -58 39 -88.5t97 -30.5z" />
    <glyph glyph-name="percent.onum" unicode="&#xf642;" horiz-adv-x="604" 
d="M292 424q0 -59 -37.5 -98t-96.5 -39t-96 39t-37 98t37 98.5t96 39.5t96.5 -39.5t37.5 -98.5zM579 126q0 -59 -37.5 -98t-96.5 -39t-96 39t-37 98t37 98.5t96 39.5t96.5 -39.5t37.5 -98.5zM525 550l-384 -550h-63l383 550h64zM226 424q0 35 -19 58t-49 23q-29 0 -48 -23
t-19 -58t19 -57.5t48 -22.5q30 0 49 22.5t19 57.5zM513 126q0 36 -18.5 58.5t-49.5 22.5q-30 0 -49 -22.5t-19 -58.5q0 -35 19 -57.5t49 -22.5t49 22.5t19 57.5z" />
    <glyph glyph-name="zero.tonum" unicode="&#xf643;" horiz-adv-x="612" 
d="M573 275q0 -120 -68.5 -203.5t-198.5 -83.5t-198.5 83.5t-68.5 203.5t68.5 203.5t198.5 83.5t198.5 -83.5t68.5 -203.5zM454 275q0 78 -38 130.5t-110 52.5t-110 -52.5t-38 -130.5t38 -130.5t110 -52.5t110 52.5t38 130.5z" />
    <glyph glyph-name="one.tonum" unicode="&#xf644;" horiz-adv-x="612" 
d="M427 0h-117v398l-109 -113l-68 71l192 194h102v-550z" />
    <glyph glyph-name="two.tonum" unicode="&#xf645;" horiz-adv-x="612" 
d="M527 0h-453v93q178 65 262 135t84 134q0 42 -33.5 69t-82.5 27q-110 0 -181 -80l-68 77q43 52 111 79.5t140 27.5q104 0 168 -49.5t64 -132.5q0 -80 -72.5 -156t-179.5 -121h241v-103z" />
    <glyph glyph-name="three.tonum" unicode="&#xf646;" horiz-adv-x="612" 
d="M304 -127q-85 0 -150.5 30t-101.5 78l63 77q32 -37 82 -59t103 -22q65 0 102 27.5t37 74.5q0 94 -148 94q-67 0 -77 -1v105q12 -1 77 -1q62 0 99.5 22t37.5 66q0 45 -38 69.5t-97 24.5q-98 0 -172 -74l-60 73q89 105 243 105q109 0 175 -48.5t66 -131.5
q0 -62 -43.5 -102.5t-101.5 -50.5q57 -5 106 -48t49 -114q0 -86 -68.5 -140t-182.5 -54z" />
    <glyph glyph-name="four.tonum" unicode="&#xf647;" horiz-adv-x="612" 
d="M566 39h-89v-156h-117v156h-318v94l274 417h161v-408h89v-103zM360 142v303l-203 -303h203z" />
    <glyph glyph-name="five.tonum" unicode="&#xf648;" horiz-adv-x="612" 
d="M308 -129q-161 0 -248 103l67 80q70 -79 180 -79q62 0 99.5 33.5t37.5 83.5q0 54 -36.5 86.5t-97.5 32.5q-85 0 -146 -58l-83 24v373h437v-103h-320v-193q58 58 151 58q88 0 150.5 -58.5t62.5 -156.5q0 -103 -70.5 -164.5t-183.5 -61.5z" />
    <glyph glyph-name="six.tonum" unicode="&#xf649;" horiz-adv-x="612" 
d="M325 -12q-71 0 -125 27.5t-86 75.5t-48 108.5t-16 132.5q0 151 77 248t217 97q116 0 192 -74l-55 -89q-56 59 -137 59q-80 0 -128 -64t-48 -156q0 -13 1 -19q25 37 74 65t105 28q95 0 159 -56.5t64 -158.5q0 -95 -68 -159.5t-178 -64.5zM319 92q62 0 98 35.5t36 80.5
q0 58 -39 88.5t-98 30.5q-42 0 -81 -21.5t-65 -57.5q6 -64 42 -110t107 -46z" />
    <glyph glyph-name="seven.tonum" unicode="&#xf64a;" horiz-adv-x="612" 
d="M288 -117h-128l252 564h-346v103h481v-81z" />
    <glyph glyph-name="eight.tonum" unicode="&#xf64b;" horiz-adv-x="612" 
d="M306 -12q-108 0 -182 48.5t-74 134.5q0 61 42.5 106t107.5 67q-62 20 -100.5 59t-38.5 99q0 44 21.5 79t57 55t78 30.5t88.5 10.5t88 -10.5t78 -30.5t57.5 -55t21.5 -79q0 -113 -140 -158q65 -22 107.5 -67t42.5 -106q0 -86 -73.5 -134.5t-181.5 -48.5zM306 390
q27 5 52 13.5t49.5 30t24.5 50.5q0 41 -35.5 65t-90.5 24q-56 0 -91.5 -24t-35.5 -65q0 -29 25 -50.5t50 -30t52 -13.5zM306 92q57 0 97 26t40 68q0 45 -46.5 72.5t-90.5 32.5q-26 -3 -55 -13.5t-56 -35t-27 -56.5q0 -42 39.5 -68t98.5 -26z" />
    <glyph glyph-name="nine.tonum" unicode="&#xf64c;" horiz-adv-x="612" 
d="M269 -127q-117 0 -191 74l54 88q56 -58 137 -58q85 0 130.5 64.5t45.5 154.5v20q-26 -37 -75 -65.5t-104 -28.5q-95 0 -159.5 57t-64.5 159q0 95 68.5 159.5t177.5 64.5q94 0 157.5 -48.5t90.5 -124t27 -172.5q0 -150 -77 -247t-217 -97zM297 223q43 0 82 21t64 57
q-5 63 -41.5 110t-107.5 47q-61 0 -97 -36t-36 -80q0 -58 39 -88.5t97 -30.5z" />
    <glyph glyph-name="colonmonetary.onum" unicode="&#xf64d;" horiz-adv-x="584" 
d="M131 -53l31 90q-58 38 -91 99.5t-33 138.5q0 126 84.5 206.5t212.5 80.5h4l13 37h51l-14 -42q27 -6 51 -15l20 57h51l-27 -80q49 -33 81 -86l-90 -45q-10 17 -25 31l-113 -335q44 1 81 22t57 55l90 -44q-78 -129 -230 -129q-11 0 -31 2l-14 -43h-51l17 50q-25 5 -51 17
l-23 -67h-51zM149 275q0 -80 49 -132l108 321q-69 -10 -113 -62t-44 -127zM238 110q22 -13 50 -21l121 359q-27 13 -52 16z" />
    <glyph glyph-name="Euro.onum" unicode="&#xf64e;" horiz-adv-x="610" 
d="M404 305h-227q-2 -10 -2 -30q0 -12 2 -32h227v-53h-211q22 -49 67 -77.5t101 -28.5q44 0 82 21.5t58 55.5l90 -44q-78 -129 -230 -129q-104 0 -181.5 55t-103.5 147h-51v53h40q-1 10 -1 32q0 20 1 30h-40v54h50q26 93 103.5 148t182.5 55q151 0 230 -129l-90 -45
q-22 35 -59 56.5t-81 21.5q-57 0 -102 -29t-67 -78h212v-54z" />
    <glyph glyph-name="florin.onum" unicode="&#xf64f;" horiz-adv-x="384" 
d="M136 -119h-119l73 327h-46v76h62l27 116q36 161 165 161q60 0 96 -36l-40 -84q-11 17 -38 17q-50 0 -68 -75l-22 -99h93v-76h-110z" />
    <glyph glyph-name="numbersign.onum" unicode="&#xf650;" horiz-adv-x="508" 
d="M489 398l-18 -59h-83l-42 -125h84l-19 -60h-85l-52 -154h-73l52 154h-82l-51 -154h-73l51 154h-79l18 60h82l42 125h-82l18 59h84l51 152h73l-52 -152h82l52 152h73l-52 -152h81zM314 339h-80l-43 -125h82z" />
    <glyph glyph-name="sterling.onum" unicode="&#xf651;" horiz-adv-x="439" 
d="M15 224v60h56q-36 56 -36 107q0 73 57 121.5t135 48.5q130 0 181 -94l-81 -44q-26 59 -90 59q-39 0 -66.5 -25.5t-27.5 -67.5q0 -41 40 -105h130v-60h-98q7 -23 7 -39q0 -26 -13.5 -50t-32.5 -37q9 3 19 3q27 0 62.5 -13t55.5 -13q50 0 73 30l43 -75q-39 -41 -120 -41
q-38 0 -78.5 16.5t-68.5 16.5q-33 0 -93 -30l-35 69q95 41 95 101q0 24 -20 62h-94z" />
    <glyph glyph-name="yen.onum" unicode="&#xf652;" horiz-adv-x="557" 
d="M334 0h-108v110h-202v59h202v59h-202v59h161l-185 263h124l156 -229l153 229h124l-183 -263h157v-59h-197v-59h197v-59h-197v-110z" />
    <glyph glyph-name="cent.onum" unicode="&#xf654;" horiz-adv-x="584" 
d="M293 -87v78q-112 13 -183.5 91t-71.5 193t71.5 193t183.5 91v62h78v-61q126 -15 194 -127l-90 -45q-36 58 -104 74v-375q70 16 104 74l90 -44q-67 -111 -194 -127v-77h-78zM149 275q0 -71 40 -122t104 -65v374q-64 -14 -104 -65t-40 -122z" />
    <glyph glyph-name="zero.dnom" unicode="&#xf655;" horiz-adv-x="393" 
d="M364 200q0 -85 -43 -146t-124 -61q-83 0 -125.5 61t-42.5 146t42.5 145.5t125.5 60.5q81 0 124 -61t43 -145zM282 200q0 141 -85 141q-45 0 -66 -38.5t-21 -102.5q0 -142 87 -142q85 0 85 142z" />
    <glyph glyph-name="one.dnom" unicode="&#xf656;" horiz-adv-x="264" 
d="M204 0h-81v296l-67 -69l-46 49l124 124h70v-400z" />
    <glyph glyph-name="two.dnom" unicode="&#xf657;" horiz-adv-x="393" 
d="M352 0h-304v59q128 91 174.5 136t46.5 83q0 31 -21.5 47t-53.5 16q-34 0 -64 -15t-46 -36l-43 51q55 65 155 65q66 0 109.5 -32t43.5 -90q0 -51 -44.5 -101.5t-139.5 -117.5h187v-65z" />
    <glyph glyph-name="three.dnom" unicode="&#xf658;" horiz-adv-x="393" 
d="M354 110q0 -52 -43.5 -84.5t-115.5 -32.5q-107 0 -159 64l41 52q45 -51 115 -51q39 0 60.5 15.5t21.5 41.5q0 57 -95 57q-37 0 -43 -1v65q8 -1 42 -1q89 0 89 53q0 25 -22.5 39t-57.5 14q-62 0 -107 -46l-39 48q58 63 154 63q70 0 111 -29.5t41 -78.5q0 -37 -26.5 -61.5
t-62.5 -30.5q35 -3 65.5 -28t30.5 -68z" />
    <glyph glyph-name="four.dnom" unicode="&#xf659;" horiz-adv-x="393" 
d="M364 92h-55v-92h-80v92h-196v56l164 252h112v-244h55v-64zM229 156v175l-116 -175h116z" />
    <glyph glyph-name="five.dnom" unicode="&#xf65a;" horiz-adv-x="393" 
d="M355 129q0 -61 -44.5 -98.5t-116.5 -37.5q-105 0 -156 64l45 53q41 -52 111 -52q37 0 59 20t22 49q0 31 -21.5 50.5t-57.5 19.5q-53 0 -89 -39l-57 19v223h278v-65h-197v-113q36 34 93 34q55 0 93 -33t38 -94z" />
    <glyph glyph-name="six.dnom" unicode="&#xf65b;" horiz-adv-x="393" 
d="M361 129q0 -59 -43 -97.5t-111 -38.5q-88 0 -131.5 57.5t-43.5 148.5q0 92 49.5 149.5t138.5 57.5q70 0 120 -42l-36 -57q-36 34 -84 34q-50 0 -78.5 -36t-28.5 -87v-12q18 23 47 39t61 16q59 0 99.5 -35.5t40.5 -96.5zM280 126q0 35 -23.5 53t-57.5 18q-52 0 -85 -44
q3 -40 25 -67.5t64 -27.5q35 0 56 21t21 47z" />
    <glyph glyph-name="seven.dnom" unicode="&#xf65c;" horiz-adv-x="367" 
d="M333 344l-156 -344h-89l157 335h-216v65h304v-56z" />
    <glyph glyph-name="eight.dnom" unicode="&#xf65d;" horiz-adv-x="393" 
d="M359 103q0 -54 -47 -82t-116 -28q-68 0 -115 27.5t-47 82.5q0 36 28 64.5t70 38.5q-40 10 -65.5 34t-25.5 60q0 53 46 79.5t109 26.5t109.5 -26.5t46.5 -79.5q0 -36 -25.5 -60t-64.5 -34q41 -10 69 -38.5t28 -64.5zM272 289q0 26 -21 40t-55 14q-33 0 -54 -14t-21 -40
q0 -19 16.5 -32.5t30.5 -18t28 -6.5q15 2 29 6.5t30.5 18t16.5 32.5zM279 111q0 30 -29 47t-54 19q-24 -3 -53 -19.5t-29 -46.5q0 -26 23 -41t59 -15t59.5 15t23.5 41z" />
    <glyph glyph-name="nine.dnom" unicode="&#xf65e;" horiz-adv-x="393" 
d="M32 270q0 58 43 97t112 39q87 0 130.5 -58t43.5 -149q0 -92 -49.5 -149t-137.5 -57q-73 0 -120 42l36 57q33 -34 84 -34q49 0 77.5 35.5t28.5 87.5v12q-17 -23 -46 -39t-61 -16q-59 0 -100 35t-41 97zM113 273q0 -35 23.5 -53.5t57.5 -18.5q54 0 85 45q-3 39 -24.5 67
t-62.5 28q-36 0 -57.5 -21t-21.5 -47z" />
    <glyph glyph-name="zero.numr" unicode="&#xf661;" horiz-adv-x="393" 
d="M364 467q0 -85 -43 -146t-124 -61q-83 0 -125.5 61t-42.5 146t42.5 145.5t125.5 60.5q81 0 124 -61t43 -145zM282 467q0 141 -85 141q-45 0 -66 -38.5t-21 -102.5q0 -142 87 -142q85 0 85 142z" />
    <glyph glyph-name="one.numr" unicode="&#xf662;" horiz-adv-x="264" 
d="M204 267h-81v296l-67 -69l-46 49l124 124h70v-400z" />
    <glyph glyph-name="two.numr" unicode="&#xf663;" horiz-adv-x="393" 
d="M352 267h-304v59q128 91 174.5 136t46.5 83q0 31 -21.5 47t-53.5 16q-34 0 -64 -15t-46 -36l-43 51q55 65 155 65q66 0 109.5 -32t43.5 -90q0 -51 -44.5 -101.5t-139.5 -117.5h187v-65z" />
    <glyph glyph-name="three.numr" unicode="&#xf664;" horiz-adv-x="393" 
d="M354 377q0 -52 -43.5 -84.5t-115.5 -32.5q-107 0 -159 64l41 52q45 -51 115 -51q39 0 60.5 15.5t21.5 41.5q0 57 -95 57q-37 0 -43 -1v65q8 -1 42 -1q89 0 89 53q0 25 -22.5 39t-57.5 14q-62 0 -107 -46l-39 48q58 63 154 63q70 0 111 -29.5t41 -78.5q0 -37 -26.5 -61.5
t-62.5 -30.5q35 -3 65.5 -28t30.5 -68z" />
    <glyph glyph-name="four.numr" unicode="&#xf665;" horiz-adv-x="393" 
d="M364 359h-55v-92h-80v92h-196v56l164 252h112v-244h55v-64zM229 423v175l-116 -175h116z" />
    <glyph glyph-name="five.numr" unicode="&#xf666;" horiz-adv-x="393" 
d="M355 396q0 -61 -44.5 -98.5t-116.5 -37.5q-105 0 -156 64l45 53q41 -52 111 -52q37 0 59 20t22 49q0 31 -21.5 50.5t-57.5 19.5q-53 0 -89 -39l-57 19v223h278v-65h-197v-113q36 34 93 34q55 0 93 -33t38 -94z" />
    <glyph glyph-name="six.numr" unicode="&#xf667;" horiz-adv-x="393" 
d="M361 396q0 -59 -43 -97.5t-111 -38.5q-88 0 -131.5 57.5t-43.5 148.5q0 92 49.5 149.5t138.5 57.5q70 0 120 -42l-36 -57q-36 34 -84 34q-50 0 -78.5 -36t-28.5 -87v-12q18 23 47 39t61 16q59 0 99.5 -35.5t40.5 -96.5zM280 393q0 35 -23.5 53t-57.5 18q-52 0 -85 -44
q3 -40 25 -67.5t64 -27.5q35 0 56 21t21 47z" />
    <glyph glyph-name="seven.numr" unicode="&#xf668;" horiz-adv-x="367" 
d="M333 611l-156 -344h-89l157 335h-216v65h304v-56z" />
    <glyph glyph-name="eight.numr" unicode="&#xf669;" horiz-adv-x="393" 
d="M359 370q0 -54 -47 -82t-116 -28q-68 0 -115 27.5t-47 82.5q0 36 28 64.5t70 38.5q-40 10 -65.5 34t-25.5 60q0 53 46 79.5t109 26.5t109.5 -26.5t46.5 -79.5q0 -36 -25.5 -60t-64.5 -34q41 -10 69 -38.5t28 -64.5zM272 556q0 26 -21 40t-55 14q-33 0 -54 -14t-21 -40
q0 -44 75 -57q15 2 29 6.5t30.5 18t16.5 32.5zM279 378q0 30 -29 47t-54 19q-24 -3 -53 -19.5t-29 -46.5q0 -26 23 -41t59 -15t59.5 15t23.5 41z" />
    <glyph glyph-name="nine.numr" unicode="&#xf66a;" horiz-adv-x="393" 
d="M32 537q0 58 43 97t112 39q87 0 130.5 -58t43.5 -149q0 -92 -49.5 -149t-137.5 -57q-73 0 -120 42l36 57q33 -34 84 -34q49 0 77.5 35.5t28.5 87.5v12q-17 -23 -46 -39t-61 -16q-59 0 -100 35t-41 97zM113 540q0 -35 23.5 -53.5t57.5 -18.5q54 0 85 45q-3 39 -24.5 67
t-62.5 28q-36 0 -57.5 -21t-21.5 -47z" />
    <glyph glyph-name="Abreve.smcp" unicode="&#xf66d;" horiz-adv-x="586" 
d="M227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM469 672q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="Amacron.smcp" unicode="&#xf66e;" horiz-adv-x="586" 
d="M475 616h-363v62h363v-62zM227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194z" />
    <glyph glyph-name="Aogonek.smcp" unicode="&#xf66f;" horiz-adv-x="586" 
d="M227 550h132l227 -550q-73 -36 -73 -93q0 -23 11 -34.5t29 -11.5q34 0 47 38l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 55 48 97h-42l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194z" />
    <glyph glyph-name="AEacute.smcp" unicode="&#xf670;" horiz-adv-x="835" 
d="M621 750l-165 -144h-75l140 144h100zM789 0h-402v103h-201l-64 -103h-123l346 550h444v-95h-293v-127h286v-95h-286v-138h293v-95zM387 198v240l-149 -240h149z" />
    <glyph glyph-name="Cacute.smcp" unicode="&#xf671;" horiz-adv-x="584" 
d="M515 750l-165 -144h-75l140 144h100zM335 -12q-128 0 -212.5 80.5t-84.5 206.5t84.5 206.5t212.5 80.5q151 0 230 -129l-90 -45q-22 35 -59 56.5t-81 21.5q-80 0 -133 -54t-53 -137q0 -82 53 -136.5t133 -54.5q44 0 82 21.5t58 55.5l90 -44q-78 -129 -230 -129z" />
    <glyph glyph-name="Ccaron.smcp" unicode="&#xf672;" horiz-adv-x="584" 
d="M387 606h-96l-96 144h66l78 -97l74 97h67zM335 -12q-128 0 -212.5 80.5t-84.5 206.5t84.5 206.5t212.5 80.5q151 0 230 -129l-90 -45q-22 35 -59 56.5t-81 21.5q-80 0 -133 -54t-53 -137q0 -82 53 -136.5t133 -54.5q44 0 82 21.5t58 55.5l90 -44q-78 -129 -230 -129z" />
    <glyph glyph-name="Ccircumflex.smcp" unicode="&#xf673;" horiz-adv-x="584" 
d="M480 606h-67l-77 96l-75 -96h-66l93 144h96zM335 -12q-128 0 -212.5 80.5t-84.5 206.5t84.5 206.5t212.5 80.5q151 0 230 -129l-90 -45q-22 35 -59 56.5t-81 21.5q-80 0 -133 -54t-53 -137q0 -82 53 -136.5t133 -54.5q44 0 82 21.5t58 55.5l90 -44q-78 -129 -230 -129z
" />
    <glyph glyph-name="Cdotaccent.smcp" unicode="&#xf674;" horiz-adv-x="584" 
d="M335 -12q-128 0 -212.5 80.5t-84.5 206.5t84.5 206.5t212.5 80.5q151 0 230 -129l-90 -45q-22 35 -59 56.5t-81 21.5q-80 0 -133 -54t-53 -137q0 -82 53 -136.5t133 -54.5q44 0 82 21.5t58 55.5l90 -44q-78 -129 -230 -129zM337 598q-24 0 -41.5 17t-17.5 42
q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="Dcaron.smcp" unicode="&#xf675;" horiz-adv-x="621" 
d="M352 606h-96l-96 144h66l78 -97l74 97h67zM275 0h-218v550h217q135 0 216.5 -76.5t81.5 -199.5q0 -122 -81 -198t-216 -76zM274 95q88 0 137 51t49 128q0 79 -47.5 130t-137.5 51h-109v-360h108z" />
    <glyph glyph-name="Dcroat.smcp" unicode="&#xf676;" horiz-adv-x="631" 
d="M285 0h-218v240h-58v71h58v239h217q135 0 216.5 -76.5t81.5 -199.5q0 -122 -81 -198t-216 -76zM298 240h-122v-145h108q88 0 137 51t49 128q0 79 -47.5 130t-137.5 51h-109v-144h122v-71z" />
    <glyph glyph-name="Ebreve.smcp" unicode="&#xf677;" horiz-adv-x="515" 
d="M469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95zM444 672q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="Ecaron.smcp" unicode="&#xf678;" horiz-adv-x="515" 
d="M318 606h-96l-96 144h66l78 -97l74 97h67zM469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95z" />
    <glyph glyph-name="Edotaccent.smcp" unicode="&#xf679;" horiz-adv-x="515" 
d="M469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95zM269 598q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="Emacron.smcp" unicode="&#xf67a;" horiz-adv-x="515" 
d="M469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95zM450 616h-363v62h363v-62z" />
    <glyph glyph-name="Eng.smcp" unicode="&#xf67b;" horiz-adv-x="641" 
d="M371 -196q-93 0 -152 63l46 81q41 -48 98 -48q45 0 72.5 27t28.5 71l-287 382v-380h-109v550h116l280 -370v370h109v-561q0 -92 -55 -138.5t-147 -46.5z" />
    <glyph glyph-name="Eogonek.smcp" unicode="&#xf67c;" horiz-adv-x="515" 
d="M483 -101l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 55 48 97h-321v550h401v-95h-292v-127h286v-95h-286v-138h292v-95q-73 -36 -73 -93q0 -23 11 -34.5t29 -11.5q34 0 47 38z" />
    <glyph glyph-name="Gbreve.smcp" unicode="&#xf67d;" horiz-adv-x="620" 
d="M337 -12q-128 0 -213.5 80t-85.5 207q0 128 85.5 207.5t213.5 79.5q141 0 225 -111l-87 -53q-52 67 -138 67q-81 0 -134 -53.5t-53 -136.5t53 -137.5t134 -54.5q73 0 124 47v79h-147v91h256v-211q-94 -101 -233 -101zM513 672q-65 -88 -175 -88q-109 0 -176 88l48 40
q48 -66 128 -66t127 66z" />
    <glyph glyph-name="Gcircumflex.smcp" unicode="&#xf67e;" horiz-adv-x="620" 
d="M337 -12q-128 0 -213.5 80t-85.5 207q0 128 85.5 207.5t213.5 79.5q141 0 225 -111l-87 -53q-52 67 -138 67q-81 0 -134 -53.5t-53 -136.5t53 -137.5t134 -54.5q73 0 124 47v79h-147v91h256v-211q-94 -101 -233 -101zM480 606h-67l-77 96l-75 -96h-66l93 144h96z" />
    <glyph glyph-name="Gcommaaccent.smcp" unicode="&#xf67f;" horiz-adv-x="620" 
d="M337 -12q-128 0 -213.5 80t-85.5 207q0 128 85.5 207.5t213.5 79.5q141 0 225 -111l-87 -53q-52 67 -138 67q-81 0 -134 -53.5t-53 -136.5t53 -137.5t134 -54.5q73 0 124 47v79h-147v91h256v-211q-94 -101 -233 -101zM396 -138q0 -42 -21.5 -80t-56.5 -63l-39 31
q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55z" />
    <glyph glyph-name="Gdotaccent.smcp" unicode="&#xf680;" horiz-adv-x="620" 
d="M337 -12q-128 0 -213.5 80t-85.5 207q0 128 85.5 207.5t213.5 79.5q141 0 225 -111l-87 -53q-52 67 -138 67q-81 0 -134 -53.5t-53 -136.5t53 -137.5t134 -54.5q73 0 124 47v79h-147v91h256v-211q-94 -101 -233 -101zM338 598q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5
t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="Hcircumflex.smcp" unicode="&#xf681;" horiz-adv-x="644" 
d="M465 606h-67l-77 96l-75 -96h-66l93 144h96zM576 0h-109v234h-290v-234h-109v550h109v-221h290v221h109v-550z" />
    <glyph glyph-name="Hbar.smcp" unicode="&#xf682;" horiz-adv-x="661" 
d="M585 0h-109v234h-290v-234h-109v411h-68v62h68v77h109v-77h290v77h109v-77h67v-62h-67v-411zM186 329h290v82h-290v-82z" />
    <glyph glyph-name="Ibreve.smcp" unicode="&#xf683;" horiz-adv-x="245" 
d="M177 0h-109v550h109v-550zM299 672q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="IJ.smcp" unicode="&#xf684;" horiz-adv-x="673" 
d="M402 -12q-94 0 -151 63l46 81q41 -48 97 -48q46 0 73.5 27t27.5 71v368h110v-377q0 -92 -55 -138.5t-148 -46.5zM177 0h-109v550h109v-550z" />
    <glyph glyph-name="Imacron.smcp" unicode="&#xf685;" horiz-adv-x="245" 
d="M305 616h-363v62h363v-62zM177 0h-109v550h109v-550z" />
    <glyph glyph-name="Iogonek.smcp" unicode="&#xf686;" horiz-adv-x="245" 
d="M177 0q-73 -36 -73 -93q0 -23 11 -34.5t29 -11.5q34 0 47 38l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 55 48 97h-29v550h109v-550z" />
    <glyph glyph-name="Itilde.smcp" unicode="&#xf687;" horiz-adv-x="245" 
d="M177 0h-109v550h109v-550zM181 604q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="Jcircumflex.smcp" unicode="&#xf688;" horiz-adv-x="428" 
d="M447 606h-67l-77 96l-75 -96h-66l93 144h96zM157 -12q-94 0 -151 63l46 81q41 -48 97 -48q46 0 73.5 27t27.5 71v368h110v-377q0 -92 -55 -138.5t-148 -46.5z" />
    <glyph glyph-name="Kcommaaccent.smcp" unicode="&#xf689;" horiz-adv-x="552" 
d="M341 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM546 0h-133l-190 227l-46 -51v-176h-109v550h109v-251l218 251h133l-235 -260z" />
    <glyph glyph-name="Lacute.smcp" unicode="&#xf68a;" horiz-adv-x="464" 
d="M430 0h-362v550h109v-455h253v-95zM428 750l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="Lcaron.smcp" unicode="&#xf68b;" horiz-adv-x="464" 
d="M365 484q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM430 0h-362v550h109v-455h253v-95z" />
    <glyph glyph-name="Lcommaaccent.smcp" unicode="&#xf68c;" horiz-adv-x="464" 
d="M310 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM430 0h-362v550h109v-455h253v-95z" />
    <glyph glyph-name="Ldot.smcp" unicode="&#xf68d;" horiz-adv-x="487" 
d="M430 0h-362v550h109v-455h253v-95zM440 299q0 -28 -21 -48.5t-49 -20.5t-49 20.5t-21 48.5q0 29 21 49.5t49 20.5t49 -20.5t21 -49.5z" />
    <glyph glyph-name="Nacute.smcp" unicode="&#xf68e;" horiz-adv-x="641" 
d="M499 750l-165 -144h-75l140 144h100zM573 0h-111l-285 380v-380h-109v550h116l280 -370v370h109v-550z" />
    <glyph glyph-name="Ncaron.smcp" unicode="&#xf68f;" horiz-adv-x="641" 
d="M369 606h-96l-96 144h66l78 -97l74 97h67zM573 0h-111l-285 380v-380h-109v550h116l280 -370v370h109v-550z" />
    <glyph glyph-name="Ncommaaccent.smcp" unicode="&#xf690;" horiz-adv-x="641" 
d="M382 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM573 0h-111l-285 380v-380h-109v550h116l280 -370v370h109v-550z" />
    <glyph glyph-name="Obreve.smcp" unicode="&#xf691;" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM505 672q-65 -88 -175 -88q-109 0 -176 88
l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="Ohungarumlaut.smcp" unicode="&#xf692;" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM392 750l-115 -144h-63l90 144h88zM538 750
l-115 -144h-63l90 144h88z" />
    <glyph glyph-name="Omacron.smcp" unicode="&#xf693;" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM511 616h-363v62h363v-62z" />
    <glyph glyph-name="Oslashacute.smcp" unicode="&#xf694;" horiz-adv-x="658" 
d="M329 -12q-84 0 -151 36l-18 -24h-87l50 65q-85 82 -85 210q0 124 81 205.5t210 81.5q81 0 147 -36l18 24h87l-49 -64q87 -82 87 -211q0 -124 -81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 74 -41 126l-227 -296q40 -21 89 -21zM149 275q0 -74 39 -125
l226 296q-37 20 -85 20q-81 0 -130.5 -54t-49.5 -137zM508 750l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="Racute.smcp" unicode="&#xf695;" horiz-adv-x="561" 
d="M466 750l-165 -144h-75l140 144h100zM522 0h-126l-124 199h-95v-199h-109v550h260q87 0 137.5 -50t50.5 -125q0 -70 -39 -114t-97 -52zM316 294q42 0 65.5 22.5t23.5 58.5t-24 58t-65 22h-139v-161h139z" />
    <glyph glyph-name="Rcaron.smcp" unicode="&#xf696;" horiz-adv-x="561" 
d="M322 606h-96l-96 144h66l78 -97l74 97h67zM522 0h-126l-124 199h-95v-199h-109v550h260q87 0 137.5 -50t50.5 -125q0 -70 -39 -114t-97 -52zM316 294q42 0 65.5 22.5t23.5 58.5t-24 58t-65 22h-139v-161h139z" />
    <glyph glyph-name="Rcommaaccent.smcp" unicode="&#xf697;" horiz-adv-x="561" 
d="M338 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM522 0h-126l-124 199h-95v-199h-109v550h260q87 0 137.5 -50t50.5 -125q0 -70 -39 -114t-97 -52zM316 294
q42 0 65.5 22.5t23.5 58.5t-24 58t-65 22h-139v-161h139z" />
    <glyph glyph-name="Sacute.smcp" unicode="&#xf698;" horiz-adv-x="511" 
d="M433 750l-165 -144h-75l140 144h100zM254 -12q-148 0 -230 84l52 84q31 -32 79.5 -53.5t101.5 -21.5q57 0 85.5 21t28.5 53q0 26 -24.5 43t-61 24.5t-79 19.5t-79 26.5t-61 47.5t-24.5 81q0 70 59.5 117.5t151.5 47.5q133 0 217 -79l-53 -78q-31 30 -76 46.5t-89 16.5
t-71.5 -19t-27.5 -49q0 -26 34 -41.5t82 -25.5t96.5 -25.5t82.5 -52.5t34 -95q0 -78 -59 -125t-169 -47z" />
    <glyph glyph-name="Scedilla.smcp" unicode="&#xf699;" horiz-adv-x="511" 
d="M252 -194q-67 0 -106 31l22 46q36 -29 81 -29q23 0 39.5 9.5t16.5 26.5q0 30 -33 30q-21 0 -34 -15l-41 23l23 62q-128 10 -196 82l52 84q31 -32 79.5 -53.5t101.5 -21.5q57 0 85.5 21t28.5 53q0 26 -24.5 43t-61 24.5t-79 19.5t-79 26.5t-61 47.5t-24.5 81
q0 70 59.5 117.5t151.5 47.5q133 0 217 -79l-53 -78q-31 30 -76 46.5t-89 16.5t-71.5 -19t-27.5 -49q0 -26 34 -41.5t82 -25.5t96.5 -25.5t82.5 -52.5t34 -95q0 -75 -53.5 -121t-154.5 -50l-17 -42q16 11 36 11q29 0 48 -19.5t19 -49.5q0 -37 -31 -60t-77 -23z" />
    <glyph glyph-name="Scircumflex.smcp" unicode="&#xf69a;" horiz-adv-x="511" 
d="M400 606h-67l-77 96l-75 -96h-66l93 144h96zM254 -12q-148 0 -230 84l52 84q31 -32 79.5 -53.5t101.5 -21.5q57 0 85.5 21t28.5 53q0 26 -24.5 43t-61 24.5t-79 19.5t-79 26.5t-61 47.5t-24.5 81q0 70 59.5 117.5t151.5 47.5q133 0 217 -79l-53 -78q-31 30 -76 46.5
t-89 16.5t-71.5 -19t-27.5 -49q0 -26 34 -41.5t82 -25.5t96.5 -25.5t82.5 -52.5t34 -95q0 -78 -59 -125t-169 -47z" />
    <glyph glyph-name="Scommaaccent.smcp" unicode="&#xf69b;" horiz-adv-x="511" 
d="M320 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM254 -12q-148 0 -230 84l52 84q31 -32 79.5 -53.5t101.5 -21.5q57 0 85.5 21t28.5 53q0 26 -24.5 43t-61 24.5
t-79 19.5t-79 26.5t-61 47.5t-24.5 81q0 70 59.5 117.5t151.5 47.5q133 0 217 -79l-53 -78q-31 30 -76 46.5t-89 16.5t-71.5 -19t-27.5 -49q0 -26 34 -41.5t82 -25.5t96.5 -25.5t82.5 -52.5t34 -95q0 -78 -59 -125t-169 -47z" />
    <glyph glyph-name="Tbar.smcp" unicode="&#xf69c;" horiz-adv-x="480" 
d="M294 0h-109v221h-108v62h108v172h-162v95h434v-95h-163v-172h110v-62h-110v-221z" />
    <glyph glyph-name="Tcaron.smcp" unicode="&#xf69d;" horiz-adv-x="480" 
d="M290 606h-96l-96 144h66l78 -97l74 97h67zM294 0h-109v455h-162v95h434v-95h-163v-455z" />
    <glyph glyph-name="Tcommaaccent.smcp" unicode="&#xf69e;" horiz-adv-x="480" 
d="M302 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM294 0h-109v455h-162v95h434v-95h-163v-455z" />
    <glyph glyph-name="Ubreve.smcp" unicode="&#xf69f;" horiz-adv-x="636" 
d="M318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -106 -64 -167t-186 -61zM493 672q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="Uhungarumlaut.smcp" unicode="&#xf6a0;" horiz-adv-x="636" 
d="M318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -106 -64 -167t-186 -61zM379 750l-115 -144h-63l90 144h88zM525 750l-115 -144h-63l90 144h88z" />
    <glyph glyph-name="Umacron.smcp" unicode="&#xf6a1;" horiz-adv-x="636" 
d="M318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -106 -64 -167t-186 -61zM500 616h-363v62h363v-62z" />
    <glyph glyph-name="Uogonek.smcp" unicode="&#xf6a2;" horiz-adv-x="636" 
d="M318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -82 -39.5 -138.5t-115.5 -77.5q-73 -36 -73 -93q0 -23 11 -34.5t29 -11.5q34 0 47 38l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 47 36 85
h-3z" />
    <glyph glyph-name="Uring.smcp" unicode="&#xf6a3;" horiz-adv-x="636" 
d="M318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -106 -64 -167t-186 -61zM319 572q-42 0 -72 30t-30 72t30 72.5t72 30.5t71.5 -30.5t29.5 -72.5t-29.5 -72t-71.5 -30zM319 622q21 0 36 15.5
t15 36.5q0 22 -14.5 37t-36.5 15q-21 0 -36.5 -15t-15.5 -37q0 -21 15.5 -36.5t36.5 -15.5z" />
    <glyph glyph-name="Utilde.smcp" unicode="&#xf6a4;" horiz-adv-x="636" 
d="M318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -106 -64 -167t-186 -61zM376 604q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36
q29 0 50 -19.5t36.5 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="Wcircumflex.smcp" unicode="&#xf6a6;" horiz-adv-x="783" 
d="M534 606h-67l-77 96l-75 -96h-66l93 144h96zM613 0h-114l-108 389l-108 -389h-114l-165 550h121l107 -405l116 405h87l115 -406l108 406h121z" />
    <glyph glyph-name="Ycircumflex.smcp" unicode="&#xf6a9;" horiz-adv-x="557" 
d="M423 606h-67l-77 96l-75 -96h-66l93 144h96zM334 0h-108v229l-226 321h124l156 -229l153 229h124l-223 -321v-229z" />
    <glyph glyph-name="Zacute.smcp" unicode="&#xf6ab;" horiz-adv-x="520" 
d="M439 750l-165 -144h-75l140 144h100zM483 0h-446v80l290 374h-290v96h439v-80l-289 -374h296v-96z" />
    <glyph glyph-name="Zdotaccent.smcp" unicode="&#xf6ac;" horiz-adv-x="520" 
d="M483 0h-446v80l290 374h-290v96h439v-80l-289 -374h296v-96zM261 598q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="Idotaccent.smcp" unicode="&#xf6ad;" horiz-adv-x="245" 
d="M177 0h-109v550h109v-550zM123 598q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="parenleft.case" unicode="&#xf6ae;" horiz-adv-x="276" 
d="M258 -59l-65 -50q-71 86 -111 203.5t-40 238.5q0 120 40 237.5t111 204.5l65 -49q-52 -98 -77 -189t-25 -204t25 -204t77 -188z" />
    <glyph glyph-name="parenright.case" unicode="&#xf6af;" horiz-adv-x="276" 
d="M82 -109l-65 50q53 97 78 188t25 204t-25 204t-78 189l65 49q71 -87 111.5 -204t40.5 -238t-40.5 -238.5t-111.5 -203.5z" />
    <glyph glyph-name="bracketleft.case" unicode="&#xf6b0;" horiz-adv-x="264" 
d="M247 -100h-203v868h203v-72h-126v-724h126v-72z" />
    <glyph glyph-name="bracketright.case" unicode="&#xf6b1;" horiz-adv-x="264" 
d="M220 -100h-203v72h126v724h-126v72h203v-868z" />
    <glyph glyph-name="braceleft.case" unicode="&#xf6b2;" horiz-adv-x="276" 
d="M259 -100h-61q-58 0 -100 41t-42 107v187q0 29 -13.5 48.5t-38.5 19.5v62q25 0 38.5 19.5t13.5 48.5v186q0 66 42.5 107.5t99.5 41.5h61v-72h-61q-27 0 -45.5 -21.5t-18.5 -55.5v-196q0 -69 -46 -89q46 -20 46 -89v-196q0 -33 18.5 -55t45.5 -22h61v-72z" />
    <glyph glyph-name="braceright.case" unicode="&#xf6b3;" horiz-adv-x="276" 
d="M17 -100v72h61q27 0 45.5 22t18.5 55v196q0 69 46 89q-46 20 -46 89v196q0 34 -18.5 55.5t-45.5 21.5h-61v72h61q57 0 99.5 -41.5t42.5 -107.5v-186q0 -29 13.5 -48.5t38.5 -19.5v-62q-25 0 -38.5 -19.5t-13.5 -48.5v-187q0 -66 -42 -107t-100 -41h-61z" />
    <glyph glyph-name="exclamdown.case" unicode="&#xf6b4;" horiz-adv-x="245" 
d="M59 0l18 462h91l19 -462h-128zM53 608q0 28 20.5 48.5t48.5 20.5t49 -20.5t21 -48.5t-21 -49t-49 -21t-48.5 20.5t-20.5 49.5z" />
    <glyph glyph-name="questiondown.case" unicode="&#xf6b5;" horiz-adv-x="458" 
d="M269 469q36 -35 36 -85q0 -41 -25 -73.5t-55.5 -50.5t-55.5 -43t-25 -53q0 -31 24.5 -51.5t70.5 -20.5q82 0 139 70l67 -75q-81 -99 -218 -99q-93 0 -148 44t-55 113q0 41 18.5 73.5t45 53t52.5 38.5t44.5 39.5t18.5 45.5q0 26 -22 45zM233 677q29 0 49.5 -20.5
t20.5 -49.5q0 -28 -21 -48.5t-49 -20.5t-49 20.5t-21 48.5q0 29 20.5 49.5t49.5 20.5z" />
    <glyph glyph-name="guillemotleft.case" unicode="&#xf6b6;" horiz-adv-x="489" 
d="M459 162h-99l-160 180l160 177h99l-160 -177zM289 162h-99l-160 180l160 177h99l-160 -177z" />
    <glyph glyph-name="guillemotright.case" unicode="&#xf6b7;" horiz-adv-x="489" 
d="M289 342l-160 -180h-99l160 180l-160 177h99zM459 342l-160 -180h-99l160 180l-160 177h99z" />
    <glyph glyph-name="guilsinglleft.case" unicode="&#xf6b8;" horiz-adv-x="319" 
d="M289 162h-99l-160 180l160 177h99l-160 -177z" />
    <glyph glyph-name="guilsinglright.case" unicode="&#xf6b9;" horiz-adv-x="319" 
d="M289 342l-160 -180h-99l160 180l-160 177h99z" />
    <glyph glyph-name="hyphen.case" unicode="&#xf6ba;" horiz-adv-x="300" 
d="M270 296h-240v90h240v-90z" />
    <glyph glyph-name="endash.case" unicode="&#xf6bb;" horiz-adv-x="593" 
d="M563 296h-533v90h533v-90z" />
    <glyph glyph-name="emdash.case" unicode="&#xf6bc;" horiz-adv-x="833" 
d="M803 296h-773v90h773v-90z" />
    <glyph glyph-name="periodcentered.case" unicode="&#xf6bd;" horiz-adv-x="245" 
d="M192 342q0 -28 -21 -48.5t-49 -20.5t-49 20.5t-21 48.5t21 49t49 21t49 -21t21 -49z" />
    <glyph glyph-name="bullet.case" unicode="&#xf6be;" horiz-adv-x="358" 
d="M294 341q0 -47 -33.5 -80t-80.5 -33t-80.5 33t-33.5 80t33.5 80.5t80.5 33.5t80.5 -33.5t33.5 -80.5z" />
    <glyph glyph-name="cent.case" unicode="&#xf6bf;" horiz-adv-x="497" 
d="M246 0v92q-93 14 -150.5 82.5t-57.5 168.5q0 98 57.5 167t150.5 83v74h78v-73q95 -12 149 -84l-69 -64q-31 43 -80 54v-315q49 11 80 54l69 -64q-54 -72 -149 -84v-91h-78zM147 343q0 -57 26.5 -98.5t72.5 -55.5v307q-46 -15 -72.5 -55.5t-26.5 -97.5z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="244" 
d="M184 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55z" />
    <glyph glyph-name="one.tnum" unicode="&#xf6dc;" horiz-adv-x="612" 
d="M427 0h-117v515l-109 -113l-68 71l192 194h102v-667z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="1155" 
d="M905 -12q-96 0 -155 79v-251h-105v667h105v-66q58 78 155 78q95 0 154.5 -68t59.5 -185t-59.5 -185.5t-154.5 -68.5zM874 81q62 0 99 45t37 116t-37 115.5t-99 44.5q-36 0 -71 -19t-53 -46v-191q18 -27 53 -46t71 -19zM587 0h-135l-148 249h-116v-249h-117v667h293
q98 0 158 -58t60 -151q0 -84 -46 -134.5t-112 -61.5zM348 352q50 0 82 29t32 77t-32 77t-82 29h-160v-212h160z" />
    <glyph glyph-name="centinferior" unicode="&#xf6df;" horiz-adv-x="310" 
d="M154 -151v55q-58 8 -94 49t-36 101q0 59 36 100.5t94 49.5v44h53v-44q55 -7 91 -53l-50 -40q-18 26 -41 33v-181q23 7 41 33l50 -40q-35 -45 -91 -52v-55h-53zM98 54q0 -70 56 -90v178q-56 -20 -56 -88z" />
    <glyph glyph-name="centsuperior" unicode="&#xf6e0;" horiz-adv-x="310" 
d="M154 416v55q-58 8 -94 49t-36 101q0 59 36 100.5t94 49.5v44h53v-44q55 -7 91 -53l-50 -40q-18 26 -41 33v-181q23 7 41 33l50 -40q-35 -45 -91 -52v-55h-53zM98 621q0 -70 56 -90v178q-56 -20 -56 -88z" />
    <glyph glyph-name="commainferior" unicode="&#xf6e1;" horiz-adv-x="149" 
d="M119 -113q0 -72 -58 -113q-6 5 -18 14.5t-14 11.5q14 8 29 24t16 29l-2 -1h-2q-17 0 -30.5 13t-13.5 31q0 19 13.5 32.5t32.5 13.5q20 0 33.5 -15t13.5 -40z" />
    <glyph glyph-name="commasuperior" unicode="&#xf6e2;" horiz-adv-x="149" 
d="M119 454q0 -72 -58 -113q-6 5 -18 14.5t-14 11.5q14 8 29 24t16 29h-2l-2 -1q-17 0 -30.5 13t-13.5 31q0 19 13.5 32.5t32.5 13.5q20 0 33.5 -15t13.5 -40z" />
    <glyph glyph-name="dollarinferior" unicode="&#xf6e3;" horiz-adv-x="377" 
d="M167 -206v54q-95 6 -147 64l46 52q46 -46 101 -54v114q-25 6 -41 10.5t-36.5 14t-32.5 21t-20.5 30.5t-8.5 43q0 45 38.5 78.5t100.5 37.5v56h54v-57q82 -10 122 -57l-45 -47q-33 33 -77 42v-104q30 -7 49.5 -14.5t42 -21t33.5 -35t11 -50.5q0 -52 -35 -83.5t-101 -38.5
v-55h-54zM274 -39q0 34 -53 49v-99q53 11 53 50zM111 150q0 -31 56 -46v94q-25 -4 -40.5 -17t-15.5 -31z" />
    <glyph glyph-name="dollarsuperior" unicode="&#xf6e4;" horiz-adv-x="377" 
d="M167 361v54q-95 6 -147 64l46 52q46 -46 101 -54v114q-25 6 -41 10.5t-36.5 14t-32.5 21t-20.5 30.5t-8.5 43q0 45 38.5 78.5t100.5 37.5v56h54v-57q82 -10 122 -57l-45 -47q-33 33 -77 42v-104q30 -7 49.5 -14.5t42 -21t33.5 -35t11 -50.5q0 -52 -35 -83.5t-101 -38.5
v-55h-54zM274 528q0 34 -53 49v-99q53 11 53 50zM111 717q0 -31 56 -46v94q-25 -4 -40.5 -17t-15.5 -31z" />
    <glyph glyph-name="hypheninferior" unicode="&#xf6e5;" horiz-adv-x="183" 
d="M165 22h-147v61h147v-61z" />
    <glyph glyph-name="hyphensuperior" unicode="&#xf6e6;" horiz-adv-x="183" 
d="M165 589h-147v61h147v-61z" />
    <glyph glyph-name="periodinferior" unicode="&#xf6e7;" horiz-adv-x="151" 
d="M122 -104q0 -19 -13.5 -32.5t-32.5 -13.5t-33 13.5t-14 32.5t14 32.5t33 13.5t32.5 -13.5t13.5 -32.5z" />
    <glyph glyph-name="periodsuperior" unicode="&#xf6e8;" horiz-adv-x="151" 
d="M122 463q0 -19 -13.5 -32.5t-32.5 -13.5t-33 13.5t-14 32.5t14 32.5t33 13.5t32.5 -13.5t13.5 -32.5z" />
    <glyph glyph-name="asuperior" unicode="&#xf6e9;" horiz-adv-x="382" 
d="M327 326h-77v33q-37 -41 -104 -41q-44 0 -77.5 27.5t-33.5 76.5t33.5 75.5t77.5 26.5q68 0 104 -40v46q0 27 -20.5 43t-52.5 16q-55 0 -95 -43l-31 50q56 52 138 52q61 0 99.5 -28t38.5 -91v-203zM177 367q49 0 73 33v44q-24 32 -73 32q-28 0 -46.5 -15t-18.5 -39
q0 -25 18.5 -40t46.5 -15z" />
    <glyph glyph-name="bsuperior" unicode="&#xf6ea;" horiz-adv-x="411" 
d="M211 379q39 0 63 28.5t24 75.5q0 46 -24 74.5t-63 28.5q-23 0 -45.5 -10.5t-33.5 -25.5v-135q26 -36 79 -36zM132 326h-78v434h78v-162q39 50 102 50t103 -45t40 -120q0 -77 -40 -121t-103 -44q-62 0 -102 49v-41z" />
    <glyph glyph-name="dsuperior" unicode="&#xf6eb;" horiz-adv-x="412" 
d="M357 326h-77v41q-40 -49 -102 -49q-63 0 -103.5 44t-40.5 121q0 75 40.5 120t103.5 45q62 0 101 -50v162h78v-434zM201 379q53 0 79 36v135q-11 15 -33.5 25.5t-45.5 10.5q-39 0 -63 -28.5t-24 -74.5q0 -47 24 -75.5t63 -28.5z" />
    <glyph glyph-name="esuperior" unicode="&#xf6ec;" horiz-adv-x="394" 
d="M205 318q-74 0 -122.5 45.5t-48.5 119.5q0 69 47 117t119 48q71 0 115.5 -48t44.5 -125v-18h-245q5 -35 30.5 -58t66.5 -23q55 0 90 32l34 -46q-49 -44 -131 -44zM286 512q-3 32 -24.5 56t-62.5 24q-38 0 -60 -24t-25 -56h172z" />
    <glyph glyph-name="isuperior" unicode="&#xf6ed;" horiz-adv-x="174" 
d="M87 677q-19 0 -32.5 13.5t-13.5 32.5t13.5 33t32.5 14t33 -14t14 -33t-14 -32.5t-33 -13.5zM126 326h-78v314h78v-314z" />
    <glyph glyph-name="lsuperior" unicode="&#xf6ee;" horiz-adv-x="186" 
d="M132 326h-78v434h78v-434z" />
    <glyph glyph-name="msuperior" unicode="&#xf6ef;" horiz-adv-x="585" 
d="M530 326h-77v201q0 56 -51 56q-43 0 -71 -39v-218h-77v201q0 56 -52 56q-20 0 -39.5 -12t-30.5 -27v-218h-78v314h78v-42q12 17 41.5 33.5t63.5 16.5q69 0 89 -58q15 23 45 40.5t66 17.5q93 0 93 -99v-223z" />
    <glyph glyph-name="osuperior" unicode="&#xf6f0;" horiz-adv-x="405" 
d="M202 318q-75 0 -121.5 47.5t-46.5 117.5t46.5 117.5t121.5 47.5q77 0 123 -47.5t46 -117.5t-46 -117.5t-123 -47.5zM202 381q41 0 65 28t24 74q0 45 -24 72.5t-65 27.5t-64.5 -27.5t-23.5 -72.5q0 -46 23.5 -74t64.5 -28z" />
    <glyph glyph-name="rsuperior" unicode="&#xf6f1;" horiz-adv-x="250" 
d="M132 326h-78v314h78v-44q45 52 104 52v-70l-23 1q-22 0 -46.5 -11.5t-34.5 -26.5v-215z" />
    <glyph glyph-name="ssuperior" unicode="&#xf6f2;" horiz-adv-x="333" 
d="M162 318q-84 0 -140 53l33 48q18 -20 48 -33.5t60 -13.5q29 0 45.5 11t16.5 29t-20 28.5t-48.5 16t-57 13.5t-48.5 29t-20 55q0 39 34.5 66.5t94.5 27.5q82 0 127 -45l-31 -46q-33 37 -93 37q-27 0 -42.5 -11t-15.5 -28q0 -19 30.5 -29t66.5 -14.5t66.5 -27.5t30.5 -65
q0 -44 -36 -72.5t-101 -28.5z" />
    <glyph glyph-name="tsuperior" unicode="&#xf6f3;" horiz-adv-x="222" 
d="M149 318q-88 0 -88 85v181h-53v56h53v86h78v-86h65v-56h-65v-168q0 -36 30 -36q20 0 30 10q2 -8 8.5 -25t8.5 -26q-21 -21 -67 -21z" />
    <glyph glyph-name="Lslash.smcp" unicode="&#xf6f9;" horiz-adv-x="480" 
d="M9 153v97l75 43v257h109v-194l87 51v-98l-87 -50v-164h253v-95h-362v196z" />
    <glyph glyph-name="OE.smcp" unicode="&#xf6fa;" horiz-adv-x="939" 
d="M893 0h-401v81q-62 -93 -182 -93q-121 0 -196.5 81t-75.5 206q0 124 76 205.5t196 81.5t182 -93v81h401v-95h-292v-127h286v-95h-286v-138h292v-95zM492 197v157q-21 56 -64 84t-98 28q-82 0 -131.5 -54t-49.5 -137t49.5 -137t131.5 -54q56 0 98.5 28t63.5 85z" />
    <glyph glyph-name="Scaron.smcp" unicode="&#xf6fd;" horiz-adv-x="511" 
d="M307 606h-96l-96 144h66l78 -97l74 97h67zM254 -12q-148 0 -230 84l52 84q31 -32 79.5 -53.5t101.5 -21.5q57 0 85.5 21t28.5 53q0 26 -24.5 43t-61 24.5t-79 19.5t-79 26.5t-61 47.5t-24.5 81q0 70 59.5 117.5t151.5 47.5q133 0 217 -79l-53 -78q-31 30 -76 46.5
t-89 16.5t-71.5 -19t-27.5 -49q0 -26 34 -41.5t82 -25.5t96.5 -25.5t82.5 -52.5t34 -95q0 -78 -59 -125t-169 -47z" />
    <glyph glyph-name="Zcaron.smcp" unicode="&#xf6ff;" horiz-adv-x="520" 
d="M309 606h-96l-96 144h66l78 -97l74 97h67zM483 0h-446v80l290 374h-290v96h439v-80l-289 -374h296v-96z" />
    <glyph glyph-name="exclam.smcp" unicode="&#xf721;" horiz-adv-x="245" 
d="M181 550l-16 -362h-84l-17 362h117zM192 59q0 -29 -20.5 -49.5t-48.5 -20.5q-29 0 -49.5 20.5t-20.5 49.5q0 28 21 48.5t49 20.5t48.5 -20.5t20.5 -48.5z" />
    <glyph glyph-name="dollar.onum" unicode="&#xf724;" horiz-adv-x="511" 
d="M220 -74v64q-128 10 -196 82l52 84q55 -59 144 -72v154q-39 10 -65.5 19.5t-55 27t-43 45.5t-14.5 67q0 63 49.5 109t128.5 54v68h79v-69q102 -12 171 -76l-53 -78q-46 45 -118 58v-137q39 -9 66.5 -19.5t57 -29t44.5 -48t15 -69.5q0 -70 -47 -115t-136 -55v-64h-79z
M371 155q0 42 -72 63v-133q36 7 54 26t18 44zM153 400q0 -36 67 -54v119q-31 -7 -49 -24t-18 -41z" />
    <glyph glyph-name="ampersand.smcp" unicode="&#xf726;" horiz-adv-x="562" 
d="M550 0h-125q-17 14 -52 47q-74 -58 -162 -58q-83 0 -135.5 42.5t-52.5 117.5q0 58 33 95t91 66q-39 64 -39 118q0 57 45.5 95.5t112.5 38.5q62 0 103.5 -31t41.5 -85q0 -30 -11 -54.5t-34.5 -43t-43 -29.5t-53.5 -27q21 -27 55 -63q31 -35 56 -62q36 55 55 115l78 -34
q-35 -80 -78 -138q40 -40 115 -110zM223 63q50 0 97 37q-39 39 -69 74q-38 43 -64 77q-63 -39 -63 -95q0 -40 28 -66.5t71 -26.5zM202 426q0 -35 27 -79q48 21 73 42t25 52q0 24 -16 38t-41 14q-30 0 -49 -19.5t-19 -47.5z" />
    <glyph glyph-name="zero.onum" unicode="&#xf730;" horiz-adv-x="616" 
d="M575 275q0 -120 -68.5 -203.5t-198.5 -83.5t-198.5 83.5t-68.5 203.5t68.5 203.5t198.5 83.5t198.5 -83.5t68.5 -203.5zM456 275q0 78 -38 130.5t-110 52.5t-110 -52.5t-38 -130.5t38 -130.5t110 -52.5t110 52.5t38 130.5z" />
    <glyph glyph-name="one.onum" unicode="&#xf731;" horiz-adv-x="380" 
d="M309 0h-117v398l-109 -113l-68 71l192 194h102v-550z" />
    <glyph glyph-name="two.onum" unicode="&#xf732;" horiz-adv-x="580" 
d="M507 0h-453v93q178 65 262 135t84 134q0 42 -33.5 69t-82.5 27q-110 0 -181 -80l-68 77q43 52 111 79.5t140 27.5q104 0 168 -49.5t64 -132.5q0 -80 -72.5 -156t-179.5 -121h241v-103z" />
    <glyph glyph-name="three.onum" unicode="&#xf733;" horiz-adv-x="568" 
d="M270 -127q-85 0 -150.5 30t-101.5 78l63 77q32 -37 82 -59t103 -22q65 0 102 27.5t37 74.5q0 94 -148 94q-67 0 -77 -1v105q12 -1 77 -1q62 0 99.5 22t37.5 66q0 45 -38 69.5t-97 24.5q-98 0 -172 -74l-60 73q89 105 243 105q109 0 175 -48.5t66 -131.5
q0 -62 -43.5 -102.5t-101.5 -50.5q57 -5 106 -48t49 -114q0 -86 -68.5 -140t-182.5 -54z" />
    <glyph glyph-name="four.onum" unicode="&#xf734;" horiz-adv-x="580" 
d="M552 39h-89v-156h-117v156h-318v94l274 417h161v-408h89v-103zM346 142v303l-203 -303h203z" />
    <glyph glyph-name="five.onum" unicode="&#xf735;" horiz-adv-x="600" 
d="M304 -129q-161 0 -248 103l67 80q70 -79 180 -79q62 0 99.5 33.5t37.5 83.5q0 54 -36.5 86.5t-97.5 32.5q-85 0 -146 -58l-83 24v373h437v-103h-320v-193q58 58 151 58q88 0 150.5 -58.5t62.5 -156.5q0 -103 -70.5 -164.5t-183.5 -61.5z" />
    <glyph glyph-name="six.onum" unicode="&#xf736;" horiz-adv-x="601" 
d="M316 -12q-71 0 -125 27.5t-86 75.5t-48 108.5t-16 132.5q0 151 77 248t217 97q116 0 192 -74l-55 -89q-56 59 -137 59q-80 0 -128 -64t-48 -156q0 -13 1 -19q25 37 74 65t105 28q95 0 159 -56.5t64 -158.5q0 -95 -68 -159.5t-178 -64.5zM310 92q62 0 98 35.5t36 80.5
q0 58 -39 88.5t-98 30.5q-42 0 -81 -21.5t-65 -57.5q6 -64 42 -110t107 -46z" />
    <glyph glyph-name="seven.onum" unicode="&#xf737;" horiz-adv-x="532" 
d="M249 -117h-128l252 564h-346v103h481v-81z" />
    <glyph glyph-name="eight.onum" unicode="&#xf738;" horiz-adv-x="595" 
d="M298 -12q-108 0 -182 48.5t-74 134.5q0 61 42.5 106t107.5 67q-62 20 -100.5 59t-38.5 99q0 44 21.5 79t57 55t78 30.5t88.5 10.5t88 -10.5t78 -30.5t57.5 -55t21.5 -79q0 -113 -140 -158q65 -22 107.5 -67t42.5 -106q0 -86 -73.5 -134.5t-181.5 -48.5zM298 390
q27 5 52 13.5t49.5 30t24.5 50.5q0 41 -35.5 65t-90.5 24q-56 0 -91.5 -24t-35.5 -65q0 -29 25 -50.5t50 -30t52 -13.5zM298 92q57 0 97 26t40 68q0 45 -46.5 72.5t-90.5 32.5q-26 -3 -55 -13.5t-56 -35t-27 -56.5q0 -42 39.5 -68t98.5 -26z" />
    <glyph glyph-name="nine.onum" unicode="&#xf739;" horiz-adv-x="601" 
d="M265 -127q-117 0 -191 74l54 88q56 -58 137 -58q85 0 130.5 64.5t45.5 154.5v20q-26 -37 -75 -65.5t-104 -28.5q-95 0 -159.5 57t-64.5 159q0 95 68.5 159.5t177.5 64.5q94 0 157.5 -48.5t90.5 -124t27 -172.5q0 -150 -77 -247t-217 -97zM293 223q43 0 82 21t64 57
q-5 63 -41.5 110t-107.5 47q-61 0 -97 -36t-36 -80q0 -58 39 -88.5t97 -30.5z" />
    <glyph glyph-name="question.smcp" unicode="&#xf73f;" horiz-adv-x="396" 
d="M377 435q0 -40 -24 -71t-53 -47t-53 -36t-24 -41t22 -37l-83 -26q-30 26 -30 65q0 32 21.5 57.5t47.5 39t47.5 33.5t21.5 41q0 26 -21 41.5t-60 15.5q-79 0 -129 -60l-53 66q71 85 192 85q81 0 129.5 -36t48.5 -90zM264 59q0 -29 -21 -49.5t-49 -20.5t-49 20.5t-21 49.5
t21 49.5t49 20.5t49 -20.5t21 -49.5z" />
    <glyph glyph-name="A.smcp" unicode="&#xf761;" horiz-adv-x="586" 
d="M227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194z" />
    <glyph glyph-name="B.smcp" unicode="&#xf762;" horiz-adv-x="572" 
d="M362 0h-294v550h286q78 0 122.5 -40t44.5 -101q0 -47 -27 -80.5t-67 -44.5q45 -11 75.5 -49t30.5 -86q0 -66 -45.5 -107.5t-125.5 -41.5zM343 95q36 0 57 18.5t21 49.5q0 29 -21 49.5t-57 20.5h-166v-138h166zM338 328q34 0 53 17.5t19 45.5q0 29 -19.5 46.5t-52.5 17.5
h-161v-127h161z" />
    <glyph glyph-name="C.smcp" unicode="&#xf763;" horiz-adv-x="584" 
d="M335 -12q-128 0 -212.5 80.5t-84.5 206.5t84.5 206.5t212.5 80.5q151 0 230 -129l-90 -45q-22 35 -59 56.5t-81 21.5q-80 0 -133 -54t-53 -137q0 -82 53 -136.5t133 -54.5q44 0 82 21.5t58 55.5l90 -44q-78 -129 -230 -129z" />
    <glyph glyph-name="D.smcp" unicode="&#xf764;" horiz-adv-x="621" 
d="M286 0h-218v550h217q135 0 216.5 -76.5t81.5 -199.5q0 -122 -81 -198t-216 -76zM285 95q88 0 137 51t49 128q0 79 -47.5 130t-137.5 51h-109v-360h108z" />
    <glyph glyph-name="E.smcp" unicode="&#xf765;" horiz-adv-x="515" 
d="M469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95z" />
    <glyph glyph-name="F.smcp" unicode="&#xf766;" horiz-adv-x="501" 
d="M177 0h-109v550h401v-95h-292v-127h286v-95h-286v-233z" />
    <glyph glyph-name="G.smcp" unicode="&#xf767;" horiz-adv-x="620" 
d="M337 -12q-128 0 -213.5 80t-85.5 207q0 128 85.5 207.5t213.5 79.5q141 0 225 -111l-87 -53q-52 67 -138 67q-81 0 -134 -53.5t-53 -136.5t53 -137.5t134 -54.5q73 0 124 47v79h-147v91h256v-211q-94 -101 -233 -101z" />
    <glyph glyph-name="H.smcp" unicode="&#xf768;" horiz-adv-x="644" 
d="M576 0h-109v234h-290v-234h-109v550h109v-221h290v221h109v-550z" />
    <glyph glyph-name="I.smcp" unicode="&#xf769;" horiz-adv-x="245" 
d="M177 0h-109v550h109v-550z" />
    <glyph glyph-name="J.smcp" unicode="&#xf76a;" horiz-adv-x="428" 
d="M157 -12q-94 0 -151 63l46 81q41 -48 97 -48q46 0 73.5 27t27.5 71v368h110v-377q0 -92 -55 -138.5t-148 -46.5z" />
    <glyph glyph-name="K.smcp" unicode="&#xf76b;" horiz-adv-x="552" 
d="M546 0h-133l-190 227l-46 -51v-176h-109v550h109v-251l218 251h133l-235 -260z" />
    <glyph glyph-name="L.smcp" unicode="&#xf76c;" horiz-adv-x="464" 
d="M430 0h-362v550h109v-455h253v-95z" />
    <glyph glyph-name="M.smcp" unicode="&#xf76d;" horiz-adv-x="740" 
d="M672 0h-109v383l-168 -383h-49l-169 383v-383h-109v550h146l156 -355l155 355h147v-550z" />
    <glyph glyph-name="N.smcp" unicode="&#xf76e;" horiz-adv-x="641" 
d="M573 0h-111l-285 380v-380h-109v550h116l280 -370v370h109v-550z" />
    <glyph glyph-name="O.smcp" unicode="&#xf76f;" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54z" />
    <glyph glyph-name="P.smcp" unicode="&#xf770;" horiz-adv-x="540" 
d="M177 0h-109v550h260q88 0 138 -50.5t50 -124.5q0 -75 -49 -125.5t-139 -50.5h-151v-199zM316 294q42 0 65 22.5t23 58.5t-23 58t-65 22h-139v-161h139z" />
    <glyph glyph-name="Q.smcp" unicode="&#xf771;" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5q0 -122 -77 -202l45 -51l-66 -54l-48 54q-63 -34 -144 -34zM329 84q41 0 76 15l-64 73l66 55l64 -73q37 51 37 121q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137
t130.5 -54z" />
    <glyph glyph-name="R.smcp" unicode="&#xf772;" horiz-adv-x="561" 
d="M522 0h-126l-124 199h-95v-199h-109v550h260q87 0 137.5 -50t50.5 -125q0 -70 -39 -114t-97 -52zM316 294q42 0 65.5 22.5t23.5 58.5t-24 58t-65 22h-139v-161h139z" />
    <glyph glyph-name="S.smcp" unicode="&#xf773;" horiz-adv-x="511" 
d="M254 -12q-148 0 -230 84l52 84q31 -32 79.5 -53.5t101.5 -21.5q57 0 85.5 21t28.5 53q0 26 -24.5 43t-61 24.5t-79 19.5t-79 26.5t-61 47.5t-24.5 81q0 70 59.5 117.5t151.5 47.5q133 0 217 -79l-53 -78q-31 30 -76 46.5t-89 16.5t-71.5 -19t-27.5 -49q0 -26 34 -41.5
t82 -25.5t96.5 -25.5t82.5 -52.5t34 -95q0 -78 -59 -125t-169 -47z" />
    <glyph glyph-name="T.smcp" unicode="&#xf774;" horiz-adv-x="480" 
d="M294 0h-109v455h-162v95h434v-95h-163v-455z" />
    <glyph glyph-name="U.smcp" unicode="&#xf775;" horiz-adv-x="636" 
d="M318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -106 -64 -167t-186 -61z" />
    <glyph glyph-name="V.smcp" unicode="&#xf776;" horiz-adv-x="586" 
d="M359 0h-132l-227 550h124l169 -431l171 431h122z" />
    <glyph glyph-name="W.smcp" unicode="&#xf777;" horiz-adv-x="783" 
d="M613 0h-114l-108 389l-108 -389h-114l-165 550h121l107 -405l116 405h87l115 -406l108 406h121z" />
    <glyph glyph-name="X.smcp" unicode="&#xf778;" horiz-adv-x="577" 
d="M575 0h-129l-158 209l-158 -209h-129l216 282l-203 268h129l145 -195l145 195h129l-203 -267z" />
    <glyph glyph-name="Y.smcp" unicode="&#xf779;" horiz-adv-x="557" 
d="M334 0h-108v229l-226 321h124l156 -229l153 229h124l-223 -321v-229z" />
    <glyph glyph-name="Z.smcp" unicode="&#xf77a;" horiz-adv-x="520" 
d="M483 0h-446v80l290 374h-290v96h439v-80l-289 -374h296v-96z" />
    <glyph glyph-name="Agrave.smcp" unicode="&#xf7e0;" horiz-adv-x="586" 
d="M227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM354 606h-75l-165 144h100z" />
    <glyph glyph-name="Aacute.smcp" unicode="&#xf7e1;" horiz-adv-x="586" 
d="M472 750l-165 -144h-75l140 144h100zM227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194z" />
    <glyph glyph-name="Acircumflex.smcp" unicode="&#xf7e2;" horiz-adv-x="586" 
d="M437 606h-67l-77 96l-75 -96h-66l93 144h96zM227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194z" />
    <glyph glyph-name="Atilde.smcp" unicode="&#xf7e3;" horiz-adv-x="586" 
d="M227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM351 604q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58
q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="Adieresis.smcp" unicode="&#xf7e4;" horiz-adv-x="586" 
d="M457 668q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM247 668q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240
l-97 -240h194z" />
    <glyph glyph-name="Aring.smcp" unicode="&#xf7e5;" horiz-adv-x="586" 
d="M294 572q-42 0 -72 30t-30 72t30 72.5t72 30.5t71.5 -30.5t29.5 -72.5t-29.5 -72t-71.5 -30zM294 622q21 0 36 15.5t15 36.5q0 22 -14.5 37t-36.5 15q-21 0 -36.5 -15t-15.5 -37q0 -21 15.5 -36.5t36.5 -15.5zM227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123z
M390 198l-97 240l-97 -240h194z" />
    <glyph glyph-name="AE.smcp" unicode="&#xf7e6;" horiz-adv-x="835" 
d="M789 0h-402v103h-201l-64 -103h-123l346 550h444v-95h-293v-127h286v-95h-286v-138h293v-95zM387 198v240l-149 -240h149z" />
    <glyph glyph-name="Ccedilla.smcp" unicode="&#xf7e7;" horiz-adv-x="584" 
d="M334 -194q-67 0 -106 31l22 46q36 -29 81 -29q23 0 39.5 9.5t16.5 26.5q0 30 -33 30q-21 0 -34 -15l-41 23l23 62q-116 11 -190 89.5t-74 195.5q0 126 84.5 206.5t212.5 80.5q151 0 230 -129l-90 -45q-22 35 -59 56.5t-81 21.5q-80 0 -133 -54t-53 -137q0 -82 53 -136.5
t133 -54.5q44 0 82 21.5t58 55.5l90 -44q-71 -118 -209 -128l-17 -42q16 11 36 11q29 0 48 -19.5t19 -49.5q0 -37 -31 -60t-77 -23z" />
    <glyph glyph-name="Egrave.smcp" unicode="&#xf7e8;" horiz-adv-x="515" 
d="M469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95zM329 606h-75l-165 144h100z" />
    <glyph glyph-name="Eacute.smcp" unicode="&#xf7e9;" horiz-adv-x="515" 
d="M447 750l-165 -144h-75l140 144h100zM469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95z" />
    <glyph glyph-name="Ecircumflex.smcp" unicode="&#xf7ea;" horiz-adv-x="515" 
d="M411 606h-67l-77 96l-75 -96h-66l93 144h96zM469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95z" />
    <glyph glyph-name="Edieresis.smcp" unicode="&#xf7eb;" horiz-adv-x="515" 
d="M432 658q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM222 658q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95z" />
    <glyph glyph-name="Igrave.smcp" unicode="&#xf7ec;" horiz-adv-x="245" 
d="M177 0h-109v550h109v-550zM183 606h-75l-165 144h100z" />
    <glyph glyph-name="Iacute.smcp" unicode="&#xf7ed;" horiz-adv-x="245" 
d="M301 750l-165 -144h-75l140 144h100zM177 0h-109v550h109v-550z" />
    <glyph glyph-name="Icircumflex.smcp" unicode="&#xf7ee;" horiz-adv-x="245" 
d="M266 606h-67l-77 96l-75 -96h-66l93 144h96zM177 0h-109v550h109v-550z" />
    <glyph glyph-name="Idieresis.smcp" unicode="&#xf7ef;" horiz-adv-x="245" 
d="M287 658q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM77 658q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM177 0h-109v550h109v-550z" />
    <glyph glyph-name="Eth.smcp" unicode="&#xf7f0;" horiz-adv-x="631" 
d="M285 0h-218v240h-58v71h58v239h217q135 0 216.5 -76.5t81.5 -199.5q0 -122 -81 -198t-216 -76zM298 240h-122v-145h108q88 0 137 51t49 128q0 79 -47.5 130t-137.5 51h-109v-144h122v-71z" />
    <glyph glyph-name="Ntilde.smcp" unicode="&#xf7f1;" horiz-adv-x="641" 
d="M573 0h-111l-285 380v-380h-109v550h116l280 -370v370h109v-550zM378 604q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q29 0 50 -19.5t36.5 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="Ograve.smcp" unicode="&#xf7f2;" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM390 606h-75l-165 144h100z" />
    <glyph glyph-name="Oacute.smcp" unicode="&#xf7f3;" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM508 750l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="Ocircumflex.smcp" unicode="&#xf7f4;" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM472 606h-67l-77 96l-75 -96h-66l93 144h96
z" />
    <glyph glyph-name="Otilde.smcp" unicode="&#xf7f5;" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM387 604q-19 0 -34.5 8t-25.5 19.5
t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="Odieresis.smcp" unicode="&#xf7f6;" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM493 658q0 -25 -17.5 -42t-41.5 -17
t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM283 658q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41z" />
    <glyph glyph-name="Oslash.smcp" unicode="&#xf7f8;" horiz-adv-x="658" 
d="M329 -12q-84 0 -151 36l-18 -24h-87l50 65q-85 82 -85 210q0 124 81 205.5t210 81.5q81 0 147 -36l18 24h87l-49 -64q87 -82 87 -211q0 -124 -81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 74 -41 126l-227 -296q40 -21 89 -21zM149 275q0 -74 39 -125
l226 296q-37 20 -85 20q-81 0 -130.5 -54t-49.5 -137z" />
    <glyph glyph-name="Ugrave.smcp" unicode="&#xf7f9;" horiz-adv-x="636" 
d="M318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -106 -64 -167t-186 -61zM379 606h-75l-165 144h100z" />
    <glyph glyph-name="Uacute.smcp" unicode="&#xf7fa;" horiz-adv-x="636" 
d="M497 750l-165 -144h-75l140 144h100zM318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -106 -64 -167t-186 -61z" />
    <glyph glyph-name="Ucircumflex.smcp" unicode="&#xf7fb;" horiz-adv-x="636" 
d="M461 606h-67l-77 96l-75 -96h-66l93 144h96zM318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -106 -64 -167t-186 -61z" />
    <glyph glyph-name="Udieresis.smcp" unicode="&#xf7fc;" horiz-adv-x="636" 
d="M482 658q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM272 658q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5
t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -106 -64 -167t-186 -61z" />
    <glyph glyph-name="Yacute.smcp" unicode="&#xf7fd;" horiz-adv-x="557" 
d="M459 750l-165 -144h-75l140 144h100zM334 0h-108v229l-226 321h124l156 -229l153 229h124l-223 -321v-229z" />
    <glyph glyph-name="Thorn.smcp" unicode="&#xf7fe;" horiz-adv-x="540" 
d="M177 0h-109v550h109v-86h151q88 0 138 -51t50 -125q0 -75 -49 -125t-139 -50h-151v-113zM316 208q42 0 65 22t23 58t-23 58.5t-65 22.5h-139v-161h139z" />
    <glyph glyph-name="Ydieresis.smcp" unicode="&#xf7ff;" horiz-adv-x="557" 
d="M444 658q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM234 658q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM334 0h-108v229l-226 321h124l156 -229l153 229h124l-223 -321v-229z" />
    <glyph glyph-name="uniF8FF" unicode="&#xf8ff;" horiz-adv-x="667" 
d="M222 154v69h-70q-28 0 -48 -20t-20 -49t20 -50t49 -21q28 0 48.5 21t20.5 50zM222 443v71q0 29 -20 49t-48 20q-29 0 -49.5 -20t-20.5 -49t20.5 -50t49.5 -21h68zM265 265h137v137h-137v-137zM582 154q0 29 -19.5 49t-48.5 20h-69v-69q0 -29 20 -50t49 -21q28 0 48 21
t20 50zM582 514q0 29 -20 49t-48 20q-29 0 -49 -20t-20 -49v-71h69q28 0 48 21t20 50zM625 154q0 -46 -32.5 -79t-78.5 -33t-79 33t-33 79v69h-137v-69q0 -46 -33 -79t-79 -33t-78.5 33t-32.5 79t32.5 78.5t78.5 32.5h69v137h-68q-46 0 -79 33t-33 79t33 78.5t79 32.5
t78.5 -32.5t32.5 -78.5v-71h137v71q0 46 33 78.5t79 32.5t78.5 -32.5t32.5 -78.5t-32.5 -79t-78.5 -33h-69v-137h69q46 0 78.5 -32.5t32.5 -78.5z" />
    <glyph glyph-name="zero.pzero" horiz-adv-x="616" 
d="M308 -12q-87 0 -149 49l-23 -37h-85l58 90q-68 95 -68 243q0 65 16 125t47 109.5t84 79.5t120 30q85 0 147 -48l24 38h85l-59 -91q70 -96 70 -243q0 -65 -16 -125t-47 -110t-84 -80t-120 -30zM308 92q76 0 112 68t36 173q0 79 -20 136l-218 -340q35 -37 90 -37zM160 333
q0 -80 18 -134l218 338q-36 36 -88 36t-86.5 -34.5t-48 -86.5t-13.5 -119z" />
    <glyph glyph-name="zero.tonumzero" horiz-adv-x="612" 
d="M573 275q0 -120 -68.5 -203.5t-198.5 -83.5q-87 0 -149 40l-22 -28h-87l57 74q-66 79 -66 201q0 120 68.5 203.5t198.5 83.5q81 0 144 -37l19 25h87l-53 -69q70 -80 70 -206zM454 275q0 63 -25 110l-205 -269q34 -24 82 -24q72 0 110 52.5t38 130.5zM158 275
q0 -59 21 -104l203 266q-32 21 -76 21q-72 0 -110 -52.5t-38 -130.5z" />
    <glyph glyph-name="zero.onumzero" horiz-adv-x="616" 
d="M575 275q0 -120 -68.5 -203.5t-198.5 -83.5q-87 0 -149 40l-22 -28h-87l57 74q-66 79 -66 201q0 120 68.5 203.5t198.5 83.5q81 0 144 -37l19 25h87l-53 -69q70 -80 70 -206zM456 275q0 63 -25 110l-205 -269q34 -24 82 -24q72 0 110 52.5t38 130.5zM160 275
q0 -59 21 -104l203 266q-32 21 -76 21q-72 0 -110 -52.5t-38 -130.5z" />
    <glyph glyph-name="G.alt1" horiz-adv-x="745" 
d="M381 -13q-141 0 -240.5 96.5t-99.5 249.5q0 154 101 249.5t249 95.5q172 0 269 -139l-96 -54q-28 39 -73.5 64t-99.5 25q-99 0 -164.5 -68t-65.5 -173t65.5 -173.5t164.5 -68.5q80 0 130 46.5t65 117.5h-236v103h364q0 -170 -88.5 -270.5t-244.5 -100.5z" />
    <glyph glyph-name="a.alt1" horiz-adv-x="581" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5z" />
    <glyph glyph-name="f.alt1" horiz-adv-x="308" 
d="M198 0h-105v391h-80v92h80v27q0 79 42 123t111 44q51 0 84 -19l-23 -78q-16 11 -42 11q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="285" 
d="M192 -12q-124 0 -124 124v555h105v-529q0 -26 12 -41.5t34 -15.5q31 0 46 17l25 -79q-33 -31 -98 -31z" />
    <glyph glyph-name="y.alt1" 
d="M257 -196q-117 0 -195 72l49 76q51 -62 146 -62q65 0 101 35t36 99v56q-68 -76 -171 -76q-155 0 -155 153v326h105v-288q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v323h105v-459q0 -105 -67 -162.5t-175 -57.5z" />
    <glyph glyph-name="G.smcp.alt1" horiz-adv-x="636" 
d="M332 -12q-125 0 -209.5 80t-84.5 207q0 128 85.5 207.5t213.5 79.5q141 0 225 -111l-87 -53q-51 68 -138 68q-81 0 -134 -54t-53 -137t53 -137.5t134 -54.5q58 0 101 33.5t54 92.5h-178v91h294q0 -147 -75.5 -229.5t-200.5 -82.5z" />
    <glyph glyph-name="Gbreve.alt1" horiz-adv-x="737" 
d="M568 789q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66zM381 -13q-141 0 -240.5 96.5t-99.5 249.5q0 154 101 249.5t249 95.5q172 0 269 -139l-96 -54q-28 39 -73.5 64t-99.5 25q-99 0 -164.5 -68t-65.5 -173t65.5 -173.5t164.5 -68.5q80 0 130 46.5
t65 117.5h-236v103h364q0 -170 -88.5 -270.5t-244.5 -100.5z" />
    <glyph glyph-name="Gcommaaccent.alt1" horiz-adv-x="737" 
d="M455 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM381 -13q-141 0 -240.5 96.5t-99.5 249.5q0 154 101 249.5t249 95.5q172 0 269 -139l-96 -54q-28 39 -73.5 64
t-99.5 25q-99 0 -164.5 -68t-65.5 -173t65.5 -173.5t164.5 -68.5q80 0 130 46.5t65 117.5h-236v103h364q0 -170 -88.5 -270.5t-244.5 -100.5z" />
    <glyph glyph-name="Gcircumflex.alt1" horiz-adv-x="737" 
d="M532 723h-67l-77 96l-75 -96h-66l93 144h96zM381 -13q-141 0 -240.5 96.5t-99.5 249.5q0 154 101 249.5t249 95.5q172 0 269 -139l-96 -54q-28 39 -73.5 64t-99.5 25q-99 0 -164.5 -68t-65.5 -173t65.5 -173.5t164.5 -68.5q80 0 130 46.5t65 117.5h-236v103h364
q0 -170 -88.5 -270.5t-244.5 -100.5z" />
    <glyph glyph-name="Gdotaccent.alt1" horiz-adv-x="737" 
d="M390 715q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17zM381 -13q-141 0 -240.5 96.5t-99.5 249.5q0 154 101 249.5t249 95.5q172 0 269 -139l-96 -54q-28 39 -73.5 64t-99.5 25q-99 0 -164.5 -68t-65.5 -173t65.5 -173.5
t164.5 -68.5q80 0 130 46.5t65 117.5h-236v103h364q0 -170 -88.5 -270.5t-244.5 -100.5z" />
    <glyph glyph-name="aacute.alt1" horiz-adv-x="581" 
d="M463 700l-165 -144h-75l140 144h100zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5z" />
    <glyph glyph-name="abreve.alt1" horiz-adv-x="581" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM474 622q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66
t127 66z" />
    <glyph glyph-name="acircumflex.alt1" horiz-adv-x="581" 
d="M441 556h-67l-77 96l-75 -96h-66l93 144h96zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5z" />
    <glyph glyph-name="adieresis.alt1" horiz-adv-x="581" 
d="M462 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM252 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69
t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5z" />
    <glyph glyph-name="agrave.alt1" horiz-adv-x="581" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM345 556h-75l-165 144h100z" />
    <glyph glyph-name="amacron.alt1" horiz-adv-x="581" 
d="M480 566h-363v62h363v-62zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5z" />
    <glyph glyph-name="aogonek.alt1" horiz-adv-x="581" 
d="M513 0q-73 -36 -73 -93q0 -23 11 -34.5t29 -11.5q34 0 47 38l45 -27q-29 -58 -92 -58q-41 0 -68 22t-27 67q0 55 48 97h-25v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5
q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5z" />
    <glyph glyph-name="aring.alt1" horiz-adv-x="581" 
d="M299 536q-42 0 -72 30t-30 72t30 72.5t72 30.5t71.5 -30.5t29.5 -72.5t-29.5 -72t-71.5 -30zM299 586q21 0 36 15.5t15 36.5q0 22 -14.5 37t-36.5 15q-21 0 -36.5 -15t-15.5 -37q0 -21 15.5 -36.5t36.5 -15.5zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185
q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5z" />
    <glyph glyph-name="atilde.alt1" horiz-adv-x="581" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM356 554q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8
q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q29 0 50 -19.5t36.5 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EA1.alt1" horiz-adv-x="581" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM299 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5
t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EA3.alt1" horiz-adv-x="581" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM262 582l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53
q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EA5.alt1" horiz-adv-x="581" 
d="M441 556h-67l-77 96l-75 -96h-66l93 144h96zM641 770l-165 -144h-75l140 144h100zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5
t37 -115.5t99 -44.5z" />
    <glyph glyph-name="uni1EAD.alt1" horiz-adv-x="581" 
d="M442 556h-67l-77 96l-75 -96h-66l93 144h96zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM299 -191
q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EA7.alt1" horiz-adv-x="581" 
d="M441 556h-67l-77 96l-75 -96h-66l93 144h96zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM192 626h-75l-165 144
h100z" />
    <glyph glyph-name="uni1EA9.alt1" horiz-adv-x="581" 
d="M441 556h-67l-77 96l-75 -96h-66l93 144h96zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM359 722l-40 12
q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EAF.alt1" horiz-adv-x="581" 
d="M477 773l-165 -144h-75l140 144h100zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM474 606q-65 -88 -175 -88
q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="uni1EAB.alt1" horiz-adv-x="581" 
d="M441 523h-67l-77 96l-75 -96h-66l93 144h96zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM353 682
q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EB1.alt1" horiz-adv-x="581" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM352 629h-75l-165 144h100zM474 609q-65 -88 -175 -88
q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="uni1EB3.alt1" horiz-adv-x="581" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM262 687l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53
q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30zM474 609q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="uni1EB5.alt1" horiz-adv-x="581" 
d="M474 609q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66zM513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5
t99 -44.5zM356 660q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q29 0 50 -19.5t36.5 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EB7.alt1" horiz-adv-x="581" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM299 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5
t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17zM474 622q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="922" 
d="M283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM513 82v-82h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-83q56 95 164 95q95 0 152 -73.5t57 -189.5v-25h-373
q6 -57 46.5 -94t106.5 -37q83 0 138 53l47 -71q-67 -70 -166 -70q-117 0 -172 94zM785 281q-3 50 -37.5 89t-99.5 39q-62 0 -97 -39t-38 -89h272z" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="922" 
d="M283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5zM513 82v-82h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-83q56 95 164 95q95 0 152 -73.5t57 -189.5v-25h-373
q6 -57 46.5 -94t106.5 -37q83 0 138 53l47 -71q-67 -70 -166 -70q-117 0 -172 94zM785 281q-3 50 -37.5 89t-99.5 39q-62 0 -97 -39t-38 -89h272zM641 700l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="fl.alt1" horiz-adv-x="594" 
d="M198 0h-105v391h-80v92h80v27q0 79 42 123t111 44q51 0 84 -19l-23 -78q-16 11 -42 11q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391zM500 -12q-124 0 -124 124v555h105v-529q0 -26 12 -41.5t34 -15.5q31 0 46 17l25 -79q-33 -31 -98 -31z" />
    <glyph glyph-name="f_f_l.alt1" horiz-adv-x="902" 
d="M506 0h-105v391h-80v92h80v27q0 79 42 123t111 44q51 0 84 -19l-23 -78q-16 11 -42 11q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391zM809 -12q-124 0 -124 124v555h105v-529q0 -26 12 -41.5t34 -15.5q31 0 46 17l25 -79q-33 -31 -98 -31zM198 0h-105v391h-80v92h80
v27q0 78 41.5 122.5t111.5 44.5q75 0 116 -42l-41 -65q-23 21 -56 21q-31 0 -49 -20.5t-18 -60.5v-27h98v-92h-98v-391z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="285" 
d="M300 867l-165 -144h-75l140 144h100zM192 -12q-124 0 -124 124v555h105v-529q0 -26 12 -41.5t34 -15.5q31 0 46 17l25 -79q-33 -31 -98 -31z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="285" 
d="M196 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM192 -12q-124 0 -124 124v555h105v-529q0 -26 12 -41.5t34 -15.5q31 0 46 17l25 -79q-33 -31 -98 -31z" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="327" 
d="M346 605q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM192 -12q-124 0 -124 124v555h105v-529q0 -26 12 -41.5t34 -15.5q31 0 46 17l25 -79q-33 -31 -98 -31z" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="351" 
d="M192 -12q-124 0 -124 124v555h105v-529q0 -26 12 -41.5t34 -15.5q31 0 46 17l25 -79q-33 -31 -98 -31zM365 244q0 -28 -21 -48.5t-49 -20.5t-49 20.5t-21 48.5t20.5 49t49.5 21t49.5 -21t20.5 -49z" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="314" 
d="M9 214v79l87 50v324h105v-264l88 51v-79l-88 -51v-186q0 -26 12 -41.5t34 -15.5q31 0 46 17l25 -79q-33 -31 -98 -31q-124 0 -124 124v152z" />
    <glyph glyph-name="yacute.alt1" 
d="M462 700l-165 -144h-75l140 144h100zM257 -196q-117 0 -195 72l49 76q51 -62 146 -62q65 0 101 35t36 99v56q-68 -76 -171 -76q-155 0 -155 153v326h105v-288q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v323h105v-459q0 -105 -67 -162.5t-175 -57.5z" />
    <glyph glyph-name="ycircumflex.alt1" 
d="M427 556h-67l-77 96l-75 -96h-66l93 144h96zM257 -196q-117 0 -195 72l49 76q51 -62 146 -62q65 0 101 35t36 99v56q-68 -76 -171 -76q-155 0 -155 153v326h105v-288q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v323h105v-459q0 -105 -67 -162.5t-175 -57.5z" />
    <glyph glyph-name="ydieresis.alt1" 
d="M448 608q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM238 608q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17q24 0 41 -17t17 -41zM257 -196q-117 0 -195 72l49 76q51 -62 146 -62q65 0 101 35t36 99v56
q-68 -76 -171 -76q-155 0 -155 153v326h105v-288q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v323h105v-459q0 -105 -67 -162.5t-175 -57.5z" />
    <glyph glyph-name="ygrave.alt1" 
d="M257 -196q-117 0 -195 72l49 76q51 -62 146 -62q65 0 101 35t36 99v56q-68 -76 -171 -76q-155 0 -155 153v326h105v-288q0 -53 25 -75.5t72 -22.5q38 0 71 18t53 45v323h105v-459q0 -105 -67 -162.5t-175 -57.5zM344 556h-75l-165 144h100z" />
    <glyph glyph-name="uni1EA0.smcp" horiz-adv-x="586" 
d="M227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM294 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EA2.smcp" horiz-adv-x="586" 
d="M227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM257 635l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EA4.smcp" horiz-adv-x="586" 
d="M437 606h-67l-77 96l-75 -96h-66l93 144h96zM636 820l-165 -144h-75l140 144h100zM227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194z" />
    <glyph glyph-name="uni1EAC.smcp" horiz-adv-x="586" 
d="M436 606h-67l-77 96l-75 -96h-66l93 144h96zM227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM293 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EA6.smcp" horiz-adv-x="586" 
d="M437 606h-67l-77 96l-75 -96h-66l93 144h96zM227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM188 676h-75l-165 144h100z" />
    <glyph glyph-name="uni1EA8.smcp" horiz-adv-x="586" 
d="M436 606h-67l-77 96l-75 -96h-66l93 144h96zM227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM354 772l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EAE.smcp" horiz-adv-x="586" 
d="M476 828l-165 -144h-75l140 144h100zM227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM469 664q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="uni1EAA.smcp" horiz-adv-x="586" 
d="M436 573h-67l-77 96l-75 -96h-66l93 144h96zM227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM351 732q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5
t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EB0.smcp" horiz-adv-x="586" 
d="M227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM353 684h-75l-165 144h100zM470 664q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="uni1EB2.smcp" horiz-adv-x="586" 
d="M227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM258 742l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30zM470 664q-65 -88 -175 -88q-109 0 -176 88l48 40
q48 -66 128 -66t127 66z" />
    <glyph glyph-name="uni1EB4.smcp" horiz-adv-x="586" 
d="M470 664q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66zM227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM352 713q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98
t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EB6.smcp" horiz-adv-x="586" 
d="M227 550h132l227 -550h-122l-42 103h-258l-41 -103h-123zM390 198l-97 240l-97 -240h194zM293 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17zM469 672q-65 -88 -175 -88q-109 0 -176 88l48 40q48 -66 128 -66t127 66
z" />
    <glyph glyph-name="uni1E02.smcp" horiz-adv-x="572" 
d="M362 0h-294v550h286q78 0 122.5 -40t44.5 -101q0 -47 -27 -80.5t-67 -44.5q45 -11 75.5 -49t30.5 -86q0 -66 -45.5 -107.5t-125.5 -41.5zM343 95q36 0 57 18.5t21 49.5q0 29 -21 49.5t-57 20.5h-166v-138h166zM338 328q34 0 53 17.5t19 45.5q0 29 -19.5 46.5t-52.5 17.5
h-161v-127h161zM294 598q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E0A.smcp" horiz-adv-x="621" 
d="M286 0h-218v550h217q135 0 216.5 -76.5t81.5 -199.5q0 -122 -81 -198t-216 -76zM285 95q88 0 137 51t49 128q0 79 -47.5 130t-137.5 51h-109v-360h108zM303 598q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EB8.smcp" horiz-adv-x="515" 
d="M469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95zM269 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EBA.smcp" horiz-adv-x="515" 
d="M469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95zM232 635l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EBC.smcp" horiz-adv-x="515" 
d="M469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95zM326 604q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EBE.smcp" horiz-adv-x="515" 
d="M411 606h-67l-77 96l-75 -96h-66l93 144h96zM611 820l-165 -144h-75l140 144h100zM469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95z" />
    <glyph glyph-name="uni1EC0.smcp" horiz-adv-x="515" 
d="M411 606h-67l-77 96l-75 -96h-66l93 144h96zM469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95zM162 676h-75l-165 144h100z" />
    <glyph glyph-name="uni1EC2.smcp" horiz-adv-x="515" 
d="M411 606h-67l-77 96l-75 -96h-66l93 144h96zM469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95zM332 772l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EC4.smcp" horiz-adv-x="515" 
d="M411 573h-67l-77 96l-75 -96h-66l93 144h96zM469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95zM326 732q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5
q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EC6.smcp" horiz-adv-x="515" 
d="M411 606h-67l-77 96l-75 -96h-66l93 144h96zM469 0h-401v550h401v-95h-292v-127h286v-95h-286v-138h292v-95zM269 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E1E.smcp" horiz-adv-x="501" 
d="M269 598q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17zM177 0h-109v550h401v-95h-292v-127h286v-95h-286v-233z" />
    <glyph glyph-name="Gbreve.smcp.alt1" horiz-adv-x="636" 
d="M332 -12q-125 0 -209.5 80t-84.5 207q0 128 85.5 207.5t213.5 79.5q141 0 225 -111l-87 -53q-51 68 -138 68q-81 0 -134 -54t-53 -137t53 -137.5t134 -54.5q58 0 101 33.5t54 92.5h-178v91h294q0 -147 -75.5 -229.5t-200.5 -82.5zM513 672q-65 -88 -175 -88
q-109 0 -176 88l48 40q48 -66 128 -66t127 66z" />
    <glyph glyph-name="Gcommaaccent.smcp.alt1" horiz-adv-x="636" 
d="M396 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM332 -12q-125 0 -209.5 80t-84.5 207q0 128 85.5 207.5t213.5 79.5q141 0 225 -111l-87 -53q-51 68 -138 68
q-81 0 -134 -54t-53 -137t53 -137.5t134 -54.5q58 0 101 33.5t54 92.5h-178v91h294q0 -147 -75.5 -229.5t-200.5 -82.5z" />
    <glyph glyph-name="Gcircumflex.smcp.alt1" horiz-adv-x="636" 
d="M480 606h-67l-77 96l-75 -96h-66l93 144h96zM332 -12q-125 0 -209.5 80t-84.5 207q0 128 85.5 207.5t213.5 79.5q141 0 225 -111l-87 -53q-51 68 -138 68q-81 0 -134 -54t-53 -137t53 -137.5t134 -54.5q58 0 101 33.5t54 92.5h-178v91h294q0 -147 -75.5 -229.5
t-200.5 -82.5z" />
    <glyph glyph-name="Gdotaccent.smcp.alt1" horiz-adv-x="636" 
d="M332 -12q-125 0 -209.5 80t-84.5 207q0 128 85.5 207.5t213.5 79.5q141 0 225 -111l-87 -53q-51 68 -138 68q-81 0 -134 -54t-53 -137t53 -137.5t134 -54.5q58 0 101 33.5t54 92.5h-178v91h294q0 -147 -75.5 -229.5t-200.5 -82.5zM338 598q-24 0 -41.5 17t-17.5 42
q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E22.smcp" horiz-adv-x="644" 
d="M576 0h-109v234h-290v-234h-109v550h109v-221h290v221h109v-550zM323 598q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EC8.smcp" horiz-adv-x="245" 
d="M177 0h-109v550h109v-550zM87 635l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1ECA.smcp" horiz-adv-x="245" 
d="M177 0h-109v550h109v-550zM124 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1ECC.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM330 -191q-24 0 -41.5 17t-17.5 42
q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1ECE.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM293 640l-40 12q18 56 77 56q33 0 54.5 -18
t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1ED0.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM472 606h-67l-77 96l-75 -96h-66l93 144h96
zM672 820l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="uni1ED2.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM472 606h-67l-77 96l-75 -96h-66l93 144h96
zM223 676h-75l-165 144h100z" />
    <glyph glyph-name="uni1ED4.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM472 606h-67l-77 96l-75 -96h-66l93 144h96
zM390 772l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1ED6.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM473 573h-67l-77 96l-75 -96h-66l93 144h96
zM388 732q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q29 0 50 -19.5t36.5 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1ED8.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q128 0 209 -81.5t81 -205.5t-81 -205.5t-209 -81.5zM329 84q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM472 606h-67l-77 96l-75 -96h-66l93 144h96
zM330 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="Ohorn.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q146 0 229 -104q31 28 38 63q-4 -2 -13 -2q-19 0 -31.5 13.5t-12.5 33.5q0 21 14.5 36t35.5 15q24 0 40.5 -18.5t16.5 -50.5q0 -75 -64 -127q37 -65 37 -146q0 -124 -81 -205.5t-209 -81.5zM329 84
q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54z" />
    <glyph glyph-name="uni1EDA.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q146 0 229 -104q31 28 38 63q-4 -2 -13 -2q-19 0 -31.5 13.5t-12.5 33.5q0 21 14.5 36t35.5 15q24 0 40.5 -18.5t16.5 -50.5q0 -75 -64 -127q37 -65 37 -146q0 -124 -81 -205.5t-209 -81.5zM329 84
q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM508 750l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="uni1EDC.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q146 0 229 -104q31 28 38 63q-4 -2 -13 -2q-19 0 -31.5 13.5t-12.5 33.5q0 21 14.5 36t35.5 15q24 0 40.5 -18.5t16.5 -50.5q0 -75 -64 -127q37 -65 37 -146q0 -124 -81 -205.5t-209 -81.5zM329 84
q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM390 606h-75l-165 144h100z" />
    <glyph glyph-name="uni1EDE.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q146 0 229 -104q31 28 38 63q-4 -2 -13 -2q-19 0 -31.5 13.5t-12.5 33.5q0 21 14.5 36t35.5 15q24 0 40.5 -18.5t16.5 -50.5q0 -75 -64 -127q37 -65 37 -146q0 -124 -81 -205.5t-209 -81.5zM329 84
q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM293 640l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EE0.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q146 0 229 -104q31 28 38 63q-4 -2 -13 -2q-19 0 -31.5 13.5t-12.5 33.5q0 21 14.5 36t35.5 15q24 0 40.5 -18.5t16.5 -50.5q0 -75 -64 -127q37 -65 37 -146q0 -124 -81 -205.5t-209 -81.5zM329 84
q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM387 604q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q30 0 50.5 -19.5t36 -39t32.5 -19.5q41 0 41 72
h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EE2.smcp" horiz-adv-x="658" 
d="M329 -12q-129 0 -210 81.5t-81 205.5t81 205.5t210 81.5q146 0 229 -104q31 28 38 63q-4 -2 -13 -2q-19 0 -31.5 13.5t-12.5 33.5q0 21 14.5 36t35.5 15q24 0 40.5 -18.5t16.5 -50.5q0 -75 -64 -127q37 -65 37 -146q0 -124 -81 -205.5t-209 -81.5zM329 84
q80 0 129.5 54.5t49.5 136.5q0 83 -49.5 137t-129.5 54q-81 0 -130.5 -54t-49.5 -137t49.5 -137t130.5 -54zM330 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E56.smcp" horiz-adv-x="540" 
d="M177 0h-109v550h260q88 0 138 -50.5t50 -124.5q0 -75 -49 -125.5t-139 -50.5h-151v-199zM316 294q42 0 65 22.5t23 58.5t-23 58t-65 22h-139v-161h139zM289 598q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1E60.smcp" horiz-adv-x="511" 
d="M254 -12q-148 0 -230 84l52 84q31 -32 79.5 -53.5t101.5 -21.5q57 0 85.5 21t28.5 53q0 26 -24.5 43t-61 24.5t-79 19.5t-79 26.5t-61 47.5t-24.5 81q0 70 59.5 117.5t151.5 47.5q133 0 217 -79l-53 -78q-31 30 -76 46.5t-89 16.5t-71.5 -19t-27.5 -49q0 -26 34 -41.5
t82 -25.5t96.5 -25.5t82.5 -52.5t34 -95q0 -78 -59 -125t-169 -47zM262 598q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni021A.smcp" horiz-adv-x="480" 
d="M302 -138q0 -42 -21.5 -80t-56.5 -63l-39 31q21 13 39 37t22 45q-4 -2 -14 -2q-20 0 -34 14.5t-14 36.5q0 23 16 39.5t39 16.5q26 0 44.5 -20t18.5 -55zM294 0h-109v455h-162v95h434v-95h-163v-455z" />
    <glyph glyph-name="uni1E6A.smcp" horiz-adv-x="480" 
d="M294 0h-109v455h-162v95h434v-95h-163v-455zM242 598q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="uni1EE4.smcp" horiz-adv-x="636" 
d="M318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -106 -64 -167t-186 -61zM318 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17z" />
    <glyph glyph-name="Uhorn.smcp" horiz-adv-x="636" 
d="M318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-41q49 33 54 77q-2 -2 -12 -2q-19 0 -32 13.5t-13 33.5q0 21 14.5 36t35.5 15q24 0 41 -18.5t17 -50.5q0 -46 -28 -87t-77 -65v-245q0 -106 -64 -167
t-186 -61z" />
    <glyph glyph-name="uni1EE6.smcp" horiz-adv-x="636" 
d="M318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-334q0 -106 -64 -167t-186 -61zM281 635l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30z" />
    <glyph glyph-name="uni1EE8.smcp" horiz-adv-x="636" 
d="M497 750l-165 -144h-75l140 144h100zM318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-41q49 33 54 77q-2 -2 -12 -2q-19 0 -32 13.5t-13 33.5q0 21 14.5 36t35.5 15q24 0 41 -18.5t17 -50.5
q0 -46 -28 -87t-77 -65v-245q0 -106 -64 -167t-186 -61z" />
    <glyph glyph-name="uni1EEA.smcp" horiz-adv-x="636" 
d="M379 606h-75l-165 144h100zM318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-41q49 33 54 77q-2 -2 -12 -2q-19 0 -32 13.5t-13 33.5q0 21 14.5 36t35.5 15q24 0 41 -18.5t17 -50.5q0 -46 -28 -87
t-77 -65v-245q0 -106 -64 -167t-186 -61z" />
    <glyph glyph-name="uni1EEC.smcp" horiz-adv-x="636" 
d="M281 635l-40 12q18 56 77 56q33 0 54.5 -18t21.5 -53q0 -26 -17 -52h-48q21 22 21 50q0 35 -32 35q-27 0 -37 -30zM318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-41q49 33 54 77q-2 -2 -12 -2
q-19 0 -32 13.5t-13 33.5q0 21 14.5 36t35.5 15q24 0 41 -18.5t17 -50.5q0 -46 -28 -87t-77 -65v-245q0 -106 -64 -167t-186 -61z" />
    <glyph glyph-name="uni1EEE.smcp" horiz-adv-x="636" 
d="M318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-41q49 33 54 77q-2 -2 -12 -2q-19 0 -32 13.5t-13 33.5q0 21 14.5 36t35.5 15q24 0 41 -18.5t17 -50.5q0 -46 -28 -87t-77 -65v-245q0 -106 -64 -167
t-186 -61zM376 604q-19 0 -34.5 8t-25.5 19.5t-18.5 23t-19 19.5t-21.5 8q-18 0 -30 -18t-12 -54h-57q0 62 26.5 98t76.5 36q29 0 50 -19.5t36.5 -39t32.5 -19.5q41 0 41 72h58q0 -62 -26.5 -98t-76.5 -36z" />
    <glyph glyph-name="uni1EF0.smcp" horiz-adv-x="636" 
d="M318 -191q-24 0 -41.5 17t-17.5 42q0 24 17.5 41.5t41.5 17.5t41 -17.5t17 -41.5q0 -25 -17 -42t-41 -17zM318 -12q-123 0 -186.5 61t-63.5 166v335h110v-326q0 -65 36.5 -102.5t103.5 -37.5t103.5 37.5t36.5 102.5v326h110v-41q49 33 54 77q-2 -2 -12 -2q-19 0 -32 13.5
t-13 33.5q0 21 14.5 36t35.5 15q24 0 41 -18.5t17 -50.5q0 -46 -28 -87t-77 -65v-245q0 -106 -64 -167t-186 -61z" />
    <glyph glyph-name="Wacute.smcp" horiz-adv-x="783" 
d="M613 0h-114l-108 389l-108 -389h-114l-165 550h121l107 -405l116 405h87l115 -406l108 406h121zM570 750l-165 -144h-75l140 144h100z" />
    <glyph glyph-name="Wdieresis.smcp" horiz-adv-x="783" 
d="M613 0h-114l-108 389l-108 -389h-114l-165 550h121l107 -405l116 405h87l115 -406l108 406h121zM555 658q0 -25 -17.5 -42t-41.5 -17t-41.5 17t-17.5 42q0 24 17.5 41t41.5 17t41.5 -17t17.5 -41zM345 658q0 -25 -17 -42t-41 -17q-25 0 -42 17t-17 42q0 24 17 41t42 17
q24 0 41 -17t17 -41z" />
    <glyph glyph-name="Wgrave.smcp" horiz-adv-x="783" 
d="M613 0h-114l-108 389l-108 -389h-114l-165 550h121l107 -405l116 405h87l115 -406l108 406h121zM452 606h-75l-165 144h100z" />
    <glyph glyph-name="Ygrave.smcp" horiz-adv-x="557" 
d="M334 0h-108v229l-226 321h124l156 -229l153 229h124l-223 -321v-229zM341 606h-75l-165 144h100z" />
    <glyph glyph-name="afii10065.alt1" horiz-adv-x="581" 
d="M513 0h-105v66q-60 -78 -155 -78t-155 68t-60 185q0 116 60 185t155 69t154 -79v67h106v-483zM283 81q37 0 72 18t53 46v191q-18 28 -53 46.5t-72 18.5q-62 0 -99 -44.5t-37 -115.5t37 -115.5t99 -44.5z" />
    <hkern u1="&#x2215;" u2="&#xf659;" k="50" />
    <hkern u1="&#x2215;" u2="&#xf656;" k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="dagger"
	k="95" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V.smcp"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="daggerdbl"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="question"
	k="75" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="95" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="J,Jcircumflex,afii10057"
	k="-15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="asterisk"
	k="95" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="65" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="65" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="13" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="v,gamma,nu"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="bullet.case"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="45" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="dagger"
	k="75" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V.smcp"
	k="30" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="daggerdbl"
	k="20" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question"
	k="45" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="80" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="45" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="20" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="asterisk"
	k="60" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="15" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V"
	k="50" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="75" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="40" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question.smcp"
	k="45" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="bullet"
	k="10" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="15" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="V"
	k="15" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Jcircumflex.smcp,J.smcp"
	k="15" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="question"
	k="45" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="V"
	k="20" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="25" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="question"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="V"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="bullet.case"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="5" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="question"
	k="15" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="5" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="65" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V"
	k="15" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="45" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="5" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="X.smcp"
	k="5" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="question"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="J,Jcircumflex,afii10057"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="25" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="V"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Jcircumflex.smcp,J.smcp"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="35" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="25" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V.smcp"
	k="20" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="question"
	k="40" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="25" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="75" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V"
	k="50" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="80" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="15" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="15" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="15" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X.smcp"
	k="25" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="15" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="50" />
    <hkern g1="Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,OE.smcp,E.smcp,AE.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="J,Jcircumflex,afii10057"
	k="65" />
    <hkern g1="F,uni1E1E"
	g2="Jcircumflex.smcp,J.smcp"
	k="60" />
    <hkern g1="F,uni1E1E"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="45" />
    <hkern g1="F,uni1E1E"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="40" />
    <hkern g1="F,uni1E1E"
	g2="X.smcp"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="F,uni1E1E"
	g2="ampersand"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="ampersand.smcp"
	k="25" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="J,Jcircumflex,afii10057"
	k="40" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="10" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="50" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="25" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="25" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="15" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="25" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="ampersand.smcp"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="question"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="V"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="15" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="5" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="question"
	k="25" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="5" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="10" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="5" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="question"
	k="5" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="45" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="V"
	k="20" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="35" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="V.smcp"
	k="45" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="55" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="55" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="15" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="30" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="45" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="bullet.case"
	k="70" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="70" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="X.smcp"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="ampersand.smcp"
	k="5" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="15" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="30" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="x,afii10072,afii10087"
	k="30" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="45" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="15" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="25" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="V"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="45" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="bullet"
	k="45" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="dagger"
	k="130" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V.smcp"
	k="55" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="daggerdbl"
	k="95" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="question"
	k="110" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="130" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="75" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="45" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="asterisk"
	k="170" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="70" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="125" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="v,gamma,nu"
	k="55" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="55" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="bullet.case"
	k="110" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="75" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="ampersand"
	k="5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="15" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V.smcp"
	k="70" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="110" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="85" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="60" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="asterisk"
	k="115" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="105" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V"
	k="95" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="120" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="100" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="J,Jcircumflex,afii10057"
	k="115" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="5" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Jcircumflex.smcp,J.smcp"
	k="100" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="75" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="15" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand"
	k="30" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand.smcp"
	k="40" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="20" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="15" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="75" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="45" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="V"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="17" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="bullet.case"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand.smcp"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="V.smcp"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="v,gamma,nu"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="x,afii10072,afii10087"
	k="15" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="55" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="V"
	k="20" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="V.smcp"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="20" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="J,Jcircumflex,afii10057"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="5" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="v,gamma,nu"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="bullet.case"
	k="70" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Jcircumflex.smcp,J.smcp"
	k="105" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="100" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="65" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="X.smcp"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="95" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand"
	k="55" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand.smcp"
	k="95" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="55" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="105" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="x,afii10072,afii10087"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="75" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="30" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="20" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="V"
	k="5" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="75" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="45" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="35" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand"
	k="40" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand.smcp"
	k="45" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="J,Jcircumflex,afii10057"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="Jcircumflex.smcp,J.smcp"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="15" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="30" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="V"
	k="20" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="V,afii10062,afii10037"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="50" />
    <hkern g1="V,afii10062,afii10037"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="5" />
    <hkern g1="V,afii10062,afii10037"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="J,Jcircumflex,afii10057"
	k="95" />
    <hkern g1="V,afii10062,afii10037"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="25" />
    <hkern g1="V,afii10062,afii10037"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="50" />
    <hkern g1="V,afii10062,afii10037"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="15" />
    <hkern g1="V,afii10062,afii10037"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="V,afii10062,afii10037"
	g2="v,gamma,nu"
	k="15" />
    <hkern g1="V,afii10062,afii10037"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="10" />
    <hkern g1="V,afii10062,afii10037"
	g2="bullet.case"
	k="45" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="30" />
    <hkern g1="V,afii10062,afii10037"
	g2="Jcircumflex.smcp,J.smcp"
	k="95" />
    <hkern g1="V,afii10062,afii10037"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="50" />
    <hkern g1="V,afii10062,afii10037"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="50" />
    <hkern g1="V,afii10062,afii10037"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand"
	k="35" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand.smcp"
	k="55" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen,periodcentered,endash,emdash"
	k="55" />
    <hkern g1="V,afii10062,afii10037"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="25" />
    <hkern g1="V,afii10062,afii10037"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="65" />
    <hkern g1="V,afii10062,afii10037"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="60" />
    <hkern g1="V,afii10062,afii10037"
	g2="x,afii10072,afii10087"
	k="25" />
    <hkern g1="V,afii10062,afii10037"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="45" />
    <hkern g1="V,afii10062,afii10037"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="50" />
    <hkern g1="V.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="V.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="25" />
    <hkern g1="V.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="75" />
    <hkern g1="V.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="30" />
    <hkern g1="V.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="V.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="V.smcp"
	g2="ampersand"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex,afii10057"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v,gamma,nu"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Jcircumflex.smcp,J.smcp"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="65" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x,afii10072,afii10087"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="30" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="25" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="25" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="45" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="20" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="45" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="ampersand"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="J,Jcircumflex,afii10057"
	k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="bullet.case"
	k="85" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="95" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Jcircumflex.smcp,J.smcp"
	k="120" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="75" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="ampersand"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="120" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="125" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="x,afii10072,afii10087"
	k="75" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="colon,semicolon"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="90" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="30" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="25" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="95" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="40" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="50" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="70" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand"
	k="45" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand.smcp"
	k="55" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="bullet.case"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="60" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="question"
	k="40" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="45" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="V"
	k="70" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="110" />
    <hkern g1="ampersand"
	g2="V.smcp"
	k="40" />
    <hkern g1="ampersand"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="60" />
    <hkern g1="ampersand"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="10" />
    <hkern g1="ampersand"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="70" />
    <hkern g1="ampersand"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="ampersand"
	g2="V"
	k="50" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="85" />
    <hkern g1="ampersand"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="60" />
    <hkern g1="ampersand.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="60" />
    <hkern g1="ampersand.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="95" />
    <hkern g1="ampersand.smcp"
	g2="V"
	k="70" />
    <hkern g1="ampersand.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="60" />
    <hkern g1="asterisk"
	g2="J,Jcircumflex,afii10057"
	k="120" />
    <hkern g1="asterisk"
	g2="Jcircumflex.smcp,J.smcp"
	k="105" />
    <hkern g1="asterisk"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="60" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="95" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="question"
	k="50" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="105" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="V"
	k="60" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="120" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="x,afii10072,afii10087"
	k="35" />
    <hkern g1="bullet"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="bullet.case"
	g2="J,Jcircumflex,afii10057"
	k="80" />
    <hkern g1="bullet.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="70" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="bullet.case"
	g2="V"
	k="45" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="85" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="35" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="50" />
    <hkern g1="bullet.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="60" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="question"
	k="25" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="5" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="75" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="V"
	k="40" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="65" />
    <hkern g1="colon,semicolon"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="65" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="question"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="asterisk"
	k="15" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="95" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="V"
	k="55" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="110" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="eth"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="question"
	k="-55" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="-65" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="asterisk"
	k="-65" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-25" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-65" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="V"
	k="-65" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="-60" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="45" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand"
	k="5" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand.smcp"
	k="5" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="exclam,B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Imacron,Iogonek,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Racute,Rcommaaccent,Rcaron"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="parenright,bracketright,braceright,parenright.case,bracketright.case,braceright.case"
	k="-70" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="question"
	k="25" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="75" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="V"
	k="50" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="j,jcircumflex,afii10105"
	k="-50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="J,Jcircumflex,afii10057"
	k="65" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="30" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="95" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="45" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="60" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="75" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="35" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="55" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="115" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="45" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="55" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="V"
	k="25" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="70" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="bullet"
	k="25" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="25" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V.smcp"
	k="30" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="80" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="60" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="45" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="30" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="95" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="65" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V"
	k="85" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="25" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="25" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="v,gamma,nu"
	k="65" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="55" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="j,jcircumflex,afii10105"
	k="-40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="seven"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one"
	k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="zero,six"
	k="25" />
    <hkern g1="questiondown"
	g2="j,jcircumflex,afii10105"
	k="-160" />
    <hkern g1="questiondown.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="65" />
    <hkern g1="questiondown.case"
	g2="V"
	k="75" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="95" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="5" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="J,Jcircumflex,afii10057"
	k="100" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="100" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="80" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="V"
	k="25" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="50" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="85" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="question"
	k="45" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="80" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="45" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="V"
	k="45" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="95" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="V"
	k="30" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="35" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="question"
	k="25" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="75" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="V"
	k="50" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="v,gamma,nu,chi"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="v,gamma,nu,chi"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="v,gamma,nu,chi"
	g2="V"
	k="15" />
    <hkern g1="v,gamma,nu,chi"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="v,gamma,nu,chi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="v,gamma,nu,chi"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="65" />
    <hkern g1="v,gamma,nu,chi"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="35" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="question"
	k="5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="V"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="55" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="35" />
    <hkern g1="X,Chi,afii10039"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="50" />
    <hkern g1="X,Chi,afii10039"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="X,Chi,afii10039"
	g2="v,gamma,nu"
	k="35" />
    <hkern g1="X,Chi,afii10039"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="35" />
    <hkern g1="X,Chi,afii10039"
	g2="bullet.case"
	k="60" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="75" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen,periodcentered,endash,emdash"
	k="45" />
    <hkern g1="X.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="25" />
    <hkern g1="X.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="25" />
    <hkern g1="X.smcp"
	g2="ampersand"
	k="20" />
    <hkern g1="zero,nine"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="25" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="95" />
    <hkern g1="x,afii10087"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="x,afii10087"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="x,afii10087"
	g2="V"
	k="25" />
    <hkern g1="x,afii10087"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="75" />
    <hkern g1="x,afii10087"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="35" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.case,bracketleft.case,braceleft.case"
	g2="j,jcircumflex,afii10105"
	k="-110" />
    <hkern g1="one.numr"
	g2="fraction"
	k="-25" />
    <hkern g1="seven.numr"
	g2="fraction"
	k="50" />
    <hkern g1="fraction"
	g2="one.dnom"
	k="-10" />
    <hkern g1="fraction"
	g2="four.dnom"
	k="50" />
  </font>
</defs></svg>
