﻿using System.Threading.Tasks;
using AutoMapper;
using DinkToPdf;
using DinkToPdf.Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Snp.PDF.Printer.Models.v2;
using Snp.PDF.Printer.Services.v2;
using SNP.PDF.Printer.Models;

namespace Snp.PDF.Printer.Controllers.v2
{
    [ApiVersion("2.0")]
    [Produces("application/json")]
    [ApiController]
    public class PrinterPdfController : ControllerBase
    {
        private readonly IMapper _mapper;

        private readonly IPrinterPdfv2Service _printerPdfv2Service;

        private readonly IValidateModel _validateModel;

        public PrinterPdfController(IPrinterPdfv2Service printerPdfv2Service,
                                    IConverter converter,
                                    IOptions<HtmlToPdfDocument> document,
                                    IMapper mapper,
                                    IValidateModel validateModel)
        {
            _printerPdfv2Service = printerPdfv2Service;

            _mapper = mapper;
            _validateModel = validateModel;
        }

        /// <summary>
        ///     Print
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [MapToApiVersion("2.0")]
        [HttpPost]
        [Route("api/v{version:apiVersion}" + "/[controller]/Single")]
        public async Task<ActionResult<PrinterPdfResponse>> Print(PrinterPdfRequest req)
        {
            return Ok(await _printerPdfv2Service.PrinterPdf(req));
        }

        [MapToApiVersion("2.0")]
        [HttpPost]
        [Route("api/v{version:apiVersion}" + "/[controller]/Zip")]
        public async Task<ActionResult<PrinterPdfResponse>> Print(ZipPrinterPdfRequest req)
        {
            return Ok(await _printerPdfv2Service.ZipPrinterPdf(req));
        }
    }
}
