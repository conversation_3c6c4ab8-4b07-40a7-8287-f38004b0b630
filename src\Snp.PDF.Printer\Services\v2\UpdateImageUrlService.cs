﻿using Snp.PDF.Printer.Models.v2;
using Snp.PDF.Printer.Services.v2.Helper;
using Snp.PDF.Printer.Services.v2.Utils;
using SNP.PDF.Printer.Helper;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Snp.PDF.Printer.Services.v2
{
    public class UpdateImageUrlService : IUpdateImageUrlService
    {
        #region IDispose pattern
        // Flag: Has Dispose already been called?
        bool disposed = false;

        // Public implementation of Dispose pattern callable by consumers.
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        // Protected implementation of Dispose pattern.
        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;

            if (disposing)
            {
                // Free any other managed objects here.
                //
            }

            // Free any unmanaged objects here.
            //
            disposed = true;
        }

        ~UpdateImageUrlService()
        {
            Dispose(false);
        }
        #endregion

        public dynamic UpdateImageEmbedded(dynamic data, string billType)
        {
            var dataRes = data;
            var path = StaticPath.Instance.GetPath(PathTypes.Path_ImageEmbedded, billType);
            var filenames = System.IO.Directory.GetFiles(path);

            var url = StaticPath.Instance.GetUrl(PathTypes.Url_ImageEmbedded, billType);
            var attrName = string.Empty;
            var filename = string.Empty;
            for (int i = filenames.Length - 1; i >= 0; i--)
            {
                attrName = System.IO.Path.GetFileNameWithoutExtension(filenames[i]);
                filename = System.IO.Path.GetFileName(filenames[i]);

                dataRes[attrName] = url + filename;
            }

            return dataRes;
        }
        public dynamic UpdateLogo(dynamic data, string siteId)
        {
            var urlLogo = StaticPath.Instance.GetUrl(PathTypes.Url_Logo_With_SiteId, siteId);

            var dataRes = data;
            dataRes[StaticPath.Instance.FolderLogo] = urlLogo;

            return dataRes;
        }

        public dynamic UpdateBarcode(dynamic data, MetaInfomation meta)
        {
            var dataRes = data;
            var barcodeConfig = ConfigHelper.Instance[meta.BillType];
            if (barcodeConfig is null)
                return dataRes;

            // get all properties
            var properties = barcodeConfig.FormatData.Matches(RegexPattern.Parameter);
            if (!properties.Any())
                return dataRes;

            var dics = new Dictionary<string, string>();

            var valInfo = string.Empty;
            object objInfo;
            // find object value in object meta
            foreach (var propertyName in properties)
            {
                objInfo = meta.GetPropValue(propertyName);
                if (objInfo is null)
                {
                    valInfo = string.Empty;
                }
                else
                {
                    valInfo = objInfo.ToString();
                }
                dics.Add(propertyName, valInfo);
            }

            // find object value in dynamic data
            foreach (var key in dics.Where(p => p.Value.Equals(string.Empty)).Select(p => p.Key).ToArray())
            {
                objInfo = data[key];
                if (objInfo is null)
                {
                    valInfo = string.Empty;
                }
                else
                {
                    valInfo = objInfo.ToString();
                }
                dics[key] = valInfo;
            }

            var textBarcode = barcodeConfig.FormatData;
            foreach (var propVal in dics)
            {
                textBarcode = textBarcode.Replace(propVal.Key, propVal.Value);
            }

            textBarcode = textBarcode.Replace("{", string.Empty).Replace("}", string.Empty);

            dataRes[barcodeConfig.NameAttribute] = QrCodeHelper.Generator(textBarcode);
            return dataRes;
        }
    }
}
