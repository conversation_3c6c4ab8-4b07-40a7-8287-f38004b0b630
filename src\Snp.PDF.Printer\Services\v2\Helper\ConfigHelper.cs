﻿using Snp.PDF.Printer.Models.v2.Barcode;
using Snp.PDF.Printer.Models.v2.Validate;
using Snp.PDF.Printer.Services.v2.Utils;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Snp.PDF.Printer.Services.v2.Helper
{
    /// <summary>
    /// ConfigHelper
    /// <para>Management all config of Users</para>
    /// </summary>
    public class ConfigHelper
    {
        #region Defines
        private static readonly Lazy<ConfigHelper> _instance = new Lazy<ConfigHelper>(() => new ConfigHelper());
        public static ConfigHelper Instance
        {
            get { return _instance.Value; }
        }

        private static IDictionary<string, IEnumerable<ValidateProperty>> _dicValidate;
        private static IEnumerable<string> _billTypes;
        public IDictionary<string, IEnumerable<ValidateProperty>> ValidateProperties { get { return _dicValidate; } }
        public IEnumerable<string> BillTypes { get { return _billTypes; } }
        private static IDictionary<string, BarcodeConfig> _barcodeConfigs;
        public BarcodeConfig this[string billType]
        {
            get
            {
                if (_barcodeConfigs.TryGetValue(billType, out BarcodeConfig barcodeConfig))
                    return barcodeConfig;

                return null;
            }
        }
        #endregion

        #region C'tor
        private static bool IsInitFlag = false;
        /// <summary>
        /// C'tor
        /// </summary>
        public ConfigHelper()
        {
            if (!IsInitFlag)
            {
                Init();

                IsInitFlag = true;
            }
        }
        #endregion

        private void Init()
        {
            _dicValidate = new Dictionary<string, IEnumerable<ValidateProperty>>();

            var billTypes = GetBillTypes();
            var currentPath = string.Empty;
            var objValidates = Enumerable.Empty<ValidateProperty>();

            foreach (var folderName in billTypes)
            {
                currentPath = StaticPath.Instance.GetPath(PathTypes.Path_ValidateObject_With_BillType, billType: folderName);
                objValidates = Utilities.DeserializeObjectFromFileConfig<IEnumerable<ValidateProperty>>(currentPath, out _);

                if (objValidates == default(IEnumerable<ValidateProperty>)) continue;

                _dicValidate.Add(folderName, objValidates);
            }

            // update _billtypes
            _billTypes = billTypes;
            
            // update barcode config
            _barcodeConfigs = DeserializeBarCode();
        }

        
        /// <summary>
        /// DeserializeBarCode
        /// </summary>
        /// <returns></returns>
        private IDictionary<string, BarcodeConfig> DeserializeBarCode()
        {
            _barcodeConfigs = new Dictionary<string, BarcodeConfig>();
            var billTypes = this.BillTypes;
            var objBarcode = default(BarcodeConfig);
            var currentPath = string.Empty;
            foreach (var folderName in billTypes)
            {
                currentPath = StaticPath.Instance.GetPath(PathTypes.Path_BarCodeConfig, billType: folderName);
                objBarcode = Utilities.DeserializeObjectFromFileConfig<BarcodeConfig>(currentPath, out _);

                if (objBarcode == default(BarcodeConfig)) continue;

                _barcodeConfigs.Add(folderName, objBarcode);
            }

            return _barcodeConfigs;
        }

        /// <summary>
        /// Get BillTypes from path, each BillType is a foldername.
        /// </summary>
        /// <returns></returns>
        private IEnumerable<string> GetBillTypes()
        {
            var path = StaticPath.Instance.GetDirectoryUserConfig();
            var parentDirectory = Directory.GetDirectories(path);

            DirectoryInfo dirInfo;
            foreach (var directory in parentDirectory)
            {
                // Notice I've created a DirectoryInfo variable.
                dirInfo = new DirectoryInfo(directory);

                if (dirInfo.Name.Equals("Logo")) continue;

                // And likewise a name variable for storing the name.
                // If this is not added, only the first directory will
                // be captured in the loop; the rest won't.
                yield return dirInfo.Name;
            }
        }
    }
}
