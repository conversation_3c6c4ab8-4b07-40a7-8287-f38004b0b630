/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   Ribbons element
   ========================================================================== */
.ribbon {
  position: absolute;
  top: 10px;
  right: -1px;
  color: #ffffff;
  background: #00a8ff;
  padding: 5px 12px 5px 9px;
  white-space: nowrap;
}
.ribbon.right-top {
  top: 10px;
  bottom: auto;
  right: -1px;
  left: auto;
}
.ribbon.right-top:after {
  position: absolute;
  left: -10px;
  top: 0px;
  display: block;
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 10px 17px 0;
  border-top-color: transparent !important;
  border-right-color: #00a8ff;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
}
.ribbon.right-top:before {
  position: absolute;
  left: -10px;
  bottom: 0px;
  display: block;
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0px 0px 17px 10px;
  border-top-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: #00a8ff;
  border-left-color: transparent !important;
}
.ribbon.right-bottom {
  top: auto;
  bottom: 10px;
  right: -1px;
  left: auto;
}
.ribbon.right-bottom:after {
  position: absolute;
  left: -10px;
  top: 0px;
  display: block;
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 10px 17px 0;
  border-top-color: transparent !important;
  border-right-color: #00a8ff;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
}
.ribbon.right-bottom:before {
  position: absolute;
  left: -10px;
  bottom: 0px;
  display: block;
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0px 0px 17px 10px;
  border-top-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: #00a8ff;
  border-left-color: transparent !important;
}
.ribbon.left-top {
  padding: 5px 9px 5px 12px;
  top: 10px;
  bottom: auto;
  right: auto;
  left: -1px;
}
.ribbon.left-top:after {
  position: absolute;
  right: -10px;
  left: auto;
  top: 0px;
  display: block;
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 17px 10px 0px 0px;
  border-top-color: #00a8ff;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
}
.ribbon.left-top:before {
  position: absolute;
  right: -10px;
  left: auto;
  bottom: 0px;
  display: block;
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 17px 0px 0px 10px;
  border-top-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  border-left-color: #00a8ff;
}
.ribbon.left-bottom {
  padding: 5px 9px 5px 12px;
  top: auto;
  bottom: 10px;
  right: auto;
  left: -1px;
}
.ribbon.left-bottom:after {
  position: absolute;
  right: -10px;
  left: auto;
  top: 0px;
  display: block;
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 17px 10px 0px 0px;
  border-top-color: #00a8ff;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
}
.ribbon.left-bottom:before {
  position: absolute;
  right: -10px;
  left: auto;
  bottom: 0px;
  display: block;
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 17px 0px 0px 10px;
  border-top-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  border-left-color: #00a8ff;
}
.ribbon.green {
  background-color: #4cc159;
}
.ribbon.green:before,
.ribbon.green:after {
  border-color: #4cc159;
}
.ribbon.purple {
  background-color: #00bbd4;
}
.ribbon.purple:before,
.ribbon.purple:after {
  border-color: #00bbd4;
}
.ribbon.yellow {
  background-color: #fdc006;
}
.ribbon.yellow:before,
.ribbon.yellow:after {
  border-color: #fdc006;
}
.ribbon.red {
  background-color: #f34135;
}
.ribbon.red:before,
.ribbon.red:after {
  border-color: #f34135;
}
.ribbon.transparent {
  background-color: rgba(255, 255, 255, 0.5);
}
.ribbon.transparent:before,
.ribbon.transparent:after {
  border-color: rgba(255, 255, 255, 0.5);
}
