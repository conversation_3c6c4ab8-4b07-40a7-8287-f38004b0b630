/* ==========================================================================
   Mixins
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   Prices
   ========================================================================== */
.prices-page {
  text-align: center;
}
.prices-page p {
  margin: 0;
}
.prices-page .prices-page-title {
  font-size: 1.75rem;
  margin: 0 0 15px;
  padding: 10px 0 0;
}
.prices-page .prices-page-subtitle {
  padding-bottom: 30px;
  font-size: 1.25rem;
}
.prices-page .prices-page-bottom {
  padding: 30px 0 15px;
}
.price-card {
  width: 100%;
  max-width: 225px;
  text-align: center;
  display: inline-block;
  vertical-align: bottom;
  margin: 20px 7px 0;
}
.price-card .price-card-header {
  border: solid 1px #d8e2e7;
  -webkit-border-radius: .25rem .25rem 0 0;
          border-radius: .25rem .25rem 0 0;
  background: #f6f8fa;
  padding: 15px;
  font-size: 1.25rem /*20/16*/;
}
.price-card .price-card-body {
  background: #fff;
  border: solid 1px #d8e2e7;
  border-top: none;
  -webkit-border-radius: 0 0 .25rem .25rem;
          border-radius: 0 0 .25rem .25rem;
  padding: 25px 0;
}
.price-card .price-card-amount {
  font-size: 2.375rem /*38/16*/;
  line-height: 1;
  white-space: nowrap;
}
.price-card .price-card-amount-lbl {
  padding: 10px 0 15px;
}
.price-card .price-card-list {
  text-align: left;
  padding: 20px 30px 18px;
}
.price-card .price-card-list li {
  margin: 0 0 .25rem;
}
.price-card .price-card-list .font-icon {
  color: #00a8ff;
  vertical-align: middle;
  margin: 0 5px 0 0;
}
.price-card .price-card-label {
  padding: 10px;
  background: #00a8ff;
  color: #fff;
}
@media (max-width: 1056px) {
  .price-card {
    display: block;
    max-width: none;
    margin-right: 0;
    margin-left: 0;
  }
  .price-card .price-card-list {
    display: inline-block;
  }
}
