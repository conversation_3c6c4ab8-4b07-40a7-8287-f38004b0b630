﻿@using SNP.PDF.Printer
@using SNP.PDF.Printer.Enums
<html>
<head>
</head>
<body>
<table class="to-vien" style="width:100%;">
<tbody>
<tr>
    <td>
        <table style="border-bottom:1px solid gray;width:100%;">
            <tbody>
            <tr colspan="4"></tr>
            <tr>
                <th width="20%" rowspan="8" style="text-align:left;">
                    <img src="@Model.LogoUrl" title="logo tân cảng sài gòn" class="img-logo" id="logo-snp" width="150"/>
                </th>
                <th></th>
                <th width="15%"></th>
                <th width="15%"></th>
            </tr>
            <tr>
                <th class="tieu-de" style="border-bottom:1px solid gray;padding-bottom:10px;">
                    @Model.TenCongTy<br/>
                </th>
                <th colspan="2" rowspan="2" style="text-align:center;">
                    <img src="@Model.MaVachUrl" alt="barcode" width="106" height="53"/>
                </th>
            </tr>
            <tr>
                <th></th>
            </tr>
            <tr>
                <th class="tieu-de">PHIẾU GIAO NHẬN CONTAINER</th>
                <th width="15%" class="chu-thuong" style="vertical-align:bottom">Số ĐK <i>(No)</i>:</th>
                <td width="15%" class="chu-thuong"> @Model.SoPhieuEIR</td>
            </tr>
            <tr>
                <th class="tieu-de">EQUIPMENT INTERCHANGE RECEIPT</th>
                <th width="15%" class="chu-thuong">Ngày <i>(Date)</i>:</th>
                <td width="15%" class="chu-thuong">@Model.NgayGioTaoEIR</td>
            </tr>
            <tr>
                <th class="tieu-de">@Model.TenPhuongAn @Model.SiteGiaoNhan (@Model.SitePhieuEIR)</th>
                <th colspan="2"></th>
            </tr>
            <tr>
                <th colspan="3"></th>
            </tr>
            <tr>
                <th colspan="3"></th>
            </tr>
            <tr>
                <th width="20%"></th>
                <th></th>
                <th width="10%">Gate No:</th>
                <td>@Model.GateNo</td>
            </tr>
            <tr>
                <td colspan="2">
                    <b>Nhận của khách hàng <i>(Receive from)</i>:</b> @Model.TenKhachHang</td>
                <th>BAT No:</th>
                <td>@Model.BatNo</td>
            </tr>
            <tr>
                <th colspan="4"></th>
            </tr>
            <tr>
                <th colspan="4"></th>
            </tr>
            </tbody>
        </table>
    </td>
</tr>
<tr>
    <td>
        <table style="width:100% !important;">
            <tbody>
            <tr>
                <td colspan="10">
                    <table style="border-collapse:separate;border-spacing:5px;width:100%;">
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th style="text-align:left;">Bill/Booking/Ref: </th>
                            <td colspan="3">@Model.SoBillOfLading</td>
                            <th style="text-align:left;" colspan="2">Cảng đích <i>(Dest)</i>:</th>
                            <td colspan="2">@Model.CangDich</td>
                        </tr>
                        <tr>
                            <th style="text-align:left;">Tàu/Chuyến: </th>
                            <td colspan="3">@Model.TenTauMaChuyen</td>
                            <th style="text-align:left;" colspan="2">Cảng chuyển tải <i>(Transit)</i>: </th>
                            <td colspan="2">@Model.CangChuyenTai</td>
                        </tr>
                        <tr>
                            <td>
                                <i>(Vessel / Voyage)</i>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <th width="13%" class="to-vien-tren to-vien-phai to-vien-duoi">
                    Container No.
                </th>
                <th width="10%" class="to-vien-tren to-vien-phai to-vien-duoi">
                    Kích cỡ
                    <br/>
                    <span>
                        <i>(Sz/Tp)</i>
                    </span>
                </th>
                <th width="10%" class="to-vien-tren to-vien-phai to-vien-duoi">
                    Chủ KT
                    <br/>
                    <span>
                        <i>(Operator)</i>
                    </span>
                </th>
                <th class="to-vien-tren to-vien-phai to-vien-duoi" style="width:22%">
                    Seal No
                </th>
                <th width="10%" class="to-vien-tren to-vien-phai to-vien-duoi">
                    Status
                </th>
                <th width="5%" class="to-vien-tren to-vien-phai to-vien-duoi">
                    Weight
                    <br/>
                    <span>
                        <i>(Tons)</i>
                    </span>
                </th>
                <th width="5%" class="to-vien-tren to-vien-phai to-vien-duoi">
                    IMDG
                </th>
                <th width="5%" class="to-vien-tren to-vien-phai to-vien-duoi">
                    UNNO
                </th>
                <th width="10%" class="to-vien-tren to-vien-phai to-vien-duoi">
                    OH
                    <br/>
                    OW / OL
                </th>
                <th width="10%" class="to-vien-tren to-vien-duoi">
                    TEMP
                </th>
            </tr>
            @{
                foreach (var detail in Model.Detail) {
                    <tr style="text-align:center;">
                        <td class="to-vien-phai to-vien-duoi">@detail.SoCont</td>
                        <td class="to-vien-phai to-vien-duoi">@detail.ISO</td>
                        <td class="to-vien-phai to-vien-duoi">@detail.ChuKT</td>
                        <td class="to-vien-phai to-vien-duoi">@detail.SoSeal</td>
                        <td class="to-vien-phai to-vien-duoi">@detail.TtCont</td>
                        <td class="to-vien-phai to-vien-duoi">@detail.TongTL</td>
                        <td class="to-vien-phai to-vien-duoi">@detail.SoIMDG</td>
                        <td class="to-vien-phai to-vien-duoi">@detail.SoUNNO</td>
                        <td class="to-vien-phai to-vien-duoi">@detail.OwOhOl</td>
                        <td class="to-vien-duoi">@detail.Temt</td>
                    </tr>
                }
            }

            <tr>
                <td colspan="4" rowspan="4" class="to-vien-phai to-vien-duoi">
                    <img src="@AppUtil.ImageEir[PrintType.GiaoContChoCang]" style="height:200px;width:100%;" alt="mo ta container"/>
                </td>
                <td colspan="6" rowspan="9" class="to-vien-duoi" style="vertical-align:top;">
                    <b>Max Gross: </b>@Model.MaxGross. <b>VGM </b>:@Model.VgmCustomer. <b>TL Vỏ :</b>@Model.TlVo
                    <br/>
                    <b>Số DK Eport: </b> @Model.SoDkEport
                    <br/>
                    @Model.GhiChuThongTinCan
                    <br/>
                    @Model.GhiChuViPhamTaiTrong
                </td>
            </tr>
            <tr></tr>
            <tr></tr>
            <tr></tr>
            <tr>
                <td colspan="4" rowspan="5" class="to-vien-duoi to-vien-phai">
                    <b>Ghi chú (Remark):</b>
                    <br/>
                    <span style="margin-left:30px;">@Model.GhiChuStopCode</span>
                    <br/>
                    <span style="margin-left:30px;">@Model.GhiChuChiDinh</span>
                    <br/>
                    <span style="margin-left:30px;">@Model.GhiChuThongTinCont</span>
                    <br/>
                    <span style="margin-left:30px;">@Model.GhiChuHuHong</span>
                </td>
                <td></td>
            </tr>
            </tbody>
        </table>
    </td>
</tr>
<tr>
    <td>
        <table style="width:100%;">
            <tbody>
            <tr>
                <td width="60%"></td>
                <td width="20%"></td>
                <td width="20%"></td>
            </tr>
            <tr>
                <td width="60%"></td>
                <td width="20%"></td>
                <td width="20%"></td>
            </tr>
            <tr>
                <th width="60%">Người nhận container <i>(Receiver)</i></th>
                <td width="20%">
                    <b>
                        <i>Số xe VC: </i>
                    </b>@Model.SoXe</td>
                <td width="20%">
                    <b>
                        <i>Số Moọc: </i>
                    </b>@Model.SoMooc</td>

            </tr>
            <tr class="nguoi-ky">
                <td width="60%">
                    <i>(Ký, đóng dấu, ghi rõ họ tên)</i>
                </td>
                <td width="20%"></td>
                <td width="20%"></td>
            </tr>
            <tr>
                <td width="60%"></td>
                <td width="20%"></td>
                <td width="20%"></td>
            </tr>
            <tr>
                <td width="60%"></td>
                <td width="20%"></td>
                <td width="20%"></td>
            </tr>
            <tr>
                <td width="60%"></td>
                <td width="20%"></td>
                <td width="20%"></td>
            </tr>
            <tr>
                <td width="60%"></td>
                <td width="20%" colspan="2">
                    <b>
                        <i>Thời gian in: </i>
                    </b> @string.Format("{0:dd/MM/yyyy HH:mm:ss}", DateTime.Now)</td>
            </tr>
            <tr>
                <td width="60%"></td>
                <td width="20%"></td>
                <td width="20%"></td>
            </tr>
            <tr>
                <td width="60%"></td>
                <td width="20%"></td>
                <td width="20%"></td>
            </tr>
            <tr style="border-top:1px solid gray;">
                <th colspan="3" style="text-align:center;">
                    <span class="tieu-de">
                        <b>"ĐẾN VỚI TÂN CẢNG - ĐẾN VỚI CHẤT LƯỢNG DỊCH VỤ HÀNG ĐẦU"</b>
                    </span>
                </th>
            </tr>
            </tbody>
        </table>
    </td>
</tr>
</tbody>
</table>
</body>
</html>