﻿using DinkToPdf;
using Microsoft.Extensions.Configuration;

namespace SNP.PDF.Printer.Config
{
    public static class HtmlToPdfDocumentConfig
    {
        public static HtmlToPdfDocument GetSettings(string dir, string environmentName)
        {
            var config = GetConfiguration(dir, environmentName);
            return GetSettings(config);
        }

        public static IConfigurationRoot GetConfiguration(string dir, string environmentName)
        {
            if(string.IsNullOrEmpty(environmentName))
                environmentName = "Development";

            var builder = new ConfigurationBuilder().SetBasePath(dir)
                                                    .AddJsonFile(AppUtil.APP_SETTINGS_FILENAME, true, true)
                                                    .AddJsonFile(string.Format(AppUtil.APP_SETTINGS_ENVIRONMENT_NAME, environmentName), true)
                                                    .AddEnvironmentVariables();

            return builder.Build();
        }

        public static HtmlToPdfDocument GetSettings(IConfiguration config)
        {
            return config.Get<HtmlToPdfDocument>();
        }
    }
}
