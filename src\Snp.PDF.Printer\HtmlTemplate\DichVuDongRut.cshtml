﻿<html>
<head>
</head>
<body>
<table class="to-vien" style="width:100%;">
<tbody>
<tr>
    <td>
        <table style="border-bottom:1px solid gray;width:100%;">
            <tbody>
            <tr colspan="4"></tr>
            <tr>
                <th width="20%" rowspan="6" style="text-align:left;">
                    <img src="@Model.LogoUrl" title="logo tân cảng sài gòn" class="img-logo" id="logo-snp" width="150">
                </th>
                <th></th>
                <th width="15%"></th>
                <th width="15%"></th>
            </tr>
            <tr>
                <th class="tieu-de" style="border-bottom:1px solid gray;padding-bottom:10px;">
                    @Model.TenCongTy<br>
                </th>
                <th colspan="2" rowspan="2" style="text-align:center;">
                </th>
            </tr>
            <tr>
                <th></th>
            </tr>
            <tr>
                <th class="tieu-de">PHIẾU ĐÓNG DỠ HÀNG CONTAINER</th>
                <th width="10%" class="chu-thuong" style="vertical-align:bottom">Số ĐK(No): </th>
                <td width="10%" class="chu-thuong">@Model.SoPhieuEIR</td>
            </tr>
            <tr>
                <th class="tieu-de">STUFFING / UNSTUFFING EIR</th>
                <th width="10%" class="chu-thuong">Ngày(Date):</th>
                <td width="10%" class="chu-thuong">@Model.NgayTaoEIR</td>
            </tr>
            <tr>
                <th colspan="3"></th>
            </tr>
            <tr>
                <th colspan="3"></th>
            </tr>
            <tr>
                <td colspan="4">
                    <table style="width:100%">
                        <tr>
                            <td style="text-align:center;">
                                <b>Phương án: </b>@Model.TenPhuongAn</td>
                            <td style="text-align:center;">
                                <b>Ngày giờ thực hiện: </b>@Model.NgayGioTaoEIR</td>
                        </tr>
                        <tr></tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    <b>Giao cho / Nhận của khách hàng <i>(Deliver / Receiver from)</i>:</b> @Model.TenKhachHang</td>
            </tr>
            <tr>
                <th colspan="4"></th>
            </tr>
            <tr>
                <th colspan="4"></th>
            </tr>
            </tbody>
        </table>
    </td>
</tr>
<tr>
<td>
    <table style="width:100% !important;">
        <tbody>
        <tr>
            <td colspan="12">
                <table style="border-collapse:separate;border-spacing:5px;width:100%;">
                    <tbody>
                    <tr>
                        <td width="20%"></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td width="20%"></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th style="text-align:left;">Bill/Booking/Ref: </th>
                        <td colspan="3"></td>
                        <td style="text-align:left;" colspan="2"><b>Cảng đích</b> <i>(Dest)</i>:</td>
                        <td colspan="2">@Model.CangDich</td>
                    </tr>
                    <tr>
                        <th style="text-align:left;">Tàu/Chuyến: </th>
                        <td colspan="3">@Model.TenTauMaChuyen</td>
                        <td style="text-align:left;" colspan="2"><b>Cảng chuyển tải</b> <i>(Transit)</i>: </td>
                        <td colspan="2">@Model.CangChuyenTai</td>
                    </tr>
                    <tr>
                        <td>
                            <i>(Vessel / Voyage)</i>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr>
            <th width="13%" class="to-vien-tren to-vien-phai to-vien-duoi">
                Container No.
            </th>
            <th width="12%" class="to-vien-tren to-vien-phai to-vien-duoi">
                Kích cỡ
                <br>
                <span>
                    <i>(Sz/Tp)</i>
                </span>
            </th>
            <th width="10%" class="to-vien-tren to-vien-phai to-vien-duoi">
                Chủ KT
                <br>
                <span>
                    <i>(Operator)</i>
                </span>
            </th>
            <th width="7%" class="to-vien-tren to-vien-phai to-vien-duoi">
                H.tàu
                <br>
                <span>
                    <i>(S.Lines)</i>
                </span>
            </th>
            <th class="to-vien-tren to-vien-phai to-vien-duoi" style="width:13%">
                Seal No
            </th>
            <th width="10%" class="to-vien-tren to-vien-phai to-vien-duoi">
                Status
            </th>
            <th width="5%" class="to-vien-tren to-vien-phai to-vien-duoi">
                Weight
                <br>
                <span>
                    <i>(Tons)</i>
                </span>
            </th>
            <th width="5%" class="to-vien-tren to-vien-phai to-vien-duoi">
                VGM
                <br/>
                <span>
                    <i>(Tons)</i>
                </span>
            </th>
            <th width="5%" class="to-vien-tren to-vien-phai to-vien-duoi">
                MGW
                <br/>
                <span>
                    <i>(Tons)</i>
                </span>
            </th>
            <th width="5%" class="to-vien-tren to-vien-phai to-vien-duoi">
                IMDG
                <br/>
                <span>
                    <i>(Class)</i>
                </span>
            </th>
            <th width="10%" class="to-vien-tren to-vien-phai to-vien-duoi">
                OH
                <br>
                <i>OW / OL</i>
            </th>
            <th width="5%" class="to-vien-tren to-vien-duoi">
                Khu vực
            </th>
        </tr>
        @{
            foreach (var detail in Model.Detail) {
                <tr style="text-align:center;">
                    <td class="to-vien-phai to-vien-duoi">@detail.SoCont</td>
                    <td class="to-vien-phai to-vien-duoi">@detail.ISO</td>
                    <td class="to-vien-phai to-vien-duoi">@detail.ChuKT</td>
                    <td class="to-vien-phai to-vien-duoi">@detail.HangTau</td>
                    <td class="to-vien-phai to-vien-duoi">@detail.SoSeal</td>
                    <td class="to-vien-phai to-vien-duoi">@detail.TtCont</td>
                    <td class="to-vien-phai to-vien-duoi">@detail.TongTL</td>
                    <td class="to-vien-phai to-vien-duoi">@detail.VgmCustomer</td>
                    <td class="to-vien-phai to-vien-duoi">@detail.MaxGross</td>
                    <td class="to-vien-phai to-vien-duoi">@detail.SoIMDG</td>
                    <td class="to-vien-phai to-vien-duoi">@detail.OwOhOl</td>
                    <td class="to-vien-duoi">@detail.KhuVuc</td>
                </tr>
            }
        }

        <tr>
            <td colspan="3"></td>
            <td colspan="3" class="to-vien-phai"></td>
            <td colspan="3"></td>
            <td colspan="3"></td>
        </tr>
        <tr>
            <th colspan="3" style="text-align:center;text-decoration:underline;">
                Số xe <i>(Vehicle)</i>:
            </th>
            <th colspan="3" style="text-align:center;text-decoration:underline;" class="to-vien-phai">
                Số lượng hàng <i>(Quantity)</i>:
            </th>
            <td colspan="6" rowspan="6" style="padding-left:15px;vertical-align:top;">
                <b>Tình trạng container sau đóng / rút hàng</b>
                <br/>
                <i>Container condition after stuffing / unstuffing</i>
                <br/>
                <br/>
                @Model.GhiChuHuHong
            </td>
        </tr>
        <tr>
            <td colspan="3" style="border-bottom:1px dotted gray"></td>
            <td colspan="3" style="border-bottom:1px dotted gray;border-spacing:100px;" class="to-vien-phai"></td>
        </tr>
        <tr>
            <td colspan="3" style="border-bottom:1px dotted gray"></td>
            <td colspan="3" style="border-bottom:1px dotted gray;border-spacing:100px;" class="to-vien-phai"></td>
        </tr>
        <tr>
            <td colspan="3" style="border-bottom:1px dotted gray"></td>
            <td colspan="3" style="border-bottom:1px dotted gray;border-spacing:100px;" class="to-vien-phai"></td>
        </tr>
        <tr>
            <td colspan="3" style="border-bottom:1px dotted gray"></td>
            <td colspan="3" style="border-bottom:1px dotted gray;border-spacing:100px;" class="to-vien-phai"></td>
        </tr>
        <tr>
            <td colspan="3"></td>
            <td colspan="3" class="to-vien-phai"></td>
        </tr>
        <tr>
            <td class="to-vien-phai" colspan="6" rowspan="2" style="text-align:center;">
                <span style="text-decoration:underline">
                    <b>Cộng <i>(Total)</i>: </b> @Model.TrongLuongHang</span>
            </td>
            <th colspan="3" style="text-align:center">Đội bốc xếp</th>
            <th colspan="3" style="text-align:center">Thao tác bằng</th>
        </tr>
        <tr>
            <td colspan="3" style="text-align:center;">@Model.TenDoiBocXep</td>
            <td colspan="3" style="text-align:center;">@Model.TenPhuongThucRutHang</td>
        </tr>
        <tr>
            <td colspan="6" class="to-vien-phai"></td>
            <td colspan="6"></td>
        </tr>
        <tr>
            <td colspan="6" class="to-vien-phai"></td>
            <td colspan="6"></td>
        </tr>
        <tr>
            <td class="to-vien-phai to-vien-duoi" colspan="6">
                Số lượng hàng và số xe ghi theo lời khai của khách hàng
                <br/>
                Vehicle Registered number & cargo quantity as customer's declaration
            </td>
            <td colspan="6" class="to-vien-duoi">
                <b>Số hóa đơn </b><i>(Invoice No): </i> @Model.SoHoaDonNoiBo
            </td>
        </tr>
        <tr></tr>
        </tbody>
    </table>
</td>
</tr>
<tr>
    <td>
        <table style="width:100%;">
            <tbody>
            <tr>
                <th width="33%" style="text-align:center;">Người phát hành</th>
                <th width="33%" style="text-align:center;">Người giao / nhận Container</th>
                <td width="33%"></td>

            </tr>
            <tr class="nguoi-ky">
                <td width="33%" style="text-align:center;">
                    <i>Issuer</i>
                </td>
                <td width="33%" style="text-align:center;">
                    <i>Deliver / Receiver</i>
                </td>
                <td width="33%"></td>
            </tr>
            <tr>
                <td width="33%"></td>
                <td width="33%"></td>
                <td width="33%"></td>
            </tr>
            <tr>
                <td width="33%"></td>
                <td width="33%"></td>
                <td width="33%"></td>
            </tr>
            <tr>
                <td width="33%"></td>
                <td width="33%"></td>
                <td width="33%"></td>
            </tr>
            <tr>
                <td width="33%"></td>
                <td width="33%" style="text-align:center">@Model.NguoiYeuCau @Model.SoDienThoai</td>
                <td width="33%" style="text-align:center">
                    <b>Thời gian in: </b>@string.Format("{0:dd/MM/yyyy HH:mm:ss}", DateTime.Now)
                </td>
            </tr>
            <tr>
                <td width="33%"></td>
                <td width="33%"></td>
                <td width="33%"></td>
            </tr>
            <tr style="border-top:1px solid gray;">
                <th colspan="3" style="text-align:center;">
                    <span class="tieu-de">
                        <b>"ĐẾN VỚI TÂN CẢNG - ĐẾN VỚI CHẤT LƯỢNG DỊCH VỤ HÀNG ĐẦU"</b>
                    </span>
                </th>
            </tr>
            </tbody>
        </table>
    </td>
</tr>
</tbody>
</table>
</body>
</html>