.spinner-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.5);
}
.spinner-windows8 {
  position: relative;
  display: block;
  margin: 0 auto;
  width: 50px;
  height: 50px;
  margin-top: 100px;
}
.spinner-windows8 .wBall {
  position: absolute;
  width: 48px;
  height: 48px;
  opacity: 0;
  -moz-transform: rotate(225deg);
  -moz-animation: orbit 2.75s infinite;
  -webkit-transform: rotate(225deg);
  -webkit-animation: orbit 2.75s infinite;
  -ms-transform: rotate(225deg);
  -ms-animation: orbit 2.75s infinite;
  -o-transform: rotate(225deg);
  -o-animation: orbit 2.75s infinite;
  transform: rotate(225deg);
  animation: orbit 2.75s infinite;
}
.spinner-windows8 .wBall .wInnerBall {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #000000;
  left: 0px;
  top: 0px;
  -moz-border-radius: 6px;
  -webkit-border-radius: 6px;
  -ms-border-radius: 6px;
  -o-border-radius: 6px;
  border-radius: 6px;
  z-index: 2;
}
.spinner-windows8 .wBall:first-child {
  -moz-animation-delay: 0.6s;
  -webkit-animation-delay: 0.6s;
  -ms-animation-delay: 0.6s;
  -o-animation-delay: 0.6s;
  animation-delay: 0.6s;
}
.spinner-windows8 .wBall:nth-child(2) {
  -moz-animation-delay: 0.12s;
  -webkit-animation-delay: 0.12s;
  -ms-animation-delay: 0.12s;
  -o-animation-delay: 0.12s;
  animation-delay: 0.12s;
}
.spinner-windows8 .wBall:nth-child(3) {
  -moz-animation-delay: 0.24s;
  -webkit-animation-delay: 0.24s;
  -ms-animation-delay: 0.24s;
  -o-animation-delay: 0.24s;
  animation-delay: 0.24s;
}
.spinner-windows8 .wBall:nth-child(4) {
  -moz-animation-delay: 0.36s;
  -webkit-animation-delay: 0.36s;
  -ms-animation-delay: 0.36s;
  -o-animation-delay: 0.36s;
  animation-delay: 0.36s;
}
.spinner-windows8 .wBall:nth-child(5) {
  -moz-animation-delay: 0.48s;
  -webkit-animation-delay: 0.48s;
  -ms-animation-delay: 0.48s;
  -o-animation-delay: 0.48s;
  animation-delay: 0.48s;
}
@-moz-keyframes orbit {
  0% {
    opacity: 1;
    z-index: 99;
    -moz-transform: rotate(180deg);
    -moz-animation-timing-function: ease-out;
  }
  7% {
    opacity: 1;
    -moz-transform: rotate(300deg);
    -moz-animation-timing-function: linear;
    -moz-origin: 0%;
  }
  30% {
    opacity: 1;
    -moz-transform: rotate(410deg);
    -moz-animation-timing-function: ease-in-out;
    -moz-origin: 7%;
  }
  39% {
    opacity: 1;
    -moz-transform: rotate(645deg);
    -moz-animation-timing-function: linear;
    -moz-origin: 30%;
  }
  70% {
    opacity: 1;
    -moz-transform: rotate(770deg);
    -moz-animation-timing-function: ease-out;
    -moz-origin: 39%;
  }
  75% {
    opacity: 1;
    -moz-transform: rotate(900deg);
    -moz-animation-timing-function: ease-out;
    -moz-origin: 70%;
  }
  76% {
    opacity: 0;
    -moz-transform: rotate(900deg);
  }
  100% {
    opacity: 0;
    -moz-transform: rotate(900deg);
  }
}
@-webkit-keyframes orbit {
  0% {
    opacity: 1;
    z-index: 99;
    -webkit-transform: rotate(180deg);
    -webkit-animation-timing-function: ease-out;
  }
  7% {
    opacity: 1;
    -webkit-transform: rotate(300deg);
    -webkit-animation-timing-function: linear;
    -webkit-origin: 0%;
  }
  30% {
    opacity: 1;
    -webkit-transform: rotate(410deg);
    -webkit-animation-timing-function: ease-in-out;
    -webkit-origin: 7%;
  }
  39% {
    opacity: 1;
    -webkit-transform: rotate(645deg);
    -webkit-animation-timing-function: linear;
    -webkit-origin: 30%;
  }
  70% {
    opacity: 1;
    -webkit-transform: rotate(770deg);
    -webkit-animation-timing-function: ease-out;
    -webkit-origin: 39%;
  }
  75% {
    opacity: 1;
    -webkit-transform: rotate(900deg);
    -webkit-animation-timing-function: ease-out;
    -webkit-origin: 70%;
  }
  76% {
    opacity: 0;
    -webkit-transform: rotate(900deg);
  }
  100% {
    opacity: 0;
    -webkit-transform: rotate(900deg);
  }
}
@-ms-keyframes orbit {
  0% {
    opacity: 1;
    z-index: 99;
    -ms-transform: rotate(180deg);
    -ms-animation-timing-function: ease-out;
  }
  7% {
    opacity: 1;
    -ms-transform: rotate(300deg);
    -ms-animation-timing-function: linear;
    -ms-origin: 0%;
  }
  30% {
    opacity: 1;
    -ms-transform: rotate(410deg);
    -ms-animation-timing-function: ease-in-out;
    -ms-origin: 7%;
  }
  39% {
    opacity: 1;
    -ms-transform: rotate(645deg);
    -ms-animation-timing-function: linear;
    -ms-origin: 30%;
  }
  70% {
    opacity: 1;
    -ms-transform: rotate(770deg);
    -ms-animation-timing-function: ease-out;
    -ms-origin: 39%;
  }
  75% {
    opacity: 1;
    -ms-transform: rotate(900deg);
    -ms-animation-timing-function: ease-out;
    -ms-origin: 70%;
  }
  76% {
    opacity: 0;
    -ms-transform: rotate(900deg);
  }
  100% {
    opacity: 0;
    -ms-transform: rotate(900deg);
  }
}
@-o-keyframes orbit {
  0% {
    opacity: 1;
    z-index: 99;
    -o-transform: rotate(180deg);
    -o-animation-timing-function: ease-out;
  }
  7% {
    opacity: 1;
    -o-transform: rotate(300deg);
    -o-animation-timing-function: linear;
    -o-origin: 0%;
  }
  30% {
    opacity: 1;
    -o-transform: rotate(410deg);
    -o-animation-timing-function: ease-in-out;
    -o-origin: 7%;
  }
  39% {
    opacity: 1;
    -o-transform: rotate(645deg);
    -o-animation-timing-function: linear;
    -o-origin: 30%;
  }
  70% {
    opacity: 1;
    -o-transform: rotate(770deg);
    -o-animation-timing-function: ease-out;
    -o-origin: 39%;
  }
  75% {
    opacity: 1;
    -o-transform: rotate(900deg);
    -o-animation-timing-function: ease-out;
    -o-origin: 70%;
  }
  76% {
    opacity: 0;
    -o-transform: rotate(900deg);
  }
  100% {
    opacity: 0;
    -o-transform: rotate(900deg);
  }
}
@keyframes orbit {
  0% {
    opacity: 1;
    z-index: 99;
    transform: rotate(180deg);
    animation-timing-function: ease-out;
  }
  7% {
    opacity: 1;
    transform: rotate(300deg);
    animation-timing-function: linear;
    origin: 0%;
  }
  30% {
    opacity: 1;
    transform: rotate(410deg);
    animation-timing-function: ease-in-out;
    origin: 7%;
  }
  39% {
    opacity: 1;
    transform: rotate(645deg);
    animation-timing-function: linear;
    origin: 30%;
  }
  70% {
    opacity: 1;
    transform: rotate(770deg);
    animation-timing-function: ease-out;
    origin: 39%;
  }
  75% {
    opacity: 1;
    transform: rotate(900deg);
    animation-timing-function: ease-out;
    origin: 70%;
  }
  76% {
    opacity: 0;
    transform: rotate(900deg);
  }
  100% {
    opacity: 0;
    transform: rotate(900deg);
  }
}
.tooltip {
  z-index: 20000;
}
body.lobipanel-minimized {
  padding-bottom: 36px;
}
.lobipanel {
  position: relative;
  margin-bottom: 15px;
}
.lobipanel > .panel-heading {
  padding: 5px;
  position: relative;
  border-top-right-radius: 0px;
  border-top-left-radius: 0px;
}
.lobipanel > .panel-heading > .panel-title {
  float: left;
  max-width: calc(100% - 30px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 16px;
  line-height: 30px;
  padding-left: 15px;
}
.lobipanel > .panel-heading > .panel-title h1,
.lobipanel > .panel-heading > .panel-title h2,
.lobipanel > .panel-heading > .panel-title h3,
.lobipanel > .panel-heading > .panel-title h4,
.lobipanel > .panel-heading > .panel-title h5,
.lobipanel > .panel-heading > .panel-title h6 {
  margin: 0;
  line-height: 30px;
}
.lobipanel > .panel-heading > .panel-title input {
  color: inherit;
  line-height: 30px;
  border-radius: 2px;
  padding: 0 5px;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid #337ab7;
  outline: 0;
}
.lobipanel > .panel-heading > .panel-title input:focus {
  background-color: rgba(0, 0, 0, 0.15);
}
.lobipanel > .panel-heading .dropdown {
  display: inline-block;
  float: right;
  position: relative;
}
.lobipanel > .panel-heading .dropdown .dropdown-menu {
  left: auto;
  right: 0;
  min-width: initial;
  margin-top: 0;
  border-radius: 0;
}
.lobipanel > .panel-heading .dropdown .dropdown-menu > li > a .control-title {
  display: inline-block;
  margin-left: 15px;
}
.lobipanel > .panel-heading .dropdown .dropdown-toggle {
  border: none;
  outline: 0;
  background-color: transparent;
  text-align: center;
  padding: 0;
  width: 30px;
  font-size: 14px;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  transition: all 0.2s;
}
.lobipanel > .panel-heading .dropdown .dropdown-toggle .panel-control-icon {
  top: 0;
  line-height: 30px;
}
.lobipanel > .panel-heading .dropdown .dropdown-toggle:hover,
.lobipanel > .panel-heading .dropdown .dropdown-toggle:hover:focus {
  text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.6);
  background-color: rgba(0, 0, 0, 0.15);
}
.lobipanel > .panel-heading .dropdown.open .dropdown-toggle {
  text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.6);
  background-color: rgba(0, 0, 0, 0.15);
}
.lobipanel > .panel-heading:before,
.lobipanel > .panel-heading:after {
  content: " ";
  display: table;
}
.lobipanel > .panel-heading:after {
  clear: both;
}
@media screen and (min-width: 768px) {
  .lobipanel .panel-heading .panel-title {
    max-width: calc(100% - 180px);
  }
  .lobipanel .panel-heading .dropdown .dropdown-menu {
    position: static;
    display: inline-block;
    border: none;
    padding: 0;
    margin: 0;
    min-width: initial;
    width: auto;
    -webkit-box-shadow: none;
    box-shadow: none;
    background: transparent;
  }
  .lobipanel .panel-heading .dropdown .dropdown-menu > li {
    display: inline-block;
  }
  .lobipanel .panel-heading .dropdown .dropdown-menu > li > a {
    cursor: pointer;
    color: inherit;
    padding: 0;
    outline: 0;
    text-align: center;
    width: 30px;
    font-size: 14px;
    -webkit-transition: all 0.2s;
    -o-transition: all 0.2s;
    transition: all 0.2s;
  }
  .lobipanel .panel-heading .dropdown .dropdown-menu > li > a .panel-control-icon {
    top: 0;
    line-height: 30px;
  }
  .lobipanel .panel-heading .dropdown .dropdown-menu > li > a .control-title {
    display: none;
  }
  .lobipanel .panel-heading .dropdown .dropdown-menu > li > a:hover,
  .lobipanel .panel-heading .dropdown .dropdown-menu > li > a:focus:hover {
    text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.6);
    background-color: rgba(0, 0, 0, 0.15);
  }
  .lobipanel .panel-heading .dropdown .dropdown-menu > li > a:focus {
    text-shadow: none;
    background-color: transparent;
  }
  .lobipanel .panel-heading .dropdown .dropdown-toggle {
    display: none;
  }
}
.lobipanel > .panel-body {
  overflow: auto;
  position: relative;
}
.lobipanel .panel-loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  opacity: 0.8;
}
.lobipanel.panel-collapsed > .panel-heading {
  border-bottom: none;
}
.lobipanel.panel-collapsed > .panel-heading .dropdown .dropdown-menu > li > a[data-func="unpin"],
.lobipanel.panel-collapsed > .panel-heading .dropdown .dropdown-menu > li > a[data-func="reload"],
.lobipanel.panel-collapsed > .panel-heading .dropdown .dropdown-menu > li > a[data-func="expand"] {
  display: none;
}
.lobipanel.panel-expanded,
.lobipanel.panel-unpin,
.lobipanel.panel-minimized.panel-unpin {
  margin-bottom: 0;
}
.lobipanel.panel-unpin {
  overflow: hidden;
  position: fixed;
  z-index: 10000;
  -webkit-box-shadow: 2px 2px 15px 5px rgba(10, 10, 10, 0.5);
  box-shadow: 2px 2px 15px 5px rgba(10, 10, 10, 0.5);
}
.lobipanel.panel-unpin > .panel-heading {
  cursor: move;
}
.lobipanel.panel-unpin.panel-minimized {
  float: left;
  position: initial;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  min-width: 170px;
  margin-right: 5px;
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading {
  padding: 0;
  cursor: pointer;
  border-bottom: none;
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading .panel-title {
  max-width: calc(100% - 60px);
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading .panel-title h1,
.lobipanel.panel-unpin.panel-minimized > .panel-heading .panel-title h2,
.lobipanel.panel-unpin.panel-minimized > .panel-heading .panel-title h3,
.lobipanel.panel-unpin.panel-minimized > .panel-heading .panel-title h4,
.lobipanel.panel-unpin.panel-minimized > .panel-heading .panel-title h5,
.lobipanel.panel-unpin.panel-minimized > .panel-heading .panel-title h6 {
  overflow: hidden;
  text-overflow: ellipsis;
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading .dropdown .dropdown-menu {
  position: static;
  display: inline-block;
  border: none;
  padding: 0;
  margin: 0;
  min-width: initial;
  width: auto;
  -webkit-box-shadow: none;
  box-shadow: none;
  background: transparent;
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading .dropdown .dropdown-menu > li {
  display: inline-block;
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading .dropdown .dropdown-menu > li > a {
  cursor: pointer;
  color: inherit;
  padding: 0;
  outline: 0;
  text-align: center;
  width: 30px;
  font-size: 14px;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  transition: all 0.2s;
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading .dropdown .dropdown-menu > li > a .panel-control-icon {
  top: 0;
  line-height: 30px;
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading .dropdown .dropdown-menu > li > a .control-title {
  display: none;
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading .dropdown .dropdown-menu > li > a:hover,
.lobipanel.panel-unpin.panel-minimized > .panel-heading .dropdown .dropdown-menu > li > a:focus:hover {
  text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.6);
  background-color: rgba(0, 0, 0, 0.15);
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading .dropdown .dropdown-menu > li > a:focus {
  text-shadow: none;
  background-color: transparent;
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading .dropdown .dropdown-toggle {
  display: none;
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading .dropdown .dropdown-menu > li > a {
  display: none;
}
.lobipanel.panel-unpin.panel-minimized > .panel-heading .dropdown .dropdown-menu > li > a[data-func="close"],
.lobipanel.panel-unpin.panel-minimized > .panel-heading .dropdown .dropdown-menu > li > a[data-func="expand"] {
  display: inline-block;
}
.lobipanel.panel-unpin.panel-minimized > .panel-body,
.lobipanel.panel-unpin.panel-minimized > .panel-footer {
  display: none !important;
}
.lobipanel.panel-expanded > .panel-heading {
  cursor: initial;
}
.lobipanel.panel-expanded > .panel-heading .dropdown .dropdown-menu > li > a[data-func="unpin"],
.lobipanel.panel-expanded > .panel-heading .dropdown .dropdown-menu > li > a[data-func="minimize"] {
  display: none;
}
.lobipanel.panel-expanded.panel-unpin > .panel-heading .panel-control[data-func="minimize"] {
  display: block;
}
.lobipanel-minimized-toolbar {
  position: fixed;
  height: 36px;
  padding: 2px;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 20000;
  background: rgba(34, 115, 182, 0.51);
}
.lobipanel-placeholder {
  background-color: #f9f5d1;
  border: 1px dashed #919191;
  margin-bottom: 15px;
}
