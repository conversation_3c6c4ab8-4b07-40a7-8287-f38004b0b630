/* ==========================================================================
   Mixins
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   Login
   ========================================================================== */
.sign-box {
  width: 100%;
  max-width: 322px;
  margin: 0 auto;
  background: #fff;
  border: solid 1px #d8e2e7;
  -webkit-border-radius: 5px;
          border-radius: 5px;
  padding: 20px;
  font-size: 1rem /*16*/;
  position: relative;
}
.sign-box .sign-avatar {
  width: 100px;
  height: 100px;
  margin: 0 auto 10px;
}
.sign-box .sign-avatar img {
  display: block;
  width: 100%;
  -webkit-border-radius: 50%;
          border-radius: 50%;
}
.sign-box .sign-avatar.no-photo {
  border: solid 2px #c5d6de;
  text-align: center;
  -webkit-border-radius: 50%;
          border-radius: 50%;
  line-height: 96px;
  color: #c5d6de;
  font-size: 3.125rem /*50/16*/;
  font-weight: 700;
}
.sign-box .sign-title {
  font-size: 1.25rem /*20/16*/;
  text-align: center;
  margin: 0 0 15px;
  line-height: normal;
}
.sign-box .btn {
  display: block;
  min-width: 108px;
  margin: 16px auto 12px;
}
.sign-box .btn.sign-up {
  margin-top: 18px;
}
.sign-box .sign-note {
  text-align: center;
}
.sign-box a {
  text-decoration: none;
  color: #0082c6;
  border-bottom: solid 1px transparent;
}
.sign-box a:hover {
  border-bottom-color: rgba(0, 130, 198, 0.5);
}
.sign-box .form-group {
  margin-bottom: 12px;
  zoom: 1;
}
.sign-box .form-group:before,
.sign-box .form-group:after {
  content: " ";
  display: table;
}
.sign-box .form-group:after {
  clear: both;
}
.sign-box .checkbox {
  margin: 0;
}
.sign-box .checkbox label {
  font-size: 0.875rem /*14/16*/;
}
.sign-box .reset {
  font-size: 0.875rem /*14/16*/;
}
.sign-box .close {
  position: absolute;
  right: 10px;
  top: 4px;
  opacity: 1;
  color: #c5d6de;
}
.sign-box .close:hover {
  color: #00a8ff;
}
.sign-box.reset-password-box .btn {
  display: inline-block !important;
  margin: 10px auto 12px;
  margin-right: 10px;
}
.sign-box.reset-password-box a {
  display: inline-block;
  margin-left: 4px;
}
