<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20150824 at Sat Jan  9 01:07:06 2016
 By Everything Fonts
Copyright (c) <PERSON>, 2005. All rights reserved.
</metadata>
<defs>
<font id="Proxima_Nova_Semibold_Italic" horiz-adv-x="567" >
  <font-face 
    font-family="Proxima_Nova_Semibold_Italic"
    font-weight="600"
    font-style="italic"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 6 3 0 0 2 0 4"
    ascent="790"
    descent="-210"
    x-height="483"
    cap-height="667"
    bbox="-226 -275 1143 894"
    underline-thickness="20"
    underline-position="-153"
    unicode-range="U+000D-FB04"
  />
<missing-glyph horiz-adv-x="493" 
d="M442 -90h-351v842h351v-842zM410 -60v782h-288v-782h288z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="858" 
d="M452 0h-105l87 391h-80l20 92h80l6 27q36 167 171 167q59 0 99 -26l-38 -74q-18 14 -49 14q-60 0 -78 -81l-6 -27h98l-20 -92h-98zM557 -196q-85 0 -126 41l46 75q24 -30 65 -30q63 0 82 82l114 511h105l-114 -511q-18 -82 -59 -125t-113 -43zM810 542q-25 0 -42 16.5
t-17 39.5q0 33 23 53.5t50 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23 -53.5t-50 -20.5zM145 0h-105l86 392h-80l21 91h79l6 27q17 76 60 121.5t116 45.5t118 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="549" 
d="M144 0h-105l87 391h-80l20 92h80l6 27q36 167 171 167q59 0 99 -26l-38 -74q-18 14 -49 14q-60 0 -78 -81l-6 -27h98l-20 -92h-98zM248 -196q-85 0 -126 41l46 75q24 -30 65 -30q63 0 82 82l114 511h105l-114 -511q-18 -82 -59 -125t-113 -43zM501 542q-25 0 -42 16.5
t-17 39.5q0 33 23 53.5t50 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23 -53.5t-50 -20.5z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="617" 
d="M145 0h-105l86 392h-80l21 91h79l6 27q17 76 60 121.5t116 45.5t118 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98zM453 0h-105l86 392h-80l21 91h79l6 27q17 76 60 121.5t116 45.5t118 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5
t-27.5 -61.5l-6 -27h99l-21 -91h-98z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="549" 
d="M144 0h-105l87 391h-80l20 92h80l6 27q36 167 171 167q59 0 99 -26l-38 -74q-18 14 -49 14q-60 0 -78 -81l-6 -27h98l-20 -92h-98zM501 542q-25 0 -42 16.5t-17 39.5q0 33 23 53.5t50 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23 -53.5t-50 -20.5zM427 0h-105l107 483h105z
" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="549" 
d="M144 0h-105l87 391h-80l20 92h80l6 27q36 167 171 167q59 0 99 -26l-38 -74q-18 14 -49 14q-60 0 -78 -81l-6 -27h98l-20 -92h-98zM427 0h-105l147 667h105z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="858" 
d="M452 0h-105l87 391h-80l20 92h80l6 27q36 167 171 167q59 0 99 -26l-38 -74q-18 14 -49 14q-60 0 -78 -81l-6 -27h98l-20 -92h-98zM810 542q-25 0 -42 16.5t-17 39.5q0 33 23 53.5t50 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23 -53.5t-50 -20.5zM736 0h-105l107 483h105z
M145 0h-105l86 392h-80l21 91h79l6 27q17 76 60 121.5t116 45.5t118 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="858" 
d="M452 0h-105l87 391h-80l20 92h80l6 27q36 167 171 167q59 0 99 -26l-38 -74q-18 14 -49 14q-60 0 -78 -81l-6 -27h98l-20 -92h-98zM736 0h-105l147 667h105zM145 0h-105l86 392h-80l21 91h79l6 27q17 76 60 121.5t116 45.5t118 -45l-54 -63q-22 22 -57 22
q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="889" 
d="M144 0h-105l87 391h-80l20 92h80l6 27q36 167 171 167q59 0 99 -26l-38 -74q-18 14 -49 14q-60 0 -78 -81l-6 -27h98l-20 -92h-98zM602 -12q-108 0 -160 81l-15 -69h-105l147 667h105l-55 -248q64 76 152 76q83 0 134 -53t51 -147q0 -120 -70 -213.5t-184 -93.5zM586 81
q69 0 114.5 57.5t45.5 135.5q0 58 -33.5 93t-85.5 35q-74 0 -127 -69l-41 -187q17 -29 50.5 -47t76.5 -18z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="876" 
d="M144 0h-105l87 391h-80l20 92h80l6 27q36 167 171 167q59 0 99 -26l-38 -74q-18 14 -49 14q-60 0 -78 -81l-6 -27h98l-20 -92h-98zM754 0h-105l66 303q5 20 5 30q0 35 -24.5 52t-65.5 17q-65 0 -129 -66l-74 -336h-105l147 667h105l-55 -250q82 78 165 78q68 0 108 -32.5
t40 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="835" 
d="M144 0h-105l87 391h-80l20 92h80l6 27q36 167 171 167q59 0 99 -26l-38 -74q-18 14 -49 14q-60 0 -78 -81l-6 -27h98l-20 -92h-98zM774 0h-127l-106 202l-86 -72l-28 -130h-105l147 667h105l-91 -411l259 227h135l-250 -219z" />
    <glyph glyph-name=".notdef" horiz-adv-x="493" 
d="M442 -90h-351v842h351v-842zM410 -60v782h-288v-782h288z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="CR" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="257" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="245" 
d="M158 204h-91l84 463h128zM75 -11q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="379" 
d="M162 391h-48q13 198 14 216q1 30 20.5 50t47.5 20q21 0 36.5 -15.5t15.5 -38.5q0 -12 -6 -27zM329 391h-48q13 198 14 216q1 30 20.5 50t47.5 20q21 0 36.5 -15.5t15.5 -38.5q0 -12 -6 -27z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="606" 
d="M268 0h-80l97 174h-102l-96 -174h-80l97 174h-101l37 69h102l100 180h-102l36 68h104l97 176h80l-97 -176h101l97 176h80l-98 -176h102l-35 -68h-104l-101 -180h106l-37 -69h-107zM322 243l101 180h-101l-100 -180h100z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="605" 
d="M191 -100l21 97q-137 27 -199 113l75 86q50 -70 148 -95l42 192q-38 15 -64 28t-54 34t-42 50t-14 65q0 84 69.5 145.5t180.5 61.5h10l20 91h80l-23 -101q108 -23 168 -97l-75 -83q-42 51 -116 75l-39 -174q39 -15 65 -28.5t54.5 -35t43 -50.5t14.5 -65q0 -91 -69 -156
t-186 -65h-11l-19 -88h-80zM431 188q0 24 -20 41.5t-59 34.5l-39 -172q58 1 88 30t30 66zM228 489q0 -39 78 -72l35 157h-3q-46 0 -78 -25.5t-32 -59.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="746" 
d="M141 0h-66l573 667h67zM519 -12q-65 0 -110.5 38t-45.5 99q0 85 54 139.5t128 54.5q65 0 110.5 -38t45.5 -99q0 -85 -54 -139.5t-128 -54.5zM521 54q42 0 72 34.5t30 86.5q0 33 -22.5 55t-57.5 22q-42 0 -72 -34t-30 -86q0 -34 22.5 -56t57.5 -22zM239 346q-65 0 -110 38
t-45 99q0 85 53.5 139.5t128.5 54.5q65 0 110 -38t45 -99q0 -85 -53.5 -139.5t-128.5 -54.5zM242 412q42 0 72 34.5t30 86.5q0 33 -23 55t-58 22q-42 0 -72 -34t-30 -86q0 -34 23 -56t58 -22z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="644" 
d="M554 0h-127q-15 18 -34 45q-89 -57 -174 -57q-93 0 -151 43.5t-58 128.5q0 88 51 138t141 85q-19 59 -19 109q0 78 57 131.5t138 53.5q70 0 116.5 -34t46.5 -95q0 -34 -13 -62t-30 -46t-49.5 -36t-54.5 -27t-63 -24q16 -33 43 -80q18 -33 52 -91q62 66 98 133l77 -47
q-62 -93 -128 -157q31 -46 81 -111zM235 72q51 0 111 40q-45 66 -63 99q-30 52 -52 101q-106 -50 -106 -139q0 -49 31.5 -75t78.5 -26zM290 493q0 -33 13 -75q71 25 106.5 51t35.5 69q0 30 -17.5 45.5t-45.5 15.5q-36 0 -64 -28.5t-28 -77.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="214" 
d="M162 391h-48q13 198 14 216q1 30 20.5 50t47.5 20q21 0 36.5 -15.5t15.5 -38.5q0 -12 -6 -27z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="276" 
d="M172 -156l-76 -43q-72 121 -72 299q0 156 70 309.5t196 275.5l55 -55q-211 -264 -211 -562q0 -103 38 -224z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="276" 
d="M103 642l75 43q73 -123 73 -299q0 -156 -70.5 -309.5t-196.5 -275.5l-54 55q211 263 211 561q0 104 -38 225z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="346" 
d="M230 376h-57l31 115l-108 -49l-16 50l110 39l-87 61l38 44l80 -69l19 110h57l-32 -115l108 49l16 -50l-109 -40l87 -60l-39 -44l-80 69z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="502" 
d="M486 303h-185l-46 -207h-74l46 207h-185l15 68h185l44 200h74l-44 -200h185z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="245" 
d="M154 57q0 -52 -38.5 -104t-94.5 -80l-39 41q71 34 92 84h-10q-22 0 -37.5 15t-15.5 41q0 30 23 52.5t53 22.5q28 0 47.5 -19t19.5 -53z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="300" 
d="M259 197h-240l20 90h240z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="245" 
d="M75 -11q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="315" 
d="M21 -20h-81l393 707h82z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="616" 
d="M281 -12q-108 0 -169.5 70t-61.5 195q0 106 39 202t115 159t170 63q108 0 169 -70t61 -194q0 -106 -39 -202.5t-114.5 -159.5t-169.5 -63zM292 92q58 0 104 53t68 126.5t22 147.5q0 71 -30 112.5t-93 41.5q-58 0 -104 -53t-68.5 -126.5t-22.5 -146.5q0 -71 30.5 -113
t93.5 -42z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="380" 
d="M255 0h-117l113 515l-133 -113l-53 71l235 194h102z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="595" 
d="M479 0h-483l24 110q96 56 153.5 91.5t119.5 78.5t94.5 74t53.5 65t21 65q0 41 -36 65.5t-94 24.5q-97 0 -168 -62l-51 83q42 38 102.5 60t124.5 22q103 0 174 -49t71 -137q0 -98 -99 -191.5t-280 -196.5h295z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="574" 
d="M267 -12q-90 0 -167 37.5t-109 101.5l80 69q28 -49 82.5 -76.5t112.5 -27.5q64 0 100 31t36 81q0 38 -33 61t-100 23q-63 0 -74 -1l24 105q11 -1 92 -1q63 0 103 23.5t40 71.5q0 39 -38 63t-102 24q-94 0 -165 -59l-44 80q41 38 98.5 60.5t120.5 22.5q113 0 181.5 -45.5
t68.5 -126.5q0 -67 -55 -113.5t-125 -53.5q54 -13 88.5 -49.5t34.5 -95.5q0 -87 -72 -146t-178 -59z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="580" 
d="M409 0h-117l33 151h-318l23 103l358 413h168l-92 -413h89l-22 -103h-89zM347 254l69 308l-272 -308h203z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="600" 
d="M288 -12q-184 0 -263 130l85 73q53 -99 181 -99q63 0 103.5 37.5t40.5 92.5q0 50 -38 78t-99 28q-70 0 -129 -44l-76 33l77 350h437l-23 -103h-320l-40 -184q56 44 136 44q84 0 140.5 -50.5t56.5 -138.5q0 -103 -75.5 -175t-193.5 -72z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="601" 
d="M293 -12q-117 0 -182 66.5t-65 186.5q0 78 22.5 154t64 139.5t109 103t149.5 39.5q140 0 214 -95l-74 -81q-47 72 -147 72q-77 0 -129 -59t-75 -142q-5 -11 -7 -24q29 31 78.5 54t102.5 23q90 0 148.5 -49t58.5 -131q0 -105 -78.5 -181t-189.5 -76zM294 92
q62 0 103.5 40.5t41.5 97.5q0 46 -38.5 73t-98.5 27q-79 0 -141 -64q-2 -16 -2 -40q0 -61 37 -97.5t98 -36.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="532" 
d="M197 0h-134l374 564h-340l23 103h481l-18 -81z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="595" 
d="M279 -12q-110 0 -184 45t-74 130q0 69 51.5 120.5t140.5 72.5q-45 19 -76 55t-31 82q0 89 76 136.5t175 47.5q100 0 173.5 -44.5t73.5 -123.5q0 -68 -49.5 -114.5t-130.5 -61.5q117 -59 117 -154q0 -86 -77 -138.5t-185 -52.5zM330 390q67 5 107.5 30.5t40.5 67.5
q0 36 -37 60t-91 24q-53 0 -90 -25t-37 -67q0 -31 33.5 -56t73.5 -34zM284 92q55 0 97 27.5t42 71.5q0 36 -35 63t-80 38q-70 -4 -116.5 -33.5t-46.5 -74.5q0 -44 38.5 -68t100.5 -24z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="601" 
d="M346 678q117 0 182 -66.5t65 -186.5q0 -62 -13.5 -123.5t-42.5 -118t-69 -100t-97.5 -69t-123.5 -25.5q-140 0 -213 94l74 82q44 -72 147 -72q77 0 128.5 58t75.5 142q6 18 7 24q-30 -31 -79.5 -54t-101.5 -23q-90 0 -149 49.5t-59 131.5q0 105 79 181t190 76zM345 574
q-62 0 -104 -41t-42 -97q0 -46 38.5 -73.5t98.5 -27.5q78 0 142 64q1 8 1 40q0 61 -36.5 98t-97.5 37z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="245" 
d="M75 -11q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22zM154 350q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="245" 
d="M154 57q0 -52 -38.5 -104t-94.5 -80l-39 41q71 34 92 84h-10q-22 0 -37.5 15t-15.5 41q0 30 23 52.5t53 22.5q28 0 47.5 -19t19.5 -53zM154 350q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="502" 
d="M438 90l-398 207l17 75l490 208l-19 -84l-401 -166l328 -163z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="502" 
d="M506 395h-444l15 68h444zM464 205h-444l15 67h444z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="502" 
d="M484 297l-490 -207l19 84l401 163l-328 166l17 77l398 -208z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="458" 
d="M273 225l-95 -29q-20 31 -20 70q0 40 22 70.5t53 49t62.5 34.5t53.5 37.5t22 48.5q0 31 -26 49.5t-73 18.5q-77 0 -135 -55l-51 80q88 78 202 78q89 0 148 -40t59 -107q0 -49 -24.5 -85.5t-59 -57.5t-69 -38t-59 -39.5t-24.5 -51.5q0 -19 14 -33zM177 -11q-26 0 -44.5 19
t-18.5 45q0 31 22.5 53.5t53.5 22.5q27 0 45.5 -19t18.5 -46q0 -31 -22.5 -53t-54.5 -22z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="783" 
d="M372 -70q-136 0 -226.5 90.5t-90.5 223.5q0 159 120 276.5t277 117.5q139 0 227 -90t88 -221q0 -114 -58 -178t-129 -64q-42 0 -66.5 22t-28.5 55l-1 6q-27 -37 -67 -60t-83 -23q-70 0 -112 46t-42 120q0 100 70.5 173.5t159.5 73.5q90 0 126 -71l12 56h94l-54 -255
q-2 -12 -2 -23q0 -23 11.5 -35.5t29.5 -12.5q36 0 68 43t32 127q0 122 -76.5 198t-202.5 76q-143 0 -249.5 -107t-106.5 -248q0 -120 80.5 -200t203.5 -80q97 0 184 55l20 -28q-98 -63 -208 -63zM368 160q73 0 127 76l27 126q-10 24 -33 42.5t-58 18.5q-64 0 -109 -50.5
t-45 -114.5q0 -44 24.5 -71t66.5 -27z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="648" 
d="M357 0h-340l147 667h304q79 0 127.5 -45t48.5 -112q0 -68 -45.5 -119t-107.5 -57q44 -12 70.5 -51t26.5 -85q0 -80 -58.5 -139t-172.5 -59zM411 391q56 0 82 29t26 70q0 31 -24 52.5t-58 21.5h-179l-38 -173h191zM351 103q55 0 87 30.5t32 74.5q0 36 -23.5 58t-64.5 22
h-185l-41 -185h195z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="682" 
d="M370 -12q-138 0 -228.5 81.5t-90.5 214.5q0 173 113 283.5t269 110.5q107 0 179.5 -48t106.5 -127l-112 -40q-23 54 -70.5 82.5t-109.5 28.5q-103 0 -178.5 -81t-75.5 -202q0 -88 55.5 -143.5t148.5 -55.5q96 0 166 76l89 -62q-52 -62 -121.5 -90t-140.5 -28z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="710" 
d="M264 0h-247l147 667h243q115 0 199.5 -81t84.5 -205q0 -55 -15 -108t-48.5 -103t-82 -87.5t-121 -60t-160.5 -22.5zM281 103h4q128 0 206 79t78 192q0 81 -52 135.5t-132 54.5h-127l-102 -461h125z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="562" 
d="M134 0h-117l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="717" 
d="M370 -12q-138 0 -228.5 82t-90.5 214q0 170 112.5 282t279.5 112q96 0 165.5 -44t104.5 -113l-107 -47q-21 46 -70 73t-109 27q-103 0 -178.5 -81.5t-75.5 -201.5q0 -88 55.5 -143.5t148.5 -55.5q83 0 151 59l23 104h-199l23 103h316l-56 -253q-106 -117 -265 -117z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="723" 
d="M598 0h-117l64 291h-347l-64 -291h-117l147 667h117l-60 -273h347l60 273h117z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="259" 
d="M134 0h-117l147 667h117z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="481" 
d="M162 -12q-128 0 -188 79l65 86q38 -61 115 -61q105 0 132 121l100 454h117l-100 -456q-25 -114 -84.5 -168.5t-156.5 -54.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="621" 
d="M560 0h-137l-165 286l-77 -73l-47 -213h-117l147 667h117l-68 -308l322 308h152l-340 -315z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="517" 
d="M428 0h-411l147 667h117l-125 -564h294z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="835" 
d="M710 0h-117l113 515l-318 -515h-50l-91 515l-113 -515h-117l147 667h160l80 -459l283 459h170z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="720" 
d="M595 0h-113l-239 494l-109 -494h-117l147 667h120l234 -481l107 481h117z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="608" 
d="M134 0h-117l147 667h268q93 0 145.5 -51.5t52.5 -128.5q0 -32 -8.5 -63.5t-28.5 -63.5t-49.5 -56t-76 -39.5t-104.5 -15.5h-174zM211 352h160q63 0 100 35.5t37 88.5q0 37 -27 62.5t-68 25.5h-155z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -96 -40.5 -179t-110.5 -138l38 -56l-94 -47l-34 52q-70 -27 -141 -27zM377 92q39 0 75 12l-67 102l93 47l63 -95q89 84 89 217q0 91 -58 145t-146 54
q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="628" 
d="M533 0h-129l-93 249h-123l-54 -249h-117l147 667h268q81 0 140 -50t59 -132q0 -91 -56.5 -154t-145.5 -75zM370 352h2q63 0 99.5 34.5t36.5 87.5q0 39 -29 64.5t-68 25.5h-153l-47 -212h159z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="594" 
d="M294 -12q-93 0 -170.5 34t-117.5 88l75 86q35 -48 94.5 -76t128.5 -28q59 0 89.5 29t30.5 67q0 30 -33.5 52t-82 39.5t-96.5 38.5t-81.5 60t-33.5 92q0 84 69.5 145.5t180.5 61.5q79 0 147 -28.5t108 -78.5l-75 -83q-34 42 -87 64.5t-109 22.5q-46 0 -78 -25.5t-32 -59.5
q0 -27 34 -48.5t82 -39t96 -39t82 -60.5t34 -93q0 -91 -69 -156t-186 -65z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="577" 
d="M293 0h-117l124 564h-202l23 103h521l-23 -103h-202z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="719" 
d="M347 -12q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-29 -133 -100 -204.5t-205 -71.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="673" 
d="M355 0h-146l-115 667h127l83 -540l321 540h139z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="903" 
d="M652 0h-125l-21 492l-238 -492h-125l-44 667h128l16 -514l252 514h93l24 -514l243 514h134z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="661" 
d="M603 0h-132l-135 256l-239 -256h-148l328 349l-166 318h133l123 -240l220 240h148l-309 -332z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="637" 
d="M323 0h-117l61 277l-173 390h127l121 -286l245 286h141l-344 -390z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="589" 
d="M490 0h-500l21 95l437 469h-333l22 103h493l-20 -95l-439 -469h342z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="264" 
d="M150 -190h-203l193 868h203l-16 -72h-126l-160 -724h125z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="315" 
d="M178 -20l-80 707h78l80 -707h-78z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="264" 
d="M123 -190h-202l16 72h125l161 724h-126l16 72h203z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="437" 
d="M437 333h-76l-62 261l-177 -261h-84l235 334h78z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="564" 
d="M487 -112h-569l17 72h569z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="240" 
d="M309 556h-75l-133 144h100z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="581" 
d="M294 -12q-108 0 -160 81l-15 -69h-105l147 667h105l-55 -248q64 76 152 76q83 0 134 -53t51 -147q0 -120 -70 -213.5t-184 -93.5zM278 81q69 0 114.5 57.5t45.5 135.5q0 58 -33.5 93t-85.5 35q-74 0 -127 -69l-41 -187q17 -29 50.5 -47t76.5 -18z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="497" 
d="M264 -12q-104 0 -167.5 59.5t-63.5 158.5q0 120 80.5 204.5t196.5 84.5q134 0 190 -93l-79 -60q-35 60 -110 60q-76 0 -123.5 -56.5t-47.5 -135.5q0 -61 37.5 -95t96.5 -34q65 0 110 52l58 -70q-71 -75 -178 -75z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l56 253h105l-148 -667h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="557" 
d="M277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5
q-56 0 -98.5 -38t-52.5 -90z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="308" 
d="M145 0h-105l86 392h-80l21 91h79l6 27q17 76 60 121.5t116 45.5t118 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="580" 
d="M212 -196q-152 0 -226 83l63 70q48 -67 161 -67q118 0 148 135l12 52q-62 -77 -155 -77q-80 0 -131.5 49.5t-51.5 148.5q0 118 69 207.5t182 89.5q46 0 90.5 -22.5t70.5 -58.5l16 69h105l-102 -458q-26 -120 -94 -170.5t-157 -50.5zM262 93q35 0 68.5 18.5t57.5 46.5
l39 175q-19 32 -54 50.5t-78 18.5q-67 0 -110.5 -55t-43.5 -130q0 -57 33 -90.5t88 -33.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="568" 
d="M446 0h-105l66 303q5 20 5 30q0 35 -24.5 52t-65.5 17q-65 0 -129 -66l-74 -336h-105l147 667h105l-55 -250q82 78 165 78q68 0 108 -32.5t40 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="241" 
d="M193 542q-25 0 -42 16.5t-17 39.5q0 33 23 53.5t50 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23 -53.5t-50 -20.5zM119 0h-105l107 483h105z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="241" 
d="M-60 -196q-85 0 -126 41l46 75q24 -30 65 -30q63 0 82 82l114 511h105l-114 -511q-18 -82 -59 -125t-113 -43zM193 542q-25 0 -42 16.5t-17 39.5q0 33 23 53.5t50 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23 -53.5t-50 -20.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="526" 
d="M466 0h-127l-106 202l-86 -72l-28 -130h-105l147 667h105l-91 -411l259 227h135l-250 -219z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="241" 
d="M119 0h-105l147 667h105z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="833" 
d="M711 0h-105l68 308q4 20 4 30q0 29 -22 46.5t-53 17.5q-57 0 -114 -65l-74 -337h-105l68 308q4 22 4 35q-1 25 -21 42t-55 17q-54 0 -113 -65l-74 -337h-105l107 483h105l-15 -66q70 78 149 78q60 0 94 -31t34 -54v-2q70 87 169 87q55 0 94 -32.5t39 -89.5q0 -15 -5 -40z
" />
    <glyph glyph-name="n" unicode="n" 
d="M445 0h-105l67 303q5 22 5 30q0 35 -23 52t-58 17q-73 0 -138 -65l-74 -337h-105l107 483h105l-15 -66q82 78 165 78q68 0 108 -32.5t40 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="573" 
d="M264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="578" 
d="M294 -12q-108 0 -160 81l-56 -253h-105l148 667h105l-15 -64q64 76 152 76q83 0 134 -53t51 -147q0 -120 -70 -213.5t-184 -93.5zM278 81q69 0 114.5 57.5t45.5 135.5q0 58 -33.5 93t-85.5 35q-74 0 -127 -69l-41 -187q17 -29 50.5 -47t76.5 -18z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="578" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-148 -667h-105l55 248q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="347" 
d="M119 0h-105l107 483h105l-16 -68q71 79 175 79l-23 -105q-17 5 -43 5q-36 0 -70.5 -19t-57.5 -48z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="472" 
d="M214 -12q-141 0 -218 86l62 73q22 -30 68.5 -53t96.5 -23q39 0 63 19t24 46q0 25 -39 44.5t-85.5 32.5t-85.5 45t-39 80q0 65 51.5 111t140.5 46q60 0 112.5 -22t83.5 -57l-56 -69q-18 25 -59 44.5t-84 19.5q-39 0 -62 -16t-23 -41q0 -19 25.5 -33.5t62.5 -27t74 -27.5
t62.5 -44t25.5 -69q0 -70 -54.5 -117.5t-146.5 -47.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="319" 
d="M187 -12q-60 0 -94.5 24.5t-34.5 73.5q0 14 3 31l61 275h-80l21 91h80l29 132h105l-29 -132h98l-21 -91h-98l-55 -249q-2 -10 -2 -19q0 -43 48 -43q25 0 40 13l6 -84q-28 -22 -77 -22z" />
    <glyph glyph-name="u" unicode="u" 
d="M121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52t65.5 -17q63 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="503" 
d="M254 0h-113l-90 483h109l60 -362l222 362h116z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="754" 
d="M545 0h-110l-34 354l-190 -354h-110l-43 483h105l24 -351l193 351h93l36 -351l180 351h113z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="496" 
d="M437 0h-114l-86 178l-165 -178h-121l231 248l-114 235h114l78 -164l151 164h122l-220 -235z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="503" 
d="M-47 -185l35 92q18 -8 48 -8q38 0 65 40l38 58l-88 486h109l60 -362l222 362h116l-360 -570q-37 -58 -77 -83.5t-95 -25.5q-42 0 -73 11z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="476" 
d="M375 0h-383l17 79l304 312h-234l20 92h379l-17 -77l-307 -315h241z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="276" 
d="M162 -190h-46q-52 0 -89.5 33.5t-37.5 85.5q0 15 3 29l42 187q2 12 2 17q0 22 -10.5 36.5t-28.5 14.5l14 62q52 0 67 68l41 186q17 74 62.5 111.5t112.5 37.5h61l-16 -72h-61q-64 0 -81 -77l-46 -204q-16 -73 -65 -89q29 -15 29 -55q0 -18 -4 -34l-41 -188q-2 -12 -2 -20
q0 -25 16 -41t40 -16h55z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="214" 
d="M163 -20h-72v707h72v-707z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="276" 
d="M113 678h46q52 0 89.5 -33.5t37.5 -85.5q0 -17 -3 -30l-42 -186q-2 -12 -2 -18q0 -22 10.5 -36t28.5 -14l-14 -62q-52 0 -67 -68l-41 -186q-34 -149 -175 -149h-61l16 72h61q64 0 81 76l46 205q16 73 65 89q-29 15 -29 55q0 18 4 34l41 188q2 12 2 19q0 25 -16 41.5
t-40 16.5h-55z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="507" 
d="M500 667l72 -9q-54 -241 -185 -241q-39 0 -61.5 18.5t-29 45.5t-10 54t-12.5 45.5t-28 18.5q-32 0 -61.5 -52.5t-46.5 -130.5l-72 9q51 241 184 241q39 0 61.5 -19t29 -46t10 -53.5t12.5 -45.5t29 -19q32 0 62 53t46 131z" />
    <glyph glyph-name="nbspace" unicode="&#xa0;" horiz-adv-x="257" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="245" 
d="M84 278h91l-84 -462h-128zM167 494q27 0 45.5 -19t18.5 -46q0 -31 -22.5 -53t-54.5 -22q-26 0 -44.5 19t-18.5 45q0 31 22.5 53.5t53.5 22.5z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="497" 
d="M168 -100l22 97q-73 19 -115 74t-42 135q0 117 77 201t190 88l16 70h78l-17 -77q83 -20 123 -86l-79 -60q-23 38 -65 52l-70 -313q58 6 98 52l58 -70q-70 -74 -176 -75l-20 -88h-78zM140 210q0 -86 71 -117l68 306q-63 -12 -101 -65.5t-38 -123.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="536" 
d="M26 268l16 70h93q-2 3 -8 15t-8.5 16.5t-7.5 15.5t-7.5 18t-5.5 17t-4 20t-1 21q0 88 74.5 152t179.5 64q81 0 143 -34.5t80 -93.5l-106 -44q-7 34 -38.5 55t-73.5 21q-55 0 -93.5 -37t-38.5 -95q0 -21 5 -38t18 -42t16 -31h144l-16 -70h-116q-3 -45 -33.5 -86.5
t-66.5 -61.5q24 7 45 7q31 0 77.5 -18t73.5 -18q49 0 85 38l27 -93q-51 -49 -121 -49q-46 0 -114 22.5t-101 22.5q-47 0 -113 -39l-22 82q66 28 109.5 76.5t43.5 100.5q0 2 -0.5 8t-0.5 8h-134z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="612" 
d="M308 91q-89 0 -151 44l-98 -80l-56 70l94 76q-24 46 -24 101q0 110 74 187l-56 73l71 57l58 -75q58 30 126 30q92 0 152 -46l97 79l55 -67l-95 -78q25 -47 25 -101q0 -108 -75 -186l56 -69l-71 -57l-57 72q-58 -30 -125 -30zM181 309q0 -55 35.5 -89.5t96.5 -34.5
q68 0 114 50t46 118q0 55 -35 90.5t-100 35.5q-68 0 -112.5 -51t-44.5 -119z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="637" 
d="M325 0h-117l26 118h-240l15 67h239l21 92h-239l15 67h194l-143 323h127l121 -286l245 286h141l-285 -323h192l-15 -67h-236l-21 -92h236l-15 -67h-235z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="214" 
d="M163 -20h-72v316h72v-316zM163 371h-72v316h72v-316z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="477" 
d="M453 328q0 -49 -31.5 -88.5t-87.5 -57.5q64 -35 64 -101q0 -68 -54.5 -115t-142.5 -47q-149 0 -228 82l61 63q26 -32 71.5 -51.5t94.5 -19.5q41 0 67 21t26 52q0 24 -25 41.5t-61 30t-72.5 27.5t-61.5 43.5t-25 69.5q0 50 34.5 87t100.5 52q-81 35 -81 108q0 64 53 108
t138 44q136 0 205 -74l-57 -58q-47 57 -144 57q-42 0 -66.5 -18.5t-24.5 -46.5q0 -24 25.5 -40.5t62 -28.5t72.5 -26.5t61.5 -43.5t25.5 -70zM345 305q0 49 -80 76q-56 -7 -84 -30t-28 -54q0 -29 25 -46.5t77 -34.5q46 13 68 37t22 52z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="285" 
d="M386 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM177 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="778" 
d="M754 334q0 -143 -101.5 -244t-244.5 -101q-142 0 -243 101t-101 244t100.5 244t243.5 101t244.5 -101t101.5 -244zM720 334q0 128 -92 219.5t-220 91.5t-219 -91.5t-91 -219.5t91 -219.5t219 -91.5t220 91.5t92 219.5zM506 207l22 -32q-64 -53 -137 -53q-87 0 -139 52.5
t-52 132.5q0 100 71 169.5t163 69.5q109 0 158 -80l-35 -25q-36 68 -125 68q-72 0 -131 -58t-59 -143q0 -62 41 -105.5t109 -43.5q64 0 114 48z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="412" 
d="M268 586q-44 0 -75 -37.5t-31 -88.5q0 -39 21 -59.5t55 -20.5q45 0 81 40l29 131q-24 35 -80 35zM367 640h78l-70 -314h-78l9 42q-43 -50 -100 -50q-53 0 -89 34.5t-36 98.5q0 81 49 139t120 58q73 0 108 -50z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="489" 
d="M244 63h-95l-120 180l200 177h103l-204 -182zM414 63h-95l-120 180l200 177h103l-204 -182z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="510" 
d="M521 463l-57 -258h-70l42 190h-374l15 68h444z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="300" 
d="M259 197h-240l20 90h240z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="494" 
d="M508 465q0 -88 -62.5 -150t-150.5 -62t-149.5 62t-61.5 150t62 150t150 62t150 -61.5t62 -150.5zM477 465q0 75 -53.5 128t-128.5 53q-76 0 -128.5 -52.5t-52.5 -128.5q0 -75 53 -128.5t128 -53.5t128.5 53.5t53.5 128.5zM368 343h-39l-42 96h-45l-21 -96h-33l53 243h92
q30 0 50.5 -18t20.5 -46q0 -35 -25 -58.5t-55 -23.5zM369 519q0 17 -11.5 27.5t-30.5 10.5h-59l-20 -88h66q24 0 39.5 14.5t15.5 35.5z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="363" 
d="M433 566h-362l14 62h362z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="302" 
d="M347 547q0 -54 -38 -91.5t-92 -37.5q-53 0 -91 37.5t-38 91.5q0 53 38 91.5t91 38.5q54 0 92 -38.5t38 -91.5zM287 547q0 29 -20.5 49.5t-49.5 20.5q-28 0 -48 -20.5t-20 -49.5t19.5 -49t47.5 -20q29 0 50 20.5t21 48.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="502" 
d="M490 325h-185l-46 -208h-74l46 208h-185l15 67h185l45 201h74l-45 -201h185zM419 0h-444l14 67h444z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="393" 
d="M391 421h-304l14 63q155 91 212 136t57 87q0 26 -21.5 40.5t-53.5 14.5q-69 0 -112 -45l-35 54q59 56 150 56q65 0 110 -29.5t45 -83.5q0 -56 -58 -108.5t-175 -119.5h185z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="393" 
d="M419 534q0 -47 -42 -83.5t-111 -36.5q-59 0 -104 19.5t-67 53.5l51 48q40 -56 116 -56q36 0 56 18t20 46q0 23 -25 36.5t-68 13.5q-26 0 -32 -1l14 65q24 -2 63 -2q38 0 59 15t21 41q0 24 -24.5 37.5t-60.5 13.5q-59 0 -105 -39l-29 51q62 53 141 53q71 0 115.5 -27.5
t44.5 -77.5q0 -41 -30 -69.5t-81 -32.5q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="240" 
d="M341 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="579" 
d="M122 24l-47 -208h-105l151 667h105l-68 -303q-11 -49 10.5 -74.5t64.5 -25.5q37 0 74 18.5t63 45.5l75 339h105l-78 -352q-5 -23 3.5 -36.5t28.5 -13.5q10 0 20 2l-13 -89q-21 -6 -52 -6q-97 0 -99 86q-33 -38 -76.5 -62t-86.5 -24q-57 0 -75 36z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="449" 
d="M315 -100h-55l158 712h-83l-158 -712h-55l94 423q-57 0 -97 44.5t-40 111.5q0 73 56.5 130.5t137.5 57.5h212z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="245" 
d="M116 175q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="214" 
d="M25 -194q-74 0 -114 40l31 43q35 -35 84 -35q26 0 40.5 11t14.5 29q0 26 -33 26q-18 0 -32 -15l-35 23l49 83h55l-40 -64q15 11 32 11q26 0 42.5 -16.5t16.5 -44.5q0 -41 -31 -66t-80 -25z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="264" 
d="M243 421h-81l64 292l-80 -66l-37 50l152 124h70z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="405" 
d="M236 318q-71 0 -114 40t-43 104q0 78 55 132t132 54q71 0 114 -40.5t43 -104.5q0 -77 -55.5 -131t-131.5 -54zM238 381q48 0 76.5 37t28.5 86q0 36 -21.5 57.5t-57.5 21.5q-48 0 -76.5 -37t-28.5 -86q0 -36 21.5 -57.5t57.5 -21.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="489" 
d="M77 420h95l120 -180l-200 -177h-103l204 182zM247 420h95l120 -180l-200 -177h-103l204 182z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="806" 
d="M209 267h-81l64 292l-80 -66l-37 50l152 124h70zM742 92h-55l-20 -92h-80l20 92h-196l12 56l220 252h112l-54 -244h55zM621 156l38 171l-153 -171h115zM712 667l-574 -667h-67l573 667h68z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="841" 
d="M209 267h-81l64 292l-80 -66l-37 50l152 124h70zM745 0h-304l14 63q155 91 212 136t57 87q0 26 -21.5 40.5t-53.5 14.5q-69 0 -112 -45l-35 54q59 56 150 56q65 0 110 -29.5t45 -83.5q0 -56 -58 -108.5t-175 -119.5h185zM712 667l-574 -667h-67l573 667h68z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="902" 
d="M839 92h-55l-20 -92h-80l20 92h-196l12 56l220 252h112l-54 -244h55zM718 156l38 171l-153 -171h115zM808 667l-574 -667h-67l573 667h68zM385 380q0 -47 -42 -83.5t-111 -36.5q-59 0 -104 19.5t-67 53.5l51 48q40 -56 116 -56q36 0 56 18t20 46q0 23 -25 36.5t-68 13.5
q-26 0 -32 -1l14 65q24 -2 63 -2q38 0 59 15t21 41q0 24 -24.5 37.5t-60.5 13.5q-59 0 -105 -39l-29 51q62 53 141 53q71 0 115.5 -27.5t44.5 -77.5q0 -41 -30 -69.5t-81 -32.5q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="396" 
d="M184 257l95 29q19 -29 19 -70q0 -40 -22 -70.5t-53 -49t-62 -34.5t-53 -37.5t-22 -48.5q0 -31 25.5 -49.5t73.5 -18.5q77 0 135 55l50 -80q-88 -78 -202 -78q-89 0 -148 40t-59 107q0 49 24.5 85.5t59 57.5t69 38t59 39.5t24.5 51.5q0 19 -13 33zM279 493q27 0 45.5 -19
t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM502 723h-75l-133 144h100z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM652 867l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="673" 
d="M583 723h-65l-56 96l-96 -96h-68l125 144h96zM617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM511 721q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="673" 
d="M616 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -14.5t15 -37.5zM407 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -14.5t14.5 -37.5zM617 0h-127l-21 128h-306l-77 -128h-139l409 667
h146zM459 231l-50 318l-190 -318h240z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM447 689q-41 0 -67 26t-26 67q0 45 33.5 78.5t78.5 33.5q41 0 67 -26t26 -67q0 -46 -33 -79t-79 -33zM451 740q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -17t-17 -39
q0 -21 13 -34t33 -13z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="959" 
d="M858 0h-457l28 128h-243l-105 -128h-137l557 667h504l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM452 231l68 312l-255 -312h187z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="682" 
d="M304 -194q-74 0 -114 40l31 43q35 -35 84 -35q26 0 40.5 11t14.5 29q0 26 -33 26q-19 0 -32 -15l-35 23l39 67q-111 20 -179.5 97t-68.5 192q0 173 113 283.5t269 110.5q107 0 179.5 -48t106.5 -127l-112 -40q-23 54 -70.5 82.5t-109.5 28.5q-103 0 -178.5 -81
t-75.5 -202q0 -88 55.5 -143.5t148.5 -55.5q96 0 166 76l89 -62q-52 -62 -121.5 -90t-140.5 -28q-14 0 -20 1l-26 -42q15 11 32 11q26 0 42.5 -16.5t16.5 -44.5q0 -41 -31 -66t-80 -25z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM467 723h-75l-133 144h100z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="577" 
d="M616 867l-197 -144h-77l172 144h102zM474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="577" 
d="M547 723h-65l-56 96l-96 -96h-68l125 144h96zM474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="577" 
d="M580 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -14.5t15 -37.5zM371 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -14.5t14.5 -37.5zM474 0h-457l147 667h457l-23 -103h-340l-38 -173
h333l-23 -103h-333l-41 -185h340z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM296 723h-75l-133 144h100z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM446 867l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="259" 
d="M377 723h-65l-56 96l-96 -96h-68l125 144h96zM134 0h-117l147 667h117z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="259" 
d="M410 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -14.5t15 -37.5zM201 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -14.5t14.5 -37.5zM134 0h-117l147 667h117z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="736" 
d="M290 0h-247l63 286h-88l21 95h88l63 286h243q115 0 199.5 -81t84.5 -205q0 -55 -15 -108t-48.5 -103t-82 -87.5t-121 -60t-160.5 -22.5zM313 103q127 1 204.5 79.5t77.5 191.5q0 81 -52 135.5t-132 54.5h-127l-41 -183h158l-21 -95h-158l-40 -183h131z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="720" 
d="M536 721q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134zM595 0h-113l-239 494l-109 -494h-117l147 667h120l234 -481l107 481h117z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="765" 
d="M551 723h-75l-133 144h100zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5
q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="765" 
d="M700 867l-197 -144h-77l172 144h102zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5
q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="765" 
d="M632 723h-65l-56 96l-96 -96h-68l125 144h96zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5
t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z
M559 721q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="765" 
d="M663 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -14.5t14.5 -37.5zM454 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -14.5t15 -37.5zM370 -12q-138 0 -228.5 80.5t-90.5 215.5
q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="502" 
d="M375 144l-111 142l-178 -146l-42 51l180 147l-111 142l54 44l109 -142l179 146l42 -52l-179 -147l110 -142z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="765" 
d="M370 -12q-119 0 -202 59l-44 -47h-87l84 89q-70 78 -70 195q0 165 110.5 279.5t271.5 114.5q111 0 193 -54l41 43h87l-79 -84q77 -80 77 -200q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 70 -37 121l-345 -365q52 -39 129 -39zM173 291
q0 -67 31 -114l343 363q-53 34 -121 34q-105 0 -179 -81.5t-74 -201.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="719" 
d="M347 -12q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-29 -133 -100 -204.5t-205 -71.5zM526 723h-75l-133 144h100z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="719" 
d="M676 867l-197 -144h-77l172 144h102zM347 -12q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-29 -133 -100 -204.5t-205 -71.5z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="719" 
d="M607 723h-65l-56 96l-96 -96h-68l125 144h96zM347 -12q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-29 -133 -100 -204.5t-205 -71.5z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="719" 
d="M639 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -14.5t14.5 -37.5zM430 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -14.5t15 -37.5zM347 -12q-133 0 -205 61.5t-72 166.5q0 22 5 47
l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-29 -133 -100 -204.5t-205 -71.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="637" 
d="M323 0h-117l61 277l-173 390h127l121 -286l245 286h141l-344 -390zM635 867l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="608" 
d="M134 0h-117l147 667h117l-25 -113h168q77 0 129 -52.5t52 -126.5q0 -93 -67 -165.5t-199 -72.5h-175zM346 240q64 0 100.5 36t36.5 87q0 38 -27.5 63t-67.5 25h-155l-46 -211h159z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="623" 
d="M588 151q0 -66 -54.5 -114.5t-142.5 -48.5q-70 0 -119 21t-91 68l61 67q58 -72 152 -72q41 0 65 20t24 47q0 24 -35.5 41.5t-78.5 29t-78.5 41t-35.5 75.5q0 38 18 66.5t44 45t52 29.5t44 27.5t18 32.5q0 26 -26 41.5t-63 15.5q-44 0 -74.5 -26t-41.5 -73l-107 -485h-105
l107 485q19 85 76.5 138.5t148.5 53.5q76 0 136 -37t60 -95q0 -45 -28.5 -76.5t-63 -46t-63 -35t-28.5 -45.5q0 -19 23.5 -32.5t57.5 -24.5t67.5 -25t57 -42t23.5 -67z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM416 556h-75l-133 144h100z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM565 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM498 556h-65l-56 96l-96 -96h-68l125 144h96z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM426 554q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM530 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM321 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM364 536q-41 0 -67 26t-26 67q0 45 33.5 78.5t78.5 33.5q41 0 67 -26t26 -67q0 -46 -33 -79t-79 -33zM368 587q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -17t-17 -39q0 -21 13 -34t33 -13z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="924" 
d="M302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187q-17 29 -50.5 47.5t-76.5 18.5zM480 95l-21 -95h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46q51 0 93.5 -21.5
t66.5 -59.5l15 69h105l-15 -70q66 82 160 82q87 0 134 -54.5t47 -152.5q0 -44 -10 -81h-372q-1 -3 -1 -17q0 -45 38.5 -79.5t107.5 -34.5q76 0 126 41l34 -76q-76 -53 -156 -53q-136 0 -179 107zM520 281h274q1 3 1 14q0 50 -31.5 82t-91.5 32q-56 0 -99 -38t-53 -90z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="497" 
d="M209 -194q-74 0 -114 40l31 43q35 -35 84 -35q26 0 40.5 11t14.5 29q0 26 -33 26q-18 0 -32 -15l-35 23l39 66q-79 16 -125 72t-46 140q0 120 80.5 204.5t196.5 84.5q134 0 190 -93l-79 -60q-35 60 -110 60q-76 0 -123.5 -56.5t-47.5 -135.5q0 -61 37.5 -95t96.5 -34
q65 0 110 52l58 -70q-71 -75 -178 -75h-10l-25 -41q15 11 32 11q26 0 42.5 -16.5t16.5 -44.5q0 -41 -31 -66t-80 -25z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="557" 
d="M277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5
q-56 0 -98.5 -38t-52.5 -90zM414 556h-75l-133 144h100z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="557" 
d="M565 700l-197 -144h-77l172 144h102zM277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14
q0 49 -33 81.5t-94 32.5q-56 0 -98.5 -38t-52.5 -90z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="557" 
d="M494 556h-65l-56 96l-96 -96h-68l125 144h96zM277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277
q1 3 1 14q0 49 -33 81.5t-94 32.5q-56 0 -98.5 -38t-52.5 -90z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="557" 
d="M529 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM320 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5
t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5q-56 0 -98.5 -38t-52.5 -90z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="241" 
d="M119 0h-105l107 483h105zM250 556h-75l-133 144h100z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="241" 
d="M401 700l-197 -144h-77l172 144h102zM119 0h-105l107 483h105z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="241" 
d="M332 556h-65l-56 96l-96 -96h-68l125 144h96zM119 0h-105l107 483h105z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="241" 
d="M365 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM156 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM119 0h-105l107 483h105z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="573" 
d="M164 496l-15 50l130 38q-38 32 -59 48l74 77q57 -43 102 -90l110 33l15 -50l-87 -26q109 -132 109 -272q0 -133 -76.5 -224.5t-199.5 -91.5q-106 0 -171 57.5t-65 150.5q0 114 71.5 193t174.5 79q55 0 98.5 -27t66.5 -81q-24 88 -119 183zM268 81q66 0 111.5 51t45.5 124
q0 49 -34 84t-97 35q-64 0 -109 -50t-45 -122q0 -56 33.5 -89t94.5 -33z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" 
d="M445 0h-105l67 303q5 22 5 30q0 35 -23 52t-58 17q-73 0 -138 -65l-74 -337h-105l107 483h105l-15 -66q82 78 165 78q68 0 108 -32.5t40 -89.5q0 -15 -5 -40zM421 554q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27
t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="573" 
d="M264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5zM417 556h-75l-133 144h100z
" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="573" 
d="M565 700l-197 -144h-77l172 144h102zM264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5
t92.5 -34.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="573" 
d="M497 556h-65l-56 96l-96 -96h-68l125 144h96zM264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134
q0 -60 33.5 -94.5t92.5 -34.5z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="573" 
d="M264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5zM427 554q-24 0 -42 12
t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="573" 
d="M530 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM321 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87
q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="511" 
d="M495 303h-453l15 68h453zM229 97q-20 0 -34.5 15t-14.5 35q0 24 17.5 41.5t41.5 17.5q21 0 35.5 -15t14.5 -35q0 -24 -18 -41.5t-42 -17.5zM311 465q-21 0 -35.5 15t-14.5 35q0 24 17.5 41.5t42.5 17.5q20 0 34.5 -15t14.5 -35q0 -25 -17.5 -42t-41.5 -17z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="573" 
d="M66 0h-72l77 74q-39 55 -39 131q0 116 79 203t196 87q93 0 155 -48l37 36h73l-75 -72q42 -56 42 -134q0 -116 -79 -202.5t-196 -86.5q-97 0 -159 49zM267 81q70 0 116.5 57.5t46.5 134.5q0 33 -13 62l-236 -226q33 -28 86 -28zM141 210q0 -32 11 -59l235 225
q-32 26 -83 26q-70 0 -116.5 -58t-46.5 -134z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" 
d="M121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52t65.5 -17q63 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM414 556h-75l-133 144h100z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" 
d="M563 700l-197 -144h-77l172 144h102zM121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52t65.5 -17q63 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" 
d="M495 556h-65l-56 96l-96 -96h-68l125 144h96zM121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52t65.5 -17q63 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" 
d="M529 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM320 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52
t65.5 -17q63 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="503" 
d="M-47 -185l35 92q18 -8 48 -8q38 0 65 40l38 58l-88 486h109l60 -362l222 362h116l-360 -570q-37 -58 -77 -83.5t-95 -25.5q-42 0 -73 11zM529 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="578" 
d="M294 -12q-108 0 -160 81l-56 -253h-105l188 851h105l-55 -248q64 76 152 76q83 0 134 -53t51 -147q0 -120 -70 -213.5t-184 -93.5zM278 81q69 0 114.5 57.5t45.5 135.5q0 58 -33.5 93t-85.5 35q-74 0 -127 -69l-41 -187q17 -29 50.5 -47t76.5 -18z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="503" 
d="M-47 -185l35 92q18 -8 48 -8q38 0 65 40l38 58l-88 486h109l60 -362l222 362h116l-360 -570q-37 -58 -77 -83.5t-95 -25.5q-42 0 -73 11zM494 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM285 614q0 -26 -20 -45.5
t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM624 733h-362l14 62h362z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM539 566h-362l14 62h362z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM626 789q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM541 622q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="673" 
d="M609 -102l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 66 65 112h-41l-21 128h-306l-77 -128h-139l409 667h146l115 -667q-97 -44 -97 -103q0 -17 10.5 -26.5t28.5 -9.5q31 0 50 37zM459 231l-50 318l-190 -318h240z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="581" 
d="M451 -102l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 66 65 112h-19l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483q-97 -44 -97 -103q0 -17 10.5 -26.5t28.5 -9.5
q31 0 50 37zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187q-17 29 -50.5 47.5t-76.5 18.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="682" 
d="M702 872l-197 -144h-77l172 144h102zM370 -12q-138 0 -228.5 81.5t-90.5 214.5q0 173 113 283.5t269 110.5q107 0 179.5 -48t106.5 -127l-112 -40q-23 54 -70.5 82.5t-109.5 28.5q-103 0 -178.5 -81t-75.5 -202q0 -88 55.5 -143.5t148.5 -55.5q96 0 166 76l89 -62
q-52 -62 -121.5 -90t-140.5 -28z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="497" 
d="M570 700l-197 -144h-77l172 144h102zM264 -12q-104 0 -167.5 59.5t-63.5 158.5q0 120 80.5 204.5t196.5 84.5q134 0 190 -93l-79 -60q-35 60 -110 60q-76 0 -123.5 -56.5t-47.5 -135.5q0 -61 37.5 -95t96.5 -34q65 0 110 52l58 -70q-71 -75 -178 -75z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="682" 
d="M631 723h-65l-56 96l-96 -96h-68l125 144h96zM370 -12q-138 0 -228.5 81.5t-90.5 214.5q0 173 113 283.5t269 110.5q107 0 179.5 -48t106.5 -127l-112 -40q-23 54 -70.5 82.5t-109.5 28.5q-103 0 -178.5 -81t-75.5 -202q0 -88 55.5 -143.5t148.5 -55.5q96 0 166 76
l89 -62q-52 -62 -121.5 -90t-140.5 -28z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="497" 
d="M500 556h-65l-56 96l-96 -96h-68l125 144h96zM264 -12q-104 0 -167.5 59.5t-63.5 158.5q0 120 80.5 204.5t196.5 84.5q134 0 190 -93l-79 -60q-35 60 -110 60q-76 0 -123.5 -56.5t-47.5 -135.5q0 -61 37.5 -95t96.5 -34q65 0 110 52l58 -70q-71 -75 -178 -75z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="682" 
d="M370 -12q-138 0 -228.5 81.5t-90.5 214.5q0 173 113 283.5t269 110.5q107 0 179.5 -48t106.5 -127l-112 -40q-23 54 -70.5 82.5t-109.5 28.5q-103 0 -178.5 -81t-75.5 -202q0 -88 55.5 -143.5t148.5 -55.5q96 0 166 76l89 -62q-52 -62 -121.5 -90t-140.5 -28zM559 780
q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="497" 
d="M264 -12q-104 0 -167.5 59.5t-63.5 158.5q0 120 80.5 204.5t196.5 84.5q134 0 190 -93l-79 -60q-35 60 -110 60q-76 0 -123.5 -56.5t-47.5 -135.5q0 -61 37.5 -95t96.5 -34q65 0 110 52l58 -70q-71 -75 -178 -75zM429 613q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 15
t-15 37q0 26 20 46t46 20q22 0 37.5 -15t15.5 -38z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="682" 
d="M539 723h-96l-64 144h64l56 -97l96 97h69zM370 -12q-138 0 -228.5 81.5t-90.5 214.5q0 173 113 283.5t269 110.5q107 0 179.5 -48t106.5 -127l-112 -40q-23 54 -70.5 82.5t-109.5 28.5q-103 0 -178.5 -81t-75.5 -202q0 -88 55.5 -143.5t148.5 -55.5q96 0 166 76l89 -62
q-52 -62 -121.5 -90t-140.5 -28z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="497" 
d="M409 556h-96l-64 144h64l56 -97l96 97h69zM264 -12q-104 0 -167.5 59.5t-63.5 158.5q0 120 80.5 204.5t196.5 84.5q134 0 190 -93l-79 -60q-35 60 -110 60q-76 0 -123.5 -56.5t-47.5 -135.5q0 -61 37.5 -95t96.5 -34q65 0 110 52l58 -70q-71 -75 -178 -75z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="710" 
d="M513 723h-96l-64 144h64l56 -97l96 97h69zM264 0h-247l147 667h243q115 0 199.5 -81t84.5 -205q0 -55 -15 -108t-48.5 -103t-82 -87.5t-121 -60t-160.5 -22.5zM281 103h4q128 0 206 79t78 192q0 81 -52 135.5t-132 54.5h-127l-102 -461h125z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="670" 
d="M754 621q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM286 495q51 0 93.5 -21.5t66.5 -59.5l56 253h105l-148 -667h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5
q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187q-17 29 -50.5 47.5t-76.5 18.5z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="736" 
d="M290 0h-247l63 286h-88l21 95h88l63 286h243q115 0 199.5 -81t84.5 -205q0 -55 -15 -108t-48.5 -103t-82 -87.5t-121 -60t-160.5 -22.5zM313 103q127 1 204.5 79.5t77.5 191.5q0 81 -52 135.5t-132 54.5h-127l-41 -183h158l-21 -95h-158l-40 -183h131z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="585" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l26 117h-159l12 62h160l17 74h105l-17 -74h52l-12 -62h-53l-118 -531h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5
t86 -34.5q37 0 70.5 19t55.5 49l42 187q-17 29 -50.5 47.5t-76.5 18.5z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM588 733h-362l14 62h362z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="557" 
d="M277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5
q-56 0 -98.5 -38t-52.5 -90zM537 566h-362l14 62h362z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM590 789q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="557" 
d="M277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5
q-56 0 -98.5 -38t-52.5 -90zM539 622q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM478 784q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="557" 
d="M277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5
q-56 0 -98.5 -38t-52.5 -90zM425 613q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 15t-15 37q0 26 20 46t46 20q22 0 37.5 -15t15.5 -38z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="577" 
d="M474 0q-97 -44 -97 -103q0 -17 10.5 -26.5t28.5 -9.5q31 0 50 37l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 66 65 112h-371l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="557" 
d="M353 -102l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 57 50 101q-105 5 -166 62t-61 153q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-38 -28 -86 -41
q-97 -44 -97 -103q0 -17 10.5 -26.5t28.5 -9.5q31 0 50 37zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5q-56 0 -98.5 -38t-52.5 -90z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="577" 
d="M454 723h-96l-64 144h64l56 -97l96 97h69zM474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="557" 
d="M404 556h-96l-64 144h64l56 -97l96 97h69zM277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277
q1 3 1 14q0 49 -33 81.5t-94 32.5q-56 0 -98.5 -38t-52.5 -90z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="717" 
d="M637 723h-65l-56 96l-96 -96h-68l125 144h96zM370 -12q-138 0 -228.5 82t-90.5 214q0 170 112.5 282t279.5 112q96 0 165.5 -44t104.5 -113l-107 -47q-21 46 -70 73t-109 27q-103 0 -178.5 -81.5t-75.5 -201.5q0 -88 55.5 -143.5t148.5 -55.5q83 0 151 59l23 104h-199
l23 103h316l-56 -253q-106 -117 -265 -117z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="580" 
d="M490 548h-65l-56 96l-96 -96h-68l125 144h96zM212 -196q-152 0 -226 83l63 70q48 -67 161 -67q118 0 148 135l12 52q-62 -77 -155 -77q-80 0 -131.5 49.5t-51.5 148.5q0 118 69 207.5t182 89.5q46 0 90.5 -22.5t70.5 -58.5l16 69h105l-102 -458q-26 -120 -94 -170.5
t-157 -50.5zM262 93q35 0 68.5 18.5t57.5 46.5l39 175q-19 32 -54 50.5t-78 18.5q-67 0 -110.5 -55t-43.5 -130q0 -57 33 -90.5t88 -33.5z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="717" 
d="M370 -12q-138 0 -228.5 82t-90.5 214q0 170 112.5 282t279.5 112q96 0 165.5 -44t104.5 -113l-107 -47q-21 46 -70 73t-109 27q-103 0 -178.5 -81.5t-75.5 -201.5q0 -88 55.5 -143.5t148.5 -55.5q83 0 151 59l23 104h-199l23 103h316l-56 -253q-106 -117 -265 -117z
M681 789q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="580" 
d="M212 -196q-152 0 -226 83l63 70q48 -67 161 -67q118 0 148 135l12 52q-62 -77 -155 -77q-80 0 -131.5 49.5t-51.5 148.5q0 118 69 207.5t182 89.5q46 0 90.5 -22.5t70.5 -58.5l16 69h105l-102 -458q-26 -120 -94 -170.5t-157 -50.5zM262 93q35 0 68.5 18.5t57.5 46.5
l39 175q-19 32 -54 50.5t-78 18.5q-67 0 -110.5 -55t-43.5 -130q0 -57 33 -90.5t88 -33.5zM534 622q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="717" 
d="M370 -12q-138 0 -228.5 82t-90.5 214q0 170 112.5 282t279.5 112q96 0 165.5 -44t104.5 -113l-107 -47q-21 46 -70 73t-109 27q-103 0 -178.5 -81.5t-75.5 -201.5q0 -88 55.5 -143.5t148.5 -55.5q83 0 151 59l23 104h-199l23 103h316l-56 -253q-106 -117 -265 -117z
M566 780q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="580" 
d="M212 -196q-152 0 -226 83l63 70q48 -67 161 -67q118 0 148 135l12 52q-62 -77 -155 -77q-80 0 -131.5 49.5t-51.5 148.5q0 118 69 207.5t182 89.5q46 0 90.5 -22.5t70.5 -58.5l16 69h105l-102 -458q-26 -120 -94 -170.5t-157 -50.5zM262 93q35 0 68.5 18.5t57.5 46.5
l39 175q-19 32 -54 50.5t-78 18.5q-67 0 -110.5 -55t-43.5 -130q0 -57 33 -90.5t88 -33.5zM420 613q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="717" 
d="M366 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM370 -12q-138 0 -228.5 82t-90.5 214q0 170 112.5 282t279.5 112q96 0 165.5 -44t104.5 -113l-107 -47q-21 46 -70 73t-109 27
q-103 0 -178.5 -81.5t-75.5 -201.5q0 -88 55.5 -143.5t148.5 -55.5q83 0 151 59l23 104h-199l23 103h316l-56 -253q-106 -117 -265 -117z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="580" 
d="M306 607q0 44 32.5 88t79.5 68l32 -34q-61 -29 -79 -74h8q19 0 31.5 -12.5t12.5 -33.5q0 -25 -19 -43t-43 -18t-39.5 15.5t-15.5 43.5zM212 -196q-152 0 -226 83l63 70q48 -67 161 -67q118 0 148 135l12 52q-62 -77 -155 -77q-80 0 -131.5 49.5t-51.5 148.5
q0 118 69 207.5t182 89.5q46 0 90.5 -22.5t70.5 -58.5l16 69h105l-102 -458q-26 -120 -94 -170.5t-157 -50.5zM262 93q35 0 68.5 18.5t57.5 46.5l39 175q-19 32 -54 50.5t-78 18.5q-67 0 -110.5 -55t-43.5 -130q0 -57 33 -90.5t88 -33.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="723" 
d="M610 723h-65l-56 96l-96 -96h-68l125 144h96zM598 0h-117l64 291h-347l-64 -291h-117l147 667h117l-60 -273h347l60 273h117z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="568" 
d="M534 723h-65l-56 96l-96 -96h-68l125 144h96zM446 0h-105l66 303q5 20 5 30q0 35 -24.5 52t-65.5 17q-65 0 -129 -66l-74 -336h-105l147 667h105l-55 -250q82 78 165 78q68 0 108 -32.5t40 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="741" 
d="M607 0h-117l64 291h-347l-64 -291h-117l110 501h-70l14 62h70l23 104h117l-23 -104h347l23 104h117l-23 -104h69l-14 -62h-69zM230 394h347l23 107h-347z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="568" 
d="M446 0h-105l66 303q5 20 5 30q0 35 -24.5 52t-65.5 17q-65 0 -129 -66l-74 -336h-105l117 531h-61l14 62h60l17 74h105l-17 -74h152l-14 -62h-151l-25 -114q82 78 165 78q68 0 108 -32.5t40 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM306 721q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="241" 
d="M119 0h-105l107 483h105zM261 554q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM419 733h-362l14 62h362z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="241" 
d="M119 0h-105l107 483h105zM373 566h-362l14 62h362z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM420 789q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="241" 
d="M119 0h-105l107 483h105zM376 622q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="259" 
d="M126 -102l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 66 65 112h-31l147 667h117l-147 -667q-97 -44 -97 -103q0 -17 10.5 -26.5t28.5 -9.5q31 0 50 37z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="241" 
d="M111 -102l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 66 65 112h-19l107 483h105l-107 -483q-97 -44 -97 -103q0 -17 10.5 -26.5t28.5 -9.5q31 0 50 37zM193 542q-25 0 -42 16.5t-17 39.5q0 33 23 53.5t50 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23 -53.5
t-50 -20.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM307 780q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="241" 
d="M119 0h-105l107 483h105z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="740" 
d="M134 0h-117l147 667h117zM421 -12q-128 0 -188 79l65 86q38 -61 115 -61q105 0 132 121l100 454h117l-100 -456q-25 -114 -84.5 -168.5t-156.5 -54.5z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="482" 
d="M193 542q-25 0 -42 16.5t-17 39.5q0 33 23 53.5t50 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23 -53.5t-50 -20.5zM119 0h-105l107 483h105zM181 -196q-85 0 -126 41l46 75q24 -30 65 -30q63 0 82 82l114 511h105l-114 -511q-18 -82 -59 -125t-113 -43zM434 542
q-25 0 -42 16.5t-17 39.5q0 33 23 53.5t50 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23 -53.5t-50 -20.5z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="481" 
d="M602 723h-65l-56 96l-96 -96h-68l125 144h96zM162 -12q-128 0 -188 79l65 86q38 -61 115 -61q105 0 132 121l100 454h117l-100 -456q-25 -114 -84.5 -168.5t-156.5 -54.5z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="241" 
d="M334 556h-65l-56 96l-96 -96h-68l125 144h96zM-60 -196q-85 0 -126 41l46 75q24 -30 65 -30q63 0 82 82l114 511h105l-114 -511q-18 -82 -59 -125t-113 -43z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="621" 
d="M298 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM560 0h-137l-165 286l-77 -73l-47 -213h-117l147 667h117l-68 -308l322 308h152l-340 -315z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="526" 
d="M247 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM466 0h-127l-106 202l-86 -72l-28 -130h-105l147 667h105l-91 -411l259 227h135l-250 -219z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="526" 
d="M466 0h-127l-106 202l-86 -72l-28 -130h-105l107 483h105l-51 -227l259 227h135l-250 -219z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="517" 
d="M591 867l-197 -144h-77l172 144h102zM428 0h-411l147 667h117l-125 -564h294z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="241" 
d="M439 867l-197 -144h-77l172 144h102zM119 0h-105l147 667h105z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="517" 
d="M247 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM428 0h-411l147 667h117l-125 -564h294z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="241" 
d="M97 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM119 0h-105l147 667h105z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="517" 
d="M472 622q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM428 0h-411l147 667h117l-125 -564h294z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="327" 
d="M426 622q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM119 0h-105l147 667h105z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="517" 
d="M428 0h-411l147 667h117l-125 -564h294zM401 272q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="351" 
d="M119 0h-105l147 667h105zM289 175q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="541" 
d="M8 204l22 97l87 45l71 321h117l-56 -254l96 50l-21 -97l-97 -50l-47 -213h294l-22 -103h-411l55 249z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="299" 
d="M2 214l18 79l99 50l71 324h105l-58 -264l99 51l-18 -79l-99 -50l-71 -325h-105l58 264z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="720" 
d="M677 867l-197 -144h-77l172 144h102zM595 0h-113l-239 494l-109 -494h-117l147 667h120l234 -481l107 481h117z" />
    <glyph glyph-name="nacute" unicode="&#x144;" 
d="M560 700l-197 -144h-77l172 144h102zM445 0h-105l67 303q5 22 5 30q0 35 -23 52t-58 17q-73 0 -138 -65l-74 -337h-105l107 483h105l-15 -66q82 78 165 78q68 0 108 -32.5t40 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="720" 
d="M335 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM595 0h-113l-239 494l-109 -494h-117l147 667h120l234 -481l107 481h117z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" 
d="M257 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM445 0h-105l67 303q5 22 5 30q0 35 -23 52t-58 17q-73 0 -138 -65l-74 -337h-105l107 483h105l-15 -66q82 78 165 78q68 0 108 -32.5
t40 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="720" 
d="M516 723h-96l-64 144h64l56 -97l96 97h69zM595 0h-113l-239 494l-109 -494h-117l147 667h120l234 -481l107 481h117z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" 
d="M400 556h-96l-64 144h64l56 -97l96 97h69zM445 0h-105l67 303q5 22 5 30q0 35 -23 52t-58 17q-73 0 -138 -65l-74 -337h-105l107 483h105l-15 -66q82 78 165 78q68 0 108 -32.5t40 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" 
d="M265 697q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM445 0h-105l67 303q5 22 5 30q0 35 -23 52t-58 17q-73 0 -138 -65l-74 -337h-105l107 483h105l-15 -66q82 78 165 78q68 0 108 -32.5
t40 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="720" 
d="M359 -196q-128 0 -188 79l64 77q16 -28 46.5 -45t68.5 -17q48 0 81 28.5t47 80.5l-235 487l-109 -494h-117l147 667h120l234 -481l107 481h117l-142 -642q-27 -114 -86.5 -167.5t-154.5 -53.5z" />
    <glyph glyph-name="eng" unicode="&#x14b;" 
d="M519 333l-80 -361q-38 -168 -172 -168q-86 0 -126 41l48 82q25 -30 64 -30q63 0 81 75l73 331q5 18 5 30q0 35 -23 52t-58 17q-73 0 -138 -65l-74 -337h-105l107 483h105l-15 -66q82 78 165 78q66 0 107 -31.5t41 -90.5q0 -17 -5 -40z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="765" 
d="M672 733h-362l14 62h362zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5
q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="573" 
d="M538 566h-362l14 62h362zM264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5z
" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z
M674 789q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="573" 
d="M264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5zM540 622q-85 -88 -194 -88
q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="765" 
d="M581 867l-147 -144h-65l122 144h90zM727 867l-147 -144h-65l122 144h90zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145
t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="573" 
d="M448 700l-147 -144h-65l122 144h90zM594 700l-147 -144h-65l122 144h90zM264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5
q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1093" 
d="M991 0h-457l19 87q-81 -99 -222 -99q-121 0 -200.5 83t-79.5 213q0 86 30.5 160t82 124.5t120.5 79.5t144 29q167 0 227 -132l26 122h457l-22 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM581 211l46 210q-16 72 -67 112t-127 40q-110 0 -185 -82t-75 -200
q0 -90 55 -144.5t142 -54.5q63 0 119.5 32t91.5 87z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="944" 
d="M902 207h-370q0 -1 -0.5 -7t-0.5 -10q0 -47 38.5 -81.5t106.5 -34.5q76 0 127 40l33 -73q-70 -53 -168 -53q-78 0 -132 37.5t-70 82.5q-21 -26 -34.5 -40.5t-40 -36.5t-58.5 -32.5t-71 -10.5q-104 0 -167 58t-63 159q0 115 77.5 202.5t195.5 87.5q74 0 123.5 -38
t65.5 -83q18 24 34 40.5t42 37.5t59.5 32t70.5 11q91 0 151 -57t60 -157q0 -33 -9 -74zM543 281h273q1 4 1 14q0 49 -32 81.5t-92 32.5q-58 0 -100.5 -40t-49.5 -88zM426 273q0 64 -35.5 96.5t-88.5 32.5q-67 0 -114 -56.5t-47 -135.5q0 -60 33 -94.5t91 -34.5
q69 0 115 56.5t46 135.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="628" 
d="M633 867l-197 -144h-77l172 144h102zM533 0h-129l-93 249h-123l-54 -249h-117l147 667h268q81 0 140 -50t59 -132q0 -91 -56.5 -154t-145.5 -75zM370 352h2q63 0 99.5 34.5t36.5 87.5q0 39 -29 64.5t-68 25.5h-153l-47 -212h159z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="347" 
d="M483 700l-197 -144h-77l172 144h102zM119 0h-105l107 483h105l-16 -68q71 79 175 79l-23 -105q-17 5 -43 5q-36 0 -70.5 -19t-57.5 -48z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="628" 
d="M291 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM533 0h-129l-93 249h-123l-54 -249h-117l147 667h268q81 0 140 -50t59 -132q0 -91 -56.5 -154t-145.5 -75zM370 352h2q63 0 99.5 34.5
t36.5 87.5q0 39 -29 64.5t-68 25.5h-153l-47 -212h159z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="347" 
d="M181 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM119 0h-105l107 483h105l-16 -68q71 79 175 79l-23 -105q-17 5 -43 5q-36 0 -70.5 -19t-57.5 -48z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="628" 
d="M471 723h-96l-64 144h64l56 -97l96 97h69zM533 0h-129l-93 249h-123l-54 -249h-117l147 667h268q81 0 140 -50t59 -132q0 -91 -56.5 -154t-145.5 -75zM370 352h2q63 0 99.5 34.5t36.5 87.5q0 39 -29 64.5t-68 25.5h-153l-47 -212h159z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="347" 
d="M322 556h-96l-64 144h64l56 -97l96 97h69zM119 0h-105l107 483h105l-16 -68q71 79 175 79l-23 -105q-17 5 -43 5q-36 0 -70.5 -19t-57.5 -48z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="594" 
d="M616 867l-197 -144h-77l172 144h102zM294 -12q-93 0 -170.5 34t-117.5 88l75 86q35 -48 94.5 -76t128.5 -28q59 0 89.5 29t30.5 67q0 30 -33.5 52t-82 39.5t-96.5 38.5t-81.5 60t-33.5 92q0 84 69.5 145.5t180.5 61.5q79 0 147 -28.5t108 -78.5l-75 -83q-34 42 -87 64.5
t-109 22.5q-46 0 -78 -25.5t-32 -59.5q0 -27 34 -48.5t82 -39t96 -39t82 -60.5t34 -93q0 -91 -69 -156t-186 -65z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="472" 
d="M518 700l-197 -144h-77l172 144h102zM214 -12q-141 0 -218 86l62 73q22 -30 68.5 -53t96.5 -23q39 0 63 19t24 46q0 25 -39 44.5t-85.5 32.5t-85.5 45t-39 80q0 65 51.5 111t140.5 46q60 0 112.5 -22t83.5 -57l-56 -69q-18 25 -59 44.5t-84 19.5q-39 0 -62 -16t-23 -41
q0 -19 25.5 -33.5t62.5 -27t74 -27.5t62.5 -44t25.5 -69q0 -70 -54.5 -117.5t-146.5 -47.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="594" 
d="M548 723h-65l-56 96l-96 -96h-68l125 144h96zM294 -12q-93 0 -170.5 34t-117.5 88l75 86q35 -48 94.5 -76t128.5 -28q59 0 89.5 29t30.5 67q0 30 -33.5 52t-82 39.5t-96.5 38.5t-81.5 60t-33.5 92q0 84 69.5 145.5t180.5 61.5q79 0 147 -28.5t108 -78.5l-75 -83
q-34 42 -87 64.5t-109 22.5q-46 0 -78 -25.5t-32 -59.5q0 -27 34 -48.5t82 -39t96 -39t82 -60.5t34 -93q0 -91 -69 -156t-186 -65z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="472" 
d="M449 556h-65l-56 96l-96 -96h-68l125 144h96zM214 -12q-141 0 -218 86l62 73q22 -30 68.5 -53t96.5 -23q39 0 63 19t24 46q0 25 -39 44.5t-85.5 32.5t-85.5 45t-39 80q0 65 51.5 111t140.5 46q60 0 112.5 -22t83.5 -57l-56 -69q-18 25 -59 44.5t-84 19.5q-39 0 -62 -16
t-23 -41q0 -19 25.5 -33.5t62.5 -27t74 -27.5t62.5 -44t25.5 -69q0 -70 -54.5 -117.5t-146.5 -47.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="594" 
d="M209 -194q-74 0 -114 40l31 43q35 -35 84 -35q26 0 40.5 11t14.5 29q0 26 -33 26q-18 0 -32 -15l-35 23l40 69q-137 27 -199 113l75 86q35 -48 94.5 -76t128.5 -28q59 0 89.5 29t30.5 67q0 30 -33.5 52t-82 39.5t-96.5 38.5t-81.5 60t-33.5 92q0 84 69.5 145.5
t180.5 61.5q79 0 147 -28.5t108 -78.5l-75 -83q-34 42 -87 64.5t-109 22.5q-46 0 -78 -25.5t-32 -59.5q0 -27 34 -48.5t82 -39t96 -39t82 -60.5t34 -93q0 -91 -69 -156t-186 -65q-13 0 -39 2l-26 -43q15 11 32 11q26 0 42.5 -16.5t16.5 -44.5q0 -41 -31 -66t-80 -25z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="472" 
d="M151 -194q-74 0 -114 40l31 43q35 -35 84 -35q26 0 40.5 11t14.5 29q0 26 -33 26q-18 0 -32 -15l-35 23l39 66q-93 17 -150 80l62 73q22 -30 68.5 -53t96.5 -23q39 0 63 19t24 46q0 25 -39 44.5t-85.5 32.5t-85.5 45t-39 80q0 65 51.5 111t140.5 46q60 0 112.5 -22
t83.5 -57l-56 -69q-18 25 -59 44.5t-84 19.5q-39 0 -62 -16t-23 -41q0 -19 25.5 -33.5t62.5 -27t74 -27.5t62.5 -44t25.5 -69q0 -70 -54.5 -117.5t-146.5 -47.5q-12 0 -17 1l-26 -42q15 11 32 11q26 0 42.5 -16.5t16.5 -44.5q0 -41 -31 -66t-80 -25z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="594" 
d="M456 723h-96l-64 144h64l56 -97l96 97h69zM294 -12q-93 0 -170.5 34t-117.5 88l75 86q35 -48 94.5 -76t128.5 -28q59 0 89.5 29t30.5 67q0 30 -33.5 52t-82 39.5t-96.5 38.5t-81.5 60t-33.5 92q0 84 69.5 145.5t180.5 61.5q79 0 147 -28.5t108 -78.5l-75 -83
q-34 42 -87 64.5t-109 22.5q-46 0 -78 -25.5t-32 -59.5q0 -27 34 -48.5t82 -39t96 -39t82 -60.5t34 -93q0 -91 -69 -156t-186 -65z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="472" 
d="M357 556h-96l-64 144h64l56 -97l96 97h69zM214 -12q-141 0 -218 86l62 73q22 -30 68.5 -53t96.5 -23q39 0 63 19t24 46q0 25 -39 44.5t-85.5 32.5t-85.5 45t-39 80q0 65 51.5 111t140.5 46q60 0 112.5 -22t83.5 -57l-56 -69q-18 25 -59 44.5t-84 19.5q-39 0 -62 -16
t-23 -41q0 -19 25.5 -33.5t62.5 -27t74 -27.5t62.5 -44t25.5 -69q0 -70 -54.5 -117.5t-146.5 -47.5z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="577" 
d="M265 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM293 0h-117l124 564h-202l23 103h521l-23 -103h-202z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="319" 
d="M130 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM187 -12q-60 0 -94.5 24.5t-34.5 73.5q0 14 3 31l61 275h-80l21 91h80l29 132h105l-29 -132h98l-21 -91h-98l-55 -249q-2 -10 -2 -19
q0 -43 48 -43q25 0 40 13l6 -84q-28 -22 -77 -22z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="577" 
d="M437 723h-96l-64 144h64l56 -97l96 97h69zM293 0h-117l124 564h-202l23 103h521l-23 -103h-202z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="351" 
d="M465 697q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM187 -12q-60 0 -94.5 24.5t-34.5 73.5q0 14 3 31l61 275h-80l21 91h80l29 132h105l-29 -132h98l-21 -91h-98l-55 -249q-2 -10 -2 -19
q0 -43 48 -43q25 0 40 13l6 -84q-28 -22 -77 -22z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="577" 
d="M293 0h-117l60 276h-140l14 62h140l50 226h-202l23 103h521l-23 -103h-202l-50 -226h141l-14 -62h-141z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="319" 
d="M187 -12q-60 0 -94.5 24.5t-34.5 73.5q0 14 3 31l22 100h-79l13 62h80l25 113h-80l21 91h80l29 132h105l-29 -132h98l-21 -91h-98l-25 -113h69l-14 -62h-69l-16 -74q-2 -10 -2 -19q0 -43 48 -43q25 0 40 13l6 -84q-28 -22 -77 -22z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="719" 
d="M347 -12q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-29 -133 -100 -204.5t-205 -71.5zM535 721q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134
q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="utilde" unicode="&#x169;" 
d="M121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52t65.5 -17q63 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM423 554q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12
t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="719" 
d="M347 -12q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-29 -133 -100 -204.5t-205 -71.5zM648 733h-362l14 62h362z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" 
d="M121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52t65.5 -17q63 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM536 566h-362l14 62h362z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="719" 
d="M347 -12q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-29 -133 -100 -204.5t-205 -71.5zM650 789q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66
q79 0 142 66z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" 
d="M121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52t65.5 -17q63 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM538 622q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="719" 
d="M470 689q-41 0 -67 26t-26 67q0 45 33.5 78.5t78.5 33.5q41 0 67 -26t26 -67q0 -46 -33 -79t-79 -33zM474 740q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -17t-17 -39q0 -21 13 -34t33 -13zM347 -12q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118
l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-29 -133 -100 -204.5t-205 -71.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" 
d="M361 536q-41 0 -67 26t-26 67q0 45 33.5 78.5t78.5 33.5q41 0 67 -26t26 -67q0 -46 -33 -79t-79 -33zM365 587q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -17t-17 -39q0 -21 13 -34t33 -13zM121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52t65.5 -17
q63 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="719" 
d="M347 -12q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-29 -133 -100 -204.5t-205 -71.5zM560 867l-147 -144h-65l122 144h90zM706 867l-147 -144h-65l122 144h90z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" 
d="M121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52t65.5 -17q63 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM445 700l-147 -144h-65l122 144h90zM591 700l-147 -144h-65l122 144h90z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="719" 
d="M435 -102l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 57 49 100q-130 1 -200.5 62.5t-70.5 165.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-48 -220 -209 -264q-97 -44 -97 -103
q0 -17 10.5 -26.5t28.5 -9.5q31 0 50 37z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" 
d="M121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52t65.5 -17q63 0 129 66l74 336h105l-107 -483q-97 -44 -97 -103q0 -17 10.5 -26.5t28.5 -9.5q31 0 50 37l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 66 65 112h-19l15 66q-82 -78 -165 -78q-68 0 -108 32.5
t-40 89.5q0 15 5 40z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="903" 
d="M698 723h-65l-56 96l-96 -96h-68l125 144h96zM652 0h-125l-21 492l-238 -492h-125l-44 667h128l16 -514l252 514h93l24 -514l243 514h134z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="754" 
d="M586 556h-65l-56 96l-96 -96h-68l125 144h96zM545 0h-110l-34 354l-190 -354h-110l-43 483h105l24 -351l193 351h93l36 -351l180 351h113z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="637" 
d="M568 723h-65l-56 96l-96 -96h-68l125 144h96zM323 0h-117l61 277l-173 390h127l121 -286l245 286h141l-344 -390z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="503" 
d="M-47 -185l35 92q18 -8 48 -8q38 0 65 40l38 58l-88 486h109l60 -362l222 362h116l-360 -570q-37 -58 -77 -83.5t-95 -25.5q-42 0 -73 11zM461 556h-65l-56 96l-96 -96h-68l125 144h96z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="637" 
d="M600 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -14.5t15 -37.5zM391 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -14.5t14.5 -37.5zM323 0h-117l61 277l-173 390h127l121 -286l245 286
h141l-344 -390z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="589" 
d="M606 867l-197 -144h-77l172 144h102zM490 0h-500l21 95l437 469h-333l22 103h493l-20 -95l-439 -469h342z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="476" 
d="M513 700l-197 -144h-77l172 144h102zM375 0h-383l17 79l304 312h-234l20 92h379l-17 -77l-307 -315h241z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="589" 
d="M490 0h-500l21 95l437 469h-333l22 103h493l-20 -95l-439 -469h342zM466 780q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="476" 
d="M375 0h-383l17 79l304 312h-234l20 92h379l-17 -77l-307 -315h241zM374 613q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="589" 
d="M447 723h-96l-64 144h64l56 -97l96 97h69zM490 0h-500l21 95l437 469h-333l22 103h493l-20 -95l-439 -469h342z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="476" 
d="M353 556h-96l-64 144h64l56 -97l96 97h69zM375 0h-383l17 79l304 312h-234l20 92h379l-17 -77l-307 -315h241z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="308" 
d="M145 0h-105l86 392h-80l21 91h79l6 27q16 75 59.5 121t116.5 46t118 -45l-54 -63q-22 22 -57 22q-59 0 -78 -81z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="444" 
d="M80 -161h-119l90 406h-60v93h80l40 182q35 157 170 157q79 0 125 -45l-46 -82q-18 24 -54 24q-62 0 -80 -78l-36 -158h125v-93h-145z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q163 0 253 -106q50 28 63 65h-7q-16 0 -27.5 11.5t-11.5 30.5q0 22 17 38.5t39 16.5q21 0 35 -14.5t14 -39.5q0 -40 -28 -79.5t-70 -61.5q42 -67 42 -156q0 -166 -110.5 -280.5t-271.5 -114.5z
M377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" horiz-adv-x="573" 
d="M589 547q0 -38 -26 -76t-65 -61q41 -57 41 -133q0 -116 -79 -202.5t-196 -86.5q-108 0 -170 60t-62 157q0 116 79 203t196 87q102 0 163 -55q47 29 60 64q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39zM267 81q70 0 116.5 57.5
t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="719" 
d="M865 743q0 -51 -40.5 -95t-102.5 -68l-70 -316q-29 -133 -100 -204.5t-205 -71.5q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-9 -39q61 32 74 72q-1 -1 -7 -1q-16 0 -27.5 11.5
t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" 
d="M656 556q0 -46 -34 -86.5t-88 -66.5l-89 -403h-105l15 65q-81 -77 -165 -77q-68 0 -108.5 32.5t-40.5 88.5q0 19 6 41l73 333h105l-67 -303q-4 -18 -4 -30q0 -35 24.5 -52t65.5 -17q63 0 128 65l75 337h105l-6 -28q41 28 51 58h-7q-16 0 -28 11.5t-12 30.5q0 22 17 38.5
t39 16.5q21 0 35.5 -14.5t14.5 -39.5z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="959" 
d="M831 867l-197 -144h-77l172 144h102zM858 0h-457l28 128h-243l-105 -128h-137l557 667h504l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM452 231l68 312l-255 -312h187z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="924" 
d="M741 700l-197 -144h-77l172 144h102zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187q-17 29 -50.5 47.5t-76.5 18.5zM480 95l-21 -95h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5
t136.5 46q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-15 -70q66 82 160 82q87 0 134 -54.5t47 -152.5q0 -44 -10 -81h-372q-1 -3 -1 -17q0 -45 38.5 -79.5t107.5 -34.5q76 0 126 41l34 -76q-76 -53 -156 -53q-136 0 -179 107zM520 281h274q1 3 1 14q0 50 -31.5 82t-91.5 32
q-56 0 -99 -38t-53 -90z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="765" 
d="M700 867l-197 -144h-77l172 144h102zM370 -12q-119 0 -202 59l-44 -47h-87l84 89q-70 78 -70 195q0 165 110.5 279.5t271.5 114.5q111 0 193 -54l41 43h87l-79 -84q77 -80 77 -200q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 70 -37 121
l-345 -365q52 -39 129 -39zM173 291q0 -67 31 -114l343 363q-53 34 -121 34q-105 0 -179 -81.5t-74 -201.5z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="573" 
d="M566 700l-197 -144h-77l172 144h102zM66 0h-72l77 74q-39 55 -39 131q0 116 79 203t196 87q93 0 155 -48l37 36h73l-75 -72q42 -56 42 -134q0 -116 -79 -202.5t-196 -86.5q-97 0 -159 49zM267 81q70 0 116.5 57.5t46.5 134.5q0 33 -13 62l-236 -226q33 -28 86 -28z
M141 210q0 -32 11 -59l235 225q-32 26 -83 26q-70 0 -116.5 -58t-46.5 -134z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="594" 
d="M275 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM294 -12q-93 0 -170.5 34t-117.5 88l75 86q35 -48 94.5 -76t128.5 -28q59 0 89.5 29t30.5 67q0 30 -33.5 52t-82 39.5t-96.5 38.5
t-81.5 60t-33.5 92q0 84 69.5 145.5t180.5 61.5q79 0 147 -28.5t108 -78.5l-75 -83q-34 42 -87 64.5t-109 22.5q-46 0 -78 -25.5t-32 -59.5q0 -27 34 -48.5t82 -39t96 -39t82 -60.5t34 -93q0 -91 -69 -156t-186 -65z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="472" 
d="M214 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM214 -12q-141 0 -218 86l62 73q22 -30 68.5 -53t96.5 -23q39 0 63 19t24 46q0 25 -39 44.5t-85.5 32.5t-85.5 45t-39 80
q0 65 51.5 111t140.5 46q60 0 112.5 -22t83.5 -57l-56 -69q-18 25 -59 44.5t-84 19.5q-39 0 -62 -16t-23 -41q0 -19 25.5 -33.5t62.5 -27t74 -27.5t62.5 -44t25.5 -69q0 -70 -54.5 -117.5t-146.5 -47.5z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="577" 
d="M265 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM293 0h-117l124 564h-202l23 103h521l-23 -103h-202z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="319" 
d="M125 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM187 -12q-60 0 -94.5 24.5t-34.5 73.5q0 14 3 31l61 275h-80l21 91h80l29 132h105l-29 -132h98l-21 -91h-98l-55 -249q-2 -10 -2 -19
q0 -43 48 -43q25 0 40 13l6 -84q-28 -22 -77 -22z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="241" 
d="M-60 -196q-85 0 -126 41l46 75q24 -30 65 -30q63 0 82 82l114 511h105l-114 -511q-18 -82 -59 -125t-113 -43z" />
    <glyph glyph-name="hsuperior" unicode="&#x2b0;" horiz-adv-x="405" 
d="M363 326h-77l42 192q2 12 2 19q0 46 -57 46q-39 0 -82 -43l-47 -214h-78l96 434h78l-36 -162q50 50 108 50q48 0 75 -21.5t27 -60.5q0 -8 -4 -30z" />
    <glyph glyph-name="uni02BC" unicode="&#x2bc;" horiz-adv-x="244" 
d="M276 605q0 -52 -38.5 -104t-95.5 -80l-39 41q71 34 92 84h-9q-23 0 -38.5 15t-15.5 41q0 30 23.5 52.5t53.5 22.5q28 0 47.5 -19t19.5 -53z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="285" 
d="M354 556h-65l-56 96l-96 -96h-68l125 144h96z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="285" 
d="M261 556h-96l-64 144h64l56 -97l96 97h69z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="363" 
d="M439 591h-362l13 62h362z" />
    <glyph glyph-name="uni02CB" unicode="&#x2cb;" horiz-adv-x="240" 
d="M309 556h-75l-133 144h100z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="351" 
d="M433 622q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="147" 
d="M214 613q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="203" 
d="M178 536q-41 0 -67 26t-26 67q0 45 33.5 78.5t78.5 33.5q41 0 67 -26t26 -67q0 -46 -33 -79t-79 -33zM182 587q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -17t-17 -39q0 -21 13 -34t33 -13z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="187" 
d="M66 -102l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 43 29.5 79.5t83.5 59.5l38 -27q-97 -44 -97 -103q0 -17 10.5 -26.5t28.5 -9.5q31 0 50 37z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="321" 
d="M300 554q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="324" 
d="M279 700l-147 -144h-65l122 144h90zM425 700l-147 -144h-65l122 144h90z" />
    <glyph glyph-name="hookabovecomb" unicode="&#x309;" horiz-adv-x="187" 
d="M218 725l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni037E" unicode="&#x37e;" horiz-adv-x="245" 
d="M154 57q0 -52 -38.5 -104t-94.5 -80l-39 41q71 34 92 84h-10q-22 0 -37.5 15t-15.5 41q0 30 23 52.5t53 22.5q28 0 47.5 -19t19.5 -53zM154 350q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="tonos" unicode="&#x384;" 
d="M225 558h-48l67 207h93z" />
    <glyph glyph-name="dieresistonos" unicode="&#x385;" 
d="M364 558h-49l67 207h93zM569 614q0 -25 -20 -45t-46 -20q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM283 614q0 -25 -20 -45t-46 -20q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="Alphatonos" unicode="&#x386;" horiz-adv-x="673" 
d="M194 558h-48l67 207h93zM617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240z" />
    <glyph glyph-name="Epsilontonos" unicode="&#x388;" horiz-adv-x="605" 
d="M104 558h-48l67 207h93zM502 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340z" />
    <glyph glyph-name="Etatonos" unicode="&#x389;" horiz-adv-x="751" 
d="M104 558h-48l67 207h93zM626 0h-117l64 291h-347l-64 -291h-117l147 667h117l-60 -273h347l60 273h117z" />
    <glyph glyph-name="Iotatonos" unicode="&#x38a;" horiz-adv-x="287" 
d="M104 558h-48l67 207h93zM162 0h-117l147 667h117z" />
    <glyph glyph-name="Omicrontonos" unicode="&#x38c;" horiz-adv-x="765" 
d="M129 558h-48l67 207h93zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5
q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="Upsilontonos" unicode="&#x38e;" horiz-adv-x="710" 
d="M104 558h-48l67 207h93zM396 0h-117l61 277l-173 390h127l121 -286l245 286h141l-344 -390z" />
    <glyph glyph-name="Omegatonos" unicode="&#x38f;" horiz-adv-x="772" 
d="M129 558h-48l67 207h93zM35 103h143q-111 83 -111 223q0 65 25.5 127.5t71.5 113t118 81t158 30.5q141 0 231 -76t90 -206q0 -104 -56.5 -178.5t-135.5 -114.5h112l-22 -103h-257l23 103q93 20 151.5 96t58.5 177q0 90 -54 143.5t-146 53.5q-111 0 -182.5 -83.5
t-71.5 -197.5q0 -131 110 -189l-22 -103h-257z" />
    <glyph glyph-name="iotadieresistonos" unicode="&#x390;" horiz-adv-x="253" 
d="M212 558h-49l67 207h93zM417 614q0 -25 -20 -45t-46 -20q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM131 614q0 -25 -20 -45t-46 -20q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM133 -12q-124 0 -96 124l85 371h105l-81 -352
q-5 -23 3.5 -36.5t28.5 -13.5q10 0 20 2l-13 -89q-21 -6 -52 -6z" />
    <glyph glyph-name="Alpha" unicode="&#x391;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240z" />
    <glyph glyph-name="Beta" unicode="&#x392;" horiz-adv-x="648" 
d="M357 0h-340l147 667h304q79 0 127.5 -45t48.5 -112q0 -68 -45.5 -119t-107.5 -57q44 -12 70.5 -51t26.5 -85q0 -80 -58.5 -139t-172.5 -59zM411 391q56 0 82 29t26 70q0 31 -24 52.5t-58 21.5h-179l-38 -173h191zM351 103q55 0 87 30.5t32 74.5q0 36 -23.5 58t-64.5 22
h-185l-41 -185h195z" />
    <glyph glyph-name="Gamma" unicode="&#x393;" horiz-adv-x="562" 
d="M134 0h-117l147 667h457l-23 -103h-340z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="673" 
d="M617 0h-670l409 667h146zM480 103l-71 446l-267 -446h338z" />
    <glyph glyph-name="Epsilon" unicode="&#x395;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340z" />
    <glyph glyph-name="Zeta" unicode="&#x396;" horiz-adv-x="589" 
d="M490 0h-500l21 95l437 469h-333l22 103h493l-20 -95l-439 -469h342z" />
    <glyph glyph-name="Eta" unicode="&#x397;" horiz-adv-x="723" 
d="M598 0h-117l64 291h-347l-64 -291h-117l147 667h117l-60 -273h347l60 273h117z" />
    <glyph glyph-name="Theta" unicode="&#x398;" horiz-adv-x="765" 
d="M554 288h-332l23 103h332zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5
q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="Iota" unicode="&#x399;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117z" />
    <glyph glyph-name="Kappa" unicode="&#x39a;" horiz-adv-x="621" 
d="M560 0h-137l-165 286l-77 -73l-47 -213h-117l147 667h117l-68 -308l322 308h152l-340 -315z" />
    <glyph glyph-name="Lambda" unicode="&#x39b;" horiz-adv-x="673" 
d="M617 0h-127l-81 549l-323 -549h-139l409 667h146z" />
    <glyph glyph-name="Mu" unicode="&#x39c;" horiz-adv-x="835" 
d="M710 0h-117l113 515l-318 -515h-50l-91 515l-113 -515h-117l147 667h160l80 -459l283 459h170z" />
    <glyph glyph-name="Nu" unicode="&#x39d;" horiz-adv-x="720" 
d="M595 0h-113l-239 494l-109 -494h-117l147 667h120l234 -481l107 481h117z" />
    <glyph glyph-name="Xi" unicode="&#x39e;" horiz-adv-x="621" 
d="M643 564h-521l22 103h521zM519 0h-521l22 103h521zM575 288h-508l23 103h508z" />
    <glyph glyph-name="Omicron" unicode="&#x39f;" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="Pi" unicode="&#x3a0;" horiz-adv-x="723" 
d="M598 0h-117l124 564h-347l-124 -564h-117l147 667h581z" />
    <glyph glyph-name="Rho" unicode="&#x3a1;" horiz-adv-x="608" 
d="M134 0h-117l147 667h268q93 0 145.5 -51.5t52.5 -128.5q0 -32 -8.5 -63.5t-28.5 -63.5t-49.5 -56t-76 -39.5t-104.5 -15.5h-174zM211 352h160q63 0 100 35.5t37 88.5q0 37 -27 62.5t-68 25.5h-155z" />
    <glyph glyph-name="Sigma" unicode="&#x3a3;" horiz-adv-x="560" 
d="M271 564l166 -228l-265 -233h312l-22 -103h-458l24 113l268 238l-164 223l20 93h457l-23 -103h-315z" />
    <glyph glyph-name="Tau" unicode="&#x3a4;" horiz-adv-x="577" 
d="M293 0h-117l124 564h-202l23 103h521l-23 -103h-202z" />
    <glyph glyph-name="Upsilon" unicode="&#x3a5;" horiz-adv-x="637" 
d="M323 0h-117l61 277l-173 390h127l121 -286l245 286h141l-344 -390z" />
    <glyph glyph-name="Phi" unicode="&#x3a6;" horiz-adv-x="834" 
d="M421 0h-117l15 68q-130 13 -196.5 73.5t-66.5 162.5q0 122 108 205t277 92l15 66h117l-16 -69q264 -29 264 -237q0 -122 -108.5 -205t-276.5 -91zM179 304q0 -54 42 -89t122 -44l74 324q-112 -11 -175 -62.5t-63 -128.5zM698 361q0 54 -42 89t-122 45l-74 -324
q111 11 174.5 62t63.5 128z" />
    <glyph glyph-name="Chi" unicode="&#x3a7;" horiz-adv-x="661" 
d="M603 0h-132l-135 256l-239 -256h-148l328 349l-166 318h133l123 -240l220 240h148l-309 -332z" />
    <glyph glyph-name="Psi" unicode="&#x3a8;" horiz-adv-x="836" 
d="M423 0h-117l29 128q-233 26 -233 211q0 35 10 80l57 248h118l-60 -264q-6 -24 -6 -48q0 -50 34 -82.5t103 -41.5l100 436h117l-99 -435q174 23 208 171l60 264h119l-65 -288q-24 -106 -116 -175.5t-231 -78.5z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="765" 
d="M28 103h143q-111 83 -111 223q0 65 25.5 127.5t71.5 113t118 81t158 30.5q141 0 231 -76t90 -206q0 -104 -56.5 -178.5t-135.5 -114.5h112l-22 -103h-257l23 103q93 20 151.5 96t58.5 177q0 90 -54 143.5t-146 53.5q-111 0 -182.5 -83.5t-71.5 -197.5q0 -131 110 -189
l-22 -103h-257z" />
    <glyph glyph-name="Iotadieresis" unicode="&#x3aa;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM413 798q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM204 798q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="Upsilondieresis" unicode="&#x3ab;" horiz-adv-x="637" 
d="M323 0h-117l61 277l-173 390h127l121 -286l245 286h141l-344 -390zM603 798q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM394 798q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15
t15 -37z" />
    <glyph glyph-name="alphatonos" unicode="&#x3ac;" horiz-adv-x="595" 
d="M383 558h-48l67 207h93zM540 83l-13 -89q-21 -6 -52 -6q-98 0 -98 87q-71 -87 -160 -87q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-78 -352q-5 -23 3.5 -36.5t28.5 -13.5q10 0 20 2zM261 81q36 0 70 19
t56 49l42 187q-17 29 -50.5 47.5t-76.5 18.5q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5z" />
    <glyph glyph-name="epsilontonos" unicode="&#x3ad;" horiz-adv-x="508" 
d="M339 558h-48l67 207h93zM220 -12q-105 0 -157 34.5t-52 92.5t55 93t112 39q-45 7 -74.5 33.5t-29.5 65.5q0 66 71 107.5t176 41.5q130 0 200 -80l-65 -62q-47 56 -134 56q-62 0 -101.5 -19.5t-39.5 -50.5q0 -53 105 -53h103l-19 -84h-103q-143 0 -143 -72
q0 -25 27.5 -40.5t77.5 -15.5q106 0 186 59l36 -69q-99 -76 -231 -76z" />
    <glyph glyph-name="etatonos" unicode="&#x3ae;" 
d="M374 558h-48l67 207h93zM403 -184h-105l109 487q5 22 5 30q0 35 -24.5 52t-65.5 17q-64 0 -129 -65l-74 -337h-105l107 483h105l-15 -66q82 78 165 78q68 0 108 -32.5t40 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="iotatonos" unicode="&#x3af;" horiz-adv-x="253" 
d="M215 558h-48l67 207h93zM133 -12q-124 0 -96 124l85 371h105l-81 -352q-5 -23 3.5 -36.5t28.5 -13.5q10 0 20 2l-13 -89q-21 -6 -52 -6z" />
    <glyph glyph-name="upsilondieresistonos" unicode="&#x3b0;" horiz-adv-x="570" 
d="M385 558h-49l67 207h93zM590 614q0 -25 -20 -45t-46 -20q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM304 614q0 -25 -20 -45t-46 -20q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM258 -12q-119 0 -168 68t-24 175l58 252h105
l-61 -266q-14 -63 8.5 -99.5t81.5 -36.5q78 0 131.5 74t53.5 174q0 75 -30 132l98 34q35 -63 35 -163q0 -145 -82 -244.5t-206 -99.5z" />
    <glyph glyph-name="alpha" unicode="&#x3b1;" horiz-adv-x="595" 
d="M540 83l-13 -89q-21 -6 -52 -6q-98 0 -98 87q-71 -87 -160 -87q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-78 -352q-5 -23 3.5 -36.5t28.5 -13.5q10 0 20 2zM261 81q36 0 70 19t56 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5z" />
    <glyph glyph-name="beta" unicode="&#x3b2;" horiz-adv-x="581" 
d="M294 -12q-112 0 -161 77l-56 -249h-105l144 635q26 114 105.5 170t172.5 56q92 0 144.5 -40t52.5 -113q0 -72 -55 -120.5t-124 -59.5q49 -8 84.5 -42t35.5 -97q0 -93 -72.5 -155t-165.5 -62zM221 451l-70 -307q33 -63 122 -63q69 0 111 36.5t42 91.5q0 41 -30.5 62.5
t-82.5 21.5h-37l21 93h34q63 0 104.5 31.5t41.5 85.5q0 37 -26.5 58.5t-72.5 21.5q-53 0 -98 -35t-59 -97z" />
    <glyph glyph-name="gamma" unicode="&#x3b3;" horiz-adv-x="503" 
d="M208 -184h-105l27 119q34 164 9 307.5t-95 240.5h120q43 -64 66 -168t12 -191q60 66 114.5 167t74.5 192h105q-27 -117 -105.5 -248t-179.5 -230z" />
    <glyph glyph-name="delta" unicode="&#x3b4;" horiz-adv-x="573" 
d="M260 -12q-113 0 -171.5 57t-58.5 145q0 77 50 141.5t129 96.5q-50 35 -50 93q0 63 62 110.5t155 47.5q119 0 182 -68l-60 -68q-45 52 -131 52q-45 0 -74.5 -19.5t-29.5 -46.5q0 -21 28.5 -37t69 -31t81 -36.5t69 -64.5t28.5 -103q0 -106 -79 -187.5t-200 -81.5zM435 247
q0 30 -10.5 52t-34.5 38.5t-42 25.5t-52 22q-73 -24 -114 -73.5t-41 -108.5q0 -52 32.5 -87t95.5 -35q70 0 118 49.5t48 116.5z" />
    <glyph glyph-name="epsilon" unicode="&#x3b5;" horiz-adv-x="508" 
d="M220 -12q-105 0 -157 34.5t-52 92.5t55 93t112 39q-45 7 -74.5 33.5t-29.5 65.5q0 66 71 107.5t176 41.5q130 0 200 -80l-65 -62q-47 56 -134 56q-62 0 -101.5 -19.5t-39.5 -50.5q0 -53 105 -53h103l-19 -84h-103q-143 0 -143 -72q0 -25 27.5 -40.5t77.5 -15.5
q106 0 186 59l36 -69q-99 -76 -231 -76z" />
    <glyph glyph-name="zeta" unicode="&#x3b6;" horiz-adv-x="487" 
d="M283 -33q0 21 -38 21q-103 0 -161 48t-58 131q0 117 96.5 222.5t263.5 185.5h-255l21 92h392l-18 -77q-175 -87 -281.5 -190.5t-106.5 -210.5q0 -53 37 -80.5t104 -27.5q63 0 91.5 -18t28.5 -56q0 -58 -84 -164h-117q85 85 85 124z" />
    <glyph glyph-name="eta" unicode="&#x3b7;" 
d="M403 -184h-105l109 487q5 22 5 30q0 35 -24.5 52t-65.5 17q-64 0 -129 -65l-74 -337h-105l107 483h105l-15 -66q82 78 165 78q68 0 108 -32.5t40 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="theta" unicode="&#x3b8;" horiz-adv-x="573" 
d="M264 -12q-102 0 -160 70t-58 195q0 106 37.5 202.5t110 160t162.5 63.5q102 0 159 -70.5t57 -195.5q0 -106 -37 -202.5t-109 -159.5t-162 -63zM270 81q58 0 103.5 60t70.5 148h-289q-3 -27 -3 -52q0 -71 29 -113.5t89 -42.5zM348 586q-57 0 -103 -58.5t-71 -146.5h288
q3 24 3 48q0 71 -28.5 114t-88.5 43z" />
    <glyph glyph-name="iota" unicode="&#x3b9;" horiz-adv-x="253" 
d="M133 -12q-124 0 -96 124l85 371h105l-81 -352q-5 -23 3.5 -36.5t28.5 -13.5q10 0 20 2l-13 -89q-21 -6 -52 -6z" />
    <glyph glyph-name="kappa" unicode="&#x3ba;" horiz-adv-x="526" 
d="M466 0h-127l-106 202l-86 -72l-28 -130h-105l107 483h105l-51 -227l259 227h135l-250 -219z" />
    <glyph glyph-name="lambda" unicode="&#x3bb;" horiz-adv-x="503" 
d="M140 577l6 94q26 8 60 8q60 -2 95.5 -26.5t45.5 -82.5l104 -570h-109l-60 362l-222 -362h-116l309 488l-11 55q-9 43 -60 43q-24 0 -42 -9z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="579" 
d="M122 24l-47 -208h-105l151 667h105l-68 -303q-11 -49 10.5 -74.5t64.5 -25.5q37 0 74 18.5t63 45.5l75 339h105l-78 -352q-5 -23 3.5 -36.5t28.5 -13.5q10 0 20 2l-13 -89q-21 -6 -52 -6q-97 0 -99 86q-33 -38 -76.5 -62t-86.5 -24q-57 0 -75 36z" />
    <glyph glyph-name="nu" unicode="&#x3bd;" horiz-adv-x="503" 
d="M246 0h-105l-87 483h112l60 -378q73 80 128.5 182t76.5 196h105q-27 -118 -107.5 -251t-182.5 -232z" />
    <glyph glyph-name="xi" unicode="&#x3be;" horiz-adv-x="487" 
d="M249 -12q-114 0 -172.5 40.5t-58.5 115.5q0 73 49.5 119t118.5 61q-38 7 -65.5 33t-27.5 72q0 44 39 86t94 60h-92l22 92h382l-22 -92h-189q-48 -12 -83 -45t-35 -70q0 -36 31.5 -55.5t83.5 -19.5h145l-21 -90h-141q-78 0 -126 -32.5t-48 -93.5q0 -42 38 -65t108 -23
q63 0 91.5 -18t28.5 -56q0 -58 -84 -164h-112q80 89 80 124q0 21 -34 21z" />
    <glyph glyph-name="omicron" unicode="&#x3bf;" horiz-adv-x="573" 
d="M264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="610" 
d="M467 0h-105l89 391h-222l-89 -391h-105l89 391h-80l21 92h592l-21 -92h-80z" />
    <glyph glyph-name="rho" unicode="&#x3c1;" horiz-adv-x="578" 
d="M294 -12q-50 0 -93 21.5t-67 59.5l-57 -253h-105l96 426q25 115 98.5 184t179.5 69q96 0 149 -58.5t53 -151.5q0 -114 -70.5 -205.5t-183.5 -91.5zM278 81q69 0 114.5 57.5t45.5 135.5q0 55 -28 91.5t-79 36.5q-60 0 -101 -44.5t-58 -115.5l-21 -96q17 -29 50.5 -47
t76.5 -18z" />
    <glyph glyph-name="sigma1" unicode="&#x3c2;" horiz-adv-x="488" 
d="M251 -12q-94 0 -156 59t-62 159q0 120 80.5 204.5t196.5 84.5q134 0 190 -93l-79 -60q-35 60 -110 60q-76 0 -123.5 -56.5t-47.5 -135.5q0 -129 156 -129q107 0 107 -77q0 -55 -84 -161h-114q85 85 85 123q0 22 -39 22z" />
    <glyph glyph-name="sigma" unicode="&#x3c3;" horiz-adv-x="573" 
d="M530 268q0 -111 -75 -195.5t-191 -84.5q-108 0 -169.5 60t-61.5 157q0 115 88 196.5t221 81.5h265l-21 -93h-101q45 -45 45 -122zM430 266q0 91 -52 124h-48q-84 0 -136 -54t-52 -126q0 -60 33 -94.5t92 -34.5q70 0 116.5 56.5t46.5 128.5z" />
    <glyph glyph-name="tau" unicode="&#x3c4;" horiz-adv-x="476" 
d="M286 -12q-67 0 -104 31.5t-23 96.5l61 275h-162l21 92h430l-21 -92h-163l-57 -258q-6 -26 9 -39t39 -13q25 0 40 13l6 -84q-28 -22 -76 -22z" />
    <glyph glyph-name="upsilon" unicode="&#x3c5;" horiz-adv-x="570" 
d="M258 -12q-119 0 -168 68t-24 175l58 252h105l-61 -266q-14 -63 8.5 -99.5t81.5 -36.5q78 0 131.5 74t53.5 174q0 75 -30 132l98 34q35 -63 35 -163q0 -145 -82 -244.5t-206 -99.5z" />
    <glyph glyph-name="phi" unicode="&#x3c6;" horiz-adv-x="773" 
d="M743 270q0 -114 -96 -194t-265 -87l-39 -173h-105l40 176q-118 13 -181 72.5t-63 156.5q0 92 66 162t179 112l26 -82q-76 -29 -118 -78.5t-42 -113.5q0 -121 154 -138l93 412h52q140 0 219.5 -60t79.5 -165zM475 399l-72 -316q116 10 172 62.5t56 124.5q0 58 -39.5 92
t-116.5 37z" />
    <glyph glyph-name="chi" unicode="&#x3c7;" horiz-adv-x="496" 
d="M395 -184h-112l-71 245l-184 -245h-119l255 343l-93 324h112l65 -226l168 226h120l-241 -324z" />
    <glyph glyph-name="psi" unicode="&#x3c8;" horiz-adv-x="773" 
d="M343 -184h-105l40 176q-217 22 -217 187q0 29 8 66l55 238h105l-57 -251q-5 -17 -5 -38q0 -45 34 -73.5t98 -36.5l133 583h105l-133 -583q78 9 129 47.5t66 100.5l57 251h105l-61 -266q-23 -99 -105.5 -160t-212.5 -68z" />
    <glyph glyph-name="omega" unicode="&#x3c9;" horiz-adv-x="833" 
d="M807 320q0 -133 -75 -232.5t-177 -99.5q-137 0 -156 141q-37 -67 -83 -104t-115 -37q-81 0 -126 56.5t-45 149.5q0 75 46 161t122 140l70 -66q-65 -47 -98.5 -109t-33.5 -126q0 -50 22 -81.5t66 -31.5q51 0 88 45.5t53 115.5l27 122h105l-27 -122q-16 -68 3 -114.5
t73 -46.5q71 0 114.5 72t43.5 166q0 77 -39 126l86 50q56 -59 56 -175z" />
    <glyph glyph-name="iotadieresis" unicode="&#x3ca;" horiz-adv-x="253" 
d="M368 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM159 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM133 -12q-124 0 -96 124l85 371h105l-81 -352
q-5 -23 3.5 -36.5t28.5 -13.5q10 0 20 2l-13 -89q-21 -6 -52 -6z" />
    <glyph glyph-name="upsilondieresis" unicode="&#x3cb;" horiz-adv-x="570" 
d="M541 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM332 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM258 -12q-119 0 -168 68t-24 175l58 252h105l-61 -266
q-14 -63 8.5 -99.5t81.5 -36.5q78 0 131.5 74t53.5 174q0 75 -30 132l98 34q35 -63 35 -163q0 -145 -82 -244.5t-206 -99.5z" />
    <glyph glyph-name="omicrontonos" unicode="&#x3cc;" horiz-adv-x="573" 
d="M377 558h-48l67 207h93zM264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5z
" />
    <glyph glyph-name="upsilontonos" unicode="&#x3cd;" horiz-adv-x="570" 
d="M386 558h-48l67 207h93zM258 -12q-119 0 -168 68t-24 175l58 252h105l-61 -266q-14 -63 8.5 -99.5t81.5 -36.5q78 0 131.5 74t53.5 174q0 75 -30 132l98 34q35 -63 35 -163q0 -145 -82 -244.5t-206 -99.5z" />
    <glyph glyph-name="omegatonos" unicode="&#x3ce;" horiz-adv-x="833" 
d="M507 558h-48l67 207h93zM807 320q0 -133 -75 -232.5t-177 -99.5q-137 0 -156 141q-37 -67 -83 -104t-115 -37q-81 0 -126 56.5t-45 149.5q0 75 46 161t122 140l70 -66q-65 -47 -98.5 -109t-33.5 -126q0 -50 22 -81.5t66 -31.5q51 0 88 45.5t53 115.5l27 122h105l-27 -122
q-16 -68 3 -114.5t73 -46.5q71 0 114.5 72t43.5 166q0 77 -39 126l86 50q56 -59 56 -175z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="577" 
d="M580 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -14.5t15 -37.5zM371 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -14.5t14.5 -37.5zM474 0h-457l147 667h457l-23 -103h-340l-38 -173
h333l-23 -103h-333l-41 -185h340z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="764" 
d="M447 -12l23 104q54 0 91.5 28t47.5 74l2 10q12 53 -16 82t-94 29q-75 0 -145 -25l-63 -290h-117l124 564h-202l22 103h521l-22 -103h-202l-38 -171q75 25 156 25q117 0 165 -61t28 -153l-2 -10q-22 -100 -99.5 -153t-179.5 -53z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="562" 
d="M622 867l-197 -144h-77l172 144h102zM134 0h-117l147 667h457l-23 -103h-340z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="682" 
d="M370 -12q-138 0 -228.5 81.5t-90.5 214.5q0 173 113 283.5t269 110.5q107 0 179.5 -48t106.5 -127l-112 -40q-23 54 -70.5 82.5t-109.5 28.5q-79 0 -145 -50t-94 -133h365l-23 -103h-357q1 -87 56.5 -141.5t147.5 -54.5q96 0 166 76l89 -62q-52 -62 -121.5 -90
t-140.5 -28z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="594" 
d="M294 -12q-93 0 -170.5 34t-117.5 88l75 86q35 -48 94.5 -76t128.5 -28q59 0 89.5 29t30.5 67q0 30 -33.5 52t-82 39.5t-96.5 38.5t-81.5 60t-33.5 92q0 84 69.5 145.5t180.5 61.5q79 0 147 -28.5t108 -78.5l-75 -83q-34 42 -87 64.5t-109 22.5q-46 0 -78 -25.5t-32 -59.5
q0 -27 34 -48.5t82 -39t96 -39t82 -60.5t34 -93q0 -91 -69 -156t-186 -65z" />
    <glyph glyph-name="afii10055" unicode="&#x406;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="259" 
d="M410 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -14.5t15 -37.5zM201 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -14.5t14.5 -37.5zM134 0h-117l147 667h117z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="481" 
d="M162 -12q-128 0 -188 79l65 86q38 -61 115 -61q105 0 132 121l100 454h117l-100 -456q-25 -114 -84.5 -168.5t-156.5 -54.5z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="1072" 
d="M-29 -12l23 104q58 0 96.5 45.5t86.5 186.5l114 343h453l-55 -249h152q93 0 145.5 -51.5t52.5 -128.5q0 -32 -8.5 -63.5t-28.5 -63.5t-49.5 -56t-76 -39.5t-104.5 -15.5h-291l124 564h-231l-83 -248q-34 -103 -70.5 -170t-77.5 -100.5t-80 -45.5t-92 -12zM822 315h-155
l-47 -212h157q65 0 102.5 35.5t37.5 88.5q0 37 -27 62.5t-68 25.5z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="1072" 
d="M784 0h-303l63 291h-347l-63 -291h-117l146 667h117l-60 -273h347l60 273h117l-60 -273h165q88 0 137.5 -48.5t49.5 -121.5q0 -38 -13.5 -75t-41 -71.5t-79 -56t-118.5 -21.5zM830 291h-169l-41 -188h172q57 0 90.5 31.5t33.5 78.5q0 33 -24 55.5t-62 22.5z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="790" 
d="M567 0l38 177q32 138 -104 138q-75 0 -145 -25l-63 -290h-117l124 564h-202l22 103h521l-22 -103h-202l-38 -171q75 25 156 25q120 0 166 -64.5t21 -176.5l-38 -177h-117z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="621" 
d="M637 867l-197 -144h-77l172 144h102zM560 0h-137l-165 286l-77 -73l-47 -213h-117l147 667h117l-68 -308l322 308h152l-340 -315z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="637" 
d="M94 -12q-42 0 -77.5 14t-48.5 32l62 96q34 -38 79 -38q35 0 60 18.5t60 65.5l5 8l-141 483h126l98 -389l270 389h140l-397 -542q-50 -68 -103.5 -102.5t-132.5 -34.5zM601 789q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="723" 
d="M220 -128l28 128h-231l146 667h117l-124 -564h347l124 564h117l-146 -667h-233l-28 -128h-117z" />
    <glyph glyph-name="afii10017" unicode="&#x410;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" horiz-adv-x="608" 
d="M163 667h457l-22 -103h-340l-33 -146h153q91 0 144 -52t53 -128q0 -40 -14 -79.5t-43.5 -76.5t-83 -59.5t-124.5 -22.5h-293zM358 315h-155l-47 -212h160q63 0 100 35.5t37 88.5q0 37 -27 62.5t-68 25.5z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="648" 
d="M357 0h-340l147 667h304q79 0 127.5 -45t48.5 -112q0 -68 -45.5 -119t-107.5 -57q44 -12 70.5 -51t26.5 -85q0 -80 -58.5 -139t-172.5 -59zM411 391q56 0 82 29t26 70q0 31 -24 52.5t-58 21.5h-179l-38 -173h191zM351 103q55 0 87 30.5t32 74.5q0 36 -23.5 58t-64.5 22
h-185l-41 -185h195z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="562" 
d="M134 0h-117l147 667h457l-23 -103h-340z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="732" 
d="M-53 -123l47 215q41 6 66 21t55 65.5t62 145.5l114 343h453l-124 -564h62l-50 -226h-117l27 123h-452l-27 -123h-116zM374 564l-83 -248q-50 -148 -135 -213h347l102 461h-231z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="933" 
d="M107 0h-154l368 352l-202 315h135l180 -300l66 300h117l-60 -275l286 275h155l-340 -315l214 -352h-134l-166 287l-48 -45l-53 -242h-117l53 241l-21 34z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="599" 
d="M287 -12q-94 0 -171.5 37.5t-109.5 101.5l80 69q28 -49 83 -76.5t117 -27.5q65 0 103 31.5t38 80.5q0 38 -35.5 61t-102.5 23h-132l23 103h151q64 0 106 24t42 71q0 38 -40 62.5t-105 24.5q-99 0 -170 -59l-44 80q40 38 100.5 60.5t123.5 22.5q114 0 184.5 -46t70.5 -126
q0 -67 -55 -113.5t-125 -53.5q54 -13 88.5 -49.5t34.5 -95.5q0 -87 -74 -146t-181 -59z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="720" 
d="M130 0h-113l147 667h117l-106 -481l447 481h120l-147 -667h-118l110 494z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="720" 
d="M130 0h-113l147 667h117l-106 -481l447 481h120l-147 -667h-118l110 494zM651 789q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="621" 
d="M560 0h-137l-165 286l-77 -73l-47 -213h-117l147 667h117l-68 -308l322 308h152l-340 -315z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="723" 
d="M-29 -12l23 104q58 0 96.5 45.5t86.5 186.5l114 343h453l-146 -667h-117l124 564h-231l-83 -248q-34 -103 -70.5 -170t-77.5 -100.5t-80 -45.5t-92 -12z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="835" 
d="M710 0h-117l113 515l-318 -515h-50l-91 515l-113 -515h-117l147 667h160l80 -459l283 459h170z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="723" 
d="M598 0h-117l64 291h-347l-64 -291h-117l147 667h117l-60 -273h347l60 273h117z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="723" 
d="M598 0h-117l124 564h-347l-124 -564h-117l147 667h581z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" horiz-adv-x="608" 
d="M134 0h-117l147 667h268q93 0 145.5 -51.5t52.5 -128.5q0 -32 -8.5 -63.5t-28.5 -63.5t-49.5 -56t-76 -39.5t-104.5 -15.5h-174zM211 352h160q63 0 100 35.5t37 88.5q0 37 -27 62.5t-68 25.5h-155z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="682" 
d="M370 -12q-138 0 -228.5 81.5t-90.5 214.5q0 173 113 283.5t269 110.5q107 0 179.5 -48t106.5 -127l-112 -40q-23 54 -70.5 82.5t-109.5 28.5q-103 0 -178.5 -81t-75.5 -202q0 -88 55.5 -143.5t148.5 -55.5q96 0 166 76l89 -62q-52 -62 -121.5 -90t-140.5 -28z" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="577" 
d="M293 0h-117l124 564h-202l23 103h521l-23 -103h-202z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="637" 
d="M94 -12q-42 0 -77.5 14t-48.5 32l62 96q34 -38 79 -38q35 0 60 18.5t60 65.5l5 8l-141 483h126l98 -389l270 389h140l-397 -542q-50 -68 -103.5 -102.5t-132.5 -34.5z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="834" 
d="M421 0h-117l15 68q-130 13 -196.5 73.5t-66.5 162.5q0 122 108 205t277 92l15 66h117l-16 -69q264 -29 264 -237q0 -122 -108.5 -205t-276.5 -91zM179 304q0 -54 42 -89t122 -44l74 324q-112 -11 -175 -62.5t-63 -128.5zM698 361q0 54 -42 89t-122 45l-74 -324
q111 11 174.5 62t63.5 128z" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="661" 
d="M603 0h-132l-135 256l-239 -256h-148l328 349l-166 318h133l123 -240l220 240h148l-309 -332z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="732" 
d="M515 -123l27 123h-525l146 667h117l-124 -564h347l124 564h117l-124 -564h62l-50 -226h-117z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="631" 
d="M261 667l-39 -177q-30 -138 105 -138q76 0 144 25l64 290h117l-146 -667h-117l60 274q-75 -25 -157 -25q-120 0 -166 64.5t-21 176.5l39 177h117z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="953" 
d="M857 667h117l-146 -667h-811l146 667h117l-124 -564h230l124 564h117l-124 -564h230z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="962" 
d="M857 667h117l-124 -564h62l-50 -226h-117l27 123h-755l146 667h117l-124 -564h230l124 564h117l-124 -564h230z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="767" 
d="M467 0h-291l124 564h-202l22 103h319l-55 -249h152q93 0 145.5 -51.5t52.5 -128.5q0 -32 -8.5 -63.5t-28.5 -63.5t-49.5 -56t-76 -39.5t-104.5 -15.5zM517 315h-155l-47 -212h157q65 0 102.5 35.5t37.5 88.5q0 37 -27 62.5t-68 25.5z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="832" 
d="M707 0h-117l146 667h117zM163 667h117l-55 -249h152q93 0 145.5 -51.5t52.5 -128.5q0 -32 -8.5 -63.5t-28.5 -63.5t-49.5 -56t-76 -39.5t-104.5 -15.5h-291zM358 315h-155l-47 -212h157q65 0 102.5 35.5t37.5 88.5q0 37 -27 62.5t-68 25.5z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" horiz-adv-x="608" 
d="M163 667h117l-55 -249h152q93 0 145.5 -51.5t52.5 -128.5q0 -32 -8.5 -63.5t-28.5 -63.5t-49.5 -56t-76 -39.5t-104.5 -15.5h-291zM358 315h-155l-47 -212h157q65 0 102.5 35.5t37.5 88.5q0 37 -27 62.5t-68 25.5z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="682" 
d="M350 678q138 0 228.5 -81.5t90.5 -214.5q0 -173 -113 -283.5t-269 -110.5q-107 0 -179.5 48t-106.5 127l112 40q23 -54 71 -82.5t109 -28.5q82 0 149.5 54t92.5 142h-365l23 103h353q-5 82 -60 132.5t-143 50.5q-96 0 -166 -76l-89 62q52 62 121.5 90t140.5 28z" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="1015" 
d="M619 -12q-138 0 -228.5 80.5t-90.5 215.5v7h-103l-63 -291h-117l146 667h117l-60 -273h94q34 125 135 204.5t233 79.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM626 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5
t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="afii10049" unicode="&#x42f;" horiz-adv-x="628" 
d="M137 0h-150l222 260q-48 16 -82.5 61.5t-34.5 107.5q0 32 8.5 63.5t28.5 63.5t49.5 56t76 39.5t104.5 15.5h291l-147 -667h-117l55 249h-101zM309 352h155l46 212h-159q-63 0 -100 -35.5t-37 -88.5q0 -37 27 -62.5t68 -25.5z" />
    <glyph glyph-name="afii10065" unicode="&#x430;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="577" 
d="M267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5zM264 -12q-108 0 -170 60t-62 157q0 54 17.5 114t38.5 98q52 96 122 145.5t179 62.5q111 16 117 42h103q-23 -105 -234 -128q-83 -9 -134 -44.5
t-73 -84.5q80 85 176 85q94 0 144.5 -60t50.5 -158q0 -116 -79 -202.5t-196 -86.5z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="572" 
d="M331 0h-317l106 483h283q57 0 89.5 -29t32.5 -76q0 -52 -30 -87t-78 -46q67 -30 67 -100q0 -59 -42 -102t-111 -43zM139 92h174q30 0 48.5 17.5t18.5 44.5q0 22 -15.5 34.5t-43.5 12.5h-158zM183 293h171q27 0 43.5 16t16.5 40q0 42 -53 42h-157z" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="452" 
d="M486 483l-21 -92h-261l-85 -391h-105l106 483h366z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="571" 
d="M-62 -123l45 210q34 8 59 40t56 121l81 235h373l-86 -391h53l-47 -215h-105l27 123h-325l-27 -123h-104zM251 391l-51 -155q-32 -98 -95 -144h256l65 299h-175z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="557" 
d="M277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5
q-56 0 -98.5 -38t-52.5 -90z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="731" 
d="M91 0h-139l263 264l-153 219h123l136 -198l44 198h105l-40 -181l206 181h137l-249 -219l147 -264h-125l-107 203l-38 -32l-37 -171h-105l37 170l-18 25z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="508" 
d="M293 495q204 0 204 -124q0 -58 -53 -93.5t-109 -39.5q46 -7 77.5 -33t31.5 -65q0 -65 -69 -108.5t-166 -43.5q-72 0 -129 22t-88 58l63 64q58 -58 155 -58q52 0 89.5 21.5t37.5 51.5q0 55 -97 55h-116l19 84h103q138 0 138 70q0 53 -100 53q-94 0 -179 -58l-43 68
q99 76 231 76z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="568" 
d="M115 0h-101l106 483h105l-72 -325l294 325h105l-106 -483h-105l73 334z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="568" 
d="M115 0h-101l106 483h105l-72 -325l294 325h105l-106 -483h-105l73 334zM537 622q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="526" 
d="M466 0h-127l-106 202l-86 -72l-28 -130h-105l107 483h105l-51 -227l259 227h135l-250 -219z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="568" 
d="M-39 -12l19 86q31 0 58 39.5t60 134.5l81 235h373l-106 -483h-105l85 391h-175l-51 -155q-42 -130 -100 -189t-139 -59z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="677" 
d="M555 0h-105l73 336l-217 -336h-42l-72 336l-73 -336h-105l106 483h132l65 -312l201 312h143z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" 
d="M119 0h-105l110 483h105l-42 -185h222l42 185h105l-110 -483h-105l47 206h-222z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" horiz-adv-x="573" 
d="M264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" 
d="M446 0h-105l89 391h-222l-89 -391h-105l110 483h432z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" horiz-adv-x="578" 
d="M294 -12q-108 0 -160 81l-56 -253h-105l148 667h105l-15 -64q64 76 152 76q83 0 134 -53t51 -147q0 -120 -70 -213.5t-184 -93.5zM278 81q69 0 114.5 57.5t45.5 135.5q0 58 -33.5 93t-85.5 35q-74 0 -127 -69l-41 -187q17 -29 50.5 -47t76.5 -18z" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="497" 
d="M264 -12q-104 0 -167.5 59.5t-63.5 158.5q0 120 80.5 204.5t196.5 84.5q134 0 190 -93l-79 -60q-35 60 -110 60q-76 0 -123.5 -56.5t-47.5 -135.5q0 -61 37.5 -95t96.5 -34q65 0 110 52l58 -70q-71 -75 -178 -75z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="418" 
d="M207 0h-105l85 391h-138l21 92h382l-21 -92h-139z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="503" 
d="M-47 -185l35 92q18 -8 48 -8q38 0 65 40l38 58l-88 486h109l60 -362l222 362h116l-360 -570q-37 -58 -77 -83.5t-95 -25.5q-42 0 -73 11z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="833" 
d="M265 495q42 0 78.5 -21t57.5 -58l55 251h105l-55 -250q56 78 138 78q71 0 108 -54t37 -133q0 -78 -26.5 -150.5t-79.5 -121t-118 -48.5q-42 0 -78.5 21t-57.5 58l-55 -251h-105l55 250q-53 -78 -138 -78q-71 0 -108 54t-37 133q0 78 26.5 150.5t79.5 121t118 48.5z
M287 402q-59 0 -98 -65.5t-39 -142.5q0 -49 22 -81t64 -32q57 0 106 65l42 191q-11 27 -37.5 46t-59.5 19zM543 81q59 0 98 65.5t39 142.5q0 49 -22 81t-64 32q-58 0 -105 -65l-42 -191q10 -27 36.5 -46t59.5 -19z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="496" 
d="M437 0h-114l-86 178l-165 -178h-121l231 248l-114 235h114l78 -164l151 164h122l-220 -235z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="571" 
d="M367 -123l27 123h-380l106 483h105l-86 -391h222l86 391h105l-86 -391h53l-47 -215h-105z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="550" 
d="M103 483h105l-29 -130q-12 -54 11 -76t83 -22q64 0 110 18l46 210h105l-106 -483h-105l41 188q-58 -26 -145 -26q-99 0 -134.5 38t-18.5 115z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="791" 
d="M670 483h105l-106 -483h-655l106 483h105l-86 -391h170l86 391h105l-86 -391h170z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="804" 
d="M670 483h105l-86 -391h53l-47 -215h-105l27 123h-603l106 483h105l-86 -391h170l86 391h105l-86 -391h170z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="628" 
d="M388 0h-286l85 391h-138l21 92h243l-39 -175h143q72 0 113.5 -37t41.5 -97q0 -71 -50.5 -122.5t-133.5 -51.5zM398 216h-144l-27 -124h162q34 0 55 20t21 51q0 25 -17.5 39t-49.5 14z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="750" 
d="M628 0h-105l106 483h105zM120 483h105l-39 -175h143q72 0 113.5 -37t41.5 -97q0 -71 -50.5 -122.5t-133.5 -51.5h-286zM310 216h-144l-27 -124h162q34 0 55 20t21 51q0 25 -17.5 39t-49.5 14z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="540" 
d="M120 483h105l-39 -175h143q72 0 113.5 -37t41.5 -97q0 -71 -50.5 -122.5t-133.5 -51.5h-286zM310 216h-144l-27 -124h162q34 0 55 20t21 51q0 25 -17.5 39t-49.5 14z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="497" 
d="M232 495q104 0 167.5 -59.5t63.5 -158.5q0 -120 -80.5 -204.5t-196.5 -84.5q-134 0 -190 93l73 55q16 -28 47.5 -45t66.5 -17q58 0 100.5 34.5t60.5 89.5h-222l20 91h213q-7 56 -43 87.5t-89 31.5q-71 0 -116 -52l-53 64q71 75 178 75z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="789" 
d="M480 -12q-107 0 -168.5 58.5t-63.5 154.5h-85l-44 -201h-105l106 483h105l-42 -190h78q26 88 97.5 145t164.5 57q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM483 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134
q0 -60 33.5 -94.5t92.5 -34.5z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" horiz-adv-x="561" 
d="M122 0h-125l163 186q-86 32 -86 123q0 71 50.5 122.5t133.5 51.5h287l-106 -483h-105l38 175h-106zM248 267h144l27 124h-162q-34 0 -55 -20t-21 -51q0 -25 17.5 -39t49.5 -14z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="557" 
d="M529 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM320 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5
t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5q-56 0 -98.5 -38t-52.5 -90z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="571" 
d="M388 531h-150l-25 -114q35 32 85 55t103 23q155 0 123 -153l-82 -370q-17 -79 -68 -123.5t-129 -44.5q-40 0 -61.5 8t-44.5 27l48 78q25 -27 61 -27q32 0 56 20.5t33 61.5l73 332q12 53 -8 75.5t-68 22.5q-75 0 -138 -65l-74 -337h-105l116 531h-62l12 62h63l17 74h105
l-17 -74h149z" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="452" 
d="M536 700l-197 -144h-77l172 144h102zM486 483l-21 -92h-261l-85 -391h-105l106 483h366z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="497" 
d="M264 -12q-104 0 -167.5 59.5t-63.5 158.5q0 120 80.5 204.5t196.5 84.5q134 0 190 -93l-73 -55q-16 28 -47.5 45t-66.5 17q-56 0 -98.5 -33.5t-62.5 -86.5h223l-20 -91h-214q5 -58 41.5 -90.5t90.5 -32.5q71 0 116 52l53 -64q-71 -75 -178 -75z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="472" 
d="M214 -12q-141 0 -218 86l62 73q22 -30 68.5 -53t96.5 -23q39 0 63 19t24 46q0 25 -39 44.5t-85.5 32.5t-85.5 45t-39 80q0 65 51.5 111t140.5 46q60 0 112.5 -22t83.5 -57l-56 -69q-18 25 -59 44.5t-84 19.5q-39 0 -62 -16t-23 -41q0 -19 25.5 -33.5t62.5 -27t74 -27.5
t62.5 -44t25.5 -69q0 -70 -54.5 -117.5t-146.5 -47.5z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="241" 
d="M193 542q-25 0 -42 16.5t-17 39.5q0 33 23 53.5t50 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23 -53.5t-50 -20.5zM119 0h-105l107 483h105z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="241" 
d="M365 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM156 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM119 0h-105l107 483h105z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="241" 
d="M-60 -196q-85 0 -126 41l46 75q24 -30 65 -30q63 0 82 82l114 511h105l-114 -511q-18 -82 -59 -125t-113 -43zM193 542q-25 0 -42 16.5t-17 39.5q0 33 23 53.5t50 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23 -53.5t-50 -20.5z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="867" 
d="M-39 -12l19 86q31 0 58 39.5t60 134.5l81 235h373l-39 -175h143q72 0 113.5 -37t41.5 -97q0 -71 -50.5 -122.5t-133.5 -51.5h-286l85 391h-175l-51 -155q-42 -130 -100 -189t-139 -59zM637 216h-144l-27 -124h162q34 0 55 20t21 51q0 25 -17.5 39t-49.5 14z" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="867" 
d="M119 0h-105l106 483h105l-42 -190h222l42 190h105l-42 -190h153q68 0 107.5 -35t39.5 -92q0 -68 -47.5 -117t-127.5 -49h-294l44 201h-222zM643 201h-153l-24 -109h169q30 0 48.5 17.5t18.5 44.5q0 22 -15.5 34.5t-43.5 12.5z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="568" 
d="M446 0h-105l66 303q5 20 5 30q0 35 -24.5 52t-65.5 17q-65 0 -129 -66l-74 -336h-105l117 531h-61l14 62h60l17 74h105l-17 -74h152l-14 -62h-151l-25 -114q82 78 165 78q68 0 108 -32.5t40 -89.5q0 -15 -5 -40z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" horiz-adv-x="526" 
d="M551 700l-197 -144h-77l172 144h102zM466 0h-127l-106 202l-86 -72l-28 -130h-105l107 483h105l-51 -227l259 227h135l-250 -219z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="503" 
d="M-47 -185l35 92q18 -8 48 -8q38 0 65 40l38 58l-88 486h109l60 -362l222 362h116l-360 -570q-37 -58 -77 -83.5t-95 -25.5q-42 0 -73 11zM503 622q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="568" 
d="M150 -127l28 127h-164l106 483h105l-86 -391h222l86 391h105l-106 -483h-163l-28 -127h-105z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="562" 
d="M134 0h-117l146 667h340l27 123h117l-49 -226h-340z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="452" 
d="M204 391l-85 -391h-105l106 483h261l28 127h105l-49 -219h-261z" />
    <glyph glyph-name="uni1E02" unicode="&#x1e02;" horiz-adv-x="648" 
d="M357 0h-340l147 667h304q79 0 127.5 -45t48.5 -112q0 -68 -45.5 -119t-107.5 -57q44 -12 70.5 -51t26.5 -85q0 -80 -58.5 -139t-172.5 -59zM411 391q56 0 82 29t26 70q0 31 -24 52.5t-58 21.5h-179l-38 -173h191zM351 103q55 0 87 30.5t32 74.5q0 36 -23.5 58t-64.5 22
h-185l-41 -185h195zM498 780q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1E03" unicode="&#x1e03;" horiz-adv-x="581" 
d="M294 -12q-108 0 -160 81l-15 -69h-105l147 667h105l-55 -248q64 76 152 76q83 0 134 -53t51 -147q0 -120 -70 -213.5t-184 -93.5zM278 81q69 0 114.5 57.5t45.5 135.5q0 58 -33.5 93t-85.5 35q-74 0 -127 -69l-41 -187q17 -29 50.5 -47t76.5 -18zM477 780
q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1E0A" unicode="&#x1e0a;" horiz-adv-x="710" 
d="M264 0h-247l147 667h243q115 0 199.5 -81t84.5 -205q0 -55 -15 -108t-48.5 -103t-82 -87.5t-121 -60t-160.5 -22.5zM281 103h4q128 0 206 79t78 192q0 81 -52 135.5t-132 54.5h-127l-102 -461h125zM535 780q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5
q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1E0B" unicode="&#x1e0b;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l56 253h105l-148 -667h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM461 780q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1E1E" unicode="&#x1e1e;" horiz-adv-x="562" 
d="M476 780q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5zM134 0h-117l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333z" />
    <glyph glyph-name="uni1E1F" unicode="&#x1e1f;" horiz-adv-x="308" 
d="M407 780q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5zM145 0h-105l86 392h-80l21 91h79l6 27q17 76 60 121.5t116 45.5t118 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98z
" />
    <glyph glyph-name="uni1E22" unicode="&#x1e22;" horiz-adv-x="723" 
d="M598 0h-117l64 291h-347l-64 -291h-117l147 667h117l-60 -273h347l60 273h117zM537 780q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1E23" unicode="&#x1e23;" horiz-adv-x="568" 
d="M446 0h-105l66 303q5 20 5 30q0 35 -24.5 52t-65.5 17q-65 0 -129 -66l-74 -336h-105l147 667h105l-55 -250q82 78 165 78q68 0 108 -32.5t40 -89.5q0 -15 -5 -40zM463 780q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5
t15.5 -37.5z" />
    <glyph glyph-name="uni1E56" unicode="&#x1e56;" horiz-adv-x="608" 
d="M134 0h-117l147 667h268q93 0 145.5 -51.5t52.5 -128.5q0 -32 -8.5 -63.5t-28.5 -63.5t-49.5 -56t-76 -39.5t-104.5 -15.5h-174zM211 352h160q63 0 100 35.5t37 88.5q0 37 -27 62.5t-68 25.5h-155zM492 780q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5
q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1E57" unicode="&#x1e57;" horiz-adv-x="578" 
d="M294 -12q-108 0 -160 81l-56 -253h-105l148 667h105l-15 -64q64 76 152 76q83 0 134 -53t51 -147q0 -120 -70 -213.5t-184 -93.5zM278 81q69 0 114.5 57.5t45.5 135.5q0 58 -33.5 93t-85.5 35q-74 0 -127 -69l-41 -187q17 -29 50.5 -47t76.5 -18zM440 613
q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="uni1E60" unicode="&#x1e60;" horiz-adv-x="594" 
d="M294 -12q-93 0 -170.5 34t-117.5 88l75 86q35 -48 94.5 -76t128.5 -28q59 0 89.5 29t30.5 67q0 30 -33.5 52t-82 39.5t-96.5 38.5t-81.5 60t-33.5 92q0 84 69.5 145.5t180.5 61.5q79 0 147 -28.5t108 -78.5l-75 -83q-34 42 -87 64.5t-109 22.5q-46 0 -78 -25.5t-32 -59.5
q0 -27 34 -48.5t82 -39t96 -39t82 -60.5t34 -93q0 -91 -69 -156t-186 -65zM478 780q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1E61" unicode="&#x1e61;" horiz-adv-x="472" 
d="M214 -12q-141 0 -218 86l62 73q22 -30 68.5 -53t96.5 -23q39 0 63 19t24 46q0 25 -39 44.5t-85.5 32.5t-85.5 45t-39 80q0 65 51.5 111t140.5 46q60 0 112.5 -22t83.5 -57l-56 -69q-18 25 -59 44.5t-84 19.5q-39 0 -62 -16t-23 -41q0 -19 25.5 -33.5t62.5 -27t74 -27.5
t62.5 -44t25.5 -69q0 -70 -54.5 -117.5t-146.5 -47.5zM378 613q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="uni1E6A" unicode="&#x1e6a;" horiz-adv-x="577" 
d="M293 0h-117l124 564h-202l23 103h521l-23 -103h-202zM466 780q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1E6B" unicode="&#x1e6b;" horiz-adv-x="319" 
d="M187 -12q-60 0 -94.5 24.5t-34.5 73.5q0 14 3 31l61 275h-80l21 91h80l29 132h105l-29 -132h98l-21 -91h-98l-55 -249q-2 -10 -2 -19q0 -43 48 -43q25 0 40 13l6 -84q-28 -22 -77 -22zM321 780q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20
q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="903" 
d="M652 0h-125l-21 492l-238 -492h-125l-44 667h128l16 -514l252 514h93l24 -514l243 514h134zM615 723h-75l-133 144h100z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="754" 
d="M545 0h-110l-34 354l-190 -354h-110l-43 483h105l24 -351l193 351h93l36 -351l180 351h113zM504 556h-75l-133 144h100z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="903" 
d="M763 867l-197 -144h-77l172 144h102zM652 0h-125l-21 492l-238 -492h-125l-44 667h128l16 -514l252 514h93l24 -514l243 514h134z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="754" 
d="M655 700l-197 -144h-77l172 144h102zM545 0h-110l-34 354l-190 -354h-110l-43 483h105l24 -351l193 351h93l36 -351l180 351h113z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="903" 
d="M728 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -14.5t15 -37.5zM519 781q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -14.5t14.5 -37.5zM652 0h-125l-21 492l-238 -492h-125l-44 667h128
l16 -514l252 514h93l24 -514l243 514h134z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="754" 
d="M619 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM410 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM545 0h-110l-34 354l-190 -354h-110l-43 483h105l24 -351
l193 351h93l36 -351l180 351h113z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM310 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM262 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM409 755l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM324 585l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="673" 
d="M568 682h-65l-56 72l-96 -72h-68l125 108h96zM617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM817 844l-197 -108h-77l172 108h102z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM498 556h-65l-56 96l-96 -96h-68l125 144h96zM747 770l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="673" 
d="M569 682h-65l-56 72l-96 -72h-68l125 108h96zM617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM337 736h-75l-133 108h100z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM498 556h-65l-56 96l-96 -96h-68l125 144h96zM267 626h-75l-133 144h100z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="673" 
d="M569 682h-65l-56 72l-96 -72h-68l125 108h96zM617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM512 800l-35 13q25 35 78 35q29 0 50 -12t21 -33q0 -26 -26 -47h-51q32 21 32 43q0 9 -9 15t-21 6q-23 0 -39 -20z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM498 556h-65l-56 96l-96 -96h-68l125 144h96zM431 725l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="673" 
d="M568 678h-65l-56 48l-96 -48h-68l125 72h96zM617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM511 757q-39 0 -68.5 19.5t-51.5 19.5q-36 0 -52 -36h-58q26 67 118 67q39 0 68.5 -19.5t51.5 -19.5q35 0 52 36h59q-29 -67 -119 -67z
" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM490 523h-65l-56 96l-96 -96h-68l125 144h96zM455 682q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM582 723h-65l-56 96l-96 -96h-68l125 144h96zM310 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM498 556h-65l-56 96l-96 -96h-68l125 144h96zM262 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM646 837l-197 -90h-77l172 90h102zM617 744q-86 -66 -194 -66q-109 0 -157 66l55 30q33 -49 113 -49q78 0 142 49z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM562 773l-197 -144h-77l172 144h102zM536 604q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM522 753h-75l-133 90h100zM617 744q-86 -66 -194 -66q-109 0 -157 66l55 30q33 -49 113 -49q78 0 142 49z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM448 629h-75l-133 144h100zM538 609q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM414 783l-35 11q27 30 78 30q28 0 49.5 -10t21.5 -28q0 -23 -26 -39h-51q32 17 32 35t-30 18q-23 0 -39 -17zM617 744q-86 -66 -194 -66q-109 0 -157 66l55 30q33 -49 113 -49
q78 0 142 49z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM341 695l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27zM538 609q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM614 729q-83 -51 -194 -51t-157 51l55 23q33 -38 113 -38t142 38zM511 759q-39 0 -68.5 19.5t-51.5 19.5q-36 0 -52 -36h-58q26 67 118 67q39 0 68.5 -19.5t51.5 -19.5q35 0 52 36
h59q-29 -67 -119 -67z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM538 609q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66zM454 677q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59
q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="673" 
d="M617 0h-127l-21 128h-306l-77 -128h-139l409 667h146zM459 231l-50 318l-190 -318h240zM310 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5zM625 789q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40
q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="581" 
d="M286 495q51 0 93.5 -21.5t66.5 -59.5l15 69h105l-107 -483h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187
q-17 29 -50.5 47.5t-76.5 18.5zM262 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5zM541 622q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM275 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="557" 
d="M277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5
q-56 0 -98.5 -38t-52.5 -90zM261 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM374 755l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="557" 
d="M277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5
q-56 0 -98.5 -38t-52.5 -90zM322 585l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM477 721q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="557" 
d="M277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5
q-56 0 -98.5 -38t-52.5 -90zM424 554q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="577" 
d="M533 682h-65l-56 72l-96 -72h-68l125 108h96zM782 844l-197 -108h-77l172 108h102zM474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="557" 
d="M495 556h-65l-56 96l-96 -96h-68l125 144h96zM744 770l-197 -144h-77l172 144h102zM277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40
l33 -73q-72 -53 -170 -53zM150 281h277q1 3 1 14q0 49 -33 81.5t-94 32.5q-56 0 -98.5 -38t-52.5 -90z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="577" 
d="M533 682h-65l-56 72l-96 -72h-68l125 108h96zM474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM300 736h-75l-133 108h100z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="557" 
d="M497 556h-65l-56 96l-96 -96h-68l125 144h96zM277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277
q1 3 1 14q0 49 -33 81.5t-94 32.5q-56 0 -98.5 -38t-52.5 -90zM266 626h-75l-133 144h100z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="577" 
d="M532 682h-65l-56 72l-96 -72h-68l125 108h96zM474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM474 800l-35 13q25 35 78 35q29 0 50 -12t21 -33q0 -26 -26 -47h-51q32 21 32 43q0 9 -9 15t-21 6q-23 0 -39 -20z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="557" 
d="M496 556h-65l-56 96l-96 -96h-68l125 144h96zM277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277
q1 3 1 14q0 49 -33 81.5t-94 32.5q-56 0 -98.5 -38t-52.5 -90zM429 725l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="577" 
d="M536 678h-65l-56 48l-96 -48h-68l125 72h96zM474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM477 757q-39 0 -68.5 19.5t-51.5 19.5q-36 0 -52 -36h-58q26 67 118 67q39 0 68.5 -19.5t51.5 -19.5q35 0 52 36h59q-29 -67 -119 -67z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="557" 
d="M490 523h-65l-56 96l-96 -96h-68l125 144h96zM277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277
q1 3 1 14q0 49 -33 81.5t-94 32.5q-56 0 -98.5 -38t-52.5 -90zM453 682q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="577" 
d="M547 723h-65l-56 96l-96 -96h-68l125 144h96zM474 0h-457l147 667h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-41 -185h340zM275 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="557" 
d="M497 556h-65l-56 96l-96 -96h-68l125 144h96zM277 -12q-113 0 -178.5 58t-65.5 158q0 120 79.5 205.5t196.5 85.5q95 0 154 -58.5t59 -155.5q0 -40 -9 -74h-374q0 -1 -0.5 -7.5t-0.5 -9.5q0 -45 39 -80.5t108 -35.5q77 0 129 40l33 -73q-72 -53 -170 -53zM150 281h277
q1 3 1 14q0 49 -33 81.5t-94 32.5q-56 0 -98.5 -38t-52.5 -90zM261 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM203 755l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="241" 
d="M119 0h-105l107 483h105zM158 585l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM105 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="241" 
d="M193 542q-25 0 -42 16.5t-17 39.5q0 33 23 53.5t50 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23 -53.5t-50 -20.5zM119 0h-105l107 483h105zM98 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z
M359 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" horiz-adv-x="573" 
d="M264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5zM262 -126
q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z
M458 755l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" horiz-adv-x="573" 
d="M264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5zM322 585l-35 18
q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="765" 
d="M616 682h-65l-56 72l-96 -72h-68l125 108h96zM865 844l-197 -108h-77l172 108h102zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5
q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" horiz-adv-x="573" 
d="M497 556h-65l-56 96l-96 -96h-68l125 144h96zM746 770l-197 -144h-77l172 144h102zM264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5
q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="765" 
d="M616 682h-65l-56 72l-96 -72h-68l125 108h96zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5
t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5zM383 736h-75l-133 108h100z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" horiz-adv-x="573" 
d="M498 556h-65l-56 96l-96 -96h-68l125 144h96zM264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134
q0 -60 33.5 -94.5t92.5 -34.5zM267 626h-75l-133 144h100z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="765" 
d="M615 682h-65l-56 72l-96 -72h-68l125 108h96zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5
t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5zM557 800l-35 13q25 35 78 35q29 0 50 -12t21 -33q0 -26 -26 -47h-51q32 21 32 43q0 9 -9 15t-21 6q-23 0 -39 -20z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" horiz-adv-x="573" 
d="M497 556h-65l-56 96l-96 -96h-68l125 144h96zM264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134
q0 -60 33.5 -94.5t92.5 -34.5zM430 725l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="765" 
d="M610 678h-65l-56 48l-96 -48h-68l125 72h96zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5
t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5zM559 757q-39 0 -68.5 19.5t-51.5 19.5q-36 0 -52 -36h-58q26 67 118 67q39 0 68.5 -19.5t51.5 -19.5q35 0 52 36h59q-29 -67 -119 -67z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" horiz-adv-x="573" 
d="M490 523h-65l-56 96l-96 -96h-68l125 144h96zM264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134
q0 -60 33.5 -94.5t92.5 -34.5zM455 682q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="765" 
d="M630 723h-65l-56 96l-96 -96h-68l125 144h96zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q138 0 228.5 -80.5t90.5 -214.5q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5
t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5zM359 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" horiz-adv-x="573" 
d="M496 556h-65l-56 96l-96 -96h-68l125 144h96zM264 -12q-108 0 -170 60t-62 157q0 116 79 203t196 87q108 0 170 -60.5t62 -157.5q0 -116 -79 -202.5t-196 -86.5zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134
q0 -60 33.5 -94.5t92.5 -34.5zM262 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="765" 
d="M700 867l-197 -144h-77l172 144h102zM370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q163 0 253 -106q50 28 63 65h-7q-16 0 -27.5 11.5t-11.5 30.5q0 22 17 38.5t39 16.5q21 0 35 -14.5t14 -39.5q0 -40 -28 -79.5t-70 -61.5q42 -67 42 -156
q0 -166 -110.5 -280.5t-271.5 -114.5zM377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" horiz-adv-x="573" 
d="M565 700l-197 -144h-77l172 144h102zM589 547q0 -38 -26 -76t-65 -61q41 -57 41 -133q0 -116 -79 -202.5t-196 -86.5q-108 0 -170 60t-62 157q0 116 79 203t196 87q102 0 163 -55q47 29 60 64q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14
t14 -39zM267 81q70 0 116.5 57.5t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q163 0 253 -106q50 28 63 65h-7q-16 0 -27.5 11.5t-11.5 30.5q0 22 17 38.5t39 16.5q21 0 35 -14.5t14 -39.5q0 -40 -28 -79.5t-70 -61.5q42 -67 42 -156q0 -166 -110.5 -280.5t-271.5 -114.5z
M377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5zM551 723h-75l-133 144h100z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" horiz-adv-x="573" 
d="M589 547q0 -38 -26 -76t-65 -61q41 -57 41 -133q0 -116 -79 -202.5t-196 -86.5q-108 0 -170 60t-62 157q0 116 79 203t196 87q102 0 163 -55q47 29 60 64q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39zM267 81q70 0 116.5 57.5
t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5zM417 556h-75l-133 144h100z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q163 0 253 -106q50 28 63 65h-7q-16 0 -27.5 11.5t-11.5 30.5q0 22 17 38.5t39 16.5q21 0 35 -14.5t14 -39.5q0 -40 -28 -79.5t-70 -61.5q42 -67 42 -156q0 -166 -110.5 -280.5t-271.5 -114.5z
M377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5zM458 755l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" horiz-adv-x="573" 
d="M589 547q0 -38 -26 -76t-65 -61q41 -57 41 -133q0 -116 -79 -202.5t-196 -86.5q-108 0 -170 60t-62 157q0 116 79 203t196 87q102 0 163 -55q47 29 60 64q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39zM267 81q70 0 116.5 57.5
t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5zM323 585l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q163 0 253 -106q50 28 63 65h-7q-16 0 -27.5 11.5t-11.5 30.5q0 22 17 38.5t39 16.5q21 0 35 -14.5t14 -39.5q0 -40 -28 -79.5t-70 -61.5q42 -67 42 -156q0 -166 -110.5 -280.5t-271.5 -114.5z
M377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5zM560 721q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59
q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" horiz-adv-x="573" 
d="M589 547q0 -38 -26 -76t-65 -61q41 -57 41 -133q0 -116 -79 -202.5t-196 -86.5q-108 0 -170 60t-62 157q0 116 79 203t196 87q102 0 163 -55q47 29 60 64q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39zM267 81q70 0 116.5 57.5
t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5zM404 554q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="765" 
d="M370 -12q-138 0 -228.5 80.5t-90.5 215.5q0 165 110.5 279.5t271.5 114.5q163 0 253 -106q50 28 63 65h-7q-16 0 -27.5 11.5t-11.5 30.5q0 22 17 38.5t39 16.5q21 0 35 -14.5t14 -39.5q0 -40 -28 -79.5t-70 -61.5q42 -67 42 -156q0 -166 -110.5 -280.5t-271.5 -114.5z
M377 92q105 0 179 81.5t74 201.5q0 91 -58 145t-146 54q-105 0 -179 -81.5t-74 -201.5q0 -92 57.5 -145.5t146.5 -53.5zM359 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" horiz-adv-x="573" 
d="M589 547q0 -38 -26 -76t-65 -61q41 -57 41 -133q0 -116 -79 -202.5t-196 -86.5q-108 0 -170 60t-62 157q0 116 79 203t196 87q102 0 163 -55q47 29 60 64q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39zM267 81q70 0 116.5 57.5
t46.5 134.5q0 60 -33.5 94.5t-92.5 34.5q-70 0 -116.5 -58t-46.5 -134q0 -60 33.5 -94.5t92.5 -34.5zM262 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="719" 
d="M347 -12q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-29 -133 -100 -204.5t-205 -71.5zM334 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46
t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" 
d="M121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52t65.5 -17q63 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM259 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20
q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="719" 
d="M347 -12q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-89 -403q-29 -133 -100 -204.5t-205 -71.5zM433 755l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51
q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" 
d="M121 483h105l-67 -302q-5 -22 -5 -31q0 -35 24.5 -52t65.5 -17q63 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM321 585l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20
t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="719" 
d="M676 867l-197 -144h-77l172 144h102zM865 743q0 -51 -40.5 -95t-102.5 -68l-70 -316q-29 -133 -100 -204.5t-205 -71.5q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-9 -39q61 32 74 72
q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" 
d="M563 700l-197 -144h-77l172 144h102zM656 556q0 -46 -34 -86.5t-88 -66.5l-89 -403h-105l15 65q-81 -77 -165 -77q-68 0 -108.5 32.5t-40.5 88.5q0 19 6 41l73 333h105l-67 -303q-4 -18 -4 -30q0 -35 24.5 -52t65.5 -17q63 0 128 65l75 337h105l-6 -28q41 28 51 58h-7
q-16 0 -28 11.5t-12 30.5q0 22 17 38.5t39 16.5q21 0 35.5 -14.5t14.5 -39.5z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="719" 
d="M526 723h-75l-133 144h100zM865 743q0 -51 -40.5 -95t-102.5 -68l-70 -316q-29 -133 -100 -204.5t-205 -71.5q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-9 -39q61 32 74 72
q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" 
d="M414 556h-75l-133 144h100zM656 556q0 -46 -34 -86.5t-88 -66.5l-89 -403h-105l15 65q-81 -77 -165 -77q-68 0 -108.5 32.5t-40.5 88.5q0 19 6 41l73 333h105l-67 -303q-4 -18 -4 -30q0 -35 24.5 -52t65.5 -17q63 0 128 65l75 337h105l-6 -28q41 28 51 58h-7
q-16 0 -28 11.5t-12 30.5q0 22 17 38.5t39 16.5q21 0 35.5 -14.5t14.5 -39.5z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="719" 
d="M433 755l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27zM865 743q0 -51 -40.5 -95t-102.5 -68l-70 -316q-29 -133 -100 -204.5t-205 -71.5q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401
q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-9 -39q61 32 74 72q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" 
d="M321 585l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27zM656 556q0 -46 -34 -86.5t-88 -66.5l-89 -403h-105l15 65q-81 -77 -165 -77q-68 0 -108.5 32.5t-40.5 88.5q0 19 6 41l73 333h105l-67 -303
q-4 -18 -4 -30q0 -35 24.5 -52t65.5 -17q63 0 128 65l75 337h105l-6 -28q41 28 51 58h-7q-16 0 -28 11.5t-12 30.5q0 22 17 38.5t39 16.5q21 0 35.5 -14.5t14.5 -39.5z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="719" 
d="M865 743q0 -51 -40.5 -95t-102.5 -68l-70 -316q-29 -133 -100 -204.5t-205 -71.5q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-9 -39q61 32 74 72q-1 -1 -7 -1q-16 0 -27.5 11.5
t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39zM535 721q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" 
d="M656 556q0 -46 -34 -86.5t-88 -66.5l-89 -403h-105l15 65q-81 -77 -165 -77q-68 0 -108.5 32.5t-40.5 88.5q0 19 6 41l73 333h105l-67 -303q-4 -18 -4 -30q0 -35 24.5 -52t65.5 -17q63 0 128 65l75 337h105l-6 -28q41 28 51 58h-7q-16 0 -28 11.5t-12 30.5q0 22 17 38.5
t39 16.5q21 0 35.5 -14.5t14.5 -39.5zM423 554q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="719" 
d="M334 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5zM865 743q0 -51 -40.5 -95t-102.5 -68l-70 -316q-29 -133 -100 -204.5t-205 -71.5q-133 0 -205 61.5t-72 166.5q0 22 5 47l89 404h118l-88 -401
q-4 -14 -4 -37q0 -60 41.5 -99t115.5 -39q149 0 187 175l89 401h118l-9 -39q61 32 74 72q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" 
d="M259 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5zM656 556q0 -46 -34 -86.5t-88 -66.5l-89 -403h-105l15 65q-81 -77 -165 -77q-68 0 -108.5 32.5t-40.5 88.5q0 19 6 41l73 333h105l-67 -303
q-4 -18 -4 -30q0 -35 24.5 -52t65.5 -17q63 0 128 65l75 337h105l-6 -28q41 28 51 58h-7q-16 0 -28 11.5t-12 30.5q0 22 17 38.5t39 16.5q21 0 35.5 -14.5t14.5 -39.5z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="637" 
d="M323 0h-117l61 277l-173 390h127l121 -286l245 286h141l-344 -390zM486 723h-75l-133 144h100z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="503" 
d="M-47 -185l35 92q18 -8 48 -8q38 0 65 40l38 58l-88 486h109l60 -362l222 362h116l-360 -570q-37 -58 -77 -83.5t-95 -25.5q-42 0 -73 11zM380 556h-75l-133 144h100z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="51" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="593" 
d="M552 197h-533l20 90h533z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="833" 
d="M792 197h-773l20 90h773z" />
    <glyph glyph-name="afii00208" unicode="&#x2015;" horiz-adv-x="593" 
d="M552 197h-533l20 90h533z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="244" 
d="M98 493q0 52 38.5 104t95.5 80l39 -41q-71 -34 -92 -84q2 1 9 1q23 0 38.5 -15.5t15.5 -41.5q0 -30 -23.5 -52t-53.5 -22q-28 0 -47.5 18.5t-19.5 52.5z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="244" 
d="M276 605q0 -52 -38.5 -104t-95.5 -80l-39 41q71 34 92 84h-9q-23 0 -38.5 15t-15.5 41q0 30 23.5 52.5t53.5 22.5q28 0 47.5 -19t19.5 -53z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="245" 
d="M154 57q0 -52 -38.5 -104t-94.5 -80l-39 41q71 34 92 84h-10q-22 0 -37.5 15t-15.5 41q0 30 23 52.5t53 22.5q28 0 47.5 -19t19.5 -53z" />
    <glyph glyph-name="quotereversed" unicode="&#x201b;" horiz-adv-x="244" 
d="M129 556q0 58 28.5 89.5t63.5 31.5q28 0 45.5 -18t17.5 -44q0 -30 -21 -49.5t-50 -19.5q-15 0 -27 5q-1 -25 13.5 -55.5t37.5 -50.5l-47 -29q-61 61 -61 140z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="429" 
d="M287 493q0 52 38.5 104.5t94.5 79.5l39 -41q-71 -34 -92 -84q2 1 10 1q22 0 37.5 -15.5t15.5 -41.5q0 -30 -23 -52t-53 -22q-29 0 -48 18.5t-19 52.5zM102 493q0 52 38.5 104t95.5 80l39 -41q-71 -34 -92 -84q2 1 9 1q23 0 38.5 -15.5t15.5 -41.5q0 -30 -23.5 -52
t-53.5 -22q-28 0 -47.5 18.5t-19.5 52.5z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="429" 
d="M276 605q0 -52 -38.5 -104t-95.5 -80l-39 41q71 34 92 84h-9q-23 0 -38.5 15t-15.5 41q0 30 23.5 52.5t53.5 22.5q28 0 47.5 -19t19.5 -53zM460 605q0 -52 -38.5 -104t-94.5 -80l-39 41q71 34 92 84h-10q-22 0 -37.5 15t-15.5 41q0 30 23 52.5t53 22.5q28 0 47.5 -19
t19.5 -53z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="429" 
d="M154 57q0 -52 -38.5 -104t-94.5 -80l-39 41q71 34 92 84h-10q-22 0 -37.5 15t-15.5 41q0 30 23 52.5t53 22.5q28 0 47.5 -19t19.5 -53zM339 57q0 -52 -38.5 -104t-95.5 -80l-39 41q71 34 92 84h-9q-23 0 -38.5 15t-15.5 41q0 30 23.5 52.5t53.5 22.5q28 0 47.5 -19
t19.5 -53z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="271" 
d="M312 526l-88 4l-41 -203h-60l50 203l-90 -4l12 54l88 -3l18 100h60l-27 -100l90 3z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="271" 
d="M324 580l-12 -54l-88 4l-45 -202l90 4l-12 -54l-88 3l-19 -101h-60l27 101l-89 -3l12 54l87 -4l45 202l-89 -4l12 54l88 -3l18 100h60l-27 -100z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="358" 
d="M294 253q0 -51 -38.5 -87.5t-89.5 -36.5q-44 0 -73 29.5t-29 72.5q0 51 38 88t89 37q44 0 73.5 -29.5t29.5 -73.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="735" 
d="M75 -11q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22zM320 -11q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22zM565 -11q-27 0 -45.5 19t-18.5 45
q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1105" 
d="M141 0h-66l573 667h67zM519 -12q-65 0 -110.5 38t-45.5 99q0 85 54 139.5t128 54.5q65 0 110.5 -38t45.5 -99q0 -85 -54 -139.5t-128 -54.5zM521 54q42 0 72 34.5t30 86.5q0 33 -22.5 55t-57.5 22q-42 0 -72 -34t-30 -86q0 -34 22.5 -56t57.5 -22zM239 346q-65 0 -110 38
t-45 99q0 85 53.5 139.5t128.5 54.5q65 0 110 -38t45 -99q0 -85 -53.5 -139.5t-128.5 -54.5zM242 412q42 0 72 34.5t30 86.5q0 33 -23 55t-58 22q-42 0 -72 -34t-30 -86q0 -34 23 -56t58 -22zM878 -12q-65 0 -110.5 38t-45.5 99q0 85 54 139.5t128 54.5q65 0 110.5 -38
t45.5 -99q0 -85 -54 -139.5t-128 -54.5zM880 54q42 0 72 34.5t30 86.5q0 33 -22.5 55t-57.5 22q-42 0 -72 -34t-30 -86q0 -34 22.5 -56t57.5 -22z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="319" 
d="M244 63h-95l-120 180l200 177h103l-204 -182z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="319" 
d="M77 420h95l120 -180l-200 -177h-103l204 182z" />
    <glyph glyph-name="uni203E" unicode="&#x203e;" horiz-adv-x="363" 
d="M433 566h-362l14 62h362z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="150" 
d="M415 667l-574 -667h-67l573 667h68z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="393" 
d="M444 669q0 -37 -11 -79t-33.5 -82.5t-63 -67t-91.5 -26.5q-71 0 -110 44t-39 114q0 37 11 79t33.5 82.5t63 67t91.5 26.5q71 0 110 -44t39 -114zM363 672q0 90 -71 90q-39 0 -67 -36t-38 -77.5t-10 -79.5q0 -90 71 -90q39 0 67 36t38 77.5t10 79.5z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="393" 
d="M414 513h-55l-20 -92h-80l20 92h-196l12 56l220 252h112l-54 -244h55zM293 577l38 171l-153 -171h115z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="393" 
d="M416 569q0 -66 -46 -110.5t-120 -44.5q-54 0 -98.5 18.5t-66.5 53.5l55 48q35 -55 107 -55q39 0 63 22t24 55q0 30 -22 46t-56 16q-47 0 -84 -35l-52 19l48 219h278l-15 -65h-196l-24 -109q36 30 84 30q50 0 85.5 -30t35.5 -78z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="393" 
d="M421 566q0 -63 -44.5 -107.5t-119.5 -44.5q-76 0 -117.5 40.5t-41.5 110.5q0 108 62 185t152 77q86 0 134 -48l-46 -54q-30 37 -90 37q-56 0 -91.5 -48t-35.5 -85v-4q51 57 110 57q54 0 91 -31t37 -85zM339 556q0 30 -22 46t-58 16q-44 0 -81 -44q-2 -5 -2 -19
q0 -35 21.5 -55.5t58.5 -20.5t60 23t23 54z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="367" 
d="M439 765l-232 -344h-90l230 335h-215l15 65h304z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="393" 
d="M412 526q0 -49 -41.5 -80.5t-115.5 -31.5q-70 0 -120.5 27.5t-50.5 79.5q0 42 34.5 74t88.5 38q-72 27 -72 85q0 52 45 80.5t106 28.5q63 0 113 -26.5t50 -76.5q0 -40 -29 -69t-84 -36q32 -12 54 -36.5t22 -56.5zM366 712q0 25 -22 38.5t-56 13.5q-32 0 -53.5 -15
t-21.5 -41q0 -23 22.5 -38t42.5 -18q35 3 61.5 17.5t26.5 42.5zM333 534q0 21 -20.5 39t-47.5 25q-43 -3 -71 -21t-28 -46q0 -24 25.5 -39.5t57.5 -15.5q37 0 60.5 16.5t23.5 41.5z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="393" 
d="M121 675q0 63 44.5 108t119.5 45q76 0 117.5 -41t41.5 -111q0 -107 -62 -184t-152 -77q-87 0 -134 48l46 54q29 -37 90 -37q56 0 91.5 48t35.5 85v4q-49 -57 -110 -57q-54 0 -91 31t-37 84zM203 685q0 -29 22 -45.5t58 -16.5q44 0 81 44q2 5 2 20q0 34 -22 55t-58 21
q-37 0 -60 -23.5t-23 -54.5z" />
    <glyph glyph-name="parenleftsuperior" unicode="&#x207d;" horiz-adv-x="179" 
d="M197 376l-52 -20q-48 71 -48 184q0 97 44.5 187t120.5 159l43 -20q-130 -173 -130 -357q0 -73 22 -133z" />
    <glyph glyph-name="parenrightsuperior" unicode="&#x207e;" horiz-adv-x="179" 
d="M155 866l53 20q48 -75 48 -184q0 -97 -45 -187.5t-120 -158.5l-44 20q131 175 131 357q0 75 -23 133z" />
    <glyph glyph-name="nsuperior" unicode="&#x207f;" horiz-adv-x="405" 
d="M363 326h-77l42 192q2 12 2 19q0 46 -57 46q-39 0 -82 -43l-47 -214h-78l69 314h78l-9 -42q50 50 108 50q48 0 75 -21.5t27 -60.5q0 -8 -4 -30z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="393" 
d="M328 102q0 -37 -11 -79t-33.5 -82.5t-63 -67t-91.5 -26.5q-71 0 -110 44t-39 114q0 37 11 79t33.5 82.5t63 67t91.5 26.5q71 0 110 -44t39 -114zM247 105q0 90 -71 90q-39 0 -67 -36t-38 -77.5t-10 -79.5q0 -90 71 -90q39 0 67 36t38 77.5t10 79.5z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="264" 
d="M118 -146h-81l64 292l-80 -66l-37 50l152 124h70z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="393" 
d="M266 -146h-304l14 63q155 91 212 136t57 87q0 26 -21.5 40.5t-53.5 14.5q-69 0 -112 -45l-35 54q59 56 150 56q65 0 110 -29.5t45 -83.5q0 -56 -58 -108.5t-175 -119.5h185z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="393" 
d="M294 -33q0 -47 -42 -83.5t-111 -36.5q-59 0 -104 19.5t-67 53.5l51 48q40 -56 116 -56q36 0 56 18t20 46q0 23 -25 36.5t-68 13.5q-26 0 -32 -1l14 65q24 -2 63 -2q38 0 59 15t21 41q0 24 -24.5 37.5t-60.5 13.5q-59 0 -105 -39l-29 51q62 53 141 53q71 0 115.5 -27.5
t44.5 -77.5q0 -41 -30 -69.5t-81 -32.5q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="393" 
d="M298 -54h-55l-20 -92h-80l20 92h-196l12 56l220 252h112l-54 -244h55zM177 10l38 171l-153 -171h115z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="393" 
d="M300 2q0 -66 -46 -110.5t-120 -44.5q-54 0 -98.5 18.5t-66.5 53.5l55 48q35 -55 107 -55q39 0 63 22t24 55q0 30 -22 46t-56 16q-47 0 -84 -35l-52 19l48 219h278l-15 -65h-196l-24 -109q36 30 84 30q50 0 85.5 -30t35.5 -78z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="393" 
d="M305 -1q0 -63 -44.5 -107.5t-119.5 -44.5q-76 0 -117.5 40.5t-41.5 110.5q0 108 62 185t152 77q86 0 134 -48l-46 -54q-30 37 -90 37q-56 0 -91.5 -48t-35.5 -85v-4q51 57 110 57q54 0 91 -31t37 -85zM223 -11q0 30 -22 46t-58 16q-44 0 -81 -44q-2 -5 -2 -19
q0 -35 21.5 -55.5t58.5 -20.5t60 23t23 54z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="367" 
d="M323 198l-232 -344h-90l230 335h-215l15 65h304z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="393" 
d="M296 -41q0 -49 -41.5 -80.5t-115.5 -31.5q-70 0 -120.5 27.5t-50.5 79.5q0 42 34.5 74t88.5 38q-72 27 -72 85q0 52 45 80.5t106 28.5q63 0 113 -26.5t50 -76.5q0 -39 -29 -68.5t-84 -36.5q32 -12 54 -36.5t22 -56.5zM250 145q0 25 -22 38.5t-56 13.5q-32 0 -53.5 -15
t-21.5 -41q0 -23 22.5 -38t42.5 -18q35 3 61.5 17.5t26.5 42.5zM217 -33q0 21 -20.5 39t-47.5 25q-43 -3 -71 -21t-28 -46q0 -24 25.5 -39.5t57.5 -15.5q37 0 60.5 16.5t23.5 41.5z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="393" 
d="M5 108q0 63 44.5 108t119.5 45q76 0 117.5 -41t41.5 -111q0 -107 -62 -184t-152 -77q-87 0 -134 48l46 54q29 -37 90 -37q56 0 91.5 48t35.5 85v4q-49 -57 -110 -57q-54 0 -91 31t-37 84zM87 118q0 -29 22 -45.5t58 -16.5q44 0 81 44q2 5 2 20q0 34 -22 55t-58 21
q-37 0 -60 -23.5t-23 -54.5z" />
    <glyph glyph-name="parenleftinferior" unicode="&#x208d;" horiz-adv-x="179" 
d="M72 -191l-52 -20q-48 71 -48 184q0 97 44.5 187t120.5 159l43 -20q-130 -173 -130 -357q0 -73 22 -133z" />
    <glyph glyph-name="parenrightinferior" unicode="&#x208e;" horiz-adv-x="179" 
d="M30 299l53 20q48 -75 48 -184q0 -97 -45 -187.5t-120 -158.5l-44 20q131 175 131 357q0 75 -23 133z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="689" 
d="M377 -12q-25 0 -49 3l-50 -91h-66l57 103q-31 9 -60 24l-70 -127h-66l88 159q-103 82 -103 225q0 173 113 283.5t269 110.5q29 0 61 -5l53 95h66l-61 -110q30 -10 58 -27l76 137h66l-96 -173q40 -40 63 -92l-112 -40q-9 17 -12 22l-218 -393q96 0 166 76l89 -62
q-52 -62 -121.5 -90t-140.5 -28zM180 291q0 -74 40 -126l226 408q-2 0 -6 0.5t-6 0.5q-103 0 -178.5 -81t-75.5 -202zM263 125q25 -17 59 -26l240 433q-29 22 -56 31z" />
    <glyph glyph-name="frenchfranc" unicode="&#x20a3;" horiz-adv-x="572" 
d="M143 0h-117l26 121h-71l14 62h71l107 484h457l-23 -103h-340l-38 -173h333l-23 -103h-333l-23 -105h174l-14 -62h-174z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="536" 
d="M40 331l13 57h56q-16 37 -16 73q0 88 74.5 152t179.5 64q81 0 143 -34.5t80 -93.5l-106 -44q-7 34 -38.5 55t-73.5 21q-55 0 -93.5 -37t-38.5 -95q0 -29 14 -61h180l-13 -57h-139q10 -27 10 -54v-6h116l-13 -57h-118q-30 -62 -86 -94q24 7 45 7q31 0 77.5 -18t73.5 -18
q49 0 85 38l27 -93q-51 -49 -121 -49q-46 0 -114 22.5t-101 22.5q-47 0 -113 -39l-22 82q53 22 93 59.5t53 79.5h-140l13 57h132q-3 25 -20 60h-99z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="739" 
d="M604 0h-113l-114 235h-182l-52 -235h-117l52 235h-71l14 62h70l17 78h-70l13 62h71l51 230h120l112 -230h178l51 230h117l-51 -230h69l-13 -62h-70l-17 -78h70l-14 -62h-69zM225 375l-17 -78h139l-38 78h-84zM435 375l38 -78h79l17 78h-134zM252 494l-13 -57h40zM503 235
l24 -49l11 49h-35z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="661" 
d="M143 0h-117l94 426h-71l14 64h71l39 177h285q76 0 127.5 -52t53.5 -125h70l-14 -64h-64q-20 -75 -84 -126t-175 -51h-174zM380 352q46 0 79 20t48 54h-271l-16 -74h160zM267 564l-17 -74h266q-5 32 -31 53t-63 21h-155z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="1049" 
d="M791 -12q-141 0 -218 86l62 73q22 -30 68.5 -53t96.5 -23q39 0 63 19t24 46q0 25 -39 44.5t-85.5 32.5t-85.5 45t-39 80q0 65 51.5 111t140.5 46q60 0 112.5 -22t83.5 -57l-56 -69q-18 25 -59 44.5t-84 19.5q-39 0 -62 -16t-23 -41q0 -16 18.5 -29.5t46.5 -22t60 -21.5
t60 -27.5t46.5 -40.5t18.5 -60q0 -70 -54.5 -117.5t-146.5 -47.5zM533 0h-129l-93 249h-123l-54 -249h-117l147 667h268q81 0 140 -50t59 -132q0 -91 -56.5 -154t-145.5 -75zM370 352h2q63 0 99.5 34.5t36.5 87.5q0 39 -29 64.5t-68 25.5h-153l-47 -212h159z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="903" 
d="M652 0h-125l-10 235h-136l-113 -235h-125l-16 235h-114l14 62h96l-5 78h-73l13 62h56l-15 230h128l7 -230h148l113 230h93l10 -230h148l109 230h134l-116 -230h53l-13 -62h-72l-39 -78h94l-14 -62h-112zM601 375l4 -78h75l37 78h-116zM236 375l2 -78h75l39 78h-116z
M449 375l-38 -78h103l-3 78h-62zM608 235l4 -82l39 82h-43zM240 235l3 -82l40 82h-43zM506 492l-27 -55h29z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="834" 
d="M527 0h-315l84 379h100l-64 -288h193q59 0 90 32.5t46 100.5l72 326h100l-74 -334q-24 -111 -75 -163.5t-157 -52.5zM135 550h232q101 0 162.5 -40t61.5 -129q0 -32 -9 -68l-31 -143h-100l32 144q6 26 6 43q0 50 -31.5 76t-86.5 26h-156l-101 -459h-100z" />
    <glyph glyph-name="dong" unicode="&#x20ab;" horiz-adv-x="585" 
d="M395 -159h-362l14 62h362zM286 495q51 0 93.5 -21.5t66.5 -59.5l26 117h-159l12 62h160l17 74h105l-17 -74h52l-12 -62h-53l-118 -531h-105l14 64q-62 -76 -151 -76q-84 0 -134.5 52.5t-50.5 146.5q0 77 29 146.5t88.5 115.5t136.5 46zM302 402q-69 0 -114.5 -58
t-45.5 -136q0 -58 33 -92.5t86 -34.5q37 0 70.5 19t55.5 49l42 187q-17 29 -50.5 47.5t-76.5 18.5z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="703" 
d="M396 -12q-123 0 -209.5 65.5t-105.5 176.5h-59l15 67h40q1 34 8 74h-32l15 67h37q43 110 140 175t214 65q107 0 179.5 -48t106.5 -127l-112 -40q-23 54 -71 82.5t-109 28.5q-67 0 -125.5 -36.5t-92.5 -99.5h282l-16 -67h-292q-10 -38 -10 -74h286l-15 -67h-263
q18 -63 69.5 -100.5t126.5 -37.5q96 0 166 76l89 -62q-52 -62 -121.5 -90t-140.5 -28z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="504" 
d="M228 494l-30 -135l146 84q35 20 51.5 31t31 29t14.5 38q0 28 -24.5 45t-60.5 17q-103 0 -128 -109zM364 367l-188 -108l-27 -121q-2 -14 -2 -22q0 -17 13 -28.5t35 -11.5q24 0 40 14l7 -80q-26 -22 -79 -22q-59 0 -93 24.5t-34 69.5q0 18 4 34l86 389q18 82 78 127
t158 45q77 0 129 -35.5t52 -90.5q0 -24 -14 -48.5t-30.5 -41.5t-47 -39t-44.5 -30.5t-43 -24.5z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="1125" 
d="M956 350q-71 0 -114 40t-43 104q0 78 55 132t132 54q71 0 114 -40.5t43 -104.5q0 -77 -55.5 -131t-131.5 -54zM958 413q48 0 76.5 37t28.5 86q0 36 -21.5 57.5t-57.5 21.5q-48 0 -76.5 -37t-28.5 -86q0 -36 21.5 -57.5t57.5 -21.5zM595 0h-113l-239 494l-109 -494h-117
l147 667h120l234 -481l107 481h117z" />
    <glyph glyph-name="published" unicode="&#x2117;" horiz-adv-x="778" 
d="M754 334q0 -142 -101.5 -243.5t-244.5 -101.5q-142 0 -243 101t-101 244t101 244t243 101q143 0 244.5 -101.5t101.5 -243.5zM720 334q0 127 -92 219t-220 92t-219 -91.5t-91 -219.5t91 -219.5t219 -91.5t220 91.5t92 219.5zM593 431q0 -57 -41.5 -93.5t-104.5 -36.5
h-121l-37 -170h-42l89 405h134q52 0 87.5 -28.5t35.5 -76.5zM550 428q0 32 -23 50.5t-62 18.5h-95l-35 -158h119q42 0 69 25.5t27 63.5z" />
    <glyph glyph-name="servicemark" unicode="&#x2120;" horiz-adv-x="476" 
d="M484 447h-28l40 182l-115 -182h-8l-35 182l-40 -182h-28l48 220h42l29 -160l99 160h44zM247 511q0 -32 -23 -52t-62 -20q-69 0 -96 40l22 19q23 -33 72 -33q24 0 38 11t14 29q0 15 -18 26.5t-39 18.5t-39 21t-18 34q0 29 23.5 48.5t60.5 19.5q63 0 86 -35l-23 -17
q-20 28 -65 28q-22 0 -35 -11t-13 -26t18 -26t39.5 -17t39.5 -21t18 -37z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="454" 
d="M464 447h-28l40 182l-115 -182h-8l-35 182l-40 -182h-28l48 220h43l29 -160l99 160h43zM257 641h-62l-43 -194h-28l43 194h-62l5 26h152z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="765" 
d="M28 103h143q-111 83 -111 223q0 65 25.5 127.5t71.5 113t118 81t158 30.5q141 0 231 -76t90 -206q0 -104 -56.5 -178.5t-135.5 -114.5h112l-22 -103h-257l23 103q93 20 151.5 96t58.5 177q0 90 -54 143.5t-146 53.5q-111 0 -182.5 -83.5t-71.5 -197.5q0 -131 110 -189
l-22 -103h-257z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="842" 
d="M829 324h-632q-4 0 -4 -5v-190q0 -15 9 -23q98 -103 240 -103q151 0 250 115h57q-54 -63 -135 -99.5t-174 -36.5q-161 0 -275.5 103t-114.5 248t114.5 248t275.5 103t275.5 -103t114.5 -248zM687 349v191q0 14 -10 24q-97 99 -236 99t-238 -102q-10 -10 -10 -24v-188
q0 -6 5 -6h485q4 0 4 6z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="841" 
d="M209 267h-81l64 292l-80 -66l-37 50l152 124h70zM712 667l-574 -667h-67l573 667h68zM773 113q0 -47 -42 -83.5t-111 -36.5q-59 0 -104 19.5t-67 53.5l51 48q40 -56 116 -56q36 0 56 18t20 46q0 23 -25 36.5t-68 13.5q-26 0 -32 -1l14 65q24 -2 63 -2q38 0 59 15t21 41
q0 24 -24.5 37.5t-60.5 13.5q-59 0 -105 -39l-29 51q62 53 141 53q71 0 115.5 -27.5t44.5 -77.5q0 -41 -30 -69.5t-81 -32.5q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="937" 
d="M357 267h-304l14 63q155 91 212 136t57 87q0 26 -21.5 40.5t-53.5 14.5q-69 0 -112 -45l-35 54q59 56 150 56q65 0 110 -29.5t45 -83.5q0 -56 -58 -108.5t-175 -119.5h185zM808 667l-574 -667h-67l573 667h68zM870 113q0 -47 -42 -83.5t-111 -36.5q-59 0 -104 19.5
t-67 53.5l51 48q40 -56 116 -56q36 0 56 18t20 46q0 23 -25 36.5t-68 13.5q-26 0 -32 -1l14 65q24 -2 63 -2q38 0 59 15t21 41q0 24 -24.5 37.5t-60.5 13.5q-59 0 -105 -39l-29 51q62 53 141 53q71 0 115.5 -27.5t44.5 -77.5q0 -41 -30 -69.5t-81 -32.5q34 -7 56 -29.5
t22 -56.5z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="841" 
d="M209 267h-81l64 292l-80 -66l-37 50l152 124h70zM775 105q0 -49 -41.5 -80.5t-115.5 -31.5q-70 0 -120.5 27.5t-50.5 79.5q0 42 34.5 74t88.5 38q-72 27 -72 85q0 52 45 80.5t106 28.5q63 0 113 -26.5t50 -76.5q0 -39 -29 -68.5t-84 -36.5q32 -12 54 -36.5t22 -56.5z
M729 291q0 25 -22 38.5t-56 13.5q-32 0 -53.5 -15t-21.5 -41q0 -23 22.5 -38t42.5 -18q35 3 61.5 17.5t26.5 42.5zM696 113q0 21 -20.5 39t-47.5 25q-43 -3 -71 -21t-28 -46q0 -24 25.5 -39.5t57.5 -15.5q37 0 60.5 16.5t23.5 41.5zM712 667l-574 -667h-67l573 667h68z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="937" 
d="M872 105q0 -49 -41.5 -80.5t-115.5 -31.5q-70 0 -120.5 27.5t-50.5 79.5q0 42 34.5 74t88.5 38q-72 27 -72 85q0 52 45 80.5t106 28.5q63 0 113 -26.5t50 -76.5q0 -39 -29 -68.5t-84 -36.5q32 -12 54 -36.5t22 -56.5zM826 291q0 25 -22 38.5t-56 13.5q-32 0 -53.5 -15
t-21.5 -41q0 -23 22.5 -38t42.5 -18q35 3 61.5 17.5t26.5 42.5zM793 113q0 21 -20.5 39t-47.5 25q-43 -3 -71 -21t-28 -46q0 -24 25.5 -39.5t57.5 -15.5q37 0 60.5 16.5t23.5 41.5zM808 667l-574 -667h-67l573 667h68zM385 380q0 -47 -42 -83.5t-111 -36.5q-59 0 -104 19.5
t-67 53.5l51 48q40 -56 116 -56q36 0 56 18t20 46q0 23 -25 36.5t-68 13.5q-26 0 -32 -1l14 65q24 -2 63 -2q38 0 59 15t21 41q0 24 -24.5 37.5t-60.5 13.5q-59 0 -105 -39l-29 51q62 53 141 53q71 0 115.5 -27.5t44.5 -77.5q0 -41 -30 -69.5t-81 -32.5q34 -7 56 -29.5
t22 -56.5z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="932" 
d="M867 105q0 -49 -41.5 -80.5t-115.5 -31.5q-70 0 -120.5 27.5t-50.5 79.5q0 42 34.5 74t88.5 38q-72 27 -72 85q0 52 45 80.5t106 28.5q63 0 113 -26.5t50 -76.5q0 -39 -29 -68.5t-84 -36.5q32 -12 54 -36.5t22 -56.5zM821 291q0 25 -22 38.5t-56 13.5q-32 0 -53.5 -15
t-21.5 -41q0 -23 22.5 -38t42.5 -18q35 3 61.5 17.5t26.5 42.5zM788 113q0 21 -20.5 39t-47.5 25q-43 -3 -71 -21t-28 -46q0 -24 25.5 -39.5t57.5 -15.5q37 0 60.5 16.5t23.5 41.5zM391 415q0 -66 -46 -110.5t-120 -44.5q-54 0 -98.5 18.5t-66.5 53.5l55 48q35 -55 107 -55
q39 0 63 22t24 55q0 30 -22 46t-56 16q-47 0 -84 -35l-52 19l48 219h278l-15 -65h-196l-24 -109q36 30 84 30q50 0 85.5 -30t35.5 -78zM803 667l-574 -667h-67l573 667h68z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="854" 
d="M789 105q0 -49 -41.5 -80.5t-115.5 -31.5q-70 0 -120.5 27.5t-50.5 79.5q0 42 34.5 74t88.5 38q-72 27 -72 85q0 52 45 80.5t106 28.5q63 0 113 -26.5t50 -76.5q0 -39 -29 -68.5t-84 -36.5q32 -12 54 -36.5t22 -56.5zM743 291q0 25 -22 38.5t-56 13.5q-32 0 -53.5 -15
t-21.5 -41q0 -23 22.5 -38t42.5 -18q35 3 61.5 17.5t26.5 42.5zM710 113q0 21 -20.5 39t-47.5 25q-43 -3 -71 -21t-28 -46q0 -24 25.5 -39.5t57.5 -15.5q37 0 60.5 16.5t23.5 41.5zM414 611l-232 -344h-90l230 335h-215l15 65h304zM725 667l-574 -667h-67l573 667h68z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="582" 
d="M558 211h-269l12 -130l-281 205l281 204l-12 -129h269v-150z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="582" 
d="M236 17v269l-130 -12l205 281l204 -281l-129 12v-269h-150z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="582" 
d="M604 286l-281 -205l12 130h-268v150h268l-12 129z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="582" 
d="M386 555v-269l130 12l-205 -281l-204 281l129 -12v269h150z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="573" 
d="M267 -12q-106 0 -172.5 58t-66.5 150q0 114 73 193t176 79q55 0 98.5 -27t66.5 -81q-18 69 -79 140t-143 132l74 77q249 -188 249 -405q0 -133 -76.5 -224.5t-199.5 -91.5zM268 81q66 0 111.5 51t45.5 124q0 49 -34 84t-97 35q-64 0 -109 -50t-45 -122q0 -56 33.5 -89
t94.5 -33z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="673" 
d="M617 0h-670l409 667h146zM480 103l-71 446l-267 -446h338z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="753" 
d="M545 -90h-117l144 654h-250l-145 -654h-117l145 654h-107l23 103h697l-22 -103h-107z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="581" 
d="M262 564l156 -273l-275 -278h338l-23 -103h-483l22 103l278 283l-154 268l22 103h482l-22 -103h-341z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="502" 
d="M485 303h-444l15 68h444z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="150" 
d="M415 667l-574 -667h-67l573 667h68z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="245" 
d="M116 175q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="700" 
d="M360 0h-73l-51 313l-153 -53l-7 64l223 75l47 -293l338 561h81z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="675" 
d="M187 163q-66 0 -104 38.5t-38 103.5q0 79 50 140.5t132 61.5q51 0 89.5 -27.5t55.5 -78.5q71 106 154 106q67 0 104.5 -38.5t37.5 -103.5q0 -80 -49.5 -141t-131.5 -61q-51 0 -89.5 27.5t-55.5 77.5q-71 -105 -155 -105zM485 231q49 0 80 36t31 89q0 37 -23.5 60.5
t-60.5 23.5q-67 0 -123 -109q6 -48 31.5 -74t64.5 -26zM202 231q69 0 125 108q-7 48 -33 74.5t-65 26.5q-48 0 -79.5 -36.5t-31.5 -89.5q0 -37 24 -60t60 -23z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="362" 
d="M1 -100h-60l15 72h61q31 0 52 21t29 56l127 570q33 149 175 149h61l-16 -72h-61q-31 0 -52 -21t-29 -56l-127 -571q-33 -148 -175 -148z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="502" 
d="M20 205l16 74q43 -31 104 -31q46 0 111 27t122 27q63 0 107 -25l-17 -74q-43 32 -105 32q-46 0 -110.5 -27.5t-122.5 -27.5q-63 0 -105 25zM61 390l16 74q43 -31 104 -31q46 0 111 27.5t122 27.5q60 0 107 -25l-16 -74q-43 31 -105 31q-46 0 -111 -27t-122 -27
q-63 0 -106 24z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="502" 
d="M98 93h-70l97 112h-105l15 67h147l106 123h-226l15 68h269l96 111h71l-95 -111h103l-16 -68h-146l-106 -123h226l-15 -67h-269z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="502" 
d="M441 105l-398 207l17 75l490 208l-19 -84l-401 -166l328 -163zM419 0h-444l14 67h444z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="502" 
d="M487 312l-490 -207l19 84l401 163l-328 166l17 77l398 -208zM418 0h-444l15 67h444z" />
    <glyph glyph-name="triangleright" unicode="&#x22b2;" horiz-adv-x="587" 
d="M609 286l-535 -309v618z" />
    <glyph glyph-name="triangleleft" unicode="&#x22b3;" horiz-adv-x="587" 
d="M555 595v-618l-535 309z" />
    <glyph glyph-name="dotmath" unicode="&#x22c5;" horiz-adv-x="245" 
d="M116 175q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="uni2318" unicode="&#x2318;" horiz-adv-x="667" 
d="M201 153l16 70h-77q-32 0 -55.5 -23.5t-23.5 -54.5q0 -26 17 -44t43 -18q31 0 52 18.5t28 51.5zM265 443l14 61q2 12 2 19q0 27 -17 43.5t-45 16.5q-32 0 -55 -22t-23 -54q0 -29 18.5 -46.5t47.5 -17.5h58zM269 265h137l31 137h-137zM564 160q0 29 -18 46t-48 17h-58
l-14 -60q-2 -14 -2 -19q0 -28 17 -44.5t45 -16.5q33 0 55.5 22t22.5 55zM644 522q0 26 -17 43.5t-43 17.5q-31 0 -52 -18.5t-28 -50.5l-16 -71h77q31 0 55 23.5t24 55.5zM607 160q0 -48 -39 -83t-89 -35q-42 0 -70 28t-28 70q0 14 3 29l13 54h-137l-18 -78
q-11 -46 -45 -74.5t-80 -28.5q-41 0 -70 28t-29 69q0 49 38 87.5t87 38.5h83l31 137h-58q-45 0 -72.5 29t-27.5 76q0 48 38.5 83t88.5 35q42 0 70 -28t28 -70q0 -14 -3 -29l-13 -55h137l18 79q11 46 45 74.5t80 28.5q42 0 71 -28t29 -69q0 -50 -38 -88t-87 -38h-83l-31 -137
h55q46 0 74.5 -29.5t28.5 -75.5z" />
    <glyph glyph-name="blacksquare" unicode="&#x25a0;" horiz-adv-x="661" 
d="M638 0h-571v572h571v-572z" />
    <glyph glyph-name="triagup" unicode="&#x25b2;" horiz-adv-x="616" 
d="M637 29l-619 1l309 535z" />
    <glyph glyph-name="triagdn" unicode="&#x25bc;" horiz-adv-x="616" 
d="M637 510l-310 -535l-309 535h619z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="375" 
d="M144 0h-21l-84 334l231 333h21l85 -333zM142 45l197 293l-67 284l-196 -292z" />
    <glyph glyph-name="circlesolid" unicode="&#x25cf;" horiz-adv-x="695" 
d="M664 285q0 -123 -87 -210t-210 -87t-210 87t-87 210t87 210t210 87t210 -87t87 -210z" />
    <glyph glyph-name="blackstar" unicode="&#x2605;" horiz-adv-x="759" 
d="M779 421l-235 -171l90 -275l-235 170l-234 -170l89 275l-234 171h290l89 276l90 -276h290z" />
    <glyph glyph-name="ballotbox" unicode="&#x2610;" horiz-adv-x="661" 
d="M636 0h-571v572h571v-572zM562 67v437h-424v-437h424z" />
    <glyph glyph-name="boxchecked" unicode="&#x2611;" horiz-adv-x="661" 
d="M656 0h-571v572h493l88 162l59 -36l-69 -128v-570zM582 67v367l-186 -343l-202 246l53 49l139 -169l155 287h-383v-437h424z" />
    <glyph glyph-name="diamond" unicode="&#x2666;" horiz-adv-x="501" 
d="M521 285l-251 -250l-250 250l250 251z" />
    <glyph glyph-name="checkmark" unicode="&#x2713;" horiz-adv-x="531" 
d="M551 698l-329 -607l-202 246l53 49l139 -169l280 517z" />
    <glyph glyph-name="perthousand.onum" unicode="&#xf628;" horiz-adv-x="893" 
d="M335 442q0 -62 -43.5 -108.5t-107.5 -46.5q-56 0 -89.5 32t-33.5 87q0 63 43 109.5t107 46.5q55 0 89.5 -32.5t34.5 -87.5zM592 550l-505 -550h-63l504 550h64zM268 439q0 29 -16 47.5t-44 18.5q-33 0 -56.5 -29t-23.5 -68q0 -29 15.5 -46.5t43.5 -17.5q33 0 57 28.5
t24 66.5zM555 144q0 -62 -43.5 -108.5t-107.5 -46.5q-55 0 -88.5 32t-33.5 87q0 63 42.5 109.5t106.5 46.5q56 0 90 -32.5t34 -87.5zM489 142q0 29 -16 47.5t-44 18.5q-33 0 -57 -29.5t-24 -67.5q0 -29 16 -47t44 -18q33 0 57 28.5t24 67.5zM845 144q0 -62 -43.5 -108.5
t-107.5 -46.5q-55 0 -89 32t-34 87q0 63 43 109.5t107 46.5q55 0 89.5 -32.5t34.5 -87.5zM778 142q0 29 -16 47.5t-44 18.5q-33 0 -56.5 -29.5t-23.5 -67.5q0 -29 15.5 -47t43.5 -18q33 0 57 28.5t24 67.5z" />
    <glyph glyph-name="uniF637" unicode="&#xf637;" horiz-adv-x="405" 
d="M363 326h-77l42 192q2 12 2 19q0 46 -57 46q-39 0 -82 -43l-47 -214h-78l96 434h78l-36 -162q50 50 108 50q48 0 75 -21.5t27 -60.5q0 -8 -4 -30z" />
    <glyph glyph-name="zero.tzero" unicode="&#xf638;" horiz-adv-x="612" 
d="M279 -12q-98 0 -158 58l-40 -46h-87l87 101q-33 61 -33 152q0 106 39 202t115 159t170 63q96 0 156 -57l41 47h87l-87 -101q33 -64 33 -153q0 -106 -39 -202.5t-114.5 -159.5t-169.5 -63zM290 92q58 0 104 53t68 126.5t22 147.5q0 16 -4 44l-284 -329q31 -42 94 -42z
M166 247q0 -23 3 -44l284 329q-32 41 -92 41q-58 0 -104 -53t-68.5 -126.5t-22.5 -146.5z" />
    <glyph glyph-name="zero.tnum" unicode="&#xf639;" horiz-adv-x="612" 
d="M279 -12q-108 0 -169.5 70t-61.5 195q0 106 39 202t115 159t170 63q108 0 169 -70t61 -194q0 -106 -39 -202.5t-114.5 -159.5t-169.5 -63zM290 92q58 0 104 53t68 126.5t22 147.5q0 71 -30 112.5t-93 41.5q-58 0 -104 -53t-68.5 -126.5t-22.5 -146.5q0 -71 30.5 -113
t93.5 -42z" />
    <glyph glyph-name="two.tnum" unicode="&#xf63a;" horiz-adv-x="612" 
d="M497 0h-483l24 110q96 56 153.5 91.5t119.5 78.5t94.5 74t53.5 65t21 65q0 41 -36 65.5t-94 24.5q-97 0 -168 -62l-51 83q42 38 102.5 60t124.5 22q103 0 174 -49t71 -137q0 -98 -99 -191.5t-280 -196.5h295z" />
    <glyph glyph-name="three.tnum" unicode="&#xf63b;" horiz-adv-x="612" 
d="M295 -12q-90 0 -167 37.5t-109 101.5l80 69q28 -49 82.5 -76.5t112.5 -27.5q64 0 100 31t36 81q0 38 -33 61t-100 23q-63 0 -74 -1l24 105q11 -1 92 -1q63 0 103 23.5t40 71.5q0 39 -38 63t-102 24q-94 0 -165 -59l-44 80q41 38 98.5 60.5t120.5 22.5q113 0 181.5 -45.5
t68.5 -126.5q0 -67 -55 -113.5t-125 -53.5q54 -13 88.5 -49.5t34.5 -95.5q0 -87 -72 -146t-178 -59z" />
    <glyph glyph-name="four.tnum" unicode="&#xf63c;" horiz-adv-x="612" 
d="M423 0h-117l33 151h-318l23 103l358 413h168l-92 -413h89l-22 -103h-89zM361 254l69 308l-272 -308h203z" />
    <glyph glyph-name="five.tnum" unicode="&#xf63d;" horiz-adv-x="612" 
d="M292 -12q-184 0 -263 130l85 73q53 -99 181 -99q63 0 103.5 37.5t40.5 92.5q0 50 -38 78t-99 28q-70 0 -129 -44l-76 33l77 350h437l-23 -103h-320l-40 -184q56 44 136 44q84 0 140.5 -50.5t56.5 -138.5q0 -103 -75.5 -175t-193.5 -72z" />
    <glyph glyph-name="six.tnum" unicode="&#xf63e;" horiz-adv-x="612" 
d="M302 -12q-117 0 -182 66.5t-65 186.5q0 78 22.5 154t64 139.5t109 103t149.5 39.5q140 0 214 -95l-74 -81q-47 72 -147 72q-77 0 -129 -59t-75 -142q-5 -11 -7 -24q29 31 78.5 54t102.5 23q90 0 148.5 -49t58.5 -131q0 -105 -78.5 -181t-189.5 -76zM303 92
q62 0 103.5 40.5t41.5 97.5q0 46 -38.5 73t-98.5 27q-79 0 -141 -64q-2 -16 -2 -40q0 -61 37 -97.5t98 -36.5z" />
    <glyph glyph-name="seven.tnum" unicode="&#xf63f;" horiz-adv-x="612" 
d="M236 0h-134l374 564h-340l23 103h481l-18 -81z" />
    <glyph glyph-name="eight.tnum" unicode="&#xf640;" horiz-adv-x="612" 
d="M287 -12q-110 0 -184 45t-74 130q0 69 51.5 120.5t140.5 72.5q-45 19 -76 55t-31 82q0 89 76 136.5t175 47.5q100 0 173.5 -44.5t73.5 -123.5q0 -68 -49.5 -114.5t-130.5 -61.5q117 -59 117 -154q0 -86 -77 -138.5t-185 -52.5zM338 390q67 5 107.5 30.5t40.5 67.5
q0 36 -37 60t-91 24q-53 0 -90 -25t-37 -67q0 -31 33.5 -56t73.5 -34zM292 92q55 0 97 27.5t42 71.5q0 36 -35 63t-80 38q-70 -4 -116.5 -33.5t-46.5 -74.5q0 -44 38.5 -68t100.5 -24z" />
    <glyph glyph-name="nine.tnum" unicode="&#xf641;" horiz-adv-x="612" 
d="M350 678q117 0 182 -66.5t65 -186.5q0 -62 -13.5 -123.5t-42.5 -118t-69 -100t-97.5 -69t-123.5 -25.5q-140 0 -213 94l74 82q44 -72 147 -72q77 0 128.5 58t75.5 142q6 18 7 24q-30 -31 -79.5 -54t-101.5 -23q-90 0 -149 49.5t-59 131.5q0 105 79 181t190 76zM349 574
q-62 0 -104 -41t-42 -97q0 -46 38.5 -73.5t98.5 -27.5q78 0 142 64q1 8 1 40q0 61 -36.5 98t-97.5 37z" />
    <glyph glyph-name="percent.onum" unicode="&#xf642;" horiz-adv-x="604" 
d="M335 442q0 -62 -43.5 -108.5t-107.5 -46.5q-56 0 -89.5 32t-33.5 87q0 63 43 109.5t107 46.5q55 0 89.5 -32.5t34.5 -87.5zM592 550l-505 -550h-63l504 550h64zM268 439q0 29 -16 47.5t-44 18.5q-33 0 -56.5 -29t-23.5 -68q0 -29 15.5 -46.5t43.5 -17.5q33 0 57 28.5
t24 66.5zM555 144q0 -62 -43.5 -108.5t-107.5 -46.5q-55 0 -88.5 32t-33.5 87q0 63 42.5 109.5t106.5 46.5q56 0 90 -32.5t34 -87.5zM489 142q0 29 -16 47.5t-44 18.5q-33 0 -57 -29.5t-24 -67.5q0 -29 16 -47t44 -18q33 0 57 28.5t24 67.5z" />
    <glyph glyph-name="zero.tonum" unicode="&#xf643;" horiz-adv-x="612" 
d="M586 322q0 -56 -19.5 -113.5t-55 -107.5t-93.5 -81.5t-127 -31.5q-121 0 -187 63.5t-66 175.5q0 44 12 90.5t36.5 90t59 78t83 55.5t104.5 21q121 0 187 -63.5t66 -176.5zM466 314q0 70 -34.5 107t-99.5 37q-55 0 -96.5 -37.5t-59.5 -87t-18 -98.5q0 -70 34.5 -106.5
t99.5 -36.5q80 0 127 71.5t47 150.5z" />
    <glyph glyph-name="one.tonum" unicode="&#xf644;" horiz-adv-x="612" 
d="M373 0h-117l87 398l-133 -113l-53 71l235 194h102z" />
    <glyph glyph-name="two.tonum" unicode="&#xf645;" horiz-adv-x="612" 
d="M473 0h-453l21 98q405 132 405 270q0 41 -33.5 65.5t-86.5 24.5q-102 0 -181 -69l-51 82q100 91 244 91q102 0 167.5 -44.5t65.5 -123.5q0 -48 -27 -93.5t-74 -82t-99.5 -64.5t-112.5 -51h238z" />
    <glyph glyph-name="three.tonum" unicode="&#xf646;" horiz-adv-x="612" 
d="M269 -127q-90 0 -167 37.5t-109 101.5l80 69q28 -49 82.5 -76.5t112.5 -27.5q64 0 100 31t36 81q0 38 -33 61t-100 23q-63 0 -74 -1l24 105q11 -1 92 -1q63 0 103 23.5t40 71.5q0 39 -38 63t-102 24q-94 0 -165 -59l-44 80q41 38 98.5 60.5t120.5 22.5q113 0 181.5 -45.5
t68.5 -126.5q0 -67 -55 -113.5t-125 -53.5q54 -13 88.5 -49.5t34.5 -95.5q0 -87 -72 -146t-178 -59z" />
    <glyph glyph-name="four.tonum" unicode="&#xf647;" horiz-adv-x="612" 
d="M520 39h-89l-34 -156h-117l34 156h-318l21 94l366 417h161l-90 -408h89zM337 142l67 303l-270 -303h203z" />
    <glyph glyph-name="five.tonum" unicode="&#xf648;" horiz-adv-x="612" 
d="M266 -129q-184 0 -263 130l85 73q53 -99 181 -99q63 0 103.5 37.5t40.5 92.5q0 50 -38 78t-99 28q-70 0 -129 -44l-76 33l77 350h437l-23 -103h-320l-40 -184q56 44 136 44q84 0 140.5 -50.5t56.5 -138.5q0 -103 -75.5 -175t-193.5 -72z" />
    <glyph glyph-name="six.tonum" unicode="&#xf649;" horiz-adv-x="612" 
d="M302 -12q-117 0 -182 66.5t-65 186.5q0 78 22.5 154t64 139.5t109 103t149.5 39.5q140 0 214 -95l-74 -81q-47 72 -147 72q-77 0 -129 -59t-75 -142q-5 -11 -7 -24q29 31 78.5 54t102.5 23q90 0 148.5 -49t58.5 -131q0 -105 -78.5 -181t-189.5 -76zM303 92
q62 0 103.5 40.5t41.5 97.5q0 46 -38.5 73t-98.5 27q-79 0 -141 -64q-2 -16 -2 -40q0 -61 37 -97.5t98 -36.5z" />
    <glyph glyph-name="seven.tonum" unicode="&#xf64a;" horiz-adv-x="612" 
d="M210 -117h-134l374 564h-340l23 103h481l-18 -81z" />
    <glyph glyph-name="eight.tonum" unicode="&#xf64b;" horiz-adv-x="612" 
d="M287 -12q-110 0 -184 45t-74 130q0 69 51.5 120.5t140.5 72.5q-45 19 -76 55t-31 82q0 89 76 136.5t175 47.5q100 0 173.5 -44.5t73.5 -123.5q0 -68 -49.5 -114.5t-130.5 -61.5q117 -59 117 -154q0 -86 -77 -138.5t-185 -52.5zM338 390q67 5 107.5 30.5t40.5 67.5
q0 36 -37 60t-91 24q-53 0 -90 -25t-37 -67q0 -31 33.5 -56t73.5 -34zM292 92q55 0 97 27.5t42 71.5q0 36 -35 63t-80 38q-70 -4 -116.5 -33.5t-46.5 -74.5q0 -44 38.5 -68t100.5 -24z" />
    <glyph glyph-name="nine.tonum" unicode="&#xf64c;" horiz-adv-x="612" 
d="M324 562q117 0 182 -66.5t65 -186.5q0 -62 -13.5 -123.5t-42.5 -118t-69 -100t-97.5 -69t-123.5 -25.5q-140 0 -213 94l74 82q44 -72 147 -72q77 0 128.5 58t75.5 142q6 18 7 24q-30 -31 -79.5 -54t-101.5 -23q-90 0 -149 49.5t-59 131.5q0 105 79 181t190 76zM323 458
q-62 0 -104 -41t-42 -97q0 -46 38.5 -73.5t98.5 -27.5q78 0 142 64q1 8 1 40q0 61 -36.5 98t-97.5 37z" />
    <glyph glyph-name="colonmonetary.onum" unicode="&#xf64d;" horiz-adv-x="584" 
d="M65 -53l58 103q-85 69 -85 188q0 133 96.5 228.5t232.5 95.5q21 0 41 -3l22 40h51l-28 -50q32 -12 46 -20l39 70h51l-54 -96q42 -36 61 -83l-97 -37q-10 18 -16 26l-182 -325h18q86 0 144 71l81 -52q-91 -115 -230 -115q-34 0 -64 6l-26 -47h-51l32 58q-22 7 -45 20
l-44 -78h-51zM151 246q0 -57 28 -96l176 316q-82 -3 -143 -67.5t-61 -152.5zM211 117q21 -16 45 -23l193 346q-23 14 -46 20z" />
    <glyph glyph-name="Euro.onum" unicode="&#xf64e;" horiz-adv-x="610" 
d="M417 305h-232q-8 -30 -8 -59v-3h226l-11 -53h-207q16 -49 59 -77.5t101 -28.5q86 0 144 71l81 -52q-91 -115 -230 -115q-109 0 -182 55t-90 147h-55l12 53h39q0 34 7 62h-33l12 54h38q37 90 120 146.5t185 56.5q81 0 142 -39t87 -103l-97 -37q-17 37 -54.5 60t-81.5 23
q-55 0 -103 -29t-78 -78h221z" />
    <glyph glyph-name="florin.onum" unicode="&#xf64f;" horiz-adv-x="384" 
d="M82 -119h-119l73 327h-46v76h62l27 116q36 161 165 161q60 0 96 -36l-40 -84q-11 17 -38 17q-50 0 -68 -75l-22 -99h93v-76h-110z" />
    <glyph glyph-name="numbersign.onum" unicode="&#xf650;" horiz-adv-x="508" 
d="M522 398l-30 -59h-83l-70 -125h84l-32 -60h-86l-85 -154h-73l85 154h-82l-84 -154h-73l85 154h-80l32 60h82l70 125h-83l31 59h84l85 152h73l-85 -152h81l86 152h73l-86 -152h81zM335 339h-81l-70 -125h82z" />
    <glyph glyph-name="sterling.onum" unicode="&#xf651;" horiz-adv-x="439" 
d="M10 224l13 60h62q-21 50 -21 89q0 79 62.5 133.5t144.5 54.5q66 0 117 -29t68 -79l-89 -36q-8 32 -33.5 48.5t-56.5 16.5q-44 0 -75 -32t-31 -82q0 -29 22 -84h129l-14 -60h-98q1 -4 1 -10q0 -31 -23.5 -67.5t-46.5 -49.5q11 4 22 4q27 0 65.5 -13t58.5 -13q38 0 68 30
l26 -76q-38 -40 -102 -40q-39 0 -92.5 16.5t-84.5 16.5q-38 0 -89 -30l-20 69q53 21 86.5 56.5t33.5 76.5q0 13 -5 30h-98z" />
    <glyph glyph-name="yen.onum" unicode="&#xf652;" horiz-adv-x="557" 
d="M280 0h-108l24 110h-202l13 59h202l13 59h-202l14 59h160l-127 263h122l106 -229l203 229h126l-241 -263h157l-13 -59h-197l-13 -59h197l-13 -59h-197z" />
    <glyph glyph-name="cent.onum" unicode="&#xf654;" horiz-adv-x="584" 
d="M217 -87l18 84q-91 20 -144 85t-53 156q0 131 94.5 226.5t228.5 97.5l13 59h79l-15 -68q56 -15 97 -50t61 -83l-97 -37q-26 53 -83 73l-82 -372q75 6 129 71l81 -52q-91 -115 -230 -115h-2l-16 -75h-79zM151 246q0 -57 28.5 -96.5t77.5 -55.5l82 370q-77 -9 -132.5 -72
t-55.5 -146z" />
    <glyph glyph-name="zero.dnom" unicode="&#xf655;" horiz-adv-x="393" 
d="M360 248q0 -37 -11 -79t-33.5 -82.5t-63 -67t-91.5 -26.5q-71 0 -110 44t-39 114q0 37 11 79t33.5 82.5t63 67t91.5 26.5q71 0 110 -44t39 -114zM279 251q0 90 -71 90q-39 0 -67 -36t-38 -77.5t-10 -79.5q0 -90 71 -90q39 0 67 36t38 77.5t10 79.5z" />
    <glyph glyph-name="one.dnom" unicode="&#xf656;" horiz-adv-x="264" 
d="M150 0h-81l64 292l-80 -66l-37 50l152 124h70z" />
    <glyph glyph-name="two.dnom" unicode="&#xf657;" horiz-adv-x="393" 
d="M298 0h-304l14 63q155 91 212 136t57 87q0 26 -21.5 40.5t-53.5 14.5q-69 0 -112 -45l-35 54q59 56 150 56q65 0 110 -29.5t45 -83.5q0 -56 -58 -108.5t-175 -119.5h185z" />
    <glyph glyph-name="three.dnom" unicode="&#xf658;" horiz-adv-x="393" 
d="M326 113q0 -47 -42 -83.5t-111 -36.5q-59 0 -104 19.5t-67 53.5l51 48q40 -56 116 -56q36 0 56 18t20 46q0 23 -25 36.5t-68 13.5q-26 0 -32 -1l14 65q24 -2 63 -2q38 0 59 15t21 41q0 24 -24.5 37.5t-60.5 13.5q-59 0 -105 -39l-29 51q62 53 141 53q71 0 115.5 -27.5
t44.5 -77.5q0 -41 -30 -69.5t-81 -32.5q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="four.dnom" unicode="&#xf659;" horiz-adv-x="393" 
d="M330 92h-55l-20 -92h-80l20 92h-196l12 56l220 252h112l-54 -244h55zM209 156l38 171l-153 -171h115z" />
    <glyph glyph-name="five.dnom" unicode="&#xf65a;" horiz-adv-x="393" 
d="M332 148q0 -66 -46 -110.5t-120 -44.5q-54 0 -98.5 18.5t-66.5 53.5l55 48q35 -55 107 -55q39 0 63 22t24 55q0 30 -22 46t-56 16q-47 0 -84 -35l-52 19l48 219h278l-15 -65h-196l-24 -109q36 30 84 30q50 0 85.5 -30t35.5 -78z" />
    <glyph glyph-name="six.dnom" unicode="&#xf65b;" horiz-adv-x="393" 
d="M337 145q0 -63 -44.5 -107.5t-119.5 -44.5q-76 0 -117.5 40.5t-41.5 110.5q0 108 62 185t152 77q86 0 134 -48l-46 -54q-30 37 -90 37q-56 0 -91.5 -48t-35.5 -85v-4q51 57 110 57q54 0 91 -31t37 -85zM255 135q0 30 -22 46t-58 16q-44 0 -81 -44q-2 -5 -2 -19
q0 -35 21.5 -55.5t58.5 -20.5t60 23t23 54z" />
    <glyph glyph-name="seven.dnom" unicode="&#xf65c;" horiz-adv-x="367" 
d="M355 344l-232 -344h-90l230 335h-215l15 65h304z" />
    <glyph glyph-name="eight.dnom" unicode="&#xf65d;" horiz-adv-x="393" 
d="M328 105q0 -49 -41.5 -80.5t-115.5 -31.5q-70 0 -120.5 27.5t-50.5 79.5q0 42 34.5 74t88.5 38q-72 27 -72 85q0 52 45 80.5t106 28.5q63 0 113 -26.5t50 -76.5q0 -39 -29 -68.5t-84 -36.5q32 -12 54 -36.5t22 -56.5zM282 291q0 25 -22 38.5t-56 13.5q-32 0 -53.5 -15
t-21.5 -41q0 -23 22.5 -38t42.5 -18q35 3 61.5 17.5t26.5 42.5zM249 113q0 21 -20.5 39t-47.5 25q-43 -3 -71 -21t-28 -46q0 -24 25.5 -39.5t57.5 -15.5q37 0 60.5 16.5t23.5 41.5z" />
    <glyph glyph-name="nine.dnom" unicode="&#xf65e;" horiz-adv-x="393" 
d="M37 254q0 63 44.5 108t119.5 45q76 0 117.5 -41t41.5 -111q0 -107 -62 -184t-152 -77q-87 0 -134 48l46 54q29 -37 90 -37q56 0 91.5 48t35.5 85v4q-49 -57 -110 -57q-54 0 -91 31t-37 84zM119 264q0 -29 22 -45.5t58 -16.5q44 0 81 44q2 5 2 20q0 34 -22 55t-58 21
q-37 0 -60 -23.5t-23 -54.5z" />
    <glyph glyph-name="zero.numr" unicode="&#xf661;" horiz-adv-x="393" 
d="M419 515q0 -37 -11 -79t-33.5 -82.5t-63 -67t-91.5 -26.5q-71 0 -110 44t-39 114q0 37 11 79t33.5 82.5t63 67t91.5 26.5q71 0 110 -44t39 -114zM338 518q0 90 -71 90q-39 0 -67 -36t-38 -77.5t-10 -79.5q0 -90 71 -90q39 0 67 36t38 77.5t10 79.5z" />
    <glyph glyph-name="one.numr" unicode="&#xf662;" horiz-adv-x="264" 
d="M209 267h-81l64 292l-80 -66l-37 50l152 124h70z" />
    <glyph glyph-name="two.numr" unicode="&#xf663;" horiz-adv-x="393" 
d="M357 267h-304l14 63q155 91 212 136t57 87q0 26 -21.5 40.5t-53.5 14.5q-69 0 -112 -45l-35 54q59 56 150 56q65 0 110 -29.5t45 -83.5q0 -56 -58 -108.5t-175 -119.5h185z" />
    <glyph glyph-name="three.numr" unicode="&#xf664;" horiz-adv-x="393" 
d="M385 380q0 -47 -42 -83.5t-111 -36.5q-59 0 -104 19.5t-67 53.5l51 48q40 -56 116 -56q36 0 56 18t20 46q0 23 -25 36.5t-68 13.5q-26 0 -32 -1l14 65q24 -2 63 -2q38 0 59 15t21 41q0 24 -24.5 37.5t-60.5 13.5q-59 0 -105 -39l-29 51q62 53 141 53q71 0 115.5 -27.5
t44.5 -77.5q0 -41 -30 -69.5t-81 -32.5q34 -7 56 -29.5t22 -56.5z" />
    <glyph glyph-name="four.numr" unicode="&#xf665;" horiz-adv-x="393" 
d="M389 359h-55l-20 -92h-80l20 92h-196l12 56l220 252h112l-54 -244h55zM268 423l38 171l-153 -171h115z" />
    <glyph glyph-name="five.numr" unicode="&#xf666;" horiz-adv-x="393" 
d="M391 415q0 -66 -46 -110.5t-120 -44.5q-54 0 -98.5 18.5t-66.5 53.5l55 48q35 -55 107 -55q39 0 63 22t24 55q0 30 -22 46t-56 16q-47 0 -84 -35l-52 19l48 219h278l-15 -65h-196l-24 -109q36 30 84 30q50 0 85.5 -30t35.5 -78z" />
    <glyph glyph-name="six.numr" unicode="&#xf667;" horiz-adv-x="393" 
d="M396 412q0 -63 -44.5 -107.5t-119.5 -44.5q-76 0 -117.5 40.5t-41.5 110.5q0 108 62 185t152 77q86 0 134 -48l-46 -54q-30 37 -90 37q-56 0 -91.5 -48t-35.5 -85v-4q51 57 110 57q54 0 91 -31t37 -85zM314 402q0 30 -22 46t-58 16q-44 0 -81 -44q-2 -5 -2 -19
q0 -35 21.5 -55.5t58.5 -20.5t60 23t23 54z" />
    <glyph glyph-name="seven.numr" unicode="&#xf668;" horiz-adv-x="367" 
d="M414 611l-232 -344h-90l230 335h-215l15 65h304z" />
    <glyph glyph-name="eight.numr" unicode="&#xf669;" horiz-adv-x="393" 
d="M387 372q0 -49 -41.5 -80.5t-115.5 -31.5q-70 0 -120.5 27.5t-50.5 79.5q0 42 34.5 74t88.5 38q-72 27 -72 85q0 52 45 80.5t106 28.5q63 0 113 -26.5t50 -76.5q0 -40 -29 -69t-84 -36q32 -12 54 -36.5t22 -56.5zM341 558q0 25 -22 38.5t-56 13.5q-32 0 -53.5 -15
t-21.5 -41q0 -23 22.5 -38t42.5 -18q35 3 61.5 17.5t26.5 42.5zM308 380q0 21 -20.5 39t-47.5 25q-43 -3 -71 -21t-28 -46q0 -24 25.5 -39.5t57.5 -15.5q37 0 60.5 16.5t23.5 41.5z" />
    <glyph glyph-name="nine.numr" unicode="&#xf66a;" horiz-adv-x="393" 
d="M96 521q0 63 44.5 108t119.5 45q76 0 117.5 -41t41.5 -111q0 -107 -62 -184t-152 -77q-87 0 -134 48l46 54q29 -37 90 -37q56 0 91.5 48t35.5 85v4q-49 -57 -110 -57q-54 0 -91 31t-37 84zM178 531q0 -29 22 -45.5t58 -16.5q44 0 81 44q2 5 2 20q0 34 -22 55t-58 21
q-37 0 -60 -23.5t-23 -54.5z" />
    <glyph glyph-name="Abreve.smcp" unicode="&#xf66d;" horiz-adv-x="586" 
d="M294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM559 672q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="Amacron.smcp" unicode="&#xf66e;" horiz-adv-x="586" 
d="M555 616h-362l14 62h362zM294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193z" />
    <glyph glyph-name="Aogonek.smcp" unicode="&#xf66f;" horiz-adv-x="586" 
d="M294 550h132l106 -550q-97 -44 -97 -103q0 -17 10.5 -26.5t28.5 -9.5q31 0 50 37l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 66 65 112h-34l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193z" />
    <glyph glyph-name="AEacute.smcp" unicode="&#xf670;" horiz-adv-x="835" 
d="M732 750l-197 -144h-77l172 144h102zM735 0h-402l23 103h-200l-86 -103h-125l467 550h444l-21 -95h-293l-28 -127h286l-21 -95h-286l-30 -138h293zM377 198l53 240l-200 -240h147z" />
    <glyph glyph-name="Cacute.smcp" unicode="&#xf671;" horiz-adv-x="584" 
d="M625 750l-197 -144h-77l172 144h102zM314 -12q-123 0 -199.5 69.5t-76.5 180.5q0 133 96.5 228.5t232.5 95.5q81 0 142 -39t87 -103l-97 -37q-17 37 -54.5 60t-81.5 23q-84 0 -148 -65t-64 -155q0 -73 47 -117.5t121 -44.5q86 0 144 71l81 -52q-91 -115 -230 -115z" />
    <glyph glyph-name="Ccaron.smcp" unicode="&#xf672;" horiz-adv-x="584" 
d="M466 606h-96l-64 144h64l56 -97l96 97h69zM314 -12q-123 0 -199.5 69.5t-76.5 180.5q0 133 96.5 228.5t232.5 95.5q81 0 142 -39t87 -103l-97 -37q-17 37 -54.5 60t-81.5 23q-84 0 -148 -65t-64 -155q0 -73 47 -117.5t121 -44.5q86 0 144 71l81 -52q-91 -115 -230 -115z
" />
    <glyph glyph-name="Ccircumflex.smcp" unicode="&#xf673;" horiz-adv-x="584" 
d="M559 606h-65l-56 96l-96 -96h-68l125 144h96zM314 -12q-123 0 -199.5 69.5t-76.5 180.5q0 133 96.5 228.5t232.5 95.5q81 0 142 -39t87 -103l-97 -37q-17 37 -54.5 60t-81.5 23q-84 0 -148 -65t-64 -155q0 -73 47 -117.5t121 -44.5q86 0 144 71l81 -52
q-91 -115 -230 -115z" />
    <glyph glyph-name="Cdotaccent.smcp" unicode="&#xf674;" horiz-adv-x="584" 
d="M314 -12q-123 0 -199.5 69.5t-76.5 180.5q0 133 96.5 228.5t232.5 95.5q81 0 142 -39t87 -103l-97 -37q-17 37 -54.5 60t-81.5 23q-84 0 -148 -65t-64 -155q0 -73 47 -117.5t121 -44.5q86 0 144 71l81 -52q-91 -115 -230 -115zM486 663q0 -26 -20.5 -45.5t-46.5 -19.5
q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="Dcaron.smcp" unicode="&#xf675;" horiz-adv-x="621" 
d="M442 606h-96l-64 144h64l56 -97l96 97h69zM245 0h-231l121 550h190q117 0 194 -65.5t77 -170.5q0 -44 -12 -87t-39 -84.5t-66.5 -73t-100 -50.5t-133.5 -19zM253 95h12q104 0 161.5 64t57.5 147q0 67 -46.5 108t-125.5 41h-89l-79 -360h109z" />
    <glyph glyph-name="Dcroat.smcp" unicode="&#xf676;" horiz-adv-x="621" 
d="M245 0h-231l53 240h-59l17 76h58l52 234h190q117 0 194 -65.5t77 -170.5q0 -44 -12 -87t-39 -84.5t-66.5 -73t-100 -50.5t-133.5 -19zM265 95q104 0 161.5 64t57.5 147q0 67 -46.5 108t-125.5 41h-89l-31 -139h121l-17 -76h-120l-32 -145h121z" />
    <glyph glyph-name="Ebreve.smcp" unicode="&#xf677;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291zM534 672q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="Ecaron.smcp" unicode="&#xf678;" horiz-adv-x="515" 
d="M399 606h-96l-64 144h64l56 -97l96 97h69zM415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291z" />
    <glyph glyph-name="Edotaccent.smcp" unicode="&#xf679;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291zM420 663q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="Emacron.smcp" unicode="&#xf67a;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291zM531 616h-362l14 62h362z" />
    <glyph glyph-name="Eng.smcp" unicode="&#xf67b;" horiz-adv-x="641" 
d="M306 -196q-116 0 -168 72l65 77q29 -52 94 -52q92 0 112 98v1h-1l-199 393l-86 -393h-109l121 550h116l196 -382l84 382h109l-124 -563q-20 -93 -71 -138t-139 -45z" />
    <glyph glyph-name="Eogonek.smcp" unicode="&#xf67c;" horiz-adv-x="515" 
d="M407 -102l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 66 65 112h-315l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291l-20 -95q-97 -44 -97 -103q0 -17 10.5 -26.5t28.5 -9.5q31 0 50 37z" />
    <glyph glyph-name="Gbreve.smcp" unicode="&#xf67d;" horiz-adv-x="620" 
d="M318 -12q-120 0 -200 67.5t-80 179.5q0 132 94.5 229.5t235.5 97.5q75 0 136.5 -34.5t91.5 -90.5l-96 -45q-18 31 -55.5 52.5t-83.5 21.5q-89 0 -149 -66.5t-60 -155.5q0 -71 48.5 -115.5t122.5 -44.5q65 0 109 30l22 97h-148l20 89h256l-53 -240q-95 -72 -211 -72z
M602 672q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="Gcircumflex.smcp" unicode="&#xf67e;" horiz-adv-x="620" 
d="M318 -12q-120 0 -200 67.5t-80 179.5q0 132 94.5 229.5t235.5 97.5q75 0 136.5 -34.5t91.5 -90.5l-96 -45q-18 31 -55.5 52.5t-83.5 21.5q-89 0 -149 -66.5t-60 -155.5q0 -71 48.5 -115.5t122.5 -44.5q65 0 109 30l22 97h-148l20 89h256l-53 -240q-95 -72 -211 -72z
M559 606h-65l-56 96l-96 -96h-68l125 144h96z" />
    <glyph glyph-name="Gcommaaccent.smcp" unicode="&#xf67f;" horiz-adv-x="620" 
d="M318 -12q-120 0 -200 67.5t-80 179.5q0 132 94.5 229.5t235.5 97.5q75 0 136.5 -34.5t91.5 -90.5l-96 -45q-18 31 -55.5 52.5t-83.5 21.5q-89 0 -149 -66.5t-60 -155.5q0 -71 48.5 -115.5t122.5 -44.5q65 0 109 30l22 97h-148l20 89h256l-53 -240q-95 -72 -211 -72z
M313 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Gdotaccent.smcp" unicode="&#xf680;" horiz-adv-x="620" 
d="M318 -12q-120 0 -200 67.5t-80 179.5q0 132 94.5 229.5t235.5 97.5q75 0 136.5 -34.5t91.5 -90.5l-96 -45q-18 31 -55.5 52.5t-83.5 21.5q-89 0 -149 -66.5t-60 -155.5q0 -71 48.5 -115.5t122.5 -44.5q65 0 109 30l22 97h-148l20 89h256l-53 -240q-95 -72 -211 -72z
M488 663q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="Hcircumflex.smcp" unicode="&#xf681;" horiz-adv-x="644" 
d="M544 606h-65l-56 96l-96 -96h-68l125 144h96zM522 0h-109l52 234h-290l-52 -234h-109l121 550h109l-48 -221h290l48 221h109z" />
    <glyph glyph-name="Hbar.smcp" unicode="&#xf682;" horiz-adv-x="661" 
d="M531 0h-109l52 234h-290l-52 -234h-109l90 411h-67l14 62h67l17 77h109l-17 -77h290l17 77h109l-17 -77h65l-13 -62h-66zM205 329h290l18 82h-290z" />
    <glyph glyph-name="Ibreve.smcp" unicode="&#xf683;" horiz-adv-x="245" 
d="M123 0h-109l121 550h109zM387 672q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="IJ.smcp" unicode="&#xf684;" horiz-adv-x="673" 
d="M379 -12q-115 0 -168 73l65 75q31 -52 94 -52q90 0 112 98l81 368h109l-83 -377q-21 -97 -72.5 -141t-137.5 -44zM123 0h-109l121 550h109z" />
    <glyph glyph-name="Imacron.smcp" unicode="&#xf685;" horiz-adv-x="245" 
d="M384 616h-362l14 62h362zM123 0h-109l121 550h109z" />
    <glyph glyph-name="Iogonek.smcp" unicode="&#xf686;" horiz-adv-x="245" 
d="M123 0q-97 -44 -97 -103q0 -17 10.5 -26.5t28.5 -9.5q31 0 50 37l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 66 65 112h-23l121 550h109z" />
    <glyph glyph-name="Itilde.smcp" unicode="&#xf687;" horiz-adv-x="245" 
d="M123 0h-109l121 550h109zM273 604q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="Jcircumflex.smcp" unicode="&#xf688;" horiz-adv-x="428" 
d="M527 606h-65l-56 96l-96 -96h-68l125 144h96zM134 -12q-115 0 -168 73l65 75q31 -52 94 -52q90 0 112 98l81 368h109l-83 -377q-21 -97 -72.5 -141t-137.5 -44z" />
    <glyph glyph-name="Kcommaaccent.smcp" unicode="&#xf689;" horiz-adv-x="552" 
d="M262 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM492 0h-131l-141 228l-58 -52l-39 -176h-109l121 550h109l-54 -248l270 248h135l-292 -260z" />
    <glyph glyph-name="Lacute.smcp" unicode="&#xf68a;" horiz-adv-x="464" 
d="M376 0h-362l121 550h109l-100 -455h252zM539 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Lcaron.smcp" unicode="&#xf68b;" horiz-adv-x="464" 
d="M419 501q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM376 0h-362l121 550h109l-100 -455h252z" />
    <glyph glyph-name="Lcommaaccent.smcp" unicode="&#xf68c;" horiz-adv-x="464" 
d="M224 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM376 0h-362l121 550h109l-100 -455h252z" />
    <glyph glyph-name="Ldot.smcp" unicode="&#xf68d;" horiz-adv-x="487" 
d="M376 0h-362l121 550h109l-100 -455h252zM375 230q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="Nacute.smcp" unicode="&#xf68e;" horiz-adv-x="641" 
d="M610 750l-197 -144h-77l172 144h102zM519 0h-111l-197 403l-88 -403h-109l121 550h116l194 -392l86 392h109z" />
    <glyph glyph-name="Ncaron.smcp" unicode="&#xf68f;" horiz-adv-x="641" 
d="M449 606h-96l-64 144h64l56 -97l96 97h69zM519 0h-111l-197 403l-88 -403h-109l121 550h116l194 -392l86 392h109z" />
    <glyph glyph-name="Ncommaaccent.smcp" unicode="&#xf690;" horiz-adv-x="641" 
d="M295 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM519 0h-111l-197 403l-88 -403h-109l121 550h116l194 -392l86 392h109z" />
    <glyph glyph-name="Obreve.smcp" unicode="&#xf691;" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM598 682
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="Ohungarumlaut.smcp" unicode="&#xf692;" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM504 750l-147 -144h-65
l122 144h90zM650 750l-147 -144h-65l122 144h90z" />
    <glyph glyph-name="Omacron.smcp" unicode="&#xf693;" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM592 616h-362l14 62
h362z" />
    <glyph glyph-name="Oslashacute.smcp" unicode="&#xf694;" horiz-adv-x="658" 
d="M311 -12q-102 0 -169 46l-34 -34h-91l77 78q-56 65 -56 162q0 131 92.5 226.5t227.5 95.5q97 0 167 -46l34 34h91l-76 -77q57 -68 57 -163q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 51 -24 90l-278 -282q41 -27 97 -27zM150 247q0 -51 23 -89
l277 282q-41 26 -94 26q-89 0 -147.5 -66t-58.5 -153zM620 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Racute.smcp" unicode="&#xf695;" horiz-adv-x="561" 
d="M578 750l-197 -144h-77l172 144h102zM468 0h-124l-80 199h-97l-44 -199h-109l121 550h231q85 0 134.5 -42.5t49.5 -109.5q0 -74 -47 -128.5t-130 -63.5zM327 294h7q47 0 74.5 27t27.5 67q0 30 -21.5 48.5t-58.5 18.5h-132l-36 -161h139z" />
    <glyph glyph-name="Rcaron.smcp" unicode="&#xf696;" horiz-adv-x="561" 
d="M417 606h-96l-64 144h64l56 -97l96 97h69zM468 0h-124l-80 199h-97l-44 -199h-109l121 550h231q85 0 134.5 -42.5t49.5 -109.5q0 -74 -47 -128.5t-130 -63.5zM327 294h7q47 0 74.5 27t27.5 67q0 30 -21.5 48.5t-58.5 18.5h-132l-36 -161h139z" />
    <glyph glyph-name="Rcommaaccent.smcp" unicode="&#xf697;" horiz-adv-x="561" 
d="M257 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM468 0h-124l-80 199h-97l-44 -199h-109l121 550h231q85 0 134.5 -42.5t49.5 -109.5q0 -74 -47 -128.5t-130 -63.5zM327 294h7
q47 0 74.5 27t27.5 67q0 30 -21.5 48.5t-58.5 18.5h-132l-36 -161h139z" />
    <glyph glyph-name="Sacute.smcp" unicode="&#xf698;" horiz-adv-x="511" 
d="M551 750l-197 -144h-77l172 144h102zM245 -12q-171 0 -251 96l69 81q28 -37 78.5 -59t110.5 -22q52 0 75.5 21.5t23.5 53.5q0 24 -29 42t-70 31.5t-82 30.5t-70 50t-29 80q0 67 62.5 118t153.5 51q70 0 130 -25.5t95 -66.5l-68 -76q-25 31 -71 51.5t-91 20.5
q-42 0 -69 -20t-27 -49q0 -23 29 -39.5t70 -30t82 -30.5t70 -51t29 -83q0 -71 -57 -123t-164 -52z" />
    <glyph glyph-name="Scedilla.smcp" unicode="&#xf699;" horiz-adv-x="511" 
d="M171 -194q-74 0 -114 40l31 43q35 -35 84 -35q26 0 40.5 11t14.5 29q0 26 -33 26q-18 0 -32 -15l-35 23l39 67q-115 19 -172 89l69 81q28 -37 78.5 -59t110.5 -22q52 0 75.5 21.5t23.5 53.5q0 24 -29 42t-70 31.5t-82 30.5t-70 50t-29 80q0 67 62.5 118t153.5 51
q70 0 130 -25.5t95 -66.5l-68 -76q-25 31 -71 51.5t-91 20.5q-42 0 -69 -20t-27 -49q0 -23 29 -39.5t70 -30t82 -30.5t70 -51t29 -83q0 -71 -57 -123t-164 -52q-5 0 -14.5 0.5t-13.5 0.5l-26 -42q15 11 32 11q26 0 42.5 -16.5t16.5 -44.5q0 -41 -31 -66t-80 -25z" />
    <glyph glyph-name="Scircumflex.smcp" unicode="&#xf69a;" horiz-adv-x="511" 
d="M482 606h-65l-56 96l-96 -96h-68l125 144h96zM245 -12q-171 0 -251 96l69 81q28 -37 78.5 -59t110.5 -22q52 0 75.5 21.5t23.5 53.5q0 24 -29 42t-70 31.5t-82 30.5t-70 50t-29 80q0 67 62.5 118t153.5 51q70 0 130 -25.5t95 -66.5l-68 -76q-25 31 -71 51.5t-91 20.5
q-42 0 -69 -20t-27 -49q0 -23 29 -39.5t70 -30t82 -30.5t70 -51t29 -83q0 -71 -57 -123t-164 -52z" />
    <glyph glyph-name="Scommaaccent.smcp" unicode="&#xf69b;" horiz-adv-x="511" 
d="M236 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM245 -12q-171 0 -251 96l69 81q28 -37 78.5 -59t110.5 -22q52 0 75.5 21.5t23.5 53.5q0 24 -29 42t-70 31.5t-82 30.5t-70 50t-29 80
q0 67 62.5 118t153.5 51q70 0 130 -25.5t95 -66.5l-68 -76q-25 31 -71 51.5t-91 20.5q-42 0 -69 -20t-27 -49q0 -23 29 -39.5t70 -30t82 -30.5t70 -51t29 -83q0 -71 -57 -123t-164 -52z" />
    <glyph glyph-name="Tbar.smcp" unicode="&#xf69c;" horiz-adv-x="480" 
d="M240 0h-109l48 221h-107l14 62h107l38 172h-162l21 95h434l-21 -95h-163l-38 -172h110l-14 -62h-110z" />
    <glyph glyph-name="Tcaron.smcp" unicode="&#xf69d;" horiz-adv-x="480" 
d="M367 606h-96l-64 144h64l56 -97l96 97h69zM240 0h-109l100 455h-162l21 95h434l-21 -95h-163z" />
    <glyph glyph-name="Tcommaaccent.smcp" unicode="&#xf69e;" horiz-adv-x="480" 
d="M216 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM240 0h-109l100 455h-162l21 95h434l-21 -95h-163z" />
    <glyph glyph-name="Ubreve.smcp" unicode="&#xf69f;" horiz-adv-x="636" 
d="M306 -12q-120 0 -185 50t-65 134q0 20 5 43l74 335h110l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM582 672q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z
" />
    <glyph glyph-name="Uhungarumlaut.smcp" unicode="&#xf6a0;" horiz-adv-x="636" 
d="M306 -12q-120 0 -185 50t-65 134q0 20 5 43l74 335h110l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM491 750l-147 -144h-65l122 144h90zM637 750l-147 -144h-65l122 144h90z" />
    <glyph glyph-name="Umacron.smcp" unicode="&#xf6a1;" horiz-adv-x="636" 
d="M306 -12q-120 0 -185 50t-65 134q0 20 5 43l74 335h110l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM580 616h-362l14 62h362z" />
    <glyph glyph-name="Uogonek.smcp" unicode="&#xf6a2;" horiz-adv-x="636" 
d="M387 -102l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 57 49 100q-114 3 -175.5 53t-61.5 131q0 20 5 43l74 335h110l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-40 -179 -166 -216q-97 -44 -97 -103q0 -17 10.5 -26.5
t28.5 -9.5q31 0 50 37z" />
    <glyph glyph-name="Uring.smcp" unicode="&#xf6a3;" horiz-adv-x="636" 
d="M306 -12q-120 0 -185 50t-65 134q0 20 5 43l74 335h110l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM403 572q-41 0 -67 26t-26 67q0 45 33.5 78.5t78.5 33.5q41 0 67 -26t26 -67q0 -46 -33 -79
t-79 -33zM407 623q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -17t-17 -39q0 -21 13 -34t33 -13z" />
    <glyph glyph-name="Utilde.smcp" unicode="&#xf6a4;" horiz-adv-x="636" 
d="M306 -12q-120 0 -185 50t-65 134q0 20 5 43l74 335h110l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM468 604q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134
q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="Wcircumflex.smcp" unicode="&#xf6a6;" horiz-adv-x="783" 
d="M612 606h-65l-56 96l-96 -96h-68l125 144h96zM559 0h-114l-22 389l-194 -389h-114l-44 550h120l18 -405l205 405h87l25 -406l198 406h122z" />
    <glyph glyph-name="Ycircumflex.smcp" unicode="&#xf6a9;" horiz-adv-x="557" 
d="M502 606h-65l-56 96l-96 -96h-68l125 144h96zM280 0h-108l50 229l-155 321h122l106 -229l203 229h126l-294 -321z" />
    <glyph glyph-name="Zacute.smcp" unicode="&#xf6ab;" horiz-adv-x="520" 
d="M546 750l-197 -144h-77l172 144h102zM429 0h-446l17 80l371 374h-288l21 96h439l-17 -80l-370 -374h294z" />
    <glyph glyph-name="Zdotaccent.smcp" unicode="&#xf6ac;" horiz-adv-x="520" 
d="M429 0h-446l17 80l371 374h-288l21 96h439l-17 -80l-370 -374h294zM407 663q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 15t-15 37q0 26 20 46t46 20q22 0 37.5 -15t15.5 -38z" />
    <glyph glyph-name="Idotaccent.smcp" unicode="&#xf6ad;" horiz-adv-x="245" 
d="M123 0h-109l121 550h109zM272 663q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="parenleft.case" unicode="&#xf6ae;" horiz-adv-x="276" 
d="M192 -66l-76 -43q-72 121 -72 299q0 156 70 309.5t196 275.5l55 -55q-211 -264 -211 -562q0 -103 38 -224z" />
    <glyph glyph-name="parenright.case" unicode="&#xf6af;" horiz-adv-x="276" 
d="M123 732l75 43q73 -123 73 -299q0 -156 -70.5 -309.5t-196.5 -275.5l-54 55q211 263 211 561q0 104 -38 225z" />
    <glyph glyph-name="bracketleft.case" unicode="&#xf6b0;" horiz-adv-x="264" 
d="M170 -100h-203l193 868h203l-16 -72h-126l-160 -724h125z" />
    <glyph glyph-name="bracketright.case" unicode="&#xf6b1;" horiz-adv-x="264" 
d="M143 -100h-202l16 72h125l161 724h-126l16 72h203z" />
    <glyph glyph-name="braceleft.case" unicode="&#xf6b2;" horiz-adv-x="276" 
d="M182 -100h-46q-52 0 -89.5 33.5t-37.5 85.5q0 15 3 29l42 187q2 12 2 17q0 22 -10.5 36.5t-28.5 14.5l14 62q52 0 67 68l41 186q17 74 62.5 111.5t112.5 37.5h61l-16 -72h-61q-64 0 -81 -77l-46 -204q-16 -73 -65 -89q29 -15 29 -55q0 -18 -4 -34l-41 -188q-2 -12 -2 -20
q0 -25 16 -41t40 -16h55z" />
    <glyph glyph-name="braceright.case" unicode="&#xf6b3;" horiz-adv-x="276" 
d="M133 768h46q52 0 89.5 -33.5t37.5 -85.5q0 -17 -3 -30l-42 -186q-2 -12 -2 -18q0 -22 10.5 -36t28.5 -14l-14 -62q-52 0 -67 -68l-41 -186q-34 -149 -175 -149h-61l16 72h61q64 0 81 76l46 205q16 73 65 89q-29 15 -29 55q0 18 4 34l41 188q2 12 2 19q0 25 -16 41.5
t-40 16.5h-55z" />
    <glyph glyph-name="exclamdown.case" unicode="&#xf6b4;" horiz-adv-x="245" 
d="M124 462h91l-84 -462h-128zM207 678q27 0 45.5 -19t18.5 -46q0 -31 -22.5 -53t-54.5 -22q-26 0 -44.5 19t-18.5 45q0 31 22.5 53.5t53.5 22.5z" />
    <glyph glyph-name="questiondown.case" unicode="&#xf6b5;" horiz-adv-x="458" 
d="M225 441l95 29q19 -29 19 -70q0 -40 -22 -70.5t-53 -49t-62 -34.5t-53 -37.5t-22 -48.5q0 -31 25.5 -49.5t73.5 -18.5q77 0 135 55l50 -80q-88 -78 -202 -78q-89 0 -148 40t-59 107q0 49 24.5 85.5t59 57.5t69 38t59 39.5t24.5 51.5q0 19 -13 33zM320 677q27 0 45.5 -19
t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22z" />
    <glyph glyph-name="guillemotleft.case" unicode="&#xf6b6;" horiz-adv-x="489" 
d="M266 162h-95l-120 180l200 177h103l-204 -182zM436 162h-95l-120 180l200 177h103l-204 -182z" />
    <glyph glyph-name="guillemotright.case" unicode="&#xf6b7;" horiz-adv-x="489" 
d="M98 519h95l120 -180l-200 -177h-103l204 182zM268 519h95l120 -180l-200 -177h-103l204 182z" />
    <glyph glyph-name="guilsinglleft.case" unicode="&#xf6b8;" horiz-adv-x="319" 
d="M266 162h-95l-120 180l200 177h103l-204 -182z" />
    <glyph glyph-name="guilsinglright.case" unicode="&#xf6b9;" horiz-adv-x="319" 
d="M99 519h95l120 -180l-200 -177h-103l204 182z" />
    <glyph glyph-name="hyphen.case" unicode="&#xf6ba;" horiz-adv-x="300" 
d="M281 296h-240l20 90h240z" />
    <glyph glyph-name="endash.case" unicode="&#xf6bb;" horiz-adv-x="593" 
d="M574 296h-533l20 90h533z" />
    <glyph glyph-name="emdash.case" unicode="&#xf6bc;" horiz-adv-x="833" 
d="M814 296h-773l20 90h773z" />
    <glyph glyph-name="periodcentered.case" unicode="&#xf6bd;" horiz-adv-x="245" 
d="M137 273q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="bullet.case" unicode="&#xf6be;" horiz-adv-x="358" 
d="M316 352q0 -51 -38.5 -87.5t-89.5 -36.5q-44 0 -73 29t-29 73q0 51 38 88t89 37q44 0 73.5 -29.5t29.5 -73.5z" />
    <glyph glyph-name="cent.case" unicode="&#xf6bf;" horiz-adv-x="497" 
d="M190 0l20 88q-73 19 -115 74t-42 135q0 118 77 201.5t191 87.5l18 81h78l-20 -88q83 -20 123 -86l-79 -60q-23 38 -65 52l-70 -313q58 6 98 52l58 -70q-71 -75 -177 -75l-17 -79h-78zM160 301q0 -86 71 -117l68 306q-62 -12 -100.5 -65.5t-38.5 -123.5z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="244" 
d="M100 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="one.tnum" unicode="&#xf6dc;" horiz-adv-x="612" 
d="M373 0h-117l113 515l-133 -113l-53 71l235 194h102z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="1155" 
d="M871 -12q-108 0 -160 81l-56 -253h-105l148 667h105l-15 -64q64 76 152 76q83 0 134 -53t51 -147q0 -120 -70 -213.5t-184 -93.5zM855 81q69 0 114.5 57.5t45.5 135.5q0 58 -33.5 93t-85.5 35q-74 0 -127 -69l-41 -187q17 -29 50.5 -47t76.5 -18zM533 0h-129l-93 249
h-123l-54 -249h-117l147 667h268q81 0 140 -50t59 -132q0 -91 -56.5 -154t-145.5 -75zM370 352h2q63 0 99.5 34.5t36.5 87.5q0 39 -29 64.5t-68 25.5h-153l-47 -212h159z" />
    <glyph glyph-name="centinferior" unicode="&#xf6df;" horiz-adv-x="310" 
d="M65 -151l13 59q-46 12 -73 45t-27 81q0 68 47.5 118.5t117.5 53.5l10 42h53l-11 -48q53 -13 75 -54l-56 -37q-11 23 -32 32l-40 -181q27 5 53 33l44 -42q-44 -46 -110 -49l-11 -53h-53zM53 37q0 -53 38 -70l39 177q-34 -7 -55.5 -37.5t-21.5 -69.5z" />
    <glyph glyph-name="centsuperior" unicode="&#xf6e0;" horiz-adv-x="310" 
d="M190 416l13 59q-46 12 -73 45t-27 81q0 68 47.5 118.5t117.5 53.5l10 42h53l-11 -48q53 -13 75 -54l-56 -37q-11 23 -32 32l-40 -181q27 5 53 33l44 -42q-44 -46 -110 -49l-11 -53h-53zM178 604q0 -53 38 -70l39 177q-34 -7 -55.5 -37.5t-21.5 -69.5z" />
    <glyph glyph-name="commainferior" unicode="&#xf6e1;" horiz-adv-x="149" 
d="M41 -102q0 -31 -23.5 -67t-61.5 -57l-24 29q16 8 33 22.5t22 27.5q-3 -1 -5 -1q-13 0 -24 11t-11 29q0 21 15.5 35.5t36.5 14.5q17 0 29.5 -12t12.5 -32z" />
    <glyph glyph-name="commasuperior" unicode="&#xf6e2;" horiz-adv-x="149" 
d="M166 465q0 -31 -23.5 -67t-61.5 -57l-24 29q16 8 33 22.5t22 27.5q-3 -1 -5 -1q-13 0 -24 11t-11 29q0 21 15.5 35.5t36.5 14.5q17 0 29.5 -12t12.5 -32z" />
    <glyph glyph-name="dollarinferior" unicode="&#xf6e3;" horiz-adv-x="377" 
d="M67 -206l12 58q-87 15 -123 69l54 49q29 -42 83 -56l25 112q-26 9 -42.5 16.5t-35.5 20.5t-28 31.5t-9 42.5q0 49 43.5 86t114.5 37h9l12 55h53l-14 -62q66 -17 103 -60l-54 -45q-24 30 -63 44l-23 -105q26 -9 42 -17t34.5 -21t28 -31t9.5 -41q0 -55 -41.5 -92.5
t-120.5 -37.5h-5l-11 -53h-53zM211 -38q0 27 -44 46l-22 -100q31 2 48.5 17t17.5 37zM89 150q0 -26 46 -45l21 95q-29 -2 -48 -15.5t-19 -34.5z" />
    <glyph glyph-name="dollarsuperior" unicode="&#xf6e4;" horiz-adv-x="377" 
d="M192 361l12 58q-87 15 -123 69l54 49q29 -42 83 -56l25 112q-26 9 -42.5 16.5t-35.5 20.5t-28 31.5t-9 42.5q0 49 43.5 86t114.5 37h9l12 55h53l-14 -62q66 -17 103 -60l-54 -45q-24 30 -63 44l-23 -105q26 -9 42 -17t34.5 -21t28 -31t9.5 -41q0 -55 -41.5 -92.5
t-120.5 -37.5h-5l-11 -53h-53zM336 529q0 27 -44 46l-22 -100q31 2 48.5 17t17.5 37zM214 717q0 -26 46 -45l21 95q-29 -2 -48 -15.5t-19 -34.5z" />
    <glyph glyph-name="hypheninferior" unicode="&#xf6e5;" horiz-adv-x="183" 
d="M116 22h-148l14 61h147z" />
    <glyph glyph-name="hyphensuperior" unicode="&#xf6e6;" horiz-adv-x="183" 
d="M241 589h-148l14 61h147z" />
    <glyph glyph-name="periodinferior" unicode="&#xf6e7;" horiz-adv-x="151" 
d="M45 -100q0 -20 -16 -35.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 16 36t37 15q18 0 30 -11.5t12 -30.5z" />
    <glyph glyph-name="periodsuperior" unicode="&#xf6e8;" horiz-adv-x="151" 
d="M170 467q0 -20 -16 -35.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 16 36t37 15q18 0 30 -11.5t12 -30.5z" />
    <glyph glyph-name="asuperior" unicode="&#xf6e9;" horiz-adv-x="412" 
d="M268 586q-44 0 -75 -37.5t-31 -88.5q0 -39 21 -59.5t55 -20.5q45 0 81 40l29 131q-24 35 -80 35zM367 640h78l-70 -314h-78l9 42q-43 -50 -100 -50q-53 0 -89 34.5t-36 98.5q0 81 49 139t120 58q73 0 108 -50z" />
    <glyph glyph-name="bsuperior" unicode="&#xf6ea;" horiz-adv-x="411" 
d="M249 379q44 0 75 37.5t31 89.5q0 39 -21 59.5t-55 20.5q-45 0 -81 -40l-29 -131q25 -36 80 -36zM150 326h-78l96 434h78l-35 -162q43 50 100 50q53 0 89 -35t36 -99q0 -81 -49 -138.5t-120 -57.5q-74 0 -108 49z" />
    <glyph glyph-name="dsuperior" unicode="&#xf6eb;" horiz-adv-x="412" 
d="M268 586q-44 0 -75 -37.5t-31 -88.5q0 -39 21 -59.5t55 -20.5q45 0 81 40l29 131q-24 35 -80 35zM394 760h77l-96 -434h-78l9 42q-43 -50 -100 -50q-53 0 -89 34.5t-36 98.5q0 81 49 139t120 58q73 0 108 -50z" />
    <glyph glyph-name="esuperior" unicode="&#xf6ec;" horiz-adv-x="394" 
d="M245 318q-74 0 -120 37.5t-46 106.5q0 75 54 130.5t132 55.5q62 0 104 -37.5t42 -102.5q0 -27 -6 -51h-247q-1 -4 -1 -8q0 -30 26 -51.5t68 -21.5q48 0 86 26l23 -48q-50 -36 -115 -36zM166 512h175v7q0 32 -21 52.5t-57 20.5q-38 0 -63.5 -24t-33.5 -56z" />
    <glyph glyph-name="isuperior" unicode="&#xf6ed;" horiz-adv-x="180" 
d="M186 677q-17 0 -29 11.5t-12 28.5q0 22 16.5 37.5t38.5 15.5q16 0 28 -12t12 -28q0 -22 -16 -37.5t-38 -15.5zM144 326h-78l69 314h78z" />
    <glyph glyph-name="lsuperior" unicode="&#xf6ee;" horiz-adv-x="180" 
d="M144 326h-78l96 434h78z" />
    <glyph glyph-name="msuperior" unicode="&#xf6ef;" horiz-adv-x="585" 
d="M542 326h-77l44 199q3 9 3 18q0 18 -12.5 29t-35.5 11q-36 0 -74 -43l-47 -214h-77l44 199q2 12 2 18q0 40 -48 40q-35 0 -73 -43l-47 -214h-78l69 314h78l-9 -42q43 50 101 50q35 0 60 -16t32 -42q46 58 108 58q39 0 64 -20.5t25 -56.5q0 -13 -2 -22z" />
    <glyph glyph-name="osuperior" unicode="&#xf6f0;" horiz-adv-x="405" 
d="M236 318q-71 0 -114 40t-43 104q0 78 55 132t132 54q71 0 114 -40.5t43 -104.5q0 -77 -55.5 -131t-131.5 -54zM238 381q48 0 76.5 37t28.5 86q0 36 -21.5 57.5t-57.5 21.5q-48 0 -76.5 -37t-28.5 -86q0 -36 21.5 -57.5t57.5 -21.5z" />
    <glyph glyph-name="rsuperior" unicode="&#xf6f1;" horiz-adv-x="250" 
d="M144 326h-78l69 314h78l-10 -44q57 52 116 52l-16 -71q-6 2 -22 2q-24 0 -50 -11.5t-40 -28.5z" />
    <glyph glyph-name="ssuperior" unicode="&#xf6f2;" horiz-adv-x="333" 
d="M199 318q-93 0 -148 61l43 44q15 -21 44.5 -36t62.5 -15q26 0 41.5 11t15.5 27q0 19 -26 32.5t-56.5 22t-56.5 30t-26 53.5q0 41 35 70.5t96 29.5q42 0 78 -15t55 -38l-40 -42q-36 41 -94 41q-25 0 -39.5 -11t-14.5 -28t26 -28.5t57 -18.5t57 -29t26 -58
q0 -42 -36.5 -72.5t-99.5 -30.5z" />
    <glyph glyph-name="tsuperior" unicode="&#xf6f3;" horiz-adv-x="222" 
d="M176 318q-86 0 -86 65q0 11 3 22l39 179h-53l12 56h53l19 86h78l-19 -86h65l-12 -56h-65l-37 -166q-2 -10 -2 -14q0 -24 29 -24q18 0 27 9l6 -54q-22 -17 -57 -17z" />
    <glyph glyph-name="Lslash.smcp" unicode="&#xf6f9;" horiz-adv-x="480" 
d="M-11 153l21 97l84 43l57 257h109l-43 -194l99 51l-22 -98l-98 -50l-36 -164h252l-20 -95h-362l43 196z" />
    <glyph glyph-name="OE.smcp" unicode="&#xf6fa;" horiz-adv-x="939" 
d="M839 0h-401l15 71q-58 -83 -170 -83q-110 0 -177.5 70t-67.5 182q0 130 92 226t229 96q60 0 108.5 -26t71.5 -77l20 91h401l-21 -95h-292l-28 -127h286l-21 -95h-286l-30 -138h292zM480 192l35 158q-15 58 -59 87t-99 29q-87 0 -147 -64.5t-60 -154.5q0 -74 45.5 -118.5
t118.5 -44.5q53 0 96.5 28t69.5 80z" />
    <glyph glyph-name="Scaron.smcp" unicode="&#xf6fd;" horiz-adv-x="511" 
d="M391 606h-96l-64 144h64l56 -97l96 97h69zM245 -12q-171 0 -251 96l69 81q28 -37 78.5 -59t110.5 -22q52 0 75.5 21.5t23.5 53.5q0 24 -29 42t-70 31.5t-82 30.5t-70 50t-29 80q0 67 62.5 118t153.5 51q70 0 130 -25.5t95 -66.5l-68 -76q-25 31 -71 51.5t-91 20.5
q-42 0 -69 -20t-27 -49q0 -23 29 -39.5t70 -30t82 -30.5t70 -51t29 -83q0 -71 -57 -123t-164 -52z" />
    <glyph glyph-name="Zcaron.smcp" unicode="&#xf6ff;" horiz-adv-x="520" 
d="M387 606h-96l-64 144h64l56 -97l96 97h69zM429 0h-446l17 80l371 374h-288l21 96h439l-17 -80l-370 -374h294z" />
    <glyph glyph-name="exclam.smcp" unicode="&#xf721;" horiz-adv-x="245" 
d="M248 550l-96 -362h-83l62 362h117zM75 -11q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="dollar.onum" unicode="&#xf724;" horiz-adv-x="511" 
d="M150 -74l15 69q-113 19 -171 89l69 81q40 -55 124 -74l33 152q-33 12 -55 23t-46 28.5t-36 42.5t-12 56q0 67 62.5 118t153.5 51h4l15 66h78l-17 -76q92 -21 145 -82l-68 -76q-35 43 -99 62l-30 -138q69 -25 110 -59t41 -96q0 -71 -57 -123t-164 -52h-3l-14 -62h-78z
M351 159q0 33 -59 57l-29 -132q46 3 67 24t21 51zM186 397q0 -30 56 -54l27 122q-37 -3 -60 -22t-23 -46z" />
    <glyph glyph-name="ampersand.smcp" unicode="&#xf726;" horiz-adv-x="562" 
d="M476 0h-117q-25 30 -28 35q-75 -46 -149 -46q-81 0 -132 39t-51 110q0 73 43.5 114t119.5 68q-18 52 -18 89q0 65 49 109t119 44q60 0 102.5 -29.5t42.5 -80.5q0 -35 -14 -61.5t-43.5 -45.5t-54 -29.5t-66.5 -24.5q15 -28 38 -63l46 -72q52 55 87 122l71 -40
q-52 -82 -115 -144q32 -47 70 -94zM197 63q44 0 91 30q-34 50 -52 81q-28 46 -47 83q-87 -40 -87 -108q0 -39 25.5 -62.5t69.5 -23.5zM239 409q0 -24 13 -60q58 19 88 39.5t30 56.5q0 23 -14.5 35.5t-39.5 12.5q-33 0 -55 -23t-22 -61z" />
    <glyph glyph-name="zero.onum" unicode="&#xf730;" horiz-adv-x="616" 
d="M588 322q0 -56 -19.5 -113.5t-55 -107.5t-93.5 -81.5t-127 -31.5q-121 0 -187 63.5t-66 175.5q0 44 12 90.5t36.5 90t59 78t83 55.5t104.5 21q121 0 187 -63.5t66 -176.5zM468 314q0 70 -34.5 107t-99.5 37q-55 0 -96.5 -37.5t-59.5 -87t-18 -98.5q0 -70 34.5 -106.5
t99.5 -36.5q80 0 127 71.5t47 150.5z" />
    <glyph glyph-name="one.onum" unicode="&#xf731;" horiz-adv-x="380" 
d="M255 0h-117l87 398l-133 -113l-53 71l235 194h102z" />
    <glyph glyph-name="two.onum" unicode="&#xf732;" horiz-adv-x="580" 
d="M453 0h-453l21 98q405 132 405 270q0 41 -33.5 65.5t-86.5 24.5q-102 0 -181 -69l-51 82q100 91 244 91q102 0 167.5 -44.5t65.5 -123.5q0 -48 -27 -93.5t-74 -82t-99.5 -64.5t-112.5 -51h238z" />
    <glyph glyph-name="three.onum" unicode="&#xf733;" horiz-adv-x="568" 
d="M235 -127q-90 0 -167 37.5t-109 101.5l80 69q28 -49 82.5 -76.5t112.5 -27.5q64 0 100 31t36 81q0 38 -33 61t-100 23q-63 0 -74 -1l24 105q11 -1 92 -1q63 0 103 23.5t40 71.5q0 39 -38 63t-102 24q-94 0 -165 -59l-44 80q41 38 98.5 60.5t120.5 22.5q113 0 181.5 -45.5
t68.5 -126.5q0 -67 -55 -113.5t-125 -53.5q54 -13 88.5 -49.5t34.5 -95.5q0 -87 -72 -146t-178 -59z" />
    <glyph glyph-name="four.onum" unicode="&#xf734;" horiz-adv-x="580" 
d="M506 39h-89l-34 -156h-117l34 156h-318l21 94l366 417h161l-90 -408h89zM323 142l67 303l-270 -303h203z" />
    <glyph glyph-name="five.onum" unicode="&#xf735;" horiz-adv-x="600" 
d="M262 -129q-184 0 -263 130l85 73q53 -99 181 -99q63 0 103.5 37.5t40.5 92.5q0 50 -38 78t-99 28q-70 0 -129 -44l-76 33l77 350h437l-23 -103h-320l-40 -184q56 44 136 44q84 0 140.5 -50.5t56.5 -138.5q0 -103 -75.5 -175t-193.5 -72z" />
    <glyph glyph-name="six.onum" unicode="&#xf736;" horiz-adv-x="601" 
d="M293 -12q-117 0 -182 66.5t-65 186.5q0 78 22.5 154t64 139.5t109 103t149.5 39.5q140 0 214 -95l-74 -81q-47 72 -147 72q-77 0 -129 -59t-75 -142q-5 -11 -7 -24q29 31 78.5 54t102.5 23q90 0 148.5 -49t58.5 -131q0 -105 -78.5 -181t-189.5 -76zM294 92
q62 0 103.5 40.5t41.5 97.5q0 46 -38.5 73t-98.5 27q-79 0 -141 -64q-2 -16 -2 -40q0 -61 37 -97.5t98 -36.5z" />
    <glyph glyph-name="seven.onum" unicode="&#xf737;" horiz-adv-x="532" 
d="M171 -117h-134l374 564h-340l23 103h481l-18 -81z" />
    <glyph glyph-name="eight.onum" unicode="&#xf738;" horiz-adv-x="595" 
d="M279 -12q-110 0 -184 45t-74 130q0 69 51.5 120.5t140.5 72.5q-45 19 -76 55t-31 82q0 89 76 136.5t175 47.5q100 0 173.5 -44.5t73.5 -123.5q0 -68 -49.5 -114.5t-130.5 -61.5q117 -59 117 -154q0 -86 -77 -138.5t-185 -52.5zM330 390q67 5 107.5 30.5t40.5 67.5
q0 36 -37 60t-91 24q-53 0 -90 -25t-37 -67q0 -31 33.5 -56t73.5 -34zM284 92q55 0 97 27.5t42 71.5q0 36 -35 63t-80 38q-70 -4 -116.5 -33.5t-46.5 -74.5q0 -44 38.5 -68t100.5 -24z" />
    <glyph glyph-name="nine.onum" unicode="&#xf739;" horiz-adv-x="601" 
d="M320 562q117 0 182 -66.5t65 -186.5q0 -62 -13.5 -123.5t-42.5 -118t-69 -100t-97.5 -69t-123.5 -25.5q-140 0 -213 94l74 82q44 -72 147 -72q77 0 128.5 58t75.5 142q6 18 7 24q-30 -31 -79.5 -54t-101.5 -23q-90 0 -149 49.5t-59 131.5q0 105 79 181t190 76zM319 458
q-62 0 -104 -41t-42 -97q0 -46 38.5 -73.5t98.5 -27.5q78 0 142 64q1 8 1 40q0 61 -36.5 98t-97.5 37z" />
    <glyph glyph-name="question.smcp" unicode="&#xf73f;" horiz-adv-x="396" 
d="M420 436q0 -38 -20.5 -66t-50 -44t-59 -29t-50 -29t-20.5 -36q0 -14 16 -29l-86 -24q-21 26 -21 53q0 38 28 65.5t61 40t61 33t28 46.5q0 25 -23.5 39.5t-63.5 14.5q-72 0 -129 -54l-40 71q79 73 184 73q82 0 133.5 -35t51.5 -90zM146 -11q-26 0 -44.5 19t-18.5 45
q0 31 22.5 53.5t53.5 22.5q27 0 45.5 -19t18.5 -46q0 -31 -22.5 -53t-54.5 -22z" />
    <glyph glyph-name="A.smcp" unicode="&#xf761;" horiz-adv-x="586" 
d="M294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193z" />
    <glyph glyph-name="B.smcp" unicode="&#xf762;" horiz-adv-x="572" 
d="M324 0h-310l121 550h260q74 0 120 -33.5t46 -92.5q0 -52 -34 -94.5t-91 -51.5q36 -13 58 -43.5t22 -66.5q0 -73 -51.5 -120.5t-140.5 -47.5zM144 95h174q37 0 62 22t25 55q0 26 -18.5 43.5t-51.5 17.5h-161zM356 328h10q36 0 57 21.5t21 51.5q0 27 -20 40.5t-52 13.5
h-148l-29 -127h161z" />
    <glyph glyph-name="C.smcp" unicode="&#xf763;" horiz-adv-x="584" 
d="M314 -12q-123 0 -199.5 69.5t-76.5 180.5q0 133 96.5 228.5t232.5 95.5q81 0 142 -39t87 -103l-97 -37q-17 37 -54.5 60t-81.5 23q-84 0 -148 -65t-64 -155q0 -73 47 -117.5t121 -44.5q86 0 144 71l81 -52q-91 -115 -230 -115z" />
    <glyph glyph-name="D.smcp" unicode="&#xf764;" horiz-adv-x="621" 
d="M245 0h-231l121 550h190q117 0 194 -65.5t77 -170.5q0 -44 -12 -87t-39 -84.5t-66.5 -73t-100 -50.5t-133.5 -19zM253 95h12q104 0 161.5 64t57.5 147q0 67 -46.5 108t-125.5 41h-89l-79 -360h109z" />
    <glyph glyph-name="E.smcp" unicode="&#xf765;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291z" />
    <glyph glyph-name="F.smcp" unicode="&#xf766;" horiz-adv-x="501" 
d="M123 0h-109l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286z" />
    <glyph glyph-name="G.smcp" unicode="&#xf767;" horiz-adv-x="620" 
d="M318 -12q-120 0 -200 67.5t-80 179.5q0 132 94.5 229.5t235.5 97.5q75 0 136.5 -34.5t91.5 -90.5l-96 -45q-18 31 -55.5 52.5t-83.5 21.5q-89 0 -149 -66.5t-60 -155.5q0 -71 48.5 -115.5t122.5 -44.5q65 0 109 30l22 97h-148l20 89h256l-53 -240q-95 -72 -211 -72z" />
    <glyph glyph-name="H.smcp" unicode="&#xf768;" horiz-adv-x="644" 
d="M522 0h-109l52 234h-290l-52 -234h-109l121 550h109l-48 -221h290l48 221h109z" />
    <glyph glyph-name="I.smcp" unicode="&#xf769;" horiz-adv-x="245" 
d="M123 0h-109l121 550h109z" />
    <glyph glyph-name="J.smcp" unicode="&#xf76a;" horiz-adv-x="428" 
d="M134 -12q-115 0 -168 73l65 75q31 -52 94 -52q90 0 112 98l81 368h109l-83 -377q-21 -97 -72.5 -141t-137.5 -44z" />
    <glyph glyph-name="K.smcp" unicode="&#xf76b;" horiz-adv-x="552" 
d="M492 0h-131l-141 228l-58 -52l-39 -176h-109l121 550h109l-54 -248l270 248h135l-292 -260z" />
    <glyph glyph-name="L.smcp" unicode="&#xf76c;" horiz-adv-x="464" 
d="M376 0h-362l121 550h109l-100 -455h252z" />
    <glyph glyph-name="M.smcp" unicode="&#xf76d;" horiz-adv-x="740" 
d="M618 0h-109l87 393l-255 -393h-49l-81 393l-88 -393h-109l121 550h145l75 -365l236 365h148z" />
    <glyph glyph-name="N.smcp" unicode="&#xf76e;" horiz-adv-x="641" 
d="M519 0h-111l-197 403l-88 -403h-109l121 550h116l194 -392l86 392h109z" />
    <glyph glyph-name="O.smcp" unicode="&#xf76f;" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46z" />
    <glyph glyph-name="P.smcp" unicode="&#xf770;" horiz-adv-x="540" 
d="M123 0h-109l121 550h230q84 0 134 -42.5t50 -110.5q0 -80 -57.5 -139t-150.5 -59h-174zM188 294h149q45 0 72 27.5t27 67.5q0 31 -22 48.5t-61 17.5h-129z" />
    <glyph glyph-name="Q.smcp" unicode="&#xf771;" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -74 -32.5 -140.5t-89.5 -112.5l27 -40l-77 -49l-29 43q-59 -23 -119 -23zM314 84q35 0 62 10l-51 78l77 50l52 -80q65 67 65 161q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66
t-58.5 -153q0 -71 44.5 -117t119.5 -46z" />
    <glyph glyph-name="R.smcp" unicode="&#xf772;" horiz-adv-x="561" 
d="M468 0h-124l-80 199h-97l-44 -199h-109l121 550h231q85 0 134.5 -42.5t49.5 -109.5q0 -74 -47 -128.5t-130 -63.5zM327 294h7q47 0 74.5 27t27.5 67q0 30 -21.5 48.5t-58.5 18.5h-132l-36 -161h139z" />
    <glyph glyph-name="S.smcp" unicode="&#xf773;" horiz-adv-x="511" 
d="M245 -12q-171 0 -251 96l69 81q28 -37 78.5 -59t110.5 -22q52 0 75.5 21.5t23.5 53.5q0 24 -29 42t-70 31.5t-82 30.5t-70 50t-29 80q0 67 62.5 118t153.5 51q70 0 130 -25.5t95 -66.5l-68 -76q-25 31 -71 51.5t-91 20.5q-42 0 -69 -20t-27 -49q0 -23 29 -39.5t70 -30
t82 -30.5t70 -51t29 -83q0 -71 -57 -123t-164 -52z" />
    <glyph glyph-name="T.smcp" unicode="&#xf774;" horiz-adv-x="480" 
d="M240 0h-109l100 455h-162l21 95h434l-21 -95h-163z" />
    <glyph glyph-name="U.smcp" unicode="&#xf775;" horiz-adv-x="636" 
d="M306 -12q-120 0 -185 50t-65 134q0 20 5 43l74 335h110l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57z" />
    <glyph glyph-name="V.smcp" unicode="&#xf776;" horiz-adv-x="586" 
d="M305 0h-132l-106 550h122l74 -431l266 431h124z" />
    <glyph glyph-name="W.smcp" unicode="&#xf777;" horiz-adv-x="783" 
d="M559 0h-114l-22 389l-194 -389h-114l-44 550h120l18 -405l205 405h87l25 -406l198 406h122z" />
    <glyph glyph-name="X.smcp" unicode="&#xf778;" horiz-adv-x="577" 
d="M521 0h-127l-112 209l-204 -209h-131l278 282l-144 268h127l102 -195l188 195h131l-262 -267z" />
    <glyph glyph-name="Y.smcp" unicode="&#xf779;" horiz-adv-x="557" 
d="M280 0h-108l50 229l-155 321h122l106 -229l203 229h126l-294 -321z" />
    <glyph glyph-name="Z.smcp" unicode="&#xf77a;" horiz-adv-x="520" 
d="M429 0h-446l17 80l371 374h-288l21 96h439l-17 -80l-370 -374h294z" />
    <glyph glyph-name="Agrave.smcp" unicode="&#xf7e0;" horiz-adv-x="586" 
d="M294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM436 606h-75l-133 144h100z" />
    <glyph glyph-name="Aacute.smcp" unicode="&#xf7e1;" horiz-adv-x="586" 
d="M584 750l-197 -144h-77l172 144h102zM294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193z" />
    <glyph glyph-name="Acircumflex.smcp" unicode="&#xf7e2;" horiz-adv-x="586" 
d="M515 606h-65l-56 96l-96 -96h-68l125 144h96zM294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193z" />
    <glyph glyph-name="Atilde.smcp" unicode="&#xf7e3;" horiz-adv-x="586" 
d="M294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM445 604q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="Adieresis.smcp" unicode="&#xf7e4;" horiz-adv-x="586" 
d="M548 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM339 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125z
M381 198l-43 240l-150 -240h193z" />
    <glyph glyph-name="Aring.smcp" unicode="&#xf7e5;" horiz-adv-x="586" 
d="M379 572q-41 0 -67 26t-26 67q0 45 33.5 78.5t78.5 33.5q41 0 67 -26t26 -67q0 -46 -33 -79t-79 -33zM383 623q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -17t-17 -39q0 -21 13 -34t33 -13zM294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125z
M381 198l-43 240l-150 -240h193z" />
    <glyph glyph-name="AE.smcp" unicode="&#xf7e6;" horiz-adv-x="835" 
d="M735 0h-402l23 103h-200l-86 -103h-125l467 550h444l-21 -95h-293l-28 -127h286l-21 -95h-286l-30 -138h293zM377 198l53 240l-200 -240h147z" />
    <glyph glyph-name="Ccedilla.smcp" unicode="&#xf7e7;" horiz-adv-x="584" 
d="M253 -194q-74 0 -114 40l31 43q35 -35 84 -35q26 0 40.5 11t14.5 29q0 26 -33 26q-18 0 -32 -15l-35 23l39 66q-96 18 -153 83.5t-57 160.5q0 133 96.5 228.5t232.5 95.5q81 0 142 -39t87 -103l-97 -37q-17 37 -54.5 60t-81.5 23q-84 0 -148 -65t-64 -155
q0 -73 47 -117.5t121 -44.5q86 0 144 71l81 -52q-91 -115 -230 -115h-16l-25 -41q15 11 32 11q26 0 42.5 -16.5t16.5 -44.5q0 -41 -31 -66t-80 -25z" />
    <glyph glyph-name="Egrave.smcp" unicode="&#xf7e8;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291zM410 606h-75l-133 144h100z" />
    <glyph glyph-name="Eacute.smcp" unicode="&#xf7e9;" horiz-adv-x="515" 
d="M560 750l-197 -144h-77l172 144h102zM415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291z" />
    <glyph glyph-name="Ecircumflex.smcp" unicode="&#xf7ea;" horiz-adv-x="515" 
d="M491 606h-65l-56 96l-96 -96h-68l125 144h96zM415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291z" />
    <glyph glyph-name="Edieresis.smcp" unicode="&#xf7eb;" horiz-adv-x="515" 
d="M524 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM315 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95
h-286l-30 -138h291z" />
    <glyph glyph-name="Igrave.smcp" unicode="&#xf7ec;" horiz-adv-x="245" 
d="M123 0h-109l121 550h109zM262 606h-75l-133 144h100z" />
    <glyph glyph-name="Iacute.smcp" unicode="&#xf7ed;" horiz-adv-x="245" 
d="M413 750l-197 -144h-77l172 144h102zM123 0h-109l121 550h109z" />
    <glyph glyph-name="Icircumflex.smcp" unicode="&#xf7ee;" horiz-adv-x="245" 
d="M344 606h-65l-56 96l-96 -96h-68l125 144h96zM123 0h-109l121 550h109z" />
    <glyph glyph-name="Idieresis.smcp" unicode="&#xf7ef;" horiz-adv-x="245" 
d="M377 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM168 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM123 0h-109l121 550h109z" />
    <glyph glyph-name="Eth.smcp" unicode="&#xf7f0;" horiz-adv-x="621" 
d="M245 0h-231l53 240h-59l17 76h58l52 234h190q117 0 194 -65.5t77 -170.5q0 -44 -12 -87t-39 -84.5t-66.5 -73t-100 -50.5t-133.5 -19zM265 95q104 0 161.5 64t57.5 147q0 67 -46.5 108t-125.5 41h-89l-31 -139h121l-17 -76h-120l-32 -145h121z" />
    <glyph glyph-name="Ntilde.smcp" unicode="&#xf7f1;" horiz-adv-x="641" 
d="M519 0h-111l-197 403l-88 -403h-109l121 550h116l194 -392l86 392h109zM472 604q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="Ograve.smcp" unicode="&#xf7f2;" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM471 606h-75l-133 144
h100z" />
    <glyph glyph-name="Oacute.smcp" unicode="&#xf7f3;" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM619 750l-197 -144h-77
l172 144h102z" />
    <glyph glyph-name="Ocircumflex.smcp" unicode="&#xf7f4;" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM552 606h-65l-56 96
l-96 -96h-68l125 144h96z" />
    <glyph glyph-name="Otilde.smcp" unicode="&#xf7f5;" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM480 604q-24 0 -42 12
t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="Odieresis.smcp" unicode="&#xf7f6;" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM584 664
q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM375 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37z" />
    <glyph glyph-name="Oslash.smcp" unicode="&#xf7f8;" horiz-adv-x="658" 
d="M311 -12q-102 0 -169 46l-34 -34h-91l77 78q-56 65 -56 162q0 131 92.5 226.5t227.5 95.5q97 0 167 -46l34 34h91l-76 -77q57 -68 57 -163q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 51 -24 90l-278 -282q41 -27 97 -27zM150 247q0 -51 23 -89
l277 282q-41 26 -94 26q-89 0 -147.5 -66t-58.5 -153z" />
    <glyph glyph-name="Ugrave.smcp" unicode="&#xf7f9;" horiz-adv-x="636" 
d="M306 -12q-120 0 -185 50t-65 134q0 20 5 43l74 335h110l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM457 606h-75l-133 144h100z" />
    <glyph glyph-name="Uacute.smcp" unicode="&#xf7fa;" horiz-adv-x="636" 
d="M608 750l-197 -144h-77l172 144h102zM306 -12q-120 0 -185 50t-65 134q0 20 5 43l74 335h110l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57z" />
    <glyph glyph-name="Ucircumflex.smcp" unicode="&#xf7fb;" horiz-adv-x="636" 
d="M538 606h-65l-56 96l-96 -96h-68l125 144h96zM306 -12q-120 0 -185 50t-65 134q0 20 5 43l74 335h110l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57z" />
    <glyph glyph-name="Udieresis.smcp" unicode="&#xf7fc;" horiz-adv-x="636" 
d="M572 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM363 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM306 -12q-120 0 -185 50t-65 134q0 20 5 43l74 335h110
l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57z" />
    <glyph glyph-name="Yacute.smcp" unicode="&#xf7fd;" horiz-adv-x="557" 
d="M572 750l-197 -144h-77l172 144h102zM280 0h-108l50 229l-155 321h122l106 -229l203 229h126l-294 -321z" />
    <glyph glyph-name="Thorn.smcp" unicode="&#xf7fe;" horiz-adv-x="540" 
d="M123 0h-109l121 550h109l-18 -87h119q82 0 133 -41t51 -111q0 -80 -57 -139t-151 -59h-173zM307 208h11q44 0 71 27.5t27 67.5q0 32 -22.5 48.5t-60.5 16.5h-129l-35 -160h138z" />
    <glyph glyph-name="Ydieresis.smcp" unicode="&#xf7ff;" horiz-adv-x="557" 
d="M536 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM327 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM280 0h-108l50 229l-155 321h122l106 -229l203 229h126
l-294 -321z" />
    <glyph glyph-name="uniF8FF" unicode="&#xf8ff;" horiz-adv-x="667" 
d="M201 153l16 70h-77q-32 0 -55.5 -23.5t-23.5 -54.5q0 -26 17 -44t43 -18q31 0 52 18.5t28 51.5zM265 443l14 61q2 12 2 19q0 27 -17 43.5t-45 16.5q-32 0 -55 -22t-23 -54q0 -29 18.5 -46.5t47.5 -17.5h58zM269 265h137l31 137h-137zM564 160q0 29 -18 46t-48 17h-58
l-14 -60q-2 -14 -2 -19q0 -28 17 -44.5t45 -16.5q33 0 55.5 22t22.5 55zM644 522q0 26 -17 43.5t-43 17.5q-31 0 -52 -18.5t-28 -50.5l-16 -71h77q31 0 55 23.5t24 55.5zM607 160q0 -48 -39 -83t-89 -35q-42 0 -70 28t-28 70q0 14 3 29l13 54h-137l-18 -78
q-11 -46 -45 -74.5t-80 -28.5q-41 0 -70 28t-29 69q0 49 38 87.5t87 38.5h83l31 137h-58q-45 0 -72.5 29t-27.5 76q0 48 38.5 83t88.5 35q42 0 70 -28t28 -70q0 -14 -3 -29l-13 -55h137l18 79q11 46 45 74.5t80 28.5q42 0 71 -28t29 -69q0 -50 -38 -88t-87 -38h-83l-31 -137
h55q46 0 74.5 -29.5t28.5 -75.5z" />
    <glyph glyph-name="zero.pzero" horiz-adv-x="616" 
d="M281 -12q-98 0 -158 58l-40 -46h-87l87 101q-33 61 -33 152q0 106 39 202t115 159t170 63q96 0 156 -57l41 47h87l-87 -101q33 -64 33 -153q0 -106 -39 -202.5t-114.5 -159.5t-169.5 -63zM292 92q58 0 104 53t68 126.5t22 147.5q0 16 -4 44l-284 -329q31 -42 94 -42z
M168 247q0 -23 3 -44l284 329q-32 41 -92 41q-58 0 -104 -53t-68.5 -126.5t-22.5 -146.5z" />
    <glyph glyph-name="zero.tonumzero" horiz-adv-x="612" 
d="M586 322q0 -56 -19.5 -113.5t-55 -107.5t-93.5 -81.5t-127 -31.5q-105 0 -170 48l-35 -36h-91l82 83q-39 57 -39 144q0 44 12 90.5t36.5 90t59 78t83 55.5t104.5 21q105 0 168 -48l36 36h91l-82 -83q40 -58 40 -145zM466 314q0 36 -9 63l-254 -258q33 -27 89 -27
q80 0 127 71.5t47 150.5zM158 235q0 -34 8 -61l254 257q-33 27 -88 27t-96.5 -37.5t-59.5 -87t-18 -98.5z" />
    <glyph glyph-name="zero.onumzero" horiz-adv-x="616" 
d="M588 322q0 -56 -19.5 -113.5t-55 -107.5t-93.5 -81.5t-127 -31.5q-105 0 -170 48l-35 -36h-91l82 83q-39 57 -39 144q0 44 12 90.5t36.5 90t59 78t83 55.5t104.5 21q105 0 168 -48l36 36h91l-82 -83q40 -58 40 -145zM468 314q0 36 -9 63l-254 -258q33 -27 89 -27
q80 0 127 71.5t47 150.5zM160 235q0 -34 8 -61l254 257q-33 27 -88 27t-96.5 -37.5t-59.5 -87t-18 -98.5z" />
    <glyph glyph-name="G.alt1" horiz-adv-x="717" 
d="M355 -12q-131 0 -217.5 82t-86.5 214q0 174 115.5 284t281.5 110q92 0 162.5 -45t102.5 -112l-107 -47q-21 45 -69 72.5t-104 27.5q-105 0 -182.5 -81t-77.5 -202q0 -89 54.5 -144t142.5 -55q80 0 136 45.5t82 117.5h-236l23 103h359q-31 -179 -129 -274.5t-250 -95.5z
" />
    <glyph glyph-name="a.alt1" horiz-adv-x="536" 
d="M421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53
q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5z" />
    <glyph glyph-name="f.alt1" horiz-adv-x="308" 
d="M144 0h-105l87 391h-80l20 92h80l6 27q36 167 171 167q59 0 99 -26l-38 -74q-18 14 -49 14q-60 0 -78 -81l-6 -27h98l-20 -92h-98z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="285" 
d="M166 -12q-61 0 -95.5 24.5t-34.5 69.5q0 16 3 34l122 551h105l-116 -525q-3 -13 -3 -22q0 -18 13.5 -28.5t35.5 -10.5q25 0 40 13l6 -84q-27 -22 -76 -22z" />
    <glyph glyph-name="y.alt1" 
d="M198 -196q-150 0 -226 83l64 70q22 -33 66 -50t95 -17q118 0 148 135l12 55q-84 -76 -164 -76q-65 0 -106.5 31t-41.5 90q0 15 5 41l71 317h105l-64 -287q-4 -17 -4 -31q0 -68 89 -68q66 0 129 65l71 321h105l-100 -450q-52 -229 -254 -229z" />
    <glyph glyph-name="G.smcp.alt1" horiz-adv-x="636" 
d="M315 -12q-122 0 -199.5 69t-77.5 179q0 138 98 232t231 94q76 0 137 -34t95 -93l-97 -46q-19 35 -57 55.5t-82 20.5q-85 0 -148.5 -63.5t-63.5 -154.5q0 -74 49 -119t123 -45q57 0 104 34.5t63 91.5h-184l19 90h295q-17 -140 -102.5 -225.5t-202.5 -85.5z" />
    <glyph glyph-name="Gbreve.alt1" horiz-adv-x="737" 
d="M674 789q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66zM355 -12q-131 0 -217.5 82t-86.5 214q0 174 115.5 284t281.5 110q92 0 162.5 -45t102.5 -112l-107 -47q-21 45 -69 72.5t-104 27.5q-105 0 -182.5 -81t-77.5 -202q0 -89 54.5 -144
t142.5 -55q80 0 136 45.5t82 117.5h-236l23 103h359q-31 -179 -129 -274.5t-250 -95.5z" />
    <glyph glyph-name="Gcommaaccent.alt1" horiz-adv-x="737" 
d="M358 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM355 -12q-131 0 -217.5 82t-86.5 214q0 174 115.5 284t281.5 110q92 0 162.5 -45t102.5 -112l-107 -47q-21 45 -69 72.5t-104 27.5
q-105 0 -182.5 -81t-77.5 -202q0 -89 54.5 -144t142.5 -55q80 0 136 45.5t82 117.5h-236l23 103h359q-31 -179 -129 -274.5t-250 -95.5z" />
    <glyph glyph-name="Gcircumflex.alt1" horiz-adv-x="737" 
d="M631 723h-65l-56 96l-96 -96h-68l125 144h96zM355 -12q-131 0 -217.5 82t-86.5 214q0 174 115.5 284t281.5 110q92 0 162.5 -45t102.5 -112l-107 -47q-21 45 -69 72.5t-104 27.5q-105 0 -182.5 -81t-77.5 -202q0 -89 54.5 -144t142.5 -55q80 0 136 45.5t82 117.5h-236
l23 103h359q-31 -179 -129 -274.5t-250 -95.5z" />
    <glyph glyph-name="Gdotaccent.alt1" horiz-adv-x="737" 
d="M559 780q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5zM355 -12q-131 0 -217.5 82t-86.5 214q0 174 115.5 284t281.5 110q92 0 162.5 -45t102.5 -112l-107 -47q-21 45 -69 72.5t-104 27.5q-105 0 -182.5 -81
t-77.5 -202q0 -89 54.5 -144t142.5 -55q80 0 136 45.5t82 117.5h-236l23 103h359q-31 -179 -129 -274.5t-250 -95.5z" />
    <glyph glyph-name="aacute.alt1" horiz-adv-x="536" 
d="M547 700l-197 -144h-77l172 144h102zM421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60
q69 0 114 48l16 72q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5z" />
    <glyph glyph-name="abreve.alt1" horiz-adv-x="536" 
d="M421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53
q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM522 622q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="acircumflex.alt1" horiz-adv-x="536" 
d="M479 556h-65l-56 96l-96 -96h-68l125 144h96zM421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49z
M226 60q69 0 114 48l16 72q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5z" />
    <glyph glyph-name="adieresis.alt1" horiz-adv-x="536" 
d="M511 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM302 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5
t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5z" />
    <glyph glyph-name="agrave.alt1" horiz-adv-x="536" 
d="M421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53
q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM399 556h-75l-133 144h100z" />
    <glyph glyph-name="amacron.alt1" horiz-adv-x="536" 
d="M520 566h-362l14 62h362zM421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72
q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5z" />
    <glyph glyph-name="aogonek.alt1" horiz-adv-x="536" 
d="M421 0q-97 -44 -97 -103q0 -17 10.5 -26.5t28.5 -9.5q31 0 50 37l38 -31q-37 -53 -93 -53q-38 0 -63 19.5t-25 54.5q0 66 65 112h-19l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22
q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5z" />
    <glyph glyph-name="aring.alt1" horiz-adv-x="536" 
d="M346 536q-41 0 -67 26t-26 67q0 45 33.5 78.5t78.5 33.5q41 0 67 -26t26 -67q0 -46 -33 -79t-79 -33zM350 587q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -17t-17 -39q0 -21 13 -34t33 -13zM421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5
q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5z" />
    <glyph glyph-name="atilde.alt1" horiz-adv-x="536" 
d="M421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53
q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM408 554q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EA1.alt1" horiz-adv-x="536" 
d="M421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53
q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM242 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1EA3.alt1" horiz-adv-x="536" 
d="M421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53
q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM304 585l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EA5.alt1" horiz-adv-x="536" 
d="M477 556h-65l-56 96l-96 -96h-68l125 144h96zM726 770l-197 -144h-77l172 144h102zM421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62
q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5z" />
    <glyph glyph-name="uni1EAD.alt1" horiz-adv-x="536" 
d="M478 556h-65l-56 96l-96 -96h-68l125 144h96zM421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49z
M226 60q69 0 114 48l16 72q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM242 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1EA7.alt1" horiz-adv-x="536" 
d="M480 556h-65l-56 96l-96 -96h-68l125 144h96zM421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49z
M226 60q69 0 114 48l16 72q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM249 626h-75l-133 144h100z" />
    <glyph glyph-name="uni1EA9.alt1" horiz-adv-x="536" 
d="M478 556h-65l-56 96l-96 -96h-68l125 144h96zM421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49z
M226 60q69 0 114 48l16 72q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM411 725l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EAF.alt1" horiz-adv-x="536" 
d="M549 773l-197 -144h-77l172 144h102zM421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60
q69 0 114 48l16 72q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM518 604q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="uni1EAB.alt1" horiz-adv-x="536" 
d="M471 523h-65l-56 96l-96 -96h-68l125 144h96zM421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49z
M226 60q69 0 114 48l16 72q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM436 682q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EB1.alt1" horiz-adv-x="536" 
d="M421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53
q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM429 629h-75l-133 144h100zM519 609q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="uni1EB3.alt1" horiz-adv-x="536" 
d="M421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53
q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM328 695l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27zM519 609q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z
" />
    <glyph glyph-name="uni1EB5.alt1" horiz-adv-x="536" 
d="M519 609q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66zM421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62
q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM435 677q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12
q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EB7.alt1" horiz-adv-x="536" 
d="M421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53
q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5zM243 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5zM522 622q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="886" 
d="M842 207h-355q-1 -1 -1 -17q0 -46 37 -81t104 -35q66 0 116 40l34 -73q-67 -53 -149 -53q-146 0 -208 100q-33 -47 -87 -73.5t-113 -26.5q-85 0 -142.5 36t-57.5 104q0 81 59 129t134 48q110 0 172 -68l10 43q5 23 5 49q0 36 -34 57.5t-86 21.5q-86 0 -151 -51l-28 76
q81 62 200 62q146 0 166 -105q30 47 79.5 76t113.5 29q87 0 139.5 -56t52.5 -158q0 -34 -10 -74zM500 281h257q1 5 1 14q0 51 -29 82.5t-84 31.5q-57 0 -94 -38t-51 -90zM370 166l3 14q-15 25 -49.5 39t-76.5 14q-47 0 -83 -26t-36 -64q0 -33 28.5 -51t71.5 -18
q50 0 91.5 26t50.5 66z" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="886" 
d="M842 207h-355q-1 -1 -1 -17q0 -46 37 -81t104 -35q66 0 116 40l34 -73q-67 -53 -149 -53q-146 0 -208 100q-33 -47 -87 -73.5t-113 -26.5q-85 0 -142.5 36t-57.5 104q0 81 59 129t134 48q110 0 172 -68l10 43q5 23 5 49q0 36 -34 57.5t-86 21.5q-86 0 -151 -51l-28 76
q81 62 200 62q146 0 166 -105q30 47 79.5 76t113.5 29q87 0 139.5 -56t52.5 -158q0 -34 -10 -74zM500 281h257q1 5 1 14q0 51 -29 82.5t-84 31.5q-57 0 -94 -38t-51 -90zM370 166l3 14q-15 25 -49.5 39t-76.5 14q-47 0 -83 -26t-36 -64q0 -33 28.5 -51t71.5 -18
q50 0 91.5 26t50.5 66zM707 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="fl.alt1" horiz-adv-x="594" 
d="M144 0h-105l87 391h-80l20 92h80l6 27q36 167 171 167q59 0 99 -26l-38 -74q-18 14 -49 14q-60 0 -78 -81l-6 -27h98l-20 -92h-98zM474 -12q-61 0 -95.5 24.5t-34.5 69.5q0 16 3 34l122 551h105l-116 -525q-3 -14 -3 -22q0 -18 13.5 -28.5t35.5 -10.5q25 0 40 13l6 -84
q-28 -22 -76 -22z" />
    <glyph glyph-name="f_f_l.alt1" horiz-adv-x="902" 
d="M452 0h-105l87 391h-80l20 92h80l6 27q36 167 171 167q59 0 99 -26l-38 -74q-18 14 -49 14q-60 0 -78 -81l-6 -27h98l-20 -92h-98zM783 -12q-61 0 -95.5 24.5t-34.5 69.5q0 16 3 34l122 551h105l-116 -525q-3 -14 -3 -22q0 -18 13.5 -28.5t35.5 -10.5q25 0 40 13l6 -84
q-27 -22 -76 -22zM145 0h-105l86 392h-80l21 91h79l6 27q17 76 60 121.5t116 45.5t118 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="285" 
d="M435 867l-197 -144h-77l172 144h102zM166 -12q-61 0 -95.5 24.5t-34.5 69.5q0 16 3 34l122 551h105l-116 -525q-3 -13 -3 -22q0 -18 13.5 -28.5t35.5 -10.5q25 0 40 13l6 -84q-27 -22 -76 -22z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="285" 
d="M106 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM166 -12q-61 0 -95.5 24.5t-34.5 69.5q0 16 3 34l122 551h105l-116 -525q-3 -13 -3 -22q0 -18 13.5 -28.5t35.5 -10.5q25 0 40 13
l6 -84q-27 -22 -76 -22z" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="330" 
d="M426 622q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM166 -12q-61 0 -95.5 24.5t-34.5 69.5q0 16 3 34l122 551h105l-116 -525q-3 -13 -3 -22q0 -18 13.5 -28.5t35.5 -10.5q25 0 40 13
l6 -84q-27 -22 -76 -22z" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="351" 
d="M166 -12q-61 0 -95.5 24.5t-34.5 69.5q0 16 3 34l122 551h105l-116 -525q-3 -13 -3 -22q0 -18 13.5 -28.5t35.5 -10.5q25 0 40 13l6 -84q-27 -22 -76 -22zM289 175q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z
" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="314" 
d="M2 214l18 79l98 50l72 324h105l-58 -264l99 51l-18 -79l-99 -50l-40 -183q-3 -14 -3 -22q0 -18 13.5 -28.5t35.5 -10.5q25 0 40 13l6 -84q-27 -22 -76 -22q-61 0 -95.5 24.5t-34.5 69.5q0 16 3 34l33 148z" />
    <glyph glyph-name="yacute.alt1" 
d="M563 700l-197 -144h-77l172 144h102zM198 -196q-150 0 -226 83l64 70q22 -33 66 -50t95 -17q118 0 148 135l12 55q-84 -76 -164 -76q-65 0 -106.5 31t-41.5 90q0 15 5 41l71 317h105l-64 -287q-4 -17 -4 -31q0 -68 89 -68q66 0 129 65l71 321h105l-100 -450
q-52 -229 -254 -229z" />
    <glyph glyph-name="ycircumflex.alt1" 
d="M494 556h-65l-56 96l-96 -96h-68l125 144h96zM198 -196q-150 0 -226 83l64 70q22 -33 66 -50t95 -17q118 0 148 135l12 55q-84 -76 -164 -76q-65 0 -106.5 31t-41.5 90q0 15 5 41l71 317h105l-64 -287q-4 -17 -4 -31q0 -68 89 -68q66 0 129 65l71 321h105l-100 -450
q-52 -229 -254 -229z" />
    <glyph glyph-name="ydieresis.alt1" 
d="M527 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM318 614q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM198 -196q-150 0 -226 83l64 70q22 -33 66 -50t95 -17
q118 0 148 135l12 55q-84 -76 -164 -76q-65 0 -106.5 31t-41.5 90q0 15 5 41l71 317h105l-64 -287q-4 -17 -4 -31q0 -68 89 -68q66 0 129 65l71 321h105l-100 -450q-52 -229 -254 -229z" />
    <glyph glyph-name="ygrave.alt1" 
d="M198 -196q-150 0 -226 83l64 70q22 -33 66 -50t95 -17q118 0 148 135l12 55q-84 -76 -164 -76q-65 0 -106.5 31t-41.5 90q0 15 5 41l71 317h105l-64 -287q-4 -17 -4 -31q0 -68 89 -68q66 0 129 65l71 321h105l-100 -450q-52 -229 -254 -229zM413 556h-75l-133 144h100z
" />
    <glyph glyph-name="uni1EA0.smcp" horiz-adv-x="586" 
d="M294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM270 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1EA2.smcp" horiz-adv-x="586" 
d="M294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM342 638l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EA4.smcp" horiz-adv-x="586" 
d="M515 606h-65l-56 96l-96 -96h-68l125 144h96zM764 820l-197 -144h-77l172 144h102zM294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193z" />
    <glyph glyph-name="uni1EAC.smcp" horiz-adv-x="586" 
d="M515 606h-65l-56 96l-96 -96h-68l125 144h96zM294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM269 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1EA6.smcp" horiz-adv-x="586" 
d="M515 606h-65l-56 96l-96 -96h-68l125 144h96zM294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM283 676h-75l-133 144h100z" />
    <glyph glyph-name="uni1EA8.smcp" horiz-adv-x="586" 
d="M516 606h-65l-56 96l-96 -96h-68l125 144h96zM294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM448 775l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EAE.smcp" horiz-adv-x="586" 
d="M584 823l-197 -144h-77l172 144h102zM294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM554 659q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="uni1EAA.smcp" horiz-adv-x="586" 
d="M509 573h-65l-56 96l-96 -96h-68l125 144h96zM294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM475 732q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12
q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EB0.smcp" horiz-adv-x="586" 
d="M294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM452 679h-75l-133 144h100zM556 659q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="uni1EB2.smcp" horiz-adv-x="586" 
d="M294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM364 745l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27zM556 659q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5
l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="uni1EB4.smcp" horiz-adv-x="586" 
d="M556 659q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66zM294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM469 717q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134
q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EB6.smcp" horiz-adv-x="586" 
d="M294 550h132l106 -550h-120l-20 103h-257l-64 -103h-125zM381 198l-43 240l-150 -240h193zM269 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5zM559 672q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40
q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="uni1E02.smcp" horiz-adv-x="572" 
d="M324 0h-310l121 550h260q74 0 120 -33.5t46 -92.5q0 -52 -34 -94.5t-91 -51.5q36 -13 58 -43.5t22 -66.5q0 -73 -51.5 -120.5t-140.5 -47.5zM144 95h174q37 0 62 22t25 55q0 26 -18.5 43.5t-51.5 17.5h-161zM356 328h10q36 0 57 21.5t21 51.5q0 27 -20 40.5t-52 13.5
h-148l-29 -127h161zM444 663q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="uni1E0A.smcp" horiz-adv-x="621" 
d="M245 0h-231l121 550h190q117 0 194 -65.5t77 -170.5q0 -44 -12 -87t-39 -84.5t-66.5 -73t-100 -50.5t-133.5 -19zM253 95h12q104 0 161.5 64t57.5 147q0 67 -46.5 108t-125.5 41h-89l-79 -360h109zM463 663q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 15t-15 37
q0 26 20 46t46 20q22 0 37.5 -15t15.5 -38z" />
    <glyph glyph-name="uni1EB8.smcp" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291zM245 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1EBA.smcp" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291zM318 638l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EBC.smcp" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291zM421 604q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EBE.smcp" horiz-adv-x="515" 
d="M491 606h-65l-56 96l-96 -96h-68l125 144h96zM740 820l-197 -144h-77l172 144h102zM415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291z" />
    <glyph glyph-name="uni1EC0.smcp" horiz-adv-x="515" 
d="M490 606h-65l-56 96l-96 -96h-68l125 144h96zM415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291zM259 676h-75l-133 144h100z" />
    <glyph glyph-name="uni1EC2.smcp" horiz-adv-x="515" 
d="M491 606h-65l-56 96l-96 -96h-68l125 144h96zM415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291zM423 775l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EC4.smcp" horiz-adv-x="515" 
d="M484 573h-65l-56 96l-96 -96h-68l125 144h96zM415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291zM448 732q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59
q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EC6.smcp" horiz-adv-x="515" 
d="M491 606h-65l-56 96l-96 -96h-68l125 144h96zM415 0h-401l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286l-30 -138h291zM245 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="uni1E1E.smcp" horiz-adv-x="501" 
d="M420 663q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38zM123 0h-109l121 550h401l-21 -95h-291l-29 -127h286l-21 -95h-286z" />
    <glyph glyph-name="Gbreve.smcp.alt1" horiz-adv-x="636" 
d="M315 -12q-122 0 -199.5 69t-77.5 179q0 138 98 232t231 94q76 0 137 -34t95 -93l-97 -46q-19 35 -57 55.5t-82 20.5q-85 0 -148.5 -63.5t-63.5 -154.5q0 -74 49 -119t123 -45q57 0 104 34.5t63 91.5h-184l19 90h295q-17 -140 -102.5 -225.5t-202.5 -85.5zM602 672
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 40q33 -66 113 -66q79 0 142 66z" />
    <glyph glyph-name="Gcommaaccent.smcp.alt1" horiz-adv-x="636" 
d="M313 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM315 -12q-122 0 -199.5 69t-77.5 179q0 138 98 232t231 94q76 0 137 -34t95 -93l-97 -46q-19 35 -57 55.5t-82 20.5
q-85 0 -148.5 -63.5t-63.5 -154.5q0 -74 49 -119t123 -45q57 0 104 34.5t63 91.5h-184l19 90h295q-17 -140 -102.5 -225.5t-202.5 -85.5z" />
    <glyph glyph-name="Gcircumflex.smcp.alt1" horiz-adv-x="636" 
d="M558 606h-65l-56 96l-96 -96h-68l125 144h96zM315 -12q-122 0 -199.5 69t-77.5 179q0 138 98 232t231 94q76 0 137 -34t95 -93l-97 -46q-19 35 -57 55.5t-82 20.5q-85 0 -148.5 -63.5t-63.5 -154.5q0 -74 49 -119t123 -45q57 0 104 34.5t63 91.5h-184l19 90h295
q-17 -140 -102.5 -225.5t-202.5 -85.5z" />
    <glyph glyph-name="Gdotaccent.smcp.alt1" horiz-adv-x="636" 
d="M315 -12q-122 0 -199.5 69t-77.5 179q0 138 98 232t231 94q76 0 137 -34t95 -93l-97 -46q-19 35 -57 55.5t-82 20.5q-85 0 -148.5 -63.5t-63.5 -154.5q0 -74 49 -119t123 -45q57 0 104 34.5t63 91.5h-184l19 90h295q-17 -140 -102.5 -225.5t-202.5 -85.5zM488 663
q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="uni1E22.smcp" horiz-adv-x="644" 
d="M522 0h-109l52 234h-290l-52 -234h-109l121 550h109l-48 -221h290l48 221h109zM473 663q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 15t-15 37q0 26 20 46t46 20q22 0 37.5 -15t15.5 -38z" />
    <glyph glyph-name="uni1EC8.smcp" horiz-adv-x="245" 
d="M123 0h-109l121 550h109zM170 638l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1ECA.smcp" horiz-adv-x="245" 
d="M123 0h-109l121 550h109zM98 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1ECC.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM306 -126
q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1ECE.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM378 638l-35 18
q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1ED0.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM550 606h-65l-56 96
l-96 -96h-68l125 144h96zM799 820l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="uni1ED2.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM551 606h-65l-56 96
l-96 -96h-68l125 144h96zM319 676h-75l-133 144h100z" />
    <glyph glyph-name="uni1ED4.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM551 606h-65l-56 96
l-96 -96h-68l125 144h96zM483 775l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1ED6.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM544 573h-65l-56 96
l-96 -96h-68l125 144h96zM508 732q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1ED8.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q123 0 198 -70.5t75 -181.5q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM551 606h-65l-56 96
l-96 -96h-68l125 144h96zM305 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="Ohorn.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q141 0 218 -92q45 29 58 63q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39q0 -39 -27 -77.5t-68 -61.5q33 -57 33 -127q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84
q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46z" />
    <glyph glyph-name="uni1EDA.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q141 0 218 -92q45 29 58 63q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39q0 -39 -27 -77.5t-68 -61.5q33 -57 33 -127q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84
q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM619 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="uni1EDC.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q141 0 218 -92q45 29 58 63q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39q0 -39 -27 -77.5t-68 -61.5q33 -57 33 -127q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84
q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM471 606h-75l-133 144h100z" />
    <glyph glyph-name="uni1EDE.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q141 0 218 -92q45 29 58 63q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39q0 -39 -27 -77.5t-68 -61.5q33 -57 33 -127q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84
q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM378 638l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EE0.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q141 0 218 -92q45 29 58 63q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39q0 -39 -27 -77.5t-68 -61.5q33 -57 33 -127q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84
q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM480 604q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z
" />
    <glyph glyph-name="uni1EE2.smcp" horiz-adv-x="658" 
d="M311 -12q-123 0 -198 70t-75 182q0 131 92.5 226.5t227.5 95.5q141 0 218 -92q45 29 58 63q-1 -1 -7 -1q-16 0 -27.5 11.5t-11.5 30.5q0 23 17 39t39 16q21 0 35 -14t14 -39q0 -39 -27 -77.5t-68 -61.5q33 -57 33 -127q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84
q89 0 147 65.5t58 153.5q0 70 -44 116.5t-119 46.5q-89 0 -147.5 -66t-58.5 -153q0 -71 44.5 -117t119.5 -46zM306 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5z" />
    <glyph glyph-name="uni1E56.smcp" horiz-adv-x="540" 
d="M123 0h-109l121 550h230q84 0 134 -42.5t50 -110.5q0 -80 -57.5 -139t-150.5 -59h-174zM188 294h149q45 0 72 27.5t27 67.5q0 31 -22 48.5t-61 17.5h-129zM440 663q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="uni1E60.smcp" horiz-adv-x="511" 
d="M245 -12q-171 0 -251 96l69 81q28 -37 78.5 -59t110.5 -22q52 0 75.5 21.5t23.5 53.5q0 24 -29 42t-70 31.5t-82 30.5t-70 50t-29 80q0 67 62.5 118t153.5 51q70 0 130 -25.5t95 -66.5l-68 -76q-25 31 -71 51.5t-91 20.5q-42 0 -69 -20t-27 -49q0 -23 29 -39.5t70 -30
t82 -30.5t70 -51t29 -83q0 -71 -57 -123t-164 -52zM412 663q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 46t46 20q23 0 38 -15t15 -38z" />
    <glyph glyph-name="uni021A.smcp" horiz-adv-x="480" 
d="M216 -121q0 -43 -32 -87t-79 -67l-32 33q61 29 78 73h-8q-18 0 -30.5 12.5t-12.5 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM240 0h-109l100 455h-162l21 95h434l-21 -95h-163z" />
    <glyph glyph-name="uni1E6A.smcp" horiz-adv-x="480" 
d="M240 0h-109l100 455h-162l21 95h434l-21 -95h-163zM391 663q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 15t-15 37q0 26 20 46t46 20q22 0 37.5 -15t15.5 -38z" />
    <glyph glyph-name="uni1EE4.smcp" horiz-adv-x="636" 
d="M306 -12q-120 0 -185 50t-65 134q0 20 5 43l74 335h110l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM293 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 46t46 20
q22 0 37.5 -15.5t15.5 -37.5z" />
    <glyph glyph-name="Uhorn.smcp" horiz-adv-x="636" 
d="M306 -12q-120 0 -185.5 50t-65.5 134q0 15 6 43l74 335h110l-72 -323q-4 -24 -4 -34q0 -52 36.5 -80.5t97.5 -28.5q119 0 150 140l72 326h110l-6 -28q42 29 52 59q-1 -1 -7 -1q-16 0 -28 11.5t-12 30.5q0 23 17 39t39 16q21 0 35.5 -14t14.5 -39q0 -46 -34 -87t-89 -67
l-56 -254q-25 -114 -84.5 -171t-170.5 -57z" />
    <glyph glyph-name="uni1EE6.smcp" horiz-adv-x="636" 
d="M306 -12q-120 0 -185 50t-65 134q0 20 5 43l74 335h110l-72 -326q-3 -17 -3 -31q0 -52 36 -80.5t97 -28.5q119 0 150 140l72 326h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM366 638l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57
q0 12 -9 20t-21 8q-23 0 -39 -27z" />
    <glyph glyph-name="uni1EE8.smcp" horiz-adv-x="636" 
d="M608 750l-197 -144h-77l172 144h102zM306 -12q-120 0 -185.5 50t-65.5 134q0 15 6 43l74 335h110l-72 -323q-4 -24 -4 -34q0 -52 36.5 -80.5t97.5 -28.5q119 0 150 140l72 326h110l-6 -28q42 29 52 59q-1 -1 -7 -1q-16 0 -28 11.5t-12 30.5q0 23 17 39t39 16
q21 0 35.5 -14t14.5 -39q0 -46 -34 -87t-89 -67l-56 -254q-25 -114 -84.5 -171t-170.5 -57z" />
    <glyph glyph-name="uni1EEA.smcp" horiz-adv-x="636" 
d="M457 606h-75l-133 144h100zM306 -12q-120 0 -185.5 50t-65.5 134q0 15 6 43l74 335h110l-72 -323q-4 -24 -4 -34q0 -52 36.5 -80.5t97.5 -28.5q119 0 150 140l72 326h110l-6 -28q42 29 52 59q-1 -1 -7 -1q-16 0 -28 11.5t-12 30.5q0 23 17 39t39 16q21 0 35.5 -14
t14.5 -39q0 -46 -34 -87t-89 -67l-56 -254q-25 -114 -84.5 -171t-170.5 -57z" />
    <glyph glyph-name="uni1EEC.smcp" horiz-adv-x="636" 
d="M366 638l-35 18q26 47 78 47q28 0 49.5 -16.5t21.5 -44.5q0 -34 -26 -62h-51q32 27 32 57q0 12 -9 20t-21 8q-23 0 -39 -27zM306 -12q-120 0 -185.5 50t-65.5 134q0 15 6 43l74 335h110l-72 -323q-4 -24 -4 -34q0 -52 36.5 -80.5t97.5 -28.5q119 0 150 140l72 326h110
l-6 -28q42 29 52 59q-1 -1 -7 -1q-16 0 -28 11.5t-12 30.5q0 23 17 39t39 16q21 0 35.5 -14t14.5 -39q0 -46 -34 -87t-89 -67l-56 -254q-25 -114 -84.5 -171t-170.5 -57z" />
    <glyph glyph-name="uni1EEE.smcp" horiz-adv-x="636" 
d="M306 -12q-120 0 -185.5 50t-65.5 134q0 15 6 43l74 335h110l-72 -323q-4 -24 -4 -34q0 -52 36.5 -80.5t97.5 -28.5q119 0 150 140l72 326h110l-6 -28q42 29 52 59q-1 -1 -7 -1q-16 0 -28 11.5t-12 30.5q0 23 17 39t39 16q21 0 35.5 -14t14.5 -39q0 -46 -34 -87t-89 -67
l-56 -254q-25 -114 -84.5 -171t-170.5 -57zM468 604q-24 0 -42 12t-26.5 27t-22 27t-29.5 12q-36 0 -52 -72h-58q26 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q35 0 52 72h59q-29 -134 -119 -134z" />
    <glyph glyph-name="uni1EF0.smcp" horiz-adv-x="636" 
d="M292 -126q0 -26 -20.5 -45.5t-46.5 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 46t46 20q23 0 38 -15.5t15 -37.5zM306 -12q-120 0 -185.5 50t-65.5 134q0 15 6 43l74 335h110l-72 -323q-4 -24 -4 -34q0 -52 36.5 -80.5t97.5 -28.5q119 0 150 140l72 326h110l-6 -28
q42 29 52 59q-1 -1 -7 -1q-16 0 -28 11.5t-12 30.5q0 23 17 39t39 16q21 0 35.5 -14t14.5 -39q0 -46 -34 -87t-89 -67l-56 -254q-25 -114 -84.5 -171t-170.5 -57z" />
    <glyph glyph-name="Wacute.smcp" horiz-adv-x="783" 
d="M559 0h-114l-22 389l-194 -389h-114l-44 550h120l18 -405l205 405h87l25 -406l198 406h122zM680 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Wdieresis.smcp" horiz-adv-x="783" 
d="M559 0h-114l-22 389l-194 -389h-114l-44 550h120l18 -405l205 405h87l25 -406l198 406h122zM645 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q23 0 37.5 -15t14.5 -37zM436 664q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37
q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="Wgrave.smcp" horiz-adv-x="783" 
d="M559 0h-114l-22 389l-194 -389h-114l-44 550h120l18 -405l205 405h87l25 -406l198 406h122zM531 606h-75l-133 144h100z" />
    <glyph glyph-name="Ygrave.smcp" horiz-adv-x="557" 
d="M280 0h-108l50 229l-155 321h122l106 -229l203 229h126l-294 -321zM422 606h-75l-133 144h100z" />
    <glyph glyph-name="afii10065.alt1" horiz-adv-x="536" 
d="M421 0h-105l11 50q-60 -62 -146 -62q-67 0 -114 36.5t-47 103.5q0 81 52 129t128 48q106 0 168 -68l15 66q3 12 3 26q0 35 -33 57t-86 22q-75 0 -138 -51l-28 76q83 62 187 62q89 0 147 -36.5t58 -110.5q0 -23 -6 -49zM226 60q69 0 114 48l16 72q-33 53 -122 53
q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -56.5t71.5 -19.5z" />
    <hkern u1="K" u2="a" k="15" />
    <hkern u1="L" u2="a" k="10" />
    <hkern u1="P" u2="a" k="20" />
    <hkern u1="R" u2="a" k="20" />
    <hkern u1="T" u2="a" k="85" />
    <hkern u1="V" u2="a" k="65" />
    <hkern u1="W" u2="a" k="40" />
    <hkern u1="Y" u2="a" k="115" />
    <hkern u1="a" u2="&#x2122;" k="10" />
    <hkern u1="a" u2="&#x201d;" k="10" />
    <hkern u1="a" u2="&#x201c;" k="10" />
    <hkern u1="a" u2="&#x2019;" k="10" />
    <hkern u1="a" u2="&#x2018;" k="10" />
    <hkern u1="a" u2="&#x1ef2;" k="110" />
    <hkern u1="a" u2="&#x1e84;" k="45" />
    <hkern u1="a" u2="&#x1e82;" k="45" />
    <hkern u1="a" u2="&#x1e80;" k="45" />
    <hkern u1="a" u2="&#x1e6a;" k="100" />
    <hkern u1="a" u2="&#x42a;" k="100" />
    <hkern u1="a" u2="&#x427;" k="100" />
    <hkern u1="a" u2="&#x422;" k="100" />
    <hkern u1="a" u2="&#x40b;" k="100" />
    <hkern u1="a" u2="&#x402;" k="100" />
    <hkern u1="a" u2="&#x3ab;" k="110" />
    <hkern u1="a" u2="&#x3a5;" k="110" />
    <hkern u1="a" u2="&#x3a4;" k="100" />
    <hkern u1="a" u2="&#x38e;" k="110" />
    <hkern u1="a" u2="&#x2bc;" k="10" />
    <hkern u1="a" u2="&#x21a;" k="100" />
    <hkern u1="a" u2="&#x178;" k="110" />
    <hkern u1="a" u2="&#x176;" k="110" />
    <hkern u1="a" u2="&#x174;" k="45" />
    <hkern u1="a" u2="&#x166;" k="100" />
    <hkern u1="a" u2="&#x164;" k="100" />
    <hkern u1="a" u2="&#x162;" k="100" />
    <hkern u1="a" u2="&#xdd;" k="110" />
    <hkern u1="a" u2="&#xae;" k="10" />
    <hkern u1="a" u2="Y" k="110" />
    <hkern u1="a" u2="W" k="45" />
    <hkern u1="a" u2="V" k="70" />
    <hkern u1="a" u2="T" k="100" />
    <hkern u1="a" u2="&#x3f;" k="40" />
    <hkern u1="a" u2="&#x27;" k="10" />
    <hkern u1="a" u2="&#x22;" k="10" />
    <hkern u1="&#xdd;" u2="a" k="115" />
    <hkern u1="&#x136;" u2="a" k="15" />
    <hkern u1="&#x139;" u2="a" k="10" />
    <hkern u1="&#x13b;" u2="a" k="10" />
    <hkern u1="&#x141;" u2="a" k="10" />
    <hkern u1="&#x154;" u2="a" k="20" />
    <hkern u1="&#x156;" u2="a" k="20" />
    <hkern u1="&#x158;" u2="a" k="20" />
    <hkern u1="&#x162;" u2="a" k="85" />
    <hkern u1="&#x164;" u2="a" k="85" />
    <hkern u1="&#x166;" u2="a" k="85" />
    <hkern u1="&#x174;" u2="a" k="40" />
    <hkern u1="&#x176;" u2="a" k="115" />
    <hkern u1="&#x178;" u2="a" k="115" />
    <hkern u1="&#x21a;" u2="a" k="85" />
    <hkern u1="&#x38e;" u2="a" k="115" />
    <hkern u1="&#x393;" u2="a" k="85" />
    <hkern u1="&#x39a;" u2="a" k="15" />
    <hkern u1="&#x3a1;" u2="a" k="20" />
    <hkern u1="&#x3a4;" u2="a" k="85" />
    <hkern u1="&#x3a5;" u2="a" k="115" />
    <hkern u1="&#x3ab;" u2="a" k="115" />
    <hkern u1="&#x403;" u2="a" k="85" />
    <hkern u1="&#x40c;" u2="a" k="15" />
    <hkern u1="&#x40e;" u2="a" k="65" />
    <hkern u1="&#x413;" u2="a" k="85" />
    <hkern u1="&#x416;" u2="a" k="15" />
    <hkern u1="&#x41a;" u2="a" k="15" />
    <hkern u1="&#x420;" u2="a" k="20" />
    <hkern u1="&#x422;" u2="a" k="85" />
    <hkern u1="&#x423;" u2="a" k="65" />
    <hkern u1="&#x490;" u2="a" k="85" />
    <hkern u1="&#x1e56;" u2="a" k="20" />
    <hkern u1="&#x1e6a;" u2="a" k="85" />
    <hkern u1="&#x1e80;" u2="a" k="40" />
    <hkern u1="&#x1e82;" u2="a" k="40" />
    <hkern u1="&#x1e84;" u2="a" k="40" />
    <hkern u1="&#x1ef2;" u2="a" k="115" />
    <hkern u1="&#x2215;" u2="&#xf659;" k="50" />
    <hkern u1="&#x2215;" u2="&#xf656;" k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="dagger"
	k="95" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V.smcp"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="daggerdbl"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="question"
	k="75" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="95" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="J,Jcircumflex,afii10057"
	k="-15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="asterisk"
	k="95" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="75" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="53" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="75" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="55" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="13" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="v,gamma,nu"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="bullet.case"
	k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="45" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="dagger"
	k="75" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V.smcp"
	k="38" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="25" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="daggerdbl"
	k="20" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question"
	k="55" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="80" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="53" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="24" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="asterisk"
	k="60" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="15" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="110" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="55" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V"
	k="60" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="18" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="55" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question.smcp"
	k="45" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="bullet"
	k="18" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-10" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="15" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="V"
	k="15" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Jcircumflex.smcp,J.smcp"
	k="15" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="question"
	k="45" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="V"
	k="20" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="25" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="question"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="V"
	k="13" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="bullet.case"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="5" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V.smcp"
	k="10" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="question"
	k="25" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="5" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="10" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="75" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V"
	k="30" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="65" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="15" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-5" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="X.smcp"
	k="5" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="question"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="J,Jcircumflex,afii10057"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="38" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="33" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="V"
	k="35" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Jcircumflex.smcp,J.smcp"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="25" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V.smcp"
	k="25" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="question"
	k="50" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="35" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="33" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="75" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V"
	k="50" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="80" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="35" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="15" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="5" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="15" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X.smcp"
	k="25" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="15" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="50" />
    <hkern g1="Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,OE.smcp,E.smcp,AE.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="J,Jcircumflex,afii10057"
	k="65" />
    <hkern g1="F,uni1E1E"
	g2="Jcircumflex.smcp,J.smcp"
	k="60" />
    <hkern g1="F,uni1E1E"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="45" />
    <hkern g1="F,uni1E1E"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="40" />
    <hkern g1="F,uni1E1E"
	g2="X.smcp"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="F,uni1E1E"
	g2="ampersand"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="ampersand.smcp"
	k="25" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="J,Jcircumflex,afii10057"
	k="40" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="10" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="50" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="25" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="25" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="15" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="25" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="ampersand.smcp"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="question"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="13" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="V"
	k="25" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="35" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="5" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="question"
	k="35" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="5" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="5" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="60" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="20" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="question"
	k="5" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="45" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="V"
	k="20" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="35" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="V.smcp"
	k="45" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="55" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="55" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="15" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="30" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="45" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="bullet.case"
	k="70" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="70" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="X.smcp"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="ampersand.smcp"
	k="5" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="30" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="x,afii10072,afii10087"
	k="30" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="45" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="15" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="25" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="V"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="50" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="bullet"
	k="45" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="dagger"
	k="130" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V.smcp"
	k="55" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="daggerdbl"
	k="95" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="question"
	k="110" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="130" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="75" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="45" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="asterisk"
	k="170" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="70" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="125" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="v,gamma,nu"
	k="55" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="55" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="bullet.case"
	k="110" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="75" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="ampersand"
	k="5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="15" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="dagger"
	k="100" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V.smcp"
	k="80" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="40" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="daggerdbl"
	k="30" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="question"
	k="75" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="110" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="85" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="60" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="50" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="asterisk"
	k="115" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="105" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="55" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V"
	k="100" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="130" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="15" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="100" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="question.smcp"
	k="55" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="bullet"
	k="30" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="J,Jcircumflex,afii10057"
	k="115" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="V"
	k="5" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="20" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Jcircumflex.smcp,J.smcp"
	k="100" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="75" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="15" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand"
	k="30" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand.smcp"
	k="40" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="15" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="J,Jcircumflex,afii10057"
	k="30" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="35" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="45" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="75" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="45" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="35" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="V"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="22" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="bullet.case"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand.smcp"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="10" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="V.smcp"
	k="5" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="question"
	k="35" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="5" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="45" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="55" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="V.smcp"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="V"
	k="13" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="33" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="v,gamma,nu"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="x,afii10072,afii10087"
	k="15" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="V.smcp"
	k="10" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="question"
	k="30" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="15" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="3" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="70" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="V"
	k="25" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="20" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="-10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="V.smcp"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="65" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="20" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="13" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="J,Jcircumflex,afii10057"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-5" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="v,gamma,nu"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="bullet.case"
	k="70" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Jcircumflex.smcp,J.smcp"
	k="105" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="90" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="55" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="X.smcp"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="95" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand.smcp"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="105" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="x,afii10072,afii10087"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="75" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="J,Jcircumflex,afii10057"
	k="65" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="20" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="V"
	k="5" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="bullet"
	k="30" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="75" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-5" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="38" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="30" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand"
	k="35" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand.smcp"
	k="35" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="J,Jcircumflex,afii10057"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="Jcircumflex.smcp,J.smcp"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="15" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="question"
	k="15" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="V"
	k="20" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="25" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-5" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="3" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="V,afii10062,afii10037"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="35" />
    <hkern g1="V,afii10062,afii10037"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="5" />
    <hkern g1="V,afii10062,afii10037"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="5" />
    <hkern g1="V,afii10062,afii10037"
	g2="J,Jcircumflex,afii10057"
	k="95" />
    <hkern g1="V,afii10062,afii10037"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-12" />
    <hkern g1="V,afii10062,afii10037"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="25" />
    <hkern g1="V,afii10062,afii10037"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="50" />
    <hkern g1="V,afii10062,afii10037"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="15" />
    <hkern g1="V,afii10062,afii10037"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="V,afii10062,afii10037"
	g2="v,gamma,nu"
	k="15" />
    <hkern g1="V,afii10062,afii10037"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="10" />
    <hkern g1="V,afii10062,afii10037"
	g2="bullet.case"
	k="35" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="30" />
    <hkern g1="V,afii10062,afii10037"
	g2="Jcircumflex.smcp,J.smcp"
	k="90" />
    <hkern g1="V,afii10062,afii10037"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="40" />
    <hkern g1="V,afii10062,afii10037"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="40" />
    <hkern g1="V,afii10062,afii10037"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand.smcp"
	k="45" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen,periodcentered,endash,emdash"
	k="55" />
    <hkern g1="V,afii10062,afii10037"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="60" />
    <hkern g1="V,afii10062,afii10037"
	g2="x,afii10072,afii10087"
	k="25" />
    <hkern g1="V,afii10062,afii10037"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="45" />
    <hkern g1="V,afii10062,afii10037"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="5" />
    <hkern g1="V,afii10062,afii10037"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="50" />
    <hkern g1="V.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="5" />
    <hkern g1="V.smcp"
	g2="J,Jcircumflex,afii10057"
	k="50" />
    <hkern g1="V.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="25" />
    <hkern g1="V.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="5" />
    <hkern g1="V.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="65" />
    <hkern g1="V.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-10" />
    <hkern g1="V.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="23" />
    <hkern g1="V.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="15" />
    <hkern g1="V.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="65" />
    <hkern g1="V.smcp"
	g2="ampersand"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="8" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex,afii10057"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v,gamma,nu"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="13" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Jcircumflex.smcp,J.smcp"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="38" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="65" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand.smcp"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x,afii10072,afii10087"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="30" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="13" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="J,Jcircumflex,afii10057"
	k="25" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="25" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="5" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="45" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-2" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="16" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="55" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="ampersand"
	k="5" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="ampersand.smcp"
	k="5" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="V.smcp"
	k="5" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="5" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="J,Jcircumflex,afii10057"
	k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="bullet.case"
	k="75" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="95" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Jcircumflex.smcp,J.smcp"
	k="110" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="ampersand"
	k="43" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="ampersand.smcp"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="120" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="125" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="x,afii10072,afii10087"
	k="75" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="colon,semicolon"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="90" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="J,Jcircumflex,afii10057"
	k="70" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="25" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="10" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="95" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-10" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="25" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="45" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="70" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand"
	k="45" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand.smcp"
	k="45" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="bullet.case"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="60" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="30" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="V"
	k="5" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="20" />
    <hkern g1="ampersand"
	g2="V.smcp"
	k="55" />
    <hkern g1="ampersand"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="15" />
    <hkern g1="ampersand"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="75" />
    <hkern g1="ampersand"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="20" />
    <hkern g1="ampersand"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="85" />
    <hkern g1="ampersand"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="ampersand"
	g2="V"
	k="65" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="103" />
    <hkern g1="ampersand"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="85" />
    <hkern g1="ampersand"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="20" />
    <hkern g1="ampersand.smcp"
	g2="V.smcp"
	k="45" />
    <hkern g1="ampersand.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="ampersand.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="75" />
    <hkern g1="ampersand.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="30" />
    <hkern g1="ampersand.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="110" />
    <hkern g1="ampersand.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="ampersand.smcp"
	g2="V"
	k="80" />
    <hkern g1="ampersand.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="45" />
    <hkern g1="ampersand.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="80" />
    <hkern g1="asterisk"
	g2="J,Jcircumflex,afii10057"
	k="120" />
    <hkern g1="asterisk"
	g2="Jcircumflex.smcp,J.smcp"
	k="105" />
    <hkern g1="asterisk"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="60" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="95" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="question"
	k="50" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="105" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="V"
	k="60" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="120" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="x,afii10072,afii10087"
	k="35" />
    <hkern g1="bullet"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="50" />
    <hkern g1="bullet"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="3" />
    <hkern g1="bullet.case"
	g2="J,Jcircumflex,afii10057"
	k="80" />
    <hkern g1="bullet.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="70" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="28" />
    <hkern g1="bullet.case"
	g2="V"
	k="55" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="95" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="25" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="50" />
    <hkern g1="bullet.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="60" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="question"
	k="25" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="5" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="75" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="V"
	k="40" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="65" />
    <hkern g1="colon,semicolon"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="65" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="question"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="asterisk"
	k="15" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="95" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="V"
	k="55" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="110" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="eth"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="question"
	k="-55" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="-65" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="asterisk"
	k="-65" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-25" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-65" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="V"
	k="-65" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="-60" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="45" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand"
	k="5" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand.smcp"
	k="5" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="exclam,B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Imacron,Iogonek,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Racute,Rcommaaccent,Rcaron"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="parenright,bracketright,braceright,parenright.case,bracketright.case,braceright.case"
	k="-70" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="question"
	k="25" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="75" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="V"
	k="50" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="j,jcircumflex,afii10105"
	k="-50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="J,Jcircumflex,afii10057"
	k="65" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="30" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="95" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="45" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="60" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="75" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="30" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="35" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="55" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="115" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="45" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="55" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="V"
	k="25" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="70" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="bullet"
	k="25" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="25" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V.smcp"
	k="65" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="80" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="60" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="55" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="30" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="95" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="65" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V"
	k="85" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="25" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="25" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="v,gamma,nu"
	k="65" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="55" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="j,jcircumflex,afii10105"
	k="-40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="seven"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one"
	k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="zero,six"
	k="25" />
    <hkern g1="questiondown"
	g2="j,jcircumflex,afii10105"
	k="-160" />
    <hkern g1="questiondown.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="65" />
    <hkern g1="questiondown.case"
	g2="V"
	k="75" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="95" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="5" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="J,Jcircumflex,afii10057"
	k="100" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="100" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="80" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="ampersand"
	k="-5" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="V"
	k="25" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="50" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="85" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="question"
	k="45" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="80" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="45" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="V"
	k="45" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="95" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="V"
	k="30" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="35" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="question"
	k="25" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="75" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="V"
	k="50" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="v,gamma,nu,chi"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="v,gamma,nu,chi"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="v,gamma,nu,chi"
	g2="V"
	k="15" />
    <hkern g1="v,gamma,nu,chi"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="v,gamma,nu,chi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="v,gamma,nu,chi"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="65" />
    <hkern g1="v,gamma,nu,chi"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="35" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="question"
	k="5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="V"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="55" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="35" />
    <hkern g1="X,Chi,afii10039"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="50" />
    <hkern g1="X,Chi,afii10039"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="X,Chi,afii10039"
	g2="v,gamma,nu"
	k="35" />
    <hkern g1="X,Chi,afii10039"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="35" />
    <hkern g1="X,Chi,afii10039"
	g2="bullet.case"
	k="60" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="75" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen,periodcentered,endash,emdash"
	k="45" />
    <hkern g1="X.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="25" />
    <hkern g1="X.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="25" />
    <hkern g1="X.smcp"
	g2="ampersand"
	k="20" />
    <hkern g1="zero,nine"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="25" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="95" />
    <hkern g1="x,afii10087"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="x,afii10087"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="x,afii10087"
	g2="V"
	k="25" />
    <hkern g1="x,afii10087"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="75" />
    <hkern g1="x,afii10087"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="35" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.case,bracketleft.case,braceleft.case"
	g2="j,jcircumflex,afii10105"
	k="-110" />
    <hkern g1="one.numr"
	g2="fraction"
	k="-25" />
    <hkern g1="seven.numr"
	g2="fraction"
	k="50" />
    <hkern g1="fraction"
	g2="one.dnom"
	k="-10" />
    <hkern g1="fraction"
	g2="four.dnom"
	k="50" />
  </font>
</defs></svg>
