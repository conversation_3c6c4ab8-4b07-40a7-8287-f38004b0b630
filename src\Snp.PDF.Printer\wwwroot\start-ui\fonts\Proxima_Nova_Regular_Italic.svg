<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20150824 at Sat Jan  9 01:02:53 2016
 By Everything Fonts
Copyright (c) <PERSON>, 2005. All rights reserved.
</metadata>
<defs>
<font id="Proxima_Nova_Regular_Italic" horiz-adv-x="572" >
  <font-face 
    font-family="Proxima_Nova_Regular_Italic"
    font-weight="400"
    font-style="italic"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 6 3 0 0 2 0 4"
    ascent="790"
    descent="-210"
    x-height="483"
    cap-height="667"
    bbox="-226 -266 1144 887"
    underline-thickness="20"
    underline-position="-153"
    unicode-range="U+000D-FB04"
  />
<missing-glyph horiz-adv-x="507" 
d="M449 -90h-351v842h351v-842zM417 -60v782h-288v-782h288z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="792" 
d="M400 0h-75l92 417h-80l15 66h80l8 37q35 157 158 157q50 0 87 -23l-29 -54q-19 15 -52 15q-69 0 -89 -95l-8 -37h98l-15 -66h-98zM499 -196q-79 0 -115 42l36 54q26 -34 69 -34q68 0 88 87l118 530h75l-118 -530q-33 -149 -153 -149zM753 552q-20 0 -33 12.5t-13 31.5
q0 25 18 41t39 16q19 0 32.5 -13t13.5 -31q0 -25 -18 -41t-39 -16zM117 0h-75l92 418h-80l15 65h80l8 37q35 157 161 157q64 0 105 -41l-40 -45q-24 24 -62 24q-69 0 -89 -95l-8 -37h98l-15 -65h-98z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="509" 
d="M117 0h-75l92 417h-80l15 66h80l8 37q35 157 158 157q50 0 87 -23l-29 -54q-19 15 -52 15q-69 0 -89 -95l-8 -37h98l-15 -66h-98zM215 -196q-79 0 -115 42l36 54q26 -34 69 -34q68 0 88 87l118 530h75l-118 -530q-33 -149 -153 -149zM469 552q-20 0 -33 12.5t-13 31.5
q0 25 18 41t39 16q19 0 32.5 -13t13.5 -31q0 -25 -18 -41t-39 -16z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="567" 
d="M117 0h-75l92 418h-80l15 65h80l8 37q35 157 161 157q64 0 105 -41l-40 -45q-24 24 -62 24q-69 0 -89 -95l-8 -37h98l-15 -65h-98zM400 0h-75l92 418h-80l15 65h80l8 37q35 157 161 157q64 0 105 -41l-40 -45q-24 24 -62 24q-69 0 -89 -95l-8 -37h98l-15 -65h-98z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="509" 
d="M117 0h-75l92 417h-80l15 66h80l8 37q35 157 158 157q50 0 87 -23l-29 -54q-19 15 -52 15q-69 0 -89 -95l-8 -37h98l-15 -66h-98zM469 552q-20 0 -33 12.5t-13 31.5q0 25 18 41t39 16q19 0 32.5 -13t13.5 -31q0 -25 -18 -41t-39 -16zM379 0h-75l107 483h75z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="509" 
d="M117 0h-75l92 417h-80l15 66h80l8 37q35 157 158 157q50 0 87 -23l-29 -54q-19 15 -52 15q-69 0 -89 -95l-8 -37h98l-15 -66h-98zM379 0h-75l147 667h75z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="792" 
d="M400 0h-75l92 417h-80l15 66h80l8 37q35 157 158 157q50 0 87 -23l-29 -54q-19 15 -52 15q-69 0 -89 -95l-8 -37h98l-15 -66h-98zM753 552q-20 0 -33 12.5t-13 31.5q0 25 18 41t39 16q19 0 32.5 -13t13.5 -31q0 -25 -18 -41t-39 -16zM663 0h-75l107 483h75zM117 0h-75
l92 418h-80l15 65h80l8 37q35 157 161 157q64 0 105 -41l-40 -45q-24 24 -62 24q-69 0 -89 -95l-8 -37h98l-15 -65h-98z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="792" 
d="M400 0h-75l92 417h-80l15 66h80l8 37q35 157 158 157q50 0 87 -23l-29 -54q-19 15 -52 15q-69 0 -89 -95l-8 -37h98l-15 -66h-98zM663 0h-75l147 667h75zM117 0h-75l92 418h-80l15 65h80l8 37q35 157 161 157q64 0 105 -41l-40 -45q-24 24 -62 24q-69 0 -89 -95l-8 -37
h98l-15 -65h-98z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="858" 
d="M117 0h-75l92 417h-80l15 66h80l8 37q35 157 158 157q50 0 87 -23l-29 -54q-19 15 -52 15q-69 0 -89 -95l-8 -37h98l-15 -66h-98zM564 -12q-56 0 -99.5 23.5t-68.5 64.5l-17 -76h-75l147 667h75l-56 -253q66 81 160 81q84 0 135.5 -53t51.5 -146q0 -123 -70.5 -215.5
t-182.5 -92.5zM554 55q80 0 132 68t52 159q0 67 -37 106.5t-98 39.5q-44 0 -83 -23.5t-65 -59.5l-47 -212q19 -35 57.5 -56.5t88.5 -21.5z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="835" 
d="M117 0h-75l92 417h-80l15 66h80l8 37q35 157 158 157q50 0 87 -23l-29 -54q-19 15 -52 15q-69 0 -89 -95l-8 -37h98l-15 -66h-98zM706 0h-75l69 317q6 27 6 33q0 78 -100 78q-76 0 -149 -77l-78 -351h-75l147 667h75l-56 -254q86 82 171 82q65 0 104.5 -31.5t39.5 -88.5
q0 -20 -6 -40z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="798" 
d="M117 0h-75l92 417h-80l15 66h80l8 37q35 157 158 157q50 0 87 -23l-29 -54q-19 15 -52 15q-69 0 -89 -95l-8 -37h98l-15 -66h-98zM729 0h-91l-128 220l-101 -82l-30 -138h-75l147 667h75l-96 -437l306 253h99l-263 -219z" />
    <glyph glyph-name=".notdef" horiz-adv-x="507" 
d="M449 -90h-351v842h351v-842zM417 -60v782h-288v-782h288z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="CR" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="258" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="230" 
d="M133 187h-63l91 480h94zM66 -10q-22 0 -36.5 14.5t-14.5 35.5q0 25 18 42.5t43 17.5q21 0 35.5 -15t14.5 -36q0 -24 -18 -41.5t-42 -17.5z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="343" 
d="M153 412h-37q19 194 20 210q2 24 17 39.5t37 15.5q17 0 28.5 -12t11.5 -30q0 -3 -5 -23zM300 412h-37q19 194 20 210q2 24 16.5 39.5t36.5 15.5q17 0 29 -12t12 -30q0 -11 -6 -23z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="590" 
d="M246 0h-61l101 181h-111l-100 -181h-62l101 181h-107l29 53h107l111 199h-108l27 52h110l100 182h62l-101 -182h111l100 182h61l-101 -182h107l-26 -52h-109l-112 -199h111l-28 -53h-112zM314 234l111 199h-110l-111 -199h110z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="593" 
d="M194 -100l21 95q-65 12 -116.5 41.5t-78.5 70.5l57 60q55 -77 154 -98l54 241q-31 12 -51.5 21.5t-45.5 25.5t-39.5 32.5t-25 41t-10.5 52.5q0 79 67.5 136.5t168.5 57.5q11 0 17 -1l21 92h61l-22 -99q111 -24 164 -97l-57 -58q-44 60 -124 81l-49 -219q39 -16 64 -29.5
t53 -34.5t42 -49.5t14 -63.5q0 -85 -65.5 -148t-177.5 -63q-2 0 -8 0.5t-8 0.5l-19 -89h-61zM444 186q0 33 -26.5 56t-76.5 45l-50 -225h6q72 0 109.5 38t37.5 86zM202 494q0 -31 25.5 -52t76.5 -43l46 205h-10q-57 0 -97.5 -33t-40.5 -77z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="731" 
d="M128 0h-54l573 667h55zM505 -12q-63 0 -106.5 37.5t-43.5 97.5q0 85 53 139.5t125 54.5q63 0 106.5 -37.5t43.5 -97.5q0 -85 -53 -139.5t-125 -54.5zM506 38q47 0 82 40t35 99q0 38 -26 63.5t-66 25.5q-47 0 -81.5 -39.5t-34.5 -98.5q0 -38 25.5 -64t65.5 -26zM236 348
q-63 0 -106 37.5t-43 97.5q0 85 52.5 139.5t125.5 54.5q63 0 106 -37.5t43 -97.5q0 -85 -52.5 -139.5t-125.5 -54.5zM238 398q47 0 82 40t35 99q0 38 -26 63.5t-66 25.5q-47 0 -81.5 -39.5t-34.5 -98.5q0 -38 25.5 -64t65.5 -26z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="641" 
d="M538 0h-95q-25 31 -43 56q-90 -68 -183 -68q-88 0 -144.5 43.5t-56.5 123.5q0 46 15.5 83t45.5 63.5t61 43.5t74 35q-23 66 -23 116q0 75 54.5 128t129.5 53q64 0 106 -31.5t42 -88.5q0 -24 -7 -46t-16 -37.5t-28.5 -31.5t-33 -25.5t-41.5 -22.5t-42 -18.5t-46 -18.5
q18 -37 51 -94q34 -59 63 -103q63 68 106 146l58 -34q-68 -103 -128 -165q32 -46 81 -107zM226 50q66 0 138 57q-48 67 -71 110q-37 62 -59 111q-64 -29 -99 -66.5t-35 -96.5q0 -55 36 -85t90 -30zM267 496q0 -37 18 -88q81 30 122.5 60.5t41.5 81.5q0 32 -20.5 50t-52.5 18
q-41 0 -75 -33t-34 -89z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="199" 
d="M153 412h-37q19 194 20 210q2 24 17 39.5t37 15.5q17 0 28.5 -12t11.5 -30q0 -3 -5 -23z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="248" 
d="M141 -169l-53 -30q-63 124 -63 290q0 155 66.5 309t190.5 285l39 -38q-217 -283 -217 -584q0 -105 37 -232z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="248" 
d="M106 655l53 30q63 -124 63 -290q0 -155 -67 -308.5t-191 -285.5l-38 38q217 283 217 583q0 106 -37 233z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="340" 
d="M224 389h-46l32 117l-111 -50l-13 40l113 41l-89 62l31 36l82 -71l19 113h47l-33 -117l111 51l13 -41l-112 -41l89 -62l-32 -36l-82 71z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="499" 
d="M484 311h-191l-48 -213h-57l47 213h-191l11 52h192l45 207h58l-46 -207h192z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="230" 
d="M128 41q0 -45 -33.5 -91t-81.5 -71l-31 33q67 34 84 84h-8q-18 0 -30.5 12t-12.5 32q0 24 18 42t42 18q23 0 38 -15.5t15 -43.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="300" 
d="M262 209h-240l14 66h240z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="230" 
d="M66 -10q-22 0 -36.5 14.5t-14.5 35.5q0 25 18 42.5t43 17.5q21 0 35.5 -15t14.5 -36q0 -24 -18 -41.5t-42 -17.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="296" 
d="M2 -20h-62l393 707h63z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="612" 
d="M278 -12q-105 0 -162 69.5t-57 190.5q0 76 22 151.5t61.5 137.5t100 101t130.5 39q104 0 161 -69t57 -190q0 -103 -36.5 -200t-110 -163.5t-166.5 -66.5zM285 62q66 0 118 58.5t77.5 140t25.5 162.5q0 83 -34 131.5t-107 48.5q-65 0 -117.5 -58.5t-78.5 -139.5t-26 -162
q0 -82 35 -131.5t107 -49.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="339" 
d="M207 0h-83l123 557l-132 -113l-39 51l205 172h73z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="588" 
d="M458 0h-458l18 81q94 58 152.5 95.5t124 85.5t101 84t59.5 75.5t24 76.5q0 49 -40 77t-104 28q-101 0 -171 -65l-39 59q38 36 95.5 58t119.5 22q95 0 161 -45.5t66 -129.5q0 -54 -32.5 -110t-95.5 -111.5t-130 -102t-162 -104.5h326z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="557" 
d="M252 -12q-86 0 -157 38.5t-98 100.5l60 47q25 -52 78.5 -82t115.5 -30q73 0 116 39.5t43 100.5q0 46 -39 74.5t-109 28.5q-53 0 -64 -1l17 76q11 -1 91 -1q68 0 114 30t46 88q0 46 -41.5 76t-110.5 30q-96 0 -169 -66l-35 57q85 83 210 83q103 0 166.5 -44.5t63.5 -123.5
q0 -71 -55 -118t-125 -53q53 -12 87.5 -49t34.5 -94q0 -87 -68 -147t-172 -60z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="558" 
d="M378 0h-83l37 169h-317l17 75l374 423h119l-95 -425h94l-15 -73h-94zM347 242l78 348l-311 -348h233z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="590" 
d="M278 -12q-174 0 -243 133l63 50q50 -109 184 -109q70 0 118.5 46t48.5 114q0 60 -42 93.5t-109 33.5q-74 0 -138 -48l-55 27l75 339h408l-16 -74h-325l-49 -220q56 45 140 45t141 -50t57 -138q0 -100 -73 -171t-185 -71z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="591" 
d="M283 -12q-109 0 -168.5 64.5t-59.5 181.5q0 73 20.5 148.5t58.5 142.5t103.5 109.5t145.5 42.5q138 0 201 -100l-56 -56q-43 82 -150 82q-88 0 -144.5 -73.5t-82.5 -177.5q-5 -15 -5 -20q30 33 83 59t109 26q89 0 145.5 -47.5t56.5 -127.5q0 -104 -74.5 -179t-182.5 -75z
M284 62q72 0 120.5 50.5t48.5 118.5q0 54 -40.5 85.5t-107.5 31.5q-94 0 -168 -82q-2 -18 -2 -48q0 -70 39.5 -113t109.5 -43z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="515" 
d="M161 0h-96l397 593h-355l17 74h455l-13 -57z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="581" 
d="M271 -12q-104 0 -172.5 44.5t-68.5 125.5q0 73 54.5 124.5t147.5 70.5q-48 19 -82 55.5t-34 85.5q0 88 72 135.5t165 47.5t160.5 -44t67.5 -120q0 -71 -52 -117.5t-137 -59.5q52 -24 88.5 -63.5t36.5 -94.5q0 -83 -72 -136.5t-174 -53.5zM320 378q77 5 124.5 37t47.5 85
q0 43 -42 72.5t-102 29.5q-62 0 -105.5 -31.5t-43.5 -84.5q0 -38 38.5 -68t82.5 -40zM274 62q63 0 111 34t48 90q0 44 -40 76t-88 45q-81 -5 -134 -41.5t-53 -92.5q0 -53 44.5 -82t111.5 -29z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="591" 
d="M347 678q109 0 168.5 -64.5t59.5 -181.5q0 -73 -20.5 -148.5t-59 -142.5t-104 -109.5t-145.5 -42.5q-138 0 -201 100l56 56q43 -82 150 -82q47 0 86 22t66 59.5t45 79t30 89.5q4 13 6 21q-31 -33 -84 -59.5t-108 -26.5q-89 0 -146 48t-57 128q0 103 75 178.5t183 75.5z
M345 604q-72 0 -121 -50.5t-49 -118.5q0 -54 41.5 -86t107.5 -32q92 0 169 82q1 9 1 48q0 70 -39.5 113.5t-109.5 43.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="230" 
d="M66 -10q-22 0 -36.5 14.5t-14.5 35.5q0 25 18 42.5t43 17.5q21 0 35.5 -15t14.5 -36q0 -24 -18 -41.5t-42 -17.5zM152 380q-21 0 -35.5 15t-14.5 36q0 25 17.5 42t42.5 17q21 0 35.5 -15t14.5 -35q0 -25 -17.5 -42.5t-42.5 -17.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="230" 
d="M128 41q0 -45 -33.5 -91t-81.5 -71l-31 33q67 34 84 84h-8q-18 0 -30.5 12t-12.5 32q0 24 18 42t42 18q23 0 38 -15.5t15 -43.5zM152 380q-21 0 -35.5 15t-14.5 36q0 25 17.5 42t42.5 17q21 0 35.5 -15t14.5 -35q0 -25 -17.5 -42.5t-42.5 -17.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="499" 
d="M435 90l-393 218l13 53l489 219l-15 -66l-417 -183l336 -181z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="499" 
d="M505 404h-441l12 53h441zM462 211h-441l12 52h441z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="499" 
d="M483 308l-489 -218l15 66l417 181l-336 183l13 60l392 -219z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="462" 
d="M257 211l-67 -26q-24 32 -24 71q0 40 24.5 70t59.5 48.5t70 36.5t59.5 44.5t24.5 60.5q0 40 -32 63.5t-90 23.5q-89 0 -153 -64l-38 56q88 82 201 82q88 0 144.5 -40.5t56.5 -105.5q0 -50 -26 -86.5t-63.5 -58t-75 -40t-63.5 -42t-26 -53.5q0 -23 18 -40zM174 -10
q-21 0 -35.5 14.5t-14.5 35.5q0 25 18 42.5t42 17.5q22 0 36.5 -15t14.5 -36q0 -25 -18 -42t-43 -17z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="783" 
d="M372 -70q-136 0 -226.5 90.5t-90.5 223.5q0 160 119.5 277t277.5 117q139 0 227 -91.5t88 -225.5q0 -111 -52 -173.5t-121 -62.5q-40 0 -64.5 23.5t-26.5 57.5l-1 6q-27 -38 -69.5 -62.5t-88.5 -24.5q-69 0 -111 45t-42 118q0 102 72 176.5t163 74.5q46 0 79.5 -22
t48.5 -56l13 62h68l-57 -271q-2 -12 -2 -21q0 -25 13.5 -38.5t33.5 -13.5q38 0 73 45t35 137q0 124 -78.5 203.5t-204.5 79.5q-144 0 -251.5 -108.5t-107.5 -249.5q0 -121 81.5 -202t205.5 -81q97 0 186 56l18 -26q-98 -63 -208 -63zM364 141q87 0 149 90l29 138
q-10 29 -36.5 51.5t-66.5 22.5q-73 0 -125 -58.5t-52 -132.5q0 -49 27.5 -80t74.5 -31z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="629" 
d="M333 0h-309l147 667h277q74 0 122 -43t48 -110q0 -70 -45.5 -121t-109.5 -58q43 -11 70.5 -50.5t27.5 -86.5q0 -81 -59 -139.5t-169 -58.5zM397 378q68 0 99.5 36.5t31.5 87.5q0 39 -29.5 65t-71.5 26h-189l-48 -215h207zM331 74q68 0 106.5 38t38.5 92q0 44 -29 72
t-78 28h-196l-51 -230h209z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="676" 
d="M368 -12q-134 0 -221 80.5t-87 211.5q0 88 31.5 163.5t83.5 126.5t120 79.5t140 28.5q97 0 167 -42t105 -115l-79 -31q-58 114 -198 114q-112 0 -197 -90t-85 -230q0 -99 61 -160.5t164 -61.5q98 0 179 76l61 -46q-97 -104 -245 -104z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="700" 
d="M251 0h-227l147 667h223q114 0 195.5 -81t81.5 -204q0 -70 -25.5 -136.5t-75 -122t-132.5 -89.5t-187 -34zM261 74h4q143 0 231 88.5t88 215.5q0 92 -59 153.5t-147 61.5h-140l-116 -519h139z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="569" 
d="M461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="550" 
d="M107 0h-83l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="713" 
d="M368 -12q-137 0 -222.5 81t-85.5 211q0 171 111 284.5t270 113.5q91 0 158.5 -40t104.5 -106l-75 -35q-26 51 -79.5 79t-119.5 28q-113 0 -197.5 -92t-84.5 -228q0 -100 61.5 -161t163.5 -61q96 0 175 71l31 136h-230l16 74h313l-54 -245q-108 -110 -256 -110z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="712" 
d="M580 0h-83l67 306h-390l-67 -306h-83l147 667h83l-63 -287h390l63 287h83z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="239" 
d="M107 0h-83l147 667h83z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="475" 
d="M158 -12q-58 0 -106 21.5t-74 62.5l50 60q39 -70 126 -70q60 0 98 37.5t52 102.5l103 465h83l-103 -466q-47 -213 -229 -213z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="601" 
d="M530 0h-97l-190 304l-87 -80l-49 -224h-83l147 667h83l-75 -337l365 337h109l-348 -317z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="517" 
d="M417 0h-393l147 667h83l-132 -593h311z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="809" 
d="M677 0h-83l124 561l-351 -561h-34l-103 561l-123 -561h-83l147 667h115l94 -514l321 514h123z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="708" 
d="M576 0h-80l-269 544l-120 -544h-83l147 667h85l266 -534l118 534h83z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z
" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="587" 
d="M107 0h-83l147 667h248q84 0 134.5 -50.5t50.5 -122.5q0 -40 -14 -78.5t-42.5 -73.5t-80 -56t-118.5 -21h-184zM181 339h175q76 0 118.5 43t42.5 106q0 44 -31.5 74.5t-79.5 30.5h-168z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -99 -41 -183.5t-112 -139.5l42 -59l-67 -36l-39 55q-75 -36 -158 -36zM373 62q56 0 109 25l-78 112l66 36l75 -107q51 44 80.5 110t29.5 144q0 103 -64 162.5
t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="608" 
d="M508 0h-92l-112 265h-140l-57 -265h-83l147 667h245q76 0 132.5 -48t56.5 -126q0 -93 -60 -155t-157 -69zM354 338h3q75 0 117.5 42.5t42.5 105.5q0 46 -34.5 76.5t-78.5 30.5h-166l-57 -255h173z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="586" 
d="M286 -12q-88 0 -161.5 33.5t-108.5 85.5l57 60q35 -49 93 -77t127 -28q72 0 109.5 38t37.5 86q0 36 -34 62.5t-83 45t-97.5 40t-82.5 59t-34 90.5q0 79 67.5 136.5t168.5 57.5q77 0 141.5 -28t99.5 -77l-57 -58q-32 44 -84 67t-109 23t-97.5 -33t-40.5 -77
q0 -33 34 -57.5t82.5 -43.5t97.5 -41t83 -61t34 -92q0 -85 -65.5 -148t-177.5 -63z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="570" 
d="M272 0h-83l131 593h-211l16 74h506l-16 -74h-212z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="700" 
d="M336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-30 -133 -97 -202.5t-193 -69.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="658" 
d="M326 0h-103l-121 667h90l98 -576l353 576h99z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="883" 
d="M624 0h-90l-28 538l-266 -538h-90l-43 667h90l24 -556l276 556h70l29 -556l271 556h94z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="652" 
d="M585 0h-94l-154 282l-272 -282h-107l336 347l-174 320h95l142 -265l252 265h108l-317 -330z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="626" 
d="M301 0h-83l62 282l-178 385h91l139 -310l275 310h103l-347 -385z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="585" 
d="M482 0h-488l15 69l485 524h-369l16 74h481l-14 -69l-487 -524h378z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="243" 
d="M129 -190h-186l193 868h186l-12 -55h-128l-168 -758h128z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="296" 
d="M178 -20l-80 707h59l80 -707h-59z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="243" 
d="M106 -190h-185l12 55h127l169 758h-128l12 55h186z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="432" 
d="M432 333h-59l-73 279l-197 -279h-65l243 334h57z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="564" 
d="M491 -95h-569l13 55h569z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="226" 
d="M295 556h-57l-137 144h78z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="575" 
d="M281 -12q-56 0 -99.5 23.5t-68.5 64.5l-17 -76h-75l147 667h75l-56 -253q66 81 160 81q84 0 135.5 -53t51.5 -146q0 -123 -70.5 -215.5t-182.5 -92.5zM271 55q80 0 132 68t52 159q0 67 -37 106.5t-98 39.5q-44 0 -83 -23.5t-65 -59.5l-47 -212q19 -35 57.5 -56.5
t88.5 -21.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="495" 
d="M262 -12q-99 0 -160.5 58t-61.5 158q0 120 79 205.5t193 85.5q127 0 181 -90l-57 -43q-37 66 -122 66q-88 0 -142 -66t-54 -157q0 -72 42.5 -111t108.5 -39q72 0 122 57l43 -50q-72 -74 -172 -74z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l58 260h75l-148 -667h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="563" 
d="M276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5
t-63 -113.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="283" 
d="M117 0h-75l92 418h-80l15 65h80l8 37q35 157 161 157q64 0 105 -41l-40 -45q-24 24 -62 24q-69 0 -89 -95l-8 -37h98l-15 -65h-98z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="574" 
d="M212 -196q-148 0 -216 82l48 51q49 -71 167 -71q129 0 162 148l14 63q-65 -82 -164 -82q-81 0 -132.5 51t-51.5 149q0 120 70 210t182 90q48 0 95 -24.5t74 -63.5l17 76h75l-104 -469q-46 -210 -236 -210zM253 62q40 0 80 22t68 55l46 209q-20 37 -61 58.5t-89 21.5
q-79 0 -129 -66.5t-50 -154.5q0 -67 36.5 -106t98.5 -39z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="552" 
d="M423 0h-75l69 317q6 27 6 33q0 78 -100 78q-76 0 -149 -77l-78 -351h-75l147 667h75l-56 -254q86 82 171 82q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="225" 
d="M186 552q-20 0 -33 12.5t-13 31.5q0 25 18 41t39 16q19 0 32.5 -13t13.5 -31q0 -25 -18 -41t-39 -16zM96 0h-75l107 483h75z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="225" 
d="M-68 -196q-79 0 -115 42l36 54q25 -34 69 -34q68 0 88 87l118 530h75l-118 -530q-33 -149 -153 -149zM186 552q-20 0 -33 12.5t-13 31.5q0 25 18 41t39 16q19 0 32.5 -13t13.5 -31q0 -25 -18 -41t-39 -16z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="514" 
d="M446 0h-91l-128 220l-101 -82l-30 -138h-75l147 667h75l-96 -437l306 253h99l-263 -219z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="225" 
d="M96 0h-75l147 667h75z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="808" 
d="M679 0h-75l71 324q4 20 4 29q0 35 -23.5 55t-58.5 20q-67 0 -132 -76l-78 -352h-75l72 324q4 26 4 35q-1 30 -22.5 49.5t-61.5 19.5q-64 0 -130 -76l-78 -352h-75l107 483h75l-16 -70q71 82 152 82q58 0 91 -31t33 -59v-2q75 92 168 92q53 0 90 -32t37 -88q0 -10 -5 -40z
" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="551" 
d="M422 0h-75l70 317q6 27 6 33q0 40 -26.5 59t-68.5 19q-82 0 -154 -76l-78 -352h-75l107 483h75l-16 -70q86 82 171 82q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40z" />
    <glyph glyph-name="o" unicode="o" 
d="M261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5z" />
    <glyph glyph-name="p" unicode="p" 
d="M281 -12q-56 0 -99.5 23.5t-68.5 64.5l-58 -260h-75l148 667h75l-16 -69q66 81 160 81q84 0 135.5 -53t51.5 -146q0 -123 -70.5 -215.5t-182.5 -92.5zM271 55q80 0 132 68t52 159q0 67 -37 106.5t-98 39.5q-44 0 -83 -23.5t-65 -59.5l-47 -212q19 -35 57.5 -56.5
t88.5 -21.5z" />
    <glyph glyph-name="q" unicode="q" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-148 -667h-75l56 253q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="330" 
d="M96 0h-75l107 483h75l-17 -74q38 43 79 63.5t101 20.5l-17 -77q-12 4 -38 4q-40 0 -77.5 -23t-62.5 -56z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="465" 
d="M207 -12q-136 0 -204 85l48 52q21 -31 65 -53.5t95 -22.5q50 0 80.5 25t30.5 60q0 25 -26 44t-63.5 32.5t-75 28.5t-63.5 42.5t-26 64.5q0 63 49 106t134 43q60 0 109.5 -22.5t74.5 -56.5l-43 -49q-17 27 -58.5 47t-86.5 20q-47 0 -75.5 -21t-28.5 -53q0 -24 26 -41.5
t63.5 -31t75 -29t63.5 -44t26 -67.5q0 -66 -51.5 -112.5t-138.5 -46.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="294" 
d="M166 -12q-52 0 -81 22t-29 66q0 13 3 28l70 314h-80l15 65h80l29 132h75l-29 -132h98l-15 -65h-98l-66 -298q-2 -12 -2 -20q0 -45 48 -45q28 0 46 16l9 -60q-30 -23 -73 -23z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="551" 
d="M128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78q73 0 149 76l77 352h75l-107 -483h-75l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="490" 
d="M232 0h-82l-94 483h79l72 -396l249 396h84z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="734" 
d="M514 0h-75l-40 388l-212 -388h-75l-47 483h75l32 -385l212 385h66l41 -385l202 385h82z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="486" 
d="M421 0h-83l-102 198l-189 -198h-89l239 248l-122 235h83l93 -184l176 184h90l-228 -235z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="490" 
d="M-46 -185l26 67q18 -8 47 -8q40 0 72 48l49 75l-92 486h79l72 -396l249 396h84l-372 -581q-63 -98 -151 -98q-36 0 -63 11z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="472" 
d="M366 0h-368l12 58l342 359h-262l15 66h364l-13 -57l-344 -361h268z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="260" 
d="M146 -190h-47q-45 0 -78.5 30t-33.5 77q0 15 3 27l46 206q2 12 2 17q0 22 -10.5 37t-28.5 15l12 50q52 0 67 69l45 205q15 67 55.5 101t99.5 34h61l-12 -55h-61q-66 0 -84 -80l-49 -217q-15 -70 -64 -88q29 -15 29 -54q0 -18 -4 -34l-45 -205q-2 -12 -2 -21
q0 -26 16 -42.5t41 -16.5h55z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="211" 
d="M153 -20h-55v707h55v-707z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="260" 
d="M113 678h48q45 0 78 -30t33 -77q0 -16 -3 -28l-46 -205q-2 -12 -2 -17q0 -23 10.5 -37.5t28.5 -14.5l-11 -50q-53 0 -68 -69l-45 -205q-15 -67 -55.5 -101t-99.5 -34h-61l12 55h62q66 0 83 79l49 218q15 70 65 88q-29 15 -29 54q0 18 4 34l45 205q2 12 2 21
q0 25 -16.5 42t-41.5 17h-55z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="503" 
d="M512 667l56 -7q-55 -237 -179 -237q-46 0 -68.5 29.5t-25 65.5t-14.5 65.5t-39 29.5q-36 0 -67.5 -52t-52.5 -139l-54 7q52 237 178 237q38 0 59.5 -19.5t27.5 -48t10 -56.5t15 -47.5t34 -19.5q37 0 69 52.5t51 139.5z" />
    <glyph glyph-name="nbspace" unicode="&#xa0;" horiz-adv-x="258" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="230" 
d="M95 296h62l-90 -480h-94zM162 493q21 0 35.5 -14.5t14.5 -36.5q0 -24 -17.5 -41.5t-42.5 -17.5q-21 0 -35.5 15t-14.5 36q0 25 17.5 42t42.5 17z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="495" 
d="M175 -100l21 96q-72 17 -114 71.5t-42 136.5q0 119 77 204t190 87l16 70h59l-17 -75q86 -16 128 -85l-57 -43q-27 47 -86 62l-82 -369h1q72 0 122 57l43 -50q-72 -74 -172 -74h-8l-20 -88h-59zM118 205q0 -55 25 -91t68 -50l81 362q-79 -9 -126.5 -72.5t-47.5 -148.5z
" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="518" 
d="M24 268l12 54h115q-4 7 -17.5 30t-19.5 34.5t-12 33t-6 42.5q0 86 71 150.5t172 64.5q73 0 131.5 -33t74.5 -87l-76 -32q-8 36 -44 59t-83 23q-64 0 -110 -42t-46 -110q0 -26 8 -48t23.5 -47.5t20.5 -37.5h147l-12 -54h-121v-8q0 -49 -33.5 -95t-75.5 -71q26 8 46 8
q33 0 82.5 -20.5t77.5 -20.5q53 0 91 40l21 -67q-54 -47 -117 -47q-46 0 -108 23t-101 23q-48 0 -115 -40l-16 62q72 29 121 82t49 108q0 7 -2 23h-148z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="598" 
d="M300 95q-92 0 -154 51l-99 -81l-42 52l98 79q-26 44 -26 104q0 114 78 192l-57 74l52 41l57 -74q63 37 134 37q94 0 156 -52l99 81l41 -50l-99 -81q27 -49 27 -105q0 -112 -78 -190l56 -70l-52 -42l-56 71q-62 -37 -135 -37zM155 307q0 -66 41.5 -104.5t109.5 -38.5
q77 0 129 58t52 134q0 62 -39 103.5t-112 41.5q-78 0 -129.5 -59t-51.5 -135z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="626" 
d="M304 0h-83l27 122h-254l12 53h253l24 107h-253l12 52h217l-154 333h91l139 -310l275 310h103l-300 -333h211l-11 -52h-247l-24 -107h247l-12 -53h-246z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="211" 
d="M153 -20h-55v316h55v-316zM153 371h-55v316h55v-316z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="468" 
d="M436 322q0 -48 -32 -87t-92 -57q69 -37 69 -103q0 -64 -51.5 -110t-135.5 -46q-142 0 -215 81l48 46q25 -33 69.5 -52.5t95.5 -19.5q49 0 81 26.5t32 64.5q0 27 -26 46.5t-63 33t-73.5 29t-62.5 43.5t-26 68q0 50 37.5 87.5t107.5 49.5q-91 35 -91 110q0 59 49.5 102
t131.5 43q131 0 195 -75l-45 -41q-46 60 -147 60q-48 0 -78.5 -23t-30.5 -57q0 -27 26 -46t63.5 -32t74.5 -28t63 -43.5t26 -69.5zM359 306q0 32 -25 52t-69 36q-68 -9 -102 -35t-34 -61q0 -34 28.5 -53t93.5 -41q56 15 82 43t26 59z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="272" 
d="M365 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM159 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="778" 
d="M754 334q0 -143 -101.5 -244t-244.5 -101q-142 0 -243 101t-101 244t100.5 244t243.5 101t244.5 -101t101.5 -244zM723 334q0 130 -92.5 222t-222.5 92t-221.5 -92t-91.5 -222t91.5 -222t221.5 -92t222.5 92t92.5 222zM505 201l19 -28q-62 -52 -134 -52q-88 0 -140 53
t-52 133q0 100 72 170.5t164 70.5q107 0 156 -80l-31 -22q-37 69 -127 69q-75 0 -136 -60t-61 -147q0 -65 43 -109.5t112 -44.5q68 0 115 47z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="410" 
d="M267 601q-50 0 -85 -42.5t-35 -100.5q0 -45 23.5 -69t63.5 -24q53 0 96 49l32 143q-28 44 -95 44zM379 640h58l-70 -314h-58l11 46q-46 -54 -108 -54q-53 0 -89 35t-36 99q0 80 48.5 138t120.5 58q77 0 114 -54z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="439" 
d="M220 63h-71l-120 180l200 177h79l-204 -182zM364 63h-71l-120 180l200 177h79l-204 -182z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="504" 
d="M517 457l-55 -246h-55l43 193h-386l12 53h441z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="300" 
d="M262 209h-240l14 66h240z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="494" 
d="M508 465q0 -88 -62.5 -150t-150.5 -62t-149.5 62t-61.5 150t62 150t150 62t150 -61.5t62 -150.5zM479 465q0 76 -54 129.5t-130 53.5q-77 0 -130 -53t-53 -130q0 -76 53.5 -130t129.5 -54t130 54t54 130zM367 343h-38l-42 96h-46l-21 -96h-32l53 243h92q29 0 49.5 -17.5
t20.5 -46.5q0 -35 -25 -58.5t-55 -23.5zM369 519q0 18 -11.5 28.5t-29.5 10.5h-61l-20 -90h67q23 0 39 14.5t16 36.5z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="363" 
d="M435 572h-362l10 48h362z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="290" 
d="M335 555q0 -51 -35.5 -86.5t-86.5 -35.5q-50 0 -85.5 35.5t-35.5 86.5q0 50 35.5 86t85.5 36q51 0 86.5 -36t35.5 -86zM287 555q0 30 -22 52t-52 22t-51.5 -22t-21.5 -52q0 -31 21 -52.5t51 -21.5t52.5 22t22.5 52z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="499" 
d="M487 324h-192l-47 -214h-57l47 214h-192l12 52h192l45 207h58l-46 -207h192zM416 0h-441l11 52h441z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="388" 
d="M380 421h-291l11 49q155 92 218.5 143t63.5 101q0 31 -25 47.5t-60 16.5q-68 0 -112 -47l-28 40q54 56 142 56q59 0 101.5 -27.5t42.5 -81.5q0 -60 -63.5 -116t-189.5 -132h201z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="388" 
d="M407 535q0 -47 -40.5 -84t-106.5 -37q-117 0 -160 73l40 34q40 -58 118 -58q41 0 64.5 22.5t23.5 56.5q0 27 -27 43.5t-72 16.5q-23 0 -30 -1l11 50q28 -2 61 -2q42 0 66.5 18t24.5 50q0 28 -27 44.5t-65 16.5q-58 0 -108 -43l-23 39q58 53 135 53q65 0 107 -26.5
t42 -75.5q0 -44 -31 -72t-80 -32q32 -6 54.5 -29t22.5 -57z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="226" 
d="M327 700l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="564" 
d="M103 36l-50 -220h-75l151 667h75l-71 -317q-13 -56 11.5 -84.5t72.5 -28.5q40 0 84 22t73 53l79 355h75l-82 -370q-13 -58 34 -58q10 0 20 2l-9 -65q-22 -4 -41 -4q-89 0 -84 86q-84 -86 -173 -86q-69 0 -90 48z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="449" 
d="M301 -100h-45l160 722h-89l-160 -722h-45l94 423q-57 0 -97 44.5t-40 111.5q0 73 56.5 130.5t137.5 57.5h198z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="230" 
d="M110 190q-21 0 -35.5 14.5t-14.5 35.5q0 25 17.5 42.5t42.5 17.5q21 0 35.5 -15t14.5 -36q0 -25 -17.5 -42t-42.5 -17z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="205" 
d="M19 -191q-68 0 -108 38l27 36q33 -35 83 -35q29 0 45 13t16 34q0 30 -37 30q-20 0 -35 -17l-29 19l50 84h45l-41 -66q15 11 34 11q26 0 42 -15.5t16 -43.5q0 -39 -30 -63.5t-78 -24.5z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="235" 
d="M214 421h-60l69 318l-78 -66l-29 38l133 110h53z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="405" 
d="M236 318q-68 0 -109 39.5t-41 102.5q0 78 53.5 133t129.5 55q68 0 109 -39.5t41 -103.5q0 -77 -53.5 -132t-129.5 -55zM238 366q54 0 87.5 41.5t33.5 98.5q0 41 -25 66.5t-66 25.5q-54 0 -88 -41.5t-34 -97.5q0 -42 25 -67.5t67 -25.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="439" 
d="M77 420h71l120 -180l-200 -177h-79l204 182zM221 420h71l120 -180l-200 -177h-79l204 182z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="770" 
d="M180 267h-60l69 318l-78 -66l-29 38l133 110h53zM701 100h-58l-22 -100h-59l22 100h-196l9 44l229 256h83l-55 -251h57zM594 149l43 194l-178 -194h135zM683 667l-574 -667h-55l573 667h56z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="807" 
d="M180 267h-60l69 318l-78 -66l-29 38l133 110h53zM705 0h-291l11 49q155 92 218.5 143t63.5 101q0 31 -25 47.5t-60 16.5q-68 0 -112 -47l-28 40q54 56 142 56q59 0 101.5 -27.5t42.5 -81.5q0 -60 -63.5 -116t-189.5 -132h201zM683 667l-574 -667h-55l573 667h56z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="878" 
d="M810 100h-58l-22 -100h-59l22 100h-196l9 44l229 256h83l-55 -251h57zM703 149l43 194l-178 -194h135zM791 667l-574 -667h-55l573 667h56zM373 381q0 -47 -40.5 -84t-106.5 -37q-117 0 -160 73l40 34q40 -58 118 -58q41 0 64.5 22.5t23.5 56.5q0 27 -27 43.5t-72 16.5
q-23 0 -30 -1l11 50q28 -2 61 -2q42 0 66.5 18t24.5 50q0 28 -27 44.5t-65 16.5q-58 0 -108 -43l-23 39q58 53 135 53q65 0 107 -26.5t42 -75.5q0 -44 -31 -72t-80 -32q32 -6 54.5 -29t22.5 -57z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="394" 
d="M204 271l66 27q23 -32 23 -71q0 -40 -24.5 -70t-59 -49t-69.5 -37t-59.5 -44t-24.5 -61q0 -40 32 -63.5t90 -23.5q87 0 154 65l37 -57q-88 -82 -201 -82q-88 0 -144.5 40.5t-56.5 106.5q0 42 18.5 75t47 52t61 38t61 33t47 35t18.5 47q0 22 -16 39zM285 493
q22 0 36.5 -15t14.5 -36q0 -25 -18 -42t-43 -17q-21 0 -35.5 14.5t-14.5 36.5q0 24 18 41.5t42 17.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM491 723h-57l-137 144h78z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM635 867l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="658" 
d="M567 723h-49l-60 106l-103 -106h-53l127 144h72zM595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM505 721q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="658" 
d="M595 776q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM389 776q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM595 0h-91l-26 148h-333l-91 -148h-99l415 667h103z
M470 222l-63 359l-220 -359h283z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM439 690q-39 0 -64 25t-25 64q0 43 32 75.5t76 32.5q39 0 64 -25t25 -64q0 -44 -32 -76t-76 -32zM442 730q26 0 45.5 19t19.5 45q0 24 -14 38.5t-38 14.5q-26 0 -45.5 -19t-19.5 -45
q0 -23 14.5 -38t37.5 -15z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="947" 
d="M839 0h-437l32 148h-259l-124 -148h-98l563 667h470l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM451 222l77 354l-296 -354h219z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="676" 
d="M306 -191q-68 0 -108 38l27 36q33 -35 83 -35q29 0 45 13t16 34q0 30 -37 30q-20 0 -35 -17l-29 19l39 66q-111 18 -179 95t-68 192q0 88 31.5 163.5t83.5 126.5t120 79.5t140 28.5q97 0 167 -42t105 -115l-79 -31q-58 114 -198 114q-112 0 -197 -90t-85 -230
q0 -99 61 -160.5t164 -61.5q98 0 179 76l61 -46q-97 -104 -245 -104q-13 0 -19 1l-27 -44q15 11 34 11q26 0 42 -15.5t16 -43.5q0 -39 -30 -63.5t-78 -24.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="569" 
d="M461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM460 723h-57l-137 144h78z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="569" 
d="M603 867l-201 -144h-59l180 144h80zM461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="569" 
d="M534 723h-49l-60 106l-103 -106h-53l127 144h72zM461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="569" 
d="M562 776q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM356 776q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM461 0h-437l147 667h437l-16 -74h-354l-48 -215h347
l-16 -74h-348l-51 -230h355z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="239" 
d="M107 0h-83l147 667h83zM282 723h-57l-137 144h78z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="239" 
d="M107 0h-83l147 667h83zM426 867l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="239" 
d="M358 723h-49l-60 106l-103 -106h-53l127 144h72zM107 0h-83l147 667h83z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="239" 
d="M385 776q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM179 776q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM107 0h-83l147 667h83z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="728" 
d="M279 0h-227l66 299h-96l15 69h96l66 299h223q114 0 195.5 -81t81.5 -204q0 -70 -25.5 -136.5t-75 -122t-132.5 -89.5t-187 -34zM370 299h-170l-50 -225h143q143 0 231 88.5t88 215.5q0 92 -59 153.5t-147 61.5h-140l-50 -225h169z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="708" 
d="M530 721q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134zM576 0h-80l-269 544l-120 -544h-83l147 667h85l266 -534l118 534h83z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="765" 
d="M546 723h-57l-137 144h78zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5
q0 -104 64 -163t161 -59z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="765" 
d="M689 867l-201 -144h-59l180 144h80zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5
t-82.5 -228.5q0 -104 64 -163t161 -59z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="765" 
d="M622 723h-49l-60 106l-103 -106h-53l127 144h72zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5
q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z
M559 721q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="765" 
d="M649 776q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM443 776q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM368 -12q-133 0 -220.5 78.5t-87.5 213.5
q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="499" 
d="M379 150l-115 147l-183 -150l-33 40l185 150l-115 147l42 34l113 -147l184 150l32 -40l-183 -151l114 -147z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="765" 
d="M368 -12q-112 0 -194 57l-41 -45h-66l69 78q-76 79 -76 202q0 167 109.5 282.5t265.5 115.5q109 0 189 -54l39 43h66l-67 -74q81 -81 81 -206q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 87 -50 147l-377 -422q58 -45 145 -45zM148 284
q0 -87 45 -143l376 421q-57 42 -139 42q-117 0 -199.5 -91.5t-82.5 -228.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="700" 
d="M336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-30 -133 -97 -202.5t-193 -69.5zM513 723h-57l-137 144h78z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="700" 
d="M657 867l-201 -144h-59l180 144h80zM336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-30 -133 -97 -202.5t-193 -69.5z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="700" 
d="M588 723h-49l-60 106l-103 -106h-53l127 144h72zM336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-30 -133 -97 -202.5t-193 -69.5z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="700" 
d="M616 776q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM410 776q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49
l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-30 -133 -97 -202.5t-193 -69.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="626" 
d="M301 0h-83l62 282l-178 385h91l139 -310l275 310h103l-347 -385zM619 867l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="587" 
d="M107 0h-83l147 667h83l-26 -119h175q74 0 124.5 -50.5t50.5 -122.5q0 -41 -15 -80.5t-44 -73.5t-80 -54.5t-116 -20.5h-184zM329 220q77 0 119.5 43.5t42.5 104.5q0 45 -33 75.5t-78 30.5h-169l-56 -254h174z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="597" 
d="M559 145q0 -63 -50.5 -110t-137.5 -47q-68 0 -115.5 21t-87.5 68l47 48q59 -76 159 -76q50 0 80 26t30 61q0 25 -25 42.5t-60 29.5t-70.5 26t-60.5 40t-25 63q0 38 19.5 66t48 44t56.5 29t47.5 29.5t19.5 37.5q0 30 -30 48.5t-68 18.5q-48 0 -83 -30t-47 -81l-110 -499
h-75l110 499q18 79 71.5 128.5t135.5 49.5q68 0 121 -34t53 -87q0 -36 -20 -62.5t-49 -41.5t-57.5 -28.5t-48.5 -32t-20 -43.5q0 -23 25 -40t60.5 -29t71 -26.5t60.5 -42t25 -65.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM408 556h-57
l-137 144h78z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM550 700
l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM483 556h-49
l-60 106l-103 -106h-53l127 144h72z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM422 554
q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM511 609
q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM305 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM359 540
q-39 0 -64 25t-25 64q0 43 32 75.5t76 32.5q39 0 64 -25t25 -64q0 -44 -32 -76t-76 -32zM362 580q26 0 45.5 19t19.5 45q0 24 -14 38.5t-38 14.5q-26 0 -45.5 -19t-19.5 -45q0 -23 14.5 -38t37.5 -15z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="938" 
d="M303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM472 118l-26 -118h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-18 -82
q30 39 76 66.5t99 27.5q90 0 138.5 -56t48.5 -151q0 -40 -9 -71h-390q-1 -4 -1 -23q0 -60 42.5 -101t120.5 -41q77 0 135 47l26 -54q-76 -57 -158 -57q-77 0 -125.5 35.5t-65.5 94.5zM505 272h320q2 8 2 19q0 63 -36.5 102.5t-105.5 39.5q-65 0 -116.5 -47.5t-63.5 -113.5z
" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="495" 
d="M207 -191q-68 0 -108 38l27 36q33 -35 83 -35q29 0 45 13t16 34q0 30 -37 30q-20 0 -35 -17l-29 19l39 66q-77 15 -122.5 70t-45.5 141q0 120 79 205.5t193 85.5q127 0 181 -90l-57 -43q-37 66 -122 66q-88 0 -142 -66t-54 -157q0 -72 42.5 -111t108.5 -39q72 0 122 57
l43 -50q-72 -74 -172 -74h-12l-27 -43q15 11 34 11q26 0 42 -15.5t16 -43.5q0 -39 -30 -63.5t-78 -24.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="563" 
d="M276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5
t-63 -113.5zM411 556h-57l-137 144h78z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="563" 
d="M555 700l-201 -144h-59l180 144h80zM276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102
t-108.5 40q-65 0 -116 -47.5t-63 -113.5z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="563" 
d="M485 556h-49l-60 106l-103 -106h-53l127 144h72zM276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19
q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5t-63 -113.5z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="563" 
d="M514 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM308 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207
t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5t-63 -113.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="225" 
d="M96 0h-75l107 483h75zM239 556h-57l-137 144h78z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="225" 
d="M383 700l-201 -144h-59l180 144h80zM96 0h-75l107 483h75z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="225" 
d="M314 556h-49l-60 106l-103 -106h-53l127 144h72zM96 0h-75l107 483h75z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="225" 
d="M342 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM136 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM96 0h-75l107 483h75z" />
    <glyph glyph-name="eth" unicode="&#xf0;" 
d="M178 511l-11 40l134 40q-33 28 -65 53l53 57q54 -40 97 -85l114 34l11 -40l-94 -28q115 -137 115 -280q0 -132 -75.5 -223t-192.5 -91q-100 0 -162.5 58t-62.5 148q0 119 73.5 202t176.5 83q53 0 98.5 -26.5t68.5 -80.5q-31 96 -120 186zM264 55q76 0 130 63t54 152
q0 57 -38 99.5t-110 42.5q-75 0 -128.5 -61t-53.5 -150q0 -65 39 -105.5t107 -40.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="551" 
d="M422 0h-75l70 317q6 27 6 33q0 40 -26.5 59t-68.5 19q-82 0 -154 -76l-78 -352h-75l107 483h75l-16 -70q86 82 171 82q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40zM415 554q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134
q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" 
d="M261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5zM413 556h-57
l-137 144h78z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" 
d="M555 700l-201 -144h-59l180 144h80zM261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5
q0 -69 38 -109.5t106 -40.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" 
d="M487 556h-49l-60 106l-103 -106h-53l127 144h72zM261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5
t-54 -155.5q0 -69 38 -109.5t106 -40.5z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" 
d="M261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5zM427 554
q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" 
d="M516 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM310 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM261 -12q-102 0 -161.5 58.5t-59.5 155.5
q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="511" 
d="M497 311h-453l11 52h453zM229 99q-17 0 -29.5 12.5t-12.5 29.5q0 20 15 34.5t35 14.5q18 0 30 -12.5t12 -29.5q0 -21 -14.5 -35t-35.5 -14zM313 480q-17 0 -29.5 12.5t-12.5 29.5q0 20 15 35t36 15q17 0 29 -12.5t12 -29.5q0 -21 -14.5 -35.5t-35.5 -14.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" 
d="M66 0h-58l71 70q-39 52 -39 132q0 117 77.5 205t191.5 88q92 0 150 -49l38 37h58l-68 -67q43 -55 43 -136q0 -117 -77.5 -204.5t-191.5 -87.5q-95 0 -155 52zM263 55q80 0 134 67.5t54 155.5q0 48 -20 83l-271 -269q37 -37 103 -37zM119 205q0 -45 17 -79l270 267
q-38 35 -99 35q-80 0 -134 -67.5t-54 -155.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="551" 
d="M128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78q73 0 149 76l77 352h75l-107 -483h-75l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40zM402 556h-57l-137 144h78z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="551" 
d="M545 700l-201 -144h-59l180 144h80zM128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78q73 0 149 76l77 352h75l-107 -483h-75l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="551" 
d="M477 556h-49l-60 106l-103 -106h-53l127 144h72zM128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78q73 0 149 76l77 352h75l-107 -483h-75l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="551" 
d="M506 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM300 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78
q73 0 149 76l77 352h75l-107 -483h-75l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="490" 
d="M-46 -185l26 67q18 -8 47 -8q40 0 72 48l49 75l-92 486h79l72 -396l249 396h84l-372 -581q-63 -98 -151 -98q-36 0 -63 11zM512 700l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" 
d="M281 -12q-56 0 -99.5 23.5t-68.5 64.5l-58 -260h-75l188 851h75l-56 -253q66 81 160 81q84 0 135.5 -53t51.5 -146q0 -123 -70.5 -215.5t-182.5 -92.5zM271 55q80 0 132 68t52 159q0 67 -37 106.5t-98 39.5q-44 0 -83 -23.5t-65 -59.5l-47 -212q19 -35 57.5 -56.5
t88.5 -21.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="490" 
d="M-46 -185l26 67q18 -8 47 -8q40 0 72 48l49 75l-92 486h79l72 -396l249 396h84l-372 -581q-63 -98 -151 -98q-36 0 -63 11zM473 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM267 609q0 -20 -16 -36t-36 -16
q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM619 739h-362l10 48h362z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM536 572
h-362l10 48h362z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM619 793q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM536 626
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="658" 
d="M591 -106l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 68 70 114h-16l-26 148h-333l-91 -148h-99l415 667h103l122 -667h-1q-101 -45 -101 -107q0 -40 43 -40q34 0 55 41zM470 222l-63 359l-220 -359h283z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="575" 
d="M443 -106l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 68 70 114h-1l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483q-101 -45 -101 -107q0 -40 43 -40q34 0 55 41zM303 428q-80 0 -132 -68
t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="676" 
d="M691 872l-201 -144h-59l180 144h80zM368 -12q-134 0 -221 80.5t-87 211.5q0 88 31.5 163.5t83.5 126.5t120 79.5t140 28.5q97 0 167 -42t105 -115l-79 -31q-58 114 -198 114q-112 0 -197 -90t-85 -230q0 -99 61 -160.5t164 -61.5q98 0 179 76l61 -46q-97 -104 -245 -104z
" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="495" 
d="M560 700l-201 -144h-59l180 144h80zM262 -12q-99 0 -160.5 58t-61.5 158q0 120 79 205.5t193 85.5q127 0 181 -90l-57 -43q-37 66 -122 66q-88 0 -142 -66t-54 -157q0 -72 42.5 -111t108.5 -39q72 0 122 57l43 -50q-72 -74 -172 -74z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="676" 
d="M621 723h-49l-60 106l-103 -106h-53l127 144h72zM368 -12q-134 0 -221 80.5t-87 211.5q0 88 31.5 163.5t83.5 126.5t120 79.5t140 28.5q97 0 167 -42t105 -115l-79 -31q-58 114 -198 114q-112 0 -197 -90t-85 -230q0 -99 61 -160.5t164 -61.5q98 0 179 76l61 -46
q-97 -104 -245 -104z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="495" 
d="M491 556h-49l-60 106l-103 -106h-53l127 144h72zM262 -12q-99 0 -160.5 58t-61.5 158q0 120 79 205.5t193 85.5q127 0 181 -90l-57 -43q-37 66 -122 66q-88 0 -142 -66t-54 -157q0 -72 42.5 -111t108.5 -39q72 0 122 57l43 -50q-72 -74 -172 -74z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="676" 
d="M368 -12q-134 0 -221 80.5t-87 211.5q0 88 31.5 163.5t83.5 126.5t120 79.5t140 28.5q97 0 167 -42t105 -115l-79 -31q-58 114 -198 114q-112 0 -197 -90t-85 -230q0 -99 61 -160.5t164 -61.5q98 0 179 76l61 -46q-97 -104 -245 -104zM546 774q0 -21 -16 -36.5t-37 -15.5
q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="495" 
d="M262 -12q-99 0 -160.5 58t-61.5 158q0 120 79 205.5t193 85.5q127 0 181 -90l-57 -43q-37 66 -122 66q-88 0 -142 -66t-54 -157q0 -72 42.5 -111t108.5 -39q72 0 122 57l43 -50q-72 -74 -172 -74zM417 607q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5
t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="676" 
d="M527 723h-72l-66 144h49l59 -106l104 106h53zM368 -12q-134 0 -221 80.5t-87 211.5q0 88 31.5 163.5t83.5 126.5t120 79.5t140 28.5q97 0 167 -42t105 -115l-79 -31q-58 114 -198 114q-112 0 -197 -90t-85 -230q0 -99 61 -160.5t164 -61.5q98 0 179 76l61 -46
q-97 -104 -245 -104z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="495" 
d="M397 556h-72l-66 144h49l59 -106l104 106h53zM262 -12q-99 0 -160.5 58t-61.5 158q0 120 79 205.5t193 85.5q127 0 181 -90l-57 -43q-37 66 -122 66q-88 0 -142 -66t-54 -157q0 -72 42.5 -111t108.5 -39q72 0 122 57l43 -50q-72 -74 -172 -74z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="700" 
d="M498 723h-72l-66 144h49l59 -106l104 106h53zM251 0h-227l147 667h223q114 0 195.5 -81t81.5 -204q0 -70 -25.5 -136.5t-75 -122t-132.5 -89.5t-187 -34zM261 74h4q143 0 231 88.5t88 215.5q0 92 -59 153.5t-147 61.5h-140l-116 -519h139z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="644" 
d="M729 630q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM293 495q55 0 99.5 -23.5t68.5 -64.5l58 260h75l-148 -667h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216
t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="728" 
d="M279 0h-227l66 299h-96l15 69h96l66 299h223q114 0 195.5 -81t81.5 -204q0 -70 -25.5 -136.5t-75 -122t-132.5 -89.5t-187 -34zM370 299h-170l-50 -225h143q143 0 231 88.5t88 215.5q0 92 -59 153.5t-147 61.5h-140l-50 -225h169z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="579" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l28 128h-168l8 48h171l19 84h75l-19 -84h53l-8 -48h-56l-118 -535h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5
l48 213q-19 34 -57.5 56t-88.5 22z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="569" 
d="M461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM586 739h-362l10 48h362z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="563" 
d="M276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5
t-63 -113.5zM539 572h-362l10 48h362z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="569" 
d="M461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM587 793q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="563" 
d="M276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5
t-63 -113.5zM539 626q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="569" 
d="M461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM461 776q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="563" 
d="M276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5
t-63 -113.5zM412 607q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="569" 
d="M461 0q-101 -45 -101 -107q0 -40 43 -40q35 0 55 41l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 68 70 114h-363l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="563" 
d="M353 -106l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 59 54 102q-105 3 -165.5 61t-60.5 153q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-36 -29 -89 -45
q-101 -45 -101 -107q0 -40 43 -40q34 0 55 41zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5t-63 -113.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="569" 
d="M439 723h-72l-66 144h49l59 -106l104 106h53zM461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="563" 
d="M392 556h-72l-66 144h49l59 -106l104 106h53zM276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19
q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5t-63 -113.5z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="713" 
d="M625 723h-49l-60 106l-103 -106h-53l127 144h72zM368 -12q-137 0 -222.5 81t-85.5 211q0 171 111 284.5t270 113.5q91 0 158.5 -40t104.5 -106l-75 -35q-26 51 -79.5 79t-119.5 28q-113 0 -197.5 -92t-84.5 -228q0 -100 61.5 -161t163.5 -61q96 0 175 71l31 136h-230
l16 74h313l-54 -245q-108 -110 -256 -110z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="574" 
d="M476 549h-49l-60 106l-103 -106h-53l127 144h72zM212 -196q-148 0 -216 82l48 51q49 -71 167 -71q129 0 162 148l14 63q-65 -82 -164 -82q-81 0 -132.5 51t-51.5 149q0 120 70 210t182 90q48 0 95 -24.5t74 -63.5l17 76h75l-104 -469q-46 -210 -236 -210zM253 62
q40 0 80 22t68 55l46 209q-20 37 -61 58.5t-89 21.5q-79 0 -129 -66.5t-50 -154.5q0 -67 36.5 -106t98.5 -39z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="713" 
d="M368 -12q-137 0 -222.5 81t-85.5 211q0 171 111 284.5t270 113.5q91 0 158.5 -40t104.5 -106l-75 -35q-26 51 -79.5 79t-119.5 28q-113 0 -197.5 -92t-84.5 -228q0 -100 61.5 -161t163.5 -61q96 0 175 71l31 136h-230l16 74h313l-54 -245q-108 -110 -256 -110zM678 793
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="574" 
d="M212 -196q-148 0 -216 82l48 51q49 -71 167 -71q129 0 162 148l14 63q-65 -82 -164 -82q-81 0 -132.5 51t-51.5 149q0 120 70 210t182 90q48 0 95 -24.5t74 -63.5l17 76h75l-104 -469q-46 -210 -236 -210zM253 62q40 0 80 22t68 55l46 209q-20 37 -61 58.5t-89 21.5
q-79 0 -129 -66.5t-50 -154.5q0 -67 36.5 -106t98.5 -39zM530 626q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="713" 
d="M368 -12q-137 0 -222.5 81t-85.5 211q0 171 111 284.5t270 113.5q91 0 158.5 -40t104.5 -106l-75 -35q-26 51 -79.5 79t-119.5 28q-113 0 -197.5 -92t-84.5 -228q0 -100 61.5 -161t163.5 -61q96 0 175 71l31 136h-230l16 74h313l-54 -245q-108 -110 -256 -110zM550 774
q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="574" 
d="M212 -196q-148 0 -216 82l48 51q49 -71 167 -71q129 0 162 148l14 63q-65 -82 -164 -82q-81 0 -132.5 51t-51.5 149q0 120 70 210t182 90q48 0 95 -24.5t74 -63.5l17 76h75l-104 -469q-46 -210 -236 -210zM253 62q40 0 80 22t68 55l46 209q-20 37 -61 58.5t-89 21.5
q-79 0 -129 -66.5t-50 -154.5q0 -67 36.5 -106t98.5 -39zM403 607q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="713" 
d="M354 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM368 -12q-137 0 -222.5 81t-85.5 211q0 171 111 284.5t270 113.5q91 0 158.5 -40t104.5 -106l-75 -35q-26 51 -79.5 79
t-119.5 28q-113 0 -197.5 -92t-84.5 -228q0 -100 61.5 -161t163.5 -61q96 0 175 71l31 136h-230l16 74h313l-54 -245q-108 -110 -256 -110z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="574" 
d="M312 605q0 41 30.5 82.5t72.5 63.5l26 -28q-26 -13 -47.5 -34.5t-28.5 -43.5h7q15 0 26 -11t11 -28q0 -21 -15 -36t-36 -15q-20 0 -33 13t-13 37zM212 -196q-148 0 -216 82l48 51q49 -71 167 -71q129 0 162 148l14 63q-65 -82 -164 -82q-81 0 -132.5 51t-51.5 149
q0 120 70 210t182 90q48 0 95 -24.5t74 -63.5l17 76h75l-104 -469q-46 -210 -236 -210zM253 62q40 0 80 22t68 55l46 209q-20 37 -61 58.5t-89 21.5q-79 0 -129 -66.5t-50 -154.5q0 -67 36.5 -106t98.5 -39z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="712" 
d="M594 723h-49l-60 106l-103 -106h-53l127 144h72zM580 0h-83l67 306h-390l-67 -306h-83l147 667h83l-63 -287h390l63 287h83z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="552" 
d="M517 723h-49l-60 106l-103 -106h-53l127 144h72zM423 0h-75l69 317q6 27 6 33q0 78 -100 78q-76 0 -149 -77l-78 -351h-75l147 667h75l-56 -254q86 82 171 82q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="731" 
d="M590 0h-83l67 306h-390l-67 -306h-83l110 501h-77l11 48h77l26 118h83l-26 -118h390l26 118h83l-26 -118h75l-10 -48h-76zM201 380h390l26 121h-390z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="553" 
d="M424 0h-75l69 317q6 27 6 33q0 78 -100 78q-76 0 -149 -77l-78 -351h-75l118 535h-67l11 48h66l19 84h75l-19 -84h158l-11 -48h-157l-27 -122q86 82 171 82q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="239" 
d="M107 0h-83l147 667h83zM296 721q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="225" 
d="M96 0h-75l107 483h75zM253 554q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="239" 
d="M107 0h-83l147 667h83zM410 739h-362l10 48h362z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="225" 
d="M96 0h-75l107 483h75zM367 572h-362l10 48h362z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="239" 
d="M107 0h-83l147 667h83zM410 793q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="225" 
d="M96 0h-75l107 483h75zM368 626q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="239" 
d="M104 -106l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 67 70 114h-9l147 667h83l-147 -667q-101 -45 -101 -107q0 -40 43 -40q35 0 55 41z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="225" 
d="M93 -106l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 67 70 114h-1l107 483h75l-107 -483q-101 -45 -101 -107q0 -40 43 -40q34 0 55 41zM186 552q-20 0 -33 12.5t-13 31.5q0 25 18 41t39 16q19 0 32.5 -13t13.5 -31q0 -25 -18 -41t-39 -16z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="239" 
d="M107 0h-83l147 667h83zM283 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="225" 
d="M96 0h-75l107 483h75z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="713" 
d="M107 0h-83l147 667h83zM397 -12q-58 0 -106 21.5t-74 62.5l50 60q39 -70 126 -70q60 0 98 37.5t52 102.5l103 465h83l-103 -466q-47 -213 -229 -213z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="451" 
d="M186 552q-20 0 -33 12.5t-13 31.5q0 25 18 41t39 16q19 0 32.5 -13t13.5 -31q0 -25 -18 -41t-39 -16zM96 0h-75l107 483h75zM157 -196q-79 0 -115 42l36 54q26 -34 69 -34q68 0 88 87l118 530h75l-118 -530q-33 -149 -153 -149zM411 552q-20 0 -33 12.5t-13 31.5
q0 25 18 41t39 16q19 0 32.5 -13t13.5 -31q0 -25 -18 -41t-39 -16z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="475" 
d="M596 723h-49l-60 106l-103 -106h-53l127 144h72zM158 -12q-58 0 -106 21.5t-74 62.5l50 60q39 -70 126 -70q60 0 98 37.5t52 102.5l103 465h83l-103 -466q-47 -213 -229 -213z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="225" 
d="M316 556h-49l-60 106l-103 -106h-53l127 144h72zM-68 -196q-79 0 -115 42l36 54q25 -34 69 -34q68 0 88 87l118 530h75l-118 -530q-33 -149 -153 -149z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="601" 
d="M279 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM530 0h-97l-190 304l-87 -80l-49 -224h-83l147 667h83l-75 -337l365 337h109l-348 -317z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="514" 
d="M236 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM446 0h-91l-128 220l-101 -82l-30 -138h-75l147 667h75l-96 -437l306 253h99l-263 -219z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="514" 
d="M446 0h-91l-128 220l-101 -82l-30 -138h-75l107 483h75l-56 -253l306 253h99l-263 -219z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="517" 
d="M580 867l-201 -144h-59l180 144h80zM417 0h-393l147 667h83l-132 -593h311z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="225" 
d="M421 867l-201 -144h-59l180 144h80zM96 0h-75l147 667h75z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="517" 
d="M235 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM417 0h-393l147 667h83l-132 -593h311z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="225" 
d="M80 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM96 0h-75l147 667h75z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="517" 
d="M435 630q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM417 0h-393l147 667h83l-132 -593h311z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="291" 
d="M393 630q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM96 0h-75l147 667h75z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="517" 
d="M417 0h-393l147 667h83l-132 -593h311zM386 287q-21 0 -35.5 14.5t-14.5 35.5q0 25 17.5 42.5t42.5 17.5q21 0 35.5 -15t14.5 -36q0 -25 -17.5 -42t-42.5 -17z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="307" 
d="M96 0h-75l147 667h75zM261 190q-21 0 -35.5 14.5t-14.5 35.5q0 25 17.5 42.5t42.5 17.5q21 0 35.5 -15t14.5 -36q0 -25 -17.5 -42t-42.5 -17z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="547" 
d="M16 221l16 72l97 50l72 324h83l-62 -276l110 56l-15 -72l-111 -57l-54 -244h311l-16 -74h-393l59 271z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="287" 
d="M6 228l14 60l107 54l72 325h75l-62 -282l108 55l-14 -60l-107 -55l-72 -325h-75l62 283z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="708" 
d="M661 867l-201 -144h-59l180 144h80zM576 0h-80l-269 544l-120 -544h-83l147 667h85l266 -534l118 534h83z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="551" 
d="M544 700l-201 -144h-59l180 144h80zM422 0h-75l70 317q6 27 6 33q0 40 -26.5 59t-68.5 19q-82 0 -154 -76l-78 -352h-75l107 483h75l-16 -70q86 82 171 82q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="708" 
d="M320 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM576 0h-80l-269 544l-120 -544h-83l147 667h85l266 -534l118 534h83z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="551" 
d="M242 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM422 0h-75l70 317q6 27 6 33q0 40 -26.5 59t-68.5 19q-82 0 -154 -76l-78 -352h-75l107 483h75l-16 -70q86 82 171 82
q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="708" 
d="M498 723h-72l-66 144h49l59 -106l104 106h53zM576 0h-80l-269 544l-120 -544h-83l147 667h85l266 -534l118 534h83z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="551" 
d="M382 556h-72l-66 144h49l59 -106l104 106h53zM422 0h-75l70 317q6 27 6 33q0 40 -26.5 59t-68.5 19q-82 0 -154 -76l-78 -352h-75l107 483h75l-16 -70q86 82 171 82q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="551" 
d="M256 693q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM422 0h-75l70 317q6 27 6 33q0 40 -26.5 59t-68.5 19q-82 0 -154 -76l-78 -352h-75l107 483h75l-16 -70q86 82 171 82
q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="708" 
d="M350 -196q-125 0 -180 84l49 54q38 -70 126 -70q113 0 149 132l-267 540l-120 -544h-83l147 667h85l266 -534l118 534h83l-144 -654q-46 -209 -229 -209z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="551" 
d="M496 335l-84 -382q-34 -149 -153 -149q-80 0 -116 42l38 58q26 -33 68 -33q69 0 88 82l80 364q6 26 6 34q0 39 -27 58t-68 19q-82 0 -154 -76l-78 -352h-75l107 483h75l-16 -70q86 82 171 82q63 0 103.5 -30.5t40.5 -89.5q0 -12 -6 -40z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="765" 
d="M674 739h-362l10 48h362zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5
q0 -104 64 -163t161 -59z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" 
d="M540 572h-362l10 48h362zM261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5
t106 -40.5z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z
M673 793q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" 
d="M261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5zM540 626
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="765" 
d="M573 867l-147 -144h-50l126 144h71zM715 867l-147 -144h-50l126 144h71zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5
t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" 
d="M440 700l-147 -144h-50l126 144h71zM582 700l-147 -144h-50l126 144h71zM261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5
q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1105" 
d="M997 0h-438l22 102q-86 -114 -240 -114q-124 0 -202.5 82t-78.5 210q0 173 110.5 285t265.5 112q183 0 239 -155l31 145h438l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM604 201l49 222q-15 86 -72.5 133t-144.5 47q-119 0 -203.5 -92t-84.5 -227
q0 -101 60.5 -161.5t157.5 -60.5q71 0 135 37t103 102z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="962" 
d="M912 217h-389q-2 -8 -2 -23q0 -60 43 -102t120 -42q79 0 136 47l26 -52q-70 -57 -167 -57q-85 0 -140.5 44t-66.5 94q-18 -28 -32.5 -45.5t-41 -42t-62 -37.5t-77.5 -13q-99 0 -159 57.5t-60 156.5q0 116 76.5 204.5t190.5 88.5q81 0 131.5 -45t59.5 -93q93 138 216 138
q91 0 149 -56.5t58 -154.5q0 -29 -9 -67zM531 272h320q1 5 1 19q0 62 -37.5 102t-106.5 40q-68 0 -118.5 -51t-58.5 -110zM447 278q0 75 -40.5 112.5t-101.5 37.5q-77 0 -131.5 -66t-54.5 -157q0 -69 37.5 -109.5t104.5 -40.5q78 0 132 66t54 157z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="608" 
d="M616 867l-201 -144h-59l180 144h80zM508 0h-92l-112 265h-140l-57 -265h-83l147 667h245q76 0 132.5 -48t56.5 -126q0 -93 -60 -155t-157 -69zM354 338h3q75 0 117.5 42.5t42.5 105.5q0 46 -34.5 76.5t-78.5 30.5h-166l-57 -255h173z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="330" 
d="M464 700l-201 -144h-59l180 144h80zM96 0h-75l107 483h75l-17 -74q38 43 79 63.5t101 20.5l-17 -77q-12 4 -38 4q-40 0 -77.5 -23t-62.5 -56z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="608" 
d="M276 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM508 0h-92l-112 265h-140l-57 -265h-83l147 667h245q76 0 132.5 -48t56.5 -126q0 -93 -60 -155t-157 -69zM354 338h3
q75 0 117.5 42.5t42.5 105.5q0 46 -34.5 76.5t-78.5 30.5h-166l-57 -255h173z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="330" 
d="M162 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM96 0h-75l107 483h75l-17 -74q38 43 79 63.5t101 20.5l-17 -77q-12 4 -38 4q-40 0 -77.5 -23t-62.5 -56z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="608" 
d="M453 723h-72l-66 144h49l59 -106l104 106h53zM508 0h-92l-112 265h-140l-57 -265h-83l147 667h245q76 0 132.5 -48t56.5 -126q0 -93 -60 -155t-157 -69zM354 338h3q75 0 117.5 42.5t42.5 105.5q0 46 -34.5 76.5t-78.5 30.5h-166l-57 -255h173z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="330" 
d="M301 556h-72l-66 144h49l59 -106l104 106h53zM96 0h-75l107 483h75l-17 -74q38 43 79 63.5t101 20.5l-17 -77q-12 4 -38 4q-40 0 -77.5 -23t-62.5 -56z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="586" 
d="M603 867l-201 -144h-59l180 144h80zM286 -12q-88 0 -161.5 33.5t-108.5 85.5l57 60q35 -49 93 -77t127 -28q72 0 109.5 38t37.5 86q0 36 -34 62.5t-83 45t-97.5 40t-82.5 59t-34 90.5q0 79 67.5 136.5t168.5 57.5q77 0 141.5 -28t99.5 -77l-57 -58q-32 44 -84 67t-109 23
t-97.5 -33t-40.5 -77q0 -33 34 -57.5t82.5 -43.5t97.5 -41t83 -61t34 -92q0 -85 -65.5 -148t-177.5 -63z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="465" 
d="M500 700l-201 -144h-59l180 144h80zM207 -12q-136 0 -204 85l48 52q21 -31 65 -53.5t95 -22.5q50 0 80.5 25t30.5 60q0 25 -26 44t-63.5 32.5t-75 28.5t-63.5 42.5t-26 64.5q0 63 49 106t134 43q60 0 109.5 -22.5t74.5 -56.5l-43 -49q-17 27 -58.5 47t-86.5 20
q-47 0 -75.5 -21t-28.5 -53q0 -24 26 -41.5t63.5 -31t75 -29t63.5 -44t26 -67.5q0 -66 -51.5 -112.5t-138.5 -46.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="586" 
d="M534 723h-49l-60 106l-103 -106h-53l127 144h72zM286 -12q-88 0 -161.5 33.5t-108.5 85.5l57 60q35 -49 93 -77t127 -28q72 0 109.5 38t37.5 86q0 36 -34 62.5t-83 45t-97.5 40t-82.5 59t-34 90.5q0 79 67.5 136.5t168.5 57.5q77 0 141.5 -28t99.5 -77l-57 -58
q-32 44 -84 67t-109 23t-97.5 -33t-40.5 -77q0 -33 34 -57.5t82.5 -43.5t97.5 -41t83 -61t34 -92q0 -85 -65.5 -148t-177.5 -63z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="465" 
d="M433 556h-49l-60 106l-103 -106h-53l127 144h72zM207 -12q-136 0 -204 85l48 52q21 -31 65 -53.5t95 -22.5q50 0 80.5 25t30.5 60q0 25 -26 44t-63.5 32.5t-75 28.5t-63.5 42.5t-26 64.5q0 63 49 106t134 43q60 0 109.5 -22.5t74.5 -56.5l-43 -49q-17 27 -58.5 47
t-86.5 20q-47 0 -75.5 -21t-28.5 -53q0 -24 26 -41.5t63.5 -31t75 -29t63.5 -44t26 -67.5q0 -66 -51.5 -112.5t-138.5 -46.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="586" 
d="M206 -191q-68 0 -108 38l27 36q33 -35 83 -35q29 0 45 13t16 34q0 30 -37 30q-20 0 -35 -17l-29 19l40 69q-64 12 -115 41.5t-77 69.5l57 60q35 -49 93 -77t127 -28q72 0 109.5 38t37.5 86q0 36 -34 62.5t-83 45t-97.5 40t-82.5 59t-34 90.5q0 79 67.5 136.5t168.5 57.5
q77 0 141.5 -28t99.5 -77l-57 -58q-32 44 -84 67t-109 23t-97.5 -33t-40.5 -77q0 -33 34 -57.5t82.5 -43.5t97.5 -41t83 -61t34 -92q0 -85 -65.5 -148t-177.5 -63q-13 0 -37 2l-27 -45q15 11 34 11q26 0 42 -15.5t16 -43.5q0 -39 -30 -63.5t-78 -24.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="465" 
d="M145 -191q-68 0 -108 38l27 36q33 -35 83 -35q29 0 45 13t16 34q0 30 -37 30q-20 0 -35 -17l-29 19l39 66q-94 17 -143 80l48 52q21 -31 65 -53.5t95 -22.5q50 0 80.5 25t30.5 60q0 25 -26 44t-63.5 32.5t-75 28.5t-63.5 42.5t-26 64.5q0 63 49 106t134 43
q60 0 109.5 -22.5t74.5 -56.5l-43 -49q-17 27 -58.5 47t-86.5 20q-47 0 -75.5 -21t-28.5 -53q0 -24 26 -41.5t63.5 -31t75 -29t63.5 -44t26 -67.5q0 -66 -51.5 -112.5t-138.5 -46.5q-13 0 -19 1l-27 -44q15 11 34 11q26 0 42 -15.5t16 -43.5q0 -39 -30 -63.5t-78 -24.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="586" 
d="M440 723h-72l-66 144h49l59 -106l104 106h53zM286 -12q-88 0 -161.5 33.5t-108.5 85.5l57 60q35 -49 93 -77t127 -28q72 0 109.5 38t37.5 86q0 36 -34 62.5t-83 45t-97.5 40t-82.5 59t-34 90.5q0 79 67.5 136.5t168.5 57.5q77 0 141.5 -28t99.5 -77l-57 -58
q-32 44 -84 67t-109 23t-97.5 -33t-40.5 -77q0 -33 34 -57.5t82.5 -43.5t97.5 -41t83 -61t34 -92q0 -85 -65.5 -148t-177.5 -63z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="465" 
d="M338 556h-72l-66 144h49l59 -106l104 106h53zM207 -12q-136 0 -204 85l48 52q21 -31 65 -53.5t95 -22.5q50 0 80.5 25t30.5 60q0 25 -26 44t-63.5 32.5t-75 28.5t-63.5 42.5t-26 64.5q0 63 49 106t134 43q60 0 109.5 -22.5t74.5 -56.5l-43 -49q-17 27 -58.5 47t-86.5 20
q-47 0 -75.5 -21t-28.5 -53q0 -24 26 -41.5t63.5 -31t75 -29t63.5 -44t26 -67.5q0 -66 -51.5 -112.5t-138.5 -46.5z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="570" 
d="M252 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM272 0h-83l131 593h-211l16 74h506l-16 -74h-212z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="294" 
d="M106 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM166 -12q-52 0 -81 22t-29 66q0 13 3 28l70 314h-80l15 65h80l29 132h75l-29 -132h98l-15 -65h-98l-66 -298q-2 -12 -2 -20
q0 -45 48 -45q28 0 46 16l9 -60q-30 -23 -73 -23z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="570" 
d="M421 723h-72l-66 144h49l59 -106l104 106h53zM272 0h-83l131 593h-211l16 74h506l-16 -74h-212z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="313" 
d="M420 693q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM166 -12q-52 0 -81 22t-29 66q0 13 3 28l70 314h-80l15 65h80l29 132h75l-29 -132h98l-15 -65h-98l-66 -298q-2 -12 -2 -20
q0 -45 48 -45q28 0 46 16l9 -60q-30 -23 -73 -23z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="570" 
d="M272 0h-83l65 294h-150l10 48h150l56 251h-211l16 74h506l-16 -74h-212l-56 -251h151l-11 -48h-150z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="294" 
d="M166 -12q-52 0 -81 22t-29 66q0 13 3 28l27 123h-79l10 48h80l32 143h-80l15 65h80l29 132h75l-29 -132h98l-15 -65h-98l-32 -143h70l-11 -48h-70l-23 -107q-2 -12 -2 -20q0 -45 48 -45q28 0 46 16l9 -60q-30 -23 -73 -23z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="700" 
d="M336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-30 -133 -97 -202.5t-193 -69.5zM527 721q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5
q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="551" 
d="M128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78q73 0 149 76l77 352h75l-107 -483h-75l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40zM415 554q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5
t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="700" 
d="M336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-30 -133 -97 -202.5t-193 -69.5zM640 739h-362l10 48h362z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="551" 
d="M128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78q73 0 149 76l77 352h75l-107 -483h-75l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40zM530 572h-362l10 48h362z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="700" 
d="M336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-30 -133 -97 -202.5t-193 -69.5zM641 793q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32
q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="551" 
d="M128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78q73 0 149 76l77 352h75l-107 -483h-75l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40zM529 626q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="700" 
d="M460 690q-39 0 -64 25t-25 64q0 43 32.5 75.5t75.5 32.5q39 0 64 -25t25 -64q0 -44 -32 -76t-76 -32zM463 730q26 0 45.5 19t19.5 45q0 24 -14 38.5t-38 14.5q-26 0 -45.5 -19t-19.5 -45q0 -23 14.5 -38t37.5 -15zM336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49l90 408
h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-30 -133 -97 -202.5t-193 -69.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="551" 
d="M353 543q-39 0 -64 25t-25 64q0 44 32 76t76 32q39 0 64 -25t25 -64q0 -44 -32 -76t-76 -32zM356 583q26 0 45.5 19t19.5 45q0 24 -14 38.5t-38 14.5q-26 0 -45.5 -19t-19.5 -45q0 -23 14.5 -38t37.5 -15zM128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78q73 0 149 76
l77 352h75l-107 -483h-75l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="700" 
d="M336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-30 -133 -97 -202.5t-193 -69.5zM542 867l-147 -144h-50l126 144h71zM684 867l-147 -144h-50l126 144
h71z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="551" 
d="M128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78q73 0 149 76l77 352h75l-107 -483h-75l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40zM430 700l-147 -144h-50l126 144h71zM572 700l-147 -144h-50l126 144h71z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="700" 
d="M336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-24 -107 -71 -173t-129 -88q-99 -45 -99 -106q0 -40 43 -40q34 0 55 41l31 -24q-34 -56 -91 -56
q-35 0 -58 19t-23 53q0 59 54 102h-2z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="551" 
d="M128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78q73 0 149 76l77 352h75l-107 -483q-101 -45 -101 -107q0 -40 43 -40q34 0 55 41l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 68 70 114h-1l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="883" 
d="M678 723h-49l-60 106l-103 -106h-53l127 144h72zM624 0h-90l-28 538l-266 -538h-90l-43 667h90l24 -556l276 556h70l29 -556l271 556h94z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="734" 
d="M567 556h-49l-60 106l-103 -106h-53l127 144h72zM514 0h-75l-40 388l-212 -388h-75l-47 483h75l32 -385l212 385h66l41 -385l202 385h82z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="626" 
d="M552 723h-49l-60 106l-103 -106h-53l127 144h72zM301 0h-83l62 282l-178 385h91l139 -310l275 310h103l-347 -385z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="490" 
d="M-46 -185l26 67q18 -8 47 -8q40 0 72 48l49 75l-92 486h79l72 -396l249 396h84l-372 -581q-63 -98 -151 -98q-36 0 -63 11zM445 556h-49l-60 106l-103 -106h-53l127 144h72z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="626" 
d="M580 776q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM374 776q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM301 0h-83l62 282l-178 385h91l139 -310l275 310
h103l-347 -385z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="585" 
d="M595 867l-201 -144h-59l180 144h80zM482 0h-488l15 69l485 524h-369l16 74h481l-14 -69l-487 -524h378z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="472" 
d="M502 700l-201 -144h-59l180 144h80zM366 0h-368l12 58l342 359h-262l15 66h364l-13 -57l-344 -361h268z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="585" 
d="M482 0h-488l15 69l485 524h-369l16 74h481l-14 -69l-487 -524h378zM452 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="472" 
d="M366 0h-368l12 58l342 359h-262l15 66h364l-13 -57l-344 -361h268zM360 607q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="585" 
d="M434 723h-72l-66 144h49l59 -106l104 106h53zM482 0h-488l15 69l485 524h-369l16 74h481l-14 -69l-487 -524h378z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="472" 
d="M339 556h-72l-66 144h49l59 -106l104 106h53zM366 0h-368l12 58l342 359h-262l15 66h364l-13 -57l-344 -361h268z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="283" 
d="M117 0h-75l92 418h-80l15 65h80l8 37q35 157 161 157q64 0 105 -41l-40 -45q-24 24 -62 24q-68 0 -89 -95z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="431" 
d="M55 -161h-84l92 415h-68v68h82l46 204q32 151 158 151q69 0 109 -41l-36 -58q-19 25 -58 25q-71 0 -92 -92l-42 -189h139v-68h-154z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q160 0 247 -107q50 29 65 72h-7q-14 0 -24 10.5t-10 25.5q0 19 14.5 33.5t33.5 14.5q18 0 30 -13t12 -35q0 -38 -27.5 -76.5t-66.5 -60.5q41 -68 41 -155q0 -168 -109.5 -283.5t-265.5 -115.5z
M373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" 
d="M575 546q0 -36 -24.5 -72.5t-61.5 -59.5q41 -55 41 -134q0 -117 -77.5 -204.5t-191.5 -87.5q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q96 0 156 -54q47 29 60 68h-6q-14 0 -24 10t-10 26q0 20 14 34t33 14t31 -12.5t12 -34.5zM263 55q80 0 134 67.5
t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="700" 
d="M843 744q0 -50 -41.5 -94.5t-105.5 -68.5l-70 -321q-30 -133 -97 -202.5t-193 -69.5q-123 0 -191.5 60t-68.5 162q0 24 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-10 -44q74 35 88 84h-6
q-14 0 -24 10t-10 26q0 20 14 34t33 14t31 -12.5t12 -34.5z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" horiz-adv-x="551" 
d="M641 551q0 -46 -36.5 -87.5t-95.5 -68.5l-87 -395h-75l16 69q-85 -81 -170 -81q-66 0 -105.5 31t-39.5 88q0 19 6 41l74 335h75l-71 -317q-4 -18 -4 -33q0 -78 100 -78q76 0 148 76l78 352h75l-10 -45q60 34 72 77h-6q-14 0 -24.5 10.5t-10.5 25.5q0 19 14.5 33t33.5 14
t31 -12.5t12 -34.5z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="947" 
d="M804 867l-201 -144h-59l180 144h80zM839 0h-437l32 148h-259l-124 -148h-98l563 667h470l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM451 222l77 354l-296 -354h219z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="938" 
d="M732 700l-201 -144h-59l180 144h80zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM472 118l-26 -118h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92q55 0 99.5 -23.5
t68.5 -64.5l17 76h75l-18 -82q30 39 76 66.5t99 27.5q90 0 138.5 -56t48.5 -151q0 -40 -9 -71h-390q-1 -4 -1 -23q0 -60 42.5 -101t120.5 -41q77 0 135 47l26 -54q-76 -57 -158 -57q-77 0 -125.5 35.5t-65.5 94.5zM505 272h320q2 8 2 19q0 63 -36.5 102.5t-105.5 39.5
q-65 0 -116.5 -47.5t-63.5 -113.5z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="765" 
d="M689 867l-201 -144h-59l180 144h80zM368 -12q-112 0 -194 57l-41 -45h-66l69 78q-76 79 -76 202q0 167 109.5 282.5t265.5 115.5q109 0 189 -54l39 43h66l-67 -74q81 -81 81 -206q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 87 -50 147
l-377 -422q58 -45 145 -45zM148 284q0 -87 45 -143l376 421q-57 42 -139 42q-117 0 -199.5 -91.5t-82.5 -228.5z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" 
d="M556 700l-201 -144h-59l180 144h80zM66 0h-58l71 70q-39 52 -39 132q0 117 77.5 205t191.5 88q92 0 150 -49l38 37h58l-68 -67q43 -55 43 -136q0 -117 -77.5 -204.5t-191.5 -87.5q-95 0 -155 52zM263 55q80 0 134 67.5t54 155.5q0 48 -20 83l-271 -269q37 -37 103 -37z
M119 205q0 -45 17 -79l270 267q-38 35 -99 35q-80 0 -134 -67.5t-54 -155.5z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="586" 
d="M262 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM286 -12q-88 0 -161.5 33.5t-108.5 85.5l57 60q35 -49 93 -77t127 -28q72 0 109.5 38t37.5 86q0 36 -34 62.5t-83 45t-97.5 40
t-82.5 59t-34 90.5q0 79 67.5 136.5t168.5 57.5q77 0 141.5 -28t99.5 -77l-57 -58q-32 44 -84 67t-109 23t-97.5 -33t-40.5 -77q0 -33 34 -57.5t82.5 -43.5t97.5 -41t83 -61t34 -92q0 -85 -65.5 -148t-177.5 -63z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="465" 
d="M198 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM207 -12q-136 0 -204 85l48 52q21 -31 65 -53.5t95 -22.5q50 0 80.5 25t30.5 60q0 25 -26 44t-63.5 32.5t-75 28.5t-63.5 42.5
t-26 64.5q0 63 49 106t134 43q60 0 109.5 -22.5t74.5 -56.5l-43 -49q-17 27 -58.5 47t-86.5 20q-47 0 -75.5 -21t-28.5 -53q0 -24 26 -41.5t63.5 -31t75 -29t63.5 -44t26 -67.5q0 -66 -51.5 -112.5t-138.5 -46.5z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="570" 
d="M252 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM272 0h-83l131 593h-211l16 74h506l-16 -74h-212z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="294" 
d="M99 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM166 -12q-52 0 -81 22t-29 66q0 13 3 28l70 314h-80l15 65h80l29 132h75l-29 -132h98l-15 -65h-98l-66 -298q-2 -12 -2 -20
q0 -45 48 -45q28 0 46 16l9 -60q-30 -23 -73 -23z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="225" 
d="M-68 -196q-79 0 -115 42l36 54q25 -34 69 -34q68 0 88 87l118 530h75l-118 -530q-33 -149 -153 -149z" />
    <glyph glyph-name="hsuperior" unicode="&#x2b0;" horiz-adv-x="398" 
d="M352 326h-57l44 200q2 12 2 20q0 26 -17.5 40t-47.5 14q-45 0 -95 -50l-49 -224h-58l96 434h58l-36 -165q51 53 112 53q47 0 73 -21t26 -59q0 -11 -4 -31z" />
    <glyph glyph-name="uni02BC" unicode="&#x2bc;" horiz-adv-x="230" 
d="M256 619q0 -46 -34 -92t-82 -71l-30 33q67 34 84 84h-8q-18 0 -30.5 12.5t-12.5 32.5q0 24 18 41.5t42 17.5q23 0 38 -15.5t15 -42.5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="265" 
d="M334 556h-49l-60 106l-103 -106h-53l127 144h72z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="265" 
d="M239 556h-72l-66 144h49l59 -106l104 106h53z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="363" 
d="M439 591h-362l10 48h362z" />
    <glyph glyph-name="uni02CB" unicode="&#x2cb;" horiz-adv-x="226" 
d="M295 556h-57l-137 144h78z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="351" 
d="M434 626q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="111" 
d="M183 607q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="196" 
d="M175 543q-39 0 -64 25t-25 64q0 44 32 76t76 32q39 0 64 -25t25 -64q0 -44 -32 -76t-76 -32zM178 583q26 0 45.5 19t19.5 45q0 24 -14 38.5t-38 14.5q-26 0 -45.5 -19t-19.5 -45q0 -23 14.5 -38t37.5 -15z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="178" 
d="M64 -106l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 86 113 137l31 -23q-101 -45 -101 -107q0 -40 43 -40q35 0 55 41z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="313" 
d="M296 554q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="305" 
d="M264 700l-147 -144h-50l126 144h71zM406 700l-147 -144h-50l126 144h71z" />
    <glyph glyph-name="hookabovecomb" unicode="&#x309;" horiz-adv-x="178" 
d="M212 729l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni037E" unicode="&#x37e;" horiz-adv-x="230" 
d="M128 41q0 -45 -33.5 -91t-81.5 -71l-31 33q67 34 84 84h-8q-18 0 -30.5 12t-12.5 32q0 24 18 42t42 18q23 0 38 -15.5t15 -43.5zM152 380q-21 0 -35.5 15t-14.5 36q0 25 17.5 42t42.5 17q21 0 35.5 -15t14.5 -35q0 -25 -17.5 -42.5t-42.5 -17.5z" />
    <glyph glyph-name="tonos" unicode="&#x384;" horiz-adv-x="551" 
d="M211 565h-38l65 200h76z" />
    <glyph glyph-name="dieresistonos" unicode="&#x385;" horiz-adv-x="551" 
d="M355 565h-38l64 200h76zM539 609q0 -20 -16 -36t-36 -16q-18 0 -29.5 12t-11.5 30q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM271 609q0 -20 -16 -36t-36 -16q-18 0 -29.5 12t-11.5 30q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29z" />
    <glyph glyph-name="Alphatonos" unicode="&#x386;" horiz-adv-x="658" 
d="M202 565h-38l65 200h76zM595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283z" />
    <glyph glyph-name="Epsilontonos" unicode="&#x388;" horiz-adv-x="586" 
d="M98 565h-38l65 200h76zM478 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355z" />
    <glyph glyph-name="Etatonos" unicode="&#x389;" horiz-adv-x="729" 
d="M98 565h-38l65 200h76zM597 0h-83l67 306h-390l-67 -306h-83l147 667h83l-63 -287h390l63 287h83z" />
    <glyph glyph-name="Iotatonos" unicode="&#x38a;" horiz-adv-x="255" 
d="M98 565h-38l65 200h76zM124 0h-83l147 667h83z" />
    <glyph glyph-name="Omicrontonos" unicode="&#x38c;" horiz-adv-x="765" 
d="M133 565h-38l65 200h76zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5
q0 -104 64 -163t161 -59z" />
    <glyph glyph-name="Upsilontonos" unicode="&#x38e;" horiz-adv-x="684" 
d="M98 565h-38l65 200h76zM359 0h-83l62 282l-178 385h91l139 -310l275 310h103l-347 -385z" />
    <glyph glyph-name="Omegatonos" unicode="&#x38f;" horiz-adv-x="769" 
d="M133 565h-38l65 200h76zM35 74h152q-117 85 -117 231q0 71 26.5 138t73 119.5t117 84t152.5 31.5q137 0 222.5 -77.5t85.5 -209.5q0 -113 -59.5 -193.5t-147.5 -123.5h122l-16 -74h-240l16 74q101 21 168 107t67 201q0 101 -60.5 161t-160.5 60q-123 0 -203.5 -94
t-80.5 -225q0 -74 34 -127.5t89 -82.5l-16 -74h-240z" />
    <glyph glyph-name="iotadieresistonos" unicode="&#x390;" horiz-adv-x="238" 
d="M201 565h-38l64 200h76zM385 609q0 -20 -16 -36t-36 -16q-18 0 -29.5 12t-11.5 30q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM117 609q0 -20 -16 -36t-36 -16q-18 0 -29.5 12t-11.5 30q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM124 -12q-106 0 -81 113l86 382
h75l-84 -370q-13 -58 34 -58q10 0 20 2l-9 -65q-22 -4 -41 -4z" />
    <glyph glyph-name="Alpha" unicode="&#x391;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283z" />
    <glyph glyph-name="Beta" unicode="&#x392;" horiz-adv-x="629" 
d="M333 0h-309l147 667h277q74 0 122 -43t48 -110q0 -70 -45.5 -121t-109.5 -58q43 -11 70.5 -50.5t27.5 -86.5q0 -81 -59 -139.5t-169 -58.5zM397 378q68 0 99.5 36.5t31.5 87.5q0 39 -29.5 65t-71.5 26h-189l-48 -215h207zM331 74q68 0 106.5 38t38.5 92q0 44 -29 72
t-78 28h-196l-51 -230h209z" />
    <glyph glyph-name="Gamma" unicode="&#x393;" horiz-adv-x="550" 
d="M107 0h-83l147 667h437l-16 -74h-354z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="658" 
d="M595 0h-640l415 667h103zM496 74l-89 507l-312 -507h401z" />
    <glyph glyph-name="Epsilon" unicode="&#x395;" horiz-adv-x="569" 
d="M461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355z" />
    <glyph glyph-name="Zeta" unicode="&#x396;" horiz-adv-x="585" 
d="M482 0h-488l15 69l485 524h-369l16 74h481l-14 -69l-487 -524h378z" />
    <glyph glyph-name="Eta" unicode="&#x397;" horiz-adv-x="712" 
d="M580 0h-83l67 306h-390l-67 -306h-83l147 667h83l-63 -287h390l63 287h83z" />
    <glyph glyph-name="Theta" unicode="&#x398;" horiz-adv-x="765" 
d="M577 304h-371l16 74h371zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5
q0 -104 64 -163t161 -59z" />
    <glyph glyph-name="Iota" unicode="&#x399;" horiz-adv-x="239" 
d="M107 0h-83l147 667h83z" />
    <glyph glyph-name="Kappa" unicode="&#x39a;" horiz-adv-x="601" 
d="M530 0h-97l-190 304l-87 -80l-49 -224h-83l147 667h83l-75 -337l365 337h109l-348 -317z" />
    <glyph glyph-name="Lambda" unicode="&#x39b;" horiz-adv-x="658" 
d="M595 0h-91l-97 581l-353 -581h-99l415 667h103z" />
    <glyph glyph-name="Mu" unicode="&#x39c;" horiz-adv-x="809" 
d="M677 0h-83l124 561l-351 -561h-34l-103 561l-123 -561h-83l147 667h115l94 -514l321 514h123z" />
    <glyph glyph-name="Nu" unicode="&#x39d;" horiz-adv-x="708" 
d="M576 0h-80l-269 544l-120 -544h-83l147 667h85l266 -534l118 534h83z" />
    <glyph glyph-name="Xi" unicode="&#x39e;" horiz-adv-x="615" 
d="M639 593h-506l15 74h506zM508 0h-506l16 74h505zM568 304h-493l16 74h493z" />
    <glyph glyph-name="Omicron" unicode="&#x39f;" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z
" />
    <glyph glyph-name="Pi" unicode="&#x3a0;" horiz-adv-x="712" 
d="M580 0h-83l130 593h-390l-130 -593h-83l147 667h556z" />
    <glyph glyph-name="Rho" unicode="&#x3a1;" horiz-adv-x="587" 
d="M107 0h-83l147 667h248q84 0 134.5 -50.5t50.5 -122.5q0 -40 -14 -78.5t-42.5 -73.5t-80 -56t-118.5 -21h-184zM181 339h175q76 0 118.5 43t42.5 106q0 44 -31.5 74.5t-79.5 30.5h-168z" />
    <glyph glyph-name="Sigma" unicode="&#x3a3;" horiz-adv-x="545" 
d="M236 593l177 -256l-287 -263h334l-16 -74h-438l16 80l291 268l-174 251l15 68h437l-16 -74h-339z" />
    <glyph glyph-name="Tau" unicode="&#x3a4;" horiz-adv-x="570" 
d="M272 0h-83l131 593h-211l16 74h506l-16 -74h-212z" />
    <glyph glyph-name="Upsilon" unicode="&#x3a5;" horiz-adv-x="626" 
d="M301 0h-83l62 282l-178 385h91l139 -310l275 310h103l-347 -385z" />
    <glyph glyph-name="Phi" unicode="&#x3a6;" horiz-adv-x="806" 
d="M390 0h-83l15 66q-125 12 -190.5 72.5t-65.5 159.5q0 124 107 210t271 93l15 66h83l-16 -68q126 -11 191.5 -71.5t65.5 -160.5q0 -123 -107.5 -209.5t-271.5 -93.5zM696 367q0 66 -49 108.5t-138 50.5l-88 -387q124 10 199.5 73t75.5 155zM153 298q0 -65 48.5 -107.5
t137.5 -50.5l88 386q-124 -10 -199 -73t-75 -155z" />
    <glyph glyph-name="Chi" unicode="&#x3a7;" horiz-adv-x="652" 
d="M585 0h-94l-154 282l-272 -282h-107l336 347l-174 320h95l142 -265l252 265h108l-317 -330z" />
    <glyph glyph-name="Psi" unicode="&#x3a8;" horiz-adv-x="796" 
d="M386 0h-83l29 126q-225 20 -225 204q0 37 10 80l59 257h83l-61 -270q-7 -23 -7 -57q0 -59 39 -96t118 -45l107 468h83l-107 -467q97 9 158 60t81 137l62 270h84l-65 -288q-25 -109 -114 -178t-223 -76z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="765" 
d="M31 74h152q-117 85 -117 231q0 71 26.5 138t73 119.5t117 84t152.5 31.5q137 0 222.5 -77.5t85.5 -209.5q0 -113 -59.5 -193.5t-147.5 -123.5h122l-16 -74h-240l16 74q101 21 168 107t67 201q0 101 -60.5 161t-160.5 60q-123 0 -203.5 -94t-80.5 -225q0 -74 34 -127.5
t89 -82.5l-16 -74h-240z" />
    <glyph glyph-name="Iotadieresis" unicode="&#x3aa;" horiz-adv-x="239" 
d="M107 0h-83l147 667h83zM389 793q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM183 793q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29z" />
    <glyph glyph-name="Upsilondieresis" unicode="&#x3ab;" horiz-adv-x="626" 
d="M301 0h-83l62 282l-178 385h91l139 -310l275 310h103l-347 -385zM583 793q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM377 793q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5
q18 0 30 -12t12 -29z" />
    <glyph glyph-name="alphatonos" unicode="&#x3ac;" horiz-adv-x="589" 
d="M373 565h-38l65 200h76zM525 57l-9 -65q-22 -4 -41 -4q-91 0 -83 89q-71 -89 -165 -89q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-82 -370q-13 -58 33 -58q11 0 21 2zM254 55q43 0 82.5 23.5t64.5 58.5l48 213
q-19 34 -57.5 56t-88.5 22q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5z" />
    <glyph glyph-name="epsilontonos" unicode="&#x3ad;" horiz-adv-x="514" 
d="M346 565h-38l65 200h76zM223 -12q-101 0 -152.5 34t-51.5 91q0 42 31 73.5t67.5 45t71.5 15.5q-43 7 -74.5 33t-31.5 68q0 65 70.5 106t169.5 41q131 0 195 -78l-48 -44q-46 60 -147 60q-68 0 -115.5 -25t-47.5 -64q0 -33 35 -51t91 -18h109l-14 -61h-108
q-77 0 -124.5 -22t-47.5 -70q0 -33 33.5 -52.5t94.5 -19.5q107 0 191 66l29 -50q-99 -78 -226 -78z" />
    <glyph glyph-name="etatonos" unicode="&#x3ae;" horiz-adv-x="551" 
d="M363 565h-38l65 200h76zM380 -184h-75l112 501q6 27 6 33q0 78 -100 78q-77 0 -149 -76l-78 -352h-75l107 483h75l-16 -70q86 82 171 82q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40z" />
    <glyph glyph-name="iotatonos" unicode="&#x3af;" horiz-adv-x="238" 
d="M203 565h-38l65 200h76zM124 -12q-106 0 -81 113l86 382h75l-84 -370q-13 -58 34 -58q10 0 20 2l-9 -65q-22 -4 -41 -4z" />
    <glyph glyph-name="upsilondieresistonos" unicode="&#x3b0;" horiz-adv-x="562" 
d="M378 565h-38l64 200h76zM562 609q0 -20 -16 -36t-36 -16q-18 0 -29.5 12t-11.5 30q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM294 609q0 -20 -16 -36t-36 -16q-18 0 -29.5 12t-11.5 30q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM244 -12q-109 0 -153 64.5
t-20 168.5l60 262h75l-62 -270q-16 -75 8.5 -116.5t91.5 -41.5q89 0 149 79.5t60 189.5q0 86 -35 144l70 27q40 -68 40 -170q0 -140 -81.5 -238.5t-202.5 -98.5z" />
    <glyph glyph-name="alpha" unicode="&#x3b1;" horiz-adv-x="589" 
d="M525 57l-9 -65q-22 -4 -41 -4q-91 0 -83 89q-71 -89 -165 -89q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-82 -370q-13 -58 33 -58q11 0 21 2zM254 55q43 0 82.5 23.5t64.5 58.5l48 213q-19 34 -57.5 56t-88.5 22
q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5z" />
    <glyph glyph-name="beta" unicode="&#x3b2;" horiz-adv-x="575" 
d="M275 -12q-116 0 -163 84l-58 -256h-75l145 639q26 112 101 167t165 55q86 0 136.5 -39t50.5 -111q0 -77 -56 -124.5t-123 -58.5q47 -6 83.5 -40.5t36.5 -97.5q0 -93 -73 -155.5t-170 -62.5zM199 455l-74 -325q15 -33 51.5 -54t86.5 -21q80 0 129 43.5t49 110.5
q0 49 -36.5 74t-95.5 25h-35l15 67h33q73 0 123 37t50 102q0 45 -31 70.5t-83 25.5q-61 0 -113 -40.5t-69 -114.5z" />
    <glyph glyph-name="gamma" unicode="&#x3b3;" horiz-adv-x="490" 
d="M187 -184h-75l30 135q33 160 6.5 299.5t-99.5 232.5h88q50 -67 76 -179.5t14 -210.5q73 77 133.5 184t82.5 206h75q-27 -116 -105 -245t-183 -233z" />
    <glyph glyph-name="delta" unicode="&#x3b4;" 
d="M258 -12q-107 0 -163.5 57t-56.5 141q0 81 53.5 147t139.5 98q-60 38 -60 97q0 60 58.5 105.5t147.5 45.5q117 0 171 -70l-46 -47q-39 56 -131 56q-54 0 -90 -25t-36 -60q0 -26 29 -45t70.5 -35t83.5 -37.5t71 -64t29 -101.5q0 -103 -76 -182.5t-194 -79.5zM453 244
q0 32 -11.5 57t-36 42.5t-44.5 27.5t-54 24q-7 3 -10 4q-85 -27 -132 -82t-47 -124q0 -58 37 -98t108 -40q80 0 135 57t55 132z" />
    <glyph glyph-name="epsilon" unicode="&#x3b5;" horiz-adv-x="514" 
d="M223 -12q-101 0 -152.5 34t-51.5 91q0 42 31 73.5t67.5 45t71.5 15.5q-43 7 -74.5 33t-31.5 68q0 65 70.5 106t169.5 41q131 0 195 -78l-48 -44q-46 60 -147 60q-68 0 -115.5 -25t-47.5 -64q0 -33 35 -51t91 -18h109l-14 -61h-108q-77 0 -124.5 -22t-47.5 -70
q0 -33 33.5 -52.5t94.5 -19.5q107 0 191 66l29 -50q-99 -78 -226 -78z" />
    <glyph glyph-name="zeta" unicode="&#x3b6;" horiz-adv-x="489" 
d="M307 -39q0 27 -50 27q-104 0 -163 48t-59 132q0 123 104.5 232.5t289.5 200.5h-284l16 66h382l-14 -57q-413 -211 -413 -427q0 -61 43.5 -94.5t121.5 -33.5q57 0 83.5 -16t26.5 -51q0 -52 -75 -145h-86q77 79 77 118z" />
    <glyph glyph-name="eta" unicode="&#x3b7;" horiz-adv-x="551" 
d="M380 -184h-75l112 501q6 27 6 33q0 78 -100 78q-77 0 -149 -76l-78 -352h-75l107 483h75l-16 -70q86 82 171 82q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40z" />
    <glyph glyph-name="theta" unicode="&#x3b8;" 
d="M261 -12q-100 0 -154 69t-54 191q0 103 36 200.5t107.5 164t161.5 66.5q100 0 153 -70t53 -191q0 -103 -35.5 -200t-106.5 -163.5t-161 -66.5zM266 55q70 0 124.5 72t79.5 175h-336q-4 -31 -4 -65q0 -83 33 -132.5t103 -49.5zM352 612q-70 0 -124.5 -71t-80.5 -173h335
q4 42 4 61q0 83 -32 133t-102 50z" />
    <glyph glyph-name="iota" unicode="&#x3b9;" horiz-adv-x="238" 
d="M124 -12q-106 0 -81 113l86 382h75l-84 -370q-13 -58 34 -58q10 0 20 2l-9 -65q-22 -4 -41 -4z" />
    <glyph glyph-name="kappa" unicode="&#x3ba;" horiz-adv-x="514" 
d="M446 0h-91l-128 220l-101 -82l-30 -138h-75l107 483h75l-56 -253l306 253h99l-263 -219z" />
    <glyph glyph-name="lambda" unicode="&#x3bb;" horiz-adv-x="490" 
d="M143 604l4 67q24 8 56 8q102 -2 120 -98l110 -581h-79l-72 396l-248 -396h-85l312 487l-15 73q-6 30 -20 41t-40 11q-24 0 -43 -8z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="564" 
d="M103 36l-50 -220h-75l151 667h75l-71 -317q-13 -56 11.5 -84.5t72.5 -28.5q40 0 84 22t73 53l79 355h75l-82 -370q-13 -58 34 -58q10 0 20 2l-9 -65q-22 -4 -41 -4q-89 0 -84 86q-84 -86 -173 -86q-69 0 -90 48z" />
    <glyph glyph-name="nu" unicode="&#x3bd;" horiz-adv-x="490" 
d="M225 0h-75l-91 483h81l72 -407q84 90 146.5 198t84.5 209h75q-27 -117 -107 -248t-186 -235z" />
    <glyph glyph-name="xi" unicode="&#x3be;" horiz-adv-x="489" 
d="M263 -12q-116 0 -176 41t-60 117q0 79 54.5 127t124.5 60q-40 7 -70.5 35t-30.5 77q0 47 42 92t100 64h-100l16 66h371l-16 -66h-187q-57 -13 -100 -53t-43 -84q0 -43 37 -66t95 -23h142l-15 -65h-138q-87 0 -143 -38.5t-56 -110.5q0 -50 44.5 -78t126.5 -28
q57 0 83.5 -16t26.5 -51q0 -52 -75 -145h-81q72 84 72 118q0 27 -44 27z" />
    <glyph glyph-name="omicron" unicode="&#x3bf;" 
d="M261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="582" 
d="M438 0h-75l95 417h-252l-95 -417h-75l95 417h-80l15 66h562l-15 -66h-80z" />
    <glyph glyph-name="rho" unicode="&#x3c1;" 
d="M281 -12q-55 0 -99.5 24t-68.5 64l-59 -260h-75l96 426q25 115 96.5 184t172.5 69q91 0 140.5 -57.5t49.5 -147.5q0 -119 -71 -210.5t-182 -91.5zM271 55q80 0 132 68t52 159q0 62 -31 104t-89 42q-70 0 -118 -52t-67 -134l-25 -109q19 -35 57.5 -56.5t88.5 -21.5z" />
    <glyph glyph-name="sigma1" unicode="&#x3c2;" horiz-adv-x="483" 
d="M259 -12q-98 0 -158.5 57.5t-60.5 158.5q0 120 79 205.5t193 85.5q127 0 181 -90l-57 -43q-37 66 -122 66q-88 0 -142 -66t-54 -157q0 -74 44.5 -112t126.5 -38q102 0 102 -69q0 -49 -74 -143h-83q78 82 78 117q0 28 -53 28z" />
    <glyph glyph-name="sigma" unicode="&#x3c3;" 
d="M525 275q0 -114 -75.5 -200.5t-188.5 -86.5q-102 0 -161 58.5t-59 155.5q0 116 86.5 198.5t221.5 82.5h254l-15 -67h-117q54 -52 54 -141zM452 274q0 105 -65 142h-47q-99 0 -159.5 -64t-60.5 -147q0 -69 38 -109.5t105 -40.5q80 0 134.5 66.5t54.5 152.5z" />
    <glyph glyph-name="tau" unicode="&#x3c4;" horiz-adv-x="468" 
d="M275 -12q-57 0 -89 28.5t-19 87.5l70 313h-170l15 66h416l-15 -66h-171l-67 -303q-6 -30 8 -44.5t40 -14.5q28 0 46 16l8 -60q-28 -23 -72 -23z" />
    <glyph glyph-name="upsilon" unicode="&#x3c5;" horiz-adv-x="562" 
d="M244 -12q-109 0 -153 64.5t-20 168.5l60 262h75l-62 -270q-16 -75 8.5 -116.5t91.5 -41.5q89 0 149 79.5t60 189.5q0 86 -35 144l70 27q40 -68 40 -170q0 -140 -81.5 -238.5t-202.5 -98.5z" />
    <glyph glyph-name="phi" unicode="&#x3c6;" horiz-adv-x="744" 
d="M705 274q0 -116 -95 -199t-258 -86l-39 -173h-75l39 175q-116 12 -175.5 70t-59.5 155q0 95 66 166.5t176 112.5l20 -60q-182 -72 -182 -219q0 -142 170 -159l100 438h38q132 0 203.5 -59t71.5 -162zM452 427l-85 -371q128 7 193 70.5t65 147.5q0 69 -42 109t-131 44z
" />
    <glyph glyph-name="chi" unicode="&#x3c7;" horiz-adv-x="486" 
d="M379 -184h-80l-86 273l-211 -273h-86l263 343l-101 324h80l79 -254l195 254h88l-249 -324z" />
    <glyph glyph-name="psi" unicode="&#x3c8;" horiz-adv-x="744" 
d="M313 -184h-75l40 175q-212 19 -212 178q0 33 9 69l56 245h75l-58 -255q-7 -30 -7 -50q0 -50 38.5 -82t113.5 -39l139 610h75l-140 -611q88 6 147.5 49.5t75.5 116.5l60 261h75l-63 -273q-23 -98 -104.5 -158t-205.5 -64z" />
    <glyph glyph-name="omega" unicode="&#x3c9;" horiz-adv-x="808" 
d="M774 326q0 -138 -71 -238t-167 -100q-140 0 -150 147q-71 -147 -187 -147q-78 0 -119.5 53.5t-41.5 142.5q0 78 44 168t121 143l48 -50q-65 -46 -100.5 -116t-35.5 -144q0 -58 23 -94t74 -36q56 0 96 52.5t59 134.5l28 123h75l-28 -123q-18 -80 3.5 -133.5t84.5 -53.5
q73 0 121.5 81.5t48.5 188.5q0 83 -40 132l61 38q53 -56 53 -169z" />
    <glyph glyph-name="iotadieresis" unicode="&#x3ca;" horiz-adv-x="238" 
d="M344 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM138 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM124 -12q-106 0 -81 113l86 382h75l-84 -370
q-13 -58 34 -58q10 0 20 2l-9 -65q-22 -4 -41 -4z" />
    <glyph glyph-name="upsilondieresis" unicode="&#x3cb;" horiz-adv-x="562" 
d="M522 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM316 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM244 -12q-109 0 -153 64.5t-20 168.5l60 262h75
l-62 -270q-16 -75 8.5 -116.5t91.5 -41.5q89 0 149 79.5t60 189.5q0 86 -35 144l70 27q40 -68 40 -170q0 -140 -81.5 -238.5t-202.5 -98.5z" />
    <glyph glyph-name="omicrontonos" unicode="&#x3cc;" 
d="M373 565h-38l65 200h76zM261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5
t106 -40.5z" />
    <glyph glyph-name="upsilontonos" unicode="&#x3cd;" horiz-adv-x="562" 
d="M378 565h-38l65 200h76zM244 -12q-109 0 -153 64.5t-20 168.5l60 262h75l-62 -270q-16 -75 8.5 -116.5t91.5 -41.5q89 0 149 79.5t60 189.5q0 86 -35 144l70 27q40 -68 40 -170q0 -140 -81.5 -238.5t-202.5 -98.5z" />
    <glyph glyph-name="omegatonos" unicode="&#x3ce;" horiz-adv-x="808" 
d="M491 565h-38l65 200h76zM774 326q0 -138 -71 -238t-167 -100q-140 0 -150 147q-71 -147 -187 -147q-78 0 -119.5 53.5t-41.5 142.5q0 78 44 168t121 143l48 -50q-65 -46 -100.5 -116t-35.5 -144q0 -58 23 -94t74 -36q56 0 96 52.5t59 134.5l28 123h75l-28 -123
q-18 -80 3.5 -133.5t84.5 -53.5q73 0 121.5 81.5t48.5 188.5q0 83 -40 132l61 38q53 -56 53 -169z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="569" 
d="M562 776q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM356 776q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM461 0h-437l147 667h437l-16 -74h-354l-48 -215h347
l-16 -74h-348l-51 -230h355z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="769" 
d="M444 -12l16 74q62 0 106.5 37t58.5 101l2 8q16 72 -16 109.5t-108 37.5q-82 0 -159 -23l-72 -332h-83l131 593h-212l16 74h506l-16 -74h-211l-42 -187q88 23 165 23q113 0 159 -61t25 -160l-2 -8q-23 -104 -96 -158t-168 -54z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="550" 
d="M610 867l-201 -144h-59l180 144h80zM107 0h-83l147 667h437l-16 -74h-354z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="676" 
d="M368 -12q-134 0 -221 80.5t-87 211.5q0 88 31.5 163.5t83.5 126.5t120 79.5t140 28.5q97 0 167 -42t105 -115l-79 -31q-58 114 -198 114q-91 0 -167.5 -62t-102.5 -164h377l-16 -74h-373v-20q0 -99 61 -160.5t164 -61.5q98 0 179 76l61 -46q-97 -104 -245 -104z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="586" 
d="M286 -12q-88 0 -161.5 33.5t-108.5 85.5l57 60q35 -49 93 -77t127 -28q72 0 109.5 38t37.5 86q0 36 -34 62.5t-83 45t-97.5 40t-82.5 59t-34 90.5q0 79 67.5 136.5t168.5 57.5q77 0 141.5 -28t99.5 -77l-57 -58q-32 44 -84 67t-109 23t-97.5 -33t-40.5 -77
q0 -33 34 -57.5t82.5 -43.5t97.5 -41t83 -61t34 -92q0 -85 -65.5 -148t-177.5 -63z" />
    <glyph glyph-name="afii10055" unicode="&#x406;" horiz-adv-x="239" 
d="M107 0h-83l147 667h83z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="239" 
d="M385 776q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM179 776q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM107 0h-83l147 667h83z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="475" 
d="M158 -12q-58 0 -106 21.5t-74 62.5l50 60q39 -70 126 -70q60 0 98 37.5t52 102.5l103 465h83l-103 -466q-47 -213 -229 -213z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="1060" 
d="M-25 -12l17 74q42 0 73 20t65.5 79.5t72.5 170.5l115 335h408l-58 -265h171q90 0 138 -53t42 -131q-8 -86 -73 -152t-182 -66h-267l130 593h-251l-92 -267q-37 -107 -73 -176t-75 -103.5t-75.5 -46.5t-85.5 -12zM824 328h-172l-56 -254h173q68 0 112 39t50 99
q6 50 -23.5 83t-83.5 33z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="1060" 
d="M775 0h-278l67 306h-391l-66 -306h-83l146 667h83l-63 -287h390l63 287h83l-63 -287h182q86 0 131.5 -50t39.5 -124q-7 -82 -68.5 -144t-172.5 -62zM832 306h-185l-51 -232h186q61 0 101.5 35.5t46.5 89.5q6 46 -21 76.5t-77 30.5z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="785" 
d="M581 0l44 201q17 76 -14 115t-108 39q-82 0 -159 -23l-72 -332h-83l131 593h-212l16 74h506l-16 -74h-211l-42 -187q88 23 165 23q114 0 159.5 -61.5t22.5 -167.5l-44 -200h-83z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="601" 
d="M615 867l-201 -144h-59l180 144h80zM530 0h-97l-190 304l-87 -80l-49 -224h-83l147 667h83l-75 -337l365 337h109l-348 -317z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="626" 
d="M88 -12q-41 0 -72.5 15t-45.5 38l50 66q31 -45 80 -45q35 0 60 19.5t66 73.5l23 30l-148 482h89l119 -414l300 414h100l-411 -549q-50 -66 -97 -98t-113 -32zM598 793q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="712" 
d="M232 -127l28 127h-236l146 667h83l-131 -593h391l130 593h83l-146 -667h-237l-28 -127h-83z" />
    <glyph glyph-name="afii10017" unicode="&#x410;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" horiz-adv-x="587" 
d="M170 667h433l-15 -74h-351l-42 -191h166q83 0 134 -50.5t51 -122.5q0 -40 -14 -78.5t-43 -73.5t-80 -56t-117 -21h-268zM348 328h-169l-57 -254h176q75 0 118 43t43 106q0 43 -32 74t-79 31z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="629" 
d="M333 0h-309l147 667h277q74 0 122 -43t48 -110q0 -70 -45.5 -121t-109.5 -58q43 -11 70.5 -50.5t27.5 -86.5q0 -81 -59 -139.5t-169 -58.5zM397 378q68 0 99.5 36.5t31.5 87.5q0 39 -29.5 65t-71.5 26h-189l-48 -215h207zM331 74q68 0 106.5 38t38.5 92q0 44 -29 72
t-78 28h-196l-51 -230h209z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="550" 
d="M107 0h-83l147 667h437l-16 -74h-354z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="723" 
d="M-49 -123l41 190q33 3 56 13t49.5 37t51.5 79.5t54 135.5l115 335h408l-130 -593h60l-43 -197h-83l27 123h-497l-27 -123h-82zM376 593l-92 -267q-68 -194 -158 -252h387l114 519h-251z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="569" 
d="M461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="909" 
d="M73 0h-110l379 350l-209 317h97l204 -324l71 324h83l-67 -305l328 305h112l-348 -317l226 -350h-97l-190 304l-55 -50l-55 -254h-83l55 254l-26 41z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="584" 
d="M269 -12q-91 0 -163.5 38t-99.5 101l60 47q25 -52 80 -82t122 -30q76 0 121.5 39.5t45.5 100.5q0 46 -41.5 74.5t-115.5 28.5h-124l16 74h152q69 0 119 30.5t50 87.5q0 46 -44.5 76t-116.5 30q-103 0 -176 -66l-35 57q39 38 97 60.5t120 22.5q107 0 173 -44.5t66 -123.5
q0 -71 -57.5 -118t-128.5 -53q55 -10 91.5 -48t36.5 -95q0 -87 -70.5 -147t-177.5 -60z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="708" 
d="M104 0h-80l147 667h83l-118 -534l502 534h85l-147 -667h-84l121 544z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="708" 
d="M104 0h-80l147 667h83l-118 -534l502 534h85l-147 -667h-84l121 544zM644 793q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="601" 
d="M530 0h-97l-190 304l-87 -80l-49 -224h-83l147 667h83l-75 -337l365 337h109l-348 -317z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="712" 
d="M-25 -12l16 74q43 0 74 20t65.5 79.5t72.5 170.5l115 335h408l-146 -667h-83l130 593h-251l-92 -267q-37 -107 -73 -176t-75 -103.5t-75.5 -46.5t-85.5 -12z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="809" 
d="M677 0h-83l124 561l-351 -561h-34l-103 561l-123 -561h-83l147 667h115l94 -514l321 514h123z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="712" 
d="M580 0h-83l67 306h-390l-67 -306h-83l147 667h83l-63 -287h390l63 287h83z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z
" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="712" 
d="M580 0h-83l130 593h-390l-130 -593h-83l147 667h556z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" horiz-adv-x="587" 
d="M107 0h-83l147 667h248q84 0 134.5 -50.5t50.5 -122.5q0 -40 -14 -78.5t-42.5 -73.5t-80 -56t-118.5 -21h-184zM181 339h175q76 0 118.5 43t42.5 106q0 44 -31.5 74.5t-79.5 30.5h-168z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="676" 
d="M368 -12q-134 0 -221 80.5t-87 211.5q0 88 31.5 163.5t83.5 126.5t120 79.5t140 28.5q97 0 167 -42t105 -115l-79 -31q-58 114 -198 114q-112 0 -197 -90t-85 -230q0 -99 61 -160.5t164 -61.5q98 0 179 76l61 -46q-97 -104 -245 -104z" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="570" 
d="M272 0h-83l131 593h-211l16 74h506l-16 -74h-212z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="626" 
d="M88 -12q-41 0 -72.5 15t-45.5 38l50 66q31 -45 80 -45q35 0 60 19.5t66 73.5l23 30l-148 482h89l119 -414l300 414h100l-411 -549q-50 -66 -97 -98t-113 -32z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="806" 
d="M390 0h-83l15 66q-125 12 -190.5 72.5t-65.5 159.5q0 124 107 210t271 93l15 66h83l-16 -68q126 -11 191.5 -71.5t65.5 -160.5q0 -123 -107.5 -209.5t-271.5 -93.5zM696 367q0 66 -49 108.5t-138 50.5l-88 -387q124 10 199.5 73t75.5 155zM153 298q0 -65 48.5 -107.5
t137.5 -50.5l88 386q-124 -10 -199 -73t-75 -155z" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="652" 
d="M585 0h-94l-154 282l-272 -282h-107l336 347l-174 320h95l142 -265l252 265h108l-317 -330z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="723" 
d="M530 -123l27 123h-533l146 667h83l-131 -593h391l130 593h83l-130 -593h60l-43 -197h-83z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="611" 
d="M233 667l-39 -174q-33 -154 123 -154q81 0 158 23l67 305h83l-146 -667h-83l63 288q-90 -23 -166 -23q-115 0 -160 61.5t-22 167.5l39 173h83z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="958" 
d="M889 667h83l-146 -667h-802l146 667h83l-131 -593h277l131 593h83l-131 -593h277z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="970" 
d="M889 667h83l-130 -593h61l-43 -197h-83l27 123h-780l146 667h83l-131 -593h277l131 593h83l-131 -593h277z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="752" 
d="M456 0h-267l131 593h-212l16 74h294l-58 -265h166q84 0 134.5 -50.5t50.5 -122.5q0 -40 -14 -78.5t-42.5 -73.5t-80 -56t-118.5 -21zM512 328h-168l-56 -254h173q77 0 120 43t43 106q0 43 -32 74t-80 31z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="788" 
d="M656 0h-83l146 667h83zM170 667h83l-58 -265h166q84 0 134.5 -50.5t50.5 -122.5q0 -40 -14 -78.5t-43 -73.5t-80.5 -56t-117.5 -21h-267zM347 328h-168l-57 -254h174q76 0 119.5 43t43.5 106q0 43 -32 74t-80 31z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" horiz-adv-x="587" 
d="M170 667h83l-58 -265h166q84 0 134.5 -50.5t50.5 -122.5q0 -40 -14 -78.5t-43 -73.5t-80.5 -56t-117.5 -21h-267zM347 328h-168l-57 -254h174q76 0 119.5 43t43.5 106q0 43 -32 74t-80 31z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="676" 
d="M346 678q135 0 221.5 -80.5t86.5 -211.5q0 -88 -31.5 -163.5t-83.5 -126.5t-120 -79.5t-140 -28.5q-96 0 -166 42t-106 115l79 31q58 -114 198 -114q95 0 173.5 66.5t101.5 175.5h-374l16 74h366v4q0 99 -61.5 160.5t-164.5 61.5q-99 0 -178 -76l-62 46q98 104 245 104z
" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="992" 
d="M579 -12q-139 0 -220 88t-68 228v2h-118l-66 -306h-83l146 667h83l-63 -287h114q34 130 136.5 214t236.5 84q139 0 220 -87.5t68 -227.5q-13 -159 -123 -267t-263 -108zM585 62q113 0 197 84.5t96 211.5q12 110 -46.5 178t-161.5 68q-114 0 -197 -84t-95 -212
q-12 -111 45.5 -178.5t161.5 -67.5z" />
    <glyph glyph-name="afii10049" unicode="&#x42f;" horiz-adv-x="608" 
d="M99 0h-107l242 271q-53 13 -93 56.5t-40 109.5q0 92 65.5 161t190.5 69h267l-148 -667h-83l59 265h-124zM304 338h164l56 255h-175q-74 0 -117.5 -43t-43.5 -106q0 -45 33.5 -75.5t82.5 -30.5z" />
    <glyph glyph-name="afii10065" unicode="&#x430;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="577" 
d="M263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5zM261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 92 44 183q48 110 122 165t190 69q70 11 99.5 22t32.5 26h73q-19 -86 -214 -111q-104 -14 -158.5 -55
t-84.5 -109q79 103 189 103q92 0 144.5 -58.5t52.5 -156.5q0 -117 -77.5 -204.5t-191.5 -87.5z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="554" 
d="M301 0h-280l106 483h246q58 0 93 -28t35 -78q0 -49 -29.5 -85t-79.5 -47q31 -12 49.5 -38t18.5 -62q0 -61 -43 -103t-116 -42zM110 66h183q41 0 66.5 24t25.5 60q0 29 -20 47t-56 18h-166zM157 281h183q37 0 59.5 23t22.5 55q0 28 -20.5 43t-55.5 15h-159z" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="436" 
d="M465 483l-15 -66h-263l-91 -417h-75l106 483h338z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="561" 
d="M-62 -123l39 185q37 7 67 45t65 138l84 238h336l-92 -417h56l-42 -189h-75l28 123h-365l-27 -123h-74zM244 417l-62 -181q-45 -128 -107 -170h287l77 351h-195z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="563" 
d="M276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5
t-63 -113.5z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="711" 
d="M61 0h-101l273 264l-166 219h90l165 -218l48 218h75l-45 -206l247 206h101l-263 -219l158 -264h-91l-128 220l-45 -37l-40 -183h-75l40 183l-25 31z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="514" 
d="M300 495q98 0 146 -32.5t48 -89.5q0 -60 -52.5 -94.5t-109.5 -38.5q44 -6 78 -33t34 -70q0 -65 -67.5 -107t-159.5 -42q-149 0 -214 82l46 45q59 -65 168 -65q62 0 106 26t44 65q0 35 -34.5 54t-90.5 19h-116l14 61h108q75 0 120 22t45 67q0 69 -119 69q-102 0 -188 -61
l-32 49q97 74 226 74z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="552" 
d="M93 0h-72l106 483h75l-81 -369l334 369h74l-106 -483h-75l82 376z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="552" 
d="M93 0h-72l106 483h75l-81 -369l334 369h74l-106 -483h-75l82 376zM529 626q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="514" 
d="M446 0h-91l-128 220l-101 -82l-30 -138h-75l107 483h75l-56 -253l306 253h99l-263 -219z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="552" 
d="M-39 -12l14 62q34 0 65.5 45t68.5 150l84 238h336l-106 -483h-75l91 417h-195l-62 -181q-45 -133 -97.5 -190.5t-123.5 -57.5z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="643" 
d="M514 0h-75l82 379l-239 -379h-28l-75 379l-83 -379h-75l106 483h96l68 -358l225 358h104z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" horiz-adv-x="551" 
d="M96 0h-75l110 483h75l-45 -198h252l45 198h75l-110 -483h-75l50 219h-252z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" 
d="M261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" horiz-adv-x="551" 
d="M423 0h-75l95 417h-252l-95 -417h-75l110 483h402z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" 
d="M281 -12q-56 0 -99.5 23.5t-68.5 64.5l-58 -260h-75l148 667h75l-16 -69q66 81 160 81q84 0 135.5 -53t51.5 -146q0 -123 -70.5 -215.5t-182.5 -92.5zM271 55q80 0 132 68t52 159q0 67 -37 106.5t-98 39.5q-44 0 -83 -23.5t-65 -59.5l-47 -212q19 -35 57.5 -56.5
t88.5 -21.5z" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="495" 
d="M262 -12q-99 0 -160.5 58t-61.5 158q0 120 79 205.5t193 85.5q127 0 181 -90l-57 -43q-37 66 -122 66q-88 0 -142 -66t-54 -157q0 -72 42.5 -111t108.5 -39q72 0 122 57l43 -50q-72 -74 -172 -74z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="402" 
d="M184 0h-75l91 417h-140l15 66h356l-15 -66h-141z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="490" 
d="M-46 -185l26 67q18 -8 47 -8q40 0 72 48l49 75l-92 486h79l72 -396l249 396h84l-372 -581q-63 -98 -151 -98q-36 0 -63 11z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="798" 
d="M263 495q45 0 80.5 -23t53.5 -62l57 257h75l-56 -254q58 82 138 82q69 0 104.5 -52.5t35.5 -132.5q0 -78 -27 -151t-78 -122t-114 -49q-44 0 -80 23t-54 62l-56 -257h-75l55 254q-56 -82 -138 -82q-69 0 -104.5 52.5t-35.5 132.5q0 78 27 151t78 122t114 49zM277 428
q-66 0 -111 -76.5t-45 -167.5q0 -59 25 -94t71 -35q64 0 119 77l48 218q-10 32 -39.5 55t-67.5 23zM518 55q66 0 111 76.5t45 167.5q0 59 -25 94t-71 35q-64 0 -119 -77l-48 -218q10 -32 39.5 -55t67.5 -23z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="486" 
d="M421 0h-83l-102 198l-189 -198h-89l239 248l-122 235h83l93 -184l176 184h90l-228 -235z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="561" 
d="M376 -123l28 123h-383l106 483h75l-92 -417h252l92 417h75l-92 -417h56l-42 -189h-75z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="541" 
d="M117 483h75l-30 -136q-14 -65 10.5 -90t87.5 -25q78 0 133 26l50 225h75l-106 -483h-75l43 197q-71 -32 -155 -32q-94 0 -127.5 38t-15.5 121z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="783" 
d="M685 483h75l-106 -483h-633l106 483h75l-92 -417h204l92 417h75l-92 -417h204z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="798" 
d="M685 483h75l-92 -417h56l-42 -189h-75l28 123h-614l106 483h75l-92 -417h204l92 417h75l-92 -417h204z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="610" 
d="M371 0h-262l91 417h-140l15 66h215l-42 -187h164q69 0 106 -38t32 -98q-6 -66 -54.5 -113t-124.5 -47zM398 230h-164l-36 -164h175q42 0 68.5 24.5t31.5 62.5q4 36 -15 56.5t-60 20.5z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="712" 
d="M583 0h-75l106 483h75zM127 483h75l-42 -187h157q67 0 106 -35t39 -93q0 -69 -48.5 -118.5t-127.5 -49.5h-265zM304 230h-158l-36 -164h179q43 0 69.5 27.5t26.5 68.5q0 32 -21 50t-60 18z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="522" 
d="M127 483h75l-42 -187h157q67 0 106 -35t39 -93q0 -69 -48.5 -118.5t-127.5 -49.5h-265zM304 230h-158l-36 -164h179q43 0 69.5 27.5t26.5 68.5q0 32 -21 50t-60 18z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="495" 
d="M248 495q99 0 161 -58t62 -158q0 -120 -79 -205.5t-193 -85.5q-127 0 -181 90l53 40q19 -32 52.5 -50t72.5 -18q71 0 121.5 45.5t67.5 116.5h-246l15 65h239q-1 73 -43 114t-108 41q-77 0 -125 -58l-40 47q72 74 171 74z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="767" 
d="M456 -12q-103 0 -162.5 58.5t-59.5 155.5v14h-91l-47 -216h-75l106 483h75l-44 -201h86q25 92 96 152.5t164 60.5q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM457 55q80 0 134.5 67.5t54.5 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134.5 -67.5
t-54.5 -155.5q0 -69 38.5 -109.5t105.5 -40.5z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" horiz-adv-x="547" 
d="M96 0h-91l173 194q-44 11 -70 42t-26 79q0 69 48.5 118.5t127.5 49.5h266l-106 -483h-75l41 187h-128zM234 253h164l36 164h-179q-43 0 -69.5 -27.5t-26.5 -68.5q0 -31 20 -49.5t55 -18.5z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="563" 
d="M514 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM308 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207
t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5t-63 -113.5z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="554" 
d="M371 535h-156l-27 -122q36 34 88 58t103 24q154 0 120 -154l-85 -388q-16 -71 -61.5 -110t-115.5 -39q-59 0 -97 36l39 57q24 -31 62 -31q78 0 98 87l80 365q14 60 -9 85t-76 25q-41 0 -85 -22t-74 -53l-77 -353h-75l117 535h-68l10 48h68l19 84h75l-19 -84h156z" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="436" 
d="M520 700l-201 -144h-59l180 144h80zM465 483l-15 -66h-263l-91 -417h-75l106 483h338z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="495" 
d="M262 -12q-99 0 -160.5 58t-61.5 158q0 120 79 205.5t193 85.5q127 0 181 -90l-54 -40q-39 68 -124 68q-70 0 -120 -44t-68 -112h246l-15 -65h-240v-4q0 -74 42 -115.5t109 -41.5q76 0 125 58l40 -47q-72 -74 -172 -74z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="465" 
d="M207 -12q-136 0 -204 85l48 52q21 -31 65 -53.5t95 -22.5q50 0 80.5 25t30.5 60q0 25 -26 44t-63.5 32.5t-75 28.5t-63.5 42.5t-26 64.5q0 63 49 106t134 43q60 0 109.5 -22.5t74.5 -56.5l-43 -49q-17 27 -58.5 47t-86.5 20q-47 0 -75.5 -21t-28.5 -53q0 -24 26 -41.5
t63.5 -31t75 -29t63.5 -44t26 -67.5q0 -66 -51.5 -112.5t-138.5 -46.5z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="225" 
d="M186 552q-20 0 -33 12.5t-13 31.5q0 25 18 41t39 16q19 0 32.5 -13t13.5 -31q0 -25 -18 -41t-39 -16zM96 0h-75l107 483h75z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="225" 
d="M342 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM136 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM96 0h-75l107 483h75z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="225" 
d="M-68 -196q-79 0 -115 42l36 54q25 -34 69 -34q68 0 88 87l118 530h75l-118 -530q-33 -149 -153 -149zM186 552q-20 0 -33 12.5t-13 31.5q0 25 18 41t39 16q19 0 32.5 -13t13.5 -31q0 -25 -18 -41t-39 -16z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="849" 
d="M-39 -12l14 62q34 0 65.5 45t68.5 150l84 238h336l-42 -187h157q67 0 106 -35t39 -93q0 -69 -48.5 -118.5t-127.5 -49.5h-265l91 417h-195l-62 -181q-45 -133 -97.5 -190.5t-123.5 -57.5zM630 230h-157l-36 -164h179q43 0 69.5 27.5t26.5 68.5q0 32 -21.5 50t-60.5 18z
" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="849" 
d="M96 0h-75l106 483h75l-44 -201h251l45 201h75l-45 -201h166q64 0 101 -33t37 -88q0 -67 -46 -114t-121 -47h-273l47 216h-252zM636 216h-166l-33 -150h186q40 0 63.5 25t23.5 63q0 29 -19 45.5t-55 16.5z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="553" 
d="M424 0h-75l69 317q6 27 6 33q0 78 -100 78q-76 0 -149 -77l-78 -351h-75l118 535h-67l11 48h66l19 84h75l-19 -84h158l-11 -48h-157l-27 -122q86 82 171 82q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" horiz-adv-x="514" 
d="M536 700l-201 -144h-59l180 144h80zM446 0h-91l-128 220l-101 -82l-30 -138h-75l107 483h75l-56 -253l306 253h99l-263 -219z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="490" 
d="M-46 -185l26 67q18 -8 47 -8q40 0 72 48l49 75l-92 486h79l72 -396l249 396h84l-372 -581q-63 -98 -151 -98q-36 0 -63 11zM496 626q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="552" 
d="M157 -126l27 126h-163l106 483h75l-92 -417h252l92 417h75l-106 -483h-164l-27 -126h-75z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="550" 
d="M107 0h-83l146 667h354l27 123h83l-42 -197h-355z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="436" 
d="M187 417l-91 -417h-75l106 483h263l28 126h75l-43 -192h-263z" />
    <glyph glyph-name="uni1E02" unicode="&#x1e02;" horiz-adv-x="629" 
d="M333 0h-309l147 667h277q74 0 122 -43t48 -110q0 -70 -45.5 -121t-109.5 -58q43 -11 70.5 -50.5t27.5 -86.5q0 -81 -59 -139.5t-169 -58.5zM397 378q68 0 99.5 36.5t31.5 87.5q0 39 -29.5 65t-71.5 26h-189l-48 -215h207zM331 74q68 0 106.5 38t38.5 92q0 44 -29 72
t-78 28h-196l-51 -230h209zM477 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E03" unicode="&#x1e03;" horiz-adv-x="575" 
d="M281 -12q-56 0 -99.5 23.5t-68.5 64.5l-17 -76h-75l147 667h75l-56 -253q66 81 160 81q84 0 135.5 -53t51.5 -146q0 -123 -70.5 -215.5t-182.5 -92.5zM271 55q80 0 132 68t52 159q0 67 -37 106.5t-98 39.5q-44 0 -83 -23.5t-65 -59.5l-47 -212q19 -35 57.5 -56.5
t88.5 -21.5zM461 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E0A" unicode="&#x1e0a;" horiz-adv-x="700" 
d="M251 0h-227l147 667h223q114 0 195.5 -81t81.5 -204q0 -70 -25.5 -136.5t-75 -122t-132.5 -89.5t-187 -34zM261 74h4q143 0 231 88.5t88 215.5q0 92 -59 153.5t-147 61.5h-140l-116 -519h139zM518 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5
t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E0B" unicode="&#x1e0b;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l58 260h75l-148 -667h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM444 774
q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E1E" unicode="&#x1e1e;" horiz-adv-x="550" 
d="M459 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30zM107 0h-83l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348z" />
    <glyph glyph-name="uni1E1F" unicode="&#x1e1f;" horiz-adv-x="283" 
d="M385 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30zM117 0h-75l92 418h-80l15 65h80l8 37q35 157 161 157q64 0 105 -41l-40 -45q-24 24 -62 24q-69 0 -89 -95l-8 -37h98l-15 -65h-98z" />
    <glyph glyph-name="uni1E22" unicode="&#x1e22;" horiz-adv-x="712" 
d="M580 0h-83l67 306h-390l-67 -306h-83l147 667h83l-63 -287h390l63 287h83zM518 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E23" unicode="&#x1e23;" horiz-adv-x="552" 
d="M423 0h-75l69 317q6 27 6 33q0 78 -100 78q-76 0 -149 -77l-78 -351h-75l147 667h75l-56 -254q86 82 171 82q65 0 104.5 -31.5t39.5 -88.5q0 -20 -6 -40zM442 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E56" unicode="&#x1e56;" horiz-adv-x="587" 
d="M107 0h-83l147 667h248q84 0 134.5 -50.5t50.5 -122.5q0 -40 -14 -78.5t-42.5 -73.5t-80 -56t-118.5 -21h-184zM181 339h175q76 0 118.5 43t42.5 106q0 44 -31.5 74.5t-79.5 30.5h-168zM470 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5
q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E57" unicode="&#x1e57;" 
d="M281 -12q-56 0 -99.5 23.5t-68.5 64.5l-58 -260h-75l148 667h75l-16 -69q66 81 160 81q84 0 135.5 -53t51.5 -146q0 -123 -70.5 -215.5t-182.5 -92.5zM271 55q80 0 132 68t52 159q0 67 -37 106.5t-98 39.5q-44 0 -83 -23.5t-65 -59.5l-47 -212q19 -35 57.5 -56.5
t88.5 -21.5zM424 607q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E60" unicode="&#x1e60;" horiz-adv-x="586" 
d="M286 -12q-88 0 -161.5 33.5t-108.5 85.5l57 60q35 -49 93 -77t127 -28q72 0 109.5 38t37.5 86q0 36 -34 62.5t-83 45t-97.5 40t-82.5 59t-34 90.5q0 79 67.5 136.5t168.5 57.5q77 0 141.5 -28t99.5 -77l-57 -58q-32 44 -84 67t-109 23t-97.5 -33t-40.5 -77
q0 -33 34 -57.5t82.5 -43.5t97.5 -41t83 -61t34 -92q0 -85 -65.5 -148t-177.5 -63zM460 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E61" unicode="&#x1e61;" horiz-adv-x="465" 
d="M207 -12q-136 0 -204 85l48 52q21 -31 65 -53.5t95 -22.5q50 0 80.5 25t30.5 60q0 25 -26 44t-63.5 32.5t-75 28.5t-63.5 42.5t-26 64.5q0 63 49 106t134 43q60 0 109.5 -22.5t74.5 -56.5l-43 -49q-17 27 -58.5 47t-86.5 20q-47 0 -75.5 -21t-28.5 -53q0 -24 26 -41.5
t63.5 -31t75 -29t63.5 -44t26 -67.5q0 -66 -51.5 -112.5t-138.5 -46.5zM358 607q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E6A" unicode="&#x1e6a;" horiz-adv-x="570" 
d="M272 0h-83l131 593h-211l16 74h506l-16 -74h-212zM449 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E6B" unicode="&#x1e6b;" horiz-adv-x="294" 
d="M166 -12q-52 0 -81 22t-29 66q0 13 3 28l70 314h-80l15 65h80l29 132h75l-29 -132h98l-15 -65h-98l-66 -298q-2 -12 -2 -20q0 -45 48 -45q28 0 46 16l9 -60q-30 -23 -73 -23zM293 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5
q18 0 30 -12t12 -30z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="883" 
d="M624 0h-90l-28 538l-266 -538h-90l-43 667h90l24 -556l276 556h70l29 -556l271 556h94zM601 723h-57l-137 144h78z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="734" 
d="M514 0h-75l-40 388l-212 -388h-75l-47 483h75l32 -385l212 385h66l41 -385l202 385h82zM490 556h-57l-137 144h78z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="883" 
d="M743 867l-201 -144h-59l180 144h80zM624 0h-90l-28 538l-266 -538h-90l-43 667h90l24 -556l276 556h70l29 -556l271 556h94z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="734" 
d="M635 700l-201 -144h-59l180 144h80zM514 0h-75l-40 388l-212 -388h-75l-47 483h75l32 -385l212 385h66l41 -385l202 385h82z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="883" 
d="M704 776q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM498 776q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM624 0h-90l-28 538l-266 -538h-90l-43 667h90
l24 -556l276 556h70l29 -556l271 556h94z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="734" 
d="M594 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM388 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM514 0h-75l-40 388l-212 -388h-75l-47 483h75
l32 -385l212 385h66l41 -385l202 385h82z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM291 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM245 -132
q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM400 759l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM317 589
l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="658" 
d="M551 682h-49l-60 79l-103 -79h-53l127 108h72zM595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM785 844l-201 -108h-59l180 108h80z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM483 556h-49
l-60 106l-103 -106h-53l127 144h72zM718 770l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="658" 
d="M551 682h-49l-60 79l-103 -79h-53l127 108h72zM595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM340 736h-57l-137 108h78z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM483 556h-49
l-60 106l-103 -106h-53l127 144h72zM272 626h-57l-137 144h78z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="658" 
d="M552 682h-49l-60 79l-103 -79h-53l127 108h72zM595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM488 803l-29 11q26 34 74 34q28 0 47.5 -11.5t19.5 -32.5q0 -26 -28 -48h-43q35 23 35 46q0 10 -10 16t-23 6q-26 0 -43 -21z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM483 556h-49
l-60 106l-103 -106h-53l127 144h72zM411 729l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="658" 
d="M549 678h-49l-60 53l-103 -53h-53l127 72h72zM595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM505 757q-39 0 -66.5 23t-52.5 23q-42 0 -61 -43h-46q27 67 114 67q39 0 66.5 -23t52.5 -23q40 0 62 43h46q-29 -67 -115 -67z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM475 523h-49
l-60 106l-103 -106h-53l127 144h72zM450 682q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM566 723h-49l-60 106l-103 -106h-53l127 144h72zM291 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM483 556h-49
l-60 106l-103 -106h-53l127 144h72zM245 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM625 841l-201 -98h-59l180 98h80zM611 756q-85 -75 -194 -75q-53 0 -94 20t-63 55l42 27q36 -61 123 -61q88 0 154 61z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM542 766
l-201 -144h-59l180 144h80zM531 606q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM507 748h-57l-137 98h78zM612 756q-85 -75 -194 -75q-53 0 -94 20t-63 55l42 27q36 -61 123 -61q88 0 154 61z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM439 622h-57
l-137 144h78zM532 611q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM406 786l-29 10q26 31 74 31q28 0 47.5 -10.5t19.5 -29.5q0 -24 -28 -43h-43q35 21 35 41q0 9 -10 15t-23 6q-26 0 -43 -20zM612 756q-85 -75 -194 -75q-53 0 -94 20t-63 55l42 27
q36 -61 123 -61q88 0 154 61z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM334 687
l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29zM533 611q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM608 735q-85 -54 -194 -54q-110 0 -157 54l42 20q36 -44 123 -44q90 0 154 44zM505 761q-39 0 -66.5 23t-52.5 23q-42 0 -61 -43h-46q27 67 114 67q39 0 66.5 -23t52.5 -23q40 0 62 43
h46q-29 -67 -115 -67z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM534 615
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72zM450 679q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="658" 
d="M595 0h-91l-26 148h-333l-91 -148h-99l415 667h103zM470 222l-63 359l-220 -359h283zM291 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30zM618 793q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72
q88 0 154 72z" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="575" 
d="M293 495q55 0 99.5 -23.5t68.5 -64.5l17 76h75l-107 -483h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22zM245 -132
q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30zM536 626q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="569" 
d="M461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM259 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="563" 
d="M276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5
t-63 -113.5zM248 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="569" 
d="M461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM368 759l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="563" 
d="M276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5
t-63 -113.5zM320 589l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="569" 
d="M461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM473 721q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="563" 
d="M276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5
t-63 -113.5zM424 554q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="569" 
d="M519 682h-49l-60 79l-103 -79h-53l127 108h72zM753 844l-201 -108h-59l180 108h80zM461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="563" 
d="M485 556h-49l-60 106l-103 -106h-53l127 144h72zM720 770l-201 -144h-59l180 144h80zM276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52
q-72 -57 -169 -57zM126 272h324q2 8 2 19q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5t-63 -113.5z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="569" 
d="M519 682h-49l-60 79l-103 -79h-53l127 108h72zM461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM307 736h-57l-137 108h78z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="563" 
d="M487 556h-49l-60 106l-103 -106h-53l127 144h72zM276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19
q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5t-63 -113.5zM276 626h-57l-137 144h78z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="569" 
d="M519 682h-49l-60 79l-103 -79h-53l127 108h72zM461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM455 803l-29 11q26 34 74 34q28 0 47.5 -11.5t19.5 -32.5q0 -26 -28 -48h-43q35 23 35 46q0 10 -10 16t-23 6q-26 0 -43 -21z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="563" 
d="M486 556h-49l-60 106l-103 -106h-53l127 144h72zM276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19
q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5t-63 -113.5zM414 729l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="569" 
d="M523 678h-49l-60 53l-103 -53h-53l127 72h72zM461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM473 757q-39 0 -66.5 23t-52.5 23q-42 0 -61 -43h-46q27 67 114 67q39 0 66.5 -23t52.5 -23q40 0 62 43h46q-29 -67 -115 -67z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="563" 
d="M480 523h-49l-60 106l-103 -106h-53l127 144h72zM276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19
q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5t-63 -113.5zM453 682q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="569" 
d="M534 723h-49l-60 106l-103 -106h-53l127 144h72zM461 0h-437l147 667h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-51 -230h355zM259 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="563" 
d="M487 556h-49l-60 106l-103 -106h-53l127 144h72zM276 -12q-109 0 -172.5 58t-63.5 156q0 121 79 207t193 86q94 0 151 -59t57 -152q0 -30 -8 -67h-394q0 -2 -0.5 -10.5t-0.5 -12.5q0 -60 43 -102t121 -42q80 0 139 47l25 -52q-72 -57 -169 -57zM126 272h324q2 8 2 19
q0 62 -38.5 102t-108.5 40q-65 0 -116 -47.5t-63 -113.5zM248 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="239" 
d="M107 0h-83l147 667h83zM191 759l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="225" 
d="M96 0h-75l107 483h75zM148 589l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="239" 
d="M107 0h-83l147 667h83zM82 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="225" 
d="M186 552q-20 0 -33 12.5t-13 31.5q0 25 18 41t39 16q19 0 32.5 -13t13.5 -31q0 -25 -18 -41t-39 -16zM96 0h-75l107 483h75zM77 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z
M346 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" 
d="M261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5zM249 -132
q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z
M456 759l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" 
d="M261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5zM320 589l-29 15
q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="765" 
d="M606 682h-49l-60 79l-103 -79h-53l127 108h72zM839 844l-201 -108h-59l180 108h80zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228
q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" 
d="M487 556h-49l-60 106l-103 -106h-53l127 144h72zM722 770l-201 -144h-59l180 144h80zM261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5
t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="765" 
d="M605 682h-49l-60 79l-103 -79h-53l127 108h72zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5
q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59zM393 736h-57l-137 108h78z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" 
d="M488 556h-49l-60 106l-103 -106h-53l127 144h72zM261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5
t-54 -155.5q0 -69 38 -109.5t106 -40.5zM277 626h-57l-137 144h78z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="765" 
d="M606 682h-49l-60 79l-103 -79h-53l127 108h72zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5
q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59zM541 803l-29 11q26 34 74 34q28 0 47.5 -11.5t19.5 -32.5q0 -26 -28 -48h-43q35 23 35 46q0 10 -10 16t-23 6q-26 0 -43 -21z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" 
d="M487 556h-49l-60 106l-103 -106h-53l127 144h72zM261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5
t-54 -155.5q0 -69 38 -109.5t106 -40.5zM415 729l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="765" 
d="M601 678h-49l-60 53l-103 -53h-53l127 72h72zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5
q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59zM559 757q-39 0 -66.5 23t-52.5 23q-42 0 -61 -43h-46q27 67 114 67q39 0 66.5 -23t52.5 -23q40 0 62 43h46q-29 -67 -115 -67z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" 
d="M480 523h-49l-60 106l-103 -106h-53l127 144h72zM261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5
t-54 -155.5q0 -69 38 -109.5t106 -40.5zM455 682q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="765" 
d="M620 723h-49l-60 106l-103 -106h-53l127 144h72zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q133 0 220.5 -78.5t87.5 -212.5q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5
q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59zM346 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" 
d="M486 556h-49l-60 106l-103 -106h-53l127 144h72zM261 -12q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q102 0 161.5 -59t59.5 -156q0 -117 -77.5 -204.5t-191.5 -87.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5
t-54 -155.5q0 -69 38 -109.5t106 -40.5zM249 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="765" 
d="M689 867l-201 -144h-59l180 144h80zM368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q160 0 247 -107q50 29 65 72h-7q-14 0 -24 10.5t-10 25.5q0 19 14.5 33.5t33.5 14.5q18 0 30 -13t12 -35q0 -38 -27.5 -76.5t-66.5 -60.5q41 -68 41 -155
q0 -168 -109.5 -283.5t-265.5 -115.5zM373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" 
d="M555 700l-201 -144h-59l180 144h80zM575 546q0 -36 -24.5 -72.5t-61.5 -59.5q41 -55 41 -134q0 -117 -77.5 -204.5t-191.5 -87.5q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q96 0 156 -54q47 29 60 68h-6q-14 0 -24 10t-10 26q0 20 14 34t33 14t31 -12.5
t12 -34.5zM263 55q80 0 134 67.5t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q160 0 247 -107q50 29 65 72h-7q-14 0 -24 10.5t-10 25.5q0 19 14.5 33.5t33.5 14.5q18 0 30 -13t12 -35q0 -38 -27.5 -76.5t-66.5 -60.5q41 -68 41 -155q0 -168 -109.5 -283.5t-265.5 -115.5z
M373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59zM546 723h-57l-137 144h78z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" 
d="M575 546q0 -36 -24.5 -72.5t-61.5 -59.5q41 -55 41 -134q0 -117 -77.5 -204.5t-191.5 -87.5q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q96 0 156 -54q47 29 60 68h-6q-14 0 -24 10t-10 26q0 20 14 34t33 14t31 -12.5t12 -34.5zM263 55q80 0 134 67.5
t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5zM413 556h-57l-137 144h78z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q160 0 247 -107q50 29 65 72h-7q-14 0 -24 10.5t-10 25.5q0 19 14.5 33.5t33.5 14.5q18 0 30 -13t12 -35q0 -38 -27.5 -76.5t-66.5 -60.5q41 -68 41 -155q0 -168 -109.5 -283.5t-265.5 -115.5z
M373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59zM455 759l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" 
d="M575 546q0 -36 -24.5 -72.5t-61.5 -59.5q41 -55 41 -134q0 -117 -77.5 -204.5t-191.5 -87.5q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q96 0 156 -54q47 29 60 68h-6q-14 0 -24 10t-10 26q0 20 14 34t33 14t31 -12.5t12 -34.5zM263 55q80 0 134 67.5
t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5zM321 589l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q160 0 247 -107q50 29 65 72h-7q-14 0 -24 10.5t-10 25.5q0 19 14.5 33.5t33.5 14.5q18 0 30 -13t12 -35q0 -38 -27.5 -76.5t-66.5 -60.5q41 -68 41 -155q0 -168 -109.5 -283.5t-265.5 -115.5z
M373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59zM560 721q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5
q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" 
d="M575 546q0 -36 -24.5 -72.5t-61.5 -59.5q41 -55 41 -134q0 -117 -77.5 -204.5t-191.5 -87.5q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q96 0 156 -54q47 29 60 68h-6q-14 0 -24 10t-10 26q0 20 14 34t33 14t31 -12.5t12 -34.5zM263 55q80 0 134 67.5
t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5zM410 554q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134
z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="765" 
d="M368 -12q-133 0 -220.5 78.5t-87.5 213.5q0 167 109.5 282.5t265.5 115.5q160 0 247 -107q50 29 65 72h-7q-14 0 -24 10.5t-10 25.5q0 19 14.5 33.5t33.5 14.5q18 0 30 -13t12 -35q0 -38 -27.5 -76.5t-66.5 -60.5q41 -68 41 -155q0 -168 -109.5 -283.5t-265.5 -115.5z
M373 62q115 0 198.5 92t83.5 228q0 103 -64 162.5t-161 59.5q-117 0 -199.5 -91.5t-82.5 -228.5q0 -104 64 -163t161 -59zM346 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" 
d="M575 546q0 -36 -24.5 -72.5t-61.5 -59.5q41 -55 41 -134q0 -117 -77.5 -204.5t-191.5 -87.5q-102 0 -161.5 58.5t-59.5 155.5q0 117 77.5 205t191.5 88q96 0 156 -54q47 29 60 68h-6q-14 0 -24 10t-10 26q0 20 14 34t33 14t31 -12.5t12 -34.5zM263 55q80 0 134 67.5
t54 155.5q0 69 -38 109.5t-106 40.5q-80 0 -134 -67.5t-54 -155.5q0 -69 38 -109.5t106 -40.5zM249 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="700" 
d="M336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-30 -133 -97 -202.5t-193 -69.5zM313 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30
q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="551" 
d="M128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78q73 0 149 76l77 352h75l-107 -483h-75l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40zM238 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="700" 
d="M336 -12q-123 0 -191.5 60t-68.5 162q0 19 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-89 -407q-30 -133 -97 -202.5t-193 -69.5zM422 759l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64
h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" horiz-adv-x="551" 
d="M128 483h75l-70 -316q-5 -25 -5 -34q0 -78 100 -78q73 0 149 76l77 352h75l-107 -483h-75l16 70q-86 -82 -170 -82q-66 0 -105 31.5t-39 88.5q0 15 5 40zM311 589l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8
q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="700" 
d="M657 867l-201 -144h-59l180 144h80zM843 744q0 -50 -41.5 -94.5t-105.5 -68.5l-70 -321q-30 -133 -97 -202.5t-193 -69.5q-123 0 -191.5 60t-68.5 162q0 24 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83
l-10 -44q74 35 88 84h-6q-14 0 -24 10t-10 26q0 20 14 34t33 14t31 -12.5t12 -34.5z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" horiz-adv-x="551" 
d="M545 700l-201 -144h-59l180 144h80zM641 551q0 -46 -36.5 -87.5t-95.5 -68.5l-87 -395h-75l16 69q-85 -81 -170 -81q-66 0 -105.5 31t-39.5 88q0 19 6 41l74 335h75l-71 -317q-4 -18 -4 -33q0 -78 100 -78q76 0 148 76l78 352h75l-10 -45q60 34 72 77h-6
q-14 0 -24.5 10.5t-10.5 25.5q0 19 14.5 33t33.5 14t31 -12.5t12 -34.5z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="700" 
d="M513 723h-57l-137 144h78zM843 744q0 -50 -41.5 -94.5t-105.5 -68.5l-70 -321q-30 -133 -97 -202.5t-193 -69.5q-123 0 -191.5 60t-68.5 162q0 24 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-10 -44
q74 35 88 84h-6q-14 0 -24 10t-10 26q0 20 14 34t33 14t31 -12.5t12 -34.5z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" horiz-adv-x="551" 
d="M402 556h-57l-137 144h78zM641 551q0 -46 -36.5 -87.5t-95.5 -68.5l-87 -395h-75l16 69q-85 -81 -170 -81q-66 0 -105.5 31t-39.5 88q0 19 6 41l74 335h75l-71 -317q-4 -18 -4 -33q0 -78 100 -78q76 0 148 76l78 352h75l-10 -45q60 34 72 77h-6q-14 0 -24.5 10.5
t-10.5 25.5q0 19 14.5 33t33.5 14t31 -12.5t12 -34.5z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="700" 
d="M422 759l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29zM843 744q0 -50 -41.5 -94.5t-105.5 -68.5l-70 -321q-30 -133 -97 -202.5t-193 -69.5q-123 0 -191.5 60t-68.5 162q0 24 5 49l90 408h83l-89 -406
q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-10 -44q74 35 88 84h-6q-14 0 -24 10t-10 26q0 20 14 34t33 14t31 -12.5t12 -34.5z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" horiz-adv-x="551" 
d="M311 589l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29zM641 551q0 -46 -36.5 -87.5t-95.5 -68.5l-87 -395h-75l16 69q-85 -81 -170 -81q-66 0 -105.5 31t-39.5 88q0 19 6 41l74 335h75l-71 -317
q-4 -18 -4 -33q0 -78 100 -78q76 0 148 76l78 352h75l-10 -45q60 34 72 77h-6q-14 0 -24.5 10.5t-10.5 25.5q0 19 14.5 33t33.5 14t31 -12.5t12 -34.5z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="700" 
d="M843 744q0 -50 -41.5 -94.5t-105.5 -68.5l-70 -321q-30 -133 -97 -202.5t-193 -69.5q-123 0 -191.5 60t-68.5 162q0 24 5 49l90 408h83l-89 -406q-5 -20 -5 -42q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-10 -44q74 35 88 84h-6
q-14 0 -24 10t-10 26q0 20 14 34t33 14t31 -12.5t12 -34.5zM527 721q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" horiz-adv-x="551" 
d="M641 551q0 -46 -36.5 -87.5t-95.5 -68.5l-87 -395h-75l16 69q-85 -81 -170 -81q-66 0 -105.5 31t-39.5 88q0 19 6 41l74 335h75l-71 -317q-4 -18 -4 -33q0 -78 100 -78q76 0 148 76l78 352h75l-10 -45q60 34 72 77h-6q-14 0 -24.5 10.5t-10.5 25.5q0 19 14.5 33t33.5 14
t31 -12.5t12 -34.5zM415 554q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="700" 
d="M313 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30zM843 744q0 -50 -41.5 -94.5t-105.5 -68.5l-70 -321q-30 -133 -97 -202.5t-193 -69.5q-123 0 -191.5 60t-68.5 162q0 24 5 49l90 408h83l-89 -406q-5 -20 -5 -42
q0 -70 46.5 -113.5t129.5 -43.5q88 0 136.5 50.5t69.5 148.5l90 406h83l-10 -44q74 35 88 84h-6q-14 0 -24 10t-10 26q0 20 14 34t33 14t31 -12.5t12 -34.5z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" horiz-adv-x="551" 
d="M238 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30zM641 551q0 -46 -36.5 -87.5t-95.5 -68.5l-87 -395h-75l16 69q-85 -81 -170 -81q-66 0 -105.5 31t-39.5 88q0 19 6 41l74 335h75l-71 -317q-4 -18 -4 -33
q0 -78 100 -78q76 0 148 76l78 352h75l-10 -45q60 34 72 77h-6q-14 0 -24.5 10.5t-10.5 25.5q0 19 14.5 33t33.5 14t31 -12.5t12 -34.5z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="626" 
d="M301 0h-83l62 282l-178 385h91l139 -310l275 310h103l-347 -385zM476 723h-57l-137 144h78z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="490" 
d="M-46 -185l26 67q18 -8 47 -8q40 0 72 48l49 75l-92 486h79l72 -396l249 396h84l-372 -581q-63 -98 -151 -98q-36 0 -63 11zM369 556h-57l-137 144h78z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="51" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="593" 
d="M555 209h-533l14 66h533z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="833" 
d="M795 209h-773l14 66h773z" />
    <glyph glyph-name="afii00208" unicode="&#x2015;" horiz-adv-x="593" 
d="M555 209h-533l14 66h533z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="230" 
d="M108 515q0 46 34 92t82 70l31 -33q-67 -34 -84 -84q1 1 8 1q18 0 30.5 -12.5t12.5 -32.5q0 -24 -18.5 -41.5t-42.5 -17.5q-22 0 -37.5 15t-15.5 43z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="230" 
d="M256 619q0 -46 -34 -92t-82 -71l-30 33q67 34 84 84h-8q-18 0 -30.5 12.5t-12.5 32.5q0 24 18 41.5t42 17.5q23 0 38 -15.5t15 -42.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="230" 
d="M128 41q0 -45 -33.5 -91t-81.5 -71l-31 33q67 34 84 84h-8q-18 0 -30.5 12t-12.5 32q0 24 18 42t42 18q23 0 38 -15.5t15 -43.5z" />
    <glyph glyph-name="quotereversed" unicode="&#x201b;" horiz-adv-x="230" 
d="M141 573q0 51 22.5 77.5t51.5 26.5q23 0 37 -14t14 -34q0 -25 -17 -40.5t-40 -15.5q-7 0 -23 4q-3 -24 9.5 -53t33.5 -49l-36 -24q-52 51 -52 122z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="390" 
d="M275 515q0 46 34 92t82 70l30 -33q-65 -33 -84 -84q2 1 9 1q17 0 29.5 -12.5t12.5 -32.5q0 -24 -18 -41.5t-42 -17.5q-23 0 -38 15t-15 43zM116 515q0 46 34 92t82 70l30 -33q-67 -34 -84 -84q1 1 8 1q18 0 30.5 -12.5t12.5 -32.5q0 -24 -18 -41.5t-42 -17.5
q-23 0 -38 15t-15 43z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="390" 
d="M256 619q0 -46 -34 -92t-82 -71l-30 33q67 34 84 84h-8q-18 0 -30.5 12.5t-12.5 32.5q0 24 18 41.5t42 17.5q23 0 38 -15.5t15 -42.5zM416 619q0 -46 -34 -92t-82 -71l-30 33q65 33 84 84h-9q-17 0 -29.5 12.5t-12.5 32.5q0 24 18 41.5t42 17.5q23 0 38 -15.5t15 -42.5z
" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="390" 
d="M128 41q0 -45 -33.5 -91t-81.5 -71l-31 33q67 34 84 84h-8q-18 0 -30.5 12t-12.5 32q0 24 18 42t42 18q23 0 38 -15.5t15 -43.5zM288 41q0 -45 -34 -91t-82 -71l-31 33q29 14 52.5 37t31.5 47h-8q-18 0 -30 12t-12 32q0 24 18 42t42 18q22 0 37.5 -15.5t15.5 -43.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="263" 
d="M304 531l-88 4l-43 -208h-48l51 208l-90 -4l9 43l88 -3l20 106h48l-28 -106l90 3z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="263" 
d="M313 574l-9 -43l-88 4l-48 -213l90 4l-9 -43l-88 3l-21 -106h-48l28 106l-89 -3l9 43l88 -4l47 213l-89 -4l9 43l88 -3l20 106h48l-28 -106z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="358" 
d="M288 253q0 -48 -36.5 -83t-84.5 -35q-41 0 -69 28t-28 69q0 48 36.5 83t84.5 35q41 0 69 -28t28 -69z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="691" 
d="M66 -10q-22 0 -36.5 14.5t-14.5 35.5q0 25 18 42.5t43 17.5q21 0 35.5 -15t14.5 -36q0 -24 -18 -41.5t-42 -17.5zM296 -10q-21 0 -35.5 14.5t-14.5 35.5q0 25 17.5 42.5t42.5 17.5q21 0 35.5 -15t14.5 -36q0 -25 -17.5 -42t-42.5 -17zM526 -10q-21 0 -35.5 14.5
t-14.5 35.5q0 25 17.5 42.5t42.5 17.5q21 0 35.5 -15t14.5 -36q0 -25 -17.5 -42t-42.5 -17z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1083" 
d="M128 0h-54l573 667h55zM505 -12q-63 0 -106.5 37.5t-43.5 97.5q0 85 53 139.5t125 54.5q63 0 106.5 -37.5t43.5 -97.5q0 -85 -53 -139.5t-125 -54.5zM506 38q47 0 82 40t35 99q0 38 -26 63.5t-66 25.5q-47 0 -81.5 -39.5t-34.5 -98.5q0 -38 25.5 -64t65.5 -26zM236 348
q-63 0 -106 37.5t-43 97.5q0 85 52.5 139.5t125.5 54.5q63 0 106 -37.5t43 -97.5q0 -85 -52.5 -139.5t-125.5 -54.5zM238 398q47 0 82 40t35 99q0 38 -26 63.5t-66 25.5q-47 0 -81.5 -39.5t-34.5 -98.5q0 -38 25.5 -64t65.5 -26zM857 -12q-63 0 -106.5 37.5t-43.5 97.5
q0 85 53 139.5t125 54.5q63 0 106.5 -37.5t43.5 -97.5q0 -85 -53 -139.5t-125 -54.5zM858 38q47 0 82 40t35 99q0 38 -25.5 63.5t-65.5 25.5q-48 0 -82.5 -39.5t-34.5 -98.5q0 -38 25.5 -64t65.5 -26z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="295" 
d="M220 63h-71l-120 180l200 177h79l-204 -182z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="295" 
d="M77 420h71l120 -180l-200 -177h-79l204 182z" />
    <glyph glyph-name="uni203E" unicode="&#x203e;" horiz-adv-x="363" 
d="M435 572h-362l10 48h362z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="138" 
d="M403 667l-574 -667h-55l573 667h56z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="388" 
d="M435 671q0 -37 -10.5 -78.5t-32 -83t-61 -68.5t-90.5 -27q-68 0 -104.5 43t-36.5 113q0 37 10.5 78.5t32 83t61 68.5t90.5 27q68 0 104.5 -43t36.5 -113zM374 673q0 105 -82 105q-46 0 -77.5 -39t-42.5 -84t-11 -87q0 -105 82 -105q35 0 61.5 22t41 56t21.5 68t7 64z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="388" 
d="M404 521h-58l-22 -100h-59l22 100h-196l9 44l229 256h83l-55 -251h57zM297 570l43 194l-178 -194h135z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="388" 
d="M404 566q0 -65 -45 -108.5t-115 -43.5q-52 0 -92.5 19t-60.5 53l43 36q35 -59 109 -59q44 0 72 26.5t28 66.5q0 34 -25 53t-62 19q-50 0 -90 -37l-40 16l48 214h261l-12 -49h-200l-28 -130q37 32 88 32q49 0 85 -30t36 -78z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="388" 
d="M409 564q0 -61 -42.5 -105.5t-114.5 -44.5t-110.5 40t-38.5 107q0 108 59 187t145 79t129 -49l-37 -41q-29 41 -93 41q-61 0 -100 -58.5t-39 -100.5v-4q54 61 116 61q54 0 90 -29.5t36 -82.5zM348 556q0 34 -24.5 53t-64.5 19q-48 0 -96 -56q-2 -5 -2 -21q0 -40 25 -64
t66 -24q44 0 70 28t26 65z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="362" 
d="M432 779l-245 -358h-68l243 351h-223l12 49h290z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="388" 
d="M400 526q0 -49 -42 -80.5t-110 -31.5q-66 0 -111.5 28t-45.5 78q0 42 35.5 74t90.5 38q-75 27 -75 86q0 52 43 80.5t101 28.5q59 0 105 -26t46 -73q0 -41 -29.5 -70.5t-87.5 -36.5q34 -13 57 -37.5t23 -57.5zM376 717q0 30 -25 46t-63 16q-37 0 -62.5 -18.5t-25.5 -48.5
q0 -27 25.5 -44t48.5 -22q39 3 70.5 20t31.5 51zM341 531q0 24 -22 44.5t-55 29.5q-50 -4 -81 -25.5t-31 -53.5q0 -28 28 -46.5t65 -18.5q39 0 67.5 20t28.5 50z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="388" 
d="M127 677q0 61 42.5 106t114.5 45t110.5 -40t38.5 -107q0 -109 -58.5 -187.5t-145.5 -78.5q-88 0 -128 49l36 41q28 -41 93 -41q43 0 77 32.5t48 68t14 58.5v4q-52 -61 -116 -61q-54 0 -90 29t-36 82zM188 685q0 -34 24.5 -52.5t64.5 -18.5q51 0 96 55q2 5 2 22
q0 40 -24.5 64t-65.5 24q-44 0 -70.5 -28.5t-26.5 -65.5z" />
    <glyph glyph-name="parenleftsuperior" unicode="&#x207d;" horiz-adv-x="161" 
d="M179 372l-39 -16q-42 74 -42 177q0 194 159 353l32 -16q-133 -179 -133 -359q0 -78 23 -139z" />
    <glyph glyph-name="parenrightsuperior" unicode="&#x207e;" horiz-adv-x="161" 
d="M160 870l39 16q42 -75 42 -177q0 -196 -159 -353l-32 16q133 179 133 359q0 78 -23 139z" />
    <glyph glyph-name="nsuperior" unicode="&#x207f;" horiz-adv-x="398" 
d="M352 326h-57l44 200q2 12 2 22q0 24 -17 38t-45 14q-46 0 -98 -50l-49 -224h-58l69 314h58l-9 -45q51 53 112 53q46 0 72.5 -22t26.5 -60q0 -9 -4 -29z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="388" 
d="M319 104q0 -37 -10.5 -78.5t-32 -83t-61 -68.5t-90.5 -27q-68 0 -104.5 43t-36.5 113q0 37 10.5 78.5t32 83t61 68.5t90.5 27q68 0 104.5 -43t36.5 -113zM258 106q0 105 -82 105q-46 0 -77.5 -39t-42.5 -84t-11 -87q0 -105 82 -105q35 0 61.5 22t41 56t21.5 68t7 64z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="235" 
d="M89 -146h-60l69 318l-78 -66l-29 38l133 110h53z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="388" 
d="M255 -146h-291l11 49q155 92 218.5 143t63.5 101q0 31 -25 47.5t-60 16.5q-68 0 -112 -47l-28 40q54 56 142 56q59 0 101.5 -27.5t42.5 -81.5q0 -60 -63.5 -116t-189.5 -132h201z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="388" 
d="M282 -32q0 -47 -40.5 -84t-106.5 -37q-117 0 -160 73l40 34q40 -58 118 -58q41 0 64.5 22.5t23.5 56.5q0 27 -27 43.5t-72 16.5q-23 0 -30 -1l11 50q28 -2 61 -2q42 0 66.5 18t24.5 50q0 28 -27 44.5t-65 16.5q-58 0 -108 -43l-23 39q58 53 135 53q65 0 107 -26.5
t42 -75.5q0 -44 -31 -72t-80 -32q32 -6 54.5 -29t22.5 -57z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="388" 
d="M288 -46h-58l-22 -100h-59l22 100h-196l9 44l229 256h83l-55 -251h57zM181 3l43 194l-178 -194h135z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="388" 
d="M288 -1q0 -65 -45 -108.5t-115 -43.5q-52 0 -92.5 19t-60.5 53l43 36q35 -59 109 -59q44 0 72 26.5t28 66.5q0 34 -25 53t-62 19q-50 0 -90 -37l-40 16l48 214h261l-12 -49h-200l-28 -130q37 32 88 32q49 0 85 -30t36 -78z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="388" 
d="M293 -3q0 -61 -42.5 -105.5t-114.5 -44.5t-110.5 40t-38.5 107q0 108 59 187t145 79t129 -49l-37 -41q-29 41 -93 41q-61 0 -100 -58.5t-39 -100.5v-4q54 61 116 61q54 0 90 -29.5t36 -82.5zM232 -11q0 34 -24.5 53t-64.5 19q-48 0 -96 -56q-2 -5 -2 -21q0 -40 25 -64
t66 -24q44 0 70 28t26 65z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="362" 
d="M316 212l-245 -358h-68l243 351h-223l12 49h290z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="388" 
d="M284 -41q0 -49 -42 -80.5t-110 -31.5q-66 0 -111.5 28t-45.5 78q0 42 35.5 74t90.5 38q-75 27 -75 86q0 52 43 80.5t101 28.5q59 0 105 -26t46 -73q0 -41 -29.5 -70.5t-87.5 -36.5q34 -13 57 -37.5t23 -57.5zM260 150q0 30 -25 46t-63 16q-37 0 -62.5 -18.5t-25.5 -48.5
q0 -27 25.5 -44t48.5 -22q39 3 70.5 20t31.5 51zM225 -36q0 24 -22 44.5t-55 29.5q-50 -4 -81 -25.5t-31 -53.5q0 -28 28 -46.5t65 -18.5q39 0 67.5 20t28.5 50z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="388" 
d="M11 110q0 61 42.5 106t114.5 45t110.5 -40t38.5 -107q0 -109 -58.5 -187.5t-145.5 -78.5q-88 0 -128 49l36 41q28 -41 93 -41q43 0 77 32.5t48 68t14 58.5v4q-52 -61 -116 -61q-54 0 -90 29t-36 82zM72 118q0 -34 24.5 -52.5t64.5 -18.5q51 0 96 55q2 5 2 22
q0 40 -24.5 64t-65.5 24q-44 0 -70.5 -28.5t-26.5 -65.5z" />
    <glyph glyph-name="parenleftinferior" unicode="&#x208d;" horiz-adv-x="161" 
d="M54 -195l-39 -16q-42 73 -42 177q0 194 159 353l32 -16q-133 -179 -133 -359q0 -78 23 -139z" />
    <glyph glyph-name="parenrightinferior" unicode="&#x208e;" horiz-adv-x="161" 
d="M35 303l39 16q42 -75 42 -177q0 -196 -159 -353l-32 16q133 179 133 359q0 78 -23 139z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="680" 
d="M373 -12q-18 0 -52 4l-50 -92h-53l56 101q-31 8 -68 27l-71 -128h-53l85 154q-102 81 -102 226q0 88 31.5 163.5t83.5 126.5t120 79.5t140 28.5q32 0 70 -7l54 97h53l-61 -109q31 -10 66 -33l79 142h52l-95 -171q32 -32 54 -76l-79 -31l-18 29l-254 -457h17q98 0 179 76
l61 -46q-97 -104 -245 -104zM153 284q0 -94 56 -155l262 472q-18 3 -36 3q-112 0 -197 -90t-85 -230zM245 98q29 -20 67 -29l270 486q-29 24 -64 36z" />
    <glyph glyph-name="frenchfranc" unicode="&#x20a3;" horiz-adv-x="561" 
d="M117 0h-83l30 137h-78l11 48h77l107 482h437l-16 -74h-354l-48 -215h347l-16 -74h-348l-26 -119h202l-11 -48h-202z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="518" 
d="M38 328l10 46h73q-25 45 -25 88q0 86 71 150.5t172 64.5q73 0 131.5 -33t74.5 -87l-76 -32q-8 36 -44 59t-83 23q-64 0 -110 -42t-46 -110q0 -40 23 -81h187l-10 -46h-151q17 -34 17 -68v-2h118l-10 -46h-117q-13 -37 -41.5 -68.5t-58.5 -49.5q26 8 46 8q33 0 82.5 -20.5
t77.5 -20.5q53 0 91 40l21 -67q-54 -47 -117 -47q-46 0 -108 23t-101 23q-48 0 -115 -40l-16 62q60 25 106 67.5t59 89.5h-157l10 46h151q-2 32 -25 70h-110z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="729" 
d="M586 0h-80l-119 241h-217l-53 -241h-83l53 241h-78l11 48h77l21 95h-77l10 48h78l52 235h85l117 -235h215l52 235h83l-52 -235h76l-10 -48h-77l-21 -95h77l-11 -48h-76zM201 384l-21 -95h183l-47 95h-115zM407 384l47 -95h112l21 95h-180zM237 544l-25 -112h80zM478 241
l54 -108l24 108h-78z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="640" 
d="M117 0h-83l97 441h-78l12 50h77l39 176h259q74 0 124.5 -51t50.5 -122v-3h73l-11 -50h-68q-18 -75 -80 -125.5t-170 -50.5h-183zM366 339q60 0 100 28t54 74h-306l-23 -102h175zM248 593l-23 -102h302q-2 42 -33 72t-78 30h-168z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="1019" 
d="M761 -12q-136 0 -204 85l48 52q21 -31 65 -53.5t95 -22.5q50 0 80.5 25t30.5 60q0 25 -26 44t-63.5 32.5t-75 28.5t-63.5 42.5t-26 64.5q0 63 49 106t134 43q60 0 109.5 -22.5t74.5 -56.5l-43 -49q-17 27 -58.5 47t-86.5 20q-47 0 -75.5 -21t-28.5 -53q0 -24 26 -41.5
t63.5 -31t75 -29t63.5 -44t26 -67.5q0 -66 -51.5 -112.5t-138.5 -46.5zM508 0h-92l-112 265h-140l-57 -265h-83l147 667h245q76 0 132.5 -48t56.5 -126q0 -93 -60 -155t-157 -69zM354 338h3q75 0 117.5 42.5t42.5 105.5q0 46 -34.5 76.5t-78.5 30.5h-166l-57 -255h173z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="883" 
d="M624 0h-90l-13 241h-162l-119 -241h-90l-16 241h-115l11 48h101l-6 95h-74l10 48h61l-15 235h90l10 -235h173l117 235h70l12 -235h173l115 235h94l-119 -235h58l-9 -48h-73l-48 -95h100l-11 -48h-113zM209 384l4 -95h96l47 95h-147zM582 384l5 -95h96l46 95h-147z
M430 384l-47 -95h136l-5 95h-84zM215 241l6 -130l64 130h-70zM589 241l7 -130l63 130h-70zM506 538l-53 -106h58z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="820" 
d="M507 0h-288l85 387h73l-71 -321h201q67 0 104.5 37t54.5 114l73 333h73l-75 -338q-24 -108 -75.5 -160t-154.5 -52zM142 550h217q92 0 152 -40.5t60 -128.5q0 -29 -9 -65l-33 -154h-73l35 157q6 26 6 46q0 59 -37 89t-100 30h-159l-107 -484h-73z" />
    <glyph glyph-name="dong" unicode="&#x20ab;" horiz-adv-x="579" 
d="M395 -143h-362l10 48h362zM293 495q55 0 99.5 -23.5t68.5 -64.5l28 128h-168l8 48h171l19 84h75l-19 -84h53l-8 -48h-56l-118 -535h-75l15 69q-65 -81 -159 -81q-85 0 -136 53t-51 146q0 124 70.5 216t182.5 92zM303 428q-80 0 -132 -68t-52 -159q0 -67 37 -106.5
t98 -39.5q43 0 83 23.5t64 58.5l48 213q-19 34 -57.5 56t-88.5 22z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="695" 
d="M392 -12q-123 0 -207 67.5t-99 181.5h-57l12 52h43q0 42 10 90h-34l12 52h38q43 113 139.5 180t209.5 67q97 0 167 -42t105 -115l-79 -31q-58 114 -198 114q-78 0 -147 -46.5t-105 -126.5h315l-12 -52h-321q-12 -42 -12 -90h313l-12 -52h-297q14 -80 72.5 -127.5
t148.5 -47.5q98 0 179 76l61 -46q-97 -104 -245 -104z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="475" 
d="M208 504l-34 -148l164 94q35 20 52.5 32t33 31t15.5 40q0 30 -28.5 49.5t-66.5 19.5q-53 0 -88 -30.5t-48 -87.5zM354 394l-196 -113l-37 -164q-2 -16 -2 -24q0 -18 12.5 -30t36.5 -12q27 0 46 17l9 -57q-26 -23 -74 -23q-50 0 -79.5 21.5t-29.5 60.5q0 18 4 34l90 407
q18 79 72 122.5t142 43.5q68 0 116 -33t48 -84q0 -68 -117 -141q-11 -8 -33 -20z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="1113" 
d="M944 350q-68 0 -109 39.5t-41 102.5q0 78 53.5 133t129.5 55q68 0 109 -39.5t41 -103.5q0 -77 -53.5 -132t-129.5 -55zM946 398q54 0 87.5 41.5t33.5 98.5q0 41 -25 66.5t-66 25.5q-54 0 -88 -41.5t-34 -97.5q0 -42 25 -67.5t67 -25.5zM576 0h-80l-269 544l-120 -544h-83
l147 667h85l266 -534l118 534h83z" />
    <glyph glyph-name="published" unicode="&#x2117;" horiz-adv-x="778" 
d="M754 334q0 -142 -101.5 -243.5t-244.5 -101.5q-142 0 -243 101t-101 244t101 244t243 101q143 0 244.5 -101.5t101.5 -243.5zM723 334q0 129 -93 221.5t-222 92.5t-221 -92.5t-92 -221.5q0 -130 91.5 -222t221.5 -92q129 0 222 92.5t93 221.5zM591 433q0 -56 -41 -92
t-103 -36h-123l-37 -174h-37l89 405h130q52 0 87 -28t35 -75zM553 430q0 33 -24 52t-64 19h-97l-35 -162h120q45 0 72.5 26t27.5 65z" />
    <glyph glyph-name="servicemark" unicode="&#x2120;" horiz-adv-x="476" 
d="M484 447h-28l40 182l-115 -182h-8l-35 182l-40 -182h-28l48 220h42l29 -160l99 160h44zM247 511q0 -32 -23 -52t-62 -20q-69 0 -96 40l22 19q23 -33 72 -33q24 0 38 11t14 29q0 15 -18 26.5t-39 18.5t-39 21t-18 34q0 29 23.5 48.5t60.5 19.5q63 0 86 -35l-23 -17
q-20 28 -65 28q-22 0 -35 -11t-13 -26t18 -26t39.5 -17t39.5 -21t18 -37z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="454" 
d="M464 447h-28l40 182l-115 -182h-8l-35 182l-40 -182h-28l48 220h43l29 -160l99 160h43zM257 641h-62l-43 -194h-28l43 194h-62l5 26h152z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="765" 
d="M31 74h152q-117 85 -117 231q0 71 26.5 138t73 119.5t117 84t152.5 31.5q137 0 222.5 -77.5t85.5 -209.5q0 -113 -59.5 -193.5t-147.5 -123.5h122l-16 -74h-240l16 74q101 21 168 107t67 201q0 101 -60.5 161t-160.5 60q-123 0 -203.5 -94t-80.5 -225q0 -74 34 -127.5
t89 -82.5l-16 -74h-240z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="842" 
d="M829 324h-632q-4 0 -4 -5v-190q0 -15 9 -23q98 -103 240 -103q151 0 250 115h57q-54 -63 -135 -99.5t-174 -36.5q-161 0 -275.5 103t-114.5 248t114.5 248t275.5 103t275.5 -103t114.5 -248zM687 349v191q0 14 -10 24q-97 99 -236 99t-238 -102q-10 -10 -10 -24v-188
q0 -6 5 -6h485q4 0 4 6z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="807" 
d="M180 267h-60l69 318l-78 -66l-29 38l133 110h53zM683 667l-574 -667h-55l573 667h56zM732 114q0 -47 -40.5 -84t-106.5 -37q-117 0 -160 73l40 34q40 -58 118 -58q41 0 64.5 22.5t23.5 56.5q0 27 -27 43.5t-72 16.5q-23 0 -30 -1l11 50q28 -2 61 -2q42 0 66.5 18t24.5 50
q0 28 -27 44.5t-65 16.5q-58 0 -108 -43l-23 39q58 53 135 53q65 0 107 -26.5t42 -75.5q0 -44 -31 -72t-80 -32q32 -6 54.5 -29t22.5 -57z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="915" 
d="M346 267h-291l11 49q155 92 218.5 143t63.5 101q0 31 -25 47.5t-60 16.5q-68 0 -112 -47l-28 40q54 56 142 56q59 0 101.5 -27.5t42.5 -81.5q0 -60 -63.5 -116t-189.5 -132h201zM791 667l-574 -667h-55l573 667h56zM841 114q0 -47 -40.5 -84t-106.5 -37q-117 0 -160 73
l40 34q40 -58 118 -58q41 0 64.5 22.5t23.5 56.5q0 27 -27 43.5t-72 16.5q-23 0 -30 -1l11 50q28 -2 61 -2q42 0 66.5 18t24.5 50q0 28 -27 44.5t-65 16.5q-58 0 -108 -43l-23 39q58 53 135 53q65 0 107 -26.5t42 -75.5q0 -44 -31 -72t-80 -32q32 -6 54.5 -29t22.5 -57z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="807" 
d="M180 267h-60l69 318l-78 -66l-29 38l133 110h53zM734 105q0 -49 -42 -80.5t-110 -31.5q-66 0 -111.5 28t-45.5 78q0 42 35.5 74t90.5 38q-75 27 -75 86q0 52 43 80.5t101 28.5q59 0 105 -26t46 -73q0 -41 -29.5 -70.5t-87.5 -36.5q34 -13 57 -37.5t23 -57.5zM710 296
q0 30 -25 46t-63 16q-37 0 -62.5 -18.5t-25.5 -48.5q0 -27 25.5 -44t48.5 -22q39 3 70.5 20t31.5 51zM675 110q0 24 -22 44.5t-55 29.5q-50 -4 -81 -25.5t-31 -53.5q0 -28 28 -46.5t65 -18.5q39 0 67.5 20t28.5 50zM683 667l-574 -667h-55l573 667h56z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="915" 
d="M843 105q0 -49 -42 -80.5t-110 -31.5q-66 0 -111.5 28t-45.5 78q0 42 35.5 74t90.5 38q-75 27 -75 86q0 52 43 80.5t101 28.5q59 0 105 -26t46 -73q0 -41 -29.5 -70.5t-87.5 -36.5q34 -13 57 -37.5t23 -57.5zM819 296q0 30 -25 46t-63 16q-37 0 -62.5 -18.5t-25.5 -48.5
q0 -27 25.5 -44t48.5 -22q39 3 70.5 20t31.5 51zM784 110q0 24 -22 44.5t-55 29.5q-50 -4 -81 -25.5t-31 -53.5q0 -28 28 -46.5t65 -18.5q39 0 67.5 20t28.5 50zM791 667l-574 -667h-55l573 667h56zM373 381q0 -47 -40.5 -84t-106.5 -37q-117 0 -160 73l40 34
q40 -58 118 -58q41 0 64.5 22.5t23.5 56.5q0 27 -27 43.5t-72 16.5q-23 0 -30 -1l11 50q28 -2 61 -2q42 0 66.5 18t24.5 50q0 28 -27 44.5t-65 16.5q-58 0 -108 -43l-23 39q58 53 135 53q65 0 107 -26.5t42 -75.5q0 -44 -31 -72t-80 -32q32 -6 54.5 -29t22.5 -57z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="912" 
d="M840 105q0 -49 -42 -80.5t-110 -31.5q-66 0 -111.5 28t-45.5 78q0 42 35.5 74t90.5 38q-75 27 -75 86q0 52 43 80.5t101 28.5q59 0 105 -26t46 -73q0 -41 -29.5 -70.5t-87.5 -36.5q34 -13 57 -37.5t23 -57.5zM816 296q0 30 -25 46t-63 16q-37 0 -62.5 -18.5t-25.5 -48.5
q0 -27 25.5 -44t48.5 -22q39 3 70.5 20t31.5 51zM781 110q0 24 -22 44.5t-55 29.5q-50 -4 -81 -25.5t-31 -53.5q0 -28 28 -46.5t65 -18.5q39 0 67.5 20t28.5 50zM379 412q0 -65 -45 -108.5t-115 -43.5q-52 0 -92.5 19t-60.5 53l43 36q35 -59 109 -59q44 0 72 26.5t28 66.5
q0 34 -25 53t-62 19q-50 0 -90 -37l-40 16l48 214h261l-12 -49h-200l-28 -130q37 32 88 32q49 0 85 -30t36 -78zM788 667l-574 -667h-55l573 667h56z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="830" 
d="M758 105q0 -49 -42 -80.5t-110 -31.5q-66 0 -111.5 28t-45.5 78q0 42 35.5 74t90.5 38q-75 27 -75 86q0 52 43 80.5t101 28.5q59 0 105 -26t46 -73q0 -41 -29.5 -70.5t-87.5 -36.5q34 -13 57 -37.5t23 -57.5zM734 296q0 30 -25 46t-63 16q-37 0 -62.5 -18.5t-25.5 -48.5
q0 -27 25.5 -44t48.5 -22q39 3 70.5 20t31.5 51zM699 110q0 24 -22 44.5t-55 29.5q-50 -4 -81 -25.5t-31 -53.5q0 -28 28 -46.5t65 -18.5q39 0 67.5 20t28.5 50zM407 625l-245 -358h-68l243 351h-223l12 49h290zM706 667l-574 -667h-55l573 667h56z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="582" 
d="M558 211h-269l12 -130l-281 205l281 204l-12 -129h269v-150z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="582" 
d="M236 17v269l-130 -12l205 281l204 -281l-129 12v-269h-150z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="582" 
d="M604 286l-281 -205l12 130h-268v150h268l-12 129z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="582" 
d="M386 555v-269l130 12l-205 -281l-204 281l129 -12v269h150z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M264 -12q-100 0 -163 58t-63 148q0 119 74 202t177 83q53 0 98.5 -26.5t68.5 -80.5q-47 144 -220 272l53 57q115 -85 179 -188t64 -211q0 -132 -75.5 -223t-192.5 -91zM264 55q76 0 130 63t54 152q0 57 -38 99.5t-110 42.5q-75 0 -128.5 -61t-53.5 -150q0 -65 39 -105.5
t107 -40.5z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="658" 
d="M595 0h-640l415 667h103zM496 74l-89 507l-312 -507h401z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="707" 
d="M492 -90h-83l151 683h-259l-151 -683h-83l151 683h-109l16 74h643l-15 -74h-110z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="575" 
d="M231 593l166 -301l-297 -308h363l-16 -74h-467l16 74l301 313l-164 296l15 74h466l-15 -74h-368z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="499" 
d="M483 309h-440l11 53h441z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="138" 
d="M403 667l-574 -667h-55l573 667h56z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="230" 
d="M110 190q-21 0 -35.5 14.5t-14.5 35.5q0 25 17.5 42.5t42.5 17.5q21 0 35.5 -15t14.5 -36q0 -25 -17.5 -42t-42.5 -17z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="725" 
d="M373 0h-58l-58 327l-168 -61l-7 50l221 79l57 -317l365 589h61z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="656" 
d="M181 170q-63 0 -99.5 37t-36.5 99q0 77 48.5 135.5t126.5 58.5q50 0 87.5 -30t51.5 -81q70 111 154 111q63 0 99 -37t36 -99q0 -77 -48 -135.5t-125 -58.5q-51 0 -88 29.5t-52 80.5q-70 -110 -154 -110zM475 223q52 0 85 39.5t33 96.5q0 39 -24.5 64t-64.5 25
q-69 0 -131 -116q7 -52 33.5 -80.5t68.5 -28.5zM190 223q71 0 134 115q-7 52 -35 81t-70 29q-51 0 -84 -40t-33 -97q0 -39 24.5 -63.5t63.5 -24.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="348" 
d="M1 -100h-60l11 55h62q32 0 53.5 21.5t29.5 58.5l134 598q30 135 154 135h62l-13 -55h-61q-32 0 -54 -21.5t-30 -58.5l-133 -599q-30 -134 -155 -134z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="499" 
d="M21 211l13 59q40 -33 103 -33q47 0 111 29t121 29q62 0 106 -27l-14 -59q-40 34 -103 34q-47 0 -111.5 -29.5t-121.5 -29.5q-62 0 -104 27zM63 400l13 58q42 -33 103 -33q46 0 110.5 29.5t121.5 29.5q62 0 106 -27l-13 -59q-40 33 -104 33q-47 0 -111 -29t-121 -29
q-62 0 -105 27z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="499" 
d="M90 93h-57l102 118h-114l12 52h147l121 141h-237l12 53h270l101 117h58l-100 -117h112l-13 -53h-145l-122 -141h237l-12 -52h-270z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="499" 
d="M437 99l-393 218l12 53l490 219l-15 -66l-417 -183l336 -181zM416 0h-441l11 52h441z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="499" 
d="M485 317l-489 -218l15 66l417 181l-336 183l13 60l392 -219zM415 0h-441l11 52h441z" />
    <glyph glyph-name="triangleright" unicode="&#x22b2;" horiz-adv-x="587" 
d="M609 286l-535 -309v618z" />
    <glyph glyph-name="triangleleft" unicode="&#x22b3;" horiz-adv-x="587" 
d="M555 595v-618l-535 309z" />
    <glyph glyph-name="dotmath" unicode="&#x22c5;" horiz-adv-x="230" 
d="M110 190q-21 0 -35.5 14.5t-14.5 35.5q0 25 17.5 42.5t42.5 17.5q21 0 35.5 -15t14.5 -36q0 -25 -17.5 -42t-42.5 -17z" />
    <glyph glyph-name="uni2318" unicode="&#x2318;" horiz-adv-x="667" 
d="M206 153l17 75h-83q-34 0 -58.5 -25t-24.5 -59q0 -28 18 -46.5t46 -18.5q69 0 85 74zM269 439l14 65q2 7 2 20q0 29 -18 46.5t-48 17.5q-34 0 -58 -23.5t-24 -57.5q0 -31 19 -49.5t50 -18.5h63zM269 265h137l31 137h-137zM569 160q0 31 -19 49.5t-51 18.5h-62l-15 -65
q-2 -14 -2 -20q0 -30 18 -47t48 -17q34 0 58.5 23.5t24.5 57.5zM648 523q0 28 -17.5 46.5t-45.5 18.5q-33 0 -55.5 -20t-29.5 -54l-17 -75h82q34 0 58.5 25t24.5 59zM607 160q0 -48 -39 -83t-89 -35q-42 0 -70 28t-28 70q0 14 3 29l14 59h-137l-19 -83q-11 -46 -45 -74.5
t-80 -28.5q-41 0 -70 28t-29 69q0 49 38 87.5t87 38.5h87l31 137h-62q-45 0 -72.5 29t-27.5 76q0 48 38.5 83t88.5 35q42 0 70 -28t28 -70q0 -14 -3 -29l-14 -59h137l19 83q11 46 45 74.5t80 28.5q42 0 71 -28t29 -69q0 -50 -38 -88t-87 -38h-87l-31 -137h59
q46 0 74.5 -29.5t28.5 -75.5z" />
    <glyph glyph-name="blacksquare" unicode="&#x25a0;" horiz-adv-x="661" 
d="M638 0h-571v572h571v-572z" />
    <glyph glyph-name="triagup" unicode="&#x25b2;" horiz-adv-x="616" 
d="M637 29l-619 1l309 535z" />
    <glyph glyph-name="triagdn" unicode="&#x25bc;" horiz-adv-x="616" 
d="M637 510l-310 -535l-309 535h619z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="375" 
d="M144 0h-21l-84 334l231 333h21l85 -333zM142 45l197 293l-67 284l-196 -292z" />
    <glyph glyph-name="circlesolid" unicode="&#x25cf;" horiz-adv-x="695" 
d="M664 285q0 -123 -87 -210t-210 -87t-210 87t-87 210t87 210t210 87t210 -87t87 -210z" />
    <glyph glyph-name="blackstar" unicode="&#x2605;" horiz-adv-x="759" 
d="M779 421l-235 -171l90 -275l-235 170l-234 -170l89 275l-234 171h290l89 276l90 -276h290z" />
    <glyph glyph-name="ballotbox" unicode="&#x2610;" horiz-adv-x="661" 
d="M636 0h-571v572h571v-572zM579 52v467h-458v-467h458z" />
    <glyph glyph-name="boxchecked" unicode="&#x2611;" horiz-adv-x="661" 
d="M717 698l-69 -126h8v-572h-571v572h502l84 155zM599 52v428l-211 -389l-202 246l42 39l152 -188l178 331h-417v-467h458z" />
    <glyph glyph-name="diamond" unicode="&#x2666;" horiz-adv-x="501" 
d="M521 285l-251 -250l-250 250l250 251z" />
    <glyph glyph-name="checkmark" unicode="&#x2713;" horiz-adv-x="531" 
d="M551 698l-329 -607l-202 246l42 39l152 -188l291 539z" />
    <glyph glyph-name="perthousand.onum" unicode="&#xf628;" horiz-adv-x="878" 
d="M331 444q0 -62 -43 -108.5t-106 -46.5q-54 0 -87 31.5t-33 85.5q0 62 42.5 109t105.5 47q54 0 87.5 -32t33.5 -86zM581 550l-505 -550h-52l503 550h54zM278 441q0 33 -18.5 55t-51.5 22q-39 0 -66.5 -33t-27.5 -77q0 -33 18.5 -54t51.5 -21q40 0 67 32.5t27 75.5z
M542 144q0 -62 -43 -108.5t-106 -46.5q-54 0 -86.5 31.5t-32.5 85.5q0 63 42 109.5t105 46.5q54 0 87.5 -32t33.5 -86zM490 142q0 33 -18.5 54.5t-51.5 21.5q-40 0 -67.5 -33t-27.5 -77q0 -33 19 -54t52 -21q39 0 66.5 32.5t27.5 76.5zM829 144q0 -62 -43 -108.5t-106 -46.5
q-54 0 -87 31.5t-33 85.5q0 63 42 109.5t106 46.5q54 0 87.5 -32t33.5 -86zM776 142q0 33 -18.5 54.5t-51.5 21.5q-39 0 -66.5 -33t-27.5 -77q0 -33 18.5 -54t51.5 -21q39 0 66.5 32.5t27.5 76.5z" />
    <glyph glyph-name="uniF637" unicode="&#xf637;" horiz-adv-x="398" 
d="M352 326h-57l44 200q2 12 2 20q0 26 -17.5 40t-47.5 14q-45 0 -95 -50l-49 -224h-58l96 434h58l-36 -165q51 53 112 53q47 0 73 -21t26 -59q0 -11 -4 -31z" />
    <glyph glyph-name="zero.tzero" unicode="&#xf638;" horiz-adv-x="598" 
d="M271 -12q-99 0 -157 64l-44 -52h-67l81 96q-32 62 -32 152q0 76 22 151.5t61.5 137.5t100 101t130.5 39q98 0 155 -63l45 53h67l-81 -97q32 -62 32 -152q0 -103 -36.5 -200t-110 -163.5t-166.5 -66.5zM278 62q66 0 118 58.5t77.5 140t25.5 162.5q0 41 -9 75l-322 -382
q37 -54 110 -54zM136 243q0 -42 9 -75l322 382q-35 53 -109 53q-65 0 -117.5 -58.5t-78.5 -139.5t-26 -162z" />
    <glyph glyph-name="zero.tnum" unicode="&#xf639;" horiz-adv-x="598" 
d="M271 -12q-105 0 -162 69.5t-57 190.5q0 76 22 151.5t61.5 137.5t100 101t130.5 39q104 0 161 -69t57 -190q0 -103 -36.5 -200t-110 -163.5t-166.5 -66.5zM278 62q66 0 118 58.5t77.5 140t25.5 162.5q0 83 -34 131.5t-107 48.5q-65 0 -117.5 -58.5t-78.5 -139.5t-26 -162
q0 -82 35 -131.5t107 -49.5z" />
    <glyph glyph-name="two.tnum" unicode="&#xf63a;" horiz-adv-x="598" 
d="M473 0h-458l18 81q94 58 152.5 95.5t124 85.5t101 84t59.5 75.5t24 76.5q0 49 -40 77t-104 28q-101 0 -171 -65l-39 59q38 36 95.5 58t119.5 22q95 0 161 -45.5t66 -129.5q0 -54 -32.5 -110t-95.5 -111.5t-130 -102t-162 -104.5h326z" />
    <glyph glyph-name="three.tnum" unicode="&#xf63b;" horiz-adv-x="598" 
d="M284 -12q-86 0 -157 38.5t-98 100.5l60 47q25 -52 78.5 -82t115.5 -30q73 0 116 39.5t43 100.5q0 46 -39 74.5t-109 28.5q-53 0 -64 -1l17 76q11 -1 91 -1q68 0 114 30t46 88q0 46 -41.5 76t-110.5 30q-96 0 -169 -66l-35 57q85 83 210 83q103 0 166.5 -44.5t63.5 -123.5
q0 -71 -55 -118t-125 -53q53 -12 87.5 -49t34.5 -94q0 -87 -68 -147t-172 -60z" />
    <glyph glyph-name="four.tnum" unicode="&#xf63c;" horiz-adv-x="598" 
d="M397 0h-83l37 169h-317l17 75l374 423h119l-95 -425h94l-15 -73h-94zM366 242l78 348l-311 -348h233z" />
    <glyph glyph-name="five.tnum" unicode="&#xf63d;" horiz-adv-x="598" 
d="M281 -12q-174 0 -243 133l63 50q50 -109 184 -109q70 0 118.5 46t48.5 114q0 60 -42 93.5t-109 33.5q-73 0 -138 -48l-55 27l75 339h408l-16 -74h-325l-49 -220q56 45 140 45t141 -50t57 -138q0 -100 -73 -171t-185 -71z" />
    <glyph glyph-name="six.tnum" unicode="&#xf63e;" horiz-adv-x="598" 
d="M290 -12q-109 0 -168.5 64.5t-59.5 181.5q0 73 20.5 148.5t58.5 142.5t103.5 109.5t145.5 42.5q138 0 201 -100l-56 -56q-43 82 -150 82q-88 0 -144.5 -73.5t-82.5 -177.5q-5 -15 -5 -20q30 33 83 59t109 26q89 0 145.5 -47.5t56.5 -127.5q0 -104 -74.5 -179t-182.5 -75z
M291 62q72 0 120.5 50.5t48.5 118.5q0 54 -40.5 85.5t-107.5 31.5q-94 0 -168 -82q-2 -18 -2 -48q0 -70 39.5 -113t109.5 -43z" />
    <glyph glyph-name="seven.tnum" unicode="&#xf63f;" horiz-adv-x="598" 
d="M202 0h-96l397 593h-355l17 74h455l-13 -57z" />
    <glyph glyph-name="eight.tnum" unicode="&#xf640;" horiz-adv-x="598" 
d="M279 -12q-104 0 -172.5 44.5t-68.5 125.5q0 73 54.5 124.5t147.5 70.5q-48 19 -82 55.5t-34 85.5q0 88 72 135.5t165 47.5t160.5 -44t67.5 -120q0 -71 -52 -117.5t-137 -59.5q52 -24 88.5 -63.5t36.5 -94.5q0 -83 -72 -136.5t-174 -53.5zM328 378q77 5 124.5 37t47.5 85
q0 43 -42 72.5t-102 29.5q-62 0 -105.5 -31.5t-43.5 -84.5q0 -38 38.5 -68t82.5 -40zM282 62q63 0 111 34t48 90q0 44 -40 76t-88 45q-81 -5 -134 -41.5t-53 -92.5q0 -53 44.5 -82t111.5 -29z" />
    <glyph glyph-name="nine.tnum" unicode="&#xf641;" horiz-adv-x="598" 
d="M347 678q109 0 168.5 -64.5t59.5 -181.5q0 -73 -20.5 -148.5t-59 -142.5t-104 -109.5t-145.5 -42.5q-138 0 -201 100l56 56q43 -82 150 -82q47 0 86 22t66 59.5t45 79t30 89.5q4 13 6 21q-31 -33 -84 -59.5t-108 -26.5q-89 0 -146 48t-57 128q0 103 75 178.5t183 75.5z
M345 604q-72 0 -121 -50.5t-49 -118.5q0 -54 41.5 -86t107.5 -32q92 0 169 82q1 9 1 48q0 70 -39.5 113.5t-109.5 43.5z" />
    <glyph glyph-name="percent.onum" unicode="&#xf642;" horiz-adv-x="592" 
d="M331 444q0 -62 -43 -108.5t-106 -46.5q-54 0 -87 31.5t-33 85.5q0 62 42.5 109t105.5 47q54 0 87.5 -32t33.5 -86zM581 550l-505 -550h-52l503 550h54zM278 441q0 33 -18.5 55t-51.5 22q-39 0 -66.5 -33t-27.5 -77q0 -33 18.5 -54t51.5 -21q40 0 67 32.5t27 75.5z
M542 144q0 -62 -43 -108.5t-106 -46.5q-54 0 -86.5 31.5t-32.5 85.5q0 63 42 109.5t105 46.5q54 0 87.5 -32t33.5 -86zM490 142q0 33 -18.5 54.5t-51.5 21.5q-40 0 -67.5 -33t-27.5 -77q0 -33 19 -54t52 -21q39 0 66.5 32.5t27.5 76.5z" />
    <glyph glyph-name="zero.tonum" unicode="&#xf643;" horiz-adv-x="598" 
d="M567 325q0 -54 -18.5 -111.5t-53 -108.5t-91 -84t-123.5 -33q-116 0 -177.5 62.5t-61.5 173.5q0 74 31 149.5t99 132t157 56.5q115 0 176.5 -63t61.5 -174zM482 320q0 82 -39 125t-115 43q-64 0 -111.5 -45t-68 -102t-20.5 -112q0 -81 38.5 -124t114.5 -43q64 0 112 45
t68.5 102t20.5 111z" />
    <glyph glyph-name="one.tonum" unicode="&#xf644;" horiz-adv-x="598" 
d="M341 0h-83l97 440l-132 -113l-39 51l205 172h73z" />
    <glyph glyph-name="two.tonum" unicode="&#xf645;" horiz-adv-x="598" 
d="M455 0h-431l15 70q207 76 316 152t109 157q0 49 -38.5 79t-97.5 30q-106 0 -187 -75l-39 58q97 91 233 91q94 0 156 -45t62 -123q0 -51 -30 -99.5t-82.5 -88.5t-112 -72t-131.5 -60h274z" />
    <glyph glyph-name="three.tonum" unicode="&#xf646;" horiz-adv-x="598" 
d="M259 -127q-86 0 -157 38.5t-98 100.5l60 47q25 -52 78.5 -82t115.5 -30q73 0 116 39.5t43 100.5q0 46 -39 74.5t-109 28.5q-53 0 -64 -1l17 76q11 -1 91 -1q68 0 114 30t46 88q0 46 -41.5 76t-110.5 30q-96 0 -169 -66l-35 57q85 83 210 83q103 0 166.5 -44.5
t63.5 -123.5q0 -71 -55 -118t-125 -53q53 -12 87.5 -49t34.5 -94q0 -87 -68 -147t-172 -60z" />
    <glyph glyph-name="four.tonum" unicode="&#xf647;" horiz-adv-x="598" 
d="M502 55h-94l-37 -172h-83l37 172h-316l15 69l380 426h114l-93 -422h93zM342 128l76 345l-310 -345h234z" />
    <glyph glyph-name="five.tonum" unicode="&#xf648;" horiz-adv-x="598" 
d="M255 -129q-174 0 -243 133l63 50q50 -109 184 -109q70 0 118.5 46t48.5 114q0 60 -42 93.5t-109 33.5q-73 0 -138 -48l-55 27l75 339h408l-16 -74h-325l-49 -220q56 45 140 45t141 -50t57 -138q0 -100 -73 -171t-185 -71z" />
    <glyph glyph-name="six.tonum" unicode="&#xf649;" horiz-adv-x="598" 
d="M290 -12q-109 0 -168.5 64.5t-59.5 181.5q0 73 20.5 148.5t58.5 142.5t103.5 109.5t145.5 42.5q138 0 201 -100l-56 -56q-43 82 -150 82q-88 0 -144.5 -73.5t-82.5 -177.5q-5 -15 -5 -20q30 33 83 59t109 26q89 0 145.5 -47.5t56.5 -127.5q0 -104 -74.5 -179t-182.5 -75z
M291 62q72 0 120.5 50.5t48.5 118.5q0 54 -40.5 85.5t-107.5 31.5q-94 0 -168 -82q-2 -18 -2 -48q0 -70 39.5 -113t109.5 -43z" />
    <glyph glyph-name="seven.tonum" unicode="&#xf64a;" horiz-adv-x="598" 
d="M176 -117h-96l397 593h-355l17 74h455l-13 -57z" />
    <glyph glyph-name="eight.tonum" unicode="&#xf64b;" horiz-adv-x="598" 
d="M279 -12q-104 0 -172.5 44.5t-68.5 125.5q0 73 54.5 124.5t147.5 70.5q-48 19 -82 55.5t-34 85.5q0 88 72 135.5t165 47.5t160.5 -44t67.5 -120q0 -71 -52 -117.5t-137 -59.5q52 -24 88.5 -63.5t36.5 -94.5q0 -83 -72 -136.5t-174 -53.5zM328 378q77 5 124.5 37t47.5 85
q0 43 -42 72.5t-102 29.5q-62 0 -105.5 -31.5t-43.5 -84.5q0 -38 38.5 -68t82.5 -40zM282 62q63 0 111 34t48 90q0 44 -40 76t-88 45q-81 -5 -134 -41.5t-53 -92.5q0 -53 44.5 -82t111.5 -29z" />
    <glyph glyph-name="nine.tonum" unicode="&#xf64c;" horiz-adv-x="598" 
d="M321 562q109 0 168.5 -64.5t59.5 -181.5q0 -73 -20.5 -148.5t-59 -142.5t-104 -109.5t-145.5 -42.5q-138 0 -201 100l56 56q43 -82 150 -82q47 0 86 22t66 59.5t45 79t30 89.5q4 13 6 21q-31 -33 -84 -59.5t-108 -26.5q-89 0 -146 48t-57 128q0 103 75 178.5t183 75.5z
M319 488q-72 0 -121 -50.5t-49 -118.5q0 -54 41.5 -86t107.5 -32q92 0 169 82q1 9 1 48q0 70 -39.5 113.5t-109.5 43.5z" />
    <glyph glyph-name="colonmonetary.onum" unicode="&#xf64d;" horiz-adv-x="582" 
d="M72 -53l58 103q-83 70 -83 188q0 132 96.5 228t229.5 96q21 0 40 -3l23 40h43l-27 -49q25 -7 52 -22l39 71h43l-51 -92q37 -29 58 -70l-68 -30q-12 20 -27 34l-213 -382q24 -2 36 -2q93 0 159 71l56 -40q-92 -100 -218 -100q-32 0 -70 7l-26 -48h-43l32 58q-27 9 -51 24
l-45 -82h-43zM128 242q0 -73 42 -121l207 372h-6q-96 0 -169.5 -73.5t-73.5 -177.5zM198 94q26 -17 49 -25l221 396q-24 16 -51 22z" />
    <glyph glyph-name="Euro.onum" unicode="&#xf64e;" horiz-adv-x="610" 
d="M425 313h-259q-10 -33 -10 -71v-6h252l-9 -44h-237q15 -62 65.5 -98.5t121.5 -36.5q93 0 159 71l56 -40q-92 -100 -218 -100q-107 0 -179.5 55.5t-87.5 148.5h-60l10 44h46v2q0 39 9 75h-39l10 44h43q37 91 120 148t183 57q73 0 132 -34.5t88 -90.5l-68 -30
q-22 39 -64 62.5t-90 23.5q-66 0 -124.5 -37.5t-90.5 -98.5h251z" />
    <glyph glyph-name="florin.onum" unicode="&#xf64f;" horiz-adv-x="367" 
d="M52 -119h-84l75 334h-51v57h62l31 135q34 154 152 154q53 0 86 -32l-33 -60q-14 18 -42 18q-59 0 -80 -90l-28 -125h106v-57h-119z" />
    <glyph glyph-name="numbersign.onum" unicode="&#xf650;" horiz-adv-x="496" 
d="M507 394l-24 -47h-88l-78 -141h89l-25 -47h-91l-88 -159h-57l88 159h-90l-87 -159h-57l88 159h-85l26 47h86l78 141h-87l24 47h89l87 156h57l-88 -156h90l88 156h57l-88 -156h86zM337 347h-88l-79 -141h90z" />
    <glyph glyph-name="sterling.onum" unicode="&#xf651;" horiz-adv-x="425" 
d="M9 224l11 48h77q-30 60 -30 103q0 78 58 132t139 54q60 0 108.5 -28t64.5 -76l-65 -27q-9 36 -38.5 54.5t-65.5 18.5q-51 0 -87.5 -35.5t-36.5 -89.5q0 -12 1.5 -23t6 -24t7 -19t9.5 -21.5t8 -18.5h132l-11 -48h-104q2 -16 2 -23q0 -33 -25 -70.5t-53 -53.5q11 4 23 4
q29 0 71 -14.5t63 -14.5q40 0 73 31l21 -55q-37 -39 -100 -39q-38 0 -88 16.5t-84 16.5q-38 0 -91 -30l-14 54q59 23 96 62t37 81q0 11 -6 35h-109z" />
    <glyph glyph-name="yen.onum" unicode="&#xf652;" horiz-adv-x="547" 
d="M260 0h-78l24 113h-212l10 48h213l16 71h-213l11 47h179l-136 271h89l122 -250l229 250h93l-253 -271h175l-10 -47h-208l-16 -71h208l-10 -48h-209z" />
    <glyph glyph-name="cent.onum" unicode="&#xf654;" horiz-adv-x="582" 
d="M232 -92l19 87q-93 17 -148.5 82.5t-55.5 160.5q0 132 96.5 228t229.5 96h4l13 57h59l-14 -64q53 -13 94 -43.5t64 -74.5l-68 -30q-35 60 -106 79l-95 -429q91 2 156 71l56 -40q-92 -100 -218 -100h-9l-17 -80h-60zM128 242q0 -69 38 -116.5t101 -62.5l95 430
q-94 -4 -164 -77t-70 -174z" />
    <glyph glyph-name="zero.dnom" unicode="&#xf655;" horiz-adv-x="388" 
d="M351 250q0 -37 -10.5 -78.5t-32 -83t-61 -68.5t-90.5 -27q-68 0 -104.5 43t-36.5 113q0 37 10.5 78.5t32 83t61 68.5t90.5 27q68 0 104.5 -43t36.5 -113zM290 252q0 105 -82 105q-46 0 -77.5 -39t-42.5 -84t-11 -87q0 -105 82 -105q35 0 61.5 22t41 56t21.5 68t7 64z" />
    <glyph glyph-name="one.dnom" unicode="&#xf656;" horiz-adv-x="235" 
d="M121 0h-60l69 318l-78 -66l-29 38l133 110h53z" />
    <glyph glyph-name="two.dnom" unicode="&#xf657;" horiz-adv-x="388" 
d="M287 0h-291l11 49q155 92 218.5 143t63.5 101q0 31 -25 47.5t-60 16.5q-68 0 -112 -47l-28 40q54 56 142 56q59 0 101.5 -27.5t42.5 -81.5q0 -60 -63.5 -116t-189.5 -132h201z" />
    <glyph glyph-name="three.dnom" unicode="&#xf658;" horiz-adv-x="388" 
d="M314 114q0 -47 -40.5 -84t-106.5 -37q-117 0 -160 73l40 34q40 -58 118 -58q41 0 64.5 22.5t23.5 56.5q0 27 -27 43.5t-72 16.5q-23 0 -30 -1l11 50q28 -2 61 -2q42 0 66.5 18t24.5 50q0 28 -27 44.5t-65 16.5q-58 0 -108 -43l-23 39q58 53 135 53q65 0 107 -26.5
t42 -75.5q0 -44 -31 -72t-80 -32q32 -6 54.5 -29t22.5 -57z" />
    <glyph glyph-name="four.dnom" unicode="&#xf659;" horiz-adv-x="388" 
d="M320 100h-58l-22 -100h-59l22 100h-196l9 44l229 256h83l-55 -251h57zM213 149l43 194l-178 -194h135z" />
    <glyph glyph-name="five.dnom" unicode="&#xf65a;" horiz-adv-x="388" 
d="M320 145q0 -65 -45 -108.5t-115 -43.5q-52 0 -92.5 19t-60.5 53l43 36q35 -59 109 -59q44 0 72 26.5t28 66.5q0 34 -25 53t-62 19q-50 0 -90 -37l-40 16l48 214h261l-12 -49h-200l-28 -130q37 32 88 32q49 0 85 -30t36 -78z" />
    <glyph glyph-name="six.dnom" unicode="&#xf65b;" horiz-adv-x="388" 
d="M325 143q0 -61 -42.5 -105.5t-114.5 -44.5t-110.5 40t-38.5 107q0 108 59 187t145 79t129 -49l-37 -41q-29 41 -93 41q-61 0 -100 -58.5t-39 -100.5v-4q54 61 116 61q54 0 90 -29.5t36 -82.5zM264 135q0 34 -24.5 53t-64.5 19q-48 0 -96 -56q-2 -5 -2 -21q0 -40 25 -64
t66 -24q44 0 70 28t26 65z" />
    <glyph glyph-name="seven.dnom" unicode="&#xf65c;" horiz-adv-x="362" 
d="M348 358l-245 -358h-68l243 351h-223l12 49h290z" />
    <glyph glyph-name="eight.dnom" unicode="&#xf65d;" horiz-adv-x="388" 
d="M316 105q0 -49 -42 -80.5t-110 -31.5q-66 0 -111.5 28t-45.5 78q0 42 35.5 74t90.5 38q-75 27 -75 86q0 52 43 80.5t101 28.5q59 0 105 -26t46 -73q0 -41 -29.5 -70.5t-87.5 -36.5q34 -13 57 -37.5t23 -57.5zM292 296q0 30 -25 46t-63 16q-37 0 -62.5 -18.5t-25.5 -48.5
q0 -27 25.5 -44t48.5 -22q39 3 70.5 20t31.5 51zM257 110q0 24 -22 44.5t-55 29.5q-50 -4 -81 -25.5t-31 -53.5q0 -28 28 -46.5t65 -18.5q39 0 67.5 20t28.5 50z" />
    <glyph glyph-name="nine.dnom" unicode="&#xf65e;" horiz-adv-x="388" 
d="M43 256q0 61 42.5 106t114.5 45t110.5 -40t38.5 -107q0 -109 -58.5 -187.5t-145.5 -78.5q-88 0 -128 49l36 41q28 -41 93 -41q43 0 77 32.5t48 68t14 58.5v4q-52 -61 -116 -61q-54 0 -90 29t-36 82zM104 264q0 -34 24.5 -52.5t64.5 -18.5q51 0 96 55q2 5 2 22
q0 40 -24.5 64t-65.5 24q-44 0 -70.5 -28.5t-26.5 -65.5z" />
    <glyph glyph-name="zero.numr" unicode="&#xf661;" horiz-adv-x="388" 
d="M410 517q0 -37 -10.5 -78.5t-32 -83t-61 -68.5t-90.5 -27q-68 0 -104.5 43t-36.5 113q0 37 10.5 78.5t32 83t61 68.5t90.5 27q68 0 104.5 -43t36.5 -113zM349 519q0 105 -82 105q-46 0 -77.5 -39t-42.5 -84t-11 -87q0 -105 82 -105q35 0 61.5 22t41 56t21.5 68t7 64z" />
    <glyph glyph-name="one.numr" unicode="&#xf662;" horiz-adv-x="235" 
d="M180 267h-60l69 318l-78 -66l-29 38l133 110h53z" />
    <glyph glyph-name="two.numr" unicode="&#xf663;" horiz-adv-x="388" 
d="M346 267h-291l11 49q155 92 218.5 143t63.5 101q0 31 -25 47.5t-60 16.5q-68 0 -112 -47l-28 40q54 56 142 56q59 0 101.5 -27.5t42.5 -81.5q0 -60 -63.5 -116t-189.5 -132h201z" />
    <glyph glyph-name="three.numr" unicode="&#xf664;" horiz-adv-x="388" 
d="M373 381q0 -47 -40.5 -84t-106.5 -37q-117 0 -160 73l40 34q40 -58 118 -58q41 0 64.5 22.5t23.5 56.5q0 27 -27 43.5t-72 16.5q-23 0 -30 -1l11 50q28 -2 61 -2q42 0 66.5 18t24.5 50q0 28 -27 44.5t-65 16.5q-58 0 -108 -43l-23 39q58 53 135 53q65 0 107 -26.5
t42 -75.5q0 -44 -31 -72t-80 -32q32 -6 54.5 -29t22.5 -57z" />
    <glyph glyph-name="four.numr" unicode="&#xf665;" horiz-adv-x="388" 
d="M379 367h-58l-22 -100h-59l22 100h-196l9 44l229 256h83l-55 -251h57zM272 416l43 194l-178 -194h135z" />
    <glyph glyph-name="five.numr" unicode="&#xf666;" horiz-adv-x="388" 
d="M379 412q0 -65 -45 -108.5t-115 -43.5q-52 0 -92.5 19t-60.5 53l43 36q35 -59 109 -59q44 0 72 26.5t28 66.5q0 34 -25 53t-62 19q-50 0 -90 -37l-40 16l48 214h261l-12 -49h-200l-28 -130q37 32 88 32q49 0 85 -30t36 -78z" />
    <glyph glyph-name="six.numr" unicode="&#xf667;" horiz-adv-x="388" 
d="M384 410q0 -61 -42.5 -105.5t-114.5 -44.5t-110.5 40t-38.5 107q0 108 59 187t145 79t129 -49l-37 -41q-29 41 -93 41q-61 0 -100 -58.5t-39 -100.5v-4q54 61 116 61q54 0 90 -29.5t36 -82.5zM323 402q0 34 -24.5 53t-64.5 19q-48 0 -96 -56q-2 -5 -2 -21q0 -40 25 -64
t66 -24q44 0 70 28t26 65z" />
    <glyph glyph-name="seven.numr" unicode="&#xf668;" horiz-adv-x="362" 
d="M407 625l-245 -358h-68l243 351h-223l12 49h290z" />
    <glyph glyph-name="eight.numr" unicode="&#xf669;" horiz-adv-x="388" 
d="M375 372q0 -49 -42 -80.5t-110 -31.5q-66 0 -111.5 28t-45.5 78q0 42 35.5 74t90.5 38q-75 27 -75 86q0 52 43 80.5t101 28.5q59 0 105 -26t46 -73q0 -41 -29.5 -70.5t-87.5 -36.5q34 -13 57 -37.5t23 -57.5zM351 563q0 30 -25 46t-63 16q-37 0 -62.5 -18.5t-25.5 -48.5
q0 -27 25.5 -44t48.5 -22q39 3 70.5 20t31.5 51zM316 377q0 24 -22 44.5t-55 29.5q-50 -4 -81 -25.5t-31 -53.5q0 -28 28 -46.5t65 -18.5q39 0 67.5 20t28.5 50z" />
    <glyph glyph-name="nine.numr" unicode="&#xf66a;" horiz-adv-x="388" 
d="M102 523q0 61 42.5 106t114.5 45t110.5 -40t38.5 -107q0 -109 -58.5 -187.5t-145.5 -78.5q-88 0 -128 49l36 41q28 -41 93 -41q43 0 77 32.5t48 68t14 58.5v4q-52 -61 -116 -61q-54 0 -90 29t-36 82zM163 531q0 -34 24.5 -52.5t64.5 -18.5q51 0 96 55q2 5 2 22
q0 40 -24.5 64t-65.5 24q-44 0 -70.5 -28.5t-26.5 -65.5z" />
    <glyph glyph-name="Abreve.smcp" unicode="&#xf66d;" 
d="M306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM551 676q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="Amacron.smcp" unicode="&#xf66e;" 
d="M549 622h-362l10 48h362zM306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233z" />
    <glyph glyph-name="Aogonek.smcp" unicode="&#xf66f;" 
d="M306 550h94l111 -550q-101 -45 -101 -107q0 -40 43 -40q35 0 55 41l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 68 70 114h-11l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233z" />
    <glyph glyph-name="AEacute.smcp" unicode="&#xf670;" horiz-adv-x="821" 
d="M706 750l-201 -144h-59l180 144h80zM716 0h-383l26 120h-214l-102 -120h-91l472 550h413l-15 -69h-305l-37 -165h298l-15 -69h-298l-39 -178h305zM374 189l62 279l-237 -279h175z" />
    <glyph glyph-name="Cacute.smcp" unicode="&#xf671;" horiz-adv-x="582" 
d="M620 750l-201 -144h-59l180 144h80zM318 -12q-120 0 -195.5 69t-75.5 181q0 132 96.5 228t229.5 96q73 0 132 -34.5t88 -90.5l-68 -30q-22 39 -64 62.5t-90 23.5q-96 0 -169.5 -73.5t-73.5 -177.5q0 -83 54.5 -134t138.5 -51q93 0 159 71l56 -40q-92 -100 -218 -100z" />
    <glyph glyph-name="Ccaron.smcp" unicode="&#xf672;" horiz-adv-x="582" 
d="M458 606h-72l-66 144h49l59 -106l104 106h53zM318 -12q-120 0 -195.5 69t-75.5 181q0 132 96.5 228t229.5 96q73 0 132 -34.5t88 -90.5l-68 -30q-22 39 -64 62.5t-90 23.5q-96 0 -169.5 -73.5t-73.5 -177.5q0 -83 54.5 -134t138.5 -51q93 0 159 71l56 -40
q-92 -100 -218 -100z" />
    <glyph glyph-name="Ccircumflex.smcp" unicode="&#xf673;" horiz-adv-x="582" 
d="M554 606h-49l-60 106l-103 -106h-53l127 144h72zM318 -12q-120 0 -195.5 69t-75.5 181q0 132 96.5 228t229.5 96q73 0 132 -34.5t88 -90.5l-68 -30q-22 39 -64 62.5t-90 23.5q-96 0 -169.5 -73.5t-73.5 -177.5q0 -83 54.5 -134t138.5 -51q93 0 159 71l56 -40
q-92 -100 -218 -100z" />
    <glyph glyph-name="Cdotaccent.smcp" unicode="&#xf674;" horiz-adv-x="582" 
d="M318 -12q-120 0 -195.5 69t-75.5 181q0 132 96.5 228t229.5 96q73 0 132 -34.5t88 -90.5l-68 -30q-22 39 -64 62.5t-90 23.5q-96 0 -169.5 -73.5t-73.5 -177.5q0 -83 54.5 -134t138.5 -51q93 0 159 71l56 -40q-92 -100 -218 -100zM477 657q0 -21 -16 -36.5t-37 -15.5
q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="Dcaron.smcp" unicode="&#xf675;" horiz-adv-x="615" 
d="M431 606h-72l-66 144h49l59 -106l104 106h53zM233 0h-212l121 550h177q114 0 188 -68t74 -169q0 -57 -21 -111t-61.5 -100t-109.5 -74t-156 -28zM238 69h9q118 0 185.5 72t67.5 167q0 77 -52.5 125t-139.5 48h-103l-91 -412h124z" />
    <glyph glyph-name="Dcroat.smcp" unicode="&#xf676;" horiz-adv-x="615" 
d="M233 0h-212l55 251h-63l12 57h63l54 242h177q114 0 188 -68t74 -169q0 -57 -21 -111t-61.5 -100t-109.5 -74t-156 -28zM247 69q118 0 185.5 72t67.5 167q0 77 -52.5 125t-139.5 48h-103l-39 -173h133l-12 -57h-133l-40 -182h133z" />
    <glyph glyph-name="Ebreve.smcp" unicode="&#xf677;" horiz-adv-x="509" 
d="M403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304zM531 676q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="Ecaron.smcp" unicode="&#xf678;" horiz-adv-x="509" 
d="M385 606h-72l-66 144h49l59 -106l104 106h53zM403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304z" />
    <glyph glyph-name="Edotaccent.smcp" unicode="&#xf679;" horiz-adv-x="509" 
d="M403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304zM405 657q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="Emacron.smcp" unicode="&#xf67a;" horiz-adv-x="509" 
d="M403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304zM531 622h-362l10 48h362z" />
    <glyph glyph-name="Eng.smcp" unicode="&#xf67b;" horiz-adv-x="632" 
d="M298 -196q-112 0 -160 75l51 54q32 -59 104 -59q104 0 128 112l3 14l-229 438l-96 -438h-78l121 550h82l227 -429l94 429h79l-126 -572q-37 -174 -200 -174z" />
    <glyph glyph-name="Eogonek.smcp" unicode="&#xf67c;" horiz-adv-x="509" 
d="M400 -106l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 68 70 114h-308l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304l-15 -69q-101 -45 -101 -107q0 -40 43 -40q35 0 55 41z" />
    <glyph glyph-name="Gbreve.smcp" unicode="&#xf67d;" horiz-adv-x="626" 
d="M321 -12q-118 0 -196.5 67.5t-78.5 178.5q0 131 94.5 229.5t233.5 98.5q71 0 130.5 -32t92.5 -84l-68 -35q-22 34 -65.5 58t-93.5 24q-102 0 -171.5 -76t-69.5 -178q0 -80 55.5 -131t139.5 -51q78 0 130 38l29 127h-170l14 65h248l-51 -231q-92 -68 -203 -68zM606 676
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="Gcircumflex.smcp" unicode="&#xf67e;" horiz-adv-x="626" 
d="M321 -12q-118 0 -196.5 67.5t-78.5 178.5q0 131 94.5 229.5t233.5 98.5q71 0 130.5 -32t92.5 -84l-68 -35q-22 34 -65.5 58t-93.5 24q-102 0 -171.5 -76t-69.5 -178q0 -80 55.5 -131t139.5 -51q78 0 130 38l29 127h-170l14 65h248l-51 -231q-92 -68 -203 -68zM554 606
h-49l-60 106l-103 -106h-53l127 144h72z" />
    <glyph glyph-name="Gcommaaccent.smcp" unicode="&#xf67f;" horiz-adv-x="626" 
d="M321 -12q-118 0 -196.5 67.5t-78.5 178.5q0 131 94.5 229.5t233.5 98.5q71 0 130.5 -32t92.5 -84l-68 -35q-22 34 -65.5 58t-93.5 24q-102 0 -171.5 -76t-69.5 -178q0 -80 55.5 -131t139.5 -51q78 0 130 38l29 127h-170l14 65h248l-51 -231q-92 -68 -203 -68zM309 -121
q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5z" />
    <glyph glyph-name="Gdotaccent.smcp" unicode="&#xf680;" horiz-adv-x="626" 
d="M321 -12q-118 0 -196.5 67.5t-78.5 178.5q0 131 94.5 229.5t233.5 98.5q71 0 130.5 -32t92.5 -84l-68 -35q-22 34 -65.5 58t-93.5 24q-102 0 -171.5 -76t-69.5 -178q0 -80 55.5 -131t139.5 -51q78 0 130 38l29 127h-170l14 65h248l-51 -231q-92 -68 -203 -68zM480 657
q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="Hcircumflex.smcp" unicode="&#xf681;" horiz-adv-x="635" 
d="M529 606h-49l-60 106l-103 -106h-53l127 144h72zM506 0h-78l55 248h-329l-55 -248h-78l121 550h78l-51 -233h329l51 233h78z" />
    <glyph glyph-name="Hbar.smcp" unicode="&#xf682;" horiz-adv-x="655" 
d="M516 0h-78l55 248h-329l-55 -248h-78l90 411h-74l11 48h74l20 91h78l-20 -91h329l20 91h78l-20 -91h73l-11 -48h-73zM179 317h329l20 94h-329z" />
    <glyph glyph-name="Ibreve.smcp" unicode="&#xf683;" horiz-adv-x="229" 
d="M99 0h-78l121 550h78zM379 676q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="IJ.smcp" unicode="&#xf684;" horiz-adv-x="650" 
d="M358 -12q-111 0 -160 76l51 52q32 -59 103 -59q103 0 128 113l84 380h78l-85 -386q-20 -91 -69 -133.5t-130 -42.5zM99 0h-78l121 550h78z" />
    <glyph glyph-name="Imacron.smcp" unicode="&#xf685;" horiz-adv-x="229" 
d="M378 622h-362l10 48h362zM99 0h-78l121 550h78z" />
    <glyph glyph-name="Iogonek.smcp" unicode="&#xf686;" horiz-adv-x="229" 
d="M99 0q-101 -45 -101 -107q0 -40 43 -40q35 0 55 41l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 67 70 114h-4l121 550h78z" />
    <glyph glyph-name="Itilde.smcp" unicode="&#xf687;" horiz-adv-x="229" 
d="M99 0h-78l121 550h78zM265 604q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="Jcircumflex.smcp" unicode="&#xf688;" horiz-adv-x="422" 
d="M519 606h-49l-60 106l-103 -106h-53l127 144h72zM129 -12q-111 0 -160 76l51 52q32 -59 103 -59q103 0 128 113l84 380h78l-85 -386q-20 -91 -69 -133.5t-130 -42.5z" />
    <glyph glyph-name="Kcommaaccent.smcp" unicode="&#xf689;" horiz-adv-x="534" 
d="M246 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM466 0h-95l-163 244l-68 -59l-41 -185h-78l121 550h78l-59 -272l306 272h99l-299 -261z" />
    <glyph glyph-name="Lacute.smcp" unicode="&#xf68a;" horiz-adv-x="458" 
d="M366 0h-345l121 550h78l-106 -481h267zM529 750l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="Lcaron.smcp" unicode="&#xf68b;" horiz-adv-x="458" 
d="M389 510q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM366 0h-345l121 550h78l-106 -481h267z" />
    <glyph glyph-name="Lcommaaccent.smcp" unicode="&#xf68c;" horiz-adv-x="458" 
d="M215 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM366 0h-345l121 550h78l-106 -481h267z" />
    <glyph glyph-name="Ldot.smcp" unicode="&#xf68d;" horiz-adv-x="472" 
d="M366 0h-345l121 550h78l-106 -481h267zM360 239q-21 0 -35.5 14.5t-14.5 35.5q0 25 17.5 42.5t42.5 17.5q21 0 35.5 -15t14.5 -36q0 -25 -17.5 -42t-42.5 -17z" />
    <glyph glyph-name="Nacute.smcp" unicode="&#xf68e;" horiz-adv-x="632" 
d="M596 750l-201 -144h-59l180 144h80zM503 0h-79l-227 444l-98 -444h-78l121 550h82l226 -435l95 435h79z" />
    <glyph glyph-name="Ncaron.smcp" unicode="&#xf68f;" horiz-adv-x="632" 
d="M433 606h-72l-66 144h49l59 -106l104 106h53zM503 0h-79l-227 444l-98 -444h-78l121 550h82l226 -435l95 435h79z" />
    <glyph glyph-name="Ncommaaccent.smcp" unicode="&#xf690;" horiz-adv-x="632" 
d="M282 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM503 0h-79l-227 444l-98 -444h-78l121 550h82l226 -435l95 435h79z" />
    <glyph glyph-name="Obreve.smcp" unicode="&#xf691;" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM599 682q-85 -88 -194 -88
q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="Ohungarumlaut.smcp" unicode="&#xf692;" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM499 750l-147 -144h-50l126 144
h71zM641 750l-147 -144h-50l126 144h71z" />
    <glyph glyph-name="Omacron.smcp" unicode="&#xf693;" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM596 622h-362l10 48h362z" />
    <glyph glyph-name="Oslashacute.smcp" unicode="&#xf694;" horiz-adv-x="662" 
d="M312 -12q-101 0 -169 49l-36 -37h-70l68 71q-58 67 -58 168q0 130 91 226.5t224 96.5q97 0 167 -49l35 37h70l-68 -70q61 -69 61 -169q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 67 -35 116l-316 -330q47 -36 118 -36zM127 243q0 -66 34 -114l315 329
q-48 35 -115 35q-102 0 -168 -75t-66 -175zM612 750l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="Racute.smcp" unicode="&#xf695;" horiz-adv-x="545" 
d="M561 750l-201 -144h-59l180 144h80zM446 0h-90l-97 213h-112l-48 -213h-78l121 550h212q78 0 126 -41t48 -105q0 -74 -49 -127.5t-140 -58.5zM312 282h8q59 0 92.5 33.5t33.5 82.5q0 37 -28 60t-72 23h-140l-44 -199h150z" />
    <glyph glyph-name="Rcaron.smcp" unicode="&#xf696;" horiz-adv-x="545" 
d="M398 606h-72l-66 144h49l59 -106l104 106h53zM446 0h-90l-97 213h-112l-48 -213h-78l121 550h212q78 0 126 -41t48 -105q0 -74 -49 -127.5t-140 -58.5zM312 282h8q59 0 92.5 33.5t33.5 82.5q0 37 -28 60t-72 23h-140l-44 -199h150z" />
    <glyph glyph-name="Rcommaaccent.smcp" unicode="&#xf697;" horiz-adv-x="545" 
d="M243 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM446 0h-90l-97 213h-112l-48 -213h-78l121 550h212q78 0 126 -41t48 -105q0 -74 -49 -127.5t-140 -58.5zM312 282h8
q59 0 92.5 33.5t33.5 82.5q0 37 -28 60t-72 23h-140l-44 -199h150z" />
    <glyph glyph-name="Sacute.smcp" unicode="&#xf698;" horiz-adv-x="504" 
d="M537 750l-201 -144h-59l180 144h80zM240 -12q-162 0 -237 97l53 56q27 -39 77.5 -61.5t110.5 -22.5q64 0 93.5 28.5t29.5 69.5q0 30 -29.5 51.5t-71.5 36.5t-84 32t-71.5 49t-29.5 77q0 66 60.5 113.5t145.5 47.5q66 0 122 -25t88 -65l-51 -54q-23 34 -69 54.5t-93 20.5
q-51 0 -85.5 -25.5t-34.5 -63.5q0 -28 29.5 -47.5t71.5 -34t84 -32t71.5 -51.5t29.5 -82q0 -69 -53.5 -119t-156.5 -50z" />
    <glyph glyph-name="Scedilla.smcp" unicode="&#xf699;" horiz-adv-x="504" 
d="M167 -191q-68 0 -108 38l27 36q33 -35 83 -35q29 0 45 13t16 34q0 30 -37 30q-20 0 -35 -17l-29 19l40 67q-112 19 -166 91l53 56q27 -39 77.5 -61.5t110.5 -22.5q64 0 93.5 28.5t29.5 69.5q0 30 -29.5 51.5t-71.5 36.5t-84 32t-71.5 49t-29.5 77q0 66 60.5 113.5
t145.5 47.5q66 0 122 -25t88 -65l-51 -54q-23 34 -69 54.5t-93 20.5q-51 0 -85.5 -25.5t-34.5 -63.5q0 -28 29.5 -47.5t71.5 -34t84 -32t71.5 -51.5t29.5 -82q0 -69 -53.5 -119t-156.5 -50q-5 0 -15 0.5t-15 0.5l-27 -44q15 11 34 11q26 0 42 -15.5t16 -43.5
q0 -39 -30 -63.5t-78 -24.5z" />
    <glyph glyph-name="Scircumflex.smcp" unicode="&#xf69a;" horiz-adv-x="504" 
d="M469 606h-49l-60 106l-103 -106h-53l127 144h72zM240 -12q-162 0 -237 97l53 56q27 -39 77.5 -61.5t110.5 -22.5q64 0 93.5 28.5t29.5 69.5q0 30 -29.5 51.5t-71.5 36.5t-84 32t-71.5 49t-29.5 77q0 66 60.5 113.5t145.5 47.5q66 0 122 -25t88 -65l-51 -54
q-23 34 -69 54.5t-93 20.5q-51 0 -85.5 -25.5t-34.5 -63.5q0 -28 29.5 -47.5t71.5 -34t84 -32t71.5 -51.5t29.5 -82q0 -69 -53.5 -119t-156.5 -50z" />
    <glyph glyph-name="Scommaaccent.smcp" unicode="&#xf69b;" horiz-adv-x="504" 
d="M224 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM240 -12q-162 0 -237 97l53 56q27 -39 77.5 -61.5t110.5 -22.5q64 0 93.5 28.5t29.5 69.5q0 30 -29.5 51.5t-71.5 36.5t-84 32
t-71.5 49t-29.5 77q0 66 60.5 113.5t145.5 47.5q66 0 122 -25t88 -65l-51 -54q-23 34 -69 54.5t-93 20.5q-51 0 -85.5 -25.5t-34.5 -63.5q0 -28 29.5 -47.5t71.5 -34t84 -32t71.5 -51.5t29.5 -82q0 -69 -53.5 -119t-156.5 -50z" />
    <glyph glyph-name="Tbar.smcp" unicode="&#xf69c;" horiz-adv-x="472" 
d="M220 0h-78l53 241h-115l10 48h115l43 192h-170l15 69h419l-15 -69h-171l-43 -192h119l-11 -48h-118z" />
    <glyph glyph-name="Tcaron.smcp" unicode="&#xf69d;" horiz-adv-x="472" 
d="M352 606h-72l-66 144h49l59 -106l104 106h53zM220 0h-78l106 481h-170l15 69h419l-15 -69h-171z" />
    <glyph glyph-name="Tcommaaccent.smcp" unicode="&#xf69e;" horiz-adv-x="472" 
d="M203 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM220 0h-78l106 481h-170l15 69h419l-15 -69h-171z" />
    <glyph glyph-name="Ubreve.smcp" unicode="&#xf69f;" horiz-adv-x="623" 
d="M295 -12q-111 0 -172 48.5t-61 131.5q0 19 5 44l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-25 -112 -82 -168t-163 -56zM577 676q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72
z" />
    <glyph glyph-name="Uhungarumlaut.smcp" unicode="&#xf6a0;" horiz-adv-x="623" 
d="M295 -12q-111 0 -172 48.5t-61 131.5q0 19 5 44l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-25 -112 -82 -168t-163 -56zM477 750l-147 -144h-50l126 144h71zM619 750l-147 -144h-50l126 144h71z" />
    <glyph glyph-name="Umacron.smcp" unicode="&#xf6a1;" horiz-adv-x="623" 
d="M295 -12q-111 0 -172 48.5t-61 131.5q0 19 5 44l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-25 -112 -82 -168t-163 -56zM576 622h-362l10 48h362z" />
    <glyph glyph-name="Uogonek.smcp" unicode="&#xf6a2;" horiz-adv-x="623" 
d="M378 -106l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 59 54 102q-109 1 -169 49t-60 131q0 19 5 44l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-40 -178 -161 -213q-99 -45 -99 -106q0 -40 43 -40
q35 0 55 41z" />
    <glyph glyph-name="Uring.smcp" unicode="&#xf6a3;" horiz-adv-x="623" 
d="M295 -12q-111 0 -172 48.5t-61 131.5q0 19 5 44l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-25 -112 -82 -168t-163 -56zM397 573q-39 0 -64 25t-25 64q0 44 32 76t76 32q39 0 64 -25t25 -64q0 -44 -32 -76
t-76 -32zM400 613q26 0 45.5 19t19.5 45q0 24 -14 38.5t-38 14.5q-26 0 -45.5 -19t-19.5 -45q0 -23 14.5 -38t37.5 -15z" />
    <glyph glyph-name="Utilde.smcp" unicode="&#xf6a4;" horiz-adv-x="623" 
d="M295 -12q-111 0 -172 48.5t-61 131.5q0 19 5 44l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-25 -112 -82 -168t-163 -56zM463 604q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46
q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="Wcircumflex.smcp" unicode="&#xf6a6;" horiz-adv-x="764" 
d="M593 606h-49l-60 106l-103 -106h-53l127 144h72zM534 0h-82l-29 431l-220 -431h-82l-44 550h86l25 -443l227 443h66l30 -444l222 444h87z" />
    <glyph glyph-name="Ycircumflex.smcp" unicode="&#xf6a9;" horiz-adv-x="547" 
d="M487 606h-49l-60 106l-103 -106h-53l127 144h72zM260 0h-78l51 233l-159 317h89l122 -250l229 250h93l-296 -317z" />
    <glyph glyph-name="Zacute.smcp" unicode="&#xf6ab;" horiz-adv-x="516" 
d="M533 750l-201 -144h-59l180 144h80zM423 0h-438l13 59l417 422h-324l15 69h431l-13 -59l-416 -422h330z" />
    <glyph glyph-name="Zdotaccent.smcp" unicode="&#xf6ac;" horiz-adv-x="516" 
d="M423 0h-438l13 59l417 422h-324l15 69h431l-13 -59l-416 -422h330zM392 657q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="Idotaccent.smcp" unicode="&#xf6ad;" horiz-adv-x="229" 
d="M99 0h-78l121 550h78zM251 657q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="parenleft.case" unicode="&#xf6ae;" horiz-adv-x="248" 
d="M161 -79l-53 -30q-63 124 -63 290q0 155 66.5 309t190.5 285l39 -38q-217 -283 -217 -584q0 -105 37 -232z" />
    <glyph glyph-name="parenright.case" unicode="&#xf6af;" horiz-adv-x="248" 
d="M126 745l53 30q63 -124 63 -290q0 -155 -67 -308.5t-191 -285.5l-38 38q217 283 217 583q0 106 -37 233z" />
    <glyph glyph-name="bracketleft.case" unicode="&#xf6b0;" horiz-adv-x="243" 
d="M149 -100h-186l193 868h186l-12 -55h-128l-168 -758h128z" />
    <glyph glyph-name="bracketright.case" unicode="&#xf6b1;" horiz-adv-x="243" 
d="M126 -100h-185l12 55h127l169 758h-128l12 55h186z" />
    <glyph glyph-name="braceleft.case" unicode="&#xf6b2;" horiz-adv-x="260" 
d="M166 -100h-47q-45 0 -78.5 30t-33.5 77q0 15 3 27l46 206q2 12 2 17q0 22 -10.5 37t-28.5 15l12 50q52 0 67 69l45 205q15 67 55.5 101t99.5 34h61l-12 -55h-61q-66 0 -84 -80l-49 -217q-15 -70 -64 -88q29 -15 29 -54q0 -18 -4 -34l-45 -205q-2 -12 -2 -21
q0 -26 16 -42.5t41 -16.5h55z" />
    <glyph glyph-name="braceright.case" unicode="&#xf6b3;" horiz-adv-x="260" 
d="M133 768h48q45 0 78 -30t33 -77q0 -16 -3 -28l-46 -205q-2 -12 -2 -17q0 -23 10.5 -37.5t28.5 -14.5l-11 -50q-53 0 -68 -69l-45 -205q-15 -67 -55.5 -101t-99.5 -34h-61l12 55h62q66 0 83 79l49 218q15 70 65 88q-29 15 -29 54q0 18 4 34l45 205q2 12 2 21
q0 25 -16.5 42t-41.5 17h-55z" />
    <glyph glyph-name="exclamdown.case" unicode="&#xf6b4;" horiz-adv-x="230" 
d="M135 480h62l-90 -480h-94zM202 677q21 0 35.5 -14.5t14.5 -36.5q0 -24 -17.5 -41.5t-42.5 -17.5q-21 0 -35.5 15t-14.5 36q0 25 17.5 42t42.5 17z" />
    <glyph glyph-name="questiondown.case" unicode="&#xf6b5;" horiz-adv-x="462" 
d="M245 455l66 27q23 -32 23 -71q0 -40 -24.5 -70t-59 -49t-69.5 -37t-59.5 -44t-24.5 -61q0 -40 32 -63.5t90 -23.5q87 0 154 65l37 -57q-88 -82 -201 -82q-88 0 -144.5 40.5t-56.5 106.5q0 42 18.5 75t47 52t61 38t61 33t47 35t18.5 47q0 22 -16 39zM326 677
q22 0 36.5 -15t14.5 -36q0 -25 -18 -42t-43 -17q-21 0 -35.5 14.5t-14.5 36.5q0 24 18 41.5t42 17.5z" />
    <glyph glyph-name="guillemotleft.case" unicode="&#xf6b6;" horiz-adv-x="439" 
d="M242 162h-71l-120 180l200 177h79l-204 -182zM386 162h-71l-120 180l200 177h79l-204 -182z" />
    <glyph glyph-name="guillemotright.case" unicode="&#xf6b7;" horiz-adv-x="439" 
d="M98 519h71l120 -180l-200 -177h-79l204 182zM242 519h71l120 -180l-200 -177h-79l204 182z" />
    <glyph glyph-name="guilsinglleft.case" unicode="&#xf6b8;" horiz-adv-x="295" 
d="M242 162h-71l-120 180l200 177h79l-204 -182z" />
    <glyph glyph-name="guilsinglright.case" unicode="&#xf6b9;" horiz-adv-x="295" 
d="M99 519h71l120 -180l-200 -177h-79l204 182z" />
    <glyph glyph-name="hyphen.case" unicode="&#xf6ba;" horiz-adv-x="300" 
d="M284 308h-240l14 66h240z" />
    <glyph glyph-name="endash.case" unicode="&#xf6bb;" horiz-adv-x="593" 
d="M577 308h-533l14 66h533z" />
    <glyph glyph-name="emdash.case" unicode="&#xf6bc;" horiz-adv-x="833" 
d="M817 308h-773l14 66h773z" />
    <glyph glyph-name="periodcentered.case" unicode="&#xf6bd;" horiz-adv-x="230" 
d="M131 288q-21 0 -35.5 14.5t-14.5 35.5q0 25 17.5 42.5t42.5 17.5q21 0 35.5 -15t14.5 -36q0 -25 -17.5 -42t-42.5 -17z" />
    <glyph glyph-name="bullet.case" unicode="&#xf6be;" horiz-adv-x="358" 
d="M310 352q0 -48 -36.5 -83t-84.5 -35q-41 0 -69 28t-28 69q0 48 36.5 83t84.5 35q41 0 69 -28t28 -69z" />
    <glyph glyph-name="cent.case" unicode="&#xf6bf;" horiz-adv-x="495" 
d="M197 0l19 87q-72 18 -114 72t-42 136q0 118 77.5 203.5t190.5 87.5l18 81h59l-19 -86q85 -16 127 -85l-57 -43q-27 47 -85 62l-83 -369h1q72 0 122 57l43 -50q-72 -74 -172 -74h-9l-17 -79h-59zM138 296q0 -55 25 -91t68 -50l81 362q-79 -9 -126.5 -72.5t-47.5 -148.5z
" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="230" 
d="M83 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5z" />
    <glyph glyph-name="one.tnum" unicode="&#xf6dc;" horiz-adv-x="598" 
d="M341 0h-83l123 557l-132 -113l-39 51l205 172h73z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="1125" 
d="M835 -12q-56 0 -99.5 23.5t-68.5 64.5l-58 -260h-75l148 667h75l-16 -69q66 81 160 81q84 0 135.5 -53t51.5 -146q0 -123 -70.5 -215.5t-182.5 -92.5zM825 55q80 0 132 68t52 159q0 67 -37 106.5t-98 39.5q-44 0 -83 -23.5t-65 -59.5l-47 -212q19 -35 57.5 -56.5
t88.5 -21.5zM508 0h-92l-112 265h-140l-57 -265h-83l147 667h245q76 0 132.5 -48t56.5 -126q0 -93 -60 -155t-157 -69zM354 338h3q75 0 117.5 42.5t42.5 105.5q0 46 -34.5 76.5t-78.5 30.5h-166l-57 -255h173z" />
    <glyph glyph-name="centinferior" unicode="&#xf6df;" horiz-adv-x="307" 
d="M69 -151l12 58q-46 11 -72.5 44t-26.5 82q0 69 48 120.5t117 52.5l10 42h41l-10 -46q54 -12 78 -54l-43 -28q-15 29 -45 37l-47 -212q36 3 69 37l33 -32q-44 -48 -109 -48h-3l-11 -53h-41zM39 35q0 -66 52 -84l46 209q-43 -6 -70.5 -42t-27.5 -83z" />
    <glyph glyph-name="centsuperior" unicode="&#xf6e0;" horiz-adv-x="307" 
d="M194 416l12 58q-46 11 -72.5 44t-26.5 82q0 69 48 120.5t117 52.5l10 42h41l-10 -46q54 -12 78 -54l-43 -28q-15 29 -45 37l-47 -212q36 3 69 37l33 -32q-44 -48 -109 -48h-3l-11 -53h-41zM164 602q0 -66 52 -84l46 209q-43 -6 -70.5 -42t-27.5 -83z" />
    <glyph glyph-name="commainferior" unicode="&#xf6e1;" horiz-adv-x="139" 
d="M25 -112q0 -29 -22 -61t-53 -50l-20 24q16 8 31.5 23t19.5 28q-3 -1 -5 -1q-11 0 -19.5 9t-8.5 23q0 18 12.5 29.5t29.5 11.5q14 0 24.5 -10t10.5 -26z" />
    <glyph glyph-name="commasuperior" unicode="&#xf6e2;" horiz-adv-x="139" 
d="M150 455q0 -29 -22 -61t-53 -50l-20 24q16 8 31.5 23t19.5 28q-3 -1 -5 -1q-11 0 -19.5 9t-8.5 23q0 18 12.5 29.5t29.5 11.5q14 0 24.5 -10t10.5 -26z" />
    <glyph glyph-name="dollarinferior" unicode="&#xf6e3;" horiz-adv-x="370" 
d="M69 -206l12 57q-87 15 -121 70l42 36q31 -45 90 -59l30 137q-35 13 -55.5 23t-39 32.5t-18.5 52.5q0 47 41.5 82t109.5 35h11l13 55h42l-14 -62q67 -15 102 -60l-42 -34q-23 34 -70 48l-28 -126q53 -19 82.5 -43t29.5 -67q0 -51 -39.5 -87.5t-115.5 -36.5h-9l-11 -53h-42
zM221 -38q0 21 -15.5 34t-45.5 25l-27 -127q41 1 64.5 19.5t23.5 48.5zM73 151q0 -19 16 -31.5t46 -24.5l26 119h-1q-37 0 -62 -18t-25 -45z" />
    <glyph glyph-name="dollarsuperior" unicode="&#xf6e4;" horiz-adv-x="370" 
d="M194 361l12 57q-87 15 -121 70l42 36q31 -45 90 -59l30 137q-35 13 -55.5 23t-39 32.5t-18.5 52.5q0 47 41.5 82t109.5 35h11l13 55h42l-14 -62q67 -15 102 -60l-42 -34q-23 34 -70 48l-28 -126q53 -19 82.5 -43t29.5 -67q0 -51 -39.5 -87.5t-115.5 -36.5h-9l-11 -53h-42
zM346 529q0 21 -15.5 34t-45.5 25l-27 -127q41 1 64.5 19.5t23.5 48.5zM198 718q0 -19 16 -31.5t46 -24.5l26 119h-1q-37 0 -62 -18t-25 -45z" />
    <glyph glyph-name="hypheninferior" unicode="&#xf6e5;" horiz-adv-x="183" 
d="M118 30h-148l10 46h148z" />
    <glyph glyph-name="hyphensuperior" unicode="&#xf6e6;" horiz-adv-x="183" 
d="M243 597h-148l10 46h148z" />
    <glyph glyph-name="periodinferior" unicode="&#xf6e7;" horiz-adv-x="142" 
d="M29 -110q0 -16 -13 -28.5t-30 -12.5q-14 0 -24 9.5t-10 24.5q0 16 13 28.5t30 12.5q15 0 24.5 -9.5t9.5 -24.5z" />
    <glyph glyph-name="periodsuperior" unicode="&#xf6e8;" horiz-adv-x="142" 
d="M154 457q0 -16 -13 -28.5t-30 -12.5q-14 0 -24 9.5t-10 24.5q0 16 13 28.5t30 12.5q15 0 24.5 -9.5t9.5 -24.5z" />
    <glyph glyph-name="asuperior" unicode="&#xf6e9;" horiz-adv-x="410" 
d="M267 601q-50 0 -85 -42.5t-35 -100.5q0 -45 23.5 -69t63.5 -24q53 0 96 49l32 143q-28 44 -95 44zM379 640h58l-70 -314h-58l11 46q-46 -54 -108 -54q-53 0 -89 35t-36 99q0 80 48.5 138t120.5 58q77 0 114 -54z" />
    <glyph glyph-name="bsuperior" unicode="&#xf6ea;" horiz-adv-x="409" 
d="M248 364q50 0 85 42.5t35 101.5q0 45 -23.5 69t-62.5 24q-53 0 -96 -49l-32 -143q12 -20 38 -32.5t56 -12.5zM136 326h-58l96 434h58l-36 -166q45 54 107 54q53 0 89.5 -35.5t36.5 -99.5q0 -79 -49 -137t-121 -58q-77 0 -113 53z" />
    <glyph glyph-name="dsuperior" unicode="&#xf6eb;" horiz-adv-x="410" 
d="M267 601q-50 0 -85 -42.5t-35 -100.5q0 -45 23.5 -69t63.5 -24q53 0 96 49l32 143q-28 44 -95 44zM406 760h57l-96 -434h-58l11 46q-46 -54 -108 -54q-53 0 -89 35t-36 99q0 80 48.5 138t120.5 58q77 0 114 -54z" />
    <glyph glyph-name="esuperior" unicode="&#xf6ec;" horiz-adv-x="398" 
d="M247 318q-73 0 -117 38t-44 105q0 75 54 131t130 56q61 0 101 -37.5t40 -100.5q0 -29 -4 -46h-261q-1 -5 -1 -11q0 -38 29 -64.5t76 -26.5q53 0 95 31l18 -36q-51 -39 -116 -39zM152 506h207v9q0 39 -24.5 64.5t-66.5 25.5q-45 0 -76.5 -30t-39.5 -69z" />
    <glyph glyph-name="isuperior" unicode="&#xf6ed;" horiz-adv-x="174" 
d="M185 685q-13 0 -22.5 9.5t-9.5 22.5q0 17 13 29.5t30 12.5q13 0 22.5 -9.5t9.5 -22.5q0 -17 -13 -29.5t-30 -12.5zM132 326h-58l69 314h58z" />
    <glyph glyph-name="lsuperior" unicode="&#xf6ee;" horiz-adv-x="174" 
d="M132 326h-58l96 434h58z" />
    <glyph glyph-name="msuperior" unicode="&#xf6ef;" horiz-adv-x="571" 
d="M525 326h-57l46 207q3 12 3 21q0 21 -13.5 33.5t-37.5 12.5q-45 0 -88 -50l-49 -224h-57l46 207q2 16 2 21q0 46 -52 46q-41 0 -87 -50l-49 -224h-58l69 314h58l-9 -45q43 53 103 53q34 0 57.5 -16.5t29.5 -42.5q49 59 111 59q36 0 60 -20t24 -56q0 -11 -3 -26z" />
    <glyph glyph-name="osuperior" unicode="&#xf6f0;" horiz-adv-x="405" 
d="M236 318q-68 0 -109 39.5t-41 102.5q0 78 53.5 133t129.5 55q68 0 109 -39.5t41 -103.5q0 -77 -53.5 -132t-129.5 -55zM238 366q54 0 87.5 41.5t33.5 98.5q0 41 -25 66.5t-66 25.5q-54 0 -88 -41.5t-34 -97.5q0 -42 25 -67.5t67 -25.5z" />
    <glyph glyph-name="rsuperior" unicode="&#xf6f1;" horiz-adv-x="240" 
d="M132 326h-58l69 314h58l-10 -48q58 55 119 55l-13 -54q-5 1 -20 1q-25 0 -52.5 -13.5t-43.5 -33.5z" />
    <glyph glyph-name="ssuperior" unicode="&#xf6f2;" horiz-adv-x="330" 
d="M197 318q-88 0 -139 60l34 33q15 -22 43.5 -36.5t62.5 -14.5q32 0 51.5 14t19.5 35q0 22 -26.5 37.5t-58.5 23.5t-58.5 29.5t-26.5 53.5q0 38 34.5 66.5t91.5 28.5q86 0 126 -54l-32 -31q-33 43 -96 43q-30 0 -48 -13.5t-18 -35.5q0 -16 17.5 -27t42.5 -18.5t50 -17
t42.5 -29.5t17.5 -50q0 -38 -34.5 -67.5t-95.5 -29.5z" />
    <glyph glyph-name="tsuperior" unicode="&#xf6f3;" horiz-adv-x="206" 
d="M168 318q-75 0 -75 59q0 11 3 20l44 200h-53l9 43h53l19 86h58l-19 -86h65l-9 -43h-65l-43 -192q-2 -10 -2 -15q0 -25 30 -25q17 0 31 10l7 -40q-22 -17 -53 -17z" />
    <glyph glyph-name="Lslash.smcp" unicode="&#xf6f9;" horiz-adv-x="481" 
d="M-6 171l16 72l99 51l56 256h78l-47 -211l108 55l-16 -72l-108 -56l-43 -197h267l-15 -69h-345l49 221z" />
    <glyph glyph-name="OE.smcp" unicode="&#xf6fa;" horiz-adv-x="953" 
d="M847 0h-382l18 85q-67 -97 -188 -97q-112 0 -180 69.5t-68 181.5q0 130 90.5 226.5t225.5 96.5q67 0 120.5 -31t77.5 -94l25 113h382l-15 -69h-304l-37 -165h299l-15 -69h-299l-39 -178h304zM506 191l37 164q-16 70 -67 104t-115 34q-98 0 -166 -73.5t-68 -176.5
q0 -85 52 -135.5t135 -50.5q62 0 113 35t79 99z" />
    <glyph glyph-name="Scaron.smcp" unicode="&#xf6fd;" horiz-adv-x="504" 
d="M376 606h-72l-66 144h49l59 -106l104 106h53zM240 -12q-162 0 -237 97l53 56q27 -39 77.5 -61.5t110.5 -22.5q64 0 93.5 28.5t29.5 69.5q0 30 -29.5 51.5t-71.5 36.5t-84 32t-71.5 49t-29.5 77q0 66 60.5 113.5t145.5 47.5q66 0 122 -25t88 -65l-51 -54q-23 34 -69 54.5
t-93 20.5q-51 0 -85.5 -25.5t-34.5 -63.5q0 -28 29.5 -47.5t71.5 -34t84 -32t71.5 -51.5t29.5 -82q0 -69 -53.5 -119t-156.5 -50z" />
    <glyph glyph-name="Zcaron.smcp" unicode="&#xf6ff;" horiz-adv-x="516" 
d="M373 606h-72l-66 144h49l59 -106l104 106h53zM423 0h-438l13 59l417 422h-324l15 69h431l-13 -59l-416 -422h330z" />
    <glyph glyph-name="exclam.smcp" unicode="&#xf721;" horiz-adv-x="230" 
d="M226 550l-99 -384h-58l70 384h87zM66 -10q-22 0 -36.5 14.5t-14.5 35.5q0 25 18 42.5t43 17.5q21 0 35.5 -15t14.5 -36q0 -24 -18 -41.5t-42 -17.5z" />
    <glyph glyph-name="dollar.onum" unicode="&#xf724;" horiz-adv-x="504" 
d="M155 -74l15 68q-110 17 -167 91l53 56q42 -61 129 -78l43 194q-33 12 -55 22.5t-45 27.5t-35 40.5t-12 53.5q0 66 60.5 113.5t145.5 47.5h9l15 66h59l-17 -73q95 -21 144 -83l-51 -54q-36 50 -108 68l-39 -176q70 -26 110.5 -59t40.5 -94q0 -69 -53.5 -119t-156.5 -50
h-13l-13 -62h-59zM367 155q0 28 -21.5 46t-63.5 35l-39 -179h1q64 0 93.5 28.5t29.5 69.5zM164 404q0 -25 20.5 -41.5t59.5 -32.5l37 163q-50 -1 -83.5 -26.5t-33.5 -62.5z" />
    <glyph glyph-name="ampersand.smcp" unicode="&#xf726;" horiz-adv-x="556" 
d="M461 0h-88l-37 45q-77 -56 -158 -56q-79 0 -127.5 38.5t-48.5 108.5q0 72 44.5 113t121.5 69q-21 51 -21 93q0 64 47 107.5t112 43.5q54 0 93 -27t39 -77q0 -28 -11 -51t-26 -38t-43 -30t-47.5 -23t-54.5 -20q20 -38 46 -76q16 -26 55 -83q55 58 95 133l53 -29
q-58 -93 -115 -150q26 -36 71 -91zM187 44q57 0 116 45q-28 39 -61 90q-34 52 -53 91q-54 -22 -83 -51t-29 -76q0 -44 29.5 -71.5t80.5 -27.5zM217 411q0 -30 17 -70q68 23 103 47t35 65q0 56 -63 56q-40 0 -66 -27t-26 -71z" />
    <glyph glyph-name="zero.onum" unicode="&#xf730;" horiz-adv-x="612" 
d="M574 325q0 -54 -18.5 -111.5t-53 -108.5t-91 -84t-123.5 -33q-116 0 -177.5 62.5t-61.5 173.5q0 74 31 149.5t99 132t157 56.5q115 0 176.5 -63t61.5 -174zM489 320q0 82 -39 125t-115 43q-64 0 -111.5 -45t-68 -102t-20.5 -112q0 -81 38.5 -124t114.5 -43q64 0 112 45
t68.5 102t20.5 111z" />
    <glyph glyph-name="one.onum" unicode="&#xf731;" horiz-adv-x="339" 
d="M207 0h-83l97 440l-132 -113l-39 51l205 172h73z" />
    <glyph glyph-name="two.onum" unicode="&#xf732;" horiz-adv-x="578" 
d="M437 0h-431l15 70q207 76 316 152t109 157q0 49 -38.5 79t-97.5 30q-106 0 -187 -75l-39 58q97 91 233 91q94 0 156 -45t62 -123q0 -51 -30 -99.5t-82.5 -88.5t-112 -72t-131.5 -60h274z" />
    <glyph glyph-name="three.onum" unicode="&#xf733;" horiz-adv-x="553" 
d="M222 -127q-86 0 -157 38.5t-98 100.5l60 47q25 -52 78.5 -82t115.5 -30q73 0 116 39.5t43 100.5q0 46 -39 74.5t-109 28.5q-53 0 -64 -1l17 76q11 -1 91 -1q68 0 114 30t46 88q0 46 -41.5 76t-110.5 30q-96 0 -169 -66l-35 57q85 83 210 83q103 0 166.5 -44.5
t63.5 -123.5q0 -71 -55 -118t-125 -53q53 -12 87.5 -49t34.5 -94q0 -87 -68 -147t-172 -60z" />
    <glyph glyph-name="four.onum" unicode="&#xf734;" horiz-adv-x="558" 
d="M483 55h-94l-37 -172h-83l37 172h-316l15 69l380 426h114l-93 -422h93zM323 128l76 345l-310 -345h234z" />
    <glyph glyph-name="five.onum" unicode="&#xf735;" horiz-adv-x="590" 
d="M252 -129q-174 0 -243 133l63 50q50 -109 184 -109q70 0 118.5 46t48.5 114q0 60 -42 93.5t-109 33.5q-74 0 -138 -48l-55 27l75 339h408l-16 -74h-325l-49 -220q56 45 140 45t141 -50t57 -138q0 -100 -73 -171t-185 -71z" />
    <glyph glyph-name="six.onum" unicode="&#xf736;" horiz-adv-x="591" 
d="M283 -12q-109 0 -168.5 64.5t-59.5 181.5q0 73 20.5 148.5t58.5 142.5t103.5 109.5t145.5 42.5q138 0 201 -100l-56 -56q-43 82 -150 82q-88 0 -144.5 -73.5t-82.5 -177.5q-5 -15 -5 -20q30 33 83 59t109 26q89 0 145.5 -47.5t56.5 -127.5q0 -104 -74.5 -179t-182.5 -75z
M284 62q72 0 120.5 50.5t48.5 118.5q0 54 -40.5 85.5t-107.5 31.5q-94 0 -168 -82q-2 -18 -2 -48q0 -70 39.5 -113t109.5 -43z" />
    <glyph glyph-name="seven.onum" unicode="&#xf737;" horiz-adv-x="515" 
d="M135 -117h-96l397 593h-355l17 74h455l-13 -57z" />
    <glyph glyph-name="eight.onum" unicode="&#xf738;" horiz-adv-x="581" 
d="M271 -12q-104 0 -172.5 44.5t-68.5 125.5q0 73 54.5 124.5t147.5 70.5q-48 19 -82 55.5t-34 85.5q0 88 72 135.5t165 47.5t160.5 -44t67.5 -120q0 -71 -52 -117.5t-137 -59.5q52 -24 88.5 -63.5t36.5 -94.5q0 -83 -72 -136.5t-174 -53.5zM320 378q77 5 124.5 37t47.5 85
q0 43 -42 72.5t-102 29.5q-62 0 -105.5 -31.5t-43.5 -84.5q0 -38 38.5 -68t82.5 -40zM274 62q63 0 111 34t48 90q0 44 -40 76t-88 45q-81 -5 -134 -41.5t-53 -92.5q0 -53 44.5 -82t111.5 -29z" />
    <glyph glyph-name="nine.onum" unicode="&#xf739;" horiz-adv-x="591" 
d="M321 562q109 0 168.5 -64.5t59.5 -181.5q0 -73 -20.5 -148.5t-59 -142.5t-104 -109.5t-145.5 -42.5q-138 0 -201 100l56 56q43 -82 150 -82q47 0 86 22t66 59.5t45 79t30 89.5q4 13 6 21q-31 -33 -84 -59.5t-108 -26.5q-89 0 -146 48t-57 128q0 103 75 178.5t183 75.5z
M319 488q-72 0 -121 -50.5t-49 -118.5q0 -54 41.5 -86t107.5 -32q92 0 169 82q1 9 1 48q0 70 -39.5 113.5t-109.5 43.5z" />
    <glyph glyph-name="question.smcp" unicode="&#xf73f;" horiz-adv-x="396" 
d="M415 437q0 -40 -22 -69t-54 -46t-64 -31t-54 -31.5t-22 -39.5q0 -19 19 -35l-61 -22q-25 27 -25 55q0 33 21 57t50.5 38.5t59.5 28.5t51 35t21 49q0 33 -29 51.5t-78 18.5q-81 0 -144 -63l-32 51q79 77 185 77q80 0 129 -35t49 -89zM142 -10q-21 0 -35.5 14.5t-14.5 35.5
q0 25 17.5 42.5t42.5 17.5q21 0 35.5 -15t14.5 -36q0 -25 -17.5 -42t-42.5 -17z" />
    <glyph glyph-name="A.smcp" unicode="&#xf761;" 
d="M306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233z" />
    <glyph glyph-name="B.smcp" unicode="&#xf762;" horiz-adv-x="554" 
d="M300 0h-279l121 550h234q70 0 115.5 -32.5t45.5 -92.5q0 -52 -33.5 -94.5t-91.5 -52.5q36 -12 58 -43.5t22 -68.5q0 -73 -51.5 -119.5t-140.5 -46.5zM114 69h185q48 0 80 28.5t32 71.5q0 34 -23.5 56t-65.5 22h-168zM339 316h14q46 0 73.5 28.5t27.5 66.5q0 35 -26 52.5
t-68 17.5h-154l-38 -165h171z" />
    <glyph glyph-name="C.smcp" unicode="&#xf763;" horiz-adv-x="582" 
d="M318 -12q-120 0 -195.5 69t-75.5 181q0 132 96.5 228t229.5 96q73 0 132 -34.5t88 -90.5l-68 -30q-22 39 -64 62.5t-90 23.5q-96 0 -169.5 -73.5t-73.5 -177.5q0 -83 54.5 -134t138.5 -51q93 0 159 71l56 -40q-92 -100 -218 -100z" />
    <glyph glyph-name="D.smcp" unicode="&#xf764;" horiz-adv-x="615" 
d="M233 0h-212l121 550h177q114 0 188 -68t74 -169q0 -57 -21 -111t-61.5 -100t-109.5 -74t-156 -28zM238 69h9q118 0 185.5 72t67.5 167q0 77 -52.5 125t-139.5 48h-103l-91 -412h124z" />
    <glyph glyph-name="E.smcp" unicode="&#xf765;" horiz-adv-x="509" 
d="M403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304z" />
    <glyph glyph-name="F.smcp" unicode="&#xf766;" horiz-adv-x="491" 
d="M99 0h-78l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298z" />
    <glyph glyph-name="G.smcp" unicode="&#xf767;" horiz-adv-x="626" 
d="M321 -12q-118 0 -196.5 67.5t-78.5 178.5q0 131 94.5 229.5t233.5 98.5q71 0 130.5 -32t92.5 -84l-68 -35q-22 34 -65.5 58t-93.5 24q-102 0 -171.5 -76t-69.5 -178q0 -80 55.5 -131t139.5 -51q78 0 130 38l29 127h-170l14 65h248l-51 -231q-92 -68 -203 -68z" />
    <glyph glyph-name="H.smcp" unicode="&#xf768;" horiz-adv-x="635" 
d="M506 0h-78l55 248h-329l-55 -248h-78l121 550h78l-51 -233h329l51 233h78z" />
    <glyph glyph-name="I.smcp" unicode="&#xf769;" horiz-adv-x="229" 
d="M99 0h-78l121 550h78z" />
    <glyph glyph-name="J.smcp" unicode="&#xf76a;" horiz-adv-x="422" 
d="M129 -12q-111 0 -160 76l51 52q32 -59 103 -59q103 0 128 113l84 380h78l-85 -386q-20 -91 -69 -133.5t-130 -42.5z" />
    <glyph glyph-name="K.smcp" unicode="&#xf76b;" horiz-adv-x="534" 
d="M466 0h-95l-163 244l-68 -59l-41 -185h-78l121 550h78l-59 -272l306 272h99l-299 -261z" />
    <glyph glyph-name="L.smcp" unicode="&#xf76c;" horiz-adv-x="458" 
d="M366 0h-345l121 550h78l-106 -481h267z" />
    <glyph glyph-name="M.smcp" unicode="&#xf76d;" horiz-adv-x="719" 
d="M590 0h-79l97 438l-285 -438h-34l-92 438l-98 -438h-78l121 550h106l86 -413l268 413h109z" />
    <glyph glyph-name="N.smcp" unicode="&#xf76e;" horiz-adv-x="632" 
d="M503 0h-79l-227 444l-98 -444h-78l121 550h82l226 -435l95 435h79z" />
    <glyph glyph-name="O.smcp" unicode="&#xf76f;" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5z" />
    <glyph glyph-name="P.smcp" unicode="&#xf770;" horiz-adv-x="522" 
d="M99 0h-78l121 550h210q80 0 127.5 -40t47.5 -105q0 -78 -56 -135t-147 -57h-177zM162 282h162q55 0 88 34t33 84q0 38 -27 59.5t-75 21.5h-137z" />
    <glyph glyph-name="Q.smcp" unicode="&#xf771;" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -75 -33.5 -143.5t-91.5 -113.5l31 -45l-57 -38l-32 47q-63 -30 -132 -30zM314 57q47 0 90 19l-62 91l57 39l63 -93q85 77 85 194q0 81 -50 133.5t-136 52.5q-102 0 -168 -75
t-66 -175q0 -81 50.5 -133.5t136.5 -52.5z" />
    <glyph glyph-name="R.smcp" unicode="&#xf772;" horiz-adv-x="545" 
d="M446 0h-90l-97 213h-112l-48 -213h-78l121 550h212q78 0 126 -41t48 -105q0 -74 -49 -127.5t-140 -58.5zM312 282h8q59 0 92.5 33.5t33.5 82.5q0 37 -28 60t-72 23h-140l-44 -199h150z" />
    <glyph glyph-name="S.smcp" unicode="&#xf773;" horiz-adv-x="504" 
d="M240 -12q-162 0 -237 97l53 56q27 -39 77.5 -61.5t110.5 -22.5q64 0 93.5 28.5t29.5 69.5q0 30 -29.5 51.5t-71.5 36.5t-84 32t-71.5 49t-29.5 77q0 66 60.5 113.5t145.5 47.5q66 0 122 -25t88 -65l-51 -54q-23 34 -69 54.5t-93 20.5q-51 0 -85.5 -25.5t-34.5 -63.5
q0 -28 29.5 -47.5t71.5 -34t84 -32t71.5 -51.5t29.5 -82q0 -69 -53.5 -119t-156.5 -50z" />
    <glyph glyph-name="T.smcp" unicode="&#xf774;" horiz-adv-x="472" 
d="M220 0h-78l106 481h-170l15 69h419l-15 -69h-171z" />
    <glyph glyph-name="U.smcp" unicode="&#xf775;" horiz-adv-x="623" 
d="M295 -12q-111 0 -172 48.5t-61 131.5q0 19 5 44l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-25 -112 -82 -168t-163 -56z" />
    <glyph glyph-name="V.smcp" unicode="&#xf776;" 
d="M279 0h-94l-111 550h87l88 -463l294 463h89z" />
    <glyph glyph-name="W.smcp" unicode="&#xf777;" horiz-adv-x="764" 
d="M534 0h-82l-29 431l-220 -431h-82l-44 550h86l25 -443l227 443h66l30 -444l222 444h87z" />
    <glyph glyph-name="X.smcp" unicode="&#xf778;" horiz-adv-x="567" 
d="M504 0h-92l-130 229l-232 -229h-95l286 282l-153 268h92l121 -215l215 215h96l-271 -267z" />
    <glyph glyph-name="Y.smcp" unicode="&#xf779;" horiz-adv-x="547" 
d="M260 0h-78l51 233l-159 317h89l122 -250l229 250h93l-296 -317z" />
    <glyph glyph-name="Z.smcp" unicode="&#xf77a;" horiz-adv-x="516" 
d="M423 0h-438l13 59l417 422h-324l15 69h431l-13 -59l-416 -422h330z" />
    <glyph glyph-name="Agrave.smcp" unicode="&#xf7e0;" 
d="M306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM424 606h-57l-137 144h78z" />
    <glyph glyph-name="Aacute.smcp" unicode="&#xf7e1;" 
d="M566 750l-201 -144h-59l180 144h80zM306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233z" />
    <glyph glyph-name="Acircumflex.smcp" unicode="&#xf7e2;" 
d="M497 606h-49l-60 106l-103 -106h-53l127 144h72zM306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233z" />
    <glyph glyph-name="Atilde.smcp" unicode="&#xf7e3;" 
d="M306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM437 604q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z
" />
    <glyph glyph-name="Adieresis.smcp" unicode="&#xf7e4;" 
d="M526 659q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM320 659q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM306 550h94l111 -550h-85l-25 120h-281l-77 -120
h-90zM392 189l-54 279l-179 -279h233z" />
    <glyph glyph-name="Aring.smcp" unicode="&#xf7e5;" 
d="M371 573q-39 0 -64 25t-25 64q0 44 32 76t76 32q39 0 64 -25t25 -64q0 -44 -32 -76t-76 -32zM374 613q26 0 45.5 19t19.5 45q0 24 -14 38.5t-38 14.5q-26 0 -45.5 -19t-19.5 -45q0 -23 14.5 -38t37.5 -15zM306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189
l-54 279l-179 -279h233z" />
    <glyph glyph-name="AE.smcp" unicode="&#xf7e6;" horiz-adv-x="821" 
d="M716 0h-383l26 120h-214l-102 -120h-91l472 550h413l-15 -69h-305l-37 -165h298l-15 -69h-298l-39 -178h305zM374 189l62 279l-237 -279h175z" />
    <glyph glyph-name="Ccedilla.smcp" unicode="&#xf7e7;" horiz-adv-x="582" 
d="M258 -191q-68 0 -108 38l27 36q33 -35 83 -35q29 0 45 13t16 34q0 30 -37 30q-20 0 -35 -17l-29 19l39 66q-96 16 -154 82t-58 163q0 132 96.5 228t229.5 96q73 0 132 -34.5t88 -90.5l-68 -30q-22 39 -64 62.5t-90 23.5q-96 0 -169.5 -73.5t-73.5 -177.5q0 -83 54.5 -134
t138.5 -51q93 0 159 71l56 -40q-92 -100 -218 -100h-17l-27 -43q15 11 34 11q26 0 42 -15.5t16 -43.5q0 -39 -30 -63.5t-78 -24.5z" />
    <glyph glyph-name="Egrave.smcp" unicode="&#xf7e8;" horiz-adv-x="509" 
d="M403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304zM404 606h-57l-137 144h78z" />
    <glyph glyph-name="Eacute.smcp" unicode="&#xf7e9;" horiz-adv-x="509" 
d="M547 750l-201 -144h-59l180 144h80zM403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304z" />
    <glyph glyph-name="Ecircumflex.smcp" unicode="&#xf7ea;" horiz-adv-x="509" 
d="M479 606h-49l-60 106l-103 -106h-53l127 144h72zM403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304z" />
    <glyph glyph-name="Edieresis.smcp" unicode="&#xf7eb;" horiz-adv-x="509" 
d="M507 659q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM301 659q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM403 0h-382l121 550h382l-15 -69h-303l-37 -165h298
l-15 -69h-298l-40 -178h304z" />
    <glyph glyph-name="Igrave.smcp" unicode="&#xf7ec;" horiz-adv-x="229" 
d="M99 0h-78l121 550h78zM250 606h-57l-137 144h78z" />
    <glyph glyph-name="Iacute.smcp" unicode="&#xf7ed;" horiz-adv-x="229" 
d="M395 750l-201 -144h-59l180 144h80zM99 0h-78l121 550h78z" />
    <glyph glyph-name="Icircumflex.smcp" unicode="&#xf7ee;" horiz-adv-x="229" 
d="M327 606h-49l-60 106l-103 -106h-53l127 144h72zM99 0h-78l121 550h78z" />
    <glyph glyph-name="Idieresis.smcp" unicode="&#xf7ef;" horiz-adv-x="229" 
d="M355 659q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM149 659q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM99 0h-78l121 550h78z" />
    <glyph glyph-name="Eth.smcp" unicode="&#xf7f0;" horiz-adv-x="615" 
d="M233 0h-212l55 251h-63l12 57h63l54 242h177q114 0 188 -68t74 -169q0 -57 -21 -111t-61.5 -100t-109.5 -74t-156 -28zM247 69q118 0 185.5 72t67.5 167q0 77 -52.5 125t-139.5 48h-103l-39 -173h133l-12 -57h-133l-40 -182h133z" />
    <glyph glyph-name="Ntilde.smcp" unicode="&#xf7f1;" horiz-adv-x="632" 
d="M503 0h-79l-227 444l-98 -444h-78l121 550h82l226 -435l95 435h79zM468 604q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="Ograve.smcp" unicode="&#xf7f2;" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM469 606h-57l-137 144h78z" />
    <glyph glyph-name="Oacute.smcp" unicode="&#xf7f3;" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM611 750l-201 -144h-59l180 144
h80z" />
    <glyph glyph-name="Ocircumflex.smcp" unicode="&#xf7f4;" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM545 606h-49l-60 106l-103 -106
h-53l127 144h72z" />
    <glyph glyph-name="Otilde.smcp" unicode="&#xf7f5;" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM483 604q-25 0 -42 14.5
t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="Odieresis.smcp" unicode="&#xf7f6;" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM572 659q0 -20 -15.5 -36
t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM366 659q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29z" />
    <glyph glyph-name="Oslash.smcp" unicode="&#xf7f8;" horiz-adv-x="662" 
d="M312 -12q-101 0 -169 49l-36 -37h-70l68 71q-58 67 -58 168q0 130 91 226.5t224 96.5q97 0 167 -49l35 37h70l-68 -70q61 -69 61 -169q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 67 -35 116l-316 -330q47 -36 118 -36zM127 243q0 -66 34 -114l315 329
q-48 35 -115 35q-102 0 -168 -75t-66 -175z" />
    <glyph glyph-name="Ugrave.smcp" unicode="&#xf7f9;" horiz-adv-x="623" 
d="M295 -12q-111 0 -172 48.5t-61 131.5q0 19 5 44l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-25 -112 -82 -168t-163 -56zM448 606h-57l-137 144h78z" />
    <glyph glyph-name="Uacute.smcp" unicode="&#xf7fa;" horiz-adv-x="623" 
d="M592 750l-201 -144h-59l180 144h80zM295 -12q-111 0 -172 48.5t-61 131.5q0 19 5 44l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-25 -112 -82 -168t-163 -56z" />
    <glyph glyph-name="Ucircumflex.smcp" unicode="&#xf7fb;" horiz-adv-x="623" 
d="M523 606h-49l-60 106l-103 -106h-53l127 144h72zM295 -12q-111 0 -172 48.5t-61 131.5q0 19 5 44l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-25 -112 -82 -168t-163 -56z" />
    <glyph glyph-name="Udieresis.smcp" unicode="&#xf7fc;" horiz-adv-x="623" 
d="M552 659q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM346 659q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM295 -12q-111 0 -172 48.5t-61 131.5q0 19 5 44
l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-25 -112 -82 -168t-163 -56z" />
    <glyph glyph-name="Yacute.smcp" unicode="&#xf7fd;" horiz-adv-x="547" 
d="M556 750l-201 -144h-59l180 144h80zM260 0h-78l51 233l-159 317h89l122 -250l229 250h93l-296 -317z" />
    <glyph glyph-name="Thorn.smcp" unicode="&#xf7fe;" horiz-adv-x="522" 
d="M99 0h-78l121 550h78l-19 -93h130q78 0 126 -39t48 -105q0 -78 -54.5 -135t-147.5 -57h-177zM291 190h12q55 0 88 34t33 84q0 39 -28 59.5t-75 20.5h-136l-44 -198h150z" />
    <glyph glyph-name="Ydieresis.smcp" unicode="&#xf7ff;" horiz-adv-x="547" 
d="M516 659q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM310 659q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM260 0h-78l51 233l-159 317h89l122 -250l229 250h93
l-296 -317z" />
    <glyph glyph-name="uniF8FF" unicode="&#xf8ff;" horiz-adv-x="667" 
d="M206 153l17 75h-83q-34 0 -58.5 -25t-24.5 -59q0 -28 18 -46.5t46 -18.5q69 0 85 74zM269 439l14 65q2 7 2 20q0 29 -18 46.5t-48 17.5q-34 0 -58 -23.5t-24 -57.5q0 -31 19 -49.5t50 -18.5h63zM269 265h137l31 137h-137zM569 160q0 31 -19 49.5t-51 18.5h-62l-15 -65
q-2 -14 -2 -20q0 -30 18 -47t48 -17q34 0 58.5 23.5t24.5 57.5zM648 523q0 28 -17.5 46.5t-45.5 18.5q-33 0 -55.5 -20t-29.5 -54l-17 -75h82q34 0 58.5 25t24.5 59zM607 160q0 -48 -39 -83t-89 -35q-42 0 -70 28t-28 70q0 14 3 29l14 59h-137l-19 -83q-11 -46 -45 -74.5
t-80 -28.5q-41 0 -70 28t-29 69q0 49 38 87.5t87 38.5h87l31 137h-62q-45 0 -72.5 29t-27.5 76q0 48 38.5 83t88.5 35q42 0 70 -28t28 -70q0 -14 -3 -29l-14 -59h137l19 83q11 46 45 74.5t80 28.5q42 0 71 -28t29 -69q0 -50 -38 -88t-87 -38h-87l-31 -137h59
q46 0 74.5 -29.5t28.5 -75.5z" />
    <glyph glyph-name="zero.pzero" horiz-adv-x="612" 
d="M278 -12q-99 0 -157 64l-44 -52h-67l81 96q-32 62 -32 152q0 76 22 151.5t61.5 137.5t100 101t130.5 39q98 0 155 -63l45 53h67l-81 -97q32 -62 32 -152q0 -103 -36.5 -200t-110 -163.5t-166.5 -66.5zM285 62q66 0 118 58.5t77.5 140t25.5 162.5q0 41 -9 75l-322 -382
q37 -54 110 -54zM143 243q0 -42 9 -75l322 382q-35 53 -109 53q-65 0 -117.5 -58.5t-78.5 -139.5t-26 -162z" />
    <glyph glyph-name="zero.tonumzero" horiz-adv-x="598" 
d="M567 325q0 -54 -18.5 -111.5t-53 -108.5t-91 -84t-123.5 -33q-106 0 -166 52l-39 -40h-69l74 77q-39 59 -39 147q0 74 31 149.5t99 132t157 56.5q104 0 165 -53l39 41h70l-75 -79q39 -59 39 -146zM482 320q0 52 -16 87l-295 -308q40 -37 110 -37q64 0 112 45t68.5 102
t20.5 111zM128 229q0 -51 15 -87l294 308q-40 38 -109 38q-64 0 -111.5 -45t-68 -102t-20.5 -112z" />
    <glyph glyph-name="zero.onumzero" horiz-adv-x="612" 
d="M573 325q0 -54 -18.5 -111.5t-53 -108.5t-91 -84t-123.5 -33q-106 0 -166 52l-39 -40h-69l74 77q-39 59 -39 147q0 74 31 149.5t99 132t157 56.5q104 0 165 -53l39 41h70l-75 -79q39 -59 39 -146zM488 320q0 52 -16 87l-295 -308q40 -37 110 -37q64 0 112 45t68.5 102
t20.5 111zM134 229q0 -51 15 -87l294 308q-40 38 -109 38q-64 0 -111.5 -45t-68 -102t-20.5 -112z" />
    <glyph glyph-name="G.alt1" horiz-adv-x="713" 
d="M355 -12q-129 0 -212 80.5t-83 211.5q0 175 114 286.5t274 111.5q86 0 153.5 -40.5t102.5 -105.5l-75 -35q-26 51 -78.5 79t-114.5 28q-113 0 -200.5 -90.5t-87.5 -229.5q0 -100 60 -161t158 -61q97 0 163 58t94 149h-274l16 74h361q-31 -169 -126.5 -262t-244.5 -93z
" />
    <glyph glyph-name="a.alt1" horiz-adv-x="527" 
d="M403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90
q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24z" />
    <glyph glyph-name="f.alt1" horiz-adv-x="283" 
d="M117 0h-75l92 417h-80l15 66h80l8 37q35 157 158 157q50 0 87 -23l-29 -54q-19 15 -52 15q-69 0 -89 -95l-8 -37h98l-15 -66h-98z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="268" 
d="M151 -12q-51 0 -81 21.5t-30 60.5q0 16 3 34l125 563h75l-121 -548q-3 -15 -3 -23q0 -41 50 -41q28 0 46 16l8 -60q-27 -23 -72 -23z" />
    <glyph glyph-name="y.alt1" horiz-adv-x="551" 
d="M188 -196q-146 0 -216 82l49 51q47 -71 167 -71q128 0 162 147l14 65q-88 -80 -169 -80q-63 0 -103.5 29t-40.5 89q0 16 5 42l72 325h75l-68 -307q-5 -23 -5 -35q0 -77 99 -77q79 0 149 76l76 343h75l-103 -465q-49 -214 -238 -214z" />
    <glyph glyph-name="G.smcp.alt1" horiz-adv-x="642" 
d="M321 -12q-121 0 -197.5 69t-76.5 177q0 139 97 233.5t228 94.5q150 0 229 -119l-69 -36q-24 40 -67.5 62.5t-93.5 22.5q-97 0 -170 -71.5t-73 -177.5q0 -84 56 -135.5t140 -51.5q70 0 127 45.5t74 119.5h-212l14 65h290q-14 -129 -97.5 -213.5t-198.5 -84.5z" />
    <glyph glyph-name="Gbreve.alt1" horiz-adv-x="742" 
d="M674 793q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72zM355 -12q-129 0 -212 80.5t-83 211.5q0 175 114 286.5t274 111.5q86 0 153.5 -40.5t102.5 -105.5l-75 -35q-26 51 -78.5 79t-114.5 28q-113 0 -200.5 -90.5t-87.5 -229.5
q0 -100 60 -161t158 -61q97 0 163 58t94 149h-274l16 74h361q-31 -169 -126.5 -262t-244.5 -93z" />
    <glyph glyph-name="Gcommaaccent.alt1" horiz-adv-x="742" 
d="M349 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM355 -12q-129 0 -212 80.5t-83 211.5q0 175 114 286.5t274 111.5q86 0 153.5 -40.5t102.5 -105.5l-75 -35q-26 51 -78.5 79
t-114.5 28q-113 0 -200.5 -90.5t-87.5 -229.5q0 -100 60 -161t158 -61q97 0 163 58t94 149h-274l16 74h361q-31 -169 -126.5 -262t-244.5 -93z" />
    <glyph glyph-name="Gcircumflex.alt1" horiz-adv-x="742" 
d="M621 723h-49l-60 106l-103 -106h-53l127 144h72zM355 -12q-129 0 -212 80.5t-83 211.5q0 175 114 286.5t274 111.5q86 0 153.5 -40.5t102.5 -105.5l-75 -35q-26 51 -78.5 79t-114.5 28q-113 0 -200.5 -90.5t-87.5 -229.5q0 -100 60 -161t158 -61q97 0 163 58t94 149h-274
l16 74h361q-31 -169 -126.5 -262t-244.5 -93z" />
    <glyph glyph-name="Gdotaccent.alt1" horiz-adv-x="742" 
d="M546 774q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30zM355 -12q-129 0 -212 80.5t-83 211.5q0 175 114 286.5t274 111.5q86 0 153.5 -40.5t102.5 -105.5l-75 -35q-26 51 -78.5 79t-114.5 28q-113 0 -200.5 -90.5
t-87.5 -229.5q0 -100 60 -161t158 -61q97 0 163 58t94 149h-274l16 74h361q-31 -169 -126.5 -262t-244.5 -93z" />
    <glyph glyph-name="aacute.alt1" horiz-adv-x="527" 
d="M530 700l-201 -144h-59l180 144h80zM403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45z
M219 42q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24z" />
    <glyph glyph-name="abreve.alt1" horiz-adv-x="527" 
d="M403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90
q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM514 626q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="acircumflex.alt1" horiz-adv-x="527" 
d="M462 556h-49l-60 106l-103 -106h-53l127 144h72zM403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5
q0 -15 -5 -45zM219 42q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24z" />
    <glyph glyph-name="adieresis.alt1" horiz-adv-x="527" 
d="M489 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM283 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM403 0h-75l11 52q-64 -64 -149 -64
q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5
q0 -44 31 -68t82 -24z" />
    <glyph glyph-name="agrave.alt1" horiz-adv-x="527" 
d="M403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90
q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM387 556h-57l-137 144h78z" />
    <glyph glyph-name="amacron.alt1" horiz-adv-x="527" 
d="M514 572h-362l10 48h362zM401 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM217 42
q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24z" />
    <glyph glyph-name="aogonek.alt1" horiz-adv-x="527" 
d="M403 0q-101 -45 -101 -107q0 -40 43 -40q35 0 55 41l31 -24q-34 -56 -91 -56q-35 0 -58 19t-23 53q0 68 70 114h-1l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57
l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24z" />
    <glyph glyph-name="aring.alt1" horiz-adv-x="527" 
d="M339 543q-39 0 -64 25t-25 64q0 44 32 76t76 32q39 0 64 -25t25 -64q0 -44 -32 -76t-76 -32zM342 583q26 0 45.5 19t19.5 45q0 24 -14 38.5t-38 14.5q-26 0 -45.5 -19t-19.5 -45q0 -23 14.5 -38t37.5 -15zM403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5
t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68
t82 -24z" />
    <glyph glyph-name="atilde.alt1" horiz-adv-x="527" 
d="M403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90
q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM400 554q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EA1.alt1" horiz-adv-x="527" 
d="M403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90
q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM223 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EA3.alt1" horiz-adv-x="527" 
d="M403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90
q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM295 589l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EA5.alt1" horiz-adv-x="527" 
d="M460 556h-49l-60 106l-103 -106h-53l127 144h72zM695 770l-201 -144h-59l180 144h80zM403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54
q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24z" />
    <glyph glyph-name="uni1EAD.alt1" horiz-adv-x="527" 
d="M461 556h-49l-60 106l-103 -106h-53l127 144h72zM403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5
q0 -15 -5 -45zM219 42q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM223 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EA7.alt1" horiz-adv-x="527" 
d="M462 556h-49l-60 106l-103 -106h-53l127 144h72zM403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5
q0 -15 -5 -45zM219 42q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM251 626h-57l-137 144h78z" />
    <glyph glyph-name="uni1EA9.alt1" horiz-adv-x="527" 
d="M460 556h-49l-60 106l-103 -106h-53l127 144h72zM403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5
q0 -15 -5 -45zM219 42q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM388 729l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EAF.alt1" horiz-adv-x="527" 
d="M524 766l-201 -144h-59l180 144h80zM403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45z
M219 42q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM510 606q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="uni1EAB.alt1" horiz-adv-x="527" 
d="M454 523h-49l-60 106l-103 -106h-53l127 144h72zM403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5
q0 -15 -5 -45zM219 42q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM429 682q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46
q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EB1.alt1" horiz-adv-x="527" 
d="M403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90
q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM415 622h-57l-137 144h78zM511 611q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="uni1EB3.alt1" horiz-adv-x="527" 
d="M403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90
q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM316 687l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29zM511 611q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72
q88 0 154 72z" />
    <glyph glyph-name="uni1EB5.alt1" horiz-adv-x="527" 
d="M511 611q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72zM403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54
q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM425 667q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5
t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EB7.alt1" horiz-adv-x="527" 
d="M403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90
q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24zM223 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30zM514 626q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="895" 
d="M844 217h-372q-1 -2 -1 -23q0 -61 39 -102.5t115 -41.5q69 0 127 47l26 -52q-70 -57 -152 -57q-157 0 -204 119q-29 -55 -84.5 -87t-119.5 -32q-80 0 -135.5 36t-55.5 104q0 82 59.5 130.5t134.5 48.5q117 0 178 -72l12 53q6 28 6 55q0 42 -37.5 65.5t-93.5 23.5
q-88 0 -155 -57l-25 54q79 66 193 66q72 0 117.5 -28t51.5 -87q29 51 80 83t114 32q88 0 139.5 -56t51.5 -155q0 -40 -9 -67zM482 272h301q1 7 1 19q0 65 -34 103.5t-99 38.5q-64 0 -108 -48.5t-61 -112.5zM385 168l5 22q-17 29 -59 46t-88 17q-55 0 -96 -33.5t-41 -81.5
q0 -41 34.5 -64.5t84.5 -23.5q55 0 102 34t58 84z" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="895" 
d="M844 217h-372q-1 -2 -1 -23q0 -61 39 -102.5t115 -41.5q69 0 127 47l26 -52q-70 -57 -152 -57q-157 0 -204 119q-29 -55 -84.5 -87t-119.5 -32q-80 0 -135.5 36t-55.5 104q0 82 59.5 130.5t134.5 48.5q117 0 178 -72l12 53q6 28 6 55q0 42 -37.5 65.5t-93.5 23.5
q-88 0 -155 -57l-25 54q79 66 193 66q72 0 117.5 -28t51.5 -87q29 51 80 83t114 32q88 0 139.5 -56t51.5 -155q0 -40 -9 -67zM482 272h301q1 7 1 19q0 65 -34 103.5t-99 38.5q-64 0 -108 -48.5t-61 -112.5zM385 168l5 22q-17 29 -59 46t-88 17q-55 0 -96 -33.5t-41 -81.5
q0 -41 34.5 -64.5t84.5 -23.5q55 0 102 34t58 84zM693 700l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="fl.alt1" horiz-adv-x="552" 
d="M117 0h-75l92 417h-80l15 66h80l8 37q35 157 158 157q50 0 87 -23l-29 -54q-19 15 -52 15q-69 0 -89 -95l-8 -37h98l-15 -66h-98zM434 -12q-51 0 -81 21.5t-30 60.5q0 16 3 34l125 563h75l-121 -548q-3 -15 -3 -23q0 -41 50 -41q28 0 46 16l8 -60q-27 -23 -72 -23z" />
    <glyph glyph-name="f_f_l.alt1" horiz-adv-x="835" 
d="M400 0h-75l92 417h-80l15 66h80l8 37q35 157 158 157q50 0 87 -23l-29 -54q-19 15 -52 15q-69 0 -89 -95l-8 -37h98l-15 -66h-98zM718 -12q-51 0 -81 21.5t-30 60.5q0 16 3 34l125 563h75l-121 -548q-3 -15 -3 -23q0 -41 50 -41q28 0 46 16l8 -60q-27 -23 -72 -23zM117 0
h-75l92 418h-80l15 65h80l8 37q35 157 161 157q64 0 105 -41l-40 -45q-24 24 -62 24q-69 0 -89 -95l-8 -37h98l-15 -65h-98z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="268" 
d="M418 869l-201 -144h-59l180 144h80zM151 -12q-51 0 -81 21.5t-30 60.5q0 16 3 34l125 563h75l-121 -548q-3 -15 -3 -23q0 -41 50 -41q28 0 46 16l8 -60q-27 -23 -72 -23z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="268" 
d="M85 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM151 -12q-51 0 -81 21.5t-30 60.5q0 16 3 34l125 563h75l-121 -548q-3 -15 -3 -23q0 -41 50 -41q28 0 46 16l8 -60
q-27 -23 -72 -23z" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="295" 
d="M393 630q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM151 -12q-51 0 -81 21.5t-30 60.5q0 16 3 34l125 563h75l-121 -548q-3 -15 -3 -23q0 -41 50 -41q28 0 46 16l8 -60
q-27 -23 -72 -23z" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="307" 
d="M151 -12q-51 0 -81 21.5t-30 60.5q0 16 3 34l125 563h75l-121 -548q-3 -15 -3 -23q0 -41 50 -41q28 0 46 16l8 -60q-27 -23 -72 -23zM261 190q-21 0 -35.5 14.5t-14.5 35.5q0 25 17.5 42.5t42.5 17.5q21 0 35.5 -15t14.5 -36q0 -25 -17.5 -42t-42.5 -17z" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="299" 
d="M6 228l14 60l107 54l72 325h75l-63 -282l109 55l-14 -60l-108 -55l-45 -206q-3 -15 -3 -23q0 -41 50 -41q28 0 46 16l8 -60q-27 -23 -72 -23q-51 0 -81 21.5t-30 60.5q0 16 3 34l39 178z" />
    <glyph glyph-name="yacute.alt1" horiz-adv-x="551" 
d="M545 700l-201 -144h-59l180 144h80zM188 -196q-146 0 -216 82l49 51q47 -71 167 -71q128 0 162 147l14 65q-88 -80 -169 -80q-63 0 -103.5 29t-40.5 89q0 16 5 42l72 325h75l-68 -307q-5 -23 -5 -35q0 -77 99 -77q79 0 149 76l76 343h75l-103 -465q-49 -214 -238 -214z
" />
    <glyph glyph-name="ycircumflex.alt1" horiz-adv-x="551" 
d="M476 556h-49l-60 106l-103 -106h-53l127 144h72zM188 -196q-146 0 -216 82l49 51q47 -71 167 -71q128 0 162 147l14 65q-88 -80 -169 -80q-63 0 -103.5 29t-40.5 89q0 16 5 42l72 325h75l-68 -307q-5 -23 -5 -35q0 -77 99 -77q79 0 149 76l76 343h75l-103 -465
q-49 -214 -238 -214z" />
    <glyph glyph-name="ydieresis.alt1" horiz-adv-x="551" 
d="M504 609q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM298 609q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29zM188 -196q-146 0 -216 82l49 51q47 -71 167 -71
q128 0 162 147l14 65q-88 -80 -169 -80q-63 0 -103.5 29t-40.5 89q0 16 5 42l72 325h75l-68 -307q-5 -23 -5 -35q0 -77 99 -77q79 0 149 76l76 343h75l-103 -465q-49 -214 -238 -214z" />
    <glyph glyph-name="ygrave.alt1" horiz-adv-x="551" 
d="M188 -196q-146 0 -216 82l49 51q47 -71 167 -71q128 0 162 147l14 65q-88 -80 -169 -80q-63 0 -103.5 29t-40.5 89q0 16 5 42l72 325h75l-68 -307q-5 -23 -5 -35q0 -77 99 -77q79 0 149 76l76 343h75l-103 -465q-49 -214 -238 -214zM405 576h-57l-137 144h78z" />
    <glyph glyph-name="uni1EA0.smcp" 
d="M306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM249 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EA2.smcp" 
d="M306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM332 642l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EA4.smcp" 
d="M497 606h-49l-60 106l-103 -106h-53l127 144h72zM732 820l-201 -144h-59l180 144h80zM306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233z" />
    <glyph glyph-name="uni1EAC.smcp" 
d="M497 606h-49l-60 106l-103 -106h-53l127 144h72zM306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM248 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EA6.smcp" 
d="M497 606h-49l-60 106l-103 -106h-53l127 144h72zM306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM286 676h-57l-137 144h78z" />
    <glyph glyph-name="uni1EA8.smcp" 
d="M498 606h-49l-60 106l-103 -106h-53l127 144h72zM306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM426 779l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EAE.smcp" 
d="M560 816l-201 -144h-59l180 144h80zM306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM547 663q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="uni1EAA.smcp" 
d="M492 573h-49l-60 106l-103 -106h-53l127 144h72zM306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM466 732q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5
t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EB0.smcp" 
d="M306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM441 672h-57l-137 144h78zM548 663q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="uni1EB2.smcp" 
d="M306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM352 737l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29zM548 663q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32
q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="uni1EB4.smcp" 
d="M548 663q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72zM306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM460 711q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46
q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EB6.smcp" 
d="M306 550h94l111 -550h-85l-25 120h-281l-77 -120h-90zM392 189l-54 279l-179 -279h233zM248 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30zM550 676q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32
q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="uni1E02.smcp" horiz-adv-x="554" 
d="M300 0h-279l121 550h234q70 0 115.5 -32.5t45.5 -92.5q0 -52 -33.5 -94.5t-91.5 -52.5q36 -12 58 -43.5t22 -68.5q0 -73 -51.5 -119.5t-140.5 -46.5zM114 69h185q48 0 80 28.5t32 71.5q0 34 -23.5 56t-65.5 22h-168zM339 316h14q46 0 73.5 28.5t27.5 66.5q0 35 -26 52.5
t-68 17.5h-154l-38 -165h171zM422 657q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E0A.smcp" horiz-adv-x="615" 
d="M233 0h-212l121 550h177q114 0 188 -68t74 -169q0 -57 -21 -111t-61.5 -100t-109.5 -74t-156 -28zM238 69h9q118 0 185.5 72t67.5 167q0 77 -52.5 125t-139.5 48h-103l-91 -412h124zM450 657q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5
q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EB8.smcp" horiz-adv-x="509" 
d="M403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304zM230 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EBA.smcp" horiz-adv-x="509" 
d="M403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304zM314 642l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EBC.smcp" horiz-adv-x="509" 
d="M403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304zM418 604q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EBE.smcp" horiz-adv-x="509" 
d="M479 606h-49l-60 106l-103 -106h-53l127 144h72zM714 820l-201 -144h-59l180 144h80zM403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304z" />
    <glyph glyph-name="uni1EC0.smcp" horiz-adv-x="509" 
d="M478 606h-49l-60 106l-103 -106h-53l127 144h72zM403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304zM267 676h-57l-137 144h78z" />
    <glyph glyph-name="uni1EC2.smcp" horiz-adv-x="509" 
d="M479 606h-49l-60 106l-103 -106h-53l127 144h72zM403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304zM406 779l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EC4.smcp" horiz-adv-x="509" 
d="M472 573h-49l-60 106l-103 -106h-53l127 144h72zM403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304zM446 732q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5
t31.5 -14.5q41 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EC6.smcp" horiz-adv-x="509" 
d="M479 606h-49l-60 106l-103 -106h-53l127 144h72zM403 0h-382l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298l-40 -178h304zM230 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E1E.smcp" horiz-adv-x="491" 
d="M405 657q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30zM99 0h-78l121 550h382l-15 -69h-303l-37 -165h298l-15 -69h-298z" />
    <glyph glyph-name="Gbreve.smcp.alt1" horiz-adv-x="642" 
d="M321 -12q-121 0 -197.5 69t-76.5 177q0 139 97 233.5t228 94.5q150 0 229 -119l-69 -36q-24 40 -67.5 62.5t-93.5 22.5q-97 0 -170 -71.5t-73 -177.5q0 -84 56 -135.5t140 -51.5q70 0 127 45.5t74 119.5h-212l14 65h290q-14 -129 -97.5 -213.5t-198.5 -84.5zM607 676
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l42 32q36 -72 123 -72q88 0 154 72z" />
    <glyph glyph-name="Gcommaaccent.smcp.alt1" horiz-adv-x="642" 
d="M309 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM321 -12q-121 0 -197.5 69t-76.5 177q0 139 97 233.5t228 94.5q150 0 229 -119l-69 -36q-24 40 -67.5 62.5t-93.5 22.5
q-97 0 -170 -71.5t-73 -177.5q0 -84 56 -135.5t140 -51.5q70 0 127 45.5t74 119.5h-212l14 65h290q-14 -129 -97.5 -213.5t-198.5 -84.5z" />
    <glyph glyph-name="Gcircumflex.smcp.alt1" horiz-adv-x="642" 
d="M554 606h-49l-60 106l-103 -106h-53l127 144h72zM321 -12q-121 0 -197.5 69t-76.5 177q0 139 97 233.5t228 94.5q150 0 229 -119l-69 -36q-24 40 -67.5 62.5t-93.5 22.5q-97 0 -170 -71.5t-73 -177.5q0 -84 56 -135.5t140 -51.5q70 0 127 45.5t74 119.5h-212l14 65h290
q-14 -129 -97.5 -213.5t-198.5 -84.5z" />
    <glyph glyph-name="Gdotaccent.smcp.alt1" horiz-adv-x="642" 
d="M321 -12q-121 0 -197.5 69t-76.5 177q0 139 97 233.5t228 94.5q150 0 229 -119l-69 -36q-24 40 -67.5 62.5t-93.5 22.5q-97 0 -170 -71.5t-73 -177.5q0 -84 56 -135.5t140 -51.5q70 0 127 45.5t74 119.5h-212l14 65h290q-14 -129 -97.5 -213.5t-198.5 -84.5zM480 657
q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E22.smcp" horiz-adv-x="635" 
d="M506 0h-78l55 248h-329l-55 -248h-78l121 550h78l-51 -233h329l51 233h78zM455 657q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EC8.smcp" horiz-adv-x="229" 
d="M99 0h-78l121 550h78zM160 642l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1ECA.smcp" horiz-adv-x="229" 
d="M99 0h-78l121 550h78zM77 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1ECC.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM295 -132q0 -21 -16 -36.5
t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1ECE.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM378 642l-29 15q26 46 74 46
q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1ED0.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM542 606h-49l-60 106l-103 -106
h-53l127 144h72zM777 820l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="uni1ED2.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM543 606h-49l-60 106l-103 -106
h-53l127 144h72zM331 676h-57l-137 144h78z" />
    <glyph glyph-name="uni1ED4.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM543 606h-49l-60 106l-103 -106
h-53l127 144h72zM471 779l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1ED6.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM536 573h-49l-60 106l-103 -106
h-53l127 144h72zM511 732q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1ED8.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q121 0 193 -70t72 -181q0 -130 -91 -226.5t-224 -96.5zM314 57q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM543 606h-49l-60 106l-103 -106
h-53l127 144h72zM294 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="Ohorn.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q145 0 217 -98q49 32 61 70q-1 -1 -7 -1q-14 0 -24 10.5t-10 26.5q0 19 14.5 33t33.5 14q18 0 30 -12.5t12 -34.5q0 -37 -26.5 -75t-64.5 -60q29 -55 29 -124q0 -130 -91 -226.5t-224 -96.5zM314 57
q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5z" />
    <glyph glyph-name="uni1EDA.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q145 0 217 -98q49 32 61 70q-1 -1 -7 -1q-14 0 -24 10.5t-10 26.5q0 19 14.5 33t33.5 14q18 0 30 -12.5t12 -34.5q0 -37 -26.5 -75t-64.5 -60q29 -55 29 -124q0 -130 -91 -226.5t-224 -96.5zM314 57
q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM611 750l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="uni1EDC.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q145 0 217 -98q49 32 61 70q-1 -1 -7 -1q-14 0 -24 10.5t-10 26.5q0 19 14.5 33t33.5 14q18 0 30 -12.5t12 -34.5q0 -37 -26.5 -75t-64.5 -60q29 -55 29 -124q0 -130 -91 -226.5t-224 -96.5zM314 57
q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM469 606h-57l-137 144h78z" />
    <glyph glyph-name="uni1EDE.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q145 0 217 -98q49 32 61 70q-1 -1 -7 -1q-14 0 -24 10.5t-10 26.5q0 19 14.5 33t33.5 14q18 0 30 -12.5t12 -34.5q0 -37 -26.5 -75t-64.5 -60q29 -55 29 -124q0 -130 -91 -226.5t-224 -96.5zM314 57
q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM378 642l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EE0.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q145 0 217 -98q49 32 61 70q-1 -1 -7 -1q-14 0 -24 10.5t-10 26.5q0 19 14.5 33t33.5 14q18 0 30 -12.5t12 -34.5q0 -37 -26.5 -75t-64.5 -60q29 -55 29 -124q0 -130 -91 -226.5t-224 -96.5zM314 57
q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM482 604q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q41 0 62 86h46
q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EE2.smcp" horiz-adv-x="662" 
d="M312 -12q-121 0 -193 69.5t-72 181.5q0 130 91 226.5t224 96.5q145 0 217 -98q49 32 61 70q-1 -1 -7 -1q-14 0 -24 10.5t-10 26.5q0 19 14.5 33t33.5 14q18 0 30 -12.5t12 -34.5q0 -37 -26.5 -75t-64.5 -60q29 -55 29 -124q0 -130 -91 -226.5t-224 -96.5zM314 57
q101 0 167 75t66 175q0 81 -50 133.5t-136 52.5q-102 0 -168 -75t-66 -175q0 -81 50.5 -133.5t136.5 -52.5zM295 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E56.smcp" horiz-adv-x="522" 
d="M99 0h-78l121 550h210q80 0 127.5 -40t47.5 -105q0 -78 -56 -135t-147 -57h-177zM162 282h162q55 0 88 34t33 84q0 38 -27 59.5t-75 21.5h-137zM419 657q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1E60.smcp" horiz-adv-x="504" 
d="M240 -12q-162 0 -237 97l53 56q27 -39 77.5 -61.5t110.5 -22.5q64 0 93.5 28.5t29.5 69.5q0 30 -29.5 51.5t-71.5 36.5t-84 32t-71.5 49t-29.5 77q0 66 60.5 113.5t145.5 47.5q66 0 122 -25t88 -65l-51 -54q-23 34 -69 54.5t-93 20.5q-51 0 -85.5 -25.5t-34.5 -63.5
q0 -28 29.5 -47.5t71.5 -34t84 -32t71.5 -51.5t29.5 -82q0 -69 -53.5 -119t-156.5 -50zM396 657q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni021A.smcp" horiz-adv-x="472" 
d="M203 -121q0 -41 -30 -82t-72 -63l-27 28q26 13 47.5 34.5t28.5 42.5h-7q-15 0 -26 11t-11 28q0 21 15.5 36t36.5 15q19 0 32 -13.5t13 -36.5zM220 0h-78l106 481h-170l15 69h419l-15 -69h-171z" />
    <glyph glyph-name="uni1E6A.smcp" horiz-adv-x="472" 
d="M220 0h-78l106 481h-170l15 69h419l-15 -69h-171zM374 657q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30z" />
    <glyph glyph-name="uni1EE4.smcp" horiz-adv-x="623" 
d="M295 -12q-111 0 -172 48.5t-61 131.5q0 19 5 44l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-25 -112 -82 -168t-163 -56zM275 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5
q18 0 30 -12t12 -30z" />
    <glyph glyph-name="Uhorn.smcp" horiz-adv-x="623" 
d="M295 -12q-111 0 -172.5 48.5t-61.5 131.5q0 14 6 44l75 338h79l-74 -331q-4 -26 -4 -38q0 -60 40.5 -92t109.5 -32q70 0 110.5 40t58.5 120l74 333h79l-10 -45q61 33 73 77q-1 -1 -7 -1q-14 0 -24 10.5t-10 26.5q0 19 14 33t33 14t31 -12.5t12 -34.5q0 -47 -37 -89
t-95 -67l-55 -250q-25 -112 -82 -168t-163 -56z" />
    <glyph glyph-name="uni1EE6.smcp" horiz-adv-x="623" 
d="M295 -12q-111 0 -172 48.5t-61 131.5q0 19 5 44l75 338h79l-74 -333q-3 -18 -3 -36q0 -60 40 -92t109 -32q70 0 110.5 40t58.5 120l74 333h79l-75 -338q-25 -112 -82 -168t-163 -56zM358 642l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61
q0 14 -10 22t-23 8q-26 0 -43 -29z" />
    <glyph glyph-name="uni1EE8.smcp" horiz-adv-x="623" 
d="M592 750l-201 -144h-59l180 144h80zM295 -12q-111 0 -172.5 48.5t-61.5 131.5q0 14 6 44l75 338h79l-74 -331q-4 -26 -4 -38q0 -60 40.5 -92t109.5 -32q70 0 110.5 40t58.5 120l74 333h79l-10 -45q61 33 73 77q-1 -1 -7 -1q-14 0 -24 10.5t-10 26.5q0 19 14 33t33 14
t31 -12.5t12 -34.5q0 -47 -37 -89t-95 -67l-55 -250q-25 -112 -82 -168t-163 -56z" />
    <glyph glyph-name="uni1EEA.smcp" horiz-adv-x="623" 
d="M448 606h-57l-137 144h78zM295 -12q-111 0 -172.5 48.5t-61.5 131.5q0 14 6 44l75 338h79l-74 -331q-4 -26 -4 -38q0 -60 40.5 -92t109.5 -32q70 0 110.5 40t58.5 120l74 333h79l-10 -45q61 33 73 77q-1 -1 -7 -1q-14 0 -24 10.5t-10 26.5q0 19 14 33t33 14t31 -12.5
t12 -34.5q0 -47 -37 -89t-95 -67l-55 -250q-25 -112 -82 -168t-163 -56z" />
    <glyph glyph-name="uni1EEC.smcp" horiz-adv-x="623" 
d="M358 642l-29 15q26 46 74 46q28 0 47.5 -16t19.5 -43q0 -36 -28 -64h-43q35 30 35 61q0 14 -10 22t-23 8q-26 0 -43 -29zM295 -12q-111 0 -172.5 48.5t-61.5 131.5q0 14 6 44l75 338h79l-74 -331q-4 -26 -4 -38q0 -60 40.5 -92t109.5 -32q70 0 110.5 40t58.5 120l74 333
h79l-10 -45q61 33 73 77q-1 -1 -7 -1q-14 0 -24 10.5t-10 26.5q0 19 14 33t33 14t31 -12.5t12 -34.5q0 -47 -37 -89t-95 -67l-55 -250q-25 -112 -82 -168t-163 -56z" />
    <glyph glyph-name="uni1EEE.smcp" horiz-adv-x="623" 
d="M295 -12q-111 0 -172.5 48.5t-61.5 131.5q0 14 6 44l75 338h79l-74 -331q-4 -26 -4 -38q0 -60 40.5 -92t109.5 -32q70 0 110.5 40t58.5 120l74 333h79l-10 -45q61 33 73 77q-1 -1 -7 -1q-14 0 -24 10.5t-10 26.5q0 19 14 33t33 14t31 -12.5t12 -34.5q0 -47 -37 -89
t-95 -67l-55 -250q-25 -112 -82 -168t-163 -56zM463 604q-25 0 -42 14.5t-24.5 31.5t-21 31.5t-31.5 14.5q-42 0 -61 -86h-46q27 134 114 134q25 0 42 -14.5t24.5 -31.5t21 -31.5t31.5 -14.5q40 0 62 86h46q-29 -134 -115 -134z" />
    <glyph glyph-name="uni1EF0.smcp" horiz-adv-x="623" 
d="M274 -132q0 -21 -16 -36.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 15.5 36.5t37.5 15.5q18 0 30 -12t12 -30zM295 -12q-111 0 -172.5 48.5t-61.5 131.5q0 14 6 44l75 338h79l-74 -331q-4 -26 -4 -38q0 -60 40.5 -92t109.5 -32q70 0 110.5 40t58.5 120l74 333h79l-10 -45
q61 33 73 77q-1 -1 -7 -1q-14 0 -24 10.5t-10 26.5q0 19 14 33t33 14t31 -12.5t12 -34.5q0 -47 -37 -89t-95 -67l-55 -250q-25 -112 -82 -168t-163 -56z" />
    <glyph glyph-name="Wacute.smcp" horiz-adv-x="764" 
d="M534 0h-82l-29 431l-220 -431h-82l-44 550h86l25 -443l227 443h66l30 -444l222 444h87zM661 750l-201 -144h-59l180 144h80z" />
    <glyph glyph-name="Wdieresis.smcp" horiz-adv-x="764" 
d="M534 0h-82l-29 431l-220 -431h-82l-44 550h86l25 -443l227 443h66l30 -444l222 444h87zM621 659q0 -20 -15.5 -36t-36.5 -16q-17 0 -29 12.5t-12 29.5q0 20 15.5 35.5t36.5 15.5q17 0 29 -12t12 -29zM415 659q0 -20 -16 -36t-36 -16q-17 0 -29.5 12.5t-12.5 29.5
q0 20 16 35.5t36 15.5q18 0 30 -12t12 -29z" />
    <glyph glyph-name="Wgrave.smcp" horiz-adv-x="764" 
d="M534 0h-82l-29 431l-220 -431h-82l-44 550h86l25 -443l227 443h66l30 -444l222 444h87zM518 606h-57l-137 144h78z" />
    <glyph glyph-name="Ygrave.smcp" horiz-adv-x="547" 
d="M260 0h-78l51 233l-159 317h89l122 -250l229 250h93l-296 -317zM412 606h-57l-137 144h78z" />
    <glyph glyph-name="afii10065.alt1" horiz-adv-x="527" 
d="M403 0h-75l11 52q-64 -64 -149 -64q-68 0 -115.5 36.5t-47.5 103.5q0 82 52.5 130.5t128.5 48.5q109 0 172 -72l18 80q3 21 3 30q0 39 -34 63t-90 24q-82 0 -146 -57l-25 54q81 66 183 66q81 0 134.5 -34.5t53.5 -103.5q0 -15 -5 -45zM219 42q78 0 131 58l20 90
q-36 63 -140 63q-55 0 -89.5 -33.5t-34.5 -85.5q0 -44 31 -68t82 -24z" />
    <hkern u1="K" u2="a" k="17" />
    <hkern u1="L" u2="a" k="14" />
    <hkern u1="P" u2="a" k="20" />
    <hkern u1="R" u2="a" k="20" />
    <hkern u1="T" u2="a" k="95" />
    <hkern u1="V" u2="a" k="63" />
    <hkern u1="W" u2="a" k="40" />
    <hkern u1="Y" u2="a" k="121" />
    <hkern u1="a" u2="&#x2122;" k="6" />
    <hkern u1="a" u2="&#x201d;" k="6" />
    <hkern u1="a" u2="&#x201c;" k="6" />
    <hkern u1="a" u2="&#x2019;" k="6" />
    <hkern u1="a" u2="&#x2018;" k="6" />
    <hkern u1="a" u2="&#x1ef2;" k="110" />
    <hkern u1="a" u2="&#x1e84;" k="43" />
    <hkern u1="a" u2="&#x1e82;" k="43" />
    <hkern u1="a" u2="&#x1e80;" k="43" />
    <hkern u1="a" u2="&#x1e6a;" k="108" />
    <hkern u1="a" u2="&#x42a;" k="108" />
    <hkern u1="a" u2="&#x427;" k="108" />
    <hkern u1="a" u2="&#x422;" k="108" />
    <hkern u1="a" u2="&#x40b;" k="108" />
    <hkern u1="a" u2="&#x402;" k="108" />
    <hkern u1="a" u2="&#x3ab;" k="110" />
    <hkern u1="a" u2="&#x3a5;" k="110" />
    <hkern u1="a" u2="&#x3a4;" k="108" />
    <hkern u1="a" u2="&#x38e;" k="110" />
    <hkern u1="a" u2="&#x2bc;" k="6" />
    <hkern u1="a" u2="&#x21a;" k="108" />
    <hkern u1="a" u2="&#x178;" k="110" />
    <hkern u1="a" u2="&#x176;" k="110" />
    <hkern u1="a" u2="&#x174;" k="43" />
    <hkern u1="a" u2="&#x166;" k="108" />
    <hkern u1="a" u2="&#x164;" k="108" />
    <hkern u1="a" u2="&#x162;" k="108" />
    <hkern u1="a" u2="&#xdd;" k="110" />
    <hkern u1="a" u2="&#xae;" k="6" />
    <hkern u1="a" u2="Y" k="110" />
    <hkern u1="a" u2="W" k="43" />
    <hkern u1="a" u2="V" k="66" />
    <hkern u1="a" u2="T" k="108" />
    <hkern u1="a" u2="&#x3f;" k="36" />
    <hkern u1="a" u2="&#x27;" k="6" />
    <hkern u1="a" u2="&#x22;" k="6" />
    <hkern u1="&#xdd;" u2="a" k="121" />
    <hkern u1="&#x136;" u2="a" k="17" />
    <hkern u1="&#x139;" u2="a" k="14" />
    <hkern u1="&#x13b;" u2="a" k="14" />
    <hkern u1="&#x141;" u2="a" k="14" />
    <hkern u1="&#x154;" u2="a" k="20" />
    <hkern u1="&#x156;" u2="a" k="20" />
    <hkern u1="&#x158;" u2="a" k="20" />
    <hkern u1="&#x162;" u2="a" k="95" />
    <hkern u1="&#x164;" u2="a" k="95" />
    <hkern u1="&#x166;" u2="a" k="95" />
    <hkern u1="&#x174;" u2="a" k="40" />
    <hkern u1="&#x176;" u2="a" k="121" />
    <hkern u1="&#x178;" u2="a" k="121" />
    <hkern u1="&#x21a;" u2="a" k="95" />
    <hkern u1="&#x38e;" u2="a" k="121" />
    <hkern u1="&#x393;" u2="a" k="95" />
    <hkern u1="&#x39a;" u2="a" k="17" />
    <hkern u1="&#x3a1;" u2="a" k="20" />
    <hkern u1="&#x3a4;" u2="a" k="95" />
    <hkern u1="&#x3a5;" u2="a" k="121" />
    <hkern u1="&#x3ab;" u2="a" k="121" />
    <hkern u1="&#x403;" u2="a" k="95" />
    <hkern u1="&#x40c;" u2="a" k="17" />
    <hkern u1="&#x40e;" u2="a" k="63" />
    <hkern u1="&#x413;" u2="a" k="95" />
    <hkern u1="&#x416;" u2="a" k="17" />
    <hkern u1="&#x41a;" u2="a" k="17" />
    <hkern u1="&#x420;" u2="a" k="20" />
    <hkern u1="&#x422;" u2="a" k="95" />
    <hkern u1="&#x423;" u2="a" k="63" />
    <hkern u1="&#x490;" u2="a" k="95" />
    <hkern u1="&#x1e56;" u2="a" k="20" />
    <hkern u1="&#x1e6a;" u2="a" k="95" />
    <hkern u1="&#x1e80;" u2="a" k="40" />
    <hkern u1="&#x1e82;" u2="a" k="40" />
    <hkern u1="&#x1e84;" u2="a" k="40" />
    <hkern u1="&#x1ef2;" u2="a" k="121" />
    <hkern u1="&#x2215;" u2="&#xf659;" k="50" />
    <hkern u1="&#x2215;" u2="&#xf656;" k="-22" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="dagger"
	k="97" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V.smcp"
	k="37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="17" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="daggerdbl"
	k="37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="question"
	k="77" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="97" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="J,Jcircumflex,afii10057"
	k="-9" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="asterisk"
	k="101" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="17" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="36" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="77" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="56" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V"
	k="64" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="73" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="9" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="57" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="11" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="17" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="v,gamma,nu"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="bullet.case"
	k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="47" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="dagger"
	k="77" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V.smcp"
	k="39" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="27" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="daggerdbl"
	k="20" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question"
	k="57" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="80" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="52" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="24" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="asterisk"
	k="64" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="9" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="110" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="55" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V"
	k="64" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="86" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="19" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="57" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question.smcp"
	k="43" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="bullet"
	k="19" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-6" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="17" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="V"
	k="13" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Jcircumflex.smcp,J.smcp"
	k="17" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="question"
	k="43" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="56" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="V"
	k="20" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="23" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="question"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="-3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="V"
	k="12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="26" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="bullet.case"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="3" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V.smcp"
	k="6" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="question"
	k="23" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="6" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="3" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="6" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="81" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="19" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V"
	k="22" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="55" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="9" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-7" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="X.smcp"
	k="3" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="question"
	k="16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="J,Jcircumflex,afii10057"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="39" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="32" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="V"
	k="33" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="56" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Jcircumflex.smcp,J.smcp"
	k="16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="24" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="17" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="27" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V.smcp"
	k="27" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="question"
	k="46" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="34" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="37" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="36" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="81" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V"
	k="50" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="76" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="37" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="13" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="7" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="17" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X.smcp"
	k="23" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="21" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="54" />
    <hkern g1="Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,OE.smcp,E.smcp,AE.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="14" />
    <hkern g1="F,uni1E1E"
	g2="J,Jcircumflex,afii10057"
	k="59" />
    <hkern g1="F,uni1E1E"
	g2="Jcircumflex.smcp,J.smcp"
	k="56" />
    <hkern g1="F,uni1E1E"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="39" />
    <hkern g1="F,uni1E1E"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="36" />
    <hkern g1="F,uni1E1E"
	g2="X.smcp"
	k="14" />
    <hkern g1="F,uni1E1E"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="F,uni1E1E"
	g2="ampersand"
	k="6" />
    <hkern g1="F,uni1E1E"
	g2="ampersand.smcp"
	k="23" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="J,Jcircumflex,afii10057"
	k="36" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="14" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="46" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="23" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="23" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="13" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="23" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="ampersand.smcp"
	k="3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="question"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="V"
	k="23" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="29" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="3" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="question"
	k="33" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="3" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="3" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="64" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="16" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="question"
	k="3" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="59" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="V"
	k="20" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="33" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="V.smcp"
	k="43" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="57" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="53" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="54" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="21" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="26" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="39" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="bullet.case"
	k="74" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="70" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="X.smcp"
	k="16" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="ampersand.smcp"
	k="3" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="34" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="x,afii10072,afii10087"
	k="26" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="43" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="17" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="35" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="17" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="V"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="46" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="bullet"
	k="47" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="37" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="dagger"
	k="130" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V.smcp"
	k="53" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="41" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="daggerdbl"
	k="101" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="question"
	k="114" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="122" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="77" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="47" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="34" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="asterisk"
	k="174" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="27" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="66" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="123" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="24" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="86" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="14" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="33" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="37" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="v,gamma,nu"
	k="57" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="57" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="bullet.case"
	k="118" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="77" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="ampersand"
	k="7" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="21" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="dagger"
	k="100" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V.smcp"
	k="88" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="48" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="daggerdbl"
	k="42" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="question"
	k="81" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="110" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="87" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="60" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="58" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="asterisk"
	k="117" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="107" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="53" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V"
	k="104" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="130" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="21" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="100" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="question.smcp"
	k="57" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="bullet"
	k="42" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="42" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="J,Jcircumflex,afii10057"
	k="109" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="V"
	k="3" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="20" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Jcircumflex.smcp,J.smcp"
	k="96" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="77" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="94" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="9" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand"
	k="26" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand.smcp"
	k="36" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="hyphen,periodcentered,endash,emdash"
	k="34" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="13" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="J,Jcircumflex,afii10057"
	k="42" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="49" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="43" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="77" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="47" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="58" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="37" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="17" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="3" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="V"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="19" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="3" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="bullet.case"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand"
	k="3" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand.smcp"
	k="13" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="6" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="V.smcp"
	k="7" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="question"
	k="37" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="7" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="55" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="57" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="V.smcp"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="V"
	k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="17" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="v,gamma,nu"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="x,afii10072,afii10087"
	k="13" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="V.smcp"
	k="10" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="question"
	k="30" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="13" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="2" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="82" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="V"
	k="23" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="36" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="20" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="-10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="V.smcp"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="28" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="16" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="J,Jcircumflex,afii10057"
	k="83" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-7" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="56" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="31" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="56" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="v,gamma,nu"
	k="56" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="56" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="bullet.case"
	k="82" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Jcircumflex.smcp,J.smcp"
	k="107" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="52" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="90" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="57" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="X.smcp"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="93" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand.smcp"
	k="91" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="115" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="x,afii10072,afii10087"
	k="66" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="91" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="59" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="42" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="85" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="17" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="J,Jcircumflex,afii10057"
	k="71" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="28" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="V"
	k="3" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="bullet"
	k="50" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="73" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-3" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="35" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="34" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand"
	k="37" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand.smcp"
	k="33" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="42" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="J,Jcircumflex,afii10057"
	k="12" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="Jcircumflex.smcp,J.smcp"
	k="12" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="9" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="16" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="question"
	k="9" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="56" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="V"
	k="20" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="23" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-7" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="2" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="14" />
    <hkern g1="V,afii10062,afii10037"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="37" />
    <hkern g1="V,afii10062,afii10037"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="3" />
    <hkern g1="V,afii10062,afii10037"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="7" />
    <hkern g1="V,afii10062,afii10037"
	g2="J,Jcircumflex,afii10057"
	k="93" />
    <hkern g1="V,afii10062,afii10037"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-11" />
    <hkern g1="V,afii10062,afii10037"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="23" />
    <hkern g1="V,afii10062,afii10037"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="42" />
    <hkern g1="V,afii10062,afii10037"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="13" />
    <hkern g1="V,afii10062,afii10037"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="V,afii10062,afii10037"
	g2="v,gamma,nu"
	k="13" />
    <hkern g1="V,afii10062,afii10037"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="6" />
    <hkern g1="V,afii10062,afii10037"
	g2="bullet.case"
	k="37" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="30" />
    <hkern g1="V,afii10062,afii10037"
	g2="Jcircumflex.smcp,J.smcp"
	k="90" />
    <hkern g1="V,afii10062,afii10037"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="16" />
    <hkern g1="V,afii10062,afii10037"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="44" />
    <hkern g1="V,afii10062,afii10037"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="44" />
    <hkern g1="V,afii10062,afii10037"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand"
	k="12" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand.smcp"
	k="47" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen,periodcentered,endash,emdash"
	k="53" />
    <hkern g1="V,afii10062,afii10037"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="60" />
    <hkern g1="V,afii10062,afii10037"
	g2="x,afii10072,afii10087"
	k="23" />
    <hkern g1="V,afii10062,afii10037"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="39" />
    <hkern g1="V,afii10062,afii10037"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="3" />
    <hkern g1="V,afii10062,afii10037"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="42" />
    <hkern g1="V.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="7" />
    <hkern g1="V.smcp"
	g2="J,Jcircumflex,afii10057"
	k="54" />
    <hkern g1="V.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="35" />
    <hkern g1="V.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="3" />
    <hkern g1="V.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="59" />
    <hkern g1="V.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-10" />
    <hkern g1="V.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="22" />
    <hkern g1="V.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="17" />
    <hkern g1="V.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="63" />
    <hkern g1="V.smcp"
	g2="ampersand"
	k="16" />
    <hkern g1="V.smcp"
	g2="ampersand.smcp"
	k="-4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="24" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="9" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex,afii10057"
	k="38" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="13" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="13" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v,gamma,nu"
	k="3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="14" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Jcircumflex.smcp,J.smcp"
	k="39" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="39" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="67" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand.smcp"
	k="6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="33" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x,afii10072,afii10087"
	k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="19" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="30" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="16" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="J,Jcircumflex,afii10057"
	k="23" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="35" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="3" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="39" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-1" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="16" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="53" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="ampersand"
	k="3" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="ampersand.smcp"
	k="-1" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="6" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="V.smcp"
	k="3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="24" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="J,Jcircumflex,afii10057"
	k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="6" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="6" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="26" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="bullet.case"
	k="77" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="97" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Jcircumflex.smcp,J.smcp"
	k="102" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="29" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="52" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="53" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="ampersand"
	k="42" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="ampersand.smcp"
	k="21" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="116" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="127" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="x,afii10072,afii10087"
	k="73" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="colon,semicolon"
	k="59" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="33" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="6" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="90" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="17" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="J,Jcircumflex,afii10057"
	k="58" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="35" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="6" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="97" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="-14" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="23" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="43" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="70" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand"
	k="39" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand.smcp"
	k="39" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="46" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="17" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="bullet.case"
	k="58" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="64" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="42" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="V"
	k="3" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="12" />
    <hkern g1="ampersand"
	g2="V.smcp"
	k="61" />
    <hkern g1="ampersand"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="21" />
    <hkern g1="ampersand"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="85" />
    <hkern g1="ampersand"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="20" />
    <hkern g1="ampersand"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="91" />
    <hkern g1="ampersand"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="58" />
    <hkern g1="ampersand"
	g2="V"
	k="67" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="110" />
    <hkern g1="ampersand"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="91" />
    <hkern g1="ampersand"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="20" />
    <hkern g1="ampersand.smcp"
	g2="V.smcp"
	k="51" />
    <hkern g1="ampersand.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="12" />
    <hkern g1="ampersand.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="81" />
    <hkern g1="ampersand.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="30" />
    <hkern g1="ampersand.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="110" />
    <hkern g1="ampersand.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="12" />
    <hkern g1="ampersand.smcp"
	g2="V"
	k="80" />
    <hkern g1="ampersand.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="27" />
    <hkern g1="ampersand.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="84" />
    <hkern g1="asterisk"
	g2="J,Jcircumflex,afii10057"
	k="116" />
    <hkern g1="asterisk"
	g2="Jcircumflex.smcp,J.smcp"
	k="103" />
    <hkern g1="asterisk"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="64" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="101" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="question"
	k="46" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="115" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="V"
	k="60" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="124" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="7" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="x,afii10072,afii10087"
	k="37" />
    <hkern g1="bullet"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="62" />
    <hkern g1="bullet"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="2" />
    <hkern g1="bullet.case"
	g2="J,Jcircumflex,afii10057"
	k="80" />
    <hkern g1="bullet.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="82" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="27" />
    <hkern g1="bullet.case"
	g2="V"
	k="57" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="97" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="27" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="58" />
    <hkern g1="bullet.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="60" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="question"
	k="23" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="3" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="81" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="23" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="V"
	k="36" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="63" />
    <hkern g1="colon,semicolon"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="59" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="question"
	k="36" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="3" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="asterisk"
	k="17" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="101" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="V"
	k="53" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="110" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="7" />
    <hkern g1="eth"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="7" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="question"
	k="-57" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="-67" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="asterisk"
	k="-67" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-23" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-63" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="V"
	k="-63" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="-56" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="43" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand"
	k="3" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand.smcp"
	k="3" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="exclam,B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Imacron,Iogonek,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Racute,Rcommaaccent,Rcaron"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="parenright,bracketright,braceright,parenright.case,bracketright.case,braceright.case"
	k="-74" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="question"
	k="23" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="85" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="V"
	k="42" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="j,jcircumflex,afii10105"
	k="-46" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="J,Jcircumflex,afii10057"
	k="67" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="30" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="97" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="47" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="64" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="77" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="42" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="6" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="33" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="53" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="109" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="46" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="43" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="69" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="23" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="V"
	k="23" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="66" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="bullet"
	k="27" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="27" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="3" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V.smcp"
	k="63" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="21" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="76" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="60" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="53" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="30" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="93" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="67" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V"
	k="87" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="14" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="23" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="23" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="44" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="v,gamma,nu"
	k="67" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="57" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="7" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="j,jcircumflex,afii10105"
	k="-40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="seven"
	k="66" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one"
	k="78" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="zero,six"
	k="31" />
    <hkern g1="questiondown"
	g2="j,jcircumflex,afii10105"
	k="-160" />
    <hkern g1="questiondown.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="104" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="63" />
    <hkern g1="questiondown.case"
	g2="V"
	k="73" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="93" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="3" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="J,Jcircumflex,afii10057"
	k="92" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="104" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="76" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="ampersand"
	k="-7" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="48" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="V"
	k="23" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="50" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="83" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="9" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="question"
	k="43" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="88" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="39" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="V"
	k="43" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="93" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="3" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="52" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="13" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="V"
	k="26" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="33" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="question"
	k="23" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="85" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="V"
	k="42" />
    <hkern g1="a,u,z,mu,aogonek,uogonek,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EA1,uni1EE5,uni1EE7"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="v,gamma,nu,chi"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="56" />
    <hkern g1="v,gamma,nu,chi"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="v,gamma,nu,chi"
	g2="V"
	k="13" />
    <hkern g1="v,gamma,nu,chi"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="v,gamma,nu,chi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="v,gamma,nu,chi"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="67" />
    <hkern g1="v,gamma,nu,chi"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="37" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="56" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="6" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="17" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="44" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="44" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="question"
	k="3" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="56" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="V"
	k="6" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="57" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="37" />
    <hkern g1="X,Chi,afii10039"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="54" />
    <hkern g1="X,Chi,afii10039"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="44" />
    <hkern g1="X,Chi,afii10039"
	g2="v,gamma,nu"
	k="37" />
    <hkern g1="X,Chi,afii10039"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="37" />
    <hkern g1="X,Chi,afii10039"
	g2="bullet.case"
	k="60" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="77" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen,periodcentered,endash,emdash"
	k="43" />
    <hkern g1="X.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="23" />
    <hkern g1="X.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="35" />
    <hkern g1="X.smcp"
	g2="ampersand"
	k="20" />
    <hkern g1="zero,nine"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="31" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="97" />
    <hkern g1="x,afii10087"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="66" />
    <hkern g1="x,afii10087"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="23" />
    <hkern g1="x,afii10087"
	g2="V"
	k="23" />
    <hkern g1="x,afii10087"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="73" />
    <hkern g1="x,afii10087"
	g2="a,c,d,e,g,o,q,ccedilla,eacute,ograve,oacute,oslash,aogonek,cacute,cdotaccent,dcaron,dcroat,edotaccent,eogonek,gdotaccent,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10065,afii10070,afii10080,afii10083,afii10086,afii10101,uni1EA1,uni1EB9,uni1ECD,uni1EE3,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,afii10065.alt1"
	k="37" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.case,bracketleft.case,braceleft.case"
	g2="j,jcircumflex,afii10105"
	k="-110" />
    <hkern g1="one.numr"
	g2="fraction"
	k="-35" />
    <hkern g1="seven.numr"
	g2="fraction"
	k="50" />
    <hkern g1="fraction"
	g2="one.dnom"
	k="-22" />
    <hkern g1="fraction"
	g2="four.dnom"
	k="50" />
  </font>
</defs></svg>
