<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20150824 at Sat Jan  9 01:01:15 2016
 By Everything Fonts
Copyright (c) <PERSON>, 2005. All rights reserved.
</metadata>
<defs>
<font id="Proxima_Nova_Regular" horiz-adv-x="572" >
  <font-face 
    font-family="Proxima_Nova_Regular"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 6 3 0 0 2 0 4"
    ascent="790"
    descent="-210"
    x-height="483"
    cap-height="667"
    bbox="-172 -272 1082 907"
    underline-thickness="20"
    underline-position="-153"
    unicode-range="U+000D-FB04"
  />
<missing-glyph horiz-adv-x="507" 
d="M429 -90h-351v842h351v-842zM397 -60v782h-288v-782h288z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="792" 
d="M454 0h-75v417h-80v66h80v37q0 74 38 115.5t102 41.5q46 0 73 -17l-19 -57q-19 12 -43 12q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417zM679 552q-20 0 -35 14.5t-15 35.5t15 36t35 15q21 0 36 -15t15 -36t-15 -35.5t-36 -14.5zM574 -196q-60 0 -106 36l27 57
q31 -31 69 -31q36 0 57 21.5t21 65.5v530h75v-530q0 -71 -37 -110t-106 -39zM171 0h-75v417h-80v66h80v37q0 74 37.5 115.5t102.5 41.5q62 0 102 -37l-31 -49q-27 24 -60 24q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="509" 
d="M171 0h-75v417h-80v66h80v37q0 74 38 115.5t102 41.5q46 0 73 -17l-19 -57q-19 12 -43 12q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417zM395 552q-20 0 -35 14.5t-15 35.5t15 36t35 15q21 0 36 -15t15 -36t-15 -35.5t-36 -14.5zM290 -196q-60 0 -106 36l27 57
q31 -31 69 -31q36 0 57 21.5t21 65.5v530h75v-530q0 -71 -37 -110t-106 -39z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="567" 
d="M171 0h-75v417h-80v66h80v37q0 74 37.5 115.5t102.5 41.5q62 0 102 -37l-31 -49q-27 24 -60 24q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417zM454 0h-75v417h-80v66h80v37q0 74 37.5 115.5t102.5 41.5q62 0 102 -37l-31 -49q-27 24 -60 24q-37 0 -56.5 -24.5
t-19.5 -70.5v-37h98v-66h-98v-417z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="509" 
d="M171 0h-75v417h-80v66h80v37q0 74 38 115.5t102 41.5q46 0 73 -17l-19 -57q-19 12 -43 12q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417zM395 552q-20 0 -35 14.5t-15 35.5t15 36t35 15q21 0 36 -15t15 -36t-15 -35.5t-36 -14.5zM433 0h-75v483h75v-483z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="509" 
d="M171 0h-75v417h-80v66h80v37q0 74 38 115.5t102 41.5q46 0 73 -17l-19 -57q-19 12 -43 12q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417zM433 0h-75v667h75v-667z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="792" 
d="M454 0h-75v417h-80v66h80v37q0 74 38 115.5t102 41.5q46 0 73 -17l-19 -57q-19 12 -43 12q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417zM679 552q-20 0 -35 14.5t-15 35.5t15 36t35 15q21 0 36 -15t15 -36t-15 -35.5t-36 -14.5zM717 0h-75v483h75v-483zM171 0
h-75v417h-80v66h80v37q0 74 37.5 115.5t102.5 41.5q62 0 102 -37l-31 -49q-27 24 -60 24q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="792" 
d="M454 0h-75v417h-80v66h80v37q0 74 38 115.5t102 41.5q46 0 73 -17l-19 -57q-19 12 -43 12q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417zM717 0h-75v667h75v-667zM171 0h-75v417h-80v66h80v37q0 74 37.5 115.5t102.5 41.5q62 0 102 -37l-31 -49q-27 24 -60 24
q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="858" 
d="M171 0h-75v417h-80v66h80v37q0 74 38 115.5t102 41.5q46 0 73 -17l-19 -57q-19 12 -43 12q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417zM433 131q20 -32 60.5 -54t83.5 -22q72 0 114 52t42 134t-42 134.5t-114 52.5q-43 0 -83.5 -22.5t-60.5 -55.5v-219zM433 0
h-75v667h75v-257q63 85 163 85q95 0 155 -69.5t60 -184.5q0 -116 -59.5 -184.5t-155.5 -68.5q-101 0 -163 84v-72z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="835" 
d="M171 0h-75v417h-80v66h80v37q0 74 38 115.5t102 41.5q46 0 73 -17l-19 -57q-19 12 -43 12q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417zM760 0h-75v318q0 60 -28 85t-81 25q-41 0 -80.5 -22t-62.5 -53v-353h-75v667h75v-254q28 33 75 57.5t98 24.5
q154 0 154 -154v-341z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="798" 
d="M171 0h-75v417h-80v66h80v37q0 74 38 115.5t102 41.5q46 0 73 -17l-19 -57q-19 12 -43 12q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417zM783 0h-96l-174 217l-80 -79v-138h-75v667h75v-441l254 257h95l-214 -219z" />
    <glyph glyph-name=".notdef" horiz-adv-x="507" 
d="M429 -90h-351v842h351v-842zM397 -60v782h-288v-782h288z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="CR" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="258" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="230" 
d="M146 187h-62l-16 480h94zM115 -10q-22 0 -38.5 16.5t-16.5 38.5q0 23 16 39t39 16t39 -16t16 -39q0 -22 -16.5 -38.5t-38.5 -16.5z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="343" 
d="M118 412h-37q-27 205 -27 219q0 19 13 32.5t32 13.5t32.5 -13.5t13.5 -32.5zM263 412h-37q-27 205 -27 219q0 19 13.5 32.5t32.5 13.5t32 -13.5t13 -32.5z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="590" 
d="M300 0h-61l61 181h-111l-60 -181h-62l61 181h-106l17 53h107l66 199h-108l16 52h110l60 182h62l-60 -182h110l60 182h61l-61 -182h107l-15 -52h-109l-67 -199h111l-16 -53h-113zM317 234l66 199h-110l-66 -199h110z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="593" 
d="M271 -100v89q-144 8 -229 105l49 63q73 -82 180 -94v244q-38 10 -61.5 18t-54.5 23.5t-49 33.5t-30.5 46t-12.5 64q0 77 58 128t150 56v92h61v-93q121 -12 197 -91l-50 -61q-59 63 -147 76v-217q47 -13 78 -25.5t65.5 -35t51.5 -57t17 -80.5q0 -76 -52 -131.5t-160 -63.5
v-89h-61zM458 177q0 45 -32.5 70t-93.5 43v-227q65 7 95.5 40t30.5 74zM149 497q0 -38 31 -59.5t91 -39.5v204q-54 -4 -88 -32.5t-34 -72.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="731" 
d="M191 348q-71 0 -115.5 46.5t-44.5 116.5q0 71 45 118.5t115 47.5q71 0 116.5 -47.5t45.5 -118.5q0 -70 -45.5 -116.5t-116.5 -46.5zM182 0h-54l426 667h55zM539 -12q-71 0 -115.5 46.5t-44.5 116.5q0 71 45 118.5t115 47.5q71 0 116 -47.5t45 -118.5q0 -70 -45 -116.5
t-116 -46.5zM191 398q44 0 73 32.5t29 80.5q0 50 -28.5 82.5t-73.5 32.5q-44 0 -72 -32.5t-28 -82.5q0 -48 28 -80.5t72 -32.5zM539 38q44 0 73 32.5t29 80.5q0 50 -28.5 82.5t-73.5 32.5t-73 -32.5t-28 -82.5q0 -48 28.5 -80.5t72.5 -32.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="641" 
d="M613 0h-104q-30 26 -73 70q-82 -82 -192 -82q-91 0 -149.5 48.5t-58.5 137.5q0 71 39 116t108 82q-51 87 -51 150q0 66 48.5 110.5t119.5 44.5q67 0 109 -34.5t42 -96.5q0 -36 -13 -64.5t-42 -52t-50.5 -36.5t-64.5 -35q32 -44 77 -95q36 -43 79 -89q44 65 72 152l65 -28
q-49 -114 -91 -172q50 -52 130 -126zM251 50q74 0 140 66q-66 69 -92 101q-46 53 -84 106q-47 -29 -72.5 -62.5t-25.5 -81.5q0 -60 39.5 -94.5t94.5 -34.5zM210 521q0 -47 40 -115q65 32 98.5 62t33.5 75q0 36 -22 55.5t-57 19.5q-39 0 -66 -27.5t-27 -69.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="199" 
d="M118 412h-37q-27 205 -27 219q0 19 13 32.5t32 13.5t32.5 -13.5t13.5 -32.5z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="248" 
d="M230 -164l-45 -35q-141 193 -141 442q0 248 141 442l45 -34q-53 -105 -78.5 -198t-25.5 -210t25.5 -210.5t78.5 -196.5z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="248" 
d="M62 -199l-45 35q53 103 79 196.5t26 210.5q0 118 -26 211t-79 197l45 34q142 -193 142 -442q0 -250 -142 -442z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="340" 
d="M193 389h-46l5 115l-96 -63l-22 40l102 52l-102 52l22 39l96 -62l-5 115h46l-6 -115l96 62l23 -39l-102 -52l102 -52l-23 -40l-96 63z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="499" 
d="M470 311h-192v-213h-57v213h-192v52h192v207h57v-207h192v-52z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="230" 
d="M176 24q0 -44 -23 -84.5t-59 -66.5l-37 31q25 16 43.5 42.5t21.5 50.5q-4 -2 -14 -2q-21 0 -34.5 14.5t-13.5 36.5t15.5 38t37.5 16q26 0 44.5 -20.5t18.5 -55.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="300" 
d="M270 209h-240v66h240v-66z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="230" 
d="M115 -10q-23 0 -39 16t-16 39t16 39t39 16t39 -16t16 -39t-16 -39t-39 -16z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="296" 
d="M59 -20h-59l237 707h59z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="612" 
d="M306 -12q-66 0 -116.5 30.5t-80 81.5t-44 110t-14.5 123t14.5 123t44 109.5t80 81t116.5 30.5q65 0 116 -30.5t80.5 -81t44 -109.5t14.5 -123t-14.5 -123t-44 -110t-80.5 -81.5t-116 -30.5zM306 62q60 0 100 41.5t55 99.5t15 130t-15 130t-55 99t-100 41t-100 -41
t-55 -99t-15 -130t15 -130t55 -99.5t100 -41.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="339" 
d="M261 0h-83v557l-107 -113l-50 51l167 172h73v-667z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="588" 
d="M512 0h-458v66q104 82 161.5 130.5t111 103t75.5 97t22 84.5q0 60 -41 91t-98 31q-115 0 -181 -84l-51 53q38 50 99.5 77.5t132.5 27.5q91 0 157.5 -51t66.5 -145q0 -93 -80 -185.5t-244 -221.5h327v-74z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="557" 
d="M266 -12q-80 0 -142 31t-95 79l49 52q30 -40 79 -64t107 -24q73 0 114.5 33.5t41.5 91.5q0 60 -44.5 89t-121.5 29q-59 0 -69 -1v76q11 -1 69 -1q68 0 111.5 27.5t43.5 82.5q0 53 -42.5 83.5t-106.5 30.5q-101 0 -176 -81l-46 52q85 103 228 103q99 0 162.5 -47.5
t63.5 -130.5q0 -66 -44 -105.5t-100 -49.5q55 -5 104.5 -48t49.5 -116q0 -85 -64 -138.5t-172 -53.5z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="558" 
d="M432 0h-83v169h-317v69l286 429h114v-425h94v-73h-94v-169zM349 242v348l-234 -348h234z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="590" 
d="M301 -12q-154 0 -235 107l51 55q69 -88 184 -88q68 0 111 41t43 102q0 65 -42.5 104.5t-110.5 39.5q-90 0 -154 -63l-61 21v360h408v-74h-325v-232q60 60 156 60q88 0 150 -57.5t62 -155.5q0 -100 -67 -160t-170 -60z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="591" 
d="M308 -12q-67 0 -118 28t-80.5 76.5t-44 109t-14.5 130.5q0 94 28 170t90.5 125.5t152.5 49.5q115 0 185 -80l-42 -62q-58 68 -143 68q-62 0 -105.5 -39t-63 -96.5t-19.5 -126.5q0 -17 1 -26q25 40 77.5 72t111.5 32q95 0 156.5 -55.5t61.5 -157.5q0 -91 -64 -154.5
t-170 -63.5zM305 62q72 0 112.5 43.5t40.5 97.5q0 70 -43 107t-111 37q-49 0 -94 -27.5t-73 -72.5q7 -74 48 -129.5t120 -55.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="515" 
d="M213 0h-92l269 593h-359v74h455v-57z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="581" 
d="M291 -12q-102 0 -170.5 49t-68.5 132q0 63 45.5 109t113.5 66q-64 18 -106 58.5t-42 101.5q0 84 68 128.5t160 44.5t160.5 -44.5t68.5 -128.5q0 -61 -42 -101.5t-107 -58.5q68 -20 113.5 -66t45.5 -109q0 -82 -68.5 -131.5t-170.5 -49.5zM291 378q20 3 40.5 10t46.5 19.5
t42 35t16 50.5q0 50 -41.5 80t-103.5 30q-63 0 -104 -30t-41 -80q0 -28 16 -50.5t42 -35t46.5 -19t40.5 -10.5zM291 62q64 0 109.5 32t45.5 84q0 40 -31.5 70.5t-64 43t-59.5 15.5q-27 -3 -59.5 -15.5t-64.5 -43t-32 -70.5q0 -52 45.5 -84t110.5 -32z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="591" 
d="M269 -11q-115 0 -185 80l42 62q58 -68 143 -68q64 0 108 40t62 97t18 124q0 18 -1 27q-25 -40 -77 -72t-111 -32q-95 0 -157 55.5t-62 157.5q0 90 64 154t169 64q68 0 119 -28t80.5 -77t44 -109t14.5 -131q0 -94 -28 -169.5t-90.5 -125t-152.5 -49.5zM287 319
q49 0 94.5 27t71.5 72q-6 74 -47 130t-120 56q-71 0 -112 -44t-41 -97q0 -70 43 -107t111 -37z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="230" 
d="M115 380q-23 0 -39 16t-16 39t16 39t39 16t39 -16t16 -39t-16 -39t-39 -16zM115 -10q-23 0 -39 16t-16 39t16 39t39 16t39 -16t16 -39t-16 -39t-39 -16z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="230" 
d="M115 380q-22 0 -38.5 16.5t-16.5 38.5q0 23 16 39t39 16t39 -16t16 -39q0 -22 -16.5 -38.5t-38.5 -16.5zM176 26q0 -45 -23 -85.5t-59 -65.5l-37 30q24 16 43 43t22 51q-6 -3 -14 -3q-21 0 -34.5 14.5t-13.5 36.5q0 23 15.5 39t37.5 16q26 0 44.5 -20.5t18.5 -55.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="499" 
d="M470 90l-441 218v53l441 219v-63l-377 -183l377 -182v-62z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="499" 
d="M470 404h-441v53h441v-53zM470 211h-441v52h441v-52z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="499" 
d="M470 308l-441 -218v62l376 182l-376 183v63l441 -219v-53z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="462" 
d="M203 185q-39 33 -39 81q0 30 13.5 55t33.5 41t43.5 33.5t43.5 32.5t33.5 36t13.5 46q0 40 -30.5 66.5t-87.5 26.5q-97 0 -159 -82l-50 54q79 102 217 102q89 0 142.5 -44.5t53.5 -110.5q0 -51 -30.5 -90.5t-66.5 -60.5t-66.5 -50t-30.5 -61q0 -28 27 -49zM224 -10
q-22 0 -38.5 16t-16.5 39t16.5 39t38.5 16t38.5 -16t16.5 -39t-16.5 -39t-38.5 -16z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="783" 
d="M352 -70q-136 0 -226.5 90.5t-90.5 223.5q0 160 119.5 277t277.5 117q139 0 227 -91.5t88 -225.5q0 -111 -52 -173.5t-121 -62.5q-40 0 -64.5 23.5t-26.5 57.5l-1 6q-27 -38 -69.5 -62.5t-88.5 -24.5q-69 0 -111 45t-42 118q0 102 72 176.5t163 74.5q46 0 79.5 -22
t48.5 -56l13 62h68l-57 -271q-2 -12 -2 -21q0 -25 13.5 -38.5t33.5 -13.5q38 0 73 45t35 137q0 124 -78.5 203.5t-204.5 79.5q-144 0 -251.5 -108.5t-107.5 -249.5q0 -121 81.5 -202t205.5 -81q97 0 186 56l18 -26q-98 -63 -208 -63zM344 141q87 0 149 90l29 138
q-10 29 -36.5 51.5t-66.5 22.5q-73 0 -125 -58.5t-52 -132.5q0 -49 27.5 -80t74.5 -31z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="629" 
d="M382 0h-304v667h297q86 0 136.5 -47t50.5 -123q0 -60 -34 -101.5t-83 -51.5q53 -8 91.5 -56t38.5 -108q0 -81 -51.5 -130.5t-141.5 -49.5zM361 378q55 0 85 30t30 77t-30.5 77.5t-84.5 30.5h-200v-215h200zM365 74q58 0 91 31t33 84q0 49 -32.5 82t-91.5 33h-204v-230
h204z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="676" 
d="M394 -12q-146 0 -244.5 96.5t-98.5 248.5t98.5 248.5t244.5 96.5q160 0 253 -132l-70 -39q-29 43 -78 70t-105 27q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -194.5t183.5 -76.5q56 0 105 26.5t78 70.5l71 -39q-97 -132 -254 -132z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="700" 
d="M306 0h-228v667h228q153 0 247.5 -95t94.5 -239q0 -145 -94.5 -239t-247.5 -94zM306 74q118 0 187 74t69 185q0 112 -68 186t-188 74h-145v-519h145z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="569" 
d="M515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="550" 
d="M161 0h-83v667h437v-74h-354v-215h347v-74h-347v-304z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="713" 
d="M394 -13q-145 0 -244 96.5t-99 249.5t99 249t244 96q158 0 259 -125l-66 -41q-32 42 -83.5 67t-109.5 25q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -195t183.5 -77q55 0 102.5 21.5t76.5 50.5v136h-229v74h312v-241q-103 -115 -262 -115z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="712" 
d="M634 0h-83v306h-390v-306h-83v667h83v-287h390v287h83v-667z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="475" 
d="M185 -12q-108 0 -174 77l43 63q55 -66 126 -66q61 0 97.5 38.5t36.5 101.5v465h83v-466q0 -104 -59 -158.5t-153 -54.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="601" 
d="M584 0h-103l-253 300l-67 -76v-224h-83v667h83v-345l295 345h104l-278 -317z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="510" 
d="M471 0h-393v667h83v-593h310v-74z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="809" 
d="M731 0h-83v549l-227 -549h-34l-226 549v-549h-83v667h119l207 -502l208 502h119v-667z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="708" 
d="M630 0h-80l-389 532v-532h-83v667h85l384 -521v521h83v-667z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="587" 
d="M161 0h-83v667h268q97 0 153 -58t56 -143t-56.5 -143t-152.5 -58h-185v-265zM336 339q59 0 96 35.5t37 91.5t-37 91.5t-96 35.5h-175v-254h175z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92 -246l64 -68l-58 -50l-65 69q-78 -50 -180 -50zM382 62q71 0 127 34l-95 103l58 49l94 -102q61 75 61 187q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5
t178 -76.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="608" 
d="M562 0h-98l-170 265h-133v-265h-83v667h268q93 0 151 -55t58 -146q0 -86 -50 -137t-123 -57zM336 338q59 0 96 36t37 92t-37 91.5t-96 35.5h-175v-255h175z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="586" 
d="M294 -12q-162 0 -256 106l49 63q85 -95 210 -95q81 0 119 34.5t38 80.5q0 35 -22 59t-56.5 37.5t-77 24.5t-84.5 24.5t-76.5 32.5t-56.5 54t-22 83q0 82 65 133.5t164 51.5q149 0 237 -93l-50 -61q-73 80 -193 80q-60 0 -98.5 -29.5t-38.5 -76.5q0 -35 29.5 -58t73 -34
t95 -27.5t95 -35t73 -59.5t29.5 -99q0 -82 -60.5 -139t-185.5 -57z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="570" 
d="M326 0h-83v593h-211v74h506v-74h-212v-593z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="700" 
d="M350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-407q0 -127 -69.5 -199.5t-202.5 -72.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="658" 
d="M380 0h-103l-268 667h95l225 -576l225 576h95z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="883" 
d="M678 0h-90l-147 538l-147 -538h-90l-190 667h92l148 -556l153 556h69l153 -556l147 556h92z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="652" 
d="M639 0h-100l-213 284l-213 -284h-101l260 342l-245 325h101l198 -267l197 267h101l-244 -324z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="626" 
d="M355 0h-83v282l-263 385h97l207 -310l207 310h97l-262 -385v-282z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="585" 
d="M536 0h-488v69l375 524h-375v74h481v-69l-375 -524h382v-74z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="243" 
d="M226 -190h-186v868h186v-55h-128v-758h128v-55z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="296" 
d="M237 -20l-237 707h59l237 -707h-59z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="243" 
d="M203 -190h-186v55h128v758h-128v55h186v-868z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="432" 
d="M413 333h-62l-135 279l-135 -279h-62l169 334h57z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="564" 
d="M567 -95h-570v55h570v-55z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="226" 
d="M226 556h-57l-169 144h78z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="575" 
d="M150 131q20 -32 60.5 -54t83.5 -22q72 0 114 52t42 134t-42 134.5t-114 52.5q-43 0 -83.5 -22.5t-60.5 -55.5v-219zM150 0h-75v667h75v-257q63 85 163 85q95 0 155 -69.5t60 -184.5q0 -116 -59.5 -184.5t-155.5 -68.5q-101 0 -163 84v-72z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="495" 
d="M288 -12q-107 0 -174 72.5t-67 181.5t67 181t174 72q112 0 177 -84l-50 -46q-46 63 -123 63q-76 0 -121.5 -52t-45.5 -134t45.5 -134.5t121.5 -52.5t123 63l50 -46q-65 -84 -177 -84z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="575" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q100 0 163 -85v257h75v-667zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="563" 
d="M293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="283" 
d="M171 0h-75v417h-80v66h80v37q0 74 37.5 115.5t102.5 41.5q62 0 102 -37l-31 -49q-27 24 -60 24q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="574" 
d="M269 -196q-64 0 -109 15.5t-86 56.5l38 56q55 -66 157 -66q67 0 111 35.5t44 109.5v69q-27 -38 -70 -62t-92 -24q-96 0 -155.5 67.5t-59.5 182.5t59.5 183t155.5 68q99 0 162 -85v73h75v-469q0 -111 -65 -160.5t-165 -49.5zM280 60q43 0 83.5 23t60.5 56v211
q-20 33 -60 55.5t-84 22.5q-72 0 -113.5 -51t-41.5 -133q0 -81 42 -132.5t113 -51.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="552" 
d="M477 0h-75v318q0 60 -28 85t-81 25q-41 0 -80.5 -22t-62.5 -53v-353h-75v667h75v-254q28 33 75 57.5t98 24.5q154 0 154 -154v-341z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="225" 
d="M112 552q-20 0 -35 14.5t-15 35.5t15 36t35 15q21 0 36 -15t15 -36t-15 -35.5t-36 -14.5zM150 0h-75v483h75v-483z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="225" 
d="M112 552q-20 0 -35 14.5t-15 35.5t15 36t35 15q21 0 36 -15t15 -36t-15 -35.5t-36 -14.5zM7 -196q-59 0 -106 36l27 57q31 -31 69 -31q36 0 57 21.5t21 65.5v530h75v-530q0 -71 -37 -110t-106 -39z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="514" 
d="M500 0h-96l-174 217l-80 -79v-138h-75v667h75v-441l254 257h95l-214 -219z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="225" 
d="M150 0h-75v667h75v-667z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="808" 
d="M733 0h-75v328q0 100 -89 100q-36 0 -72 -22t-56 -52v-354h-75v328q0 100 -89 100q-35 0 -70.5 -22t-56.5 -53v-353h-75v483h75v-70q18 28 63 55t93 27q53 0 86 -25t44 -65q22 36 67 63t95 27q135 0 135 -146v-349z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="551" 
d="M476 0h-75v316q0 61 -28 86.5t-80 25.5q-42 0 -81.5 -22t-61.5 -53v-353h-75v483h75v-70q28 33 75 57.5t97 24.5q154 0 154 -156v-339z" />
    <glyph glyph-name="o" unicode="o" 
d="M286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z" />
    <glyph glyph-name="p" unicode="p" 
d="M313 -12q-102 0 -163 85v-257h-75v667h75v-72q27 38 70 61t93 23q96 0 155.5 -68.5t59.5 -184.5t-59.5 -185t-155.5 -69zM294 55q71 0 113 52.5t42 134.5t-42 134t-113 52q-43 0 -83.5 -22.5t-60.5 -54.5v-218q20 -33 60.5 -55.5t83.5 -22.5z" />
    <glyph glyph-name="q" unicode="q" 
d="M277 55q44 0 84.5 22.5t59.5 55.5v218q-19 32 -59.5 54.5t-84.5 22.5q-71 0 -112.5 -52t-41.5 -134t41.5 -134.5t112.5 -52.5zM259 -12q-96 0 -155.5 69t-59.5 185t59.5 184.5t155.5 68.5q50 0 93 -23t69 -61v72h75v-667h-75v257q-61 -85 -162 -85z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="330" 
d="M150 0h-75v483h75v-78q67 88 161 88v-77q-14 3 -30 3q-34 0 -74 -24t-57 -53v-342z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="465" 
d="M228 -12q-123 0 -195 76l39 54q25 -29 68.5 -49t90.5 -20q54 0 84 23t30 60q0 32 -31 50.5t-74.5 28t-87.5 22t-75 43t-31 80.5q0 59 48.5 99t131.5 40q109 0 179 -70l-35 -52q-51 61 -144 61q-49 0 -78 -21.5t-29 -54.5q0 -29 31 -45.5t74.5 -26t87.5 -22.5t75 -45.5
t31 -84.5q0 -64 -50 -105t-140 -41z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="294" 
d="M197 -12q-53 0 -80 29t-27 84v316h-80v66h80v132h75v-132h98v-66h-98v-300q0 -28 12.5 -45t36.5 -17q33 0 51 20l22 -56q-33 -31 -90 -31z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="551" 
d="M476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="490" 
d="M286 0h-82l-201 483h81l161 -396l160 396h82z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="734" 
d="M568 0h-75l-126 388l-126 -388h-75l-154 483h78l118 -385l127 385h64l127 -385l118 385h78z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="486" 
d="M475 0h-87l-145 198l-145 -198h-86l185 248l-175 235h87l134 -184l134 184h87l-175 -235z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="490" 
d="M48 -189l12 68q18 -8 41 -8q26 0 43 11t29 40l32 73l-202 488h81l161 -396l160 396h82l-242 -581q-40 -96 -142 -98q-30 0 -55 7z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="472" 
d="M420 0h-368v58l266 359h-266v66h364v-57l-268 -361h272v-65z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="260" 
d="M243 -190h-61q-50 0 -87.5 37.5t-37.5 96.5v206q0 30 -13.5 49.5t-38.5 19.5v50q25 0 38.5 19.5t13.5 49.5v205q0 60 37.5 97.5t87.5 37.5h61v-55h-61q-27 0 -46.5 -22.5t-19.5 -57.5v-211q0 -68 -46 -88q46 -20 46 -88v-211q0 -34 19.5 -57t46.5 -23h61v-55z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="211" 
d="M133 -20h-55v707h55v-707z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="260" 
d="M17 -190v55h61q27 0 46.5 23t19.5 57v211q0 68 46 88q-46 20 -46 88v211q0 35 -19.5 57.5t-46.5 22.5h-61v55h61q50 0 87.5 -37.5t37.5 -97.5v-205q0 -30 13.5 -49.5t38.5 -19.5v-50q-25 0 -38.5 -19.5t-13.5 -49.5v-206q0 -59 -37.5 -96.5t-87.5 -37.5h-61z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="503" 
d="M419 667l56 -7q-5 -51 -12.5 -89t-22.5 -74t-41 -55t-62 -19q-35 0 -58 19.5t-34.5 47.5t-21 56t-23.5 47.5t-35 19.5q-63 0 -82 -191l-56 7q5 50 13 88.5t23 74.5t41 55t61 19t58.5 -19.5t35 -48t20.5 -56.5t23 -47.5t35 -19.5q65 0 82 192z" />
    <glyph glyph-name="nbspace" unicode="&#xa0;" horiz-adv-x="258" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="230" 
d="M69 -184l15 480h62l16 -480h-93zM60 438q0 22 16.5 38.5t38.5 16.5t38.5 -16.5t16.5 -38.5t-16 -38.5t-39 -16.5t-39 16.5t-16 38.5z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="495" 
d="M252 -100v91q-92 12 -148.5 81.5t-56.5 169.5q0 98 56.5 167.5t148.5 82.5v73h59v-71q94 -8 154 -83l-50 -46q-40 54 -104 62v-371q63 6 104 62l50 -46q-58 -75 -154 -83v-89h-59zM125 242q0 -70 34 -120t93 -63v364q-59 -13 -93 -62.5t-34 -118.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="518" 
d="M19 268v54h106q-28 32 -38.5 45.5t-24.5 46t-14 66.5q0 82 66 139.5t158 57.5q68 0 121 -29.5t81 -82.5l-67 -40q-14 34 -48.5 58t-78.5 24q-59 0 -101 -35t-42 -93q0 -26 9 -50t17.5 -37t28.5 -37.5t26 -32.5h150v-54h-122q7 -23 7 -43q0 -78 -76 -132q25 9 52 9
q33 0 76.5 -20.5t71.5 -20.5q31 0 57 12.5t38 27.5l36 -67q-49 -47 -135 -47q-47 0 -95 23t-84 23q-42 0 -118 -40l-30 62q64 29 98.5 68.5t34.5 84.5q0 30 -15 60h-145z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="598" 
d="M299 93q-86 0 -149 47l-75 -76l-47 45l77 77q-45 64 -45 147q0 86 46 147l-79 79l47 46l78 -79q62 46 147 46q86 0 146 -46l78 78l46 -46l-78 -78q47 -63 47 -147q0 -83 -46 -148l77 -76l-46 -45l-77 75q-62 -46 -147 -46zM299 161q75 0 118.5 50.5t43.5 121.5
t-43.5 120.5t-118.5 49.5t-118.5 -50t-43.5 -120q0 -71 43.5 -121.5t118.5 -50.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="626" 
d="M355 0h-83v122h-250v53h250v107h-250v52h214l-227 333h97l207 -310l207 310h97l-227 -333h215v-52h-250v-107h250v-53h-250v-122z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="211" 
d="M133 -20h-55v316h55v-316zM133 371h-55v316h55v-316z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="468" 
d="M418 315q0 -87 -92 -128q92 -35 92 -121q0 -69 -52 -108t-138 -39q-114 0 -194 78l38 49q60 -72 156 -72q53 0 85 23t32 64q0 34 -31 53.5t-74.5 30t-87.5 23t-75 43t-31 79.5q0 50 32.5 82.5t82.5 45.5q-115 36 -115 123q0 58 48.5 97t131.5 39q117 0 178 -71l-34 -45
q-50 60 -142 60q-48 0 -78.5 -22t-30.5 -58q0 -31 31 -48.5t74.5 -26.5t87.5 -22t75 -45t31 -84zM344 305q0 39 -30.5 59t-82.5 33q-62 -16 -87 -40t-25 -59q0 -26 22 -44.5t41.5 -25t67.5 -18.5q7 -2 11 -3q83 36 83 98z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="272" 
d="M286 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM79 604q0 -19 -13.5 -33t-32.5 -14t-33 13.5t-14 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="778" 
d="M734 334q0 -143 -101 -244t-244 -101t-244 101.5t-101 243.5q0 143 101 244t244 101t244 -101t101 -244zM703 334q0 130 -92 222t-222 92t-222 -92t-92 -222t92 -222t222 -92t222 92t92 222zM512 212l26 -25q-57 -66 -147 -66q-87 0 -147.5 61.5t-60.5 152.5t60.5 152
t147.5 61q90 0 146 -66l-25 -24q-19 26 -52.5 41.5t-68.5 15.5q-70 0 -120.5 -50.5t-50.5 -129.5q0 -77 51 -129t120 -52q36 0 68.5 16t52.5 42z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="378" 
d="M317 326h-57v35q-40 -43 -107 -43q-44 0 -78 28t-34 76q0 49 33.5 76t78.5 27q67 0 107 -42v55q0 30 -23 47.5t-56 17.5q-59 0 -99 -46l-25 37q52 54 131 54q57 0 93 -27t36 -84v-211zM175 356q56 0 85 38v56q-29 38 -85 38q-33 0 -54.5 -18.5t-21.5 -47.5
q0 -30 21.5 -48t54.5 -18z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="439" 
d="M409 63h-75l-160 180l160 177h75l-160 -177zM265 63h-75l-160 180l160 177h75l-160 -177z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="504" 
d="M470 457v-246h-55v193h-386v53h441z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="300" 
d="M270 209h-240v66h240v-66z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="494" 
d="M459 465q0 -88 -62 -150t-150 -62t-150 62t-62 150t62 150t150 62q89 0 150.5 -61.5t61.5 -150.5zM430 465q0 76 -53.5 129.5t-129.5 53.5q-77 0 -130.5 -53.5t-53.5 -129.5t54 -130t130 -54t129.5 54t53.5 130zM345 343h-40l-63 96h-44v-96h-32v243h99q32 0 54.5 -20.5
t22.5 -53.5q0 -35 -22.5 -53.5t-39.5 -18.5zM309 512q0 19 -13.5 32.5t-30.5 13.5h-67v-90h67q16 0 30 13t14 31z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="363" 
d="M363 572h-363v48h363v-48z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="290" 
d="M267 555q0 -51 -36 -86.5t-87 -35.5q-50 0 -85.5 35.5t-35.5 86.5q0 50 35.5 86t85.5 36q51 0 87 -36t36 -86zM219 555q0 30 -22 52t-53 22q-30 0 -51 -22t-21 -52q0 -31 21 -52.5t51 -21.5q31 0 53 21.5t22 52.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="499" 
d="M470 324h-192v-214h-57v214h-192v52h192v207h57v-207h192v-52zM470 0h-441v52h441v-52z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="388" 
d="M341 421h-291v45q124 88 176.5 140.5t52.5 98.5q0 36 -25 54.5t-60 18.5q-73 0 -112 -52l-33 38q50 63 147 63q60 0 101.5 -30.5t41.5 -87.5q0 -56 -50 -111.5t-151 -127.5h203v-49z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="388" 
d="M343 530q0 -52 -41 -84t-110 -32q-103 0 -150 65l32 38q44 -54 116 -54q43 0 68 19t25 51q0 69 -104 69q-34 0 -40 -1v50q7 -1 39 -1q98 0 98 64q0 30 -25.5 47t-63.5 17q-64 0 -109 -49l-31 36q55 62 145 62q64 0 104 -29t40 -78q0 -40 -27 -63.5t-62 -29.5
q34 -3 65 -28t31 -69z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="226" 
d="M226 700l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="565" 
d="M150 36v-220h-75v667h75v-318q0 -112 110 -112q41 0 80 22t61 53v355h75v-370q0 -58 47 -58q11 0 19 2l6 -65q-22 -4 -40 -4q-88 0 -104 86q-27 -37 -68 -61.5t-85 -24.5q-75 0 -101 48z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="449" 
d="M378 -100h-45v722h-89v-722h-45v423q-71 0 -121.5 50.5t-50.5 121.5t50.5 121.5t121.5 50.5h179v-767z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="230" 
d="M170 244q0 -22 -16.5 -38t-38.5 -16t-38.5 16t-16.5 38t16 38.5t39 16.5t39 -16.5t16 -38.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="205" 
d="M101 -191q-63 0 -101 30l19 38q35 -29 80 -29q25 0 43 11.5t18 30.5q0 35 -37 35q-23 0 -38 -17l-33 19l31 84h45l-26 -66q16 11 37 11q29 0 47.5 -18.5t18.5 -48.5q0 -36 -30 -58t-74 -22z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="235" 
d="M175 421h-60v322l-66 -69l-35 37l108 110h53v-400z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="405" 
d="M202 318q-73 0 -117.5 47.5t-44.5 117.5t44.5 117.5t117.5 47.5q74 0 118.5 -47.5t44.5 -117.5t-44.5 -117.5t-118.5 -47.5zM202 366q48 0 76 33t28 84q0 50 -28 82.5t-76 32.5q-47 0 -74.5 -32.5t-27.5 -82.5q0 -51 27.5 -84t74.5 -33z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="439" 
d="M265 243l-160 -180h-75l160 180l-160 177h75zM409 243l-160 -180h-75l160 180l-160 177h75z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="770" 
d="M175 267h-60v322l-66 -69l-35 37l108 110h53v-400zM733 100h-58v-100h-59v100h-196v44l172 256h83v-251h58v-49zM616 149v198l-136 -198h136zM590 667l-427 -667h-55l426 667h56z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="807" 
d="M175 267h-60v322l-66 -69l-35 37l108 110h53v-400zM759 0h-291v45q124 88 176.5 140.5t52.5 98.5q0 36 -25 54.5t-60 18.5q-73 0 -112 -52l-33 38q49 63 147 63q60 0 101.5 -30.5t41.5 -87.5q0 -56 -50 -111.5t-151 -127.5h203v-49zM590 667l-427 -667h-55l426 667h56z
" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="878" 
d="M842 100h-58v-100h-59v100h-196v44l172 256h83v-251h58v-49zM725 149v198l-136 -198h136zM698 667l-427 -667h-55l426 667h56zM343 376q0 -52 -41 -84t-110 -32q-103 0 -150 65l32 38q44 -54 116 -54q43 0 68 19t25 51q0 69 -104 69q-34 0 -40 -1v50q7 -1 39 -1
q98 0 98 64q0 30 -25.5 47t-63.5 17q-64 0 -109 -49l-31 36q55 62 145 62q64 0 104 -29t40 -78q0 -40 -27 -63.5t-62 -29.5q34 -3 65 -28t31 -69z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="394" 
d="M258 296q39 -35 39 -82q0 -35 -18.5 -63t-45 -47.5t-53 -37.5t-45 -42.5t-18.5 -53.5q0 -39 30.5 -65.5t86.5 -26.5q99 0 160 81l49 -53q-78 -102 -216 -102q-89 0 -142.5 44.5t-53.5 110.5q0 42 20 75.5t48 54.5t56.5 40t48.5 42t20 50q0 28 -26 48zM238 493
q22 0 38.5 -16.5t16.5 -39.5q0 -22 -16.5 -38t-38.5 -16t-38.5 16t-16.5 38q0 23 16.5 39.5t38.5 16.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM386 723h-57l-169 144h78z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM498 867l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="658" 
d="M463 723h-51l-83 106l-80 -106h-51l95 144h72zM649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM389 721q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5
q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="658" 
d="M480 771q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM273 771q0 -19 -13.5 -33t-32.5 -14t-33 14t-14 33t14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM329 690q-41 0 -69.5 29t-28.5 69q0 41 29 70t69 29t69 -29t29 -70q0 -40 -28.5 -69t-69.5 -29zM329 729q24 0 41 17.5t17 41.5q0 25 -16.5 42t-41.5 17q-24 0 -41 -17t-17 -42
q0 -24 17 -41.5t41 -17.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="947" 
d="M893 0h-437v148h-263l-91 -148h-95l416 667h470v-74h-354v-215h347v-74h-347v-230h354v-74zM456 222v359l-222 -359h222z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="676" 
d="M388 -187q-64 0 -101 30l19 38q35 -29 80 -29q25 0 43 11.5t18 30.5q0 35 -37 35q-23 0 -38 -17l-33 19l21 59q-134 11 -221.5 105.5t-87.5 237.5q0 152 98.5 248.5t244.5 96.5q160 0 253 -132l-70 -39q-29 43 -78 70t-105 27q-110 0 -183.5 -76.5t-73.5 -194.5
t73.5 -194.5t183.5 -76.5q56 0 105 26.5t78 70.5l71 -39q-95 -128 -244 -132l-15 -39q16 11 37 11q29 0 47.5 -18.5t18.5 -48.5q0 -36 -30 -58t-74 -22z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="569" 
d="M515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74zM353 723h-57l-169 144h78z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="569" 
d="M465 867l-169 -144h-57l148 144h78zM515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="569" 
d="M428 723h-51l-83 106l-80 -106h-51l95 144h72zM515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="569" 
d="M446 771q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM239 771q0 -19 -13.5 -33t-32.5 -14t-33 14t-14 33t14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667zM176 723h-57l-169 144h78z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667zM289 867l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="239" 
d="M253 723h-51l-83 106l-80 -106h-51l95 144h72zM161 0h-83v667h83v-667z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="239" 
d="M270 771q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM63 771q0 -19 -13.5 -33t-32.5 -14t-33 14t-14 33t14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM161 0h-83v667h83v-667z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="728" 
d="M334 0h-228v299h-96v61h96v307h228q153 0 247.5 -95t94.5 -239q0 -145 -94.5 -239t-247.5 -94zM334 74q118 0 187 74t69 185q0 112 -68 186t-188 74h-145v-233h169v-61h-169v-225h145z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="708" 
d="M412 721q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37zM630 0h-80l-389 532v-532h-83v667h85
l384 -521v521h83v-667z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="765" 
d="M440 723h-57l-169 144h78zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="765" 
d="M552 867l-169 -144h-57l148 144h78zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="765" 
d="M517 723h-51l-83 106l-80 -106h-51l95 144h72zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5
z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5zM442 721q-25 0 -43.5 14.5t-27.5 31.5
t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="765" 
d="M532 771q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM325 771q0 -19 -13.5 -33t-32.5 -14t-33 14t-14 33t14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247
t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="499" 
d="M398 148l-149 149l-148 -149l-37 37l148 149l-148 148l37 38l148 -149l149 149l37 -38l-148 -148l148 -149z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="765" 
d="M382 -12q-97 0 -173 45l-22 -33h-66l44 65q-114 98 -114 268q0 149 92 247t239 98q92 0 167 -42l21 31h66l-42 -62q57 -47 88 -117.5t31 -154.5q0 -149 -92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 130 -78 205l-297 -441q55 -35 130 -35zM137 333
q0 -126 73 -201l296 440q-56 32 -124 32q-111 0 -178 -76t-67 -195z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="700" 
d="M350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-407q0 -127 -69.5 -199.5t-202.5 -72.5zM407 723h-57l-169 144h78z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="700" 
d="M519 867l-169 -144h-57l148 144h78zM350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-407q0 -127 -69.5 -199.5t-202.5 -72.5z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="700" 
d="M485 723h-51l-83 106l-80 -106h-51l95 144h72zM350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-407q0 -127 -69.5 -199.5t-202.5 -72.5z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="700" 
d="M501 771q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM294 771q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 14t-13.5 33t14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53
t140 53t49 146v406h83v-407q0 -127 -69.5 -199.5t-202.5 -72.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="626" 
d="M355 0h-83v282l-263 385h97l207 -310l207 310h97l-262 -385v-282zM482 867l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="587" 
d="M161 0h-83v667h83v-119h185q97 0 153 -58t56 -144q0 -85 -56.5 -142.5t-152.5 -57.5h-185v-146zM336 220q59 0 96 35t37 91t-37 92t-96 36h-175v-254h175z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="597" 
d="M578 135q0 -62 -48.5 -104.5t-137.5 -42.5q-66 0 -107.5 20t-78.5 58l38 52q23 -30 61.5 -49.5t86.5 -19.5q54 0 83.5 24.5t29.5 59.5q0 32 -29.5 50.5t-71.5 28.5t-84.5 22.5t-72 43t-29.5 78.5q0 37 23 66t50 44t50 35t23 41q0 32 -30 50t-69 18q-49 0 -82 -30.5
t-33 -80.5v-499h-75v499q0 75 52.5 126.5t138.5 51.5q72 0 123.5 -34.5t51.5 -91.5q0 -37 -23.5 -65t-51.5 -42.5t-51.5 -37t-23.5 -49.5q0 -28 29.5 -44.5t71.5 -25.5t84.5 -22t72 -45.5t29.5 -84.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM322 556h-57l-169 144h78z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM434 700l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM398 556h-51l-83 106l-80 -106h-51l95 144h72z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM325 554q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM415 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM208 604q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 13.5t-13.5 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM265 543q-41 0 -69.5 28.5t-28.5 69.5t29 70t69 29t69 -29t29 -70t-28.5 -69.5t-69.5 -28.5zM265 582q24 0 41 17.5t17 41.5q0 25 -16.5 42t-41.5 17q-24 0 -41 -17t-17 -42q0 -24 17 -41.5t41 -17.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="889" 
d="M843 217h-367q5 -71 48 -118t113 -47q84 0 139 61l36 -51q-72 -74 -178 -74q-136 0 -193 116q-26 -52 -79.5 -84t-119.5 -32q-82 0 -138 42.5t-56 117.5t51.5 117t126.5 42q112 0 175 -64v86q0 47 -36.5 74t-96.5 27q-94 0 -161 -67l-35 52q79 80 205 80q64 0 111 -26
t60 -79q60 105 179 105q101 0 158.5 -73t57.5 -186v-19zM476 272h293q-1 63 -38 112t-109 49q-66 0 -104.5 -49.5t-41.5 -111.5zM401 180v12q-52 61 -145 61q-59 0 -95 -27.5t-36 -73.5q0 -45 36 -72.5t95 -27.5q62 0 103.5 39t41.5 89z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="495" 
d="M289 -189q-63 0 -101 30l19 38q35 -29 80 -29q25 0 43 11.5t18 30.5q0 35 -37 35q-23 0 -38 -17l-33 19l22 61q-96 9 -155.5 79t-59.5 173q0 109 67 181t174 72q112 0 177 -84l-50 -46q-46 63 -123 63q-76 0 -121.5 -52t-45.5 -134t45.5 -134.5t121.5 -52.5t123 63
l50 -46q-60 -77 -159 -83l-16 -42q16 11 37 11q29 0 47.5 -18.5t18.5 -48.5q0 -36 -30 -58t-74 -22z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="563" 
d="M293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317zM342 556h-57l-169 144h78z
" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="563" 
d="M454 700l-169 -144h-57l148 144h78zM293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49
t-44.5 -112h317z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="563" 
d="M419 556h-51l-83 106l-80 -106h-51l95 144h72zM293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5
q-72 0 -113.5 -49t-44.5 -112h317z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="563" 
d="M436 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM229 604q0 -19 -13.5 -33t-32.5 -14t-33 13.5t-14 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5
q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="225" 
d="M150 0h-75v483h75v-483zM169 556h-57l-169 144h78z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="225" 
d="M282 700l-169 -144h-57l148 144h78zM150 0h-75v483h75v-483z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="225" 
d="M245 556h-51l-83 106l-80 -106h-51l95 144h72zM150 0h-75v483h75v-483z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="225" 
d="M263 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM56 604q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 13.5t-13.5 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM150 0h-75v483h75v-483z" />
    <glyph glyph-name="eth" unicode="&#xf0;" 
d="M122 497l-18 42l118 52q-11 7 -23.5 15t-28 18t-23.5 15l41 62q67 -41 112 -76l108 48l17 -41l-86 -38q186 -158 186 -347q0 -114 -65 -186.5t-174 -72.5q-107 0 -173 70t-66 176t62 175.5t158 69.5t158 -89q-49 88 -160 170zM286 55q74 0 117 52t43 127q0 74 -43 126
t-117 52t-117.5 -52t-43.5 -126q0 -75 43.5 -127t117.5 -52z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="551" 
d="M476 0h-75v316q0 61 -28 86.5t-80 25.5q-42 0 -81.5 -22t-61.5 -53v-353h-75v483h75v-70q28 33 75 57.5t97 24.5q154 0 154 -156v-339zM336 554q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37
q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" 
d="M286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5zM343 556h-57l-169 144h78z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" 
d="M455 700l-169 -144h-57l148 144h78zM286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" 
d="M419 556h-51l-83 106l-80 -106h-51l95 144h72zM286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z
" />
    <glyph glyph-name="otilde" unicode="&#xf5;" 
d="M286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5zM346 554q-25 0 -43.5 14.5t-27.5 31.5
t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" 
d="M436 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM229 604q0 -19 -13.5 -33t-32.5 -14t-33 13.5t-14 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5
t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="511" 
d="M302 526q0 -19 -14 -32.5t-33 -13.5t-32 13.5t-13 32.5t13 32.5t32 13.5q20 0 33.5 -13.5t13.5 -32.5zM482 311h-453v52h453v-52zM302 144q0 -19 -14 -32.5t-33 -13.5t-32 13.5t-13 32.5t13 32.5t32 13.5t33 -13.5t14 -32.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" 
d="M118 0h-54l48 61q-65 71 -65 181q0 108 65.5 180.5t173.5 72.5q79 0 137 -41l23 29h54l-45 -57q70 -72 70 -184q0 -108 -65.5 -181t-173.5 -73q-83 0 -142 45zM286 55q74 0 117 54.5t43 132.5q0 73 -38 125l-220 -278q42 -34 98 -34zM125 242q0 -70 34 -121l219 277
q-38 30 -92 30q-74 0 -117.5 -54.5t-43.5 -131.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="551" 
d="M476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483zM332 556h-57l-169 144h78z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="551" 
d="M444 700l-169 -144h-57l148 144h78zM476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="551" 
d="M408 556h-51l-83 106l-80 -106h-51l95 144h72zM476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="551" 
d="M427 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM220 604q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 13.5t-13.5 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75
v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="490" 
d="M48 -189l12 68q18 -8 41 -8q26 0 43 11t29 40l32 73l-202 488h81l161 -396l160 396h82l-242 -581q-40 -96 -142 -98q-30 0 -55 7zM414 700l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" 
d="M313 -12q-102 0 -163 85v-257h-75v851h75v-256q27 38 70 61t93 23q96 0 155.5 -68.5t59.5 -184.5t-59.5 -185t-155.5 -69zM294 55q71 0 113 52.5t42 134.5t-42 134t-113 52q-43 0 -83.5 -22.5t-60.5 -54.5v-218q20 -33 60.5 -55.5t83.5 -22.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="490" 
d="M48 -189l12 68q18 -8 41 -8q26 0 43 11t29 40l32 73l-202 488h81l161 -396l160 396h82l-242 -581q-40 -96 -142 -98q-30 0 -55 7zM395 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM188 604q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 13.5
t-13.5 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM510 739h-363v48h363v-48z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM445 572h-363v48h363v-48z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM504 793q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM439 626q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="658" 
d="M669 -105l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 57 53 99h-27l-59 148h-332l-59 -148h-95l268 667h103l269 -667q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42zM470 222l-141 359l-142 -359h283z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="527" 
d="M472 -105l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 57 53 99h-7v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332
q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5q0 -46 33.5 -75.5t85.5 -29.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="676" 
d="M563 867l-169 -144h-57l148 144h78zM394 -12q-146 0 -244.5 96.5t-98.5 248.5t98.5 248.5t244.5 96.5q160 0 253 -132l-70 -39q-29 43 -78 70t-105 27q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -194.5t183.5 -76.5q56 0 105 26.5t78 70.5l71 -39q-97 -132 -254 -132z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="495" 
d="M456 700l-169 -144h-57l148 144h78zM288 -12q-107 0 -174 72.5t-67 181.5t67 181t174 72q112 0 177 -84l-50 -46q-46 63 -123 63q-76 0 -121.5 -52t-45.5 -134t45.5 -134.5t121.5 -52.5t123 63l50 -46q-65 -84 -177 -84z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="676" 
d="M526 723h-51l-83 106l-80 -106h-51l95 144h72zM394 -12q-146 0 -244.5 96.5t-98.5 248.5t98.5 248.5t244.5 96.5q160 0 253 -132l-70 -39q-29 43 -78 70t-105 27q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -194.5t183.5 -76.5q56 0 105 26.5t78 70.5l71 -39
q-97 -132 -254 -132z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="495" 
d="M421 556h-51l-83 106l-80 -106h-51l95 144h72zM288 -12q-107 0 -174 72.5t-67 181.5t67 181t174 72q112 0 177 -84l-50 -46q-46 63 -123 63q-76 0 -121.5 -52t-45.5 -134t45.5 -134.5t121.5 -52.5t123 63l50 -46q-65 -84 -177 -84z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="676" 
d="M394 -12q-146 0 -244.5 96.5t-98.5 248.5t98.5 248.5t244.5 96.5q160 0 253 -132l-70 -39q-29 43 -78 70t-105 27q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -194.5t183.5 -76.5q56 0 105 26.5t78 70.5l71 -39q-97 -132 -254 -132zM396 722q-19 0 -33 14t-14 33t14 33
t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="495" 
d="M288 -12q-107 0 -174 72.5t-67 181.5t67 181t174 72q112 0 177 -84l-50 -46q-46 63 -123 63q-76 0 -121.5 -52t-45.5 -134t45.5 -134.5t121.5 -52.5t123 63l50 -46q-65 -84 -177 -84zM290 555q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5
t-32.5 -13.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="676" 
d="M431 723h-72l-98 144h51l83 -106l80 106h51zM394 -12q-146 0 -244.5 96.5t-98.5 248.5t98.5 248.5t244.5 96.5q160 0 253 -132l-70 -39q-29 43 -78 70t-105 27q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -194.5t183.5 -76.5q56 0 105 26.5t78 70.5l71 -39
q-97 -132 -254 -132z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="495" 
d="M326 556h-72l-98 144h51l83 -106l80 106h51zM288 -12q-107 0 -174 72.5t-67 181.5t67 181t174 72q112 0 177 -84l-50 -46q-46 63 -123 63q-76 0 -121.5 -52t-45.5 -134t45.5 -134.5t121.5 -52.5t123 63l50 -46q-65 -84 -177 -84z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="700" 
d="M389 723h-72l-98 144h51l83 -106l80 106h51zM306 0h-228v667h228q153 0 247.5 -95t94.5 -239q0 -145 -94.5 -239t-247.5 -94zM306 74q118 0 187 74t69 185q0 112 -68 186t-188 74h-145v-519h145z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="644" 
d="M648 613q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q100 0 163 -85
v257h75v-667zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="728" 
d="M334 0h-228v299h-96v61h96v307h228q153 0 247.5 -95t94.5 -239q0 -145 -94.5 -239t-247.5 -94zM334 74q118 0 187 74t69 185q0 112 -68 186t-188 74h-145v-233h169v-61h-169v-225h145z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="577" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q100 0 163 -85v125h-157v48h157v84h75v-84h68v-48h-68v-535zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="569" 
d="M515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74zM477 739h-363v48h363v-48z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="563" 
d="M293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317zM466 572h-363v48h363v-48z
" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="569" 
d="M515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74zM473 793q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="563" 
d="M293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317zM462 626q-65 -88 -175 -88
q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="569" 
d="M515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74zM300 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="563" 
d="M293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317zM286 555q-19 0 -33 14
t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="569" 
d="M515 0q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 57 53 99h-369v667h437v-74h-354v-215h347v-74h-347v-230h354v-74z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="563" 
d="M293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-46 -46 -106 -62q-75 -38 -75 -97q0 -50 44 -50q38 0 52 42l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65
q0 48 39 87h-5zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="569" 
d="M335 723h-72l-98 144h51l83 -106l80 106h51zM515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="563" 
d="M323 556h-72l-98 144h51l83 -106l80 106h51zM293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49
t-44.5 -112h317z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="713" 
d="M531 723h-51l-83 106l-80 -106h-51l95 144h72zM394 -13q-145 0 -244 96.5t-99 249.5t99 249t244 96q158 0 259 -125l-66 -41q-32 42 -83.5 67t-109.5 25q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -195t183.5 -77q55 0 102.5 21.5t76.5 50.5v136h-229v74h312v-241
q-103 -115 -262 -115z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="574" 
d="M423 556h-51l-83 106l-80 -106h-51l95 144h72zM269 -196q-64 0 -109 15.5t-86 56.5l38 56q55 -66 157 -66q67 0 111 35.5t44 109.5v69q-27 -38 -70 -62t-92 -24q-96 0 -155.5 67.5t-59.5 182.5t59.5 183t155.5 68q99 0 162 -85v73h75v-469q0 -111 -65 -160.5t-165 -49.5z
M280 60q43 0 83.5 23t60.5 56v211q-20 33 -60 55.5t-84 22.5q-72 0 -113.5 -51t-41.5 -133q0 -81 42 -132.5t113 -51.5z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="713" 
d="M394 -13q-145 0 -244 96.5t-99 249.5t99 249t244 96q158 0 259 -125l-66 -41q-32 42 -83.5 67t-109.5 25q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -195t183.5 -77q55 0 102.5 21.5t76.5 50.5v136h-229v74h312v-241q-103 -115 -262 -115zM572 793q-65 -88 -175 -88
q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="574" 
d="M269 -196q-64 0 -109 15.5t-86 56.5l38 56q55 -66 157 -66q67 0 111 35.5t44 109.5v69q-27 -38 -70 -62t-92 -24q-96 0 -155.5 67.5t-59.5 182.5t59.5 183t155.5 68q99 0 162 -85v73h75v-469q0 -111 -65 -160.5t-165 -49.5zM280 60q43 0 83.5 23t60.5 56v211
q-20 33 -60 55.5t-84 22.5q-72 0 -113.5 -51t-41.5 -133q0 -81 42 -132.5t113 -51.5zM465 626q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="713" 
d="M394 -13q-145 0 -244 96.5t-99 249.5t99 249t244 96q158 0 259 -125l-66 -41q-32 42 -83.5 67t-109.5 25q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -195t183.5 -77q55 0 102.5 21.5t76.5 50.5v136h-229v74h312v-241q-103 -115 -262 -115zM396 722q-19 0 -33 14t-14 33
t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="574" 
d="M269 -196q-64 0 -109 15.5t-86 56.5l38 56q55 -66 157 -66q67 0 111 35.5t44 109.5v69q-27 -38 -70 -62t-92 -24q-96 0 -155.5 67.5t-59.5 182.5t59.5 183t155.5 68q99 0 162 -85v73h75v-469q0 -111 -65 -160.5t-165 -49.5zM280 60q43 0 83.5 23t60.5 56v211
q-20 33 -60 55.5t-84 22.5q-72 0 -113.5 -51t-41.5 -133q0 -81 42 -132.5t113 -51.5zM289 555q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="713" 
d="M448 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM394 -13q-145 0 -244 96.5t-99 249.5t99 249t244 96q158 0 259 -125l-66 -41q-32 42 -83.5 67t-109.5 25
q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -195t183.5 -77q55 0 102.5 21.5t76.5 50.5v136h-229v74h312v-241q-103 -115 -262 -115z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="574" 
d="M238 611q0 43 19.5 81.5t49.5 59.5l33 -27q-22 -15 -39 -38.5t-18 -45.5q2 1 5 1q17 0 31.5 -13.5t14.5 -31.5t-14 -32t-34 -14q-19 0 -33.5 15.5t-14.5 44.5zM269 -196q-64 0 -109 15.5t-86 56.5l38 56q55 -66 157 -66q67 0 111 35.5t44 109.5v69q-27 -38 -70 -62
t-92 -24q-96 0 -155.5 67.5t-59.5 182.5t59.5 183t155.5 68q99 0 162 -85v73h75v-469q0 -111 -65 -160.5t-165 -49.5zM280 60q43 0 83.5 23t60.5 56v211q-20 33 -60 55.5t-84 22.5q-72 0 -113.5 -51t-41.5 -133q0 -81 42 -132.5t113 -51.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="712" 
d="M486 723h-51l-83 106l-80 -106h-51l95 144h72zM634 0h-83v306h-390v-306h-83v667h83v-287h390v287h83v-667z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="552" 
d="M410 723h-51l-83 106l-80 -106h-51l95 144h72zM477 0h-75v318q0 60 -28 85t-81 25q-41 0 -80.5 -22t-62.5 -53v-353h-75v667h75v-254q28 33 75 57.5t98 24.5q154 0 154 -154v-341z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="731" 
d="M644 0h-83v306h-390v-306h-83v498h-78v48h78v121h83v-121h390v121h83v-121h77v-48h-77v-498zM171 380h390v118h-390v-118z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="554" 
d="M308 535h-156v-122q28 33 75 57.5t98 24.5q154 0 154 -154v-341h-75v318q0 60 -28 85t-81 25q-41 0 -80.5 -22t-62.5 -53v-353h-75v535h-68v48h68v84h75v-84h156v-48z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667zM179 721q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="225" 
d="M150 0h-75v483h75v-483zM172 554q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667zM302 739h-363v48h363v-48z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="225" 
d="M150 0h-75v483h75v-483zM294 572h-363v48h363v-48z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667zM296 793q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="225" 
d="M150 0h-75v483h75v-483zM288 626q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="239" 
d="M181 -105l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 57 53 99h-15v667h83v-667q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="225" 
d="M170 -105l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 57 53 99h-7v483h75v-483q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42zM112 552q-20 0 -35 14.5t-15 35.5t15 36t35 15q21 0 36 -15t15 -36t-15 -35.5t-36 -14.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667zM120 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="225" 
d="M150 0h-75v483h75v-483z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="713" 
d="M161 0h-83v667h83v-667zM424 -12q-108 0 -174 77l43 63q55 -66 126 -66q61 0 97.5 38.5t36.5 101.5v465h83v-466q0 -104 -59 -158.5t-153 -54.5z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="451" 
d="M112 552q-20 0 -35 14.5t-15 35.5t15 36t35 15q21 0 36 -15t15 -36t-15 -35.5t-36 -14.5zM150 0h-75v483h75v-483zM337 552q-20 0 -35 14.5t-15 35.5t15 36t35 15q21 0 36 -15t15 -36t-15 -35.5t-36 -14.5zM232 -196q-59 0 -106 36l27 57q31 -31 69 -31q36 0 57 21.5
t21 65.5v530h75v-530q0 -71 -37 -110t-106 -39z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="475" 
d="M488 723h-51l-83 106l-80 -106h-51l95 144h72zM185 -12q-108 0 -174 77l43 63q55 -66 126 -66q61 0 97.5 38.5t36.5 101.5v465h83v-466q0 -104 -59 -158.5t-153 -54.5z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="225" 
d="M247 556h-51l-83 106l-80 -106h-51l95 144h72zM7 -196q-59 0 -106 36l27 57q31 -31 69 -31q36 0 57 21.5t21 65.5v530h75v-530q0 -71 -37 -110t-106 -39z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="606" 
d="M360 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM584 0h-103l-253 300l-67 -76v-224h-83v667h83v-345l295 345h104l-278 -317z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="514" 
d="M314 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM500 0h-96l-174 217l-80 -79v-138h-75v667h75v-441l254 257h95l-214 -219z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="514" 
d="M500 0h-96l-174 217l-80 -79v-138h-75v483h75v-257l254 257h95l-214 -219z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="510" 
d="M443 867l-169 -144h-57l148 144h78zM471 0h-393v667h83v-593h310v-74z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="225" 
d="M282 867l-169 -144h-57l148 144h78zM150 0h-75v667h75v-667z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="510" 
d="M319 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM471 0h-393v667h83v-593h310v-74z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="225" 
d="M170 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM150 0h-75v667h75v-667z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="510" 
d="M354 613q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM471 0h-393v667h83v-593h310v-74z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="291" 
d="M311 613q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM150 0h-75v667h75v-667z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="510" 
d="M471 0h-393v667h83v-593h310v-74zM424 341q0 -22 -16.5 -38t-38.5 -16t-38.5 16t-16.5 38q0 23 16 39t39 16t39 -16t16 -39z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="307" 
d="M150 0h-75v667h75v-667zM321 244q0 -22 -16.5 -38t-38.5 -16t-38.5 16t-16.5 38t16.5 38.5t38.5 16.5t38.5 -16.5t16.5 -38.5z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="529" 
d="M10 221v72l87 50v324h83v-276l97 56v-72l-97 -56v-245h310v-74h-393v271z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="287" 
d="M10 228v60l96 55v324h75v-281l95 54v-60l-95 -54v-326h-75v283z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="708" 
d="M531 867l-169 -144h-57l148 144h78zM630 0h-80l-389 532v-532h-83v667h85l384 -521v521h83v-667z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="551" 
d="M444 700l-169 -144h-57l148 144h78zM476 0h-75v316q0 61 -28 86.5t-80 25.5q-42 0 -81.5 -22t-61.5 -53v-353h-75v483h75v-70q28 33 75 57.5t97 24.5q154 0 154 -156v-339z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="708" 
d="M411 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM630 0h-80l-389 532v-532h-83v667h85l384 -521v521h83v-667z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="551" 
d="M330 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM476 0h-75v316q0 61 -28 86.5t-80 25.5q-42 0 -81.5 -22t-61.5 -53v-353h-75v483h75v-70q28 33 75 57.5
t97 24.5q154 0 154 -156v-339z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="708" 
d="M390 723h-72l-98 144h51l83 -106l80 106h51zM630 0h-80l-389 532v-532h-83v667h85l384 -521v521h83v-667z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="551" 
d="M314 556h-72l-98 144h51l83 -106l80 106h51zM476 0h-75v316q0 61 -28 86.5t-80 25.5q-42 0 -81.5 -22t-61.5 -53v-353h-75v483h75v-70q28 33 75 57.5t97 24.5q154 0 154 -156v-339z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="551" 
d="M161 676q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM476 0h-75v316q0 61 -28 86.5t-80 25.5q-42 0 -81.5 -22t-61.5 -53v-353h-75v483h75v-70q28 33 75 57.5
t97 24.5q154 0 154 -156v-339z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="708" 
d="M418 -196q-108 0 -174 77l44 63q53 -66 125 -66q58 0 94 34.5t39 91.5l-385 528v-532h-83v667h85l384 -521v521h83v-652q0 -104 -59 -157.5t-153 -53.5z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="551" 
d="M476 339v-386q0 -72 -37 -110.5t-106 -38.5q-59 0 -105 36l26 61q30 -30 69 -30q36 0 57 20.5t21 61.5v363q0 61 -28 86.5t-80 25.5q-42 0 -81.5 -22t-61.5 -53v-353h-75v483h75v-70q28 33 75 57.5t97 24.5q154 0 154 -156z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="765" 
d="M563 739h-363v48h363v-48zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" 
d="M467 572h-363v48h363v-48zM286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5zM557 793q-65 -88 -175 -88q-109 0 -176 88
l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" 
d="M286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5zM462 626q-65 -88 -175 -88q-109 0 -176 88
l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="765" 
d="M435 867l-115 -144h-48l94 144h69zM577 867l-115 -144h-48l94 144h69zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195
q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" 
d="M339 700l-115 -144h-48l94 144h69zM481 700l-115 -144h-48l94 144h69zM286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5
q0 -78 43.5 -132.5t117.5 -54.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1105" 
d="M1051 0h-438v120q-36 -63 -100.5 -97.5t-137.5 -34.5q-144 0 -234 98t-90 247t90 246.5t234 97.5q73 0 137.5 -34t100.5 -97v121h438v-74h-355v-215h348v-74h-348v-230h355v-74zM613 223v220q-29 78 -89.5 119t-139.5 41q-112 0 -179.5 -75.5t-67.5 -194.5t67.5 -195
t179.5 -76q79 0 139.5 41.5t89.5 119.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="962" 
d="M916 217h-390q5 -71 51.5 -119t121.5 -48q89 0 150 61l36 -49q-76 -74 -192 -74q-83 0 -133 41.5t-75 96.5q-62 -138 -199 -138q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5q42 0 78 -14t59.5 -36.5t38 -43.5t24.5 -44q22 52 71.5 95t126.5 43q107 0 169.5 -73.5
t62.5 -185.5v-19zM525 272h318q-2 62 -43 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112zM446 242q0 79 -43.5 132.5t-116.5 53.5t-117 -53.5t-44 -132.5t44 -133.5t117 -54.5t116.5 54t43.5 134z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="608" 
d="M480 867l-169 -144h-57l148 144h78zM562 0h-98l-170 265h-133v-265h-83v667h268q93 0 151 -55t58 -146q0 -86 -50 -137t-123 -57zM336 338q59 0 96 36t37 92t-37 91.5t-96 35.5h-175v-255h175z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="330" 
d="M362 700l-169 -144h-57l148 144h78zM150 0h-75v483h75v-78q67 88 161 88v-77q-14 3 -30 3q-34 0 -74 -24t-57 -53v-342z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="608" 
d="M362 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM562 0h-98l-170 265h-133v-265h-83v667h268q93 0 151 -55t58 -146q0 -86 -50 -137t-123 -57zM336 338
q59 0 96 36t37 92t-37 91.5t-96 35.5h-175v-255h175z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="330" 
d="M221 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM150 0h-75v483h75v-78q67 88 161 88v-77q-14 3 -30 3q-34 0 -74 -24t-57 -53v-342z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="608" 
d="M343 723h-72l-98 144h51l83 -106l80 106h51zM562 0h-98l-170 265h-133v-265h-83v667h268q93 0 151 -55t58 -146q0 -86 -50 -137t-123 -57zM336 338q59 0 96 36t37 92t-37 91.5t-96 35.5h-175v-255h175z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="330" 
d="M229 556h-72l-98 144h51l83 -106l80 106h51zM150 0h-75v483h75v-78q67 88 161 88v-77q-14 3 -30 3q-34 0 -74 -24t-57 -53v-342z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="586" 
d="M460 867l-169 -144h-57l148 144h78zM294 -12q-162 0 -256 106l49 63q85 -95 210 -95q81 0 119 34.5t38 80.5q0 35 -22 59t-56.5 37.5t-77 24.5t-84.5 24.5t-76.5 32.5t-56.5 54t-22 83q0 82 65 133.5t164 51.5q149 0 237 -93l-50 -61q-73 80 -193 80q-60 0 -98.5 -29.5
t-38.5 -76.5q0 -35 29.5 -58t73 -34t95 -27.5t95 -35t73 -59.5t29.5 -99q0 -82 -60.5 -139t-185.5 -57z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="465" 
d="M397 700l-169 -144h-57l148 144h78zM228 -12q-123 0 -195 76l39 54q25 -29 68.5 -49t90.5 -20q54 0 84 23t30 60q0 32 -31 50.5t-74.5 28t-87.5 22t-75 43t-31 80.5q0 59 48.5 99t131.5 40q109 0 179 -70l-35 -52q-51 61 -144 61q-49 0 -78 -21.5t-29 -54.5
q0 -29 31 -45.5t74.5 -26t87.5 -22.5t75 -45.5t31 -84.5q0 -64 -50 -105t-140 -41z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="586" 
d="M418 723h-51l-83 106l-80 -106h-51l95 144h72zM294 -12q-162 0 -256 106l49 63q85 -95 210 -95q81 0 119 34.5t38 80.5q0 35 -22 59t-56.5 37.5t-77 24.5t-84.5 24.5t-76.5 32.5t-56.5 54t-22 83q0 82 65 133.5t164 51.5q149 0 237 -93l-50 -61q-73 80 -193 80
q-60 0 -98.5 -29.5t-38.5 -76.5q0 -35 29.5 -58t73 -34t95 -27.5t95 -35t73 -59.5t29.5 -99q0 -82 -60.5 -139t-185.5 -57z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="465" 
d="M361 556h-51l-83 106l-80 -106h-51l95 144h72zM228 -12q-123 0 -195 76l39 54q25 -29 68.5 -49t90.5 -20q54 0 84 23t30 60q0 32 -31 50.5t-74.5 28t-87.5 22t-75 43t-31 80.5q0 59 48.5 99t131.5 40q109 0 179 -70l-35 -52q-51 61 -144 61q-49 0 -78 -21.5t-29 -54.5
q0 -29 31 -45.5t74.5 -26t87.5 -22.5t75 -45.5t31 -84.5q0 -64 -50 -105t-140 -41z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="586" 
d="M288 -191q-64 0 -101 30l19 38q35 -29 80 -29q25 0 43 11.5t18 30.5q0 35 -37 35q-23 0 -38 -17l-33 19l23 62q-140 11 -224 105l49 63q85 -95 210 -95q81 0 119 34.5t38 80.5q0 35 -22 59t-56.5 37.5t-77 24.5t-84.5 24.5t-76.5 32.5t-56.5 54t-22 83q0 82 65 133.5
t164 51.5q149 0 237 -93l-50 -61q-73 80 -193 80q-60 0 -98.5 -29.5t-38.5 -76.5q0 -35 29.5 -58t73 -34t95 -27.5t95 -35t73 -59.5t29.5 -99q0 -80 -57.5 -136.5t-176.5 -59.5l-17 -43q16 11 37 11q29 0 47.5 -18.5t18.5 -48.5q0 -36 -30 -58t-74 -22z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="465" 
d="M227 -191q-63 0 -101 30l19 38q35 -29 80 -29q25 0 43 11.5t18 30.5q0 35 -37 35q-23 0 -38 -17l-33 19l23 62q-105 8 -168 75l39 54q25 -29 68.5 -49t90.5 -20q54 0 84 23t30 60q0 32 -31 50.5t-74.5 28t-87.5 22t-75 43t-31 80.5q0 59 48.5 99t131.5 40q109 0 179 -70
l-35 -52q-51 61 -144 61q-49 0 -78 -21.5t-29 -54.5q0 -29 31 -45.5t74.5 -26t87.5 -22.5t75 -45.5t31 -84.5q0 -61 -45 -101t-128 -44l-17 -44q16 11 37 11q29 0 47.5 -18.5t18.5 -48.5q0 -36 -30 -58t-74 -22z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="586" 
d="M323 723h-72l-98 144h51l83 -106l80 106h51zM294 -12q-162 0 -256 106l49 63q85 -95 210 -95q81 0 119 34.5t38 80.5q0 35 -22 59t-56.5 37.5t-77 24.5t-84.5 24.5t-76.5 32.5t-56.5 54t-22 83q0 82 65 133.5t164 51.5q149 0 237 -93l-50 -61q-73 80 -193 80
q-60 0 -98.5 -29.5t-38.5 -76.5q0 -35 29.5 -58t73 -34t95 -27.5t95 -35t73 -59.5t29.5 -99q0 -82 -60.5 -139t-185.5 -57z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="465" 
d="M266 556h-72l-98 144h51l83 -106l80 106h51zM228 -12q-123 0 -195 76l39 54q25 -29 68.5 -49t90.5 -20q54 0 84 23t30 60q0 32 -31 50.5t-74.5 28t-87.5 22t-75 43t-31 80.5q0 59 48.5 99t131.5 40q109 0 179 -70l-35 -52q-51 61 -144 61q-49 0 -78 -21.5t-29 -54.5
q0 -29 31 -45.5t74.5 -26t87.5 -22.5t75 -45.5t31 -84.5q0 -64 -50 -105t-140 -41z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="570" 
d="M341 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM326 0h-83v593h-211v74h506v-74h-212v-593z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="294" 
d="M203 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM197 -12q-53 0 -80 29t-27 84v316h-80v66h80v132h75v-132h98v-66h-98v-300q0 -28 12.5 -45t36.5 -17
q33 0 51 20l22 -56q-33 -31 -90 -31z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="570" 
d="M322 723h-72l-98 144h51l83 -106l80 106h51zM326 0h-83v593h-211v74h506v-74h-212v-593z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="313" 
d="M325 676q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM197 -12q-53 0 -80 29t-27 84v316h-80v66h80v132h75v-132h98v-66h-98v-300q0 -28 12.5 -45t36.5 -17
q33 0 51 20l22 -56q-33 -31 -90 -31z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="570" 
d="M326 0h-83v294h-150v48h150v251h-211v74h506v-74h-212v-251h152v-48h-152v-294z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="294" 
d="M197 -12q-53 0 -80 29t-27 84v125h-80v48h80v143h-80v66h80v132h75v-132h98v-66h-98v-143h70v-48h-70v-109q0 -28 12.5 -45t36.5 -17q33 0 51 20l22 -56q-33 -31 -90 -31z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="700" 
d="M350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-407q0 -127 -69.5 -199.5t-202.5 -72.5zM412 721q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37
q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="551" 
d="M476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483zM335 554q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37
q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="700" 
d="M350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-407q0 -127 -69.5 -199.5t-202.5 -72.5zM532 739h-363v48h363v-48z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="551" 
d="M476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483zM456 572h-363v48h363v-48z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="700" 
d="M350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-407q0 -127 -69.5 -199.5t-202.5 -72.5zM528 793q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="551" 
d="M476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483zM452 626q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="700" 
d="M351 710q-41 0 -69.5 29t-28.5 69q0 41 29 70t69 29t69 -29t29 -70q0 -40 -28.5 -69t-69.5 -29zM351 749q24 0 41 17.5t17 41.5q0 25 -16.5 42t-41.5 17q-24 0 -41 -17t-17 -42q0 -24 17 -41.5t41 -17.5zM350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406
q0 -93 49 -146t140 -53t140 53t49 146v406h83v-407q0 -127 -69.5 -199.5t-202.5 -72.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="551" 
d="M276 543q-40 0 -69 28.5t-29 69.5t29 70t69 29t69 -29t29 -70t-29 -69.5t-69 -28.5zM276 582q24 0 41 17.5t17 41.5q0 25 -16.5 42t-41.5 17q-24 0 -41 -17t-17 -42q0 -24 17 -41.5t41 -17.5zM476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318
q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="700" 
d="M350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-407q0 -127 -69.5 -199.5t-202.5 -72.5zM404 867l-115 -144h-48l94 144h69zM546 867l-115 -144h-48l94 144h69z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="551" 
d="M476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483zM329 700l-115 -144h-48l94 144h69zM471 700l-115 -144h-48l94 144h69z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="700" 
d="M350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-407q0 -101 -44.5 -168.5t-130.5 -91.5q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 48 39 87h-15z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="551" 
d="M476 0q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 57 53 99h-7v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="883" 
d="M574 723h-51l-83 106l-80 -106h-51l95 144h72zM678 0h-90l-147 538l-147 -538h-90l-190 667h92l148 -556l153 556h69l153 -556l147 556h92z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="734" 
d="M500 556h-51l-83 106l-80 -106h-51l95 144h72zM568 0h-75l-126 388l-126 -388h-75l-154 483h78l118 -385l127 385h64l127 -385l118 385h78z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="626" 
d="M447 723h-51l-83 106l-80 -106h-51l95 144h72zM355 0h-83v282l-263 385h97l207 -310l207 310h97l-262 -385v-282z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="490" 
d="M48 -189l12 68q18 -8 41 -8q26 0 43 11t29 40l32 73l-202 488h81l161 -396l160 396h82l-242 -581q-40 -96 -142 -98q-30 0 -55 7zM378 556h-51l-83 106l-80 -106h-51l95 144h72z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="626" 
d="M465 771q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM258 771q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 14t-13.5 33t14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM355 0h-83v282l-263 385h97l207 -310l207 310h97l-262 -385v-282z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="585" 
d="M461 867l-169 -144h-57l148 144h78zM536 0h-488v69l375 524h-375v74h481v-69l-375 -524h382v-74z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="472" 
d="M404 700l-169 -144h-57l148 144h78zM420 0h-368v58l266 359h-266v66h364v-57l-268 -361h272v-65z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="585" 
d="M536 0h-488v69l375 524h-375v74h481v-69l-375 -524h382v-74zM290 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="472" 
d="M420 0h-368v58l266 359h-266v66h364v-57l-268 -361h272v-65zM235 555q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="585" 
d="M327 723h-72l-98 144h51l83 -106l80 106h51zM536 0h-488v69l375 524h-375v74h481v-69l-375 -524h382v-74z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="472" 
d="M268 556h-72l-98 144h51l83 -106l80 106h51zM420 0h-368v58l266 359h-266v66h364v-57l-268 -361h272v-65z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="283" 
d="M171 0h-75v417h-80v66h80v37q0 74 37.5 115.5t102.5 41.5q62 0 102 -37l-31 -53q-26 23 -60 23q-36 0 -56 -24t-20 -66v-520z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="431" 
d="M109 -161h-84l92 415h-68v68h82l46 204q15 70 62 110.5t112 40.5q59 0 94 -36l-37 -61q-18 23 -50 23q-36 0 -63 -24.5t-37 -67.5l-42 -189h139v-68h-154z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q154 0 249 -110q41 34 46 76q-2 -2 -11 -2q-17 0 -28 12t-11 29q0 18 12 31t30 13q21 0 35.5 -17t14.5 -46q0 -37 -18.5 -71t-46.5 -55q59 -91 59 -205q0 -149 -92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5
q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" 
d="M527 530q0 -71 -58 -120q56 -68 56 -168q0 -108 -65.5 -181t-173.5 -73t-173.5 73t-65.5 181t65.5 180.5t173.5 72.5q92 0 157 -57q37 32 42 72q-1 0 -5 -0.5t-6 -0.5q-17 0 -28.5 11.5t-11.5 29.5q0 17 12.5 30t30.5 13q21 0 35.5 -17t14.5 -46zM286 55q74 0 117 54.5
t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="700" 
d="M738 728q0 -48 -31 -90t-85 -64v-314q0 -127 -69.5 -199.5t-202.5 -72.5t-202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-54q29 15 50 41.5t23 53.5h-5q-4 -1 -6 -1q-17 0 -28 11.5t-11 29.5t12.5 30.5t30.5 12.5q21 0 35.5 -17
t14.5 -46z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" horiz-adv-x="551" 
d="M476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-48q48 36 51 81h-5q-4 -1 -6 -1q-17 0 -28.5 11.5t-11.5 29.5q0 17 12.5 30t30.5 13q21 0 36 -17t15 -46q0 -43 -25.5 -82t-68.5 -62v-392
z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="947" 
d="M668 867l-169 -144h-57l148 144h78zM893 0h-437v148h-263l-91 -148h-95l416 667h470v-74h-354v-215h347v-74h-347v-230h354v-74zM456 222v359l-222 -359h222z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="889" 
d="M586 700l-169 -144h-57l148 144h78zM843 217h-367q5 -71 48 -118t113 -47q84 0 139 61l36 -51q-72 -74 -178 -74q-136 0 -193 116q-26 -52 -79.5 -84t-119.5 -32q-82 0 -138 42.5t-56 117.5t51.5 117t126.5 42q112 0 175 -64v86q0 47 -36.5 74t-96.5 27q-94 0 -161 -67
l-35 52q79 80 205 80q64 0 111 -26t60 -79q60 105 179 105q101 0 158.5 -73t57.5 -186v-19zM476 272h293q-1 63 -38 112t-109 49q-66 0 -104.5 -49.5t-41.5 -111.5zM401 180v12q-52 61 -145 61q-59 0 -95 -27.5t-36 -73.5q0 -45 36 -72.5t95 -27.5q62 0 103.5 39t41.5 89z
" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="765" 
d="M552 867l-169 -144h-57l148 144h78zM382 -12q-97 0 -173 45l-22 -33h-66l44 65q-114 98 -114 268q0 149 92 247t239 98q92 0 167 -42l21 31h66l-42 -62q57 -47 88 -117.5t31 -154.5q0 -149 -92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 130 -78 205
l-297 -441q55 -35 130 -35zM137 333q0 -126 73 -201l296 440q-56 32 -124 32q-111 0 -178 -76t-67 -195z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" 
d="M455 700l-169 -144h-57l148 144h78zM118 0h-54l48 61q-65 71 -65 181q0 108 65.5 180.5t173.5 72.5q79 0 137 -41l23 29h54l-45 -57q70 -72 70 -184q0 -108 -65.5 -181t-173.5 -73q-83 0 -142 45zM286 55q74 0 117 54.5t43 132.5q0 73 -38 125l-220 -278q42 -34 98 -34z
M125 242q0 -70 34 -121l219 277q-38 30 -92 30q-74 0 -117.5 -54.5t-43.5 -131.5z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="586" 
d="M345 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM294 -12q-162 0 -256 106l49 63q85 -95 210 -95q81 0 119 34.5t38 80.5q0 35 -22 59t-56.5 37.5t-77 24.5
t-84.5 24.5t-76.5 32.5t-56.5 54t-22 83q0 82 65 133.5t164 51.5q149 0 237 -93l-50 -61q-73 80 -193 80q-60 0 -98.5 -29.5t-38.5 -76.5q0 -35 29.5 -58t73 -34t95 -27.5t95 -35t73 -59.5t29.5 -99q0 -82 -60.5 -139t-185.5 -57z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="465" 
d="M291 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM228 -12q-123 0 -195 76l39 54q25 -29 68.5 -49t90.5 -20q54 0 84 23t30 60q0 32 -31 50.5t-74.5 28
t-87.5 22t-75 43t-31 80.5q0 59 48.5 99t131.5 40q109 0 179 -70l-35 -52q-51 61 -144 61q-49 0 -78 -21.5t-29 -54.5q0 -29 31 -45.5t74.5 -26t87.5 -22.5t75 -45.5t31 -84.5q0 -64 -50 -105t-140 -41z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="570" 
d="M341 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM326 0h-83v593h-211v74h506v-74h-212v-593z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="294" 
d="M203 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM197 -12q-53 0 -80 29t-27 84v316h-80v66h80v132h75v-132h98v-66h-98v-300q0 -28 12.5 -45t36.5 -17
q33 0 51 20l22 -56q-33 -31 -90 -31z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="225" 
d="M7 -196q-59 0 -106 36l27 57q31 -31 69 -31q36 0 57 21.5t21 65.5v530h75v-530q0 -71 -37 -110t-106 -39z" />
    <glyph glyph-name="hsuperior" unicode="&#x2b0;" horiz-adv-x="398" 
d="M338 326h-58v202q0 72 -71 72q-54 0 -92 -46v-228h-57v434h57v-165q47 53 116 53q105 0 105 -107v-215z" />
    <glyph glyph-name="uni02BC" unicode="&#x2bc;" horiz-adv-x="230" 
d="M176 601q0 -44 -23 -84.5t-59 -66.5l-37 31q24 16 43 42.5t22 50.5q-4 -2 -14 -2q-21 0 -34.5 14.5t-13.5 36.5t15.5 38t37.5 16q26 0 44.5 -20.5t18.5 -55.5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="265" 
d="M265 556h-51l-83 106l-80 -106h-51l95 144h72z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="265" 
d="M170 556h-72l-98 144h51l83 -106l80 106h51z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="363" 
d="M363 591h-363v48h363v-48z" />
    <glyph glyph-name="uni02CB" unicode="&#x2cb;" horiz-adv-x="226" 
d="M226 556h-57l-169 144h78z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="351" 
d="M351 626q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="111" 
d="M56 555q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="196" 
d="M98 543q-40 0 -69 28.5t-29 69.5t29 70t69 29t69 -29t29 -70t-29 -69.5t-69 -28.5zM98 582q24 0 41 17.5t17 41.5q0 25 -16.5 42t-41.5 17q-24 0 -41 -17t-17 -42q0 -24 17 -41.5t41 -17.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="178" 
d="M141 -105l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 37 24 69t64 53l33 -23q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="313" 
d="M216 554q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="305" 
d="M163 700l-115 -144h-48l94 144h69zM305 700l-115 -144h-48l94 144h69z" />
    <glyph glyph-name="hookabovecomb" unicode="&#x309;" horiz-adv-x="178" 
d="M48 726l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni037E" unicode="&#x37e;" horiz-adv-x="230" 
d="M115 380q-22 0 -38.5 16.5t-16.5 38.5q0 23 16 39t39 16t39 -16t16 -39q0 -22 -16.5 -38.5t-38.5 -16.5zM176 26q0 -45 -23 -85.5t-59 -65.5l-37 30q24 16 43 43t22 51q-6 -3 -14 -3q-21 0 -34.5 14.5t-13.5 36.5q0 23 15.5 39t37.5 16q26 0 44.5 -20.5t18.5 -55.5z" />
    <glyph glyph-name="tonos" unicode="&#x384;" horiz-adv-x="199" 
d="M137 565h-37l19 200h76z" />
    <glyph glyph-name="dieresistonos" unicode="&#x385;" horiz-adv-x="551" 
d="M281 565h-38l19 200h76zM456 604q0 -19 -13.5 -33t-32.5 -14t-32.5 13.5t-13.5 33.5q0 19 13.5 32.5t32.5 13.5t32.5 -13.5t13.5 -32.5zM187 604q0 -19 -13.5 -33t-32.5 -14t-32.5 13.5t-13.5 33.5q0 19 13.5 32.5t32.5 13.5t32.5 -13.5t13.5 -32.5z" />
    <glyph glyph-name="Alphatonos" unicode="&#x386;" horiz-adv-x="658" 
d="M182 565h-37l19 200h76zM649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283z" />
    <glyph glyph-name="Epsilontonos" unicode="&#x388;" horiz-adv-x="586" 
d="M24 565h-37l19 200h76zM532 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74z" />
    <glyph glyph-name="Etatonos" unicode="&#x389;" horiz-adv-x="729" 
d="M24 565h-37l19 200h76zM651 0h-83v306h-390v-306h-83v667h83v-287h390v287h83v-667z" />
    <glyph glyph-name="Iotatonos" unicode="&#x38a;" horiz-adv-x="255" 
d="M24 565h-37l19 200h76zM178 0h-83v667h83v-667z" />
    <glyph glyph-name="Omicrontonos" unicode="&#x38c;" horiz-adv-x="765" 
d="M59 565h-37l19 200h76zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="Upsilontonos" unicode="&#x38e;" horiz-adv-x="684" 
d="M24 565h-37l19 200h76zM413 0h-83v282l-263 385h97l207 -310l207 310h97l-262 -385v-282z" />
    <glyph glyph-name="Omegatonos" unicode="&#x38f;" horiz-adv-x="769" 
d="M59 565h-37l19 200h76zM73 74h135q-153 92 -153 274q0 145 92 237.5t239 92.5q148 0 239.5 -92.5t91.5 -237.5q0 -182 -151 -274h134v-74h-240v74q71 18 121 87t50 173q0 115 -65 192t-180 77t-180 -77t-65 -192q0 -104 50.5 -173t121.5 -87v-74h-240v74z" />
    <glyph glyph-name="iotadieresistonos" unicode="&#x390;" horiz-adv-x="239" 
d="M125 565h-38l19 200h76zM300 604q0 -19 -13.5 -33t-32.5 -14t-32.5 13.5t-13.5 33.5q0 19 13.5 32.5t32.5 13.5t32.5 -13.5t13.5 -32.5zM31 604q0 -19 -13.5 -33t-32.5 -14t-32.5 13.5t-13.5 33.5q0 19 13.5 32.5t32.5 13.5t32.5 -13.5t13.5 -32.5zM182 -12q-53 0 -80 29
t-27 84v382h75v-370q0 -58 47 -58q11 0 19 2l6 -65q-22 -4 -40 -4z" />
    <glyph glyph-name="Alpha" unicode="&#x391;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283z" />
    <glyph glyph-name="Beta" unicode="&#x392;" horiz-adv-x="629" 
d="M382 0h-304v667h297q86 0 136.5 -47t50.5 -123q0 -60 -34 -101.5t-83 -51.5q53 -8 91.5 -56t38.5 -108q0 -81 -51.5 -130.5t-141.5 -49.5zM361 378q55 0 85 30t30 77t-30.5 77.5t-84.5 30.5h-200v-215h200zM365 74q58 0 91 31t33 84q0 49 -32.5 82t-91.5 33h-204v-230
h204z" />
    <glyph glyph-name="Gamma" unicode="&#x393;" horiz-adv-x="550" 
d="M161 0h-83v667h437v-74h-354v-593z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="658" 
d="M649 0h-640l268 667h103zM530 74l-201 507l-201 -507h402z" />
    <glyph glyph-name="Epsilon" unicode="&#x395;" horiz-adv-x="569" 
d="M515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74z" />
    <glyph glyph-name="Zeta" unicode="&#x396;" horiz-adv-x="585" 
d="M536 0h-488v69l375 524h-375v74h481v-69l-375 -524h382v-74z" />
    <glyph glyph-name="Eta" unicode="&#x397;" horiz-adv-x="712" 
d="M634 0h-83v306h-390v-306h-83v667h83v-287h390v287h83v-667z" />
    <glyph glyph-name="Theta" unicode="&#x398;" horiz-adv-x="765" 
d="M569 304h-373v74h373v-74zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="Iota" unicode="&#x399;" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667z" />
    <glyph glyph-name="Kappa" unicode="&#x39a;" horiz-adv-x="601" 
d="M584 0h-103l-253 300l-67 -76v-224h-83v667h83v-345l295 345h104l-278 -317z" />
    <glyph glyph-name="Lambda" unicode="&#x39b;" horiz-adv-x="658" 
d="M649 0h-95l-225 581l-225 -581h-95l268 667h103z" />
    <glyph glyph-name="Mu" unicode="&#x39c;" horiz-adv-x="809" 
d="M731 0h-83v549l-227 -549h-34l-226 549v-549h-83v667h119l207 -502l208 502h119v-667z" />
    <glyph glyph-name="Nu" unicode="&#x39d;" horiz-adv-x="708" 
d="M630 0h-80l-389 532v-532h-83v667h85l384 -521v521h83v-667z" />
    <glyph glyph-name="Xi" unicode="&#x39e;" horiz-adv-x="615" 
d="M560 593h-505v74h505v-74zM560 0h-506v74h506v-74zM554 304h-492v74h492v-74z" />
    <glyph glyph-name="Omicron" unicode="&#x39f;" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="Pi" unicode="&#x3a0;" horiz-adv-x="712" 
d="M634 0h-83v593h-390v-593h-83v667h556v-667z" />
    <glyph glyph-name="Rho" unicode="&#x3a1;" horiz-adv-x="587" 
d="M161 0h-83v667h268q97 0 153 -58t56 -143t-56.5 -143t-152.5 -58h-185v-265zM336 339q59 0 96 35.5t37 91.5t-37 91.5t-96 35.5h-175v-254h175z" />
    <glyph glyph-name="Sigma" unicode="&#x3a3;" horiz-adv-x="575" 
d="M157 593l229 -250l-232 -269h367v-74h-467v74l231 268l-230 251v74h466v-74h-364z" />
    <glyph glyph-name="Tau" unicode="&#x3a4;" horiz-adv-x="570" 
d="M326 0h-83v593h-211v74h506v-74h-212v-593z" />
    <glyph glyph-name="Upsilon" unicode="&#x3a5;" horiz-adv-x="626" 
d="M355 0h-83v282l-263 385h97l207 -310l207 310h97l-262 -385v-282z" />
    <glyph glyph-name="Phi" unicode="&#x3a6;" horiz-adv-x="806" 
d="M444 0h-83v65q-144 10 -227 82t-83 186q0 113 83 185t227 82v67h83v-67q143 -10 227 -82.5t84 -184.5q0 -114 -83.5 -186t-227.5 -82v-65zM669 333q0 80 -60 132t-165 61v-386q105 9 165 61t60 132zM137 333q0 -80 59.5 -132t164.5 -61v386q-105 -9 -164.5 -61
t-59.5 -132z" />
    <glyph glyph-name="Chi" unicode="&#x3a7;" horiz-adv-x="652" 
d="M639 0h-100l-213 284l-213 -284h-101l260 342l-245 325h101l198 -267l197 267h101l-244 -324z" />
    <glyph glyph-name="Psi" unicode="&#x3a8;" horiz-adv-x="796" 
d="M440 0h-83v125q-137 10 -208 81.5t-71 188.5v272h83v-270q0 -86 50 -137t146 -61v468h83v-467q94 9 144 60.5t50 136.5v270h84v-271q0 -118 -71 -189.5t-207 -81.5v-125z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="765" 
d="M69 74h135q-153 92 -153 274q0 145 92 237.5t239 92.5q148 0 239.5 -92.5t91.5 -237.5q0 -182 -151 -274h134v-74h-240v74q71 18 121 87t50 173q0 115 -65 192t-180 77t-180 -77t-65 -192q0 -104 50.5 -173t121.5 -87v-74h-240v74z" />
    <glyph glyph-name="Iotadieresis" unicode="&#x3aa;" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667zM269 788q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM62 788q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 13.5t-13.5 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5z" />
    <glyph glyph-name="Upsilondieresis" unicode="&#x3ab;" horiz-adv-x="626" 
d="M355 0h-83v282l-263 385h97l207 -310l207 310h97l-262 -385v-282zM463 788q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM256 788q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 13.5t-13.5 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5z" />
    <glyph glyph-name="alphatonos" unicode="&#x3ac;" horiz-adv-x="589" 
d="M303 565h-37l19 200h76zM566 57l5 -65q-20 -4 -39 -4q-91 0 -104 89q-26 -39 -70.5 -64t-95.5 -25q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-370q0 -27 12 -42.5t34 -15.5q10 0 20 2zM280 55q44 0 84 22t61 54v219
q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z" />
    <glyph glyph-name="epsilontonos" unicode="&#x3ad;" horiz-adv-x="514" 
d="M265 565h-37l19 200h76zM268 -12q-102 0 -161.5 39.5t-59.5 101.5q0 54 44.5 84t95.5 34q-49 6 -89.5 37t-40.5 81q0 61 61.5 95.5t160.5 33.5q123 0 198 -77l-39 -46q-61 62 -162 62q-61 0 -101 -21.5t-40 -55.5q0 -40 40 -60.5t103 -20.5h108v-61h-108q-152 0 -152 -80
q0 -38 38.5 -61t103.5 -23q113 0 181 65l37 -48q-79 -79 -218 -79z" />
    <glyph glyph-name="etatonos" unicode="&#x3ae;" horiz-adv-x="551" 
d="M284 565h-37l19 200h76zM476 -184h-75v500q0 61 -28 86.5t-80 25.5q-42 0 -81.5 -22t-61.5 -53v-353h-75v483h75v-70q28 33 75 57.5t97 24.5q154 0 154 -156v-523z" />
    <glyph glyph-name="iotatonos" unicode="&#x3af;" horiz-adv-x="239" 
d="M129 565h-37l19 200h76zM182 -12q-53 0 -80 29t-27 84v382h75v-370q0 -58 47 -58q11 0 19 2l6 -65q-22 -4 -40 -4z" />
    <glyph glyph-name="upsilondieresistonos" unicode="&#x3b0;" horiz-adv-x="562" 
d="M286 565h-38l19 200h76zM461 604q0 -19 -13.5 -33t-32.5 -14t-32.5 13.5t-13.5 33.5q0 19 13.5 32.5t32.5 13.5t32.5 -13.5t13.5 -32.5zM192 604q0 -19 -13.5 -33t-32.5 -14t-32.5 13.5t-13.5 33.5q0 19 13.5 32.5t32.5 13.5t32.5 -13.5t13.5 -32.5zM275 -12
q-98 0 -149 60t-51 164v271h75v-270q0 -74 33 -116t92 -42q75 0 118 55.5t43 139.5q0 126 -73 211l67 34q85 -107 85 -245q0 -114 -65.5 -188t-174.5 -74z" />
    <glyph glyph-name="alpha" unicode="&#x3b1;" horiz-adv-x="589" 
d="M566 57l5 -65q-20 -4 -39 -4q-91 0 -104 89q-26 -39 -70.5 -64t-95.5 -25q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-370q0 -27 12 -42.5t34 -15.5q10 0 20 2zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5
q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z" />
    <glyph glyph-name="beta" unicode="&#x3b2;" horiz-adv-x="575" 
d="M150 455v-324q22 -33 62.5 -54.5t86.5 -21.5q72 0 111.5 35t39.5 94q0 61 -44 92.5t-113 31.5h-35v67h33q63 0 103 30t40 87q0 54 -37.5 86t-93.5 32q-65 0 -109 -40.5t-44 -114.5zM150 72v-256h-75v639q0 109 67 165.5t161 56.5q92 0 151 -47.5t59 -130.5
q0 -66 -42.5 -105.5t-96.5 -49.5q55 -5 104.5 -49t49.5 -122q0 -82 -59 -133.5t-148 -51.5q-109 0 -171 84z" />
    <glyph glyph-name="gamma" unicode="&#x3b3;" horiz-adv-x="490" 
d="M283 -184h-75v135q0 151 -60.5 295t-154.5 237h88q66 -66 117 -179t62 -211q55 77 91 184t36 206h75q0 -116 -49 -245t-130 -233v-189z" />
    <glyph glyph-name="delta" unicode="&#x3b4;" 
d="M289 -12q-108 0 -175 70t-67 165q0 74 38.5 128t102.5 81q-82 36 -82 108q0 59 48 99t131 40q111 0 179 -70l-35 -52q-51 61 -144 61q-49 0 -78 -21.5t-29 -54.5q0 -22 19 -37.5t49.5 -25.5t68 -21t74.5 -28.5t67.5 -42t49.5 -67.5t19 -100q0 -93 -65 -162.5t-171 -69.5z
M452 220q0 43 -14.5 74t-45 52t-55 31.5t-67.5 24.5q-71 -21 -108 -68t-37 -111q0 -65 44.5 -116.5t117.5 -51.5q74 0 119.5 51t45.5 114z" />
    <glyph glyph-name="epsilon" unicode="&#x3b5;" horiz-adv-x="514" 
d="M268 -12q-102 0 -161.5 39.5t-59.5 101.5q0 54 44.5 84t95.5 34q-49 6 -89.5 37t-40.5 81q0 61 61.5 95.5t160.5 33.5q123 0 198 -77l-39 -46q-61 62 -162 62q-61 0 -101 -21.5t-40 -55.5q0 -40 40 -60.5t103 -20.5h108v-61h-108q-152 0 -152 -80q0 -38 38.5 -61
t103.5 -23q113 0 181 65l37 -48q-79 -79 -218 -79z" />
    <glyph glyph-name="zeta" unicode="&#x3b6;" horiz-adv-x="489" 
d="M371 -48q0 20 -13 28t-44 8q-120 0 -193.5 60t-73.5 163q0 227 300 390h-284v66h382v-57q-320 -184 -320 -394q0 -77 53.5 -119t144.5 -42q67 0 97 -21t30 -68q0 -46 -44 -123h-81q46 71 46 109z" />
    <glyph glyph-name="eta" unicode="&#x3b7;" horiz-adv-x="551" 
d="M476 -184h-75v500q0 61 -28 86.5t-80 25.5q-42 0 -81.5 -22t-61.5 -53v-353h-75v483h75v-70q28 33 75 57.5t97 24.5q154 0 154 -156v-523z" />
    <glyph glyph-name="theta" unicode="&#x3b8;" 
d="M286 -12q-116 0 -177.5 97.5t-61.5 248.5t61.5 248t177.5 97t177.5 -97t61.5 -248t-61.5 -248.5t-177.5 -97.5zM286 55q150 0 159 247h-319q5 -114 43.5 -180.5t116.5 -66.5zM286 612q-77 0 -116 -66.5t-44 -177.5h319q-5 112 -43.5 178t-115.5 66z" />
    <glyph glyph-name="iota" unicode="&#x3b9;" horiz-adv-x="239" 
d="M182 -12q-53 0 -80 29t-27 84v382h75v-370q0 -58 47 -58q11 0 19 2l6 -65q-22 -4 -40 -4z" />
    <glyph glyph-name="kappa" unicode="&#x3ba;" horiz-adv-x="514" 
d="M500 0h-96l-174 217l-80 -79v-138h-75v483h75v-257l254 257h95l-214 -219z" />
    <glyph glyph-name="lambda" unicode="&#x3bb;" horiz-adv-x="490" 
d="M60 604l-12 67q22 8 55 8q102 -2 142 -98l242 -581h-82l-160 396l-161 -396h-81l202 487l-32 73q-12 29 -29 40.5t-43 11.5q-23 0 -41 -8z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="565" 
d="M150 36v-220h-75v667h75v-318q0 -112 110 -112q41 0 80 22t61 53v355h75v-370q0 -58 47 -58q11 0 19 2l6 -65q-22 -4 -40 -4q-88 0 -104 86q-27 -37 -68 -61.5t-85 -24.5q-75 0 -101 48z" />
    <glyph glyph-name="nu" unicode="&#x3bd;" horiz-adv-x="490" 
d="M279 0h-75l-201 483h81l164 -407q64 89 101.5 197t37.5 210h75q0 -117 -50 -248t-133 -235z" />
    <glyph glyph-name="xi" unicode="&#x3be;" horiz-adv-x="489" 
d="M314 -12q-128 0 -197.5 48.5t-69.5 135.5q0 69 42.5 110t94.5 51q-48 8 -87.5 41t-39.5 89q0 44 31 82.5t77 55.5h-100v66h371v-66h-187q-49 -11 -81 -45.5t-32 -74.5q0 -51 43.5 -78.5t108.5 -27.5h143v-65h-143q-73 0 -118 -31.5t-45 -94.5q0 -62 52 -95.5t146 -33.5
q67 0 97 -21t30 -68q0 -46 -44 -123h-81q46 71 46 109q0 20 -13 28t-44 8z" />
    <glyph glyph-name="omicron" unicode="&#x3bf;" 
d="M286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="582" 
d="M492 0h-75v417h-252v-417h-75v417h-80v66h562v-66h-80v-417z" />
    <glyph glyph-name="rho" unicode="&#x3c1;" 
d="M313 -12q-102 0 -163 85v-257h-75v426q0 108 61.5 180.5t163.5 72.5q103 0 165.5 -72.5t62.5 -180.5q0 -116 -59.5 -185t-155.5 -69zM294 55q71 0 113 52.5t42 134.5q0 78 -40 132t-110 54q-69 0 -109 -54t-40 -132v-109q20 -33 60.5 -55.5t83.5 -22.5z" />
    <glyph glyph-name="sigma1" unicode="&#x3c2;" horiz-adv-x="483" 
d="M316 -12q-122 0 -195.5 68t-73.5 186q0 109 67 181t174 72q112 0 177 -84l-50 -46q-46 63 -123 63q-76 0 -121.5 -52t-45.5 -134q0 -91 53 -139t142 -48q67 0 98.5 -21t31.5 -68q0 -45 -43 -123h-78q46 71 46 109q0 36 -59 36z" />
    <glyph glyph-name="sigma" unicode="&#x3c3;" 
d="M520 225q0 -100 -63.5 -168.5t-170.5 -68.5q-108 0 -173.5 73t-65.5 181q0 103 68 172t184 69h248v-67h-115q88 -69 88 -191zM446 236q0 62 -27.5 109.5t-72.5 70.5h-47q-82 0 -128 -51.5t-46 -122.5q0 -78 43.5 -132.5t117.5 -54.5t117 53.5t43 127.5z" />
    <glyph glyph-name="tau" unicode="&#x3c4;" horiz-adv-x="468" 
d="M303 -12q-53 0 -80 29t-27 84v316h-170v66h416v-66h-171v-300q0 -28 12.5 -45t36.5 -17q33 0 51 20l22 -56q-33 -31 -90 -31z" />
    <glyph glyph-name="upsilon" unicode="&#x3c5;" horiz-adv-x="562" 
d="M275 -12q-98 0 -149 60t-51 164v271h75v-270q0 -74 33 -116t92 -42q75 0 118 55.5t43 139.5q0 126 -73 211l67 34q85 -107 85 -245q0 -114 -65.5 -188t-174.5 -74z" />
    <glyph glyph-name="phi" unicode="&#x3c6;" horiz-adv-x="744" 
d="M696 242q0 -109 -73 -177t-214 -76v-173h-75v174q-142 8 -214.5 77.5t-72.5 186.5q0 83 50 144t129 97l32 -58q-133 -65 -133 -183q0 -84 50.5 -136.5t158.5 -61.5v439h38q160 0 242 -68.5t82 -184.5zM618 242q0 79 -53 129.5t-156 55.5v-371q107 8 158 58.5t51 127.5z
" />
    <glyph glyph-name="chi" unicode="&#x3c7;" horiz-adv-x="486" 
d="M475 -184h-84l-148 273l-148 -273h-83l185 343l-175 324h84l137 -254l137 254h84l-175 -324z" />
    <glyph glyph-name="psi" unicode="&#x3c8;" horiz-adv-x="744" 
d="M409 -184h-75v173q-127 8 -193 69.5t-66 163.5v261h75v-261q0 -72 48 -114.5t136 -50.5v610h75v-611q87 8 136 51t49 115v261h75v-261q0 -103 -66.5 -164t-193.5 -69v-173z" />
    <glyph glyph-name="omega" unicode="&#x3c9;" horiz-adv-x="808" 
d="M760 242q0 -108 -52 -181t-137 -73q-66 0 -108 43t-59 104q-18 -62 -60 -104.5t-107 -42.5q-86 0 -138 73t-52 181q0 172 98 253l54 -46q-74 -69 -74 -207q0 -78 32.5 -132.5t88.5 -54.5t88 54.5t32 132.5v123h75v-123q0 -78 32 -132.5t88 -54.5t88.5 54.5t32.5 132.5
q0 139 -74 207l55 46q97 -80 97 -253z" />
    <glyph glyph-name="iotadieresis" unicode="&#x3ca;" horiz-adv-x="239" 
d="M270 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM63 604q0 -19 -13.5 -33t-32.5 -14t-33 13.5t-14 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM182 -12q-53 0 -80 29t-27 84v382h75v-370q0 -58 47 -58q11 0 19 2l6 -65
q-22 -4 -40 -4z" />
    <glyph glyph-name="upsilondieresis" unicode="&#x3cb;" horiz-adv-x="562" 
d="M430 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM223 604q0 -19 -13.5 -33t-32.5 -14t-33 13.5t-14 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM275 -12q-98 0 -149 60t-51 164v271h75v-270q0 -74 33 -116t92 -42
q75 0 118 55.5t43 139.5q0 126 -73 211l67 34q85 -107 85 -245q0 -114 -65.5 -188t-174.5 -74z" />
    <glyph glyph-name="omicrontonos" unicode="&#x3cc;" 
d="M294 565h-37l19 200h76zM286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z" />
    <glyph glyph-name="upsilontonos" unicode="&#x3cd;" horiz-adv-x="562" 
d="M290 565h-37l19 200h76zM275 -12q-98 0 -149 60t-51 164v271h75v-270q0 -74 33 -116t92 -42q75 0 118 55.5t43 139.5q0 126 -73 211l67 34q85 -107 85 -245q0 -114 -65.5 -188t-174.5 -74z" />
    <glyph glyph-name="omegatonos" unicode="&#x3ce;" horiz-adv-x="808" 
d="M412 565h-37l19 200h76zM760 242q0 -108 -52 -181t-137 -73q-66 0 -108 43t-59 104q-18 -62 -60 -104.5t-107 -42.5q-86 0 -138 73t-52 181q0 172 98 253l54 -46q-74 -69 -74 -207q0 -78 32.5 -132.5t88.5 -54.5t88 54.5t32 132.5v123h75v-123q0 -78 32 -132.5t88 -54.5
t88.5 54.5t32.5 132.5q0 139 -74 207l55 46q97 -80 97 -253z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="569" 
d="M446 771q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM239 771q0 -19 -13.5 -33t-32.5 -14t-33 14t-14 33t14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="769" 
d="M501 -12v74q62 0 98 37t36 101v8q0 72 -39.5 109.5t-115.5 37.5q-83 0 -154 -23v-332h-83v593h-211v74h506v-74h-212v-187q84 23 161 23q113 0 172 -61t59 -160v-8q0 -104 -61 -158t-156 -54z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="550" 
d="M454 867l-169 -144h-57l148 144h78zM161 0h-83v667h437v-74h-354v-593z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="676" 
d="M394 -12q-146 0 -244.5 96.5t-98.5 248.5t98.5 248.5t244.5 96.5q160 0 253 -132l-70 -39q-29 43 -78 70t-105 27q-99 0 -169.5 -62.5t-84.5 -163.5h365v-74h-367q10 -107 81.5 -174.5t174.5 -67.5q56 0 105 26.5t78 70.5l71 -39q-97 -132 -254 -132z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="586" 
d="M294 -12q-162 0 -256 106l49 63q85 -95 210 -95q81 0 119 34.5t38 80.5q0 35 -22 59t-56.5 37.5t-77 24.5t-84.5 24.5t-76.5 32.5t-56.5 54t-22 83q0 82 65 133.5t164 51.5q149 0 237 -93l-50 -61q-73 80 -193 80q-60 0 -98.5 -29.5t-38.5 -76.5q0 -35 29.5 -58t73 -34
t95 -27.5t95 -35t73 -59.5t29.5 -99q0 -82 -60.5 -139t-185.5 -57z" />
    <glyph glyph-name="afii10055" unicode="&#x406;" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667zM269 771q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM62 771q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 14t-13.5 33t14 32.5t33 13.5t32.5 -13.5t13.5 -32.5z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="475" 
d="M185 -12q-108 0 -174 77l43 63q55 -66 126 -66q61 0 97.5 38.5t36.5 101.5v465h83v-466q0 -104 -59 -158.5t-153 -54.5z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="1060" 
d="M32 -12v74q43 0 70 20t48 79.5t35 170.5l41 335h408v-265h185q96 0 152.5 -58t56.5 -143t-56 -143t-153 -58h-268v593h-251l-33 -267q-13 -107 -34.5 -176t-53 -103.5t-65 -46.5t-82.5 -12zM809 328h-175v-254h175q59 0 96 35.5t37 91.5t-37 91.5t-96 35.5z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="1060" 
d="M78 0v667h83v-287h390v287h83v-287h197q91 0 144 -55t53 -135q0 -81 -53 -135.5t-144 -54.5h-280v306h-390v-306h-83zM634 74h187q54 0 87.5 32t33.5 84q0 51 -33.5 83.5t-87.5 32.5h-187v-232z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="785" 
d="M635 0v201q0 154 -155 154q-83 0 -154 -23v-332h-83v593h-211v74h506v-74h-212v-187q84 23 161 23q114 0 172.5 -61.5t58.5 -167.5v-200h-83z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="601" 
d="M473 867l-169 -144h-57l148 144h78zM584 0h-103l-253 300l-67 -76v-224h-83v667h83v-345l295 345h104l-278 -317z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="626" 
d="M145 -12q-41 0 -76 15t-54 38l35 66q43 -45 91 -45q38 0 60.5 20t51.5 75l12 23l-256 487h95l209 -413l209 413h95l-291 -549q-35 -66 -75 -98t-106 -32zM489 793q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="712" 
d="M78 0v667h83v-593h390v593h83v-667h-236v-127h-83v127h-237z" />
    <glyph glyph-name="afii10017" unicode="&#x410;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" horiz-adv-x="587" 
d="M78 667h433v-74h-350v-191h185q96 0 152.5 -58t56.5 -143t-56 -143t-153 -58h-268v667zM336 328h-175v-254h175q59 0 96 35.5t37 91.5t-37 91.5t-96 35.5z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="629" 
d="M382 0h-304v667h297q86 0 136.5 -47t50.5 -123q0 -60 -34 -101.5t-83 -51.5q53 -8 91.5 -56t38.5 -108q0 -81 -51.5 -130.5t-141.5 -49.5zM361 378q55 0 85 30t30 77t-30.5 77.5t-84.5 30.5h-200v-215h200zM365 74q58 0 91 31t33 84q0 49 -32.5 82t-91.5 33h-204v-230
h204z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="550" 
d="M161 0h-83v667h437v-74h-354v-593z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="723" 
d="M32 -123v190q44 3 70 21t47.5 76t35.5 168l41 335h408v-593h61v-197h-83v123h-497v-123h-83zM267 326q-23 -193 -102 -252h386v519h-251z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="569" 
d="M515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="909" 
d="M120 0h-103l302 350l-278 317h104l268 -314v314h83v-314l269 314h104l-279 -317l303 -350h-104l-253 300l-40 -46v-254h-83v254l-40 46z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="584" 
d="M280 -12q-84 0 -151 31t-100 79l49 53q30 -40 84.5 -64.5t115.5 -24.5q78 0 123 33.5t45 91.5q0 60 -46.5 89t-126.5 29h-136v74h136q71 0 116.5 27.5t45.5 82.5q0 54 -45.5 84t-114.5 30q-113 0 -189 -81l-46 52q40 46 102.5 74.5t138.5 28.5q102 0 170.5 -48t68.5 -130
q0 -66 -44.5 -105.5t-99.5 -49.5q55 -5 104.5 -48t49.5 -116q0 -84 -69 -138t-181 -54z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="708" 
d="M158 0h-80v667h83v-521l384 521h85v-667h-83v532z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="708" 
d="M158 0h-80v667h83v-521l384 521h85v-667h-83v532zM529 793q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="601" 
d="M584 0h-103l-253 300l-67 -76v-224h-83v667h83v-345l295 345h104l-278 -317z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="712" 
d="M32 -12v74q43 0 70 20t48 79.5t35 170.5l41 335h408v-667h-83v593h-251l-33 -267q-13 -107 -34.5 -176t-53 -103.5t-65 -46.5t-82.5 -12z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="809" 
d="M731 0h-83v549l-227 -549h-34l-226 549v-549h-83v667h119l207 -502l208 502h119v-667z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="712" 
d="M634 0h-83v306h-390v-306h-83v667h83v-287h390v287h83v-667z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="712" 
d="M634 0h-83v593h-390v-593h-83v667h556v-667z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" horiz-adv-x="587" 
d="M161 0h-83v667h268q97 0 153 -58t56 -143t-56.5 -143t-152.5 -58h-185v-265zM336 339q59 0 96 35.5t37 91.5t-37 91.5t-96 35.5h-175v-254h175z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="676" 
d="M394 -12q-146 0 -244.5 96.5t-98.5 248.5t98.5 248.5t244.5 96.5q160 0 253 -132l-70 -39q-29 43 -78 70t-105 27q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -194.5t183.5 -76.5q56 0 105 26.5t78 70.5l71 -39q-97 -132 -254 -132z" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="570" 
d="M326 0h-83v593h-211v74h506v-74h-212v-593z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="626" 
d="M145 -12q-41 0 -76 15t-54 38l35 66q43 -45 91 -45q38 0 60.5 20t51.5 75l12 23l-256 487h95l209 -413l209 413h95l-291 -549q-35 -66 -75 -98t-106 -32z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="806" 
d="M444 0h-83v65q-144 10 -227 82t-83 186q0 113 83 185t227 82v67h83v-67q143 -10 227 -82.5t84 -184.5q0 -114 -83.5 -186t-227.5 -82v-65zM669 333q0 80 -60 132t-165 61v-386q105 9 165 61t60 132zM137 333q0 -80 59.5 -132t164.5 -61v386q-105 -9 -164.5 -61
t-59.5 -132z" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="652" 
d="M639 0h-100l-213 284l-213 -284h-101l260 342l-245 325h101l198 -267l197 267h101l-244 -324z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="723" 
d="M612 -123v123h-534v667h83v-593h390v593h83v-593h61v-197h-83z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="611" 
d="M141 667v-174q0 -154 155 -154q83 0 154 23v305h83v-667h-83v288q-84 -23 -161 -23q-114 0 -172.5 61.5t-58.5 167.5v173h83z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="958" 
d="M797 667h83v-667h-802v667h83v-593h277v593h83v-593h276v593z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="975" 
d="M803 667h83v-593h60v-197h-83v123h-780v667h83v-593h277v593h83v-593h277v593z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="752" 
d="M511 0h-268v593h-211v74h294v-265h185q96 0 152.5 -58t56.5 -143t-56 -143t-153 -58zM501 328h-175v-254h175q59 0 96 35.5t37 91.5t-37 91.5t-96 35.5z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="788" 
d="M78 667h83v-265h185q96 0 152.5 -58t56.5 -143t-56 -143t-153 -58h-268v667zM336 328h-175v-254h175q59 0 96 35.5t37 91.5t-37 91.5t-96 35.5zM710 0h-83v667h83v-667z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" horiz-adv-x="587" 
d="M78 667h83v-265h185q96 0 152.5 -58t56.5 -143t-56 -143t-153 -58h-268v667zM336 328h-175v-254h175q59 0 96 35.5t37 91.5t-37 91.5t-96 35.5z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="676" 
d="M282 -12q-157 0 -254 132l71 39q29 -44 78 -70.5t105 -26.5q103 0 174.5 67.5t80.5 174.5h-366v74h364q-14 101 -84 163.5t-169 62.5q-56 0 -105 -27t-78 -70l-70 39q93 132 253 132q146 0 244.5 -96.5t98.5 -248.5t-98.5 -248.5t-244.5 -96.5z" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="992" 
d="M609 -12q-140 0 -231 89.5t-99 228.5h-118v-306h-83v667h83v-287h119q15 132 105 215t224 83q146 0 239 -98t93 -247t-93 -247t-239 -98zM609 62q110 0 178 76.5t68 194.5t-68 194.5t-178 76.5q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="afii10049" unicode="&#x42f;" horiz-adv-x="608" 
d="M144 0h-97l180 272q-73 6 -123.5 57t-50.5 137q0 91 58.5 146t151.5 55h268v-667h-83v265h-134zM272 338h176v255h-176q-59 0 -96 -35.5t-37 -91.5t37.5 -92t95.5 -36z" />
    <glyph glyph-name="afii10065" unicode="&#x430;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="577" 
d="M286 55q74 0 117 54.5t43 133.5q0 77 -43 131t-117 54t-117.5 -54t-43.5 -131q0 -79 43.5 -133.5t117.5 -54.5zM286 -12q-109 0 -174 77t-65 199q0 172 62 254.5t191 99.5q77 12 106.5 23t29.5 26h73q0 -28 -17 -48.5t-48.5 -32.5t-59 -18.5t-65.5 -11.5
q-100 -14 -144.5 -55t-58.5 -109q66 103 182 103q100 0 163 -73t63 -179q0 -108 -65 -181.5t-173 -73.5z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="554" 
d="M346 0h-271v483h266q63 0 99.5 -34t36.5 -90q0 -41 -22.5 -70.5t-55.5 -39.5q37 -9 62.5 -43t25.5 -75q0 -59 -37.5 -95t-103.5 -36zM331 66q36 0 57 20t21 54q0 31 -21 53t-57 22h-181v-149h181zM328 281q34 0 53 18.5t19 48.5q0 32 -19 50.5t-53 18.5h-178v-136h178z
" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="436" 
d="M413 483v-66h-263v-417h-75v483h338z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="561" 
d="M18 -123v185q37 7 58 44.5t34 138.5l31 238h336v-417h56v-189h-75v123h-365v-123h-75zM207 417l-22 -181q-17 -127 -70 -170h287v351h-195z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="563" 
d="M293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="711" 
d="M110 0h-96l215 264l-214 219h95l208 -213v213h75v-212l208 212h95l-215 -219l216 -264h-96l-174 217l-34 -34v-183h-75v183l-34 34z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="514" 
d="M246 -12q-139 0 -218 79l37 48q68 -65 181 -65q65 0 103.5 23t38.5 61q0 80 -152 80h-108v61h108q63 0 103 20.5t40 60.5q0 34 -40 55.5t-101 21.5q-101 0 -162 -62l-39 46q75 77 198 77q99 1 160.5 -33.5t61.5 -95.5q0 -50 -40.5 -81t-89.5 -37q51 -4 95.5 -34t44.5 -84
q0 -62 -59.5 -101.5t-161.5 -39.5z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="552" 
d="M147 0h-72v483h75v-369l253 369h74v-483h-75v376z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="552" 
d="M147 0h-72v483h75v-369l253 369h74v-483h-75v376zM451 626q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="514" 
d="M500 0h-96l-174 217l-80 -79v-138h-75v483h75v-257l254 257h95l-214 -219z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="552" 
d="M18 -12v62q34 0 56 45t36 150l31 238h336v-483h-75v417h-195l-22 -181q-16 -133 -56 -190.5t-111 -57.5z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="643" 
d="M568 0h-75v379l-157 -379h-28l-158 379v-379h-75v483h99l148 -358l146 358h100v-483z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" horiz-adv-x="552" 
d="M150 0h-75v483h75v-201h252v201h75v-483h-75v216h-252v-216z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" 
d="M286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" horiz-adv-x="552" 
d="M477 0h-75v417h-252v-417h-75v483h402v-483z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" 
d="M313 -12q-102 0 -163 85v-257h-75v667h75v-72q27 38 70 61t93 23q96 0 155.5 -68.5t59.5 -184.5t-59.5 -185t-155.5 -69zM294 55q71 0 113 52.5t42 134.5t-42 134t-113 52q-43 0 -83.5 -22.5t-60.5 -54.5v-218q20 -33 60.5 -55.5t83.5 -22.5z" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="495" 
d="M288 -12q-107 0 -174 72.5t-67 181.5t67 181t174 72q112 0 177 -84l-50 -46q-46 63 -123 63q-76 0 -121.5 -52t-45.5 -134t45.5 -134.5t121.5 -52.5t123 63l50 -46q-65 -84 -177 -84z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="402" 
d="M238 0h-75v417h-140v66h356v-66h-141v-417z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="490" 
d="M48 -189l12 68q18 -8 41 -8q26 0 43 11t29 40l32 73l-202 488h81l161 -396l160 396h82l-242 -581q-40 -96 -142 -98q-30 0 -55 7z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="798" 
d="M230 495q82 0 132 -85v257h75v-256q22 38 56.5 61t74.5 23q78 0 126 -68.5t48 -184.5t-48.5 -185t-125.5 -69q-82 0 -131 85v-257h-75v256q-22 -38 -57 -61t-75 -23q-78 0 -126 68.5t-48 184.5t48.5 185t125.5 69zM251 428q-55 0 -87 -52.5t-32 -134.5t32 -134t87 -52
q34 0 65 22t46 55v218q-16 33 -47 55.5t-64 22.5zM547 55q55 0 87 52.5t32 134.5t-32 134t-87 52q-33 0 -64 -22.5t-46 -54.5v-218q16 -33 46.5 -55.5t63.5 -22.5z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="486" 
d="M475 0h-87l-145 198l-145 -198h-86l185 248l-175 235h87l134 -184l134 184h87l-175 -235z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="561" 
d="M458 -123v123h-383v483h75v-417h252v417h75v-417h56v-189h-75z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="541" 
d="M65 483h75v-136q0 -65 30 -90t93 -25q78 0 128 26v225h75v-483h-75v197q-64 -32 -147 -32q-94 0 -136.5 38t-42.5 121v159z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="783" 
d="M633 483h75v-483h-633v483h75v-417h204v417h75v-417h204v417z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="798" 
d="M633 483h75v-417h62v-189h-75v123h-620v483h75v-417h204v417h75v-417h204v417z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="610" 
d="M419 0h-256v417h-140v66h215v-187h181q72 0 112 -42.5t40 -105.5t-40.5 -105.5t-111.5 -42.5zM411 230h-173v-164h173q40 0 61.5 22.5t21.5 59.5t-21.5 59.5t-61.5 22.5z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="712" 
d="M75 483h75v-187h181q72 0 112 -42.5t40 -105.5t-40.5 -105.5t-111.5 -42.5h-256v483zM323 230h-173v-164h173q40 0 61.5 22.5t21.5 59.5t-21.5 59.5t-61.5 22.5zM637 0h-75v483h75v-483z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="522" 
d="M75 483h75v-187h181q72 0 112 -42.5t40 -105.5t-40.5 -105.5t-111.5 -42.5h-256v483zM323 230h-173v-164h173q40 0 61.5 22.5t21.5 59.5t-21.5 59.5t-61.5 22.5z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="495" 
d="M207 -12q-112 0 -177 84l48 44q49 -66 126 -66q69 0 112.5 44t51.5 118h-243v65h242q-9 71 -52.5 113.5t-110.5 42.5q-78 0 -126 -66l-48 44q65 84 177 84q107 0 174.5 -72t67.5 -181t-67.5 -181.5t-174.5 -72.5z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="767" 
d="M480 -12q-100 0 -164.5 64.5t-72.5 163.5h-93v-216h-75v483h75v-201h94q12 93 75.5 153t160.5 60q108 0 174 -72.5t66 -180.5t-66 -181t-174 -73zM480 55q75 0 118 54.5t43 132.5t-43 132t-118 54q-73 0 -116.5 -54.5t-43.5 -131.5q0 -78 43 -132.5t117 -54.5z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" horiz-adv-x="547" 
d="M147 0h-88l129 190q-55 7 -89.5 46t-34.5 99q0 64 41 106t112 42h255v-483h-75v187h-132zM225 253h172v164h-172q-39 0 -61.5 -22.5t-22.5 -59.5t22 -59.5t62 -22.5z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="563" 
d="M435 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM228 604q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 13.5t-13.5 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5
q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="554" 
d="M309 535h-157v-122q28 33 75 57.5t98 24.5q154 0 154 -154v-388q0 -71 -37 -110t-107 -39q-60 0 -105 36l26 57q31 -31 69 -31q36 0 57.5 21.5t21.5 65.5v365q0 60 -28 85t-81 25q-41 0 -80.5 -22t-62.5 -53v-353h-75v535h-68v48h68v84h75v-84h157v-48z" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="436" 
d="M414 700l-169 -144h-57l148 144h78zM413 483v-66h-263v-417h-75v483h338z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="495" 
d="M288 -12q-107 0 -174 72.5t-67 181.5t67 181t174 72q112 0 177 -84l-48 -44q-48 66 -125 66q-68 0 -112 -42.5t-53 -113.5h243v-65h-244q8 -74 52 -118t114 -44q76 0 125 66l48 -44q-65 -84 -177 -84z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="465" 
d="M228 -12q-123 0 -195 76l39 54q25 -29 68.5 -49t90.5 -20q54 0 84 23t30 60q0 32 -31 50.5t-74.5 28t-87.5 22t-75 43t-31 80.5q0 59 48.5 99t131.5 40q109 0 179 -70l-35 -52q-51 61 -144 61q-49 0 -78 -21.5t-29 -54.5q0 -29 31 -45.5t74.5 -26t87.5 -22.5t75 -45.5
t31 -84.5q0 -64 -50 -105t-140 -41z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="225" 
d="M112 552q-20 0 -35 14.5t-15 35.5t15 36t35 15q21 0 36 -15t15 -36t-15 -35.5t-36 -14.5zM150 0h-75v483h75v-483z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="225" 
d="M263 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM56 604q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 13.5t-13.5 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM150 0h-75v483h75v-483z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="225" 
d="M112 552q-20 0 -35 14.5t-15 35.5t15 36t35 15q21 0 36 -15t15 -36t-15 -35.5t-36 -14.5zM7 -196q-59 0 -106 36l27 57q31 -31 69 -31q36 0 57 21.5t21 65.5v530h75v-530q0 -71 -37 -110t-106 -39z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="849" 
d="M18 -12v62q34 0 56 45t36 150l31 238h336v-187h180q72 0 112.5 -42.5t40.5 -105.5t-40.5 -105.5t-112.5 -42.5h-255v417h-195l-22 -181q-16 -133 -56 -190.5t-111 -57.5zM650 230h-173v-164h173q40 0 61.5 22.5t21.5 59.5q0 36 -22 59t-61 23z" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="849" 
d="M150 0h-75v483h75v-201h252v201h75v-201h188q68 0 106.5 -40.5t38.5 -101.5q0 -59 -38.5 -99.5t-106.5 -40.5h-263v216h-252v-216zM657 216h-180v-150h180q36 0 56 20.5t20 53.5q0 34 -20 55t-56 21z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="554" 
d="M308 535h-156v-122q28 33 75 57.5t98 24.5q154 0 154 -154v-341h-75v318q0 60 -28 85t-81 25q-41 0 -80.5 -22t-62.5 -53v-353h-75v535h-68v48h68v84h75v-84h156v-48z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" horiz-adv-x="514" 
d="M428 700l-169 -144h-57l148 144h78zM500 0h-96l-174 217l-80 -79v-138h-75v483h75v-257l254 257h95l-214 -219z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="490" 
d="M48 -189l12 68q18 -8 41 -8q26 0 43 11t29 40l32 73l-202 488h81l161 -396l160 396h82l-242 -581q-40 -96 -142 -98q-30 0 -55 7zM421 626q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="552" 
d="M239 -126v126h-164v483h75v-417h252v417h75v-483h-163v-126h-75z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="550" 
d="M161 593v-593h-83v667h354v123h83v-197h-354z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="436" 
d="M150 417v-417h-75v483h263v126h75v-192h-263z" />
    <glyph glyph-name="uni1E02" unicode="&#x1e02;" horiz-adv-x="629" 
d="M382 0h-304v667h297q86 0 136.5 -47t50.5 -123q0 -60 -34 -101.5t-83 -51.5q53 -8 91.5 -56t38.5 -108q0 -81 -51.5 -130.5t-141.5 -49.5zM361 378q55 0 85 30t30 77t-30.5 77.5t-84.5 30.5h-200v-215h200zM365 74q58 0 91 31t33 84q0 49 -32.5 82t-91.5 33h-204v-230
h204zM314 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E03" unicode="&#x1e03;" horiz-adv-x="575" 
d="M150 131q20 -32 60.5 -54t83.5 -22q72 0 114 52t42 134t-42 134.5t-114 52.5q-43 0 -83.5 -22.5t-60.5 -55.5v-219zM150 0h-75v667h75v-257q63 85 163 85q95 0 155 -69.5t60 -184.5q0 -116 -59.5 -184.5t-155.5 -68.5q-101 0 -163 84v-72zM294 722q-19 0 -33 14t-14 33
t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E0A" unicode="&#x1e0a;" horiz-adv-x="700" 
d="M306 0h-228v667h228q153 0 247.5 -95t94.5 -239q0 -145 -94.5 -239t-247.5 -94zM306 74q118 0 187 74t69 185q0 112 -68 186t-188 74h-145v-519h145zM350 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E0B" unicode="&#x1e0b;" horiz-adv-x="575" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q100 0 163 -85v257h75v-667zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52zM282 722q-19 0 -33 14t-14 33t14 33
t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E1E" unicode="&#x1e1e;" horiz-adv-x="550" 
d="M297 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5zM161 0h-83v667h437v-74h-354v-215h347v-74h-347v-304z" />
    <glyph glyph-name="uni1E1F" unicode="&#x1e1f;" horiz-adv-x="283" 
d="M237 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5zM171 0h-75v417h-80v66h80v37q0 74 37.5 115.5t102.5 41.5q62 0 102 -37l-31 -49q-27 24 -60 24q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417z" />
    <glyph glyph-name="uni1E22" unicode="&#x1e22;" horiz-adv-x="712" 
d="M634 0h-83v306h-390v-306h-83v667h83v-287h390v287h83v-667zM355 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E23" unicode="&#x1e23;" horiz-adv-x="552" 
d="M477 0h-75v318q0 60 -28 85t-81 25q-41 0 -80.5 -22t-62.5 -53v-353h-75v667h75v-254q28 33 75 57.5t98 24.5q154 0 154 -154v-341zM276 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E56" unicode="&#x1e56;" horiz-adv-x="587" 
d="M161 0h-83v667h268q97 0 153 -58t56 -143t-56.5 -143t-152.5 -58h-185v-265zM336 339q59 0 96 35.5t37 91.5t-37 91.5t-96 35.5h-175v-254h175zM293 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E57" unicode="&#x1e57;" 
d="M313 -12q-102 0 -163 85v-257h-75v667h75v-72q27 38 70 61t93 23q96 0 155.5 -68.5t59.5 -184.5t-59.5 -185t-155.5 -69zM294 55q71 0 113 52.5t42 134.5t-42 134t-113 52q-43 0 -83.5 -22.5t-60.5 -54.5v-218q20 -33 60.5 -55.5t83.5 -22.5zM293 555q-19 0 -33 14
t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E60" unicode="&#x1e60;" horiz-adv-x="586" 
d="M294 -12q-162 0 -256 106l49 63q85 -95 210 -95q81 0 119 34.5t38 80.5q0 35 -22 59t-56.5 37.5t-77 24.5t-84.5 24.5t-76.5 32.5t-56.5 54t-22 83q0 82 65 133.5t164 51.5q149 0 237 -93l-50 -61q-73 80 -193 80q-60 0 -98.5 -29.5t-38.5 -76.5q0 -35 29.5 -58t73 -34
t95 -27.5t95 -35t73 -59.5t29.5 -99q0 -82 -60.5 -139t-185.5 -57zM293 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E61" unicode="&#x1e61;" horiz-adv-x="465" 
d="M228 -12q-123 0 -195 76l39 54q25 -29 68.5 -49t90.5 -20q54 0 84 23t30 60q0 32 -31 50.5t-74.5 28t-87.5 22t-75 43t-31 80.5q0 59 48.5 99t131.5 40q109 0 179 -70l-35 -52q-51 61 -144 61q-49 0 -78 -21.5t-29 -54.5q0 -29 31 -45.5t74.5 -26t87.5 -22.5t75 -45.5
t31 -84.5q0 -64 -50 -105t-140 -41zM232 555q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E6A" unicode="&#x1e6a;" horiz-adv-x="570" 
d="M326 0h-83v593h-211v74h506v-74h-212v-593zM284 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E6B" unicode="&#x1e6b;" horiz-adv-x="294" 
d="M197 -12q-53 0 -80 29t-27 84v316h-80v66h80v132h75v-132h98v-66h-98v-300q0 -28 12.5 -45t36.5 -17q33 0 51 20l22 -56q-33 -31 -90 -31zM134 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="883" 
d="M678 0h-90l-147 538l-147 -538h-90l-190 667h92l148 -556l153 556h69l153 -556l147 556h92zM498 723h-57l-169 144h78z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="734" 
d="M568 0h-75l-126 388l-126 -388h-75l-154 483h78l118 -385l127 385h64l127 -385l118 385h78zM424 556h-57l-169 144h78z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="883" 
d="M610 867l-169 -144h-57l148 144h78zM678 0h-90l-147 538l-147 -538h-90l-190 667h92l148 -556l153 556h69l153 -556l147 556h92z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="734" 
d="M536 700l-169 -144h-57l148 144h78zM568 0h-75l-126 388l-126 -388h-75l-154 483h78l118 -385l127 385h64l127 -385l118 385h78z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="883" 
d="M591 771q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM384 771q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 14t-13.5 33t14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM678 0h-90l-147 538l-147 -538h-90l-190 667h92l148 -556l153 556h69l153 -556
l147 556h92z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="734" 
d="M517 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM310 604q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 13.5t-13.5 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM568 0h-75l-126 388l-126 -388h-75l-154 483h78l118 -385l127 385h64
l127 -385l118 385h78z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM329 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM264 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM289 756l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM224 586l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="658" 
d="M463 682h-51l-83 79l-80 -79h-51l95 108h72zM649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM657 844l-169 -108h-57l148 108h78z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM398 556h-51l-83 106l-80 -106h-51l95 144h72zM587 770l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="658" 
d="M463 682h-51l-83 79l-80 -79h-51l95 108h72zM649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM229 736h-57l-169 108h78z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM398 556h-51l-83 106l-80 -106h-51l95 144h72zM169 626h-57l-169 144h78z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="658" 
d="M463 682h-51l-83 79l-80 -79h-51l95 108h72zM649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM428 788l-33 7q18 41 73 41q31 0 51.5 -13t20.5 -38q0 -21 -19 -41h-40q23 18 23 39q0 29 -36 29q-29 0 -40 -24z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM398 556h-51l-83 106l-80 -106h-51l95 144h72zM315 726l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="658" 
d="M463 678h-51l-83 53l-80 -53h-51l95 72h72zM649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM388 757q-40 0 -71 23t-52 23q-48 0 -48 -43h-45q0 30 25.5 48.5t71.5 18.5q39 0 70.5 -23t52.5 -23q48 0 48 43h45q0 -30 -25.5 -48.5
t-71.5 -18.5z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM398 523h-51l-83 106l-80 -106h-51l95 144h72zM323 682q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5
t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM462 723h-51l-83 106l-80 -106h-51l95 144h72zM329 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM396 556h-51l-83 106l-80 -106h-51l95 144h72zM264 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM497 839l-169 -98h-57l148 98h78zM504 760q-65 -75 -175 -75q-109 0 -176 75l37 27q53 -61 139 -61q88 0 138 61z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM435 766l-169 -144h-57l148 144h78zM440 609q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM388 741h-57l-169 98h78zM504 757q-65 -75 -175 -75q-109 0 -176 75l37 27q53 -61 139 -61q88 0 138 61z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM319 622h-57l-169 144h78zM440 613q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM289 784l-33 6q18 37 73 37q31 0 51.5 -11.5t20.5 -34.5q0 -19 -19 -37h-40q23 16 23 35q0 27 -36 27q-29 0 -40 -22zM504 759q-65 -75 -175 -75q-109 0 -176 75l37 27
q53 -61 139 -61q88 0 138 61z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM224 681l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32zM440 613q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM505 733q-65 -54 -175 -54q-109 0 -176 54l37 20q52 -44 139 -44q89 0 138 44zM388 757q-40 0 -71 23t-52 23q-48 0 -48 -43h-45q0 30 25.5 48.5t71.5 18.5q39 0 70.5 -23t52.5 -23
q48 0 48 43h45q0 -30 -25.5 -48.5t-71.5 -18.5z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM440 613q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72zM323 657q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5
t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="658" 
d="M649 0h-95l-59 148h-332l-59 -148h-95l268 667h103zM470 222l-141 359l-142 -359h283zM329 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5zM504 793q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="527" 
d="M452 0h-75v55q-61 -67 -160 -67q-67 0 -118 43t-51 117q0 76 50.5 117.5t118.5 41.5q103 0 160 -66v87q0 48 -34 75t-88 27q-85 0 -148 -67l-35 52q77 80 193 80q83 0 135 -40t52 -123v-332zM244 42q90 0 133 60v91q-43 60 -133 60q-52 0 -85.5 -29.5t-33.5 -76.5
q0 -46 33.5 -75.5t85.5 -29.5zM264 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5zM437 626q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="569" 
d="M515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74zM300 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="563" 
d="M293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317zM286 -184q-19 0 -33 14
t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="569" 
d="M515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74zM252 756l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="563" 
d="M293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317zM244 586l-33 9q18 55 73 55
q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="569" 
d="M515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74zM357 721q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45
q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="563" 
d="M293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317zM344 554q-25 0 -43.5 14.5
t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="569" 
d="M434 682h-51l-83 79l-80 -79h-51l95 108h72zM628 844l-169 -108h-57l148 108h78zM515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="563" 
d="M418 556h-51l-83 106l-80 -106h-51l95 144h72zM604 770l-169 -144h-57l148 144h78zM293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272
q-1 62 -42 111.5t-117 49.5q-72 0 -113.5 -49t-44.5 -112h317z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="569" 
d="M433 682h-51l-83 79l-80 -79h-51l95 108h72zM515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74zM199 736h-57l-169 108h78z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="563" 
d="M417 556h-51l-83 106l-80 -106h-51l95 144h72zM293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5
q-72 0 -113.5 -49t-44.5 -112h317zM188 626h-57l-169 144h78z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="569" 
d="M434 682h-51l-83 79l-80 -79h-51l95 108h72zM515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74zM399 788l-33 7q18 41 73 41q31 0 51.5 -13t20.5 -38q0 -21 -19 -41h-40q23 18 23 39q0 29 -36 29q-29 0 -40 -24z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="563" 
d="M416 556h-51l-83 106l-80 -106h-51l95 144h72zM293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5
q-72 0 -113.5 -49t-44.5 -112h317zM333 726l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="569" 
d="M434 678h-51l-83 53l-80 -53h-51l95 72h72zM515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74zM359 757q-40 0 -71 23t-52 23q-48 0 -48 -43h-45q0 30 25.5 48.5t71.5 18.5q39 0 70.5 -23t52.5 -23q48 0 48 43h45q0 -30 -25.5 -48.5t-71.5 -18.5z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="563" 
d="M416 523h-51l-83 106l-80 -106h-51l95 144h72zM293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5
q-72 0 -113.5 -49t-44.5 -112h317zM340 682q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="569" 
d="M428 723h-51l-83 106l-80 -106h-51l95 144h72zM515 0h-437v667h437v-74h-354v-215h347v-74h-347v-230h354v-74zM300 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="563" 
d="M419 556h-51l-83 106l-80 -106h-51l95 144h72zM293 -12q-108 0 -177 70.5t-69 183.5q0 106 68 179.5t170 73.5q107 0 169.5 -73.5t62.5 -185.5v-19h-391q5 -71 52 -119t122 -48q90 0 149 61l36 -49q-74 -74 -192 -74zM443 272q-1 62 -42 111.5t-117 49.5
q-72 0 -113.5 -49t-44.5 -112h317zM286 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667zM80 756l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="225" 
d="M150 0h-75v483h75v-483zM73 586l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="239" 
d="M161 0h-83v667h83v-667zM120 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="225" 
d="M112 552q-20 0 -35 14.5t-15 35.5t15 36t35 15q21 0 36 -15t15 -36t-15 -35.5t-36 -14.5zM150 0h-75v483h75v-483zM113 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5zM384 -184q-19 0 -33 14t-14 33t14 33t33 14
t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" 
d="M286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5zM286 -184q-19 0 -33 14t-14 33t14 33t33 14
t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5zM341 756l-33 9q18 55 73 55q31 0 51.5 -17.5
t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" 
d="M286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5zM246 586l-33 9q18 55 73 55q31 0 51.5 -17.5
t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="765" 
d="M513 682h-51l-83 79l-80 -79h-51l95 108h72zM707 844l-169 -108h-57l148 108h78zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76
t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" 
d="M419 556h-51l-83 106l-80 -106h-51l95 144h72zM606 770l-169 -144h-57l148 144h78zM286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5
t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="765" 
d="M512 682h-51l-83 79l-80 -79h-51l95 108h72zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z
M278 736h-57l-169 108h78z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" 
d="M419 556h-51l-83 106l-80 -106h-51l95 144h72zM286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z
M190 626h-57l-169 144h78z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="765" 
d="M512 682h-51l-83 79l-80 -79h-51l95 108h72zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z
M477 788l-33 7q18 41 73 41q31 0 51.5 -13t20.5 -38q0 -21 -19 -41h-40q23 18 23 39q0 29 -36 29q-29 0 -40 -24z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" 
d="M419 556h-51l-83 106l-80 -106h-51l95 144h72zM286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z
M335 726l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="765" 
d="M514 678h-51l-83 53l-80 -53h-51l95 72h72zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z
M439 757q-40 0 -71 23t-52 23q-48 0 -48 -43h-45q0 30 25.5 48.5t71.5 18.5q39 0 70.5 -23t52.5 -23q48 0 48 43h45q0 -30 -25.5 -48.5t-71.5 -18.5z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" 
d="M417 523h-51l-83 106l-80 -106h-51l95 144h72zM286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z
M342 682q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="765" 
d="M517 723h-51l-83 106l-80 -106h-51l95 144h72zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q146 0 238.5 -98t92.5 -247t-92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5
zM385 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" 
d="M418 556h-51l-83 106l-80 -106h-51l95 144h72zM286 -12q-108 0 -173.5 73t-65.5 181t65.5 180.5t173.5 72.5t173.5 -72.5t65.5 -180.5t-65.5 -181t-173.5 -73zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z
M286 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="765" 
d="M552 867l-169 -144h-57l148 144h78zM382 -12q-147 0 -239 98t-92 247t92 247t239 98q154 0 249 -110q41 34 46 76q-2 -2 -11 -2q-17 0 -28 12t-11 29q0 18 12 31t30 13q21 0 35.5 -17t14.5 -46q0 -37 -18.5 -71t-46.5 -55q59 -91 59 -205q0 -149 -92.5 -247t-238.5 -98z
M382 62q110 0 177.5 76.5t67.5 194.5q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" 
d="M455 700l-169 -144h-57l148 144h78zM527 530q0 -71 -58 -120q56 -68 56 -168q0 -108 -65.5 -181t-173.5 -73t-173.5 73t-65.5 181t65.5 180.5t173.5 72.5q92 0 157 -57q37 32 42 72q-1 0 -5 -0.5t-6 -0.5q-17 0 -28.5 11.5t-11.5 29.5q0 17 12.5 30t30.5 13
q21 0 35.5 -17t14.5 -46zM286 55q74 0 117 54.5t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q154 0 249 -110q41 34 46 76q-2 -2 -11 -2q-17 0 -28 12t-11 29q0 18 12 31t30 13q21 0 35.5 -17t14.5 -46q0 -37 -18.5 -71t-46.5 -55q59 -91 59 -205q0 -149 -92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5
q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5zM440 723h-57l-169 144h78z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" 
d="M527 530q0 -71 -58 -120q56 -68 56 -168q0 -108 -65.5 -181t-173.5 -73t-173.5 73t-65.5 181t65.5 180.5t173.5 72.5q92 0 157 -57q37 32 42 72q-1 0 -5 -0.5t-6 -0.5q-17 0 -28.5 11.5t-11.5 29.5q0 17 12.5 30t30.5 13q21 0 35.5 -17t14.5 -46zM286 55q74 0 117 54.5
t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5zM343 556h-57l-169 144h78z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q154 0 249 -110q41 34 46 76q-2 -2 -11 -2q-17 0 -28 12t-11 29q0 18 12 31t30 13q21 0 35.5 -17t14.5 -46q0 -37 -18.5 -71t-46.5 -55q59 -91 59 -205q0 -149 -92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5
q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5zM342 756l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" 
d="M527 530q0 -71 -58 -120q56 -68 56 -168q0 -108 -65.5 -181t-173.5 -73t-173.5 73t-65.5 181t65.5 180.5t173.5 72.5q92 0 157 -57q37 32 42 72q-1 0 -5 -0.5t-6 -0.5q-17 0 -28.5 11.5t-11.5 29.5q0 17 12.5 30t30.5 13q21 0 35.5 -17t14.5 -46zM286 55q74 0 117 54.5
t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5zM246 586l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q154 0 249 -110q41 34 46 76q-2 -2 -11 -2q-17 0 -28 12t-11 29q0 18 12 31t30 13q21 0 35.5 -17t14.5 -46q0 -37 -18.5 -71t-46.5 -55q59 -91 59 -205q0 -149 -92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5
q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5zM442 721q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5
t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" 
d="M527 530q0 -71 -58 -120q56 -68 56 -168q0 -108 -65.5 -181t-173.5 -73t-173.5 73t-65.5 181t65.5 180.5t173.5 72.5q92 0 157 -57q37 32 42 72q-1 0 -5 -0.5t-6 -0.5q-17 0 -28.5 11.5t-11.5 29.5q0 17 12.5 30t30.5 13q21 0 35.5 -17t14.5 -46zM286 55q74 0 117 54.5
t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5zM345 554q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5
q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="765" 
d="M382 -12q-147 0 -239 98t-92 247t92 247t239 98q154 0 249 -110q41 34 46 76q-2 -2 -11 -2q-17 0 -28 12t-11 29q0 18 12 31t30 13q21 0 35.5 -17t14.5 -46q0 -37 -18.5 -71t-46.5 -55q59 -91 59 -205q0 -149 -92.5 -247t-238.5 -98zM382 62q110 0 177.5 76.5t67.5 194.5
q0 119 -67.5 195t-177.5 76q-111 0 -178 -76t-67 -195q0 -118 67 -194.5t178 -76.5zM384 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" 
d="M527 530q0 -71 -58 -120q56 -68 56 -168q0 -108 -65.5 -181t-173.5 -73t-173.5 73t-65.5 181t65.5 180.5t173.5 72.5q92 0 157 -57q37 32 42 72q-1 0 -5 -0.5t-6 -0.5q-17 0 -28.5 11.5t-11.5 29.5q0 17 12.5 30t30.5 13q21 0 35.5 -17t14.5 -46zM286 55q74 0 117 54.5
t43 132.5t-43 132t-117 54t-117.5 -54.5t-43.5 -131.5q0 -78 43.5 -132.5t117.5 -54.5zM286 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="700" 
d="M350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-407q0 -127 -69.5 -199.5t-202.5 -72.5zM354 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="551" 
d="M476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483zM278 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="700" 
d="M350 -12q-133 0 -202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-407q0 -127 -69.5 -199.5t-202.5 -72.5zM311 756l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" horiz-adv-x="551" 
d="M476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-483zM236 586l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="700" 
d="M519 867l-169 -144h-57l148 144h78zM738 728q0 -48 -31 -90t-85 -64v-314q0 -127 -69.5 -199.5t-202.5 -72.5t-202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-54q29 15 50 41.5t23 53.5h-5q-4 -1 -6 -1q-17 0 -28 11.5t-11 29.5
t12.5 30.5t30.5 12.5q21 0 35.5 -17t14.5 -46z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" horiz-adv-x="551" 
d="M444 700l-169 -144h-57l148 144h78zM476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-48q48 36 51 81h-5q-4 -1 -6 -1q-17 0 -28.5 11.5t-11.5 29.5q0 17 12.5 30t30.5 13q21 0 36 -17
t15 -46q0 -43 -25.5 -82t-68.5 -62v-392z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="700" 
d="M407 723h-57l-169 144h78zM738 728q0 -48 -31 -90t-85 -64v-314q0 -127 -69.5 -199.5t-202.5 -72.5t-202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-54q29 15 50 41.5t23 53.5h-5q-4 -1 -6 -1q-17 0 -28 11.5t-11 29.5t12.5 30.5
t30.5 12.5q21 0 35.5 -17t14.5 -46z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" horiz-adv-x="551" 
d="M332 556h-57l-169 144h78zM476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-48q48 36 51 81h-5q-4 -1 -6 -1q-17 0 -28.5 11.5t-11.5 29.5q0 17 12.5 30t30.5 13q21 0 36 -17t15 -46
q0 -43 -25.5 -82t-68.5 -62v-392z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="700" 
d="M311 756l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32zM738 728q0 -48 -31 -90t-85 -64v-314q0 -127 -69.5 -199.5t-202.5 -72.5t-202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146
v406h83v-54q29 15 50 41.5t23 53.5h-5q-4 -1 -6 -1q-17 0 -28 11.5t-11 29.5t12.5 30.5t30.5 12.5q21 0 35.5 -17t14.5 -46z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" horiz-adv-x="551" 
d="M236 586l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32zM476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-48q48 36 51 81h-5
q-4 -1 -6 -1q-17 0 -28.5 11.5t-11.5 29.5q0 17 12.5 30t30.5 13q21 0 36 -17t15 -46q0 -43 -25.5 -82t-68.5 -62v-392z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="700" 
d="M738 728q0 -48 -31 -90t-85 -64v-314q0 -127 -69.5 -199.5t-202.5 -72.5t-202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-54q29 15 50 41.5t23 53.5h-5q-4 -1 -6 -1q-17 0 -28 11.5t-11 29.5t12.5 30.5t30.5 12.5q21 0 35.5 -17
t14.5 -46zM410 721q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" horiz-adv-x="551" 
d="M476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-48q48 36 51 81h-5q-4 -1 -6 -1q-17 0 -28.5 11.5t-11.5 29.5q0 17 12.5 30t30.5 13q21 0 36 -17t15 -46q0 -43 -25.5 -82t-68.5 -62v-392
zM335 554q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="700" 
d="M354 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5zM738 728q0 -48 -31 -90t-85 -64v-314q0 -127 -69.5 -199.5t-202.5 -72.5t-202.5 72.5t-69.5 198.5v408h83v-406q0 -93 49 -146t140 -53t140 53t49 146v406h83v-54
q29 15 50 41.5t23 53.5h-5q-4 -1 -6 -1q-17 0 -28 11.5t-11 29.5t12.5 30.5t30.5 12.5q21 0 35.5 -17t14.5 -46z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" horiz-adv-x="551" 
d="M278 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5zM476 0h-75v68q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v341h75v-318q0 -60 27.5 -85t80.5 -25q42 0 81.5 21t61.5 52v355h75v-48q48 36 51 81h-5q-4 -1 -6 -1
q-17 0 -28.5 11.5t-11.5 29.5q0 17 12.5 30t30.5 13q21 0 36 -17t15 -46q0 -43 -25.5 -82t-68.5 -62v-392z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="626" 
d="M355 0h-83v282l-263 385h97l207 -310l207 310h97l-262 -385v-282zM370 723h-57l-169 144h78z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="490" 
d="M48 -189l12 68q18 -8 41 -8q26 0 43 11t29 40l32 73l-202 488h81l161 -396l160 396h82l-242 -581q-40 -96 -142 -98q-30 0 -55 7zM302 556h-57l-169 144h78z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="51" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="593" 
d="M563 209h-533v66h533v-66z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="833" 
d="M803 209h-773v66h773v-66z" />
    <glyph glyph-name="afii00208" unicode="&#x2015;" horiz-adv-x="593" 
d="M563 209h-533v66h533v-66z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="230" 
d="M47 527q0 44 22.5 84t58.5 66l37 -30q-24 -16 -42.5 -42.5t-21.5 -50.5q1 0 6 0.5t7 0.5q21 0 35 -14.5t14 -36.5t-15.5 -37.5t-37.5 -15.5q-26 0 -44.5 20.5t-18.5 55.5z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="230" 
d="M176 601q0 -44 -23 -84.5t-59 -66.5l-37 31q24 16 43 42.5t22 50.5q-4 -2 -14 -2q-21 0 -34.5 14.5t-13.5 36.5t15.5 38t37.5 16q26 0 44.5 -20.5t18.5 -55.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="230" 
d="M176 24q0 -44 -23 -84.5t-59 -66.5l-37 31q25 16 43.5 42.5t21.5 50.5q-4 -2 -14 -2q-21 0 -34.5 14.5t-13.5 36.5t15.5 38t37.5 16q26 0 44.5 -20.5t18.5 -55.5z" />
    <glyph glyph-name="quotereversed" unicode="&#x201b;" horiz-adv-x="230" 
d="M54 601q0 35 18 55.5t44 20.5q22 0 38 -16t16 -38t-14 -36.5t-35 -14.5q-9 0 -13 2q3 -24 21.5 -50.5t42.5 -42.5l-37 -31q-36 26 -58.5 66.5t-22.5 84.5z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="390" 
d="M54 527q0 44 22.5 84t58.5 66l37 -30q-24 -16 -43 -42.5t-21 -50.5h5q5 1 8 1q21 0 34.5 -14.5t13.5 -36.5t-15.5 -37.5t-37.5 -15.5q-26 0 -44 20.5t-18 55.5zM214 527q0 44 22.5 84t58.5 66l37 -30q-24 -16 -42.5 -42.5t-21.5 -50.5h5q5 1 8 1q21 0 35 -14.5t14 -36.5
t-16 -37.5t-38 -15.5q-26 0 -44 20.5t-18 55.5z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="390" 
d="M336 601q0 -44 -23 -84.5t-58 -66.5l-37 31q24 16 42.5 42.5t21.5 50.5q-4 -2 -13 -2q-21 0 -35 14.5t-14 36.5t15.5 38t37.5 16q26 0 44.5 -20.5t18.5 -55.5zM176 601q0 -44 -23 -84.5t-59 -66.5l-37 31q24 16 43 42.5t22 50.5q-4 -2 -14 -2q-21 0 -34.5 14.5
t-13.5 36.5t15.5 38t37.5 16q26 0 44.5 -20.5t18.5 -55.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="390" 
d="M176 24q0 -44 -23 -84.5t-59 -66.5l-37 31q25 16 43.5 42.5t21.5 50.5q-4 -2 -14 -2q-21 0 -34.5 14.5t-13.5 36.5t15.5 38t37.5 16q26 0 44.5 -20.5t18.5 -55.5zM336 24q0 -44 -23 -84.5t-58 -66.5l-37 31q24 16 42.5 42.5t21.5 50.5q-4 -2 -13 -2q-21 0 -35 14.5
t-14 36.5t15.5 38t37.5 16q26 0 44.5 -20.5t18.5 -55.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="263" 
d="M240 531l-89 4l4 -208h-48l5 208l-89 -4v43l89 -3l-5 106h48l-4 -106l89 3v-43z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="263" 
d="M240 574v-43l-89 4v-213l89 4v-43l-89 3l4 -106h-48l4 106l-88 -3v43l88 -4v213l-88 -4v43l88 -3l-4 106h48l-4 -106z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="358" 
d="M288 242q0 -44 -32 -75.5t-76 -31.5t-76 31.5t-32 75.5t32 76t76 32t76 -32t32 -76z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="690" 
d="M169 45q0 -22 -16.5 -38.5t-38.5 -16.5t-38 16.5t-16 38.5q0 23 16 39t38 16t38.5 -16t16.5 -39zM400 45q0 -22 -17 -38.5t-39 -16.5t-38 16.5t-16 38.5q0 23 16 39t38 16q23 0 39.5 -16.5t16.5 -38.5zM630 45q0 -22 -16.5 -38.5t-38.5 -16.5t-38.5 16.5t-16.5 38.5
q0 23 16 39t39 16t39 -16t16 -39z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1083" 
d="M353 511q0 -70 -45.5 -116.5t-116.5 -46.5t-115.5 46.5t-44.5 116.5q0 71 45 118.5t115 47.5q71 0 116.5 -47.5t45.5 -118.5zM609 667l-427 -667h-54l426 667h55zM700 151q0 -70 -45 -116.5t-116 -46.5t-115.5 46.5t-44.5 116.5q0 71 45 118.5t115 47.5q71 0 116 -47.5
t45 -118.5zM293 511q0 51 -28.5 85t-73.5 34q-44 0 -72 -34t-28 -85q0 -50 28 -83t72 -33q45 0 73.5 33t28.5 83zM641 151q0 51 -28.5 85t-73.5 34t-73 -34t-28 -85q0 -50 28 -83t73 -33t73.5 33t28.5 83zM1052 151q0 -70 -45 -116.5t-116 -46.5t-115.5 46.5t-44.5 116.5
q0 71 45 118.5t115 47.5q71 0 116 -47.5t45 -118.5zM993 151q0 51 -28.5 85t-73.5 34t-72.5 -33.5t-27.5 -85.5q0 -50 28 -83t72 -33q45 0 73.5 33t28.5 83z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="295" 
d="M265 63h-75l-160 180l160 177h75l-160 -177z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="295" 
d="M265 243l-160 -180h-75l160 180l-160 177h75z" />
    <glyph glyph-name="uni203E" unicode="&#x203e;" horiz-adv-x="363" 
d="M363 572h-363v48h363v-48z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="138" 
d="M310 667l-427 -667h-55l426 667h56z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="388" 
d="M355 621q0 -83 -41 -145t-120 -62q-80 0 -120.5 61.5t-40.5 145.5t40.5 145t120.5 61q79 0 120 -61.5t41 -144.5zM293 621q0 68 -24 112.5t-75 44.5q-52 0 -76 -44.5t-24 -112.5t24 -113t76 -45q51 0 75 45t24 113z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="388" 
d="M352 521h-58v-100h-59v100h-196v44l172 256h83v-251h58v-49zM235 570v198l-136 -198h136z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="388" 
d="M344 547q0 -60 -43 -96.5t-109 -36.5q-99 0 -148 65l35 39q42 -55 113 -55q41 0 66.5 23.5t25.5 58.5q0 37 -24.5 60t-65.5 23q-56 0 -95 -39l-43 15v217h261v-49h-201v-134q38 36 96 36q55 0 93.5 -33.5t38.5 -93.5z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="388" 
d="M350 546q0 -56 -40.5 -94t-106.5 -38q-84 0 -124.5 57.5t-40.5 148.5q0 90 45 148.5t129 58.5q69 0 117 -46l-29 -42q-36 39 -88 39q-56 0 -85 -43t-29 -103v-17q18 24 49 42.5t66 18.5q59 0 98 -34.5t39 -95.5zM290 544q0 41 -26 62.5t-66 21.5q-28 0 -54.5 -15
t-43.5 -41q3 -45 28 -77t72 -32q41 0 65.5 25t24.5 56z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="362" 
d="M323 779l-166 -358h-67l166 351h-223v49h290v-42z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="388" 
d="M347 523q0 -52 -44.5 -80.5t-108.5 -28.5q-65 0 -109 28.5t-44 80.5q0 38 29 66t72 38q-41 10 -67.5 34t-26.5 61q0 52 43.5 78.5t102.5 26.5t102.5 -26.5t43.5 -78.5q0 -37 -26.5 -61t-67.5 -34q43 -10 72 -38t29 -66zM280 715q0 30 -24.5 47t-61.5 17q-38 0 -62.5 -17
t-24.5 -47q0 -51 87 -68q86 16 86 68zM287 528q0 35 -32 54t-61 23q-29 -4 -61.5 -23t-32.5 -54q0 -31 27 -49t67 -18q39 0 66 18.5t27 48.5z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="388" 
d="M38 695q0 55 41 93.5t107 38.5q83 0 123.5 -58t40.5 -149q0 -89 -45 -147.5t-129 -58.5q-72 0 -116 46l28 42q36 -39 88 -39q55 0 84 42.5t30 104.5v16q-17 -24 -48 -42.5t-66 -18.5q-59 0 -98.5 34t-39.5 96zM98 697q0 -41 26 -62.5t66 -21.5q63 0 98 56q-4 44 -28 76.5
t-71 32.5q-42 0 -66.5 -25t-24.5 -56z" />
    <glyph glyph-name="parenleftsuperior" unicode="&#x207d;" horiz-adv-x="161" 
d="M151 372l-35 -16q-90 111 -90 265q0 153 90 265l35 -16q-63 -124 -63 -249q0 -126 63 -249z" />
    <glyph glyph-name="parenrightsuperior" unicode="&#x207e;" horiz-adv-x="161" 
d="M148 621q0 -152 -91 -265l-34 16q63 123 63 249t-63 249l34 16q43 -53 67 -123t24 -142z" />
    <glyph glyph-name="nsuperior" unicode="&#x207f;" horiz-adv-x="398" 
d="M338 326h-58v200q0 74 -70 74q-56 0 -92 -46v-228h-58v314h58v-45q45 53 115 53q105 0 105 -107v-215z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="388" 
d="M355 54q0 -83 -41 -145t-120 -62q-80 0 -120.5 61.5t-40.5 145.5t40.5 145t120.5 61q79 0 120 -61.5t41 -144.5zM293 54q0 68 -24 112.5t-75 44.5q-52 0 -76 -44.5t-24 -112.5t24 -113t76 -45q51 0 75 45t24 113z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="235" 
d="M175 -146h-60v322l-66 -69l-35 37l108 110h53v-400z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="388" 
d="M341 -146h-291v45q124 88 176.5 140.5t52.5 98.5q0 36 -25 54.5t-60 18.5q-73 0 -112 -52l-33 38q50 63 147 63q60 0 101.5 -30.5t41.5 -87.5q0 -56 -50 -111.5t-151 -127.5h203v-49z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="388" 
d="M343 -37q0 -52 -41 -84t-110 -32q-103 0 -150 65l32 38q44 -54 116 -54q43 0 68 19t25 51q0 69 -104 69q-34 0 -40 -1v50q7 -1 39 -1q98 0 98 64q0 30 -25.5 47t-63.5 17q-64 0 -109 -49l-31 36q55 62 145 62q64 0 104 -29t40 -78q0 -40 -27 -63.5t-62 -29.5
q34 -3 65 -28t31 -69z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="388" 
d="M352 -46h-58v-100h-59v100h-196v44l172 256h83v-251h58v-49zM235 3v198l-136 -198h136z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="388" 
d="M344 -20q0 -60 -43 -96.5t-109 -36.5q-99 0 -148 65l35 39q42 -55 113 -55q41 0 66.5 23.5t25.5 58.5q0 37 -24.5 60t-65.5 23q-56 0 -95 -39l-43 15v217h261v-49h-201v-134q38 36 96 36q55 0 93.5 -33.5t38.5 -93.5z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="388" 
d="M350 -21q0 -56 -40.5 -94t-106.5 -38q-84 0 -124.5 57.5t-40.5 148.5q0 90 45 148.5t129 58.5q69 0 117 -46l-29 -42q-36 39 -88 39q-56 0 -85 -43t-29 -103v-17q18 24 49 42.5t66 18.5q59 0 98 -34.5t39 -95.5zM290 -23q0 41 -26 62.5t-66 21.5q-28 0 -54.5 -15
t-43.5 -41q3 -45 28 -77t72 -32q41 0 65.5 25t24.5 56z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="362" 
d="M323 212l-166 -358h-67l166 351h-223v49h290v-42z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="388" 
d="M347 -44q0 -52 -44.5 -80.5t-108.5 -28.5q-65 0 -109 28.5t-44 80.5q0 38 29 66t72 38q-41 10 -67.5 34t-26.5 61q0 52 43.5 78.5t102.5 26.5t102.5 -26.5t43.5 -78.5q0 -37 -26.5 -61t-67.5 -34q43 -10 72 -38t29 -66zM280 148q0 30 -24.5 47t-61.5 17q-38 0 -62.5 -17
t-24.5 -47q0 -51 87 -68q86 16 86 68zM287 -39q0 35 -32 54t-61 23q-29 -4 -61.5 -23t-32.5 -54q0 -31 27 -49t67 -18q39 0 66 18.5t27 48.5z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="388" 
d="M38 128q0 55 41 93.5t107 38.5q83 0 123.5 -58t40.5 -149q0 -89 -45 -147.5t-129 -58.5q-72 0 -116 46l28 42q36 -39 88 -39q55 0 84 42.5t30 104.5v16q-17 -24 -48 -42.5t-66 -18.5q-59 0 -98.5 34t-39.5 96zM98 130q0 -41 26 -62.5t66 -21.5q63 0 98 56q-4 44 -28 76.5
t-71 32.5q-42 0 -66.5 -25t-24.5 -56z" />
    <glyph glyph-name="parenleftinferior" unicode="&#x208d;" horiz-adv-x="161" 
d="M151 -195l-35 -16q-90 111 -90 265q0 153 90 265l35 -16q-63 -124 -63 -249q0 -126 63 -249z" />
    <glyph glyph-name="parenrightinferior" unicode="&#x208e;" horiz-adv-x="161" 
d="M148 54q0 -152 -91 -265l-34 16q63 123 63 249t-63 249l34 16q43 -53 67 -123t24 -142z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="680" 
d="M398 -12q-4 0 -11.5 0.5t-10.5 0.5l-30 -89h-50l32 95q-45 8 -79 23l-39 -118h-50l47 141q-71 45 -111.5 121t-40.5 171q0 152 98.5 248.5t244.5 96.5q4 0 11 -0.5t11 -0.5l30 91h50l-32 -97q40 -9 76 -29l42 126h50l-51 -152q34 -26 66 -70l-70 -39q-12 17 -23 28
l-157 -473q55 1 103 27.5t77 69.5l71 -39q-97 -132 -254 -132zM141 333q0 -136 93 -213l161 484q-110 -2 -182 -77.5t-72 -193.5zM275 92q35 -18 76 -26l168 501q-36 23 -75 32z" />
    <glyph glyph-name="frenchfranc" unicode="&#x20a3;" horiz-adv-x="561" 
d="M171 0h-83v137h-78v48h78v482h437v-74h-354v-215h347v-74h-347v-119h202v-48h-202v-137z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="518" 
d="M19 328v46h64q-35 49 -35 106q0 82 66 139.5t158 57.5q68 0 121 -29.5t81 -82.5l-67 -40q-14 34 -48.5 58t-78.5 24q-59 0 -101 -35t-42 -93q0 -53 39 -105h192v-46h-155q29 -38 36 -70h119v-46h-116q-5 -69 -75 -119q25 9 52 9q33 0 76.5 -20.5t71.5 -20.5q31 0 57 12.5
t38 27.5l36 -67q-49 -47 -135 -47q-47 0 -95 23t-84 23q-42 0 -118 -40l-30 62q64 29 98.5 68.5t34.5 84.5v4h-160v46h150q-9 22 -49 70h-101z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="729" 
d="M640 0h-80l-177 241h-212v-241h-83v241h-78v48h78v95h-78v48h78v235h85l173 -235h211v235h83v-235h78v-48h-78v-95h78v-48h-78v-241zM171 384v-95h177l-69 95h-108zM381 384l70 -95h106v95h-176zM171 532v-100h73zM487 241l70 -95v95h-70z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="640" 
d="M635 439h-72q-9 -76 -64 -125t-143 -49h-185v-265h-83v439h-78v52h78v176h268q89 0 144 -50t63 -126h72v-52zM171 593v-102h306q-8 46 -43.5 74t-87.5 28h-175zM346 339q51 0 86.5 27.5t43.5 72.5h-305v-100h175z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="1019" 
d="M782 -12q-123 0 -195 76l39 54q25 -29 68.5 -49t90.5 -20q54 0 84 23t30 60q0 32 -31 50.5t-74.5 28t-87.5 22t-75 43t-31 80.5q0 59 48.5 99t131.5 40q109 0 179 -70l-35 -52q-51 61 -144 61q-49 0 -78 -21.5t-29 -54.5q0 -29 31 -45.5t74.5 -26t87.5 -22.5t75 -45.5
t31 -84.5q0 -64 -50 -105t-140 -41zM562 0h-98l-170 265h-133v-265h-83v667h268q93 0 151 -55t58 -146q0 -86 -50 -137t-123 -57zM336 338q59 0 96 36t37 92t-37 91.5t-96 35.5h-175v-255h175z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="883" 
d="M678 0h-90l-66 241h-162l-66 -241h-90l-69 241h-115v48h102l-27 95h-75v48h61l-67 235h92l62 -235h174l65 235h69l64 -235h173l63 235h92l-68 -235h63v-48h-76l-27 -95h103v-48h-117zM181 384l25 -95h97l26 95h-148zM553 384l27 -95h96l25 95h-148zM399 384l-26 -95h136
l-26 95h-84zM593 241l36 -130l34 130h-70zM219 241l35 -130l36 130h-71zM441 538l-29 -106h58z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="820" 
d="M521 0h-248v387h73v-321h175q151 0 151 151v333h73v-338q0 -212 -224 -212zM323 550q225 0 225 -212v-176h-73v170q0 152 -152 152h-175v-484h-73v550h248z" />
    <glyph glyph-name="dong" unicode="&#x20ab;" horiz-adv-x="579" 
d="M483 -143h-363v48h363v-48zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q100 0 163 -85v125h-157v48h157v84h75v-84h68v-48h-68v-535zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5
t-42 -134.5t42 -134t113 -52z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="695" 
d="M415 -12q-121 0 -211.5 67.5t-119.5 181.5h-53v52h43q-2 28 -2 44t2 46h-43v52h53q30 112 120.5 179.5t210.5 67.5q160 0 253 -132l-70 -39q-29 43 -78 70t-105 27q-85 0 -150 -47t-92 -126h303v-52h-315q-3 -23 -3 -46t3 -44h315v-52h-303q26 -80 91.5 -127.5
t150.5 -47.5q56 0 105 26.5t78 70.5l71 -39q-97 -132 -254 -132z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="475" 
d="M150 281v-170q0 -28 12.5 -45t36.5 -17q28 0 51 21l22 -51q-31 -31 -90 -31q-49 0 -78 27.5t-29 85.5v404q0 73 53.5 122.5t137.5 49.5q75 0 125.5 -36t50.5 -90q0 -45 -31.5 -81t-89.5 -75zM150 355l142 96q41 28 59.5 49t18.5 48q0 32 -31.5 52.5t-73.5 20.5
q-49 0 -82 -32t-33 -85v-149z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="1113" 
d="M910 350q-73 0 -117.5 47.5t-44.5 117.5t44.5 117.5t117.5 47.5q74 0 118.5 -47.5t44.5 -117.5t-44.5 -117.5t-118.5 -47.5zM910 398q48 0 76 33t28 84q0 50 -28 82.5t-76 32.5q-47 0 -74.5 -32.5t-27.5 -82.5q0 -51 27.5 -84t74.5 -33zM630 0h-80l-389 532v-532h-83v667
h85l384 -521v521h83v-667z" />
    <glyph glyph-name="published" unicode="&#x2117;" horiz-adv-x="778" 
d="M734 334q0 -142 -101 -243.5t-244 -101.5t-244 101.5t-101 243.5t101 243.5t244 101.5t244 -101.5t101 -243.5zM703 334q0 129 -92.5 221.5t-221.5 92.5q-130 0 -222 -92.5t-92 -221.5q0 -130 92 -222t222 -92q129 0 221.5 92t92.5 222zM549 419q0 -52 -36 -83t-89 -31
h-112v-174h-37v405h149q52 0 88.5 -31.5t36.5 -85.5zM512 419q0 37 -25 59.5t-63 22.5h-112v-162h112q38 0 63 22t25 58z" />
    <glyph glyph-name="servicemark" unicode="&#x2120;" horiz-adv-x="476" 
d="M439 447h-28v182l-75 -182h-8l-75 182v-182h-28v220h43l64 -160l64 160h43v-220zM188 506q0 -31 -22.5 -49t-64.5 -18q-55 0 -86 36l18 21q28 -32 67 -32q55 0 55 39q0 19 -21.5 30t-46.5 15t-46.5 19.5t-21.5 42.5q0 28 22.5 45.5t57.5 17.5q55 0 82 -32l-18 -19
q-22 27 -63 27q-21 0 -34.5 -10t-13.5 -26q0 -17 21 -27t47 -14.5t47 -20.5t21 -45z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="454" 
d="M419 447h-28v182l-75 -182h-8l-75 182v-182h-28v220h43l64 -160l64 160h43v-220zM169 641h-62v-194h-28v194h-62v26h152v-26z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="765" 
d="M69 74h135q-153 92 -153 274q0 145 92 237.5t239 92.5q148 0 239.5 -92.5t91.5 -237.5q0 -182 -151 -274h134v-74h-240v74q71 18 121 87t50 173q0 115 -65 192t-180 77t-180 -77t-65 -192q0 -104 50.5 -173t121.5 -87v-74h-240v74z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="842" 
d="M811 324h-632q-5 0 -5 -5v-190q0 -14 9 -23q98 -103 240 -103q151 0 250 115h57q-54 -63 -135 -99.5t-174 -36.5q-161 0 -275.5 103t-114.5 248t114.5 248t275.5 103t275.5 -103t114.5 -248v-9zM668 349v191q0 14 -10 24q-97 99 -235 99q-140 0 -239 -102
q-10 -10 -10 -24v-188q0 -6 5 -6h485q4 0 4 6z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="807" 
d="M175 267h-60v322l-66 -69l-35 37l108 110h53v-400zM590 667l-427 -667h-55l426 667h56zM761 109q0 -52 -41 -84t-110 -32q-103 0 -150 65l32 38q44 -54 116 -54q43 0 68 19t25 51q0 69 -104 69q-34 0 -40 -1v50q7 -1 39 -1q98 0 98 64q0 30 -25.5 47t-63.5 17
q-64 0 -109 -49l-31 36q55 62 145 62q64 0 104 -29t40 -78q0 -40 -27 -63.5t-62 -29.5q34 -3 65 -28t31 -69z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="915" 
d="M341 267h-291v45q124 88 176.5 140.5t52.5 98.5q0 36 -25 54.5t-60 18.5q-73 0 -112 -52l-33 38q50 63 147 63q60 0 101.5 -30.5t41.5 -87.5q0 -56 -50 -111.5t-151 -127.5h203v-49zM698 667l-427 -667h-55l426 667h56zM870 109q0 -52 -41 -84t-110 -32q-103 0 -150 65
l32 38q44 -54 116 -54q43 0 68 19t25 51q0 69 -104 69q-34 0 -40 -1v50q7 -1 39 -1q98 0 98 64q0 30 -25.5 47t-63.5 17q-64 0 -109 -49l-31 36q55 62 145 62q64 0 104 -29t40 -78q0 -40 -27 -63.5t-62 -29.5q34 -3 65 -28t31 -69z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="807" 
d="M175 267h-60v322l-66 -69l-35 37l108 110h53v-400zM765 102q0 -52 -44.5 -80.5t-108.5 -28.5q-65 0 -109 28.5t-44 80.5q0 38 29 66t72 38q-41 10 -67.5 34t-26.5 61q0 52 43.5 78.5t102.5 26.5t102.5 -26.5t43.5 -78.5q0 -37 -26.5 -61t-67.5 -34q43 -10 72 -38t29 -66z
M698 294q0 30 -24.5 47t-61.5 17q-38 0 -62.5 -17t-24.5 -47q0 -51 87 -68q86 16 86 68zM705 107q0 35 -32 54t-61 23q-29 -4 -61.5 -23t-32.5 -54q0 -31 27 -49t67 -18q39 0 66 18.5t27 48.5zM590 667l-427 -667h-55l426 667h56z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="915" 
d="M874 102q0 -52 -44.5 -80.5t-108.5 -28.5q-65 0 -109 28.5t-44 80.5q0 38 29 66t72 38q-41 10 -67.5 34t-26.5 61q0 52 43.5 78.5t102.5 26.5t102.5 -26.5t43.5 -78.5q0 -37 -26.5 -61t-67.5 -34q43 -10 72 -38t29 -66zM807 294q0 30 -24.5 47t-61.5 17q-38 0 -62.5 -17
t-24.5 -47q0 -51 87 -68q86 16 86 68zM814 107q0 35 -32 54t-61 23q-29 -4 -61.5 -23t-32.5 -54q0 -31 27 -49t67 -18q39 0 66 18.5t27 48.5zM698 667l-427 -667h-55l426 667h56zM343 376q0 -52 -41 -84t-110 -32q-103 0 -150 65l32 38q44 -54 116 -54q43 0 68 19t25 51
q0 69 -104 69q-34 0 -40 -1v50q7 -1 39 -1q98 0 98 64q0 30 -25.5 47t-63.5 17q-64 0 -109 -49l-31 36q55 62 145 62q64 0 104 -29t40 -78q0 -40 -27 -63.5t-62 -29.5q34 -3 65 -28t31 -69z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="912" 
d="M871 102q0 -52 -44.5 -80.5t-108.5 -28.5q-65 0 -109 28.5t-44 80.5q0 38 29 66t72 38q-41 10 -67.5 34t-26.5 61q0 52 43.5 78.5t102.5 26.5t102.5 -26.5t43.5 -78.5q0 -37 -26.5 -61t-67.5 -34q43 -10 72 -38t29 -66zM804 294q0 30 -24.5 47t-61.5 17q-38 0 -62.5 -17
t-24.5 -47q0 -51 87 -68q86 16 86 68zM811 107q0 35 -32 54t-61 23q-29 -4 -61.5 -23t-32.5 -54q0 -31 27 -49t67 -18q39 0 66 18.5t27 48.5zM344 393q0 -60 -43 -96.5t-109 -36.5q-99 0 -148 65l35 39q42 -55 113 -55q41 0 66.5 23.5t25.5 58.5q0 37 -24.5 60t-65.5 23
q-56 0 -95 -39l-43 15v217h261v-49h-201v-134q38 36 96 36q55 0 93.5 -33.5t38.5 -93.5zM695 667l-427 -667h-55l426 667h56z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="830" 
d="M789 102q0 -52 -44.5 -80.5t-108.5 -28.5q-65 0 -109 28.5t-44 80.5q0 38 29 66t72 38q-41 10 -67.5 34t-26.5 61q0 52 43.5 78.5t102.5 26.5t102.5 -26.5t43.5 -78.5q0 -37 -26.5 -61t-67.5 -34q43 -10 72 -38t29 -66zM722 294q0 30 -24.5 47t-61.5 17q-38 0 -62.5 -17
t-24.5 -47q0 -51 87 -68q86 16 86 68zM729 107q0 35 -32 54t-61 23q-29 -4 -61.5 -23t-32.5 -54q0 -31 27 -49t67 -18q39 0 66 18.5t27 48.5zM323 625l-166 -358h-67l166 351h-223v49h290v-42zM613 667l-427 -667h-55l426 667h56z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="582" 
d="M538 211h-269l12 -130l-281 205l281 204l-12 -129h269v-150z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="582" 
d="M216 17v269l-130 -12l205 281l204 -281l-129 12v-269h-150z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="582" 
d="M584 286l-281 -205l12 130h-268v150h268l-12 129z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="582" 
d="M366 555v-269l130 12l-205 -281l-204 281l129 -12v269h150z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M286 -12q-108 0 -173.5 70.5t-65.5 175.5q0 104 62 174.5t158 70.5q99 0 155 -82q-30 67 -95 130t-154 107l56 46q123 -64 209.5 -178t86.5 -253q0 -115 -65 -188t-174 -73zM286 55q74 0 117 51.5t43 127.5q0 74 -43 126t-117 52t-117.5 -52t-43.5 -126q0 -75 43.5 -127
t117.5 -52z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="658" 
d="M649 0h-640l268 667h103zM530 74l-201 507l-201 -507h402z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="707" 
d="M566 -90h-83v683h-259v-683h-83v683h-109v74h643v-74h-109v-683z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="575" 
d="M157 593l229 -295l-232 -314h367v-74h-467v74l231 313l-230 296v74h466v-74h-364z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="499" 
d="M470 311h-441v52h441v-52z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="138" 
d="M310 667l-427 -667h-55l426 667h56z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="230" 
d="M170 244q0 -22 -16.5 -38t-38.5 -16t-38.5 16t-16.5 38t16 38.5t39 16.5t39 -16.5t16 -38.5z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="725" 
d="M427 0h-58l-130 327l-155 -61l-18 50l206 79l127 -317l235 589h59z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="656" 
d="M183 170q-75 0 -114.5 49.5t-39.5 115.5q0 67 39 116t115 49q48 0 85.5 -31t58.5 -78q23 48 60.5 78.5t85.5 30.5q75 0 114.5 -49t39.5 -116q0 -66 -39.5 -115.5t-114.5 -49.5q-48 0 -85.5 30.5t-60.5 78.5q-21 -47 -58.5 -78t-85.5 -31zM468 223q48 0 75.5 31t27.5 81
t-27.5 81.5t-75.5 31.5q-39 0 -68.5 -33t-46.5 -80q17 -47 46.5 -79.5t68.5 -32.5zM189 222q72 0 115 113q-43 113 -115 113q-48 0 -76 -31.5t-28 -81.5q0 -49 28 -81t76 -32z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="348" 
d="M78 -100h-61v55h61q27 0 46.5 23t19.5 57v598q0 60 37.5 97.5t87.5 37.5h62v-55h-62q-27 0 -46.5 -22.5t-19.5 -57.5v-599q0 -59 -37.5 -96.5t-87.5 -37.5z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="499" 
d="M29 211v59q47 -33 110 -33q46 0 104.5 29t115.5 29q60 0 111 -27v-59q-49 34 -111 34q-47 0 -105 -29.5t-115 -29.5q-63 0 -110 27zM29 400v58q49 -33 110 -33q46 0 104.5 29.5t115.5 29.5q60 0 111 -27v-59q-47 33 -111 33q-47 0 -105 -29t-115 -29q-61 0 -110 27z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="499" 
d="M123 93h-54l75 118h-115v52h148l90 141h-238v53h272l74 117h55l-75 -117h115v-53h-148l-90 -141h238v-52h-272z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="499" 
d="M470 99l-441 218v53l441 219v-63l-377 -183l377 -182v-62zM470 0h-441v52h441v-52z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="499" 
d="M469 317l-441 -218v62l377 182l-377 183v63l441 -219v-53zM469 0h-441v52h441v-52z" />
    <glyph glyph-name="triangleright" unicode="&#x22b2;" horiz-adv-x="587" 
d="M589 286l-535 -309v618z" />
    <glyph glyph-name="triangleleft" unicode="&#x22b3;" horiz-adv-x="587" 
d="M535 595v-618l-535 309z" />
    <glyph glyph-name="dotmath" unicode="&#x22c5;" horiz-adv-x="230" 
d="M170 244q0 -22 -16.5 -38t-38.5 -16t-38.5 16t-16.5 38t16 38.5t39 16.5t39 -16.5t16 -38.5z" />
    <glyph glyph-name="uni2318" unicode="&#x2318;" horiz-adv-x="667" 
d="M226 154v74h-74q-31 0 -51.5 -21.5t-20.5 -52.5t21 -53t52 -22t52 22t21 53zM226 439v75q0 31 -20.5 52.5t-51.5 21.5t-52.5 -21.5t-21.5 -52.5t21.5 -53t52.5 -22h72zM265 265h137v137h-137v-137zM586 154q0 31 -20.5 52.5t-51.5 21.5h-73v-74q0 -31 21 -53t52 -22
q30 0 51 22t21 53zM586 514q0 31 -20.5 52.5t-51.5 21.5t-52 -21.5t-21 -52.5v-75h73q30 0 51 22t21 53zM625 154q0 -46 -32.5 -79t-78.5 -33t-79 33t-33 79v74h-137v-74q0 -46 -33 -79t-79 -33t-78.5 33t-32.5 79t32.5 78.5t78.5 32.5h73v137h-72q-46 0 -79 33t-33 79
t33 78.5t79 32.5t78.5 -32.5t32.5 -78.5v-75h137v75q0 46 33 78.5t79 32.5t78.5 -32.5t32.5 -78.5t-32.5 -79t-78.5 -33h-73v-137h73q46 0 78.5 -32.5t32.5 -78.5z" />
    <glyph glyph-name="blacksquare" unicode="&#x25a0;" horiz-adv-x="661" 
d="M618 0h-571v572h571v-572z" />
    <glyph glyph-name="triagup" unicode="&#x25b2;" horiz-adv-x="616" 
d="M617 29l-619 1l309 535z" />
    <glyph glyph-name="triagdn" unicode="&#x25bc;" horiz-adv-x="616" 
d="M617 510l-310 -535l-309 535h619z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="375" 
d="M198 0h-21l-158 334l158 333h21l158 -333zM188 41l132 293l-133 292l-132 -292z" />
    <glyph glyph-name="circlesolid" unicode="&#x25cf;" horiz-adv-x="695" 
d="M644 285q0 -123 -87 -210t-210 -87t-210 87t-87 210t87 210t210 87t210 -87t87 -210z" />
    <glyph glyph-name="blackstar" unicode="&#x2605;" horiz-adv-x="759" 
d="M759 421l-235 -171l90 -275l-235 170l-234 -170l89 275l-234 171h290l89 276l90 -276h290z" />
    <glyph glyph-name="ballotbox" unicode="&#x2610;" horiz-adv-x="661" 
d="M616 0h-571v572h571v-572zM559 52v467h-458v-467h458z" />
    <glyph glyph-name="boxchecked" unicode="&#x2611;" horiz-adv-x="661" 
d="M677 698l-69 -126h8v-572h-571v572h502l84 155zM559 52v428l-211 -389l-202 246l42 39l152 -188l178 331h-417v-467h458z" />
    <glyph glyph-name="diamond" unicode="&#x2666;" horiz-adv-x="501" 
d="M501 285l-251 -250l-250 250l250 251z" />
    <glyph glyph-name="checkmark" unicode="&#x2713;" horiz-adv-x="531" 
d="M531 698l-329 -607l-202 246l42 39l152 -188l291 539z" />
    <glyph glyph-name="perthousand.onum" unicode="&#xf628;" horiz-adv-x="878" 
d="M288 425q0 -58 -37 -97t-95 -39q-57 0 -93.5 39t-36.5 97t36.5 97.5t93.5 39.5q58 0 95 -39.5t37 -97.5zM566 125q0 -58 -37 -97t-94 -39t-94 39t-37 97q0 59 37 98t94 39t94 -39t37 -98zM514 550l-384 -550h-52l382 550h54zM236 425q0 40 -22.5 66.5t-57.5 26.5
q-34 0 -56.5 -27t-22.5 -66t22.5 -65.5t56.5 -26.5q36 0 58 26t22 66zM514 125q0 40 -22 66.5t-57 26.5q-36 0 -58 -26.5t-22 -66.5q0 -39 22.5 -65.5t57.5 -26.5t57 26.5t22 65.5zM852 125q0 -58 -37 -97t-94 -39t-93.5 40t-36.5 96q0 59 36.5 98t93.5 39t94 -39t37 -98z
M801 125q0 40 -22 66.5t-58 26.5q-35 0 -57 -26.5t-22 -66.5q0 -39 22 -65.5t57 -26.5t57.5 26.5t22.5 65.5z" />
    <glyph glyph-name="uniF637" unicode="&#xf637;" horiz-adv-x="398" 
d="M338 326h-58v202q0 72 -71 72q-54 0 -92 -46v-228h-57v434h57v-165q47 53 116 53q105 0 105 -107v-215z" />
    <glyph glyph-name="zero.tzero" unicode="&#xf638;" horiz-adv-x="598" 
d="M299 -12q-88 0 -149 55l-27 -43h-65l54 86q-68 95 -68 247q0 64 14.5 123t44 109.5t80 81t116.5 30.5q85 0 147 -54l28 44h65l-54 -87q69 -96 69 -247q0 -64 -14.5 -123t-44 -110t-80.5 -81.5t-116 -30.5zM299 62q60 0 100 41.5t55 99.5t15 130q0 102 -31 171l-246 -394
q42 -48 107 -48zM129 333q0 -101 30 -171l246 394q-42 47 -106 47q-60 0 -100 -41t-55 -99t-15 -130z" />
    <glyph glyph-name="zero.tnum" unicode="&#xf639;" horiz-adv-x="598" 
d="M299 -12q-66 0 -116.5 30.5t-80 81.5t-44 110t-14.5 123t14.5 123t44 109.5t80 81t116.5 30.5q65 0 116 -30.5t80.5 -81t44 -109.5t14.5 -123t-14.5 -123t-44 -110t-80.5 -81.5t-116 -30.5zM299 62q60 0 100 41.5t55 99.5t15 130t-15 130t-55 99t-100 41t-100 -41
t-55 -99t-15 -130t15 -130t55 -99.5t100 -41.5z" />
    <glyph glyph-name="two.tnum" unicode="&#xf63a;" horiz-adv-x="598" 
d="M527 0h-458v66q104 82 161.5 130.5t111 103t75.5 97t22 84.5q0 60 -41 91t-98 31q-115 0 -181 -84l-51 53q38 50 99.5 77.5t132.5 27.5q91 0 157.5 -51t66.5 -145q0 -93 -80 -185.5t-244 -221.5h327v-74z" />
    <glyph glyph-name="three.tnum" unicode="&#xf63b;" horiz-adv-x="598" 
d="M298 -12q-80 0 -142 31t-95 79l49 52q30 -40 79 -64t107 -24q73 0 114.5 33.5t41.5 91.5q0 60 -44.5 89t-121.5 29q-59 0 -69 -1v76q11 -1 69 -1q68 0 111.5 27.5t43.5 82.5q0 53 -42.5 83.5t-106.5 30.5q-101 0 -176 -81l-46 52q85 103 228 103q99 0 162.5 -47.5
t63.5 -130.5q0 -66 -44 -105.5t-100 -49.5q55 -5 104.5 -48t49.5 -116q0 -85 -64 -138.5t-172 -53.5z" />
    <glyph glyph-name="four.tnum" unicode="&#xf63c;" horiz-adv-x="598" 
d="M451 0h-83v169h-317v69l286 429h114v-425h94v-73h-94v-169zM368 242v348l-234 -348h234z" />
    <glyph glyph-name="five.tnum" unicode="&#xf63d;" horiz-adv-x="598" 
d="M304 -12q-154 0 -235 107l51 55q69 -88 184 -88q68 0 111 41t43 102q0 65 -42.5 104.5t-110.5 39.5q-90 0 -154 -63l-61 21v360h408v-74h-325v-232q60 60 156 60q88 0 150 -57.5t62 -155.5q0 -100 -67 -160t-170 -60z" />
    <glyph glyph-name="six.tnum" unicode="&#xf63e;" horiz-adv-x="598" 
d="M315 -12q-67 0 -118 28t-80.5 76.5t-44 109t-14.5 130.5q0 94 28 170t90.5 125.5t152.5 49.5q115 0 185 -80l-42 -62q-58 68 -143 68q-62 0 -105.5 -39t-63 -96.5t-19.5 -126.5q0 -17 1 -26q25 40 77.5 72t111.5 32q95 0 156.5 -55.5t61.5 -157.5q0 -91 -64 -154.5
t-170 -63.5zM312 62q72 0 112.5 43.5t40.5 97.5q0 70 -43 107t-111 37q-49 0 -94 -27.5t-73 -72.5q7 -74 48 -129.5t120 -55.5z" />
    <glyph glyph-name="seven.tnum" unicode="&#xf63f;" horiz-adv-x="598" 
d="M254 0h-92l269 593h-359v74h455v-57z" />
    <glyph glyph-name="eight.tnum" unicode="&#xf640;" horiz-adv-x="598" 
d="M299 -12q-102 0 -170.5 49t-68.5 132q0 63 45.5 109t113.5 66q-64 18 -106 58.5t-42 101.5q0 84 68 128.5t160 44.5t160.5 -44.5t68.5 -128.5q0 -61 -42 -101.5t-107 -58.5q68 -20 113.5 -66t45.5 -109q0 -82 -68.5 -131.5t-170.5 -49.5zM299 378q20 3 40.5 10t46.5 19.5
t42 35t16 50.5q0 50 -41.5 80t-103.5 30q-63 0 -104 -30t-41 -80q0 -28 16 -50.5t42 -35t46.5 -19t40.5 -10.5zM299 62q64 0 109.5 32t45.5 84q0 40 -31.5 70.5t-64 43t-59.5 15.5q-27 -3 -59.5 -15.5t-64.5 -43t-32 -70.5q0 -52 45.5 -84t110.5 -32z" />
    <glyph glyph-name="nine.tnum" unicode="&#xf641;" horiz-adv-x="598" 
d="M269 -11q-115 0 -185 80l42 62q58 -68 143 -68q64 0 108 40t62 97t18 124q0 18 -1 27q-25 -40 -77 -72t-111 -32q-95 0 -157 55.5t-62 157.5q0 90 64 154t169 64q68 0 119 -28t80.5 -77t44 -109t14.5 -131q0 -94 -28 -169.5t-90.5 -125t-152.5 -49.5zM287 319
q49 0 94.5 27t71.5 72q-6 74 -47 130t-120 56q-71 0 -112 -44t-41 -97q0 -70 43 -107t111 -37z" />
    <glyph glyph-name="percent.onum" unicode="&#xf642;" horiz-adv-x="592" 
d="M288 425q0 -58 -37 -97t-95 -39q-57 0 -93.5 39t-36.5 97t36.5 97.5t93.5 39.5q58 0 95 -39.5t37 -97.5zM566 125q0 -58 -37 -97t-94 -39t-94 39t-37 97q0 59 37 98t94 39t94 -39t37 -98zM514 550l-384 -550h-52l382 550h54zM236 425q0 40 -22.5 66.5t-57.5 26.5
q-34 0 -56.5 -27t-22.5 -66t22.5 -65.5t56.5 -26.5q36 0 58 26t22 66zM514 125q0 40 -22 66.5t-57 26.5q-36 0 -58 -26.5t-22 -66.5t22 -66t58 -26q35 0 57 26.5t22 65.5z" />
    <glyph glyph-name="zero.tonum" unicode="&#xf643;" horiz-adv-x="598" 
d="M554 275q0 -75 -25.5 -138t-85 -106t-144.5 -43t-144.5 43t-85 106t-25.5 138q0 56 15 106t45 91t80.5 65.5t114.5 24.5t114.5 -24.5t80.5 -65.5t45 -91t15 -106zM469 275q0 89 -43 151t-127 62t-127 -62t-43 -151t43 -151t127 -62t127 62t43 151z" />
    <glyph glyph-name="one.tonum" unicode="&#xf644;" horiz-adv-x="598" 
d="M395 0h-83v440l-107 -113l-50 51l167 172h73v-550z" />
    <glyph glyph-name="two.tonum" unicode="&#xf645;" horiz-adv-x="598" 
d="M509 0h-431v67q180 74 269 152t89 152q0 51 -39.5 84t-96.5 33q-58 0 -106.5 -22.5t-79.5 -61.5l-51 53q38 49 102.5 77t136.5 28q95 0 156.5 -49.5t61.5 -130.5q0 -88 -81 -170.5t-208 -137.5h278v-74z" />
    <glyph glyph-name="three.tonum" unicode="&#xf646;" horiz-adv-x="598" 
d="M298 -131q-80 0 -142 31t-95 79l49 52q30 -40 79 -64t107 -24q73 0 114.5 33.5t41.5 91.5q0 60 -44.5 89t-121.5 29q-59 0 -69 -1v76q11 -1 69 -1q68 0 111.5 27.5t43.5 82.5q0 53 -42.5 83.5t-106.5 30.5q-101 0 -176 -81l-46 52q85 103 228 103q99 0 162.5 -47.5
t63.5 -130.5q0 -66 -44 -105.5t-100 -49.5q55 -5 104.5 -48t49.5 -116q0 -85 -64 -138.5t-172 -53.5z" />
    <glyph glyph-name="four.tonum" unicode="&#xf647;" horiz-adv-x="598" 
d="M545 55h-94v-172h-83v172h-317v69l286 426h114v-422h94v-73zM368 128v345l-234 -345h234z" />
    <glyph glyph-name="five.tonum" unicode="&#xf648;" horiz-adv-x="598" 
d="M304 -129q-154 0 -235 107l51 55q69 -88 184 -88q68 0 111 41t43 102q0 65 -42.5 104.5t-110.5 39.5q-90 0 -154 -63l-61 21v360h408v-74h-325v-232q60 60 156 60q88 0 150 -57.5t62 -155.5q0 -100 -67 -160t-170 -60z" />
    <glyph glyph-name="six.tonum" unicode="&#xf649;" horiz-adv-x="598" 
d="M315 -12q-67 0 -118 28t-80.5 76.5t-44 109t-14.5 130.5q0 94 28 170t90.5 125.5t152.5 49.5q115 0 185 -80l-42 -62q-58 68 -143 68q-62 0 -105.5 -39t-63 -96.5t-19.5 -126.5q0 -17 1 -26q25 40 77.5 72t111.5 32q95 0 156.5 -55.5t61.5 -157.5q0 -91 -64 -154.5
t-170 -63.5zM312 62q72 0 112.5 43.5t40.5 97.5q0 70 -43 107t-111 37q-49 0 -94 -27.5t-73 -72.5q7 -74 48 -129.5t120 -55.5z" />
    <glyph glyph-name="seven.tonum" unicode="&#xf64a;" horiz-adv-x="598" 
d="M254 -117h-92l269 593h-359v74h455v-57z" />
    <glyph glyph-name="eight.tonum" unicode="&#xf64b;" horiz-adv-x="598" 
d="M299 -12q-102 0 -170.5 49t-68.5 132q0 63 45.5 109t113.5 66q-64 18 -106 58.5t-42 101.5q0 84 68 128.5t160 44.5t160.5 -44.5t68.5 -128.5q0 -61 -42 -101.5t-107 -58.5q68 -20 113.5 -66t45.5 -109q0 -82 -68.5 -131.5t-170.5 -49.5zM299 378q20 3 40.5 10t46.5 19.5
t42 35t16 50.5q0 50 -41.5 80t-103.5 30q-63 0 -104 -30t-41 -80q0 -28 16 -50.5t42 -35t46.5 -19t40.5 -10.5zM299 62q64 0 109.5 32t45.5 84q0 40 -31.5 70.5t-64 43t-59.5 15.5q-27 -3 -59.5 -15.5t-64.5 -43t-32 -70.5q0 -52 45.5 -84t110.5 -32z" />
    <glyph glyph-name="nine.tonum" unicode="&#xf64c;" horiz-adv-x="598" 
d="M269 -127q-115 0 -185 80l42 62q58 -68 143 -68q64 0 108 40t62 97t18 124q0 18 -1 27q-25 -40 -77 -72t-111 -32q-95 0 -157 55.5t-62 157.5q0 90 64 154t169 64q68 0 119 -28t80.5 -77t44 -109t14.5 -131q0 -94 -28 -169.5t-90.5 -125t-152.5 -49.5zM287 203
q49 0 94.5 27t71.5 72q-6 74 -47 130t-120 56q-71 0 -112 -44t-41 -97q0 -70 43 -107t111 -37z" />
    <glyph glyph-name="colonmonetary.onum" unicode="&#xf64d;" horiz-adv-x="582" 
d="M138 -53l31 91q-57 38 -89.5 99.5t-32.5 137.5q0 126 83.5 206.5t209.5 80.5h5l13 37h43l-14 -41q29 -4 58 -17l20 58h43l-26 -77q45 -28 77 -73l-63 -35q-15 22 -38 39l-133 -396h15q48 0 90 21.5t66 57.5l64 -35q-83 -113 -220 -113q-12 0 -38 2l-14 -43h-43l16 50
q-27 7 -57 20l-23 -70h-43zM127 275q0 -98 67 -162l128 379q-85 -6 -140 -66.5t-55 -150.5zM228 87q32 -18 57 -24l138 412q-30 13 -59 16z" />
    <glyph glyph-name="Euro.onum" unicode="&#xf64e;" horiz-adv-x="610" 
d="M410 313h-253q-2 -13 -2 -38q0 -21 3 -39h252v-44h-240q23 -62 76 -98.5t122 -36.5q48 0 90 21.5t66 57.5l64 -35q-83 -113 -220 -113q-103 0 -180 55.5t-102 148.5h-55v44h46q-2 12 -2 39q0 25 2 38h-46v44h54q26 93 103 149t180 56q139 0 219 -113l-63 -35
q-25 36 -66.5 57.5t-89.5 21.5q-69 0 -122.5 -36.5t-76.5 -99.5h241v-44z" />
    <glyph glyph-name="florin.onum" unicode="&#xf64f;" horiz-adv-x="367" 
d="M106 -119h-84l75 334h-51v57h62l31 135q34 154 152 154q53 0 86 -32l-33 -60q-14 18 -42 18q-59 0 -80 -90l-28 -125h106v-57h-119z" />
    <glyph glyph-name="numbersign.onum" unicode="&#xf650;" horiz-adv-x="496" 
d="M475 394l-14 -47h-88l-47 -141h89l-15 -47h-90l-54 -159h-57l54 159h-90l-53 -159h-57l53 159h-84l14 47h87l47 141h-87l14 47h88l53 156h57l-54 -156h90l54 156h57l-54 -156h87zM315 347h-88l-48 -141h90z" />
    <glyph glyph-name="sterling.onum" unicode="&#xf651;" horiz-adv-x="425" 
d="M14 224v48h71q-48 67 -48 125q0 70 53.5 117t130.5 47q55 0 100 -24t67 -66l-59 -32q-30 64 -102 64q-47 0 -80 -29.5t-33 -77.5q0 -8 1 -15.5t3 -15.5t4 -13.5t6.5 -14.5t6 -13t7.5 -14t8 -12.5t9 -13.5t8 -12h135v-48h-106q12 -27 12 -51q0 -61 -54 -96q9 4 22 4
q29 0 68.5 -14.5t60.5 -14.5q23 0 45 9t32 22l34 -55q-39 -39 -115 -39q-37 0 -75 16.5t-70 16.5q-33 0 -95 -30l-26 54q106 45 106 113q0 28 -23 65h-104z" />
    <glyph glyph-name="yen.onum" unicode="&#xf652;" horiz-adv-x="547" 
d="M314 0h-78v113h-213v48h213v71h-213v47h180l-196 271h91l177 -250l174 250h91l-193 -271h175v-47h-208v-71h208v-48h-208v-113z" />
    <glyph glyph-name="cent.onum" unicode="&#xf654;" horiz-adv-x="582" 
d="M309 -92v82q-115 10 -188.5 88.5t-73.5 196.5t73.5 196.5t188.5 88.5v59h58v-59q120 -10 192 -111l-63 -35q-46 65 -129 77v-432q86 11 129 77l64 -35q-75 -101 -193 -111v-82h-58zM127 275q0 -85 51.5 -145t130.5 -71v432q-80 -11 -131 -70.5t-51 -145.5z" />
    <glyph glyph-name="zero.dnom" unicode="&#xf655;" horiz-adv-x="388" 
d="M355 200q0 -83 -41 -145t-120 -62q-80 0 -120.5 61.5t-40.5 145.5t40.5 145t120.5 61q79 0 120 -61.5t41 -144.5zM293 200q0 68 -24 112.5t-75 44.5q-52 0 -76 -44.5t-24 -112.5t24 -113t76 -45q51 0 75 45t24 113z" />
    <glyph glyph-name="one.dnom" unicode="&#xf656;" horiz-adv-x="235" 
d="M175 0h-60v322l-66 -69l-35 37l108 110h53v-400z" />
    <glyph glyph-name="two.dnom" unicode="&#xf657;" horiz-adv-x="388" 
d="M341 0h-291v45q124 88 176.5 140.5t52.5 98.5q0 36 -25 54.5t-60 18.5q-73 0 -112 -52l-33 38q50 63 147 63q60 0 101.5 -30.5t41.5 -87.5q0 -56 -50 -111.5t-151 -127.5h203v-49z" />
    <glyph glyph-name="three.dnom" unicode="&#xf658;" horiz-adv-x="388" 
d="M343 109q0 -52 -41 -84t-110 -32q-103 0 -150 65l32 38q44 -54 116 -54q43 0 68 19t25 51q0 69 -104 69q-34 0 -40 -1v50q7 -1 39 -1q98 0 98 64q0 30 -25.5 47t-63.5 17q-64 0 -109 -49l-31 36q55 62 145 62q64 0 104 -29t40 -78q0 -40 -27 -63.5t-62 -29.5
q34 -3 65 -28t31 -69z" />
    <glyph glyph-name="four.dnom" unicode="&#xf659;" horiz-adv-x="388" 
d="M352 100h-58v-100h-59v100h-196v44l172 256h83v-251h58v-49zM235 149v198l-136 -198h136z" />
    <glyph glyph-name="five.dnom" unicode="&#xf65a;" horiz-adv-x="388" 
d="M344 126q0 -60 -43 -96.5t-109 -36.5q-99 0 -148 65l35 39q42 -55 113 -55q41 0 66.5 23.5t25.5 58.5q0 37 -24.5 60t-65.5 23q-56 0 -95 -39l-43 15v217h261v-49h-201v-134q38 36 96 36q55 0 93.5 -33.5t38.5 -93.5z" />
    <glyph glyph-name="six.dnom" unicode="&#xf65b;" horiz-adv-x="388" 
d="M350 125q0 -56 -40.5 -94t-106.5 -38q-84 0 -124.5 57.5t-40.5 148.5q0 90 45 148.5t129 58.5q69 0 117 -46l-29 -42q-36 39 -88 39q-56 0 -85 -43t-29 -103v-17q18 24 49 42.5t66 18.5q59 0 98 -34.5t39 -95.5zM290 123q0 41 -26 62.5t-66 21.5q-28 0 -54.5 -15
t-43.5 -41q3 -45 28 -77t72 -32q41 0 65.5 25t24.5 56z" />
    <glyph glyph-name="seven.dnom" unicode="&#xf65c;" horiz-adv-x="362" 
d="M323 358l-166 -358h-67l166 351h-223v49h290v-42z" />
    <glyph glyph-name="eight.dnom" unicode="&#xf65d;" horiz-adv-x="388" 
d="M347 102q0 -52 -44.5 -80.5t-108.5 -28.5q-65 0 -109 28.5t-44 80.5q0 38 29 66t72 38q-41 10 -67.5 34t-26.5 61q0 52 43.5 78.5t102.5 26.5t102.5 -26.5t43.5 -78.5q0 -37 -26.5 -61t-67.5 -34q43 -10 72 -38t29 -66zM280 294q0 30 -24.5 47t-61.5 17q-38 0 -62.5 -17
t-24.5 -47q0 -51 87 -68q86 16 86 68zM287 107q0 35 -32 54t-61 23q-29 -4 -61.5 -23t-32.5 -54q0 -31 27 -49t67 -18q39 0 66 18.5t27 48.5z" />
    <glyph glyph-name="nine.dnom" unicode="&#xf65e;" horiz-adv-x="388" 
d="M38 274q0 55 41 93.5t107 38.5q83 0 123.5 -58t40.5 -149q0 -89 -45 -147.5t-129 -58.5q-72 0 -116 46l28 42q36 -39 88 -39q55 0 84 42.5t30 104.5v16q-17 -24 -48 -42.5t-66 -18.5q-59 0 -98.5 34t-39.5 96zM98 276q0 -41 26 -62.5t66 -21.5q63 0 98 56q-4 44 -28 76.5
t-71 32.5q-42 0 -66.5 -25t-24.5 -56z" />
    <glyph glyph-name="zero.numr" unicode="&#xf661;" horiz-adv-x="388" 
d="M355 467q0 -83 -41 -145t-120 -62q-80 0 -120.5 61.5t-40.5 145.5t40.5 145t120.5 61q79 0 120 -61.5t41 -144.5zM293 467q0 68 -24 112.5t-75 44.5q-52 0 -76 -44.5t-24 -112.5t24 -113t76 -45q51 0 75 45t24 113z" />
    <glyph glyph-name="one.numr" unicode="&#xf662;" horiz-adv-x="235" 
d="M175 267h-60v322l-66 -69l-35 37l108 110h53v-400z" />
    <glyph glyph-name="two.numr" unicode="&#xf663;" horiz-adv-x="388" 
d="M341 267h-291v45q124 88 176.5 140.5t52.5 98.5q0 36 -25 54.5t-60 18.5q-73 0 -112 -52l-33 38q50 63 147 63q60 0 101.5 -30.5t41.5 -87.5q0 -56 -50 -111.5t-151 -127.5h203v-49z" />
    <glyph glyph-name="three.numr" unicode="&#xf664;" horiz-adv-x="388" 
d="M343 376q0 -52 -41 -84t-110 -32q-103 0 -150 65l32 38q44 -54 116 -54q43 0 68 19t25 51q0 69 -104 69q-34 0 -40 -1v50q7 -1 39 -1q98 0 98 64q0 30 -25.5 47t-63.5 17q-64 0 -109 -49l-31 36q55 62 145 62q64 0 104 -29t40 -78q0 -40 -27 -63.5t-62 -29.5
q34 -3 65 -28t31 -69z" />
    <glyph glyph-name="four.numr" unicode="&#xf665;" horiz-adv-x="388" 
d="M352 367h-58v-100h-59v100h-196v44l172 256h83v-251h58v-49zM235 416v198l-136 -198h136z" />
    <glyph glyph-name="five.numr" unicode="&#xf666;" horiz-adv-x="388" 
d="M344 393q0 -60 -43 -96.5t-109 -36.5q-99 0 -148 65l35 39q42 -55 113 -55q41 0 66.5 23.5t25.5 58.5q0 37 -24.5 60t-65.5 23q-56 0 -95 -39l-43 15v217h261v-49h-201v-134q38 36 96 36q55 0 93.5 -33.5t38.5 -93.5z" />
    <glyph glyph-name="six.numr" unicode="&#xf667;" horiz-adv-x="388" 
d="M350 392q0 -56 -40.5 -94t-106.5 -38q-84 0 -124.5 57.5t-40.5 148.5q0 90 45 148.5t129 58.5q69 0 117 -46l-29 -42q-36 39 -88 39q-56 0 -85 -43t-29 -103v-17q18 24 49 42.5t66 18.5q59 0 98 -34.5t39 -95.5zM290 390q0 41 -26 62.5t-66 21.5q-28 0 -54.5 -15
t-43.5 -41q3 -45 28 -77t72 -32q41 0 65.5 25t24.5 56z" />
    <glyph glyph-name="seven.numr" unicode="&#xf668;" horiz-adv-x="362" 
d="M323 625l-166 -358h-67l166 351h-223v49h290v-42z" />
    <glyph glyph-name="eight.numr" unicode="&#xf669;" horiz-adv-x="388" 
d="M347 369q0 -52 -44.5 -80.5t-108.5 -28.5q-65 0 -109 28.5t-44 80.5q0 38 29 66t72 38q-41 10 -67.5 34t-26.5 61q0 52 43.5 78.5t102.5 26.5t102.5 -26.5t43.5 -78.5q0 -37 -26.5 -61t-67.5 -34q43 -10 72 -38t29 -66zM280 561q0 30 -24.5 47t-61.5 17q-38 0 -62.5 -17
t-24.5 -47q0 -51 87 -68q86 16 86 68zM287 374q0 35 -32 54t-61 23q-29 -4 -61.5 -23t-32.5 -54q0 -31 27 -49t67 -18q39 0 66 18.5t27 48.5z" />
    <glyph glyph-name="nine.numr" unicode="&#xf66a;" horiz-adv-x="388" 
d="M38 541q0 55 41 93.5t107 38.5q83 0 123.5 -58t40.5 -149q0 -89 -45 -147.5t-129 -58.5q-72 0 -116 46l28 42q36 -39 88 -39q55 0 84 42.5t30 104.5v16q-17 -24 -48 -42.5t-66 -18.5q-59 0 -98.5 34t-39.5 96zM98 543q0 -41 26 -62.5t66 -21.5q63 0 98 56q-4 44 -28 76.5
t-71 32.5q-42 0 -66.5 -25t-24.5 -56z" />
    <glyph glyph-name="Abreve.smcp" unicode="&#xf66d;" 
d="M239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM462 676q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="Amacron.smcp" unicode="&#xf66e;" 
d="M468 622h-363v48h363v-48zM239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234z" />
    <glyph glyph-name="Aogonek.smcp" unicode="&#xf66f;" 
d="M239 550h94l232 -550q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 57 53 99h-19l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234z" />
    <glyph glyph-name="AEacute.smcp" unicode="&#xf670;" horiz-adv-x="821" 
d="M595 750l-169 -144h-57l148 144h78zM770 0h-383v120h-216l-76 -120h-89l351 550h413v-69h-305v-165h298v-69h-298v-178h305v-69zM387 189v279l-178 -279h178z" />
    <glyph glyph-name="Cacute.smcp" unicode="&#xf671;" horiz-adv-x="582" 
d="M510 750l-169 -144h-57l148 144h78zM340 -12q-126 0 -209.5 80.5t-83.5 206.5t83.5 206.5t209.5 80.5q139 0 219 -113l-63 -35q-25 36 -66.5 57.5t-89.5 21.5q-91 0 -152 -61.5t-61 -156.5q0 -94 61 -156t152 -62q48 0 90 21.5t66 57.5l64 -35q-83 -113 -220 -113z" />
    <glyph glyph-name="Ccaron.smcp" unicode="&#xf672;" horiz-adv-x="582" 
d="M380 606h-72l-98 144h51l83 -106l80 106h51zM340 -12q-126 0 -209.5 80.5t-83.5 206.5t83.5 206.5t209.5 80.5q139 0 219 -113l-63 -35q-25 36 -66.5 57.5t-89.5 21.5q-91 0 -152 -61.5t-61 -156.5q0 -94 61 -156t152 -62q48 0 90 21.5t66 57.5l64 -35
q-83 -113 -220 -113z" />
    <glyph glyph-name="Ccircumflex.smcp" unicode="&#xf673;" horiz-adv-x="582" 
d="M475 606h-51l-83 106l-80 -106h-51l95 144h72zM340 -12q-126 0 -209.5 80.5t-83.5 206.5t83.5 206.5t209.5 80.5q139 0 219 -113l-63 -35q-25 36 -66.5 57.5t-89.5 21.5q-91 0 -152 -61.5t-61 -156.5q0 -94 61 -156t152 -62q48 0 90 21.5t66 57.5l64 -35
q-83 -113 -220 -113z" />
    <glyph glyph-name="Cdotaccent.smcp" unicode="&#xf674;" horiz-adv-x="582" 
d="M340 -12q-126 0 -209.5 80.5t-83.5 206.5t83.5 206.5t209.5 80.5q139 0 219 -113l-63 -35q-25 36 -66.5 57.5t-89.5 21.5q-91 0 -152 -61.5t-61 -156.5q0 -94 61 -156t152 -62q48 0 90 21.5t66 57.5l64 -35q-83 -113 -220 -113zM343 605q-19 0 -33 14t-14 33t14 33t33 14
t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="Dcaron.smcp" unicode="&#xf675;" horiz-adv-x="615" 
d="M334 606h-72l-98 144h51l83 -106l80 106h51zM262 0h-200v550h199q133 0 213.5 -78t80.5 -198q0 -119 -80 -196.5t-213 -77.5zM261 69q100 0 156.5 58t56.5 147q0 90 -56 148.5t-156 58.5h-122v-412h121z" />
    <glyph glyph-name="Dcroat.smcp" unicode="&#xf676;" horiz-adv-x="626" 
d="M273 0h-200v251h-63v53h63v246h199q133 0 213.5 -78t80.5 -198q0 -119 -80 -196.5t-213 -77.5zM285 251h-134v-182h121q100 0 156.5 58t56.5 147q0 90 -56 148.5t-156 58.5h-122v-177h134v-53z" />
    <glyph glyph-name="Ebreve.smcp" unicode="&#xf677;" horiz-adv-x="509" 
d="M457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69zM441 676q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="Ecaron.smcp" unicode="&#xf678;" horiz-adv-x="509" 
d="M304 606h-72l-98 144h51l83 -106l80 106h51zM457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69z" />
    <glyph glyph-name="Edotaccent.smcp" unicode="&#xf679;" horiz-adv-x="509" 
d="M457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69zM269 605q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="Emacron.smcp" unicode="&#xf67a;" horiz-adv-x="509" 
d="M457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69zM446 622h-363v48h363v-48z" />
    <glyph glyph-name="Eng.smcp" unicode="&#xf67b;" horiz-adv-x="632" 
d="M368 -196q-93 0 -149 68l37 57q43 -56 107 -56q53 0 84 31t31 82v14l-325 428v-428h-78v550h82l321 -420v420h79v-570q0 -86 -52.5 -131t-136.5 -45z" />
    <glyph glyph-name="Eogonek.smcp" unicode="&#xf67c;" horiz-adv-x="509" 
d="M477 -105l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 57 53 99h-314v550h382v-69h-304v-165h299v-69h-299v-178h304v-69q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42z" />
    <glyph glyph-name="Gbreve.smcp" unicode="&#xf67d;" horiz-adv-x="626" 
d="M341 -12q-126 0 -210 80t-84 207t84 207t210 80q134 0 221 -101l-61 -41q-60 72 -160 72q-91 0 -152 -61t-61 -156q0 -94 61 -156.5t152 -62.5q87 0 147 58v107h-170v66h248v-203q-89 -96 -225 -96zM517 676q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72
q88 0 138 72z" />
    <glyph glyph-name="Gcircumflex.smcp" unicode="&#xf67e;" horiz-adv-x="626" 
d="M341 -12q-126 0 -210 80t-84 207t84 207t210 80q134 0 221 -101l-61 -41q-60 72 -160 72q-91 0 -152 -61t-61 -156q0 -94 61 -156.5t152 -62.5q87 0 147 58v107h-170v66h248v-203q-89 -96 -225 -96zM474 606h-51l-83 106l-80 -106h-51l95 144h72z" />
    <glyph glyph-name="Gcommaaccent.smcp" unicode="&#xf67f;" horiz-adv-x="626" 
d="M341 -12q-126 0 -210 80t-84 207t84 207t210 80q134 0 221 -101l-61 -41q-60 72 -160 72q-91 0 -152 -61t-61 -156q0 -94 61 -156.5t152 -62.5q87 0 147 58v107h-170v66h248v-203q-89 -96 -225 -96zM393 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5
q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49z" />
    <glyph glyph-name="Gdotaccent.smcp" unicode="&#xf680;" horiz-adv-x="626" 
d="M341 -12q-126 0 -210 80t-84 207t84 207t210 80q134 0 221 -101l-61 -41q-60 72 -160 72q-91 0 -152 -61t-61 -156q0 -94 61 -156.5t152 -62.5q87 0 147 58v107h-170v66h248v-203q-89 -96 -225 -96zM343 605q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5
t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="Hcircumflex.smcp" unicode="&#xf681;" horiz-adv-x="635" 
d="M449 606h-51l-83 106l-80 -106h-51l95 144h72zM560 0h-78v248h-329v-248h-78v550h78v-233h329v233h78v-550z" />
    <glyph glyph-name="Hbar.smcp" unicode="&#xf682;" horiz-adv-x="655" 
d="M570 0h-78v248h-329v-248h-78v411h-75v48h75v91h78v-91h329v91h78v-91h74v-48h-74v-411zM163 317h329v94h-329v-94z" />
    <glyph glyph-name="Ibreve.smcp" unicode="&#xf683;" horiz-adv-x="229" 
d="M153 0h-78v550h78v-550zM289 676q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="IJ.smcp" unicode="&#xf684;" horiz-adv-x="650" 
d="M386 -12q-93 0 -149 68l38 57q43 -56 107 -56q52 0 83.5 31t31.5 82v380h78v-386q0 -87 -52 -131.5t-137 -44.5zM153 0h-78v550h78v-550z" />
    <glyph glyph-name="Imacron.smcp" unicode="&#xf685;" horiz-adv-x="229" 
d="M297 622h-363v48h363v-48zM153 0h-78v550h78v-550z" />
    <glyph glyph-name="Iogonek.smcp" unicode="&#xf686;" horiz-adv-x="229" 
d="M153 0q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 57 53 99h-10v550h78v-550z" />
    <glyph glyph-name="Itilde.smcp" unicode="&#xf687;" horiz-adv-x="229" 
d="M153 0h-78v550h78v-550zM175 604q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="Jcircumflex.smcp" unicode="&#xf688;" horiz-adv-x="422" 
d="M439 606h-51l-83 106l-80 -106h-51l95 144h72zM157 -12q-93 0 -149 68l38 57q43 -56 107 -56q52 0 83.5 31t31.5 82v380h78v-386q0 -87 -52 -131.5t-137 -44.5z" />
    <glyph glyph-name="Kcommaaccent.smcp" unicode="&#xf689;" horiz-adv-x="534" 
d="M323 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM520 0h-97l-216 243l-54 -58v-185h-78v550h78v-275l249 275h97l-242 -261z" />
    <glyph glyph-name="Lacute.smcp" unicode="&#xf68a;" horiz-adv-x="458" 
d="M420 0h-345v550h78v-481h267v-69zM417 750l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="Lcaron.smcp" unicode="&#xf68b;" horiz-adv-x="458" 
d="M333 493q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM420 0h-345v550h78v-481h267v-69z" />
    <glyph glyph-name="Lcommaaccent.smcp" unicode="&#xf68c;" horiz-adv-x="458" 
d="M299 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM420 0h-345v550h78v-481h267v-69z" />
    <glyph glyph-name="Ldot.smcp" unicode="&#xf68d;" horiz-adv-x="472" 
d="M420 0h-345v550h78v-481h267v-69zM410 293q0 -22 -16.5 -38t-38.5 -16t-38.5 16t-16.5 38q0 23 16 39t39 16t39 -16t16 -39z" />
    <glyph glyph-name="Nacute.smcp" unicode="&#xf68e;" horiz-adv-x="632" 
d="M485 750l-169 -144h-57l148 144h78zM557 0h-79l-325 428v-428h-78v550h82l321 -420v420h79v-550z" />
    <glyph glyph-name="Ncaron.smcp" unicode="&#xf68f;" horiz-adv-x="632" 
d="M353 606h-72l-98 144h51l83 -106l80 106h51zM557 0h-79l-325 428v-428h-78v550h82l321 -420v420h79v-550z" />
    <glyph glyph-name="Ncommaaccent.smcp" unicode="&#xf690;" horiz-adv-x="632" 
d="M366 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM557 0h-79l-325 428v-428h-78v550h82l321 -420v420h79v-550z" />
    <glyph glyph-name="Obreve.smcp" unicode="&#xf691;" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM508 676q-65 -88 -175 -88q-109 0 -176 88
l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="Ohungarumlaut.smcp" unicode="&#xf692;" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM387 750l-115 -144h-48l94 144h69zM529 750
l-115 -144h-48l94 144h69z" />
    <glyph glyph-name="Omacron.smcp" unicode="&#xf693;" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM513 622h-363v48h363v-48z" />
    <glyph glyph-name="Oslashacute.smcp" unicode="&#xf694;" horiz-adv-x="662" 
d="M331 -12q-85 0 -151 39l-20 -27h-67l43 59q-89 81 -89 216q0 124 78.5 205.5t205.5 81.5q82 0 148 -38l19 26h67l-43 -58q93 -82 93 -217q0 -123 -79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -57 157l-255 -347q47 -28 108 -28zM127 275q0 -95 54 -155
l255 346q-48 27 -105 27q-92 0 -148 -61.5t-56 -156.5zM500 750l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="Racute.smcp" unicode="&#xf695;" horiz-adv-x="545" 
d="M448 750l-169 -144h-57l148 144h78zM500 0h-92l-144 213h-111v-213h-78v550h236q84 0 132.5 -47.5t48.5 -120.5q0 -70 -41 -114t-107 -49zM304 282q52 0 80 27.5t28 72.5q0 44 -28.5 71.5t-79.5 27.5h-151v-199h151z" />
    <glyph glyph-name="Rcaron.smcp" unicode="&#xf696;" horiz-adv-x="545" 
d="M302 606h-72l-98 144h51l83 -106l80 106h51zM500 0h-92l-144 213h-111v-213h-78v550h236q84 0 132.5 -47.5t48.5 -120.5q0 -70 -41 -114t-107 -49zM304 282q52 0 80 27.5t28 72.5q0 44 -28.5 71.5t-79.5 27.5h-151v-199h151z" />
    <glyph glyph-name="Rcommaaccent.smcp" unicode="&#xf697;" horiz-adv-x="545" 
d="M322 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM500 0h-92l-144 213h-111v-213h-78v550h236q84 0 132.5 -47.5t48.5 -120.5q0 -70 -41 -114t-107 -49z
M304 282q52 0 80 27.5t28 72.5q0 44 -28.5 71.5t-79.5 27.5h-151v-199h151z" />
    <glyph glyph-name="Sacute.smcp" unicode="&#xf698;" horiz-adv-x="504" 
d="M419 750l-169 -144h-57l148 144h78zM252 -12q-143 0 -220 86l42 59q31 -33 78 -55.5t101 -22.5q68 0 101.5 27t33.5 68q0 38 -35 60t-84.5 33.5t-99 25t-84.5 47.5t-35 89q0 69 57 113t142 44q129 0 206 -79l-41 -55q-62 66 -166 66q-51 0 -84.5 -24.5t-33.5 -62.5
q0 -28 25 -46t62.5 -26.5t81.5 -21.5t81.5 -28t62.5 -49t25 -83q0 -73 -55 -119t-161 -46z" />
    <glyph glyph-name="Scedilla.smcp" unicode="&#xf699;" horiz-adv-x="504" 
d="M249 -191q-63 0 -101 30l19 38q35 -29 80 -29q25 0 43 11.5t18 30.5q0 35 -37 35q-23 0 -38 -17l-33 19l23 62q-125 10 -191 85l42 59q31 -33 78 -55.5t101 -22.5q68 0 101.5 27t33.5 68q0 38 -35 60t-84.5 33.5t-99 25t-84.5 47.5t-35 89q0 69 57 113t142 44
q129 0 206 -79l-41 -55q-62 66 -166 66q-51 0 -84.5 -24.5t-33.5 -62.5q0 -28 25 -46t62.5 -26.5t81.5 -21.5t81.5 -28t62.5 -49t25 -83q0 -71 -51.5 -116t-149.5 -49l-17 -43q16 11 37 11q29 0 47.5 -18.5t18.5 -48.5q0 -36 -30 -58t-74 -22z" />
    <glyph glyph-name="Scircumflex.smcp" unicode="&#xf69a;" horiz-adv-x="504" 
d="M386 606h-51l-83 106l-80 -106h-51l95 144h72zM252 -12q-143 0 -220 86l42 59q31 -33 78 -55.5t101 -22.5q68 0 101.5 27t33.5 68q0 38 -35 60t-84.5 33.5t-99 25t-84.5 47.5t-35 89q0 69 57 113t142 44q129 0 206 -79l-41 -55q-62 66 -166 66q-51 0 -84.5 -24.5
t-33.5 -62.5q0 -28 25 -46t62.5 -26.5t81.5 -21.5t81.5 -28t62.5 -49t25 -83q0 -73 -55 -119t-161 -46z" />
    <glyph glyph-name="Scommaaccent.smcp" unicode="&#xf69b;" horiz-adv-x="504" 
d="M308 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM252 -12q-143 0 -220 86l42 59q31 -33 78 -55.5t101 -22.5q68 0 101.5 27t33.5 68q0 38 -35 60t-84.5 33.5
t-99 25t-84.5 47.5t-35 89q0 69 57 113t142 44q129 0 206 -79l-41 -55q-62 66 -166 66q-51 0 -84.5 -24.5t-33.5 -62.5q0 -28 25 -46t62.5 -26.5t81.5 -21.5t81.5 -28t62.5 -49t25 -83q0 -73 -55 -119t-161 -46z" />
    <glyph glyph-name="Tbar.smcp" unicode="&#xf69c;" horiz-adv-x="472" 
d="M274 0h-78v241h-116v48h116v192h-170v69h419v-69h-171v-192h118v-48h-118v-241z" />
    <glyph glyph-name="Tcaron.smcp" unicode="&#xf69d;" horiz-adv-x="472" 
d="M274 606h-72l-98 144h51l83 -106l80 106h51zM274 0h-78v481h-170v69h419v-69h-171v-481z" />
    <glyph glyph-name="Tcommaaccent.smcp" unicode="&#xf69e;" horiz-adv-x="472" 
d="M288 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM274 0h-78v481h-170v69h419v-69h-171v-481z" />
    <glyph glyph-name="Ubreve.smcp" unicode="&#xf69f;" horiz-adv-x="623" 
d="M311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-338q0 -104 -61 -164t-176 -60zM486 676q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="Uhungarumlaut.smcp" unicode="&#xf6a0;" horiz-adv-x="623" 
d="M311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-338q0 -104 -61 -164t-176 -60zM365 750l-115 -144h-48l94 144h69zM507 750l-115 -144h-48l94 144h69z" />
    <glyph glyph-name="Umacron.smcp" unicode="&#xf6a1;" horiz-adv-x="623" 
d="M311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-338q0 -104 -61 -164t-176 -60zM494 622h-363v48h363v-48z" />
    <glyph glyph-name="Uogonek.smcp" unicode="&#xf6a2;" horiz-adv-x="623" 
d="M311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-338q0 -81 -37 -136t-109 -76q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 48 39 87h-9z" />
    <glyph glyph-name="Uring.smcp" unicode="&#xf6a3;" horiz-adv-x="623" 
d="M311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-338q0 -104 -61 -164t-176 -60zM313 573q-41 0 -69.5 28.5t-28.5 69.5t29 70t69 29t69 -29t29 -70t-28.5 -69.5t-69.5 -28.5zM313 612q24 0 41 17.5t17 41.5
q0 25 -16.5 42t-41.5 17q-24 0 -41 -17t-17 -42q0 -24 17 -41.5t41 -17.5z" />
    <glyph glyph-name="Utilde.smcp" unicode="&#xf6a4;" horiz-adv-x="623" 
d="M311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-338q0 -104 -61 -164t-176 -60zM371 604q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37
q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="Wcircumflex.smcp" unicode="&#xf6a6;" horiz-adv-x="764" 
d="M514 606h-51l-83 106l-80 -106h-51l95 144h72zM588 0h-82l-125 431l-124 -431h-82l-165 550h87l123 -443l129 443h66l128 -444l124 444h86z" />
    <glyph glyph-name="Ycircumflex.smcp" unicode="&#xf6a9;" horiz-adv-x="547" 
d="M408 606h-51l-83 106l-80 -106h-51l95 144h72zM314 0h-78v233l-229 317h91l177 -250l174 250h91l-226 -317v-233z" />
    <glyph glyph-name="Zacute.smcp" unicode="&#xf6ab;" horiz-adv-x="516" 
d="M427 750l-169 -144h-57l148 144h78zM477 0h-438v59l326 422h-326v69h431v-59l-325 -422h332v-69z" />
    <glyph glyph-name="Zdotaccent.smcp" unicode="&#xf6ac;" horiz-adv-x="516" 
d="M477 0h-438v59l326 422h-326v69h431v-59l-325 -422h332v-69zM260 605q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="Idotaccent.smcp" unicode="&#xf6ad;" horiz-adv-x="229" 
d="M153 0h-78v550h78v-550zM114 605q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="parenleft.case" unicode="&#xf6ae;" horiz-adv-x="248" 
d="M230 -74l-45 -35q-141 193 -141 442q0 248 141 442l45 -34q-53 -105 -78.5 -198t-25.5 -210t25.5 -210.5t78.5 -196.5z" />
    <glyph glyph-name="parenright.case" unicode="&#xf6af;" horiz-adv-x="248" 
d="M62 -109l-45 35q53 103 79 196.5t26 210.5q0 118 -26 211t-79 197l45 34q142 -193 142 -442q0 -250 -142 -442z" />
    <glyph glyph-name="bracketleft.case" unicode="&#xf6b0;" horiz-adv-x="243" 
d="M226 -100h-186v868h186v-55h-128v-758h128v-55z" />
    <glyph glyph-name="bracketright.case" unicode="&#xf6b1;" horiz-adv-x="243" 
d="M203 -100h-186v55h128v758h-128v55h186v-868z" />
    <glyph glyph-name="braceleft.case" unicode="&#xf6b2;" horiz-adv-x="260" 
d="M243 -100h-61q-50 0 -87.5 37.5t-37.5 96.5v206q0 30 -13.5 49.5t-38.5 19.5v50q25 0 38.5 19.5t13.5 49.5v205q0 60 37.5 97.5t87.5 37.5h61v-55h-61q-27 0 -46.5 -22.5t-19.5 -57.5v-211q0 -68 -46 -88q46 -20 46 -88v-211q0 -34 19.5 -57t46.5 -23h61v-55z" />
    <glyph glyph-name="braceright.case" unicode="&#xf6b3;" horiz-adv-x="260" 
d="M17 -100v55h61q27 0 46.5 23t19.5 57v211q0 68 46 88q-46 20 -46 88v211q0 35 -19.5 57.5t-46.5 22.5h-61v55h61q50 0 87.5 -37.5t37.5 -97.5v-205q0 -30 13.5 -49.5t38.5 -19.5v-50q-25 0 -38.5 -19.5t-13.5 -49.5v-206q0 -59 -37.5 -96.5t-87.5 -37.5h-61z" />
    <glyph glyph-name="exclamdown.case" unicode="&#xf6b4;" horiz-adv-x="230" 
d="M69 0l15 480h62l16 -480h-93zM60 622q0 22 16.5 38.5t38.5 16.5t38.5 -16.5t16.5 -38.5t-16 -38.5t-39 -16.5t-39 16.5t-16 38.5z" />
    <glyph glyph-name="questiondown.case" unicode="&#xf6b5;" horiz-adv-x="462" 
d="M258 480q39 -35 39 -82q0 -35 -18.5 -63t-45 -47.5t-53 -37.5t-45 -42.5t-18.5 -53.5q0 -39 30.5 -65.5t86.5 -26.5q99 0 160 81l49 -53q-78 -102 -216 -102q-89 0 -142.5 44.5t-53.5 110.5q0 42 20 75.5t48 54.5t56.5 40t48.5 42t20 50q0 29 -26 48zM238 677
q22 0 38.5 -16.5t16.5 -39.5q0 -22 -16.5 -38t-38.5 -16t-38.5 16t-16.5 38q0 23 16.5 39.5t38.5 16.5z" />
    <glyph glyph-name="guillemotleft.case" unicode="&#xf6b6;" horiz-adv-x="439" 
d="M409 162h-75l-160 180l160 177h75l-160 -177zM265 162h-75l-160 180l160 177h75l-160 -177z" />
    <glyph glyph-name="guillemotright.case" unicode="&#xf6b7;" horiz-adv-x="439" 
d="M265 342l-160 -180h-75l160 180l-160 177h75zM409 342l-160 -180h-75l160 180l-160 177h75z" />
    <glyph glyph-name="guilsinglleft.case" unicode="&#xf6b8;" horiz-adv-x="295" 
d="M265 162h-75l-160 180l160 177h75l-160 -177z" />
    <glyph glyph-name="guilsinglright.case" unicode="&#xf6b9;" horiz-adv-x="295" 
d="M265 342l-160 -180h-75l160 180l-160 177h75z" />
    <glyph glyph-name="hyphen.case" unicode="&#xf6ba;" horiz-adv-x="300" 
d="M270 308h-240v66h240v-66z" />
    <glyph glyph-name="endash.case" unicode="&#xf6bb;" horiz-adv-x="593" 
d="M563 308h-533v66h533v-66z" />
    <glyph glyph-name="emdash.case" unicode="&#xf6bc;" horiz-adv-x="833" 
d="M803 308h-773v66h773v-66z" />
    <glyph glyph-name="periodcentered.case" unicode="&#xf6bd;" horiz-adv-x="230" 
d="M170 342q0 -22 -16.5 -38t-38.5 -16t-38.5 16t-16.5 38t16 38.5t39 16.5t39 -16.5t16 -38.5z" />
    <glyph glyph-name="bullet.case" unicode="&#xf6be;" horiz-adv-x="358" 
d="M288 341q0 -44 -32 -75.5t-76 -31.5t-76 31.5t-32 75.5t32 76t76 32t76 -32t32 -76z" />
    <glyph glyph-name="cent.case" unicode="&#xf6bf;" horiz-adv-x="495" 
d="M252 -4v91q-92 13 -148.5 82.5t-56.5 169.5q0 98 56.5 167.5t148.5 82.5v74h59v-72q94 -8 154 -83l-50 -46q-40 54 -104 62v-371q63 6 104 62l50 -46q-59 -77 -154 -83v-90h-59zM125 339q0 -70 34 -120t93 -63v364q-59 -13 -93 -62.5t-34 -118.5z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="230" 
d="M167 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49z" />
    <glyph glyph-name="one.tnum" unicode="&#xf6dc;" horiz-adv-x="598" 
d="M395 0h-83v557l-107 -113l-50 51l167 172h73v-667z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="1125" 
d="M867 -12q-102 0 -163 85v-257h-75v667h75v-72q27 38 70 61t93 23q96 0 155.5 -68.5t59.5 -184.5t-59.5 -185t-155.5 -69zM848 55q71 0 113 52.5t42 134.5t-42 134t-113 52q-43 0 -83.5 -22.5t-60.5 -54.5v-218q20 -33 60.5 -55.5t83.5 -22.5zM562 0h-98l-170 265h-133
v-265h-83v667h268q93 0 151 -55t58 -146q0 -86 -50 -137t-123 -57zM336 338q59 0 96 36t37 92t-37 91.5t-96 35.5h-175v-255h175z" />
    <glyph glyph-name="centinferior" unicode="&#xf6df;" horiz-adv-x="307" 
d="M157 -151v55q-58 7 -93.5 49t-35.5 101t36 101t93 49v44h42v-43q56 -6 94 -53l-38 -30q-21 31 -56 37v-212q33 6 56 38l38 -30q-38 -46 -94 -52v-54h-42zM84 54q0 -41 19.5 -69.5t53.5 -36.5v210q-34 -8 -53.5 -36t-19.5 -68z" />
    <glyph glyph-name="centsuperior" unicode="&#xf6e0;" horiz-adv-x="307" 
d="M157 416v55q-58 7 -93.5 49t-35.5 101t36 101t93 49v44h42v-43q56 -6 94 -53l-38 -30q-21 31 -56 37v-212q33 6 56 38l38 -30q-38 -46 -94 -52v-54h-42zM84 621q0 -41 19.5 -69.5t53.5 -36.5v210q-34 -8 -53.5 -36t-19.5 -68z" />
    <glyph glyph-name="commainferior" unicode="&#xf6e1;" horiz-adv-x="139" 
d="M105 -122q0 -65 -51 -101q-4 4 -12.5 11t-13.5 10q15 9 27 25t13 29q-2 -1 -3 -1q-14 0 -25 10.5t-11 25.5t10.5 26t26.5 11q17 0 28 -13t11 -33z" />
    <glyph glyph-name="commasuperior" unicode="&#xf6e2;" horiz-adv-x="139" 
d="M105 445q0 -65 -51 -101q-4 4 -12.5 11t-13.5 10q15 9 27 25t13 29q-2 -1 -3 -1q-14 0 -25 10.5t-11 25.5t10.5 26t26.5 11q17 0 28 -13t11 -33z" />
    <glyph glyph-name="dollarinferior" unicode="&#xf6e3;" horiz-adv-x="370" 
d="M169 -206v54q-93 6 -145 64l36 40q47 -49 109 -57v139q-31 7 -50 13.5t-41.5 19t-33.5 32.5t-11 49q0 46 38 77t98 34v56h42v-57q78 -9 121 -57l-34 -35q-34 37 -87 45v-126q30 -7 49.5 -14t41 -20.5t32.5 -34t11 -49.5q0 -51 -34.5 -82.5t-99.5 -36.5v-54h-42zM283 -39
q0 25 -18.5 39t-53.5 24v-128q35 4 53.5 22t18.5 43zM95 152q0 -23 18.5 -35t55.5 -22v118q-33 -3 -53.5 -20t-20.5 -41z" />
    <glyph glyph-name="dollarsuperior" unicode="&#xf6e4;" horiz-adv-x="370" 
d="M169 361v54q-93 6 -145 64l36 40q47 -49 109 -57v139q-31 7 -50 13.5t-41.5 19t-33.5 32.5t-11 49q0 46 38 77t98 34v56h42v-57q78 -9 121 -57l-34 -35q-34 37 -87 45v-126q30 -7 49.5 -14t41 -20.5t32.5 -34t11 -49.5q0 -51 -34.5 -82.5t-99.5 -36.5v-54h-42zM283 528
q0 26 -18.5 39.5t-53.5 23.5v-128q35 4 53.5 22t18.5 43zM95 719q0 -23 18.5 -35t55.5 -22v118q-33 -3 -53.5 -20t-20.5 -41z" />
    <glyph glyph-name="hypheninferior" unicode="&#xf6e5;" horiz-adv-x="183" 
d="M165 30h-147v46h147v-46z" />
    <glyph glyph-name="hyphensuperior" unicode="&#xf6e6;" horiz-adv-x="183" 
d="M165 597h-147v46h147v-46z" />
    <glyph glyph-name="periodinferior" unicode="&#xf6e7;" horiz-adv-x="142" 
d="M109 -113q0 -15 -11 -26t-26 -11q-16 0 -27.5 11t-11.5 26t11.5 26t27.5 11q15 0 26 -11t11 -26z" />
    <glyph glyph-name="periodsuperior" unicode="&#xf6e8;" horiz-adv-x="142" 
d="M109 454q0 -15 -11 -26t-26 -11q-16 0 -27.5 11t-11.5 26t11.5 26t27.5 11q15 0 26 -11t11 -26z" />
    <glyph glyph-name="asuperior" unicode="&#xf6e9;" horiz-adv-x="378" 
d="M317 326h-57v35q-40 -43 -107 -43q-44 0 -78 28t-34 76q0 49 33.5 76t78.5 27q67 0 107 -42v55q0 30 -23 47.5t-56 17.5q-59 0 -99 -46l-25 37q52 54 131 54q57 0 93 -27t36 -84v-211zM175 356q56 0 85 38v56q-29 38 -85 38q-33 0 -54.5 -18.5t-21.5 -47.5
q0 -30 21.5 -48t54.5 -18z" />
    <glyph glyph-name="bsuperior" unicode="&#xf6ea;" horiz-adv-x="409" 
d="M211 364q45 0 72.5 33t27.5 86q0 52 -27.5 85t-72.5 33q-28 0 -54 -13t-39 -32v-147q13 -19 39 -32t54 -13zM118 326h-58v434h58v-166q41 54 108 54q64 0 104 -45t40 -120q0 -76 -40 -120.5t-104 -44.5q-67 0 -108 53v-45z" />
    <glyph glyph-name="dsuperior" unicode="&#xf6eb;" horiz-adv-x="410" 
d="M349 326h-57v45q-41 -53 -108 -53q-64 0 -104 44.5t-40 120.5q0 75 40.5 120t103.5 45q66 0 107 -54v166h58v-434zM199 364q29 0 54.5 13t38.5 32v147q-13 19 -39 32t-54 13q-45 0 -72 -33t-27 -85q0 -53 27 -86t72 -33z" />
    <glyph glyph-name="esuperior" unicode="&#xf6ec;" horiz-adv-x="398" 
d="M207 318q-73 0 -120 46t-47 119q0 69 46 117t116 48q71 0 113.5 -48t42.5 -122v-14h-258q4 -44 34 -73t77 -29q59 0 99 38l26 -35q-50 -47 -129 -47zM303 506q-2 38 -28 68.5t-74 30.5q-45 0 -71.5 -30t-29.5 -69h203z" />
    <glyph glyph-name="isuperior" unicode="&#xf6ed;" horiz-adv-x="161" 
d="M81 685q-15 0 -26 11t-11 25q0 16 11 27t26 11t26 -11t11 -27q0 -14 -11 -25t-26 -11zM110 326h-58v314h58v-314z" />
    <glyph glyph-name="lsuperior" unicode="&#xf6ee;" horiz-adv-x="178" 
d="M118 326h-58v434h58v-434z" />
    <glyph glyph-name="msuperior" unicode="&#xf6ef;" horiz-adv-x="571" 
d="M510 326h-57v209q0 65 -57 65q-23 0 -46 -14t-36 -32v-228h-57v209q0 65 -59 65q-22 0 -44.5 -14t-35.5 -32v-228h-58v314h58v-45q12 18 42 35.5t63 17.5q69 0 87 -59q15 24 45 41.5t65 17.5q90 0 90 -100v-222z" />
    <glyph glyph-name="osuperior" unicode="&#xf6f0;" horiz-adv-x="405" 
d="M202 318q-73 0 -117.5 47.5t-44.5 117.5t44.5 117.5t117.5 47.5q74 0 118.5 -47.5t44.5 -117.5t-44.5 -117.5t-118.5 -47.5zM202 366q48 0 76 33t28 84q0 50 -28 82.5t-76 32.5q-47 0 -74.5 -32.5t-27.5 -82.5q0 -51 27.5 -84t74.5 -33z" />
    <glyph glyph-name="rsuperior" unicode="&#xf6f1;" horiz-adv-x="240" 
d="M118 326h-58v314h58v-48q48 55 106 55v-53h-20q-23 0 -49.5 -14t-36.5 -32v-222z" />
    <glyph glyph-name="ssuperior" unicode="&#xf6f2;" horiz-adv-x="330" 
d="M160 318q-80 0 -133 53l27 36q40 -47 107 -47q35 0 54.5 14t19.5 38q0 25 -31.5 38.5t-68.5 18.5t-68.5 26.5t-31.5 61.5q0 38 33 64.5t90 26.5q80 0 122 -46l-25 -35q-32 39 -95 39q-32 0 -50.5 -14t-18.5 -35q0 -19 20.5 -29t50 -15.5t58.5 -13t49.5 -28.5t20.5 -55
q0 -43 -34 -70.5t-96 -27.5z" />
    <glyph glyph-name="tsuperior" unicode="&#xf6f3;" horiz-adv-x="206" 
d="M139 318q-77 0 -77 78v201h-53v43h53v86h58v-86h65v-43h-65v-193q0 -39 31 -39q21 0 33 12q3 -8 9 -22t7 -16q-23 -21 -61 -21z" />
    <glyph glyph-name="Lslash.smcp" unicode="&#xf6f9;" horiz-adv-x="481" 
d="M10 171v72l88 51v256h78v-211l95 55v-72l-95 -55v-198h267v-69h-345v222z" />
    <glyph glyph-name="OE.smcp" unicode="&#xf6fa;" horiz-adv-x="953" 
d="M901 0h-382v99q-64 -111 -199 -111q-122 0 -197.5 81.5t-75.5 205.5t75.5 205.5t197.5 81.5q135 0 199 -111v99h382v-69h-304v-165h298v-69h-298v-178h304v-69zM519 194v164q-23 68 -73.5 101.5t-113.5 33.5q-93 0 -149 -61.5t-56 -156.5t56 -156.5t149 -61.5
q65 0 114.5 34t72.5 103z" />
    <glyph glyph-name="Scaron.smcp" unicode="&#xf6fd;" horiz-adv-x="504" 
d="M291 606h-72l-98 144h51l83 -106l80 106h51zM252 -12q-143 0 -220 86l42 59q31 -33 78 -55.5t101 -22.5q68 0 101.5 27t33.5 68q0 38 -35 60t-84.5 33.5t-99 25t-84.5 47.5t-35 89q0 69 57 113t142 44q129 0 206 -79l-41 -55q-62 66 -166 66q-51 0 -84.5 -24.5
t-33.5 -62.5q0 -28 25 -46t62.5 -26.5t81.5 -21.5t81.5 -28t62.5 -49t25 -83q0 -73 -55 -119t-161 -46z" />
    <glyph glyph-name="Zcaron.smcp" unicode="&#xf6ff;" horiz-adv-x="516" 
d="M294 606h-72l-98 144h51l83 -106l80 106h51zM477 0h-438v59l326 422h-326v69h431v-59l-325 -422h332v-69z" />
    <glyph glyph-name="exclam.smcp" unicode="&#xf721;" horiz-adv-x="230" 
d="M159 550l-14 -384h-59l-14 384h87zM170 45q0 -23 -16 -39t-39 -16t-39 16t-16 39q0 22 16.5 38t38.5 16t38.5 -16t16.5 -38z" />
    <glyph glyph-name="dollar.onum" unicode="&#xf724;" horiz-adv-x="504" 
d="M225 -74v63q-125 9 -193 85l42 59q60 -66 151 -77v197q-40 10 -65 19t-53.5 26t-42.5 44t-14 63q0 63 49 106t126 50v67h60v-68q103 -10 170 -77l-41 -55q-48 51 -129 63v-174q41 -10 67.5 -20.5t56 -28.5t44.5 -47t15 -68q0 -67 -46.5 -112t-136.5 -52v-63h-60zM388 150
q0 35 -26.5 54.5t-76.5 33.5v-181q52 6 77.5 32t25.5 61zM130 407q0 -29 24 -45.5t71 -29.5v160q-42 -5 -68.5 -28.5t-26.5 -56.5z" />
    <glyph glyph-name="ampersand.smcp" unicode="&#xf726;" horiz-adv-x="556" 
d="M536 0h-95q-4 3 -64 58q-74 -69 -168 -69q-81 0 -131.5 41.5t-50.5 114.5q0 59 33.5 96.5t92.5 67.5q-43 68 -43 122q0 56 42.5 93.5t104.5 37.5q58 0 96 -29t38 -82q0 -30 -11.5 -54t-36 -43.5t-44.5 -30.5t-55 -28q25 -31 66 -74q32 -36 68 -72q39 57 60 125l59 -24
q-36 -87 -77 -143q48 -46 116 -107zM216 44q63 0 119 54q-48 48 -78 81q-45 50 -73 86q-84 -48 -84 -115q0 -46 32.5 -76t83.5 -30zM179 430q0 -39 35 -92q56 26 84.5 49.5t28.5 60.5q0 29 -18.5 45t-48.5 16q-36 0 -58.5 -22.5t-22.5 -56.5z" />
    <glyph glyph-name="zero.onum" unicode="&#xf730;" horiz-adv-x="612" 
d="M561 275q0 -75 -25.5 -138t-85 -106t-144.5 -43t-144.5 43t-85 106t-25.5 138q0 56 15 106t45 91t80.5 65.5t114.5 24.5t114.5 -24.5t80.5 -65.5t45 -91t15 -106zM476 275q0 89 -43 151t-127 62t-127 -62t-43 -151t43 -151t127 -62t127 62t43 151z" />
    <glyph glyph-name="one.onum" unicode="&#xf731;" horiz-adv-x="339" 
d="M261 0h-83v440l-107 -113l-50 51l167 172h73v-550z" />
    <glyph glyph-name="two.onum" unicode="&#xf732;" horiz-adv-x="578" 
d="M491 0h-431v67q180 74 269 152t89 152q0 51 -39.5 84t-96.5 33q-58 0 -106.5 -22.5t-79.5 -61.5l-51 53q38 49 102.5 77t136.5 28q95 0 156.5 -49.5t61.5 -130.5q0 -88 -81 -170.5t-208 -137.5h278v-74z" />
    <glyph glyph-name="three.onum" unicode="&#xf733;" horiz-adv-x="553" 
d="M262 -128q-80 0 -142 31t-95 79l49 52q30 -40 79 -64t107 -24q73 0 114.5 33.5t41.5 91.5q0 60 -44.5 89t-121.5 29q-59 0 -69 -1v76q11 -1 69 -1q68 0 111.5 27.5t43.5 82.5q0 53 -42.5 83.5t-106.5 30.5q-101 0 -176 -81l-46 52q85 103 228 103q99 0 162.5 -47.5
t63.5 -130.5q0 -66 -44 -105.5t-100 -49.5q55 -5 104.5 -48t49.5 -116q0 -85 -64 -138.5t-172 -53.5z" />
    <glyph glyph-name="four.onum" unicode="&#xf734;" horiz-adv-x="558" 
d="M526 55h-94v-172h-83v172h-317v69l286 426h114v-422h94v-73zM349 128v345l-234 -345h234z" />
    <glyph glyph-name="five.onum" unicode="&#xf735;" horiz-adv-x="590" 
d="M301 -129q-154 0 -235 107l51 55q69 -88 184 -88q68 0 111 41t43 102q0 65 -42.5 104.5t-110.5 39.5q-90 0 -154 -63l-61 21v360h408v-74h-325v-232q60 60 156 60q88 0 150 -57.5t62 -155.5q0 -100 -67 -160t-170 -60z" />
    <glyph glyph-name="six.onum" unicode="&#xf736;" horiz-adv-x="591" 
d="M308 -12q-67 0 -118 28t-80.5 76.5t-44 109t-14.5 130.5q0 94 28 170t90.5 125.5t152.5 49.5q115 0 185 -80l-42 -62q-58 68 -143 68q-62 0 -105.5 -39t-63 -96.5t-19.5 -126.5q0 -17 1 -26q25 40 77.5 72t111.5 32q95 0 156.5 -55.5t61.5 -157.5q0 -91 -64 -154.5
t-170 -63.5zM305 62q72 0 112.5 43.5t40.5 97.5q0 70 -43 107t-111 37q-49 0 -94 -27.5t-73 -72.5q7 -74 48 -129.5t120 -55.5z" />
    <glyph glyph-name="seven.onum" unicode="&#xf737;" horiz-adv-x="515" 
d="M213 -117h-92l269 593h-359v74h455v-57z" />
    <glyph glyph-name="eight.onum" unicode="&#xf738;" horiz-adv-x="581" 
d="M291 -12q-102 0 -170.5 49t-68.5 132q0 63 45.5 109t113.5 66q-64 18 -106 58.5t-42 101.5q0 84 68 128.5t160 44.5t160.5 -44.5t68.5 -128.5q0 -61 -42 -101.5t-107 -58.5q68 -20 113.5 -66t45.5 -109q0 -82 -68.5 -131.5t-170.5 -49.5zM291 378q20 3 40.5 10t46.5 19.5
t42 35t16 50.5q0 50 -41.5 80t-103.5 30q-63 0 -104 -30t-41 -80q0 -28 16 -50.5t42 -35t46.5 -19t40.5 -10.5zM291 62q64 0 109.5 32t45.5 84q0 40 -31.5 70.5t-64 43t-59.5 15.5q-27 -3 -59.5 -15.5t-64.5 -43t-32 -70.5q0 -52 45.5 -84t110.5 -32z" />
    <glyph glyph-name="nine.onum" unicode="&#xf739;" horiz-adv-x="591" 
d="M269 -127q-115 0 -185 80l42 62q58 -68 143 -68q64 0 108 40t62 97t18 124q0 18 -1 27q-25 -40 -77 -72t-111 -32q-95 0 -157 55.5t-62 157.5q0 90 64 154t169 64q68 0 119 -28t80.5 -77t44 -109t14.5 -131q0 -94 -28 -169.5t-90.5 -125t-152.5 -49.5zM287 203
q49 0 94.5 27t71.5 72q-6 74 -47 130t-120 56q-71 0 -112 -44t-41 -97q0 -70 43 -107t111 -37z" />
    <glyph glyph-name="question.smcp" unicode="&#xf73f;" horiz-adv-x="396" 
d="M373 434q0 -42 -26.5 -74t-57.5 -49t-57.5 -39.5t-26.5 -46.5q0 -23 26 -40l-58 -24q-35 27 -35 65q0 28 16.5 50.5t39.5 38t46 30.5t39.5 34t16.5 42q0 33 -27 53.5t-75 20.5q-88 0 -144 -69l-40 47q68 88 190 88q78 0 125.5 -37t47.5 -90zM246 45q0 -23 -16 -39
t-39 -16t-39 16t-16 39t16 39t39 16t39 -16t16 -39z" />
    <glyph glyph-name="A.smcp" unicode="&#xf761;" 
d="M239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234z" />
    <glyph glyph-name="B.smcp" unicode="&#xf762;" horiz-adv-x="554" 
d="M340 0h-265v550h259q75 0 119 -39t44 -102q0 -47 -27 -80.5t-67 -44.5q45 -11 75.5 -49.5t30.5 -85.5q0 -68 -45 -108.5t-124 -40.5zM329 69q46 0 72.5 23.5t26.5 64.5q0 37 -26 63.5t-73 26.5h-176v-178h176zM324 316q44 0 68.5 23t24.5 59q0 38 -24.5 60.5t-68.5 22.5
h-171v-165h171z" />
    <glyph glyph-name="C.smcp" unicode="&#xf763;" horiz-adv-x="582" 
d="M340 -12q-126 0 -209.5 80.5t-83.5 206.5t83.5 206.5t209.5 80.5q139 0 219 -113l-63 -35q-25 36 -66.5 57.5t-89.5 21.5q-91 0 -152 -61.5t-61 -156.5q0 -94 61 -156t152 -62q48 0 90 21.5t66 57.5l64 -35q-83 -113 -220 -113z" />
    <glyph glyph-name="D.smcp" unicode="&#xf764;" horiz-adv-x="615" 
d="M275 0h-200v550h199q133 0 213.5 -78t80.5 -198q0 -119 -80 -196.5t-213 -77.5zM274 69q100 0 156.5 58t56.5 147q0 90 -56 148.5t-156 58.5h-122v-412h121z" />
    <glyph glyph-name="E.smcp" unicode="&#xf765;" horiz-adv-x="509" 
d="M457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69z" />
    <glyph glyph-name="F.smcp" unicode="&#xf766;" horiz-adv-x="491" 
d="M153 0h-78v550h382v-69h-304v-165h299v-69h-299v-247z" />
    <glyph glyph-name="G.smcp" unicode="&#xf767;" horiz-adv-x="626" 
d="M341 -12q-126 0 -210 80t-84 207t84 207t210 80q134 0 221 -101l-61 -41q-60 72 -160 72q-91 0 -152 -61t-61 -156q0 -94 61 -156.5t152 -62.5q87 0 147 58v107h-170v66h248v-203q-89 -96 -225 -96z" />
    <glyph glyph-name="H.smcp" unicode="&#xf768;" horiz-adv-x="635" 
d="M560 0h-78v248h-329v-248h-78v550h78v-233h329v233h78v-550z" />
    <glyph glyph-name="I.smcp" unicode="&#xf769;" horiz-adv-x="229" 
d="M153 0h-78v550h78v-550z" />
    <glyph glyph-name="J.smcp" unicode="&#xf76a;" horiz-adv-x="422" 
d="M157 -12q-93 0 -149 68l38 57q43 -56 107 -56q52 0 83.5 31t31.5 82v380h78v-386q0 -87 -52 -131.5t-137 -44.5z" />
    <glyph glyph-name="K.smcp" unicode="&#xf76b;" horiz-adv-x="534" 
d="M520 0h-97l-216 243l-54 -58v-185h-78v550h78v-275l249 275h97l-242 -261z" />
    <glyph glyph-name="L.smcp" unicode="&#xf76c;" horiz-adv-x="458" 
d="M420 0h-345v550h78v-481h267v-69z" />
    <glyph glyph-name="M.smcp" unicode="&#xf76d;" horiz-adv-x="719" 
d="M644 0h-79v432l-188 -432h-34l-190 432v-432h-78v550h107l177 -407l177 407h108v-550z" />
    <glyph glyph-name="N.smcp" unicode="&#xf76e;" horiz-adv-x="632" 
d="M557 0h-79l-325 428v-428h-78v550h82l321 -420v420h79v-550z" />
    <glyph glyph-name="O.smcp" unicode="&#xf76f;" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5z" />
    <glyph glyph-name="P.smcp" unicode="&#xf770;" horiz-adv-x="522" 
d="M153 0h-78v550h236q85 0 133 -48.5t48 -119.5q0 -72 -47 -120.5t-134 -48.5h-158v-213zM304 282q52 0 79.5 27.5t27.5 72.5q0 44 -28 71.5t-79 27.5h-151v-199h151z" />
    <glyph glyph-name="Q.smcp" unicode="&#xf771;" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205q0 -126 -80 -206l51 -57l-49 -41l-53 58q-68 -41 -153 -41zM331 57q58 0 104 26l-76 84l49 42l76 -84q51 61 51 150q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5
t56 -156.5t148 -61.5z" />
    <glyph glyph-name="R.smcp" unicode="&#xf772;" horiz-adv-x="545" 
d="M500 0h-92l-144 213h-111v-213h-78v550h236q84 0 132.5 -47.5t48.5 -120.5q0 -70 -41 -114t-107 -49zM304 282q52 0 80 27.5t28 72.5q0 44 -28.5 71.5t-79.5 27.5h-151v-199h151z" />
    <glyph glyph-name="S.smcp" unicode="&#xf773;" horiz-adv-x="504" 
d="M252 -12q-143 0 -220 86l42 59q31 -33 78 -55.5t101 -22.5q68 0 101.5 27t33.5 68q0 38 -35 60t-84.5 33.5t-99 25t-84.5 47.5t-35 89q0 69 57 113t142 44q129 0 206 -79l-41 -55q-62 66 -166 66q-51 0 -84.5 -24.5t-33.5 -62.5q0 -28 25 -46t62.5 -26.5t81.5 -21.5
t81.5 -28t62.5 -49t25 -83q0 -73 -55 -119t-161 -46z" />
    <glyph glyph-name="T.smcp" unicode="&#xf774;" horiz-adv-x="472" 
d="M274 0h-78v481h-170v69h419v-69h-171v-481z" />
    <glyph glyph-name="U.smcp" unicode="&#xf775;" horiz-adv-x="623" 
d="M311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-338q0 -104 -61 -164t-176 -60z" />
    <glyph glyph-name="V.smcp" unicode="&#xf776;" 
d="M333 0h-94l-232 550h89l190 -463l192 463h87z" />
    <glyph glyph-name="W.smcp" unicode="&#xf777;" horiz-adv-x="764" 
d="M588 0h-82l-125 431l-124 -431h-82l-165 550h87l123 -443l129 443h66l128 -444l124 444h86z" />
    <glyph glyph-name="X.smcp" unicode="&#xf778;" horiz-adv-x="567" 
d="M558 0h-94l-181 229l-181 -229h-93l224 282l-212 268h94l168 -215l168 215h94l-212 -267z" />
    <glyph glyph-name="Y.smcp" unicode="&#xf779;" horiz-adv-x="547" 
d="M314 0h-78v233l-229 317h91l177 -250l174 250h91l-226 -317v-233z" />
    <glyph glyph-name="Z.smcp" unicode="&#xf77a;" horiz-adv-x="516" 
d="M477 0h-438v59l326 422h-326v69h431v-59l-325 -422h332v-69z" />
    <glyph glyph-name="Agrave.smcp" unicode="&#xf7e0;" 
d="M239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM343 606h-57l-169 144h78z" />
    <glyph glyph-name="Aacute.smcp" unicode="&#xf7e1;" 
d="M455 750l-169 -144h-57l148 144h78zM239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234z" />
    <glyph glyph-name="Acircumflex.smcp" unicode="&#xf7e2;" 
d="M420 606h-51l-83 106l-80 -106h-51l95 144h72zM239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234z" />
    <glyph glyph-name="Atilde.smcp" unicode="&#xf7e3;" 
d="M239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM346 604q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5
q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="Adieresis.smcp" unicode="&#xf7e4;" 
d="M437 660q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM230 660q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 13.5t-13.5 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189
l-117 279l-117 -279h234z" />
    <glyph glyph-name="Aring.smcp" unicode="&#xf7e5;" 
d="M287 573q-41 0 -69.5 28.5t-28.5 69.5t29 70t69 29t69 -29t29 -70t-28.5 -69.5t-69.5 -28.5zM287 612q24 0 41 17.5t17 41.5q0 25 -16.5 42t-41.5 17q-24 0 -41 -17t-17 -42q0 -24 17 -41.5t41 -17.5zM239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189
l-117 279l-117 -279h234z" />
    <glyph glyph-name="AE.smcp" unicode="&#xf7e6;" horiz-adv-x="821" 
d="M770 0h-383v120h-216l-76 -120h-89l351 550h413v-69h-305v-165h298v-69h-298v-178h305v-69zM387 189v279l-178 -279h178z" />
    <glyph glyph-name="Ccedilla.smcp" unicode="&#xf7e7;" horiz-adv-x="582" 
d="M340 -191q-64 0 -101 30l19 38q35 -29 80 -29q25 0 43 11.5t18 30.5q0 35 -37 35q-23 0 -38 -17l-33 19l23 62q-116 8 -191.5 87.5t-75.5 198.5q0 126 83.5 206.5t209.5 80.5q139 0 219 -113l-63 -35q-25 36 -66.5 57.5t-89.5 21.5q-91 0 -152 -61.5t-61 -156.5
q0 -94 61 -156t152 -62q48 0 90 21.5t66 57.5l64 -35q-77 -105 -202 -112l-17 -44q16 11 37 11q29 0 47.5 -18.5t18.5 -48.5q0 -36 -30 -58t-74 -22z" />
    <glyph glyph-name="Egrave.smcp" unicode="&#xf7e8;" horiz-adv-x="509" 
d="M457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69zM323 606h-57l-169 144h78z" />
    <glyph glyph-name="Eacute.smcp" unicode="&#xf7e9;" horiz-adv-x="509" 
d="M435 750l-169 -144h-57l148 144h78zM457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69z" />
    <glyph glyph-name="Ecircumflex.smcp" unicode="&#xf7ea;" horiz-adv-x="509" 
d="M401 606h-51l-83 106l-80 -106h-51l95 144h72zM457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69z" />
    <glyph glyph-name="Edieresis.smcp" unicode="&#xf7eb;" horiz-adv-x="509" 
d="M416 654q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM209 654q0 -19 -13.5 -33t-32.5 -14t-33 13.5t-14 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69z" />
    <glyph glyph-name="Igrave.smcp" unicode="&#xf7ec;" horiz-adv-x="229" 
d="M153 0h-78v550h78v-550zM171 606h-57l-169 144h78z" />
    <glyph glyph-name="Iacute.smcp" unicode="&#xf7ed;" horiz-adv-x="229" 
d="M283 750l-169 -144h-57l148 144h78zM153 0h-78v550h78v-550z" />
    <glyph glyph-name="Icircumflex.smcp" unicode="&#xf7ee;" horiz-adv-x="229" 
d="M248 606h-51l-83 106l-80 -106h-51l95 144h72zM153 0h-78v550h78v-550z" />
    <glyph glyph-name="Idieresis.smcp" unicode="&#xf7ef;" horiz-adv-x="229" 
d="M265 654q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM58 654q0 -19 -13.5 -33t-32.5 -14q-20 0 -33.5 13.5t-13.5 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM153 0h-78v550h78v-550z" />
    <glyph glyph-name="Eth.smcp" unicode="&#xf7f0;" horiz-adv-x="626" 
d="M273 0h-200v251h-63v53h63v246h199q133 0 213.5 -78t80.5 -198q0 -119 -80 -196.5t-213 -77.5zM285 251h-134v-182h121q100 0 156.5 58t56.5 147q0 90 -56 148.5t-156 58.5h-122v-177h134v-53z" />
    <glyph glyph-name="Ntilde.smcp" unicode="&#xf7f1;" horiz-adv-x="632" 
d="M557 0h-79l-325 428v-428h-78v550h82l321 -420v420h79v-550zM375 604q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45
q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="Ograve.smcp" unicode="&#xf7f2;" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM388 606h-57l-169 144h78z" />
    <glyph glyph-name="Oacute.smcp" unicode="&#xf7f3;" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM500 750l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="Ocircumflex.smcp" unicode="&#xf7f4;" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM465 606h-51l-83 106l-80 -106h-51l95 144
h72z" />
    <glyph glyph-name="Otilde.smcp" unicode="&#xf7f5;" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM392 604q-25 0 -43.5 14.5t-27.5 31.5
t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="Odieresis.smcp" unicode="&#xf7f6;" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM482 654q0 -19 -14 -33t-33 -14t-33 14
t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM275 654q0 -19 -13.5 -33t-32.5 -14t-33 13.5t-14 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5z" />
    <glyph glyph-name="Oslash.smcp" unicode="&#xf7f8;" horiz-adv-x="662" 
d="M331 -12q-85 0 -151 39l-20 -27h-67l43 59q-89 81 -89 216q0 124 78.5 205.5t205.5 81.5q82 0 148 -38l19 26h67l-43 -58q93 -82 93 -217q0 -123 -79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -57 157l-255 -347q47 -28 108 -28zM127 275q0 -95 54 -155
l255 346q-48 27 -105 27q-92 0 -148 -61.5t-56 -156.5z" />
    <glyph glyph-name="Ugrave.smcp" unicode="&#xf7f9;" horiz-adv-x="623" 
d="M311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-338q0 -104 -61 -164t-176 -60zM368 606h-57l-169 144h78z" />
    <glyph glyph-name="Uacute.smcp" unicode="&#xf7fa;" horiz-adv-x="623" 
d="M480 750l-169 -144h-57l148 144h78zM311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-338q0 -104 -61 -164t-176 -60z" />
    <glyph glyph-name="Ucircumflex.smcp" unicode="&#xf7fb;" horiz-adv-x="623" 
d="M444 606h-51l-83 106l-80 -106h-51l95 144h72zM311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-338q0 -104 -61 -164t-176 -60z" />
    <glyph glyph-name="Udieresis.smcp" unicode="&#xf7fc;" horiz-adv-x="623" 
d="M462 654q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM255 654q0 -19 -13.5 -33t-32.5 -14t-33 13.5t-14 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5
q76 0 117 43t41 117v333h79v-338q0 -104 -61 -164t-176 -60z" />
    <glyph glyph-name="Yacute.smcp" unicode="&#xf7fd;" horiz-adv-x="547" 
d="M444 750l-169 -144h-57l148 144h78zM314 0h-78v233l-229 317h91l177 -250l174 250h91l-226 -317v-233z" />
    <glyph glyph-name="Thorn.smcp" unicode="&#xf7fe;" horiz-adv-x="522" 
d="M153 0h-78v550h78v-92h158q85 0 133 -49t48 -120q0 -72 -47 -120t-134 -48h-158v-121zM304 190q52 0 79.5 27t27.5 72t-27.5 72.5t-79.5 27.5h-151v-199h151z" />
    <glyph glyph-name="Ydieresis.smcp" unicode="&#xf7ff;" horiz-adv-x="547" 
d="M426 654q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM219 654q0 -19 -13.5 -33t-32.5 -14t-33 13.5t-14 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM314 0h-78v233l-229 317h91l177 -250l174 250h91l-226 -317v-233z" />
    <glyph glyph-name="uniF8FF" unicode="&#xf8ff;" horiz-adv-x="667" 
d="M226 154v74h-74q-31 0 -51.5 -21.5t-20.5 -52.5t21 -53t52 -22t52 22t21 53zM226 439v75q0 31 -20.5 52.5t-51.5 21.5t-52.5 -21.5t-21.5 -52.5t21.5 -53t52.5 -22h72zM265 265h137v137h-137v-137zM586 154q0 31 -20.5 52.5t-51.5 21.5h-73v-74q0 -31 21 -53t52 -22
q30 0 51 22t21 53zM586 514q0 31 -20.5 52.5t-51.5 21.5t-52 -21.5t-21 -52.5v-75h73q30 0 51 22t21 53zM625 154q0 -46 -32.5 -79t-78.5 -33t-79 33t-33 79v74h-137v-74q0 -46 -33 -79t-79 -33t-78.5 33t-32.5 79t32.5 78.5t78.5 32.5h73v137h-72q-46 0 -79 33t-33 79
t33 78.5t79 32.5t78.5 -32.5t32.5 -78.5v-75h137v75q0 46 33 78.5t79 32.5t78.5 -32.5t32.5 -78.5t-32.5 -79t-78.5 -33h-73v-137h73q46 0 78.5 -32.5t32.5 -78.5z" />
    <glyph glyph-name="zero.pzero" horiz-adv-x="612" 
d="M306 -12q-88 0 -149 55l-27 -43h-65l54 86q-68 95 -68 247q0 64 14.5 123t44 109.5t80 81t116.5 30.5q85 0 147 -54l28 44h65l-54 -87q69 -96 69 -247q0 -64 -14.5 -123t-44 -110t-80.5 -81.5t-116 -30.5zM306 62q60 0 100 41.5t55 99.5t15 130q0 102 -31 171l-246 -394
q42 -48 107 -48zM136 333q0 -101 30 -171l246 394q-42 47 -106 47q-60 0 -100 -41t-55 -99t-15 -130z" />
    <glyph glyph-name="zero.tonumzero" horiz-adv-x="598" 
d="M554 275q0 -75 -25.5 -138t-85 -106t-144.5 -43q-87 0 -150 45l-24 -33h-67l52 71q-66 79 -66 204q0 56 15 106t45 91t80.5 65.5t114.5 24.5q82 0 143 -41l22 29h66l-48 -65q72 -81 72 -210zM469 275q0 83 -37 142l-236 -320q41 -35 103 -35q84 0 127 62t43 151zM129 275
q0 -80 32 -135l234 317q-41 31 -96 31q-84 0 -127 -62t-43 -151z" />
    <glyph glyph-name="zero.onumzero" horiz-adv-x="612" 
d="M561 275q0 -75 -25.5 -138t-85 -106t-144.5 -43q-87 0 -150 45l-24 -33h-67l52 71q-66 79 -66 204q0 56 15 106t45 91t80.5 65.5t114.5 24.5q82 0 143 -41l22 29h66l-48 -65q72 -81 72 -210zM476 275q0 83 -37 142l-236 -320q41 -35 103 -35q84 0 127 62t43 151zM136 275
q0 -80 32 -135l234 317q-41 31 -96 31q-84 0 -127 -62t-43 -151z" />
    <glyph glyph-name="G.alt1" horiz-adv-x="730" 
d="M388 -13q-141 0 -239 97t-98 249q0 153 99 249t244 96q158 0 259 -125l-66 -41q-32 42 -83.5 67t-109.5 25q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -195t183.5 -77q97 0 154 59t70 149h-274v74h364q0 -160 -84.5 -258t-235.5 -98z" />
    <glyph glyph-name="a.alt1" horiz-adv-x="575" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z" />
    <glyph glyph-name="f.alt1" horiz-adv-x="283" 
d="M171 0h-75v417h-80v66h80v37q0 74 38 115.5t102 41.5q46 0 73 -17l-19 -57q-19 12 -43 12q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="268" 
d="M182 -12q-53 0 -80 29t-27 84v566h75v-550q0 -28 12.5 -45t36.5 -17q32 0 50 20l22 -56q-33 -31 -89 -31z" />
    <glyph glyph-name="y.alt1" horiz-adv-x="551" 
d="M255 -196q-111 0 -185 72l38 56q52 -66 147 -66q69 0 107.5 38.5t38.5 108.5v65q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v331h75v-308q0 -61 27.5 -86t80.5 -25q42 0 81.5 21t61.5 52v346h75v-470q0 -103 -62.5 -156t-158.5 -53z" />
    <glyph glyph-name="G.smcp.alt1" horiz-adv-x="642" 
d="M338 -12q-124 0 -207.5 80.5t-83.5 206.5q0 127 84 207t210 80q134 0 221 -101l-61 -41q-61 73 -160 73q-91 0 -152 -61.5t-61 -156.5q0 -94 61 -156.5t152 -62.5q71 0 122.5 44.5t61.5 120.5h-207v66h290q0 -136 -74 -217.5t-196 -81.5z" />
    <glyph glyph-name="Gbreve.alt1" horiz-adv-x="742" 
d="M572 793q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72zM388 -13q-141 0 -239 97t-98 249q0 153 99 249t244 96q158 0 259 -125l-66 -41q-32 42 -83.5 67t-109.5 25q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -195t183.5 -77q97 0 154 59t70 149h-274
v74h364q0 -160 -84.5 -258t-235.5 -98z" />
    <glyph glyph-name="Gcommaaccent.alt1" horiz-adv-x="742" 
d="M448 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM388 -13q-141 0 -239 97t-98 249q0 153 99 249t244 96q158 0 259 -125l-66 -41q-32 42 -83.5 67t-109.5 25
q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -195t183.5 -77q97 0 154 59t70 149h-274v74h364q0 -160 -84.5 -258t-235.5 -98z" />
    <glyph glyph-name="Gcircumflex.alt1" horiz-adv-x="742" 
d="M526 723h-51l-83 106l-80 -106h-51l95 144h72zM388 -13q-141 0 -239 97t-98 249q0 153 99 249t244 96q158 0 259 -125l-66 -41q-32 42 -83.5 67t-109.5 25q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -195t183.5 -77q97 0 154 59t70 149h-274v74h364q0 -160 -84.5 -258
t-235.5 -98z" />
    <glyph glyph-name="Gdotaccent.alt1" horiz-adv-x="742" 
d="M392 722q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5zM388 -13q-141 0 -239 97t-98 249q0 153 99 249t244 96q158 0 259 -125l-66 -41q-32 42 -83.5 67t-109.5 25q-110 0 -183.5 -76.5t-73.5 -194.5t73.5 -195t183.5 -77
q97 0 154 59t70 149h-274v74h364q0 -160 -84.5 -258t-235.5 -98z" />
    <glyph glyph-name="aacute.alt1" horiz-adv-x="575" 
d="M450 700l-169 -144h-57l148 144h78zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z
" />
    <glyph glyph-name="abreve.alt1" horiz-adv-x="575" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52zM469 626q-65 -88 -175 -88
q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="acircumflex.alt1" horiz-adv-x="575" 
d="M427 556h-51l-83 106l-80 -106h-51l95 144h72zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134
t113 -52z" />
    <glyph glyph-name="adieresis.alt1" horiz-adv-x="575" 
d="M444 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM237 604q0 -19 -13.5 -33t-32.5 -14t-33 13.5t-14 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5
q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z" />
    <glyph glyph-name="agrave.alt1" horiz-adv-x="575" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52zM338 556h-57l-169 144h78z" />
    <glyph glyph-name="amacron.alt1" horiz-adv-x="575" 
d="M476 572h-363v48h363v-48zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z" />
    <glyph glyph-name="aogonek.alt1" horiz-adv-x="575" 
d="M500 0q-76 -38 -76 -97q0 -50 44 -50q38 0 52 42l37 -21q-26 -60 -89 -60q-38 0 -63.5 22t-25.5 65q0 57 53 99h-7v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219
q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z" />
    <glyph glyph-name="aring.alt1" horiz-adv-x="575" 
d="M292 543q-40 0 -69 28.5t-29 69.5t29 70t69 29t69 -29t29 -70t-29 -69.5t-69 -28.5zM292 582q24 0 41 17.5t17 41.5q0 25 -16.5 42t-41.5 17q-24 0 -41 -17t-17 -42q0 -24 17 -41.5t41 -17.5zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5
q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z" />
    <glyph glyph-name="atilde.alt1" horiz-adv-x="575" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52zM353 554q-25 0 -43.5 14.5
t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EA1.alt1" horiz-adv-x="575" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52zM292 -184q-19 0 -33 14t-14 33t14 33
t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EA3.alt1" horiz-adv-x="575" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52zM255 586l-33 9q18 55 73 55
q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EA5.alt1" horiz-adv-x="575" 
d="M427 556h-51l-83 106l-80 -106h-51l95 144h72zM613 770l-169 -144h-57l148 144h78zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5
q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z" />
    <glyph glyph-name="uni1EAD.alt1" horiz-adv-x="575" 
d="M428 556h-51l-83 106l-80 -106h-51l95 144h72zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134
t113 -52zM295 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EA7.alt1" horiz-adv-x="575" 
d="M427 556h-51l-83 106l-80 -106h-51l95 144h72zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134
t113 -52zM198 626h-57l-169 144h78z" />
    <glyph glyph-name="uni1EA9.alt1" horiz-adv-x="575" 
d="M427 556h-51l-83 106l-80 -106h-51l95 144h72zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134
t113 -52zM343 726l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EAF.alt1" horiz-adv-x="575" 
d="M460 766l-169 -144h-57l148 144h78zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z
M467 609q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="uni1EAB.alt1" horiz-adv-x="575" 
d="M427 523h-51l-83 106l-80 -106h-51l95 144h72zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134
t113 -52zM349 682q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EB1.alt1" horiz-adv-x="575" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52zM344 622h-57l-169 144h78zM467 613
q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="uni1EB3.alt1" horiz-adv-x="575" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52zM251 681l-33 9q18 55 73 55
q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32zM467 613q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="uni1EB5.alt1" horiz-adv-x="575" 
d="M467 613q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72zM500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5
q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52zM349 658q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97
t-71.5 -37z" />
    <glyph glyph-name="uni1EB7.alt1" horiz-adv-x="575" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52zM295 -184q-19 0 -33 14t-14 33t14 33
t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5zM467 626q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="936" 
d="M280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52zM500 97v-97h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-98q57 110 175 110q99 0 157 -73.5
t58 -185.5v-19h-390q5 -71 51.5 -118t121.5 -47q91 0 150 61l35 -51q-71 -74 -174 -74q-128 0 -184 109zM817 272q-2 62 -43 111.5t-116 49.5q-72 0 -113.5 -49.5t-44.5 -111.5h317z" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="936" 
d="M280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52zM500 97v-97h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-98q57 110 175 110q99 0 157 -73.5
t58 -185.5v-19h-390q5 -71 51.5 -118t121.5 -47q91 0 150 61l35 -51q-71 -74 -174 -74q-128 0 -184 109zM817 272q-2 62 -43 111.5t-116 49.5q-72 0 -113.5 -49.5t-44.5 -111.5h317zM633 700l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="fl.alt1" horiz-adv-x="552" 
d="M171 0h-75v417h-80v66h80v37q0 74 38 115.5t102 41.5q46 0 73 -17l-19 -57q-19 12 -43 12q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417zM465 -12q-53 0 -80 29t-27 84v566h75v-550q0 -28 12.5 -45t36.5 -17q32 0 50 20l22 -56q-33 -31 -89 -31z" />
    <glyph glyph-name="f_f_l.alt1" horiz-adv-x="835" 
d="M454 0h-75v417h-80v66h80v37q0 74 38 115.5t102 41.5q46 0 73 -17l-19 -57q-19 12 -43 12q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417zM749 -12q-53 0 -80 29t-27 84v566h75v-550q0 -28 12.5 -45t36.5 -17q32 0 50 20l22 -56q-33 -31 -89 -31zM171 0h-75v417
h-80v66h80v37q0 74 37.5 115.5t102.5 41.5q62 0 102 -37l-31 -49q-27 24 -60 24q-37 0 -56.5 -24.5t-19.5 -70.5v-37h98v-66h-98v-417z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="268" 
d="M282 867l-169 -144h-57l148 144h78zM182 -12q-53 0 -80 29t-27 84v566h75v-550q0 -28 12.5 -45t36.5 -17q32 0 50 20l22 -56q-33 -31 -89 -31z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="268" 
d="M184 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM182 -12q-53 0 -80 29t-27 84v566h75v-550q0 -28 12.5 -45t36.5 -17q32 0 50 20l22 -56q-33 -31 -89 -31z
" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="293" 
d="M311 613q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM182 -12q-53 0 -80 29t-27 84v566h75v-550q0 -28 12.5 -45t36.5 -17q32 0 50 20l22 -56q-33 -31 -89 -31z
" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="307" 
d="M182 -12q-53 0 -80 29t-27 84v566h75v-550q0 -28 12.5 -45t36.5 -17q32 0 50 20l22 -56q-33 -31 -89 -31zM321 244q0 -22 -16.5 -38t-38.5 -16t-38.5 16t-16.5 38t16.5 38.5t38.5 16.5t38.5 -16.5t16.5 -38.5z" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="299" 
d="M10 228v60l95 54v325h75v-282l96 55v-60l-96 -55v-208q0 -28 12.5 -45t36.5 -17q32 0 50 20l22 -56q-33 -31 -89 -31q-53 0 -80 29t-27 84v181z" />
    <glyph glyph-name="yacute.alt1" horiz-adv-x="551" 
d="M444 700l-169 -144h-57l148 144h78zM255 -196q-111 0 -185 72l38 56q52 -66 147 -66q69 0 107.5 38.5t38.5 108.5v65q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v331h75v-308q0 -61 27.5 -86t80.5 -25q42 0 81.5 21t61.5 52v346h75v-470q0 -103 -62.5 -156t-158.5 -53
z" />
    <glyph glyph-name="ycircumflex.alt1" horiz-adv-x="551" 
d="M409 556h-51l-83 106l-80 -106h-51l95 144h72zM255 -196q-111 0 -185 72l38 56q52 -66 147 -66q69 0 107.5 38.5t38.5 108.5v65q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v331h75v-308q0 -61 27.5 -86t80.5 -25q42 0 81.5 21t61.5 52v346h75v-470q0 -103 -62.5 -156
t-158.5 -53z" />
    <glyph glyph-name="ydieresis.alt1" horiz-adv-x="551" 
d="M424 604q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM217 604q0 -19 -13.5 -33t-32.5 -14t-33 13.5t-14 33.5q0 19 14 32.5t33 13.5t32.5 -13.5t13.5 -32.5zM255 -196q-111 0 -185 72l38 56q52 -66 147 -66q69 0 107.5 38.5t38.5 108.5v65
q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v331h75v-308q0 -61 27.5 -86t80.5 -25q42 0 81.5 21t61.5 52v346h75v-470q0 -103 -62.5 -156t-158.5 -53z" />
    <glyph glyph-name="ygrave.alt1" horiz-adv-x="551" 
d="M255 -196q-111 0 -185 72l38 56q52 -66 147 -66q69 0 107.5 38.5t38.5 108.5v65q-30 -34 -75.5 -57t-96.5 -23q-154 0 -154 154v331h75v-308q0 -61 27.5 -86t80.5 -25q42 0 81.5 21t61.5 52v346h75v-470q0 -103 -62.5 -156t-158.5 -53zM332 556h-57l-169 144h78z" />
    <glyph glyph-name="uni1EA0.smcp" 
d="M239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM287 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EA2.smcp" 
d="M239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM247 639l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EA4.smcp" 
d="M420 606h-51l-83 106l-80 -106h-51l95 144h72zM606 820l-169 -144h-57l148 144h78zM239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234z" />
    <glyph glyph-name="uni1EAC.smcp" 
d="M419 606h-51l-83 106l-80 -106h-51l95 144h72zM239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM286 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EA6.smcp" 
d="M420 606h-51l-83 106l-80 -106h-51l95 144h72zM239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM191 676h-57l-169 144h78z" />
    <glyph glyph-name="uni1EA8.smcp" 
d="M420 606h-51l-83 106l-80 -106h-51l95 144h72zM239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM336 776l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EAE.smcp" 
d="M458 819l-169 -144h-57l148 144h78zM239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM462 664q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="uni1EAA.smcp" 
d="M420 573h-51l-83 106l-80 -106h-51l95 144h72zM239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM346 732q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37
q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EB0.smcp" 
d="M239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM343 675h-57l-169 144h78zM463 664q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="uni1EB2.smcp" 
d="M239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM248 732l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32zM463 664q-65 -88 -175 -88q-109 0 -176 88l37 32
q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="uni1EB4.smcp" 
d="M463 664q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72zM239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM346 708q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45
q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EB6.smcp" 
d="M239 550h94l232 -550h-87l-51 120h-282l-50 -120h-88zM403 189l-117 279l-117 -279h234zM286 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5zM462 676q-65 -88 -175 -88q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="uni1E02.smcp" horiz-adv-x="554" 
d="M340 0h-265v550h259q75 0 119 -39t44 -102q0 -47 -27 -80.5t-67 -44.5q45 -11 75.5 -49.5t30.5 -85.5q0 -68 -45 -108.5t-124 -40.5zM329 69q46 0 72.5 23.5t26.5 64.5q0 37 -26 63.5t-73 26.5h-176v-178h176zM324 316q44 0 68.5 23t24.5 59q0 38 -24.5 60.5t-68.5 22.5
h-171v-165h171zM284 605q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E0A.smcp" horiz-adv-x="615" 
d="M275 0h-200v550h199q133 0 213.5 -78t80.5 -198q0 -119 -80 -196.5t-213 -77.5zM274 69q100 0 156.5 58t56.5 147q0 90 -56 148.5t-156 58.5h-122v-412h121zM305 605q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EB8.smcp" horiz-adv-x="509" 
d="M457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69zM268 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EBA.smcp" horiz-adv-x="509" 
d="M457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69zM228 639l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EBC.smcp" horiz-adv-x="509" 
d="M457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69zM328 604q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45
q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EBE.smcp" horiz-adv-x="509" 
d="M399 606h-51l-83 106l-80 -106h-51l95 144h72zM585 820l-169 -144h-57l148 144h78zM457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69z" />
    <glyph glyph-name="uni1EC0.smcp" horiz-adv-x="509" 
d="M399 606h-51l-83 106l-80 -106h-51l95 144h72zM457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69zM170 676h-57l-169 144h78z" />
    <glyph glyph-name="uni1EC2.smcp" horiz-adv-x="509" 
d="M399 606h-51l-83 106l-80 -106h-51l95 144h72zM457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69zM318 776l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EC4.smcp" horiz-adv-x="509" 
d="M399 573h-51l-83 106l-80 -106h-51l95 144h72zM457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69zM325 732q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5
t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EC6.smcp" horiz-adv-x="509" 
d="M401 606h-51l-83 106l-80 -106h-51l95 144h72zM457 0h-382v550h382v-69h-304v-165h299v-69h-299v-178h304v-69zM268 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E1E.smcp" horiz-adv-x="491" 
d="M270 605q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5zM153 0h-78v550h382v-69h-304v-165h299v-69h-299v-247z" />
    <glyph glyph-name="Gbreve.smcp.alt1" horiz-adv-x="642" 
d="M338 -12q-124 0 -207.5 80.5t-83.5 206.5q0 127 84 207t210 80q134 0 221 -101l-61 -41q-61 73 -160 73q-91 0 -152 -61.5t-61 -156.5q0 -94 61 -156.5t152 -62.5q71 0 122.5 44.5t61.5 120.5h-207v66h290q0 -136 -74 -217.5t-196 -81.5zM517 676q-65 -88 -175 -88
q-109 0 -176 88l37 32q52 -72 139 -72q88 0 138 72z" />
    <glyph glyph-name="Gcommaaccent.smcp.alt1" horiz-adv-x="642" 
d="M393 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM338 -12q-124 0 -207.5 80.5t-83.5 206.5q0 127 84 207t210 80q134 0 221 -101l-61 -41q-61 73 -160 73
q-91 0 -152 -61.5t-61 -156.5q0 -94 61 -156.5t152 -62.5q71 0 122.5 44.5t61.5 120.5h-207v66h290q0 -136 -74 -217.5t-196 -81.5z" />
    <glyph glyph-name="Gcircumflex.smcp.alt1" horiz-adv-x="642" 
d="M476 606h-51l-83 106l-80 -106h-51l95 144h72zM338 -12q-124 0 -207.5 80.5t-83.5 206.5q0 127 84 207t210 80q134 0 221 -101l-61 -41q-61 73 -160 73q-91 0 -152 -61.5t-61 -156.5q0 -94 61 -156.5t152 -62.5q71 0 122.5 44.5t61.5 120.5h-207v66h290
q0 -136 -74 -217.5t-196 -81.5z" />
    <glyph glyph-name="Gdotaccent.smcp.alt1" horiz-adv-x="642" 
d="M338 -12q-124 0 -207.5 80.5t-83.5 206.5q0 127 84 207t210 80q134 0 221 -101l-61 -41q-61 73 -160 73q-91 0 -152 -61.5t-61 -156.5q0 -94 61 -156.5t152 -62.5q71 0 122.5 44.5t61.5 120.5h-207v66h290q0 -136 -74 -217.5t-196 -81.5zM343 605q-19 0 -33 14t-14 33
t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E22.smcp" horiz-adv-x="635" 
d="M560 0h-78v248h-329v-248h-78v550h78v-233h329v233h78v-550zM318 605q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EC8.smcp" horiz-adv-x="229" 
d="M153 0h-78v550h78v-550zM76 639l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1ECA.smcp" horiz-adv-x="229" 
d="M153 0h-78v550h78v-550zM116 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1ECC.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM332 -184q-19 0 -33 14t-14 33t14 33t33 14
t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1ECE.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM292 644l-33 9q18 55 73 55
q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1ED0.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM464 606h-51l-83 106l-80 -106h-51l95 144
h72zM651 820l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="uni1ED2.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM464 606h-51l-83 106l-80 -106h-51l95 144
h72zM235 676h-57l-169 144h78z" />
    <glyph glyph-name="uni1ED4.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM464 606h-51l-83 106l-80 -106h-51l95 144
h72zM381 776l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1ED6.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM465 573h-51l-83 106l-80 -106h-51l95 144
h72zM392 732q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1ED8.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q126 0 205 -82t79 -205t-79 -205t-205 -82zM331 57q91 0 147.5 62t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM465 606h-51l-83 106l-80 -106h-51l95 144
h72zM334 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="Ohorn.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q143 0 225 -106q35 30 40 71q-2 -2 -11 -2q-16 0 -27.5 12t-11.5 29q0 18 12.5 31t30.5 13q21 0 35.5 -17t14.5 -46q0 -73 -62 -123q38 -68 38 -149q0 -123 -79 -205t-205 -82zM331 57q91 0 147.5 62
t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5z" />
    <glyph glyph-name="uni1EDA.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q143 0 225 -106q35 30 40 71q-2 -2 -11 -2q-16 0 -27.5 12t-11.5 29q0 18 12.5 31t30.5 13q21 0 35.5 -17t14.5 -46q0 -73 -62 -123q38 -68 38 -149q0 -123 -79 -205t-205 -82zM331 57q91 0 147.5 62
t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM500 750l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="uni1EDC.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q143 0 225 -106q35 30 40 71q-2 -2 -11 -2q-16 0 -27.5 12t-11.5 29q0 18 12.5 31t30.5 13q21 0 35.5 -17t14.5 -46q0 -73 -62 -123q38 -68 38 -149q0 -123 -79 -205t-205 -82zM331 57q91 0 147.5 62
t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM388 606h-57l-169 144h78z" />
    <glyph glyph-name="uni1EDE.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q143 0 225 -106q35 30 40 71q-2 -2 -11 -2q-16 0 -27.5 12t-11.5 29q0 18 12.5 31t30.5 13q21 0 35.5 -17t14.5 -46q0 -73 -62 -123q38 -68 38 -149q0 -123 -79 -205t-205 -82zM331 57q91 0 147.5 62
t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM292 644l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EE0.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q143 0 225 -106q35 30 40 71q-2 -2 -11 -2q-16 0 -27.5 12t-11.5 29q0 18 12.5 31t30.5 13q21 0 35.5 -17t14.5 -46q0 -73 -62 -123q38 -68 38 -149q0 -123 -79 -205t-205 -82zM331 57q91 0 147.5 62
t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM392 604q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5
q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EE2.smcp" horiz-adv-x="662" 
d="M331 -12q-127 0 -205.5 81.5t-78.5 205.5t78.5 205.5t205.5 81.5q143 0 225 -106q35 30 40 71q-2 -2 -11 -2q-16 0 -27.5 12t-11.5 29q0 18 12.5 31t30.5 13q21 0 35.5 -17t14.5 -46q0 -73 -62 -123q38 -68 38 -149q0 -123 -79 -205t-205 -82zM331 57q91 0 147.5 62
t56.5 156q0 95 -56.5 156.5t-147.5 61.5q-92 0 -148 -61.5t-56 -156.5t56 -156.5t148 -61.5zM332 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E56.smcp" horiz-adv-x="522" 
d="M153 0h-78v550h236q85 0 133 -48.5t48 -119.5q0 -72 -47 -120.5t-134 -48.5h-158v-213zM304 282q52 0 79.5 27.5t27.5 72.5q0 44 -28 71.5t-79 27.5h-151v-199h151zM280 605q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1E60.smcp" horiz-adv-x="504" 
d="M252 -12q-143 0 -220 86l42 59q31 -33 78 -55.5t101 -22.5q68 0 101.5 27t33.5 68q0 38 -35 60t-84.5 33.5t-99 25t-84.5 47.5t-35 89q0 69 57 113t142 44q129 0 206 -79l-41 -55q-62 66 -166 66q-51 0 -84.5 -24.5t-33.5 -62.5q0 -28 25 -46t62.5 -26.5t81.5 -21.5
t81.5 -28t62.5 -49t25 -83q0 -73 -55 -119t-161 -46zM258 605q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni021A.smcp" horiz-adv-x="472" 
d="M288 -138q0 -39 -20 -75t-51 -59l-33 26q23 15 39.5 39.5t18.5 46.5q-2 -2 -11 -2q-18 0 -30 12.5t-12 31.5q0 20 13.5 33.5t32.5 13.5q22 0 37.5 -18t15.5 -49zM274 0h-78v481h-170v69h419v-69h-171v-481z" />
    <glyph glyph-name="uni1E6A.smcp" horiz-adv-x="472" 
d="M274 0h-78v481h-170v69h419v-69h-171v-481zM237 605q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="uni1EE4.smcp" horiz-adv-x="623" 
d="M311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-338q0 -104 -61 -164t-176 -60zM312 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5z" />
    <glyph glyph-name="Uhorn.smcp" horiz-adv-x="623" 
d="M311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-48q27 15 46 40t20 50q-7 -1 -10 -1q-17 0 -28.5 12t-11.5 29q0 18 12.5 31t30.5 13q21 0 35.5 -17t14.5 -46q0 -47 -29.5 -88.5t-79.5 -63.5v-249
q0 -104 -61 -164t-176 -60z" />
    <glyph glyph-name="uni1EE6.smcp" horiz-adv-x="623" 
d="M311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-338q0 -104 -61 -164t-176 -60zM271 639l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32z" />
    <glyph glyph-name="uni1EE8.smcp" horiz-adv-x="623" 
d="M480 750l-169 -144h-57l148 144h78zM311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-48q27 15 46 40t20 50q-7 -1 -10 -1q-17 0 -28.5 12t-11.5 29q0 18 12.5 31t30.5 13q21 0 35.5 -17t14.5 -46
q0 -47 -29.5 -88.5t-79.5 -63.5v-249q0 -104 -61 -164t-176 -60z" />
    <glyph glyph-name="uni1EEA.smcp" horiz-adv-x="623" 
d="M368 606h-57l-169 144h78zM311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-48q27 15 46 40t20 50q-7 -1 -10 -1q-17 0 -28.5 12t-11.5 29q0 18 12.5 31t30.5 13q21 0 35.5 -17t14.5 -46q0 -47 -29.5 -88.5
t-79.5 -63.5v-249q0 -104 -61 -164t-176 -60z" />
    <glyph glyph-name="uni1EEC.smcp" horiz-adv-x="623" 
d="M271 639l-33 9q18 55 73 55q31 0 51.5 -17.5t20.5 -51.5q0 -28 -19 -54h-40q23 23 23 52q0 39 -36 39q-29 0 -40 -32zM311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-48q27 15 46 40t20 50q-7 -1 -10 -1
q-17 0 -28.5 12t-11.5 29q0 18 12.5 31t30.5 13q21 0 35.5 -17t14.5 -46q0 -47 -29.5 -88.5t-79.5 -63.5v-249q0 -104 -61 -164t-176 -60z" />
    <glyph glyph-name="uni1EEE.smcp" horiz-adv-x="623" 
d="M311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-48q27 15 46 40t20 50q-7 -1 -10 -1q-17 0 -28.5 12t-11.5 29q0 18 12.5 31t30.5 13q21 0 35.5 -17t14.5 -46q0 -47 -29.5 -88.5t-79.5 -63.5v-249
q0 -104 -61 -164t-176 -60zM371 604q-25 0 -43.5 14.5t-27.5 31.5t-22.5 31.5t-29.5 14.5q-21 0 -34.5 -21.5t-13.5 -64.5h-45q0 60 25.5 97t71.5 37q24 0 42.5 -14.5t28 -31.5t23 -31.5t29.5 -14.5q21 0 34.5 21.5t13.5 64.5h45q0 -60 -25.5 -97t-71.5 -37z" />
    <glyph glyph-name="uni1EF0.smcp" horiz-adv-x="623" 
d="M311 -184q-19 0 -33 14t-14 33t14 33t33 14t32.5 -13.5t13.5 -33.5t-13.5 -33.5t-32.5 -13.5zM311 -12q-115 0 -175.5 60t-60.5 164v338h79v-333q0 -75 41 -117.5t116 -42.5q76 0 117 43t41 117v333h79v-48q27 15 46 40t20 50q-7 -1 -10 -1q-17 0 -28.5 12t-11.5 29
q0 18 12.5 31t30.5 13q21 0 35.5 -17t14.5 -46q0 -47 -29.5 -88.5t-79.5 -63.5v-249q0 -104 -61 -164t-176 -60z" />
    <glyph glyph-name="Wacute.smcp" horiz-adv-x="764" 
d="M588 0h-82l-125 431l-124 -431h-82l-165 550h87l123 -443l129 443h66l128 -444l124 444h86zM550 750l-169 -144h-57l148 144h78z" />
    <glyph glyph-name="Wdieresis.smcp" horiz-adv-x="764" 
d="M588 0h-82l-125 431l-124 -431h-82l-165 550h87l123 -443l129 443h66l128 -444l124 444h86zM532 654q0 -19 -14 -33t-33 -14t-33 14t-14 33t14 32.5t33 13.5t33 -13.5t14 -32.5zM325 654q0 -19 -13.5 -33t-32.5 -14t-33 13.5t-14 33.5q0 19 14 32.5t33 13.5t32.5 -13.5
t13.5 -32.5z" />
    <glyph glyph-name="Wgrave.smcp" horiz-adv-x="764" 
d="M588 0h-82l-125 431l-124 -431h-82l-165 550h87l123 -443l129 443h66l128 -444l124 444h86zM438 606h-57l-169 144h78z" />
    <glyph glyph-name="Ygrave.smcp" horiz-adv-x="547" 
d="M314 0h-78v233l-229 317h91l177 -250l174 250h91l-226 -317v-233zM332 606h-57l-169 144h78z" />
    <glyph glyph-name="afii10065.alt1" horiz-adv-x="575" 
d="M500 0h-75v72q-62 -84 -163 -84q-96 0 -155.5 68.5t-59.5 184.5q0 115 59.5 184.5t155.5 69.5q101 0 162 -85v73h76v-483zM280 55q44 0 84 22t61 54v219q-21 33 -61.5 55.5t-83.5 22.5q-71 0 -113 -52.5t-42 -134.5t42 -134t113 -52z" />
    <hkern u1="&#x2215;" u2="&#xf659;" k="50" />
    <hkern u1="&#x2215;" u2="&#xf656;" k="-22" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="dagger"
	k="97" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V.smcp"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="17" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="daggerdbl"
	k="37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="question"
	k="77" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="97" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="J,Jcircumflex,afii10057"
	k="-9" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="asterisk"
	k="101" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="26" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="67" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="V"
	k="54" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="63" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="9" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="11" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="17" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="v,gamma,nu"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="bullet.case"
	k="37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,uni0394,Lambda,afii10051,afii10058,afii10059,afii10060,afii10017,afii10044,afii10046,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="47" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="dagger"
	k="77" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V.smcp"
	k="30" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="17" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="daggerdbl"
	k="20" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question"
	k="43" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="80" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="43" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="20" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="asterisk"
	k="64" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="9" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="V"
	k="54" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="69" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="10" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="40" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="question.smcp"
	k="43" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="bullet"
	k="10" />
    <hkern g1="lambda,afii10092,afii10094,afii10106,afii10107,Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-6" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="17" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="V"
	k="13" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="30" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="Jcircumflex.smcp,J.smcp"
	k="17" />
    <hkern g1="B,afii10018,afii10019,afii10025,uni1E02"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="question"
	k="43" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="56" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="V"
	k="20" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="afii10067,afii10073,B.smcp,uni1E02.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="23" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="question"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="V"
	k="2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="16" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="bullet.case"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,zeta,afii10053,afii10035"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="3" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="question"
	k="9" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="6" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="3" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="75" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="13" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V"
	k="9" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="39" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="3" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="X.smcp"
	k="3" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="question"
	k="16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="J,Jcircumflex,afii10057"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="27" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="V"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="40" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Jcircumflex.smcp,J.smcp"
	k="16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="37" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="17" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="27" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,Phi,theta,afii10032,afii10038,afii10047,afii10048,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V.smcp"
	k="20" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="question"
	k="32" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="27" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="81" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="V"
	k="50" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="76" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="13" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="17" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="17" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X.smcp"
	k="23" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="21" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp,uni1ECC.smcp,uni1ECE.smcp,uni1ED0.smcp,uni1ED2.smcp,uni1ED4.smcp,uni1ED6.smcp,uni1ED8.smcp,Ohorn.smcp,uni1EDA.smcp,uni1EDC.smcp,uni1EDE.smcp,uni1EE0.smcp,uni1EE2.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="54" />
    <hkern g1="Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,OE.smcp,E.smcp,AE.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="14" />
    <hkern g1="F,uni1E1E"
	g2="J,Jcircumflex,afii10057"
	k="59" />
    <hkern g1="F,uni1E1E"
	g2="Jcircumflex.smcp,J.smcp"
	k="56" />
    <hkern g1="F,uni1E1E"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="39" />
    <hkern g1="F,uni1E1E"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="36" />
    <hkern g1="F,uni1E1E"
	g2="X.smcp"
	k="14" />
    <hkern g1="F,uni1E1E"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="F,uni1E1E"
	g2="ampersand"
	k="6" />
    <hkern g1="F,uni1E1E"
	g2="ampersand.smcp"
	k="23" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="J,Jcircumflex,afii10057"
	k="36" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="14" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="46" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="23" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="23" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="13" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="23" />
    <hkern g1="F.smcp,uni1E1E.smcp"
	g2="ampersand.smcp"
	k="3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="question"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="V"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="9" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="3" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="question"
	k="19" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="3" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="44" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="14" />
    <hkern g1="Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="3" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="question"
	k="3" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="63" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="V"
	k="20" />
    <hkern g1="Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp,uni1E22.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="33" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="V.smcp"
	k="43" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="57" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="53" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="54" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="21" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="26" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="39" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="bullet.case"
	k="74" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="70" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="X.smcp"
	k="16" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="ampersand.smcp"
	k="3" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="20" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="17" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="34" />
    <hkern g1="K,Kcommaaccent,Kappa,afii10061,afii10024,afii10028"
	g2="x,afii10072,afii10087"
	k="26" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="43" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="17" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="35" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="17" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="V"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="16" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="39" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="bullet"
	k="47" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="37" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="dagger"
	k="130" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V.smcp"
	k="53" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="41" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="daggerdbl"
	k="101" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="question"
	k="114" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="122" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="77" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="47" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="34" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="asterisk"
	k="174" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="27" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="100" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="66" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="123" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="24" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="86" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="14" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="33" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="37" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="v,gamma,nu"
	k="57" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="57" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="bullet.case"
	k="118" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="77" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="ampersand"
	k="7" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="14" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="21" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V.smcp"
	k="74" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="21" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="110" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="87" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="60" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="34" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="asterisk"
	k="117" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="107" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="46" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V"
	k="97" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="116" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="100" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="J,Jcircumflex,afii10057"
	k="109" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="3" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="Jcircumflex.smcp,J.smcp"
	k="96" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="77" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="94" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="9" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand"
	k="26" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="ampersand.smcp"
	k="36" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="hyphen,periodcentered,endash,emdash"
	k="34" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="20" />
    <hkern g1="P,Rho,afii10034,uni1E56"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="13" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="77" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="47" />
    <hkern g1="P.smcp,uni1E56.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="58" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="17" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="3" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="V"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="16" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="3" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="bullet.case"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand"
	k="3" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand.smcp"
	k="13" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="V.smcp"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="17" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="v,gamma,nu"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,uni1E60"
	g2="x,afii10072,afii10087"
	k="13" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="69" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	g2="V"
	k="16" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="V.smcp"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="81" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="28" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="27" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="J,Jcircumflex,afii10057"
	k="83" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="3" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="56" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="31" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="56" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="v,gamma,nu"
	k="56" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="56" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="bullet.case"
	k="82" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Jcircumflex.smcp,J.smcp"
	k="107" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="100" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="67" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="X.smcp"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="93" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand"
	k="57" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="ampersand.smcp"
	k="97" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="65" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="95" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="115" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="x,afii10072,afii10087"
	k="66" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="91" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="59" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="42" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Gamma,Tau,afii10052,afii10020,afii10036,afii10050,uni1E6A"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="85" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="30" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="28" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="V"
	k="3" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="73" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="43" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="37" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand"
	k="44" />
    <hkern g1="tau,afii10068,afii10084,afii10100,afii10098,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	g2="ampersand.smcp"
	k="47" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="J,Jcircumflex,afii10057"
	k="12" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="Jcircumflex.smcp,J.smcp"
	k="12" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="9" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="26" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,afii10057,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="56" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="V"
	k="20" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="10" />
    <hkern g1="psi,IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="14" />
    <hkern g1="V,afii10062,afii10037"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="50" />
    <hkern g1="V,afii10062,afii10037"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="3" />
    <hkern g1="V,afii10062,afii10037"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="J,Jcircumflex,afii10057"
	k="93" />
    <hkern g1="V,afii10062,afii10037"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="23" />
    <hkern g1="V,afii10062,afii10037"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="42" />
    <hkern g1="V,afii10062,afii10037"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="13" />
    <hkern g1="V,afii10062,afii10037"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="V,afii10062,afii10037"
	g2="v,gamma,nu"
	k="13" />
    <hkern g1="V,afii10062,afii10037"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="6" />
    <hkern g1="V,afii10062,afii10037"
	g2="bullet.case"
	k="47" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="30" />
    <hkern g1="V,afii10062,afii10037"
	g2="Jcircumflex.smcp,J.smcp"
	k="97" />
    <hkern g1="V,afii10062,afii10037"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="54" />
    <hkern g1="V,afii10062,afii10037"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="54" />
    <hkern g1="V,afii10062,afii10037"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand"
	k="29" />
    <hkern g1="V,afii10062,afii10037"
	g2="ampersand.smcp"
	k="53" />
    <hkern g1="V,afii10062,afii10037"
	g2="hyphen,periodcentered,endash,emdash"
	k="53" />
    <hkern g1="V,afii10062,afii10037"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="23" />
    <hkern g1="V,afii10062,afii10037"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="63" />
    <hkern g1="V,afii10062,afii10037"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="60" />
    <hkern g1="V,afii10062,afii10037"
	g2="x,afii10072,afii10087"
	k="23" />
    <hkern g1="V,afii10062,afii10037"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="39" />
    <hkern g1="V,afii10062,afii10037"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="20" />
    <hkern g1="V,afii10062,afii10037"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="42" />
    <hkern g1="V.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="V.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="35" />
    <hkern g1="V.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="73" />
    <hkern g1="V.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="30" />
    <hkern g1="V.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="V.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="42" />
    <hkern g1="V.smcp"
	g2="ampersand"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex,afii10057"
	k="38" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="13" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="13" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v,gamma,nu"
	k="3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Jcircumflex.smcp,J.smcp"
	k="46" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="47" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="67" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="33" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x,afii10072,afii10087"
	k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="19" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="30" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="27" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="35" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="39" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="20" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="47" />
    <hkern g1="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	g2="ampersand"
	k="13" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="76" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="J,Jcircumflex,afii10057"
	k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="u,mu,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni03BC,upsilon,upsilontonos,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1,ygrave.alt1"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="26" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="v,gamma,nu"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="bullet.case"
	k="87" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="97" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Jcircumflex.smcp,J.smcp"
	k="116" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="69" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="ampersand"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="116" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp,uni1E60.smcp"
	k="39" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="121" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="127" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="x,afii10072,afii10087"
	k="73" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="colon,semicolon"
	k="59" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp,uni1E02.smcp,uni1E0A.smcp,uni1EB8.smcp,uni1EBA.smcp,uni1EBC.smcp,uni1EBE.smcp,uni1EC0.smcp,uni1EC2.smcp,uni1EC4.smcp,uni1EC6.smcp,uni1E1E.smcp,uni1E22.smcp,uni1E56.smcp"
	k="33" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	g2="m,n,p,r,z,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,zacute,zdotaccent,zcaron,iota,kappa,afii10067,afii10074,afii10076,afii10078,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10097,afii10100,afii10107,afii10193,afii10098"
	k="90" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="30" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="35" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="97" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="40" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="50" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="70" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand"
	k="43" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	g2="ampersand.smcp"
	k="53" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="17" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="bullet.case"
	k="58" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="64" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="42" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="question"
	k="36" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="6" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="108" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="43" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="V"
	k="66" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,afii10065,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7,f_h"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="110" />
    <hkern g1="ampersand"
	g2="V.smcp"
	k="40" />
    <hkern g1="ampersand"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="64" />
    <hkern g1="ampersand"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="10" />
    <hkern g1="ampersand"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="74" />
    <hkern g1="ampersand"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="48" />
    <hkern g1="ampersand"
	g2="V"
	k="50" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="91" />
    <hkern g1="ampersand"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="64" />
    <hkern g1="ampersand.smcp"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="60" />
    <hkern g1="ampersand.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="101" />
    <hkern g1="ampersand.smcp"
	g2="V"
	k="74" />
    <hkern g1="ampersand.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="64" />
    <hkern g1="asterisk"
	g2="J,Jcircumflex,afii10057"
	k="116" />
    <hkern g1="asterisk"
	g2="Jcircumflex.smcp,J.smcp"
	k="103" />
    <hkern g1="asterisk"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="64" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="101" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="question"
	k="46" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="115" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="V"
	k="60" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="124" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="7" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,ohorn,oslashacute,omicron,rho,sigma,upsilon,phi,omega,omicrontonos,upsilontonos,omegatonos,afii10080,afii10082,afii10086,afii10095,afii10096,uni1E03,uni1E57,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,f_b"
	g2="x,afii10072,afii10087"
	k="37" />
    <hkern g1="bullet"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="10" />
    <hkern g1="bullet.case"
	g2="J,Jcircumflex,afii10057"
	k="80" />
    <hkern g1="bullet.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="82" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="bullet.case"
	g2="V"
	k="47" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="87" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="37" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="58" />
    <hkern g1="bullet.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="60" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="question"
	k="23" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="3" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="81" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="23" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="V"
	k="36" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,epsilontonos,epsilon,sigma1,afii10083,afii10101,cent.onum"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="63" />
    <hkern g1="colon,semicolon"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="59" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="question"
	k="36" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="3" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="asterisk"
	k="17" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="101" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="V"
	k="53" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="110" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,afii10070,afii10071,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,ae.alt1,aeacute.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="7" />
    <hkern g1="eth"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="7" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="question"
	k="-57" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="-67" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="asterisk"
	k="-67" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,afii10054,afii10025,uni1E60"
	k="-23" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-63" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="V"
	k="-63" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="-56" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="43" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand"
	k="3" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="ampersand.smcp"
	k="3" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="exclam,B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Imacron,Iogonek,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Racute,Rcommaaccent,Rcaron"
	k="-40" />
    <hkern g1="f,longs,uni1E1F,f_f,f.alt1"
	g2="parenright,bracketright,braceright,parenright.case,bracketright.case,braceright.case"
	k="-74" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="question"
	k="23" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="85" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="V"
	k="42" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,jcircumflex,eng,afii10105,y.alt1,yacute.alt1,ycircumflex.alt1,ydieresis.alt1"
	g2="j,jcircumflex,afii10105"
	k="-46" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="J,Jcircumflex,afii10057"
	k="67" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="30" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="97" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="47" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="64" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="77" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="33" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="53" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="109" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="43" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="69" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="23" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="V"
	k="23" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="66" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="bullet"
	k="27" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="27" />
    <hkern g1="k,kcommaaccent,kgreenlandic,kappa,afii10072,afii10076,afii10109,f_k"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="3" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V.smcp"
	k="42" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="21" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="76" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="tau,afii10084,afii10092,Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp,uni021A.smcp,uni1E6A.smcp"
	k="60" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Wcircumflex.smcp,W.smcp,Wacute.smcp,Wdieresis.smcp,Wgrave.smcp"
	k="47" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,Psi,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="30" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="93" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="67" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V"
	k="87" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="psi,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp,uni1EE4.smcp,Uhorn.smcp,uni1EE6.smcp,uni1EE8.smcp,uni1EEA.smcp,uni1EEC.smcp,uni1EEE.smcp,uni1EF0.smcp"
	k="14" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="23" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp,Ygrave.smcp"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="23" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="44" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="v,gamma,nu"
	k="67" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="57" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="7" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="j,jcircumflex,afii10105"
	k="-40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="seven"
	k="66" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one"
	k="78" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="zero,six"
	k="31" />
    <hkern g1="questiondown"
	g2="j,jcircumflex,afii10105"
	k="-160" />
    <hkern g1="questiondown.case"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="104" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="63" />
    <hkern g1="questiondown.case"
	g2="V"
	k="73" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="93" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="3" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="J,Jcircumflex,afii10057"
	k="92" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="lambda,afii10069,afii10077,afii10106,Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp,uni1EA0.smcp,uni1EA2.smcp,uni1EA4.smcp,uni1EAC.smcp,uni1EA6.smcp,uni1EA8.smcp,uni1EAE.smcp,uni1EAA.smcp,uni1EB0.smcp,uni1EB2.smcp,uni1EB4.smcp,uni1EB6.smcp"
	k="80" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="104" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="76" />
    <hkern g1="quotedbl,quotesingle,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,epsilontonos,epsilon,afii10073,afii10095,afii10102,uni1E61"
	k="30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="48" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="V"
	k="23" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="50" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="83" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="9" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="question"
	k="43" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="quotedbl,quotesingle,registered,uni02BC,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="88" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="39" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="V"
	k="43" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="93" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,afii10102,uni1E61"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="3" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="52" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="13" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="V"
	k="26" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="33" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="question"
	k="23" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="85" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="V"
	k="42" />
    <hkern g1="u,z,mu,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek,zacute,zdotaccent,zcaron,uhorn,alphatonos,alpha,iota,uni03BC,afii10069,afii10074,afii10077,afii10078,afii10079,afii10081,afii10088,afii10089,afii10090,afii10091,afii10093,afii10097,afii10193,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="90" />
    <hkern g1="v,gamma,nu,chi"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="56" />
    <hkern g1="v,gamma,nu,chi"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="v,gamma,nu,chi"
	g2="V"
	k="13" />
    <hkern g1="v,gamma,nu,chi"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="v,gamma,nu,chi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="v,gamma,nu,chi"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="67" />
    <hkern g1="v,gamma,nu,chi"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="37" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="56" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="6" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="17" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="44" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="44" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="question"
	k="3" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="56" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="V"
	k="6" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alphatonos,Alpha,uni0394,Lambda,afii10058,afii10017,afii10021,afii10029,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6,Delta"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="57" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,afii10085,afii10110,ygrave"
	g2="X,Chi,afii10062,afii10024,afii10037,afii10039"
	k="37" />
    <hkern g1="X,Chi,afii10039"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="54" />
    <hkern g1="X,Chi,afii10039"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron,Phi,theta,afii10053,afii10032,afii10035,afii10038,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="f,longs,uni1E1F,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="t,tcommaaccent,tcaron,tbar,uni021B,uni1E6B"
	k="30" />
    <hkern g1="X,Chi,afii10039"
	g2="w,wcircumflex,afii10089,wgrave,wacute,wdieresis"
	k="44" />
    <hkern g1="X,Chi,afii10039"
	g2="v,gamma,nu"
	k="37" />
    <hkern g1="X,Chi,afii10039"
	g2="y,yacute,ydieresis,ycircumflex,chi,afii10085,afii10110"
	k="37" />
    <hkern g1="X,Chi,afii10039"
	g2="bullet.case"
	k="60" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="77" />
    <hkern g1="X,Chi,afii10039"
	g2="hyphen,periodcentered,endash,emdash"
	k="43" />
    <hkern g1="X.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="23" />
    <hkern g1="X.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="35" />
    <hkern g1="X.smcp"
	g2="ampersand"
	k="20" />
    <hkern g1="zero,nine"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="31" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="97" />
    <hkern g1="x,afii10087"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau,afii10051,afii10060,afii10036,afii10041,afii10044,uni1E6A"
	k="66" />
    <hkern g1="x,afii10087"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="23" />
    <hkern g1="x,afii10087"
	g2="V"
	k="23" />
    <hkern g1="x,afii10087"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Upsilontonos,Upsilon,Upsilondieresis,Ygrave"
	k="73" />
    <hkern g1="x,afii10087"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,alphatonos,alpha,omicron,sigma1,sigma,phi,omega,omicrontonos,omegatonos,afii10066,afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3,cent.onum,a.alt1,aacute.alt1,abreve.alt1,acircumflex.alt1,adieresis.alt1,agrave.alt1,amacron.alt1,aogonek.alt1,aring.alt1,atilde.alt1,ae.alt1,aeacute.alt1"
	k="37" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.case,bracketleft.case,braceleft.case"
	g2="j,jcircumflex,afii10105"
	k="-110" />
    <hkern g1="one.numr"
	g2="fraction"
	k="-35" />
    <hkern g1="seven.numr"
	g2="fraction"
	k="50" />
    <hkern g1="fraction"
	g2="one.dnom"
	k="-22" />
    <hkern g1="fraction"
	g2="four.dnom"
	k="50" />
  </font>
</defs></svg>
