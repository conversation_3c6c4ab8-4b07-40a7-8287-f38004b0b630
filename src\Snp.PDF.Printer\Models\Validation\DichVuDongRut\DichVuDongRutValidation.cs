﻿using FluentValidation;
using SNP.PDF.Printer.Resources;

namespace SNP.PDF.Printer.Models.Validation
{
    public class DichVuDongRutValidation : AbstractValidator<DichVuDongRutModel>
    {
        public DichVuDongRutValidation()
        {
            RuleFor(c => c.LogoType)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.LogoCongTyUrlIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.TenCongTy)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.TenCongTyIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.TenPhuongAn)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.TenPhuongAnIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.SoPhieuEIR)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.SoPhieuEirIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.NgayTaoEIR)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.NgayTaoEIRIsNullOrEmpty.ErrorCode)
                    .Must(AppUtil.IsFormatDate)
                    .WithMessage(Resource.Instance.NgayTaoEIRInvalid.ErrorCode);
            RuleFor(c => c.NgayGioTaoEIR)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.NgayGioTaoEIRIsNullOrEmpty.ErrorCode)
                    .Must(AppUtil.IsFormatDateTime)
                    .WithMessage(Resource.Instance.NgayGioTaoEIRInvalid.ErrorCode);
            RuleFor(c => c.TrongLuongHang)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.TrongLuongHangIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.Detail)
                    .Must(AppUtil.IsEmptyArray)
                    .WithMessage(Resource.Instance.ArrayDetailIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.Detail)
                    .ForEach(c => c.SetValidator(new DichVuDongRutDetailValidation()));
        }
    }
}
