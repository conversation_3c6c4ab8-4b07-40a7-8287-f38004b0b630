﻿using Autofac.Features.Indexed;
using Snp.PDF.Printer.Models.v2.Validate;
using Snp.PDF.Printer.Services.v2.Helper;
using Snp.PDF.Printer.Services.v2.Validate;
using Snp.PDF.Printer.Services.v2.Validate.RuleValidate;
using SNP.PDF.Printer.Resources;
using System;
using System.Collections.Generic;

namespace Snp.PDF.Printer.Services.v2
{
    public class ValidateModel : IValidateModel
    {
        private readonly IObjectValue _objectValue;
        private readonly IIndex<RequireTypes, IRuleValidating> _validate;
        public ValidateModel(IObjectValue objectValue, IIndex<RequireTypes, IRuleValidating> validate)
        {
            _objectValue = objectValue;
            _validate = validate;
        }
        #region IDispose pattern
        // Flag: Has Dispose already been called?
        bool disposed = false;

        // Public implementation of Dispose pattern callable by consumers.
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        // Protected implementation of Dispose pattern.
        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;

            if (disposing)
            {
                // Free any other managed objects here.
                //
            }

            // Free any unmanaged objects here.
            //
            disposed = true;
        }

        ~ValidateModel()
        {
            Dispose(false);
        }
        #endregion


        public Error Validate(dynamic model, string BillType)
        {
            var validateProperties = ConfigHelper.Instance.ValidateProperties;

            if (!validateProperties.TryGetValue(BillType, out IEnumerable<ValidateProperty> validateProperty_ies))
            {
                return Resource.Instance.NotFoundFileValidate;
            }
            foreach (var validateProperty in validateProperty_ies)
            {
                if (!validateProperty.Require)
                    return null;// no need validate

                // need validate
                foreach (var valid in validateProperty.Validates)
                {
                    if (valid.ValidateType == RequireTypes.None)
                    {
                        var error = Resource.Instance.NotTryParseRequireType;
                        return new Error(error.ErrorCode, error.Message, valid.RequireType);
                    }

                    if (!_objectValue.TryGetValue(model, validateProperty.PropertyName, out string value, out Error errorTryParse))
                    {
                        return errorTryParse;
                    }

                    var errorValidate = _validate[valid.ValidateType].Validating(value, valid.ValueCompare, validateProperty.PropertyName, valid.Message);
                    if (errorValidate is null)
                        continue;

                    return errorValidate;
                }
            }
            
            return null;// when loop all rule but values valid
        }
    }
}
