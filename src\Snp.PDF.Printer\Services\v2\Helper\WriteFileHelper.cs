﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Snp.PDF.Printer.Services.v2.Helper
{
    public static class WriteFileHelper
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="data">array byte</param>
        /// <param name="fileNameDistination"> filename distination</param>
        /// <param name="exResult">Exception if write file error</param>
        public static void WriteFile(this IEnumerable<byte> data, string fileNameDistination, out Exception exResult)
        {
            try
            {
                exResult = null;
                using (MemoryStream ms = new MemoryStream(data.ToArray()))
                {
                    using (var w = new FileStream(fileNameDistination, FileMode.Create, FileAccess.Write))
                    {
                        ms.CopyTo(w);
                    }
                }
            }
            catch (Exception ex)
            {
                exResult = ex;
            }

        }
    }
}
