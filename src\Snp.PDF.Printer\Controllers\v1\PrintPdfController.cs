﻿using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using SNP.PDF.Printer.Enums;
using SNP.PDF.Printer.Models.Entities;
using SNP.PDF.Printer.Models.Request;
using SNP.PDF.Printer.Models.Response;
using SNP.PDF.Printer.Models.ViewModels;
using SNP.PDF.Printer.Services;

namespace SNP.PDF.Printer.Controllers
{
    //[Route("api/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    public class PrintPdfController : ControllerBase
    {
        private readonly IPhieu _phieuService;

        private readonly IMapper _mapper;

        public PrintPdfController(IPhieu phieuService, IMapper mapper)
        {
            _phieuService = phieuService;
            _mapper = mapper;
        }

        /// <summary>
        ///     in 1 phiếu in giao cont cho cảng
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [MapToApiVersion("1.0")]
        [Route("api/v1/giaocontchocang")]
        [HttpPost]
        public async Task<IActionResult> Print(GiaoContChoCangRequest request)
        {
            GiaoContChoCangVm viewModel = _mapper.Map<GiaoContChoCangVm>(request.Data);
            viewModel.Update();
            var url = await _phieuService.CreatePhieu(viewModel, new MetaEntity(request.MetaData, PrintType.GiaoContChoCang));
            return Ok(new GiaoContChoCangResponse(url, viewModel));
        }

        /// <summary>
        ///     in 1 phiếu in nhận cont từ cảng
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [MapToApiVersion("1.0")]
        [Route("api/v1/nhanconttucang")]
        [HttpPost]
        public async Task<IActionResult> Print(NhanContTuCangRequest request)
        {
            NhanContTuCangVm viewModel = _mapper.Map<NhanContTuCangVm>(request.Data);
            viewModel.Update();
            var url = await _phieuService.CreatePhieu(viewModel, new MetaEntity(request.MetaData, PrintType.NhanContTuCang));
            return Ok(new NhanContTuCangResponse(url, viewModel));
        }

        /// <summary>
        ///     in 1 phiếu in dịch vụ đóng rút
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [MapToApiVersion("1.0")]
        [Route("api/v1/dichvudongrut")]
        [HttpPost]
        public async Task<IActionResult> Print(DichVuDongRutRequest request)
        {
            DichVuDongRutVm viewModel = _mapper.Map<DichVuDongRutVm>(request.Data);
            viewModel.Update();
            var url = await _phieuService.CreatePhieu(viewModel, new MetaEntity(request.MetaData, PrintType.DichVuDongRut));
            return Ok(new DichVuDongRutResponse(url, viewModel));
        }
    }
}
