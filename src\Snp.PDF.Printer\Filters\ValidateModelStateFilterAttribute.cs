﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using SNP.PDF.Printer.Models.Base;
using SNP.PDF.Printer.Resources;

namespace SNP.PDF.Printer.Filters
{
    public class ValidateModelStateFilterAttribute : ActionFilterAttribute
    {
        private static IDictionary<string, Error> _dicError;

        /// <summary>
        ///     Init Dictionary Error
        /// </summary>
        private void Init()
        {
            if(_dicError is null)
            {
                _dicError = new Dictionary<string, Error>
                            {
                                    {Resource.Instance.ArrayDetailIsNullOrEmpty.ErrorCode, Resource.Instance.ArrayDetailIsNullOrEmpty},
                                    {Resource.Instance.LogoCongTyUrlIsNullOrEmpty.ErrorCode, Resource.Instance.LogoCongTyUrlIsNullOrEmpty},
                                    {Resource.Instance.TenCongTyIsNullOrEmpty.ErrorCode, Resource.Instance.TenCongTyIsNullOrEmpty},
                                    {Resource.Instance.TenPhuongAnIsNullOrEmpty.ErrorCode, Resource.Instance.TenPhuongAnIsNullOrEmpty},
                                    {Resource.Instance.SoPhieuEirIsNullOrEmpty.ErrorCode, Resource.Instance.SoPhieuEirIsNullOrEmpty},
                                    {Resource.Instance.NgayTaoEIRIsNullOrEmpty.ErrorCode, Resource.Instance.NgayTaoEIRIsNullOrEmpty},
                                    {Resource.Instance.NgayTaoEIRInvalid.ErrorCode, Resource.Instance.NgayTaoEIRInvalid},
                                    {Resource.Instance.NgayGioTaoEIRIsNullOrEmpty.ErrorCode, Resource.Instance.NgayGioTaoEIRIsNullOrEmpty},
                                    {Resource.Instance.NgayGioTaoEIRInvalid.ErrorCode, Resource.Instance.NgayGioTaoEIRInvalid},
                                    {Resource.Instance.TenKhachHangIsNullOrEmpty.ErrorCode, Resource.Instance.TenKhachHangIsNullOrEmpty},
                                    {Resource.Instance.TenTauMaChuyenIsNullOrEmpty.ErrorCode, Resource.Instance.TenTauMaChuyenIsNullOrEmpty},
                                    {Resource.Instance.CangDichIsNullOrEmpty.ErrorCode, Resource.Instance.CangDichIsNullOrEmpty},
                                    {Resource.Instance.CangChuyenTaiIsNullOrEmpty.ErrorCode, Resource.Instance.CangChuyenTaiIsNullOrEmpty},
                                    {Resource.Instance.SoContIsNullOrEmpty.ErrorCode, Resource.Instance.SoContIsNullOrEmpty},
                                    {Resource.Instance.ISOIsNullOrEmpty.ErrorCode, Resource.Instance.ISOIsNullOrEmpty},
                                    {Resource.Instance.ChuKtIsNullOrEmpty.ErrorCode, Resource.Instance.ChuKtIsNullOrEmpty},
                                    {Resource.Instance.HangTauIsNullOrEmpty.ErrorCode, Resource.Instance.HangTauIsNullOrEmpty},
                                    {Resource.Instance.SoSealIsNullOrEmpty.ErrorCode, Resource.Instance.SoSealIsNullOrEmpty},
                                    {Resource.Instance.TtContIsNullOrEmpty.ErrorCode, Resource.Instance.TtContIsNullOrEmpty},
                                    {Resource.Instance.TongTlIsNullOrEmpty.ErrorCode, Resource.Instance.TongTlIsNullOrEmpty},
                                    {Resource.Instance.SoIMDGIsNullOrEmpty.ErrorCode, Resource.Instance.SoIMDGIsNullOrEmpty},
                                    {Resource.Instance.OwOhOlIsNullOrEmpty.ErrorCode, Resource.Instance.OwOhOlIsNullOrEmpty},
                                    {Resource.Instance.MaxGrossIsNullOrEmpty.ErrorCode, Resource.Instance.MaxGrossIsNullOrEmpty},
                                    {Resource.Instance.VgmCustomerIsNullOrEmpty.ErrorCode, Resource.Instance.VgmCustomerIsNullOrEmpty},
                                    {Resource.Instance.GhiChuHuHongIsNullOrEmpty.ErrorCode, Resource.Instance.GhiChuHuHongIsNullOrEmpty},
                                    {Resource.Instance.KhuVucIsNullOrEmpty.ErrorCode, Resource.Instance.KhuVucIsNullOrEmpty},
                                    {Resource.Instance.TrongLuongHangIsNullOrEmpty.ErrorCode, Resource.Instance.TrongLuongHangIsNullOrEmpty},
                                    {Resource.Instance.TenDoiBocXepIsNullOrEmpty.ErrorCode, Resource.Instance.TenDoiBocXepIsNullOrEmpty},
                                    {Resource.Instance.TenPhuongThucRutHangIsNullOrEmpty.ErrorCode, Resource.Instance.TenPhuongThucRutHangIsNullOrEmpty},
                                    {Resource.Instance.SoHoaDonNoiBoIsNullOrEmpty.ErrorCode, Resource.Instance.SoHoaDonNoiBoIsNullOrEmpty},
                                    {Resource.Instance.NguoiYeuCauIsNullOrEmpty.ErrorCode, Resource.Instance.NguoiYeuCauIsNullOrEmpty},
                                    {Resource.Instance.SoDienThoaiIsNullOrEmpty.ErrorCode, Resource.Instance.SoDienThoaiIsNullOrEmpty},
                                    {Resource.Instance.SiteGiaoNhanIsNullOrEmpty.ErrorCode, Resource.Instance.SiteGiaoNhanIsNullOrEmpty},
                                    {Resource.Instance.SitePhieuEIRIsNullOrEmpty.ErrorCode, Resource.Instance.SitePhieuEIRIsNullOrEmpty},
                                    {Resource.Instance.MaVachUrlIsNullOrEmpty.ErrorCode, Resource.Instance.MaVachUrlIsNullOrEmpty},
                                    {Resource.Instance.GateNoIsNullOrEmpty.ErrorCode, Resource.Instance.GateNoIsNullOrEmpty},
                                    {Resource.Instance.BatNoIsNullOrEmpty.ErrorCode, Resource.Instance.BatNoIsNullOrEmpty},
                                    {Resource.Instance.SoBillOfLadingIsNullOrEmpty.ErrorCode, Resource.Instance.SoBillOfLadingIsNullOrEmpty},
                                    {Resource.Instance.SoBookIsNullOrEmpty.ErrorCode, Resource.Instance.SoBookIsNullOrEmpty},
                                    {Resource.Instance.SoUNNOIsNullOrEmpty.ErrorCode, Resource.Instance.SoUNNOIsNullOrEmpty},
                                    {Resource.Instance.NhietDoIsNullOrEmpty.ErrorCode, Resource.Instance.NhietDoIsNullOrEmpty},
                                    {Resource.Instance.ThongGioIsNullOrEmpty.ErrorCode, Resource.Instance.ThongGioIsNullOrEmpty},
                                    {Resource.Instance.DoAmIsNullOrEmpty.ErrorCode, Resource.Instance.DoAmIsNullOrEmpty},
                                    {Resource.Instance.TlVoIsNullOrEmpty.ErrorCode, Resource.Instance.TlVoIsNullOrEmpty},
                                    {Resource.Instance.SoDkEportIsNullOrEmpty.ErrorCode, Resource.Instance.SoDkEportIsNullOrEmpty},
                                    {Resource.Instance.GhiChuThongTinCanIsNullOrEmpty.ErrorCode, Resource.Instance.GhiChuThongTinCanIsNullOrEmpty},
                                    {Resource.Instance.GhiChuViPhamTaiTrongIsNullOrEmpty.ErrorCode, Resource.Instance.GhiChuViPhamTaiTrongIsNullOrEmpty},
                                    {Resource.Instance.GhiChuStopCodeIsNullOrEmpty.ErrorCode, Resource.Instance.GhiChuStopCodeIsNullOrEmpty},
                                    {Resource.Instance.GhiChuChiDinhIsNullOrEmpty.ErrorCode, Resource.Instance.GhiChuChiDinhIsNullOrEmpty},
                                    {Resource.Instance.GhiChuThongTinContIsNullOrEmpty.ErrorCode, Resource.Instance.GhiChuThongTinContIsNullOrEmpty},
                                    {Resource.Instance.SoXeIsNullOrEmpty.ErrorCode, Resource.Instance.SoXeIsNullOrEmpty},
                                    {Resource.Instance.SoMoocIsNullOrEmpty.ErrorCode, Resource.Instance.SoMoocIsNullOrEmpty},
                                    {Resource.Instance.GhiChuHanLenhInvalid.ErrorCode, Resource.Instance.GhiChuHanLenhInvalid},
                                    {Resource.Instance.GhiChuHanLenhIsNullOrEmpty.ErrorCode, Resource.Instance.GhiChuHanLenhIsNullOrEmpty},
                                    {Resource.Instance.NoiHaRongIsNullOrEmpty.ErrorCode, Resource.Instance.NoiHaRongIsNullOrEmpty},
                                    {Resource.Instance.SoNgayMienIsNullOrEmpty.ErrorCode, Resource.Instance.SoNgayMienIsNullOrEmpty}
                            };
            }
        }

        public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            if(!context.ModelState.IsValid)
            {
                object tmp;
                if(context.ActionArguments.TryGetValue("request", out tmp))
                {
                    Init();
                    var response = new BaseResponse<string>(string.Empty);
                    var errorCodes = new List<string>();
                    var msgs = new List<string>();
                    var attr = string.Empty;
                    var errorCode = string.Empty;
                    var error = default(Error);
                    for (int i = 0; i < context.ModelState.Count; i++)
                    {
                        attr = context.ModelState.Keys.ElementAt(i);
                        errorCode = context.ModelState.Values.ElementAt(i)
                                           .Errors[context.ModelState.Values.ElementAt(i)
                                                          .Errors.Count - 1]
                                           .ErrorMessage;
                        errorCodes.Add(errorCode);

                        error = _dicError[errorCode];
                        error.UpdateAttribute(attr);
                        msgs.Add(error.Message);
                    }

                    response.UpdateMessageError(new Error(string.Join("|", errorCodes), string.Join(" | ", msgs)));

                    context.Result = new OkObjectResult(response);
                }
            }
            else
            {
                await next();
            }
        }
    }
}
