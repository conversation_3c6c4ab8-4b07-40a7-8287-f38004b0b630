@charset "UTF-8";/*!
 * jQuery contextMenu - Plugin for simple contextMenu handling
 *
 * Version: v2.1.0
 *
 * Authors: <AUTHORS>
 * Web: http://swisnl.github.io/jQuery-contextMenu/
 *
 * Copyright (c) 2011-2016 SWIS BV and contributors
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 * Date: 2016-02-28T09:41:28.803Z
 */@font-face{font-family:context-menu-icons;font-style:normal;font-weight:400;src:url(font/context-menu-icons.eot?2qmzf);src:url(font/context-menu-icons.eot?2qmzf#iefix) format("embedded-opentype"),url(font/context-menu-icons.woff2?2qmzf) format("woff2"),url(font/context-menu-icons.woff?2qmzf) format("woff"),url(font/context-menu-icons.ttf?2qmzf) format("truetype")}.context-menu-icon:before{position:absolute;top:50%;left:0;width:28px;font-family:context-menu-icons;font-size:16px;font-style:normal;font-weight:400;line-height:1;color:#2980b9;text-align:center;-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.context-menu-icon-add:before{content:""}.context-menu-icon-copy:before{content:""}.context-menu-icon-cut:before{content:""}.context-menu-icon-delete:before{content:""}.context-menu-icon-edit:before{content:""}.context-menu-icon-paste:before{content:""}.context-menu-icon-quit:before{content:""}.context-menu-icon.context-menu-hover:before{color:#fff}.context-menu-list{position:absolute;display:inline-block;min-width:180px;max-width:360px;padding:4px 0;margin:5px;font-family:inherit;font-size:inherit;list-style-type:none;background:#fff;border:1px solid #bebebe;border-radius:3px;-webkit-box-shadow:0 2px 5px rgba(0,0,0,.5);box-shadow:0 2px 5px rgba(0,0,0,.5)}.context-menu-item{position:relative;padding:3px 28px;color:#2f2f2f;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-color:#fff}.context-menu-separator{padding:0;margin:5px 0;border-bottom:1px solid #e6e6e6}.context-menu-item>label>input,.context-menu-item>label>textarea{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}.context-menu-item.context-menu-hover{color:#fff;cursor:pointer;background-color:#2980b9}.context-menu-item.context-menu-disabled{background-color:#fff;color:#626262}.context-menu-input.context-menu-hover,.context-menu-item.context-menu-disabled.context-menu-hover{cursor:default;background-color:#eee}.context-menu-submenu:after{position:absolute;top:50%;right:8px;z-index:1;width:0;height:0;content:'';border-color:transparent transparent transparent #2f2f2f;border-style:solid;border-width:4px 0 4px 4px;-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.context-menu-item.context-menu-input{padding:5px 10px}.context-menu-input>label>*{vertical-align:top}.context-menu-input>label>input[type=checkbox],.context-menu-input>label>input[type=radio]{position:relative;top:3px}.context-menu-input>label,.context-menu-input>label>input[type=text],.context-menu-input>label>select,.context-menu-input>label>textarea{display:block;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.context-menu-input>label>textarea{height:100px}.context-menu-item>.context-menu-list{top:5px;right:-5px;display:none}.context-menu-item.context-menu-visible>.context-menu-list{display:block}.context-menu-accesskey{text-decoration:underline}
/*# sourceMappingURL=jquery.contextMenu.min.css.map */
