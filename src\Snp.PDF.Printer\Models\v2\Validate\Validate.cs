﻿using Snp.PDF.Printer.Services.v2.Validate;
using System;

namespace Snp.PDF.Printer.Models.v2.Validate
{
    public class Validate
    {
        public string RequireType { get; set; }
        public string Message { get; set; }
        public string ValueCompare { get; set; }
        public RequireTypes ValidateType
        {
            get
            {
                if (!Enum.TryParse(RequireType, out RequireTypes type))
                    return RequireTypes.None;
                return type;
            }
        }

        public Validate()
        {
            this.RequireType = string.Empty;
            this.Message = string.Empty;
            this.ValueCompare = string.Empty;
        }
    }
}
