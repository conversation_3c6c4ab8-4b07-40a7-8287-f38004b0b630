/* ==========================================================================
   Mixins
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   Typical cards
   ========================================================================== */
.card-typical {
  background: #fff;
  border: solid 1px #d8e2e7;
  -webkit-border-radius: .25rem;
          border-radius: .25rem;
}
.card-typical p {
  margin: 0;
}
.card-typical .card-typical-section {
  padding: 15px;
  border-top: solid 1px #d8e2e7;
  zoom: 1;
}
.card-typical .card-typical-section:before,
.card-typical .card-typical-section:after {
  content: " ";
  display: table;
}
.card-typical .card-typical-section:after {
  clear: both;
}
.card-typical .card-typical-section:first-child {
  border-top: none;
}
.card-typical .card-typical-content {
  font-size: .875rem;
}
.card-typical .card-typical-content .photo {
  margin: -10px -10px 15px;
}
.card-typical .card-typical-content .photo img {
  display: block;
  width: 100%;
}
.card-typical .card-typical-content .title {
  font-weight: 600;
  font-size: 1rem;
  margin: 0 0 5px;
}
.card-typical .card-typical-content .title a {
  color: #343434;
}
.card-typical .card-typical-content .title a:hover {
  color: #00a8ff;
}
.card-typical .card-typical-linked,
.card-typical .card-typical-likes {
  color: #919fa9;
  line-height: 1rem;
  font-size: .875rem;
}
.card-typical .card-typical-linked {
  float: left;
}
.card-typical .card-typical-likes {
  float: right;
}
.card-typical .card-typical-likes .font-icon {
  margin: 0 2px 0 0;
  vertical-align: middle;
  font-size: .875rem;
}
.card-typical .card-typical-likes:hover {
  color: #00a8ff;
}
.cards-grid .card-typical {
  margin: 0 15px 30px;
}
/* ==========================================================================
   Grid cards
   ========================================================================== */
.cards-grid[data-columns]::before {
  content: '3 .column.size-1of3';
}
.cards-grid .column {
  float: left;
}
.cards-grid .size-1of4 {
  width: 25%;
}
.cards-grid .size-1of3 {
  width: 33.333%;
}
.cards-grid .size-1of2 {
  width: 50%;
}
@media screen and (max-width: 520px) {
  .cards-grid[data-columns]::before {
    content: '1 .column';
  }
}
@media screen and (min-width: 521px) and (max-width: 767px) {
  .cards-grid[data-columns]::before {
    content: '2 .column.size-1of2';
  }
}
@media screen and (min-width: 768px) and (max-width: 1300px) {
  .cards-grid[data-columns]::before {
    content: '3 .column.size-1of3';
  }
}
@media screen and (min-width: 1301px) {
  .cards-grid[data-columns]::before {
    content: '4 .column.size-1of4';
  }
}
