﻿using System.Collections.Generic;
using System.Linq;
using SNP.PDF.Printer.Models.Base;
using SNP.PDF.Printer.Models.ViewModels;
using SNP.PDF.Printer.Resources;

namespace SNP.PDF.Printer.Models.Response
{
    public class NhanContTuCangResponse : BaseResponse<string>
    {
        public NhanContTuCangResponse(string url) : base(url)
        {
        }

        public NhanContTuCangResponse(string url, NhanContTuCangVm model) : base(url)
        {
            if(string.IsNullOrEmpty(model.LogoUrl))
            {
                Error missingLogoType = new Error("400100", "Logo site {0} không tồn tại", model.LogoType.ToUpper());
                Update(missingLogoType);
            }
        }

        public NhanContTuCangResponse(string url, List<NhanContTuCangVm> model) : this(url)
        {
            if(model != null
               && model.Any())
            {
                List<Error> errors = new List<Error>();
                foreach (var data in model)
                {
                    if(string.IsNullOrEmpty(data.LogoUrl))
                    {
                        errors.Add(new Error("400100", "Logo site {0} không tồn tại", data.LogoType.ToUpper()));
                    }
                }

                if(errors.Any())
                {
                    Update(errors);
                }
            }
        }
    }
}
