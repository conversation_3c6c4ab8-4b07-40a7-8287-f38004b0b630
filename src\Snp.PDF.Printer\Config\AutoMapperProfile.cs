﻿using AutoMapper;
using SNP.PDF.Printer.Models;
using SNP.PDF.Printer.Models.Base;
using SNP.PDF.Printer.Models.ViewModels;

namespace SNP.PDF.Printer.Config
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            CreateMap<BaseChiTietPhieuEir, BaseChiTietPhieuEirVm>();

            CreateMap<GiaoContChoCangModel, GiaoContChoCangVm>();
            CreateMap<GiaoContChoCangDetail, GiaoContChoCangDetailVm>();

            CreateMap<NhanContTuCangModel, NhanContTuCangVm>();
            CreateMap<NhanContTuCangDetail, NhanContTuCangDetailVm>();

            CreateMap<DichVuDongRutModel, DichVuDongRutVm>();
            CreateMap<DichVuDongRutDetail, DichVuDongRutDetailVm>();
        }
    }
}
