﻿using FluentValidation;
using SNP.PDF.Printer.Models.Validation;
using SNP.PDF.Printer.Resources;

namespace SNP.PDF.Printer.Models.Request
{
    public class NhanContTuCangValidation : AbstractValidator<NhanContTuCangModel>
    {
        public NhanContTuCangValidation()
        {
            RuleFor(c => c.LogoType)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.LogoCongTyUrlIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.TenCongTy)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.TenCongTyIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.Ten<PERSON>huong<PERSON>n)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.TenPhuongAnIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.SoPhieuEIR)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.SoPhieuEirIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.NgayGioTaoEIR)
                    .NotNull()
                    .NotEmpty()
                    .WithMessage(Resource.Instance.NgayGioTaoEIRIsNullOrEmpty.ErrorCode)
                    .Must(AppUtil.IsFormatDateTime)
                    .WithMessage(Resource.Instance.NgayGioTaoEIRInvalid.ErrorCode);
            RuleFor(c => c.Detail)
                    .Must(AppUtil.IsEmptyArray)
                    .WithMessage(Resource.Instance.ArrayDetailIsNullOrEmpty.ErrorCode);
            RuleFor(c => c.Detail)
                    .ForEach(c => c.SetValidator(new NhanContTuCangDetailValidation()));
        }
    }
}
