/* ==========================================================================
   HTML5 display definitions
   ========================================================================== */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
  display: block;
}
audio,
canvas,
video {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
[hidden] {
  display: none;
}
/* ==========================================================================
   Base
   ========================================================================== */
/**
 * 1. Correct text resizing oddly in IE 6/7 when body `font-size` is set using
 *    `em` units.
 * 2. Prevent iOS text size adjust after orientation change, without disabling
 *    user zoom.
 */
html {
  font-size: 100%;
  /* 1 */
  -ms-text-size-adjust: 100%;
  /* 2 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}
html,
button,
input,
select,
textarea {
  font-family: sans-serif;
}
body {
  margin: 0;
}
*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
/* ==========================================================================
   Links
   ========================================================================== */
*:focus {
  outline: none;
}
*:active,
*:hover {
  outline: 0;
}
/* ==========================================================================
   Typography
   ========================================================================== */
abbr[title] {
  border-bottom: 1px dotted;
}
b,
strong {
  font-weight: bold;
}
blockquote {
  margin: 1em 40px;
}
dfn {
  font-style: italic;
}
hr {
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  height: 0;
}
mark {
  background: #ff0;
  color: #000;
}
pre {
  margin: 0px;
}
/**
 * Correct font family set oddly in IE 6, Safari 4/5, and Chrome.
 */
code,
kbd,
pre,
samp {
  font-family: monospace, serif;
  _font-family: 'courier new', monospace;
  font-size: 1em;
}
/**
 * Improve readability of pre-formatted text in all browsers.
 */
pre {
  white-space: pre;
  white-space: pre-wrap;
  word-wrap: break-word;
}
/**
 * Address CSS quotes not supported in IE 6/7.
 */
q {
  quotes: none;
}
/**
 * Address `quotes` property not supported in Safari 4.
 */
q:before,
q:after {
  content: '';
  content: none;
}
/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%;
}
/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
/* ==========================================================================
   Lists
   ========================================================================== */
/**
 * Address margins set differently in IE 6/7.
 */
dl,
menu,
ol,
ul {
  margin: 0;
}
dd {
  margin: 0 0 0 40px;
}
/**
 * Correct list images handled incorrectly in IE 7.
 */
nav ul,
nav ol {
  list-style: none;
  list-style-image: none;
}
/* ==========================================================================
   Embedded content
   ========================================================================== */
/**
 * 1. Remove border when inside `a` element in IE 6/7/8/9 and Firefox 3.
 * 2. Improve image quality when scaled in IE 7.
 */
img {
  border: 0;
  /* 1 */
  -ms-interpolation-mode: bicubic;
  /* 2 */
}
/**
 * Correct overflow displayed oddly in IE 9.
 */
svg:not(:root) {
  overflow: hidden;
}
/* ==========================================================================
   Figures
   ========================================================================== */
/**
 * Address margin not present in IE 6/7/8/9, Safari 5, and Opera 11.
 */
figure {
  margin: 0;
}
/* ==========================================================================
   Forms
   ========================================================================== */
/**
 * Correct margin displayed oddly in IE 6/7.
 */
form {
  margin: 0;
}
/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
/**
 * 1. Correct color not being inherited in IE 6/7/8/9.
 * 2. Correct text not wrapping in Firefox 3.
 * 3. Correct alignment displayed oddly in IE 6/7.
 */
legend {
  border: 0;
  /* 1 */
  padding: 0;
  white-space: normal;
  /* 2 */
  *margin-left: -7px;
  /* 3 */
}
/**
 * 1. Correct font size not being inherited in all browsers.
 * 2. Address margins set differently in IE 6/7, Firefox 3+, Safari 5,
 *    and Chrome.
 * 3. Improve appearance and consistency in all browsers.
 */
button,
input,
select,
textarea {
  font-size: 100%;
  /* 1 */
  margin: 0;
  /* 2 */
  vertical-align: baseline;
  /* 3 */
  *vertical-align: middle;
  /* 3 */
}
/**
 * Address Firefox 3+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
button,
input {
  line-height: normal;
}
/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Chrome, Safari 5+, and IE 6+.
 * Correct `select` style inheritance in Firefox 4+ and Opera.
 */
button,
select {
  text-transform: none;
}
/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 * 4. Remove inner spacing in IE 7 without affecting normal text inputs.
 *    Known issue: inner spacing remains in IE 6.
 */
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
  cursor: pointer;
  /* 3 */
  *overflow: visible;
  /* 4 */
}
/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default;
}
/**
 * 1. Address box sizing set to content-box in IE 8/9.
 * 2. Remove excess padding in IE 8/9.
 * 3. Remove excess padding in IE 7.
 *    Known issue: excess padding remains in IE 6.
 */
input[type="checkbox"],
input[type="radio"] {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
  *height: 13px;
  /* 3 */
  *width: 13px;
  /* 3 */
}
/**
 * 1. Address `appearance` set to `searchfield` in Safari 5 and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari 5 and Chrome
 *    (include `-moz` to future-proof).
 */
input[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  /* 2 */
  box-sizing: content-box;
}
/**
 * Remove inner padding and search cancel button in Safari 5 and Chrome
 * on OS X.
 */
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
/**
 * Remove inner padding and border in Firefox 3+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
/**
 * 1. Remove default vertical scrollbar in IE 6/7/8/9.
 * 2. Improve readability and alignment in all browsers.
 */
textarea {
  overflow: auto;
  /* 1 */
  vertical-align: top;
  /* 2 */
}
/* ==========================================================================
   Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}
/* ==========================================================================
   Opinionated defaults
   ========================================================================== */
html {
  font-size: 1em;
  line-height: 1.4;
}
* {
  padding: 0px;
  margin: 0px;
}
img {
  border: none;
}
ul {
  list-style: none;
}
hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
  margin: 1em 0;
  padding: 0;
}
textarea {
  resize: vertical;
}
fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}
audio,
canvas,
img,
video {
  vertical-align: middle;
}
.browsehappy {
  margin: 0.2em 0;
  background: #ccc;
  color: #000;
  padding: 0.2em 0;
}
::-moz-selection {
  background: #b3d4fc;
  text-shadow: none;
}
::selection {
  background: #b3d4fc;
  text-shadow: none;
}
/* ==========================================================================
   Mixins
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   Helper classes
   ========================================================================== */
/* Image replacement */
.ir {
  background-color: transparent;
  border: 0;
  overflow: hidden;
  *text-indent: -9999px;
}
.ir:before {
  content: "";
  display: block;
  width: 0;
  height: 150%;
}
/* Hide from both screenreaders and browsers: h5bp.com/u */
.hidden {
  display: none !important;
  visibility: hidden;
}
/* Hide only visually, but have it available for screenreaders: h5bp.com/v */
.visuallyhidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}
/* Extends the .visuallyhidden class to allow the element to be focusable when navigated to via the keyboard: h5bp.com/p */
.visuallyhidden.focusable:active,
.visuallyhidden.focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}
/* Hide visually and from screenreaders, but maintain layout */
.invisible {
  visibility: hidden;
}
/* Clear fix */
.clear {
  clear: both;
  height: 0px;
  font-size: 0px;
  line-height: 0px;
  overflow: hidden;
}
.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}
.clearfix:after {
  clear: both;
}
.clearfix {
  *zoom: 1;
}
/* Align */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
/* Float */
.float-left {
  float: left;
}
.float-right {
  float: right;
}
.tbl {
  display: table;
  width: 100%;
  border-collapse: collapse;
}
.tbl-row {
  display: table-row;
}
.tbl-cell {
  display: table-cell;
  vertical-align: middle;
}
.no-border,
.b-a-0 {
  border: none;
}
.b-a {
  border: solid 1px #d8e2e7;
}
.b-l {
  border-left: solid 1px #d8e2e7;
}
.b-r {
  border-right: solid 1px #d8e2e7;
}
.b-t {
  border-top: solid 1px #d8e2e7;
}
.b-b {
  border-bottom: solid 1px #d8e2e7;
}
.b-t-0 {
  border-top: none;
}
.b-r-0 {
  border-bottom: none;
}
.border-primary {
  border-color: #00a8ff;
}
.border-default {
  border-color: #d8e2e7;
}
.border-warning {
  border-color: #f29824;
}
.border-success {
  border-color: #46c35f;
}
.border-info {
  border-color: #ac6bec;
}
.border-white {
  border-color: #fff;
}
.border-danger {
  border-color: #fa424a;
}
.no-radius {
  -webkit-border-radius: 0;
          border-radius: 0;
}
.round {
  -webkit-border-radius: .25rem;
          border-radius: .25rem;
}
.brtl {
  -webkit-border-top-left-radius: .25rem;
          border-top-left-radius: .25rem;
}
.brtr {
  -webkit-border-top-right-radius: .25rem;
          border-top-right-radius: .25rem;
}
.blbl {
  -webkit-border-bottom-left-radius: .25rem;
          border-bottom-left-radius: .25rem;
}
.brbr {
  -webkit-border-bottom-right-radius: .25rem;
          border-bottom-right-radius: .25rem;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-visible {
  overflow: visible;
}
.overflow-auto {
  overflow: auto;
}
.w100 {
  width: 100px;
}
.w150 {
  width: 150px;
}
.w200 {
  width: 200px;
}
.w250 {
  width: 250px;
}
.w300 {
  width: 300px;
}
.w350 {
  width: 350px;
}
.w400 {
  width: 400px;
}
.w450 {
  width: 450px;
}
.w500 {
  width: 500px;
}
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.lt {
  left: 0;
}
.rt {
  right: 0;
}
.bt {
  bottom: 0;
}
.tp {
  top: 0;
}
.no-padding {
  padding: 0;
}
.no-margin {
  margin: 0;
}
.no-bg {
  background: none;
}
.no-shadow {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.shadow {
  -webkit-box-shadow: 1px 1px rgba(0, 0, 0, 0.3);
          box-shadow: 1px 1px rgba(0, 0, 0, 0.3);
}
.text-shadow {
  text-shadow: 1px 1px rgba(0, 0, 0, 0.3);
}
.block {
  display: block;
}
.m-x-n {
  margin-left: -15px;
  margin-right: -15px;
}
.m-r-n {
  margin-right: -15px;
}
.m-l-n-n {
  margin-left: -15px;
}
.m-a-lg {
  margin: 30px;
}
.m-r-lg {
  margin-right: 30px;
}
.m-b-lg {
  margin-bottom: 30px;
}
.m-l-lg {
  margin-left: 30px;
}
.m-t-lg {
  margin-top: 30px;
}
.m-x-lg {
  margin-left: 30px;
  margin-right: 30px;
}
.m-y-lg {
  margin-top: 30px;
  margin-bottom: 30px;
}
.m-a-md {
  margin: 20px;
}
.m-r-md {
  margin-right: 20px;
}
.m-b-md {
  margin-bottom: 20px;
}
.m-l-md {
  margin-left: 20px;
}
.m-t-md {
  margin-top: 20px;
}
.m-x-md {
  margin-left: 20px;
  margin-right: 20px;
}
.m-y-md {
  margin-top: 20px;
  margin-bottom: 20px;
}
.m-a {
  margin: 15px;
}
.m-r {
  margin-right: 15px;
}
.m-b {
  margin-bottom: 15px;
}
.m-l {
  margin-left: 15px;
}
.m-t {
  margin-top: 15px;
}
.m-x {
  margin-left: 15px;
  margin-right: 15px;
}
.m-y {
  margin-top: 15px;
  margin-bottom: 15px;
}
.m-a-0 {
  margin: 0;
}
.m-r-0 {
  margin-right: 0;
}
.m-b-0 {
  margin-bottom: 0;
}
.m-l-0 {
  margin-left: 0;
}
.m-t-0 {
  margin-top: 0;
}
.m-x-0 {
  margin-left: 0;
  margin-right: 0;
}
.m-y-0 {
  margin-top: 0;
  margin-bottom: 0;
}
.m-x-auto {
  margin-right: auto;
  margin-left: auto;
}
.p-x-n {
  padding-left: -15px;
  padding-right: -15px;
}
.p-r-n {
  padding-right: -15px;
}
.p-l-n-n {
  padding-left: -15px;
}
.p-a-lg {
  padding: 30px;
}
.p-r-lg {
  padding-right: 30px;
}
.p-b-lg {
  padding-bottom: 30px;
}
.p-l-lg {
  padding-left: 30px;
}
.p-t-lg {
  padding-top: 30px;
}
.p-x-lg {
  padding-left: 30px;
  padding-right: 30px;
}
.p-y-lg {
  padding-top: 30px;
  padding-bottom: 30px;
}
.p-a-md {
  padding: 20px;
}
.p-r-md {
  padding-right: 20px;
}
.p-b-md {
  padding-bottom: 20px;
}
.p-l-md {
  padding-left: 20px;
}
.p-t-md {
  padding-top: 20px;
}
.p-x-md {
  padding-left: 20px;
  padding-right: 20px;
}
.p-y-md {
  padding-top: 20px;
  padding-bottom: 20px;
}
.p-a {
  padding: 15px;
}
.p-r {
  padding-right: 15px;
}
.p-b {
  padding-bottom: 15px;
}
.p-l {
  padding-left: 15px;
}
.p-t {
  padding-top: 15px;
}
.p-x {
  padding-left: 15px;
  padding-right: 15px;
}
.p-y {
  padding-top: 15px;
  padding-bottom: 15px;
}
.p-a-0 {
  padding: 0;
}
.p-r-0 {
  padding-right: 0;
}
.p-b-0 {
  padding-bottom: 0;
}
.p-l-0 {
  padding-left: 0;
}
.p-t-0 {
  padding-top: 0;
}
.p-x-0 {
  padding-left: 0;
  padding-right: 0;
}
.p-y-0 {
  padding-top: 0;
  padding-bottom: 0;
}
.p-x-auto {
  padding-right: auto;
  padding-left: auto;
}
.display-table {
  display: table;
  width: 100%;
}
.text-vertical-middle {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}
.inline-block {
  display: inline-block !important;
}
