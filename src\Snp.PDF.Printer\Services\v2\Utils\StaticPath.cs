﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.IO;

namespace Snp.PDF.Printer.Services.v2.Utils
{
    public class StaticPath
    {
        private static Lazy<StaticPath> _instance = new Lazy<StaticPath>(() => new StaticPath());
        public static StaticPath Instance
        {
            get
            {
                return _instance.Value;
            }
        }

        #region Defines
        private const string _relativePathCssWithBillType = @"wwwroot\UserConfigs\{0}\style.css";
        private const string _relativePathHtmlTemplateWithBillType = @"wwwroot\UserConfigs\{0}\template.cshtml";
        private const string _relativePathValidateObjectWithBillType = @"wwwroot\UserConfigs\{0}\validateobject.json";
        private const string _relativeDirectoryImageEmbedded = @"wwwroot\UserConfigs\{0}\ImgAttachments";
        private const string _relativePathBarcodeConfig = @"wwwroot\UserConfigs\{0}\barcodeconfig.json";
        private const string _relativePathZip = @"wwwroot\exports\zips\{0}";
        private const string _relativePathExportPdf = @"wwwroot\exports\pdfs\{0}\{1}";
        /************************************************************************************************************/
        private const string _relativeUrlLogoWithSite = @"{0}/UserConfigs/Logo/SiteId/{1}/logo.png";
        private const string _relativeUrlImageEmbedded = @"{0}/UserConfigs/{1}/ImgAttachments/";
        private const string _relativeUrlExportPdf = "{0}/exports/pdfs/{1}/{2}";
        private const string _folderConfig = @"wwwroot\UserConfigs";
        private const string _folderRoot = "wwwroot";
        private const string _folderLogo = "Logo";
        private const string _relativeUrlZipFile = "{0}/exports/zips/{1}/{2}";
        private static string _currentDirectory = Directory.GetCurrentDirectory();


        private static IHttpContextAccessor _httpContextAccessor;
        public static HttpContext _context => _httpContextAccessor.HttpContext;

        private static string _baseUrl => $"{_context.Request.Scheme}://{_context.Request.Host}{_context.Request.PathBase}";
        private static IDictionary<PathTypes, string> _subPaths;
        #endregion

        #region C'tor
        /// <summary>
        /// C'tor
        /// </summary>
        public StaticPath()
        {
            if (_subPaths == null || _subPaths.Count == 0)
            {
                _subPaths = new Dictionary<PathTypes, string>();

                _subPaths.Add(PathTypes.Path_Css_With_BillType, _relativePathCssWithBillType);
                _subPaths.Add(PathTypes.Path_HtmlTemplate_With_BillType, _relativePathHtmlTemplateWithBillType);
                _subPaths.Add(PathTypes.Path_ValidateObject_With_BillType, _relativePathValidateObjectWithBillType);
                _subPaths.Add(PathTypes.Path_ImageEmbedded, _relativeDirectoryImageEmbedded);
                _subPaths.Add(PathTypes.Url_Logo_With_SiteId, _relativeUrlLogoWithSite);
                _subPaths.Add(PathTypes.Url_ImageEmbedded, _relativeUrlImageEmbedded);
                _subPaths.Add(PathTypes.Path_BarCodeConfig, _relativePathBarcodeConfig);
            }
        }
        #endregion
        internal static void Configure(IHttpContextAccessor contextAccessor)
        {
            _httpContextAccessor = contextAccessor;
        }
        /// <summary>
        /// Get full path
        /// </summary>
        /// <param name="type">path type</param>
        /// <param name="billType">bill type</param>
        /// <returns></returns>
        public string GetPath(PathTypes type, string billType)
        {
            if (type == PathTypes.Url_ImageEmbedded || type == PathTypes.Url_Logo_With_SiteId)
                return string.Empty;

            if (!_subPaths.TryGetValue(type, out string subPathInfo))
                return string.Empty;
            
            return Path.Combine(_currentDirectory, string.Format(_subPaths[type], billType));
        }

        /// <summary>
        /// get full url
        /// </summary>
        /// <param name="type"></param>
        /// <param name="siteIdOrBillType"></param>
        /// <returns></returns>
        public string GetUrl(PathTypes type, string siteIdOrBillType)
        {
            if (type != PathTypes.Url_ImageEmbedded && type != PathTypes.Url_Logo_With_SiteId)
                return string.Empty;

            if (!_subPaths.TryGetValue(type, out string subRelativeInfo))
                return string.Empty;

            return string.Format(subRelativeInfo, _baseUrl, siteIdOrBillType);
        }
        
        /// <summary>
        /// Get Directory User Config 
        /// </summary>
        /// <returns></returns>
        public string GetDirectoryUserConfig()
        {
            return Path.Combine(_currentDirectory, _folderConfig);
        }

        public string GetUrlFilePdf(string folder, string filename)
        {
            return string.Format(_relativeUrlExportPdf, _baseUrl, folder, filename);
        }
        private static string ConvertRelativePathToUrl(string relativePath)
        {
            return relativePath.Replace("\\", "/").Replace(@"\", "/");
        }

        /// <summary>
        /// CreateDestinationFilePdf
        /// </summary>
        /// <param name="folder"></param>
        /// <param name="filename"></param>
        /// <returns></returns>
        public string CreateDestinationFilePdf(string folder, string filename)
        {
            var fullFileName = Path.Combine(_currentDirectory, string.Format(_relativeUrlExportPdf, _folderRoot, folder, filename));
            var path = Path.GetDirectoryName(fullFileName);
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
            return fullFileName;
        }
        /// <summary>
        /// get path pdf local
        /// </summary>
        /// <param name="folder"></param>
        /// <param name="filename"></param>
        /// <returns></returns>
        public string GetPathPdf(string folder, string filename)
        {
            return Path.Combine(_currentDirectory, string.Format(_relativePathExportPdf,folder, filename));
        }
        public string FolderLogo { get { return _folderLogo; } }

        /// <summary>
        /// Create destination file zip
        /// </summary>
        /// <param name="folder"></param>
        /// <param name="filename"></param>
        /// <returns></returns>
        public string CreateDestinationFileZip(string folder, string filename)
        {
            var path = Path.Combine(_currentDirectory, string.Format(_relativePathZip, folder));
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
            return Path.Combine(path, filename);
        }

        /// <summary>
        /// Get full url zip file
        /// </summary>
        /// <param name="folder"></param>
        /// <param name="filename"></param>
        /// <returns></returns>
        public string GetUrlZipFile(string folder, string filename)
        {
            return string.Format(_relativeUrlZipFile, _baseUrl, folder, filename);
        }
        
    }
}
