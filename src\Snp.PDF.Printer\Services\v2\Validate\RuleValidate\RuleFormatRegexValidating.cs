﻿using System;
using SNP.PDF.Printer.Resources;

namespace Snp.PDF.Printer.Services.v2.Validate.RuleValidate
{
    public class RuleFormatRegexValidating : BaseRuleValidate, IRuleValidating
    {
        #region IDispose pattern
        // Flag: Has Dispose already been called?
        bool disposed = false;

        // Public implementation of Dispose pattern callable by consumers.
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        // Protected implementation of Dispose pattern.
        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;

            if (disposing)
            {
                // Free any other managed objects here.
                //
            }

            // Free any unmanaged objects here.
            //
            disposed = true;
        }

        ~RuleFormatRegexValidating()
        {
            Dispose(false);
        }
        #endregion

        public Error Validating(string value, string valueCompare = "", string propertyName = "", string message = "")
        {
            throw new NotImplementedException();
        }
        
    }
}
