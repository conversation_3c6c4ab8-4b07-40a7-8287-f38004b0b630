.wizard .form-group.has-error .form-control {
  background-color: #fff6f6;
  border-color: #f84646;
}
.wizard .form-group.has-error .error {
  margin-top: 3px;
}
.wizard .current-info {
  display: none;
}
.wizard .steps li {
  display: inline-block;
  -webkit-border-radius: 25rem;
          border-radius: 25rem;
  margin-right: 20px;
}
.wizard .steps li a {
  font-size: 20px;
  font-weight: 600;
  padding: 8px 20px;
  display: block;
  color: #818181;
}
.wizard .steps li.done {
  background: #adb7bf;
}
.wizard .steps li.done a {
  color: #fff;
}
.wizard .steps li.current {
  background: #00a6ff;
}
.wizard .steps li.current a {
  color: #fff;
}
.wizard .steps li:last-child {
  margin-right: 0;
}
.wizard > .content {
  position: relative;
  margin-top: 30px;
  border: 2px solid #dbe4eb;
  -webkit-border-radius: 6px;
          border-radius: 6px;
  display: block;
  min-height: 15em;
  overflow: hidden;
  width: auto;
}
.wizard > .content > .title,
.tabcontrol > .content > .title {
  position: absolute;
  left: -999em;
}
.wizard > .content > .body {
  float: left;
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  padding: 22px;
}
.wizard > .actions {
  position: relative;
  display: block;
  text-align: right;
  width: 100%;
  margin-top: 30px;
}
.wizard > .actions li {
  display: inline-block;
  margin-left: 20px;
}
.wizard > .actions li a {
  display: block;
  color: #fff;
  background-color: #00a8ff;
  font-size: 20px;
  font-weight: 600;
  padding: 8px 20px;
  -webkit-border-radius: 25rem;
          border-radius: 25rem;
}
.wizard > .actions li:last-child {
  margin-right: 0;
}
.wizard > .actions li.disabled a {
  opacity: 0.5;
}
.wizard .steps li.error {
  background: #f84646;
}
.wizard .steps li.error a {
  color: #fff;
}
.wizard .steps li.error a .number {
  color: #fff !important;
}
.form-wizard .wizard > .content {
  border: none !important;
  min-height: 17em;
}
.form-wizard .wizard > .content > .body {
  padding: 0;
}
.wizard.vertical .steps {
  float: left;
  width: 15%;
  margin-top: 15px;
  padding-right: 20px;
}
.wizard.vertical .steps li {
  display: block !important;
  margin-bottom: 20px;
}
.wizard.vertical .steps li:last-child {
  margin-right: 20px;
}
.wizard.vertical .actions {
  margin-top: 15px;
  float: right;
}
.wizard.vertical .content {
  margin-top: 15px;
  float: left;
  width: 85%;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
  line-height: 0;
  clear: both;
}
