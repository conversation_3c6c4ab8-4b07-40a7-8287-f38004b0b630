using AnonymousGateway;
using DinkToPdf;
using DinkToPdf.Contracts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Snp.PDF.Printer.Logs;
using Snp.PDF.Printer.Models;
using Snp.PDF.Printer.Models.ESignRequest;
using Snp.PDF.Printer.Services.v2.Utils;
using SNP.PDF.Printer;
using Spire.Pdf;
using Spire.Pdf.General.Find;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Snp.PDF.Printer.Services.v2
{
    public class ConvertHtml2PdfService : IConvertHtml2PdfService
    {
        private readonly string _textToSearch = "Version";

        #region IDispose pattern

        // Flag: Has Dispose already been called?
        private bool disposed = false;

        // Public implementation of Dispose pattern callable by consumers.
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        // Protected implementation of Dispose pattern.
        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;

            if (disposing)
            {
                if (_converter is IDisposable d)
                {
                    d.Dispose();
                }
            }

            // Free any unmanaged objects here.
            //
            disposed = true;
        }

        ~ConvertHtml2PdfService()
        {
            Dispose(false);
        }

        #endregion IDispose pattern

        #region Defines

        private readonly IOptions<HtmlToPdfDocument> _document;
        private readonly IConverter _converter;
        private readonly IAnonymousGateway _anonymousGateway;
        private readonly IConfiguration _config;
        private readonly IOptions<List<ESignRequestDTO>> _eSignRequest;
        private static readonly SemaphoreSlim _pdfConvertLock = new SemaphoreSlim(1, 1);
        #endregion Defines

        #region C'tor

        /// <summary>
        /// C'tor
        /// </summary>
        public ConvertHtml2PdfService(IConverter converter,
            IOptions<HtmlToPdfDocument> document,
            IAnonymousGateway anonymousGateway,
            IConfiguration config,
            IOptions<List<ESignRequestDTO>> eSignRequest)
        {
            _converter = converter;
            _document = document;
            _config = config;
            _anonymousGateway = anonymousGateway;
            _eSignRequest = eSignRequest;
        }

        #endregion C'tor

        /// <summary>
        /// Convert Html to Pdf
        /// </summary>
        /// <param name="data"></param>
        /// <param name="billType"></param>
        /// <param name="fileName"></param>
        /// <param name="siteId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<byte>> ConvertAsync(dynamic data, string billType, string fileName, string siteId)
        {
            var siteApply = _config.GetValue<string>("SiteApplyDigitalSign");
            bool needSign = siteApply.Contains(siteId.Trim());

            try
            {
                if (!needSign)
                    return await ConvertPdfWithoutSignAsync(data, billType, fileName);

                if (siteId == "GNL")
                    billType += $"_{siteId}";

                string html = await BindingDataToHtml.Instance.FillDataIntoHtmlTemplate(billType, data);
                string documentTitle = Path.GetFileNameWithoutExtension(fileName);
                var pdf = PreparePdfDocument(html, billType, documentTitle);

                DateTime eirDate = ExtractEirDate(data);

                byte[] bytes = await SafeConvertPdfAsync(pdf);
                var pdfInfo = GetCoordinatesText(bytes);
                if (pdfInfo == null) return bytes;

                try
                {
                    var response = await ESignPdf(siteId.Trim(), eirDate, new MemoryStream(bytes), pdfInfo.PageNumber, pdfInfo.Y + 3);
                    if (response?.ReturnCode == 0 && response.Data?.Length > 100)
                        return response.Data;

                    LogManager.Logger(_config).Information($"ESignPdf failed: {response?.Message}");
                    return await ConvertPdfWithoutSignAsync(data, billType, fileName);
                }
                catch (Exception ex)
                {
                    LogManager.Logger(_config).Information($"ESignPdf exception: {(ex.InnerException ?? ex).Message}");
                    return await ConvertPdfWithoutSignAsync(data, billType, fileName);
                }
            }
            catch (Exception ex)
            {
                LogManager.Logger(_config).Information($"ConvertAsync Error: {(ex.InnerException ?? ex).Message}");
                return Enumerable.Empty<byte>();
            }
        }
        private DateTime ExtractEirDate(dynamic data)
        {
            try
            {
                foreach (Newtonsoft.Json.Linq.JProperty item in data)
                {
                    if (item.Name == "NgayGioTaoEIR")
                    {
                        if (DateTime.TryParseExact(item.Value?.ToString(), "dd/MM/yyyy HH:mm:ss",
                            new DateTimeFormatInfo { LongTimePattern = "dd/MM/yyyy HH:mm:ss" },
                            DateTimeStyles.None, out var parsedDate))
                        {
                            return parsedDate;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.Logger(_config).Information($"ExtractEirDate error: {(ex.InnerException ?? ex).Message}");
            }

            return DateTime.Now; // fallback nếu không có field hoặc lỗi parse
        }
        private async Task<IEnumerable<byte>> ConvertPdfWithoutSignAsync(dynamic data, string billType, string fileName)
        {
            try
            {
                string adjustedBillType = billType.Replace("_GNL", "");
                string html = await BindingDataToHtml.Instance.FillDataIntoHtmlTemplate($"{adjustedBillType}_NO_SIGN", data);

                string documentTitle = Path.GetFileNameWithoutExtension(fileName);
                var pdf = PreparePdfDocument(html, adjustedBillType, documentTitle);

                return await SafeConvertPdfAsync(pdf);
            }
            catch (Exception ex)
            {
                LogManager.Logger(_config).Information($"ConvertPdfWithoutSignAsync Error: {(ex.InnerException ?? ex).Message}");
                return Enumerable.Empty<byte>();
            }
        }

        private async Task<Response> ESignPdf(string siteId, DateTime eirDate, MemoryStream ms, int pageNumber, float positionY)
        {
            var config = _eSignRequest.Value.FirstOrDefault(x => x.SiteId == siteId);
            if (config == null)
                throw new InvalidOperationException($"No e-sign config found for siteId: {siteId}");

            var request = new Request
            {
                AccessToken = config.AccessToken,
                AppName = config.AppName,
                DataType = config.DataType,
                Position = new AnonymousGateway.Position
                {
                    X = config.Position.X,
                    Y = Convert.ToInt32(positionY),
                    Width = config.Position.Width,
                    Height = config.Position.Height,
                },
                Data = ms.ToArray(),
                VisibleImage = false,
                Page = pageNumber,
                SignDate = eirDate
            };

            return await _anonymousGateway.ESignPdfAsync(siteId, request, "EportReport", "", AppUtil.GetTransId());
        }
        //private IDocument PreparePdfDocument(string html, string billType, string title)
        //{
        //    var pdf = _document.Value;
        //    if (pdf?.Objects != null && pdf.Objects.Any())
        //    {
        //        pdf.GlobalSettings.DocumentTitle = title;
        //        pdf.Objects[0].HtmlContent = html;
        //        pdf.Objects[0].WebSettings.UserStyleSheet = StaticPath.Instance.GetPath(PathTypes.Path_Css_With_BillType, billType);
        //        if (pdf.GlobalSettings.PaperSize == null)
        //        {
        //            pdf.GlobalSettings.PaperSize = PaperKind.A4;
        //        }
        //    }
        //    return pdf;
        //}
        private HtmlToPdfDocument PreparePdfDocument(string html, string billType, string title)
        {
            var objectSection = _config.GetSection("HtmlToPdfDocument:Objects:0");

            var printMediaType = objectSection.GetSection("WebSettings:PrintMediaType").Get<bool?>() ?? true;
            var encoding = objectSection.GetSection("WebSettings:DefaultEncoding").Get<string>() ?? "utf-8";

            var headerFontName = objectSection.GetSection("HeaderSettings:FontName").Get<string>() ?? "Arial";
            var headerFontSize = objectSection.GetSection("HeaderSettings:FontSize").Get<int?>() ?? 9;
            var headerRight = objectSection.GetSection("HeaderSettings:Right").Get<string>();
            var headerLine = objectSection.GetSection("HeaderSettings:Line").Get<bool?>() ?? true;

            var footerFontName = objectSection.GetSection("FooterSettings:FontName").Get<string>() ?? "Arial";
            var footerFontSize = objectSection.GetSection("FooterSettings:FontSize").Get<int?>() ?? 9;
            var footerCenter = objectSection.GetSection("FooterSettings:Center").Get<string>();
            var footerLine = objectSection.GetSection("FooterSettings:Line").Get<bool?>() ?? true;
            var footerSpacing = objectSection.GetSection("FooterSettings:Spacing").Get<double?>() ?? 1.8;

            var pdf = new HtmlToPdfDocument
            {
                GlobalSettings = new GlobalSettings
                {
                    DocumentTitle = title,
                    PaperSize = PaperKind.A4,
                    Orientation = Orientation.Portrait,
                    Margins = new MarginSettings { Top = 10, Bottom = 10, Left = 10, Right = 10 }
                },
                Objects = {
                    new ObjectSettings
                    {
                        HtmlContent = html,
                        WebSettings = new WebSettings
                        {
                            PrintMediaType = printMediaType,
                            DefaultEncoding = encoding,
                            UserStyleSheet = StaticPath.Instance.GetPath(PathTypes.Path_Css_With_BillType, billType)
                        },
                        HeaderSettings = new HeaderSettings
                        {
                            FontName = headerFontName,
                            FontSize = headerFontSize,
                            Right = headerRight,
                            Line = headerLine
                        },
                        FooterSettings = new FooterSettings
                        {
                            FontName = footerFontName,
                            FontSize = footerFontSize,
                            Center = footerCenter,
                            Line = footerLine,
                            Spacing = footerSpacing
                        }
                    }
                }
            };

            return pdf;
        }

        private async Task<byte[]> SafeConvertPdfAsync(IDocument pdf)
        {
            // .NET Framework không hỗ trợ WaitAsync
            _pdfConvertLock.Wait();
            try
            {
                var PdfConvertTimeoutMs = _config.GetValue<int>("PdfConvertTimeoutMs", 15000);
                var cts = new CancellationTokenSource(PdfConvertTimeoutMs);
                var task = Task.Run(() => _converter.Convert(pdf), cts.Token);

                if (await Task.WhenAny(task, Task.Delay(PdfConvertTimeoutMs, cts.Token)) == task)
                {
                    return task.Result;
                }

                throw new TimeoutException("PDF conversion timed out.");
            }
            catch (Exception ex)
            {
                LogManager.Logger(_config).Information($"PDF Convert Error: {(ex.InnerException ?? ex).Message}");
                throw;
            }
            finally
            {
                _pdfConvertLock.Release();
            }
        }

        private CoordinatePdfModal GetCoordinatesText(byte[] bytes)
        {
            var result = new CoordinatePdfModal();
            PdfDocument doc = new PdfDocument();
            doc.LoadFromBytes(bytes);

            int pageIndex = 1;
            foreach (PdfPageBase page in doc.Pages)
            {
                var results = page.FindText(_textToSearch, TextFindParameter.IgnoreCase).Finds;
                if (results.Length > 0)
                {
                    foreach (PdfTextFind text in results)
                    {
                        PointF p = text.Position;
                        result.X = p.X;
                        result.Y = p.Y;
                    }
                    result.PageNumber = pageIndex; // đúng trang có text
                    break;
                }
                pageIndex++;
            }

            result.PageNumber = doc.Pages.Count;
            return result;
        }
    }
}