using AnonymousGateway;
using DinkToPdf;
using DinkToPdf.Contracts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Snp.PDF.Printer.Logs;
using Snp.PDF.Printer.Models;
using Snp.PDF.Printer.Models.ESignRequest;
using Snp.PDF.Printer.Services.v2.Utils;
using SNP.PDF.Printer;
using Spire.Pdf;
using Spire.Pdf.General.Find;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Snp.PDF.Printer.Services.v2
{
    public class ConvertHtml2PdfService : IConvertHtml2PdfService
    {
        private readonly string _textToSearch = "Version";

        #region IDispose pattern

        // Flag: Has Dispose already been called?
        private bool disposed = false;

        // Public implementation of Dispose pattern callable by consumers.
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        // Protected implementation of Dispose pattern.
        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;

            if (disposing)
            {
                // Free any other managed objects here.
                //
            }

            // Free any unmanaged objects here.
            //
            disposed = true;
        }

        ~ConvertHtml2PdfService()
        {
            Dispose(false);
        }

        #endregion IDispose pattern

        #region Defines

        private readonly IOptions<HtmlToPdfDocument> _document;
        private readonly IConverter _converter;
        private readonly IAnonymousGateway _anonymousGateway;
        private readonly IConfiguration _config;
        private readonly IOptions<List<ESignRequestDTO>> _eSignRequest;
        #endregion Defines

        #region C'tor

        /// <summary>
        /// C'tor
        /// </summary>
        public ConvertHtml2PdfService(IConverter converter,
            IOptions<HtmlToPdfDocument> document,
            IAnonymousGateway anonymousGateway,
            IConfiguration config,
            IOptions<List<ESignRequestDTO>> eSignRequest)
        {
            _converter = converter;
            _document = document;
            _config = config;
            _anonymousGateway = anonymousGateway;
            _eSignRequest = eSignRequest;
        }

        #endregion C'tor

        /// <summary>
        /// Convert Html to Pdf
        /// </summary>
        /// <param name="data"></param>
        /// <param name="billType"></param>
        /// <param name="fileName"></param>
        /// <param name="siteId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<byte>> ConvertAsync(dynamic data, string billType, string fileName, string siteId)
        {
            try
            {
                var siteApply = _config.GetValue<string>("SiteApplyDigitalSign");

                if (!siteApply.Contains(siteId.Trim()))
                {
                    return await ConvertPdfWithoutSignAsync(data, billType, fileName);
                }
                if (siteId == "GNL")
                {
                    billType = billType + $"_{siteId}";
                }
                var html = await BindingDataToHtml.Instance.FillDataIntoHtmlTemplate(billType, data);

                var documentTitle = System.IO.Path.GetFileNameWithoutExtension(fileName);

                var pdf = _document.Value;
                if (pdf?.Objects != null
                   && pdf.Objects.Any())
                {
                    pdf.GlobalSettings.DocumentTitle = documentTitle;

                    pdf.Objects[0].HtmlContent = html;
                    pdf.Objects[0].WebSettings.UserStyleSheet = StaticPath.Instance.GetPath(PathTypes.Path_Css_With_BillType, billType: billType);
                    if (pdf.GlobalSettings.PaperSize == null)
                    {
                        pdf.GlobalSettings.PaperSize = PaperKind.A4;
                    }
                }

                var eirDate = DateTime.Now;

                foreach (Newtonsoft.Json.Linq.JProperty item in data)
                {
                    if (item.Name == "NgayGioTaoEIR")
                    {
                        eirDate = DateTime.ParseExact(item.Value.ToString(), "dd/MM/yyyy HH:mm:ss", new DateTimeFormatInfo { LongTimePattern = "dd/MM/yyyy HH:mm:ss" });
                        break;
                    }
                }

                var bytes = _converter.Convert(pdf);
                var pdfInfo = GetCoordinatesText(bytes);

                using (MemoryStream ms = new MemoryStream(bytes))
                using (MemoryStream outStream = new MemoryStream())
                {
                    try
                    {
                        if (siteApply.Contains(siteId.Trim()))
                        {
                            Response response = await ESignPdf(siteId.Trim(), eirDate, ms, pdfInfo.PageNumber, pdfInfo.Y + 3);
                            if (response != null && response.ReturnCode == 0)
                            {
                                byte[] buff = response.Data;
                                outStream.Write(buff, 0, buff.Length);
                                return await Task.FromResult(outStream.ToArray());
                            }
                            else
                            {
                                var logger = LogManager.Logger(_config);
                                logger.Information(string.Format("Signed Error: {0}", response.Message));
                                return await ConvertPdfWithoutSignAsync(data, billType, fileName);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var logger = LogManager.Logger(_config);
                        logger.Information(string.Format("Signed Error: {0}", (ex.InnerException ?? ex).Message));
                        //return await Task.FromResult(bytes);
                        return await ConvertPdfWithoutSignAsync(data, billType, fileName);
                    }
                }

                return await Task.FromResult(bytes);
            }
            catch (Exception)
            {
                return await Task.FromResult(Enumerable.Empty<byte>());
            }
        }

        private async Task<IEnumerable<byte>> ConvertPdfWithoutSignAsync(dynamic data, string billType, string fileName)
        {
            try
            {
                billType = billType.Replace("_GNL", "");
                var html = await BindingDataToHtml.Instance.FillDataIntoHtmlTemplate($"{ billType }_NO_SIGN", data);

                var documentTitle = Path.GetFileNameWithoutExtension(fileName);

                var pdf = _document.Value;
                if (pdf?.Objects != null
                   && pdf.Objects.Any())
                {
                    pdf.GlobalSettings.DocumentTitle = documentTitle;

                    pdf.Objects[0].HtmlContent = html;
                    pdf.Objects[0].WebSettings.UserStyleSheet = StaticPath.Instance.GetPath(PathTypes.Path_Css_With_BillType, billType: billType);
                    if (pdf.GlobalSettings.PaperSize == null)
                    {
                        pdf.GlobalSettings.PaperSize = PaperKind.A4;
                    }
                }

                var bytes = _converter.Convert(pdf);

                return await Task.FromResult(bytes);
            }
            catch (Exception)
            {
                return await Task.FromResult(Enumerable.Empty<byte>());
            }
        }

        private async Task<Response> ESignPdf(string siteId, DateTime eirDate, MemoryStream ms, int pageNumber, float positionY)
        {
            var config = _eSignRequest.Value.Where(x => x.SiteId == siteId).FirstOrDefault();
            var request = new Request
            {
                AccessToken = config.AccessToken,
                AppName = config.AppName,
                DataType = config.DataType,
                Position = new AnonymousGateway.Position
                {
                    X = config.Position.X,
                    Y = Convert.ToInt32(positionY),
                    Width = config.Position.Width,
                    Height = config.Position.Height,
                },
                Data = ms.ToArray(),
                VisibleImage = false,
                Page = pageNumber,
                SignDate = eirDate
            };

            var response = await _anonymousGateway.ESignPdfAsync(siteId, request, "EportReport", "", AppUtil.GetTransId());
            return response;
        }

        private CoordinatePdfModal GetCoordinatesText(byte[] bytes)
        {
            var result = new CoordinatePdfModal();
            PdfDocument doc = new PdfDocument();
            doc.LoadFromBytes(bytes);

            foreach (PdfPageBase page in doc.Pages)
            {
                var results = page.FindText(_textToSearch, TextFindParameter.IgnoreCase).Finds;

                if (results.Length < 1)
                {
                    continue;
                }

                foreach (PdfTextFind text in results)
                {
                    PointF p = text.Position;
                    result.X = p.X;
                    result.Y = p.Y;
                }
            }

            result.PageNumber = doc.Pages.Count;
            return result;
        }
    }
}