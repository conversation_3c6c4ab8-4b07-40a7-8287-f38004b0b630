﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;
using DigitalSignService;
using DinkToPdf;
using DinkToPdf.Contracts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using SNP.PDF.Printer.Enums;
using SNP.PDF.Printer.Helper;
using SNP.PDF.Printer.Models.Entities;

namespace SNP.PDF.Printer.Services
{
    /// <inheritdoc />
    public class PhieuBase : IPhieu
    {
        private readonly IOptions<HtmlToPdfDocument> _document;

        protected IConverter Converter;
        private readonly IConfiguration _config;
        private readonly ISnpDigitalSignBE _snpDigitalSignBE;
        public PhieuBase(IConverter converter,
            IOptions<HtmlToPdfDocument> document,
            ISnpDigitalSignBE snpDigitalSignBE,
            IConfiguration config)
        {
            Converter = converter;
            _document = document;
            _snpDigitalSignBE = snpDigitalSignBE;
            _config = config;
        }

        #region IPhieu Members

        public async Task<string> CreatePhieu<T>(T data, MetaEntity meta)
        {
            var documentName = meta.FileName;

            var fileName = $"{Identity.Next()}.{FileType.pdf}";

            #region Set file name and document name

            if (string.IsNullOrEmpty(documentName))
            {
                documentName = $"{meta.Type}";
            }

            if (documentName.EndsWith(FileType.pdf.ToString(), StringComparison.CurrentCultureIgnoreCase))
            {
                documentName = documentName.Substring(0, documentName.Length - 4);
            }

            #endregion

            #region Set html, css

            var html = await AppContext.Instance()
                                       .GetHtmlTemplatebyKey($"{meta.Type.ToString()}.{FileType.cshtml.ToString()}", data);
            var pathCss = string.Format(AppUtil.CSS_PATH, meta.Type, FileType.css.ToString());

            #endregion

            if (string.IsNullOrEmpty(html))
            {
                return string.Empty;
            }

            Print(html, pathCss, fileName, documentName);

            return string.Format(AppUtil.EXPORT_PDF_PATH, AppContext.AppBaseUrl, $"{DateTime.Now:ddMMyyyy}", fileName);
        }

        public async Task<string> CreateMultiplePhieu<T>(List<T> data, MetaEntity meta)
        {
            List<string> pdfFileNames = new List<string>();
            string fileZipName = string.Empty;
            var documentTitle = meta.FileName;

            foreach (var model in data)
            {
                var fileName = $"{Identity.Next()}.{FileType.pdf}";

                #region Set html, css

                var html = await AppContext.Instance()
                                           .GetHtmlTemplatebyKey($"{meta.Type.ToString()}.{FileType.cshtml.ToString()}", model);
                var pathCss = string.Format(AppUtil.CSS_PATH, meta.Type, FileType.css.ToString());

                #endregion

                if (string.IsNullOrEmpty(html))
                {
                    return string.Empty;
                }

                Print(html, pathCss, fileName, documentTitle);

                pdfFileNames.Add(string.Format(AppUtil.PDF_PATH, Directory.GetCurrentDirectory(), $"{DateTime.Now:ddMMyyyy}", fileName));
            }

            if (string.IsNullOrEmpty(fileZipName))
            {
                fileZipName = $"{Identity.Next()}.{FileType.zip}";
            }

            CreateZipFile(AppUtil.GetZipPath(fileZipName), pdfFileNames);

            return string.Format(AppUtil.EXPORT_ZIP_PATH, AppContext.AppBaseUrl, $"{DateTime.Now:ddMMyyyy}", fileZipName);
        }

        #endregion

        /// <summary>
        ///     Tạo file pdf
        /// </summary>
        /// <param name="html">Nội dung html</param>
        /// <param name="pathCss">Đường dẫn css</param>
        /// <param name="fileName">Tên file</param>
        /// <param name="documentTitle">Tên document hiển thị trên pdf</param>
        private void Print(string html, string pathCss, string fileName, string documentTitle)
        {
            var pdf = _document.Value;
            if (pdf?.Objects != null
               && pdf.Objects.Any())
            {
                pdf.GlobalSettings.DocumentTitle = documentTitle;

                pdf.Objects[0]
                   .HtmlContent = html;
                pdf.Objects[0]
                   .WebSettings.UserStyleSheet = Path.Combine(Directory.GetCurrentDirectory(), pathCss);
                if (pdf.GlobalSettings.PaperSize == null)
                {
                    pdf.GlobalSettings.PaperSize = PaperKind.A4;
                }
            }

            var file = Converter.Convert(pdf);

            using (MemoryStream ms = new MemoryStream(file))
            using (MemoryStream outStream = new MemoryStream())
            {
                try
                {
                    var request = new ESignRequest
                    {
                        AccessToken = _config.GetValue<string>("ESignRequest:AccessToken"),
                        AppName = _config.GetValue<string>("ESignRequest:AppName"),
                        DataType = _config.GetValue<string>("ESignRequest:DataType"),
                        Position = _config.GetValue<Position>("ESignRequest:Position"),
                        Data = ms.ToArray(),
                        VisibleImage = true,
                        Page = 1
                    };

                    var response = _snpDigitalSignBE.ESignPdfAsync(request);
                    if (response.IsCompleted)
                    {
                        byte[] data = response.Result.Data;
                        outStream.Write(data, 0, data.Length);
                    }
                }
                catch (Exception ex)
                {
                    ms.CopyTo(outStream);
                }
                finally
                {
                    var destPath = AppUtil.GetDesPath(fileName);
                    using (var fileStream = new FileStream(destPath, FileMode.Create, FileAccess.Write))
                    {
                        outStream.CopyTo(fileStream);
                    }
                }
            }
        }

        /// <summary>
        ///     nén nhiều file thành file zip
        /// </summary>
        /// <param name="fileZipName"></param>
        /// <param name="files"></param>
        private void CreateZipFile(string fileZipName, List<string> files)
        {
            using (var zip = ZipFile.Open(fileZipName, ZipArchiveMode.Create))
            {
                foreach (var file in files)
                {
                    // Add the entry for each file
                    zip.CreateEntryFromFile(file, Path.GetFileName(file), CompressionLevel.Optimal);
                }
            }
        }
    }
}
