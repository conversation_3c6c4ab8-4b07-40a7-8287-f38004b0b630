﻿using SNP.PDF.Printer.Resources;

namespace Snp.PDF.Printer.Services.v2.Validate.RuleValidate
{
    public class BaseRuleValidate
    {
        protected Error Invalid(string value, string propertyName, string message)
        {
            var error = Resource.Instance.RequireInvalid;
            return new Error(error.ErrorCode, error.Message, value, propertyName, message);// value not compare 
        }
    }
}
