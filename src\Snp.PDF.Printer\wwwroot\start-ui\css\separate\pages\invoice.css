/* ==========================================================================
   Variables
   ========================================================================== */
.invoice h5 {
  color: #343434;
  font-weight: 600;
  font-size: 1.125rem;
}
.invoice .company-info h5 {
  margin-bottom: 5px;
}
.invoice .invoice-block {
  margin-top: 30px;
}
.invoice .invoice-block div {
  margin-bottom: 10px;
}
.invoice .payment-details {
  margin-top: 25px;
  float: right;
  width: 500px;
  border: 1px solid #d8e2e7;
  -webkit-border-radius: 4px;
          border-radius: 4px;
  padding: 25px;
}
.invoice .payment-details strong {
  display: block;
  margin-bottom: 25px;
}
.invoice .payment-details table {
  width: 100%;
}
.invoice .payment-details table td {
  padding-bottom: 10px;
}
.invoice .payment-details table tr:first-child td {
  padding-bottom: 25px;
}
.invoice .payment-details table tr:last-child td {
  padding-bottom: 0;
}
.invoice .payment-details table td {
  width: 50%;
}
.invoice .table-details {
  margin-top: 35px;
  margin-bottom: 35px;
}
.invoice .terms-and-conditions strong {
  display: block;
  margin-bottom: 10px;
}
.invoice .total-amount {
  width: 300px;
  float: right;
}
.invoice .total-amount div {
  margin-bottom: 15px;
  padding-left: 30px;
}
.invoice .total-amount .colored {
  color: #00A8FF;
}
.invoice .total-amount .actions {
  margin-top: 35px;
  padding-left: 0;
}
.invoice .total-amount .actions .btn {
  min-width: 120px;
  margin-right: 20px;
}
.invoice .total-amount .actions .btn:last-child {
  margin-right: 0;
}
@media (max-width: 1199px) {
  .invoice .payment-details {
    width: 354px;
  }
  .invoice .total-amount,
  .invoice .payment-details {
    float: none;
  }
  .invoice .total-amount {
    margin-top: 35px;
  }
}
@media (max-width: 1056px) {
  .invoice .payment-details {
    width: 500px;
  }
  .invoice .invoice-info {
    margin-top: 35px;
  }
}
