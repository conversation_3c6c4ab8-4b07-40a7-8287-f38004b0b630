﻿using Microsoft.Extensions.Configuration;
using Serilog;
using Serilog.Core;

namespace Snp.PDF.Printer.Logs
{
    public class SerilogHelper
    {
        private static IConfiguration _configuration;

        private Logger _default;

        public SerilogHelper(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        private Logger Instance()
        {
            if(_default == null)
            {
                _default = new LoggerConfiguration().ReadFrom.Configuration(_configuration)
                                                    .CreateLogger();
            }

            return _default;
        }

        public void Information(string obj)
        {
            Instance()
                    .Information(obj);
        }
    }
}
