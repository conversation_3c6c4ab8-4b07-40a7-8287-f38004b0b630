﻿using SNP.PDF.Printer.Models.Base;

namespace Snp.PDF.Printer.Models.v2
{
    public class PrinterPdfResponse : BaseResponse<string>
    {
        public PrinterPdfResponse(string data) : base(data)
        {
        }
        public PrinterPdfResponse(string data, bool isError, string message, string errorCode)
            : base(data, isError, message, errorCode)
        {

        }

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
        }
    }
}
