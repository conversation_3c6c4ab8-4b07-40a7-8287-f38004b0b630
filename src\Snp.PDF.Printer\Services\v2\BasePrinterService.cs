﻿using Microsoft.AspNetCore.Http;
using Snp.PDF.Printer.Models.v2;
using SNP.PDF.Printer.Resources;
using System;
using System.Threading.Tasks;

namespace Snp.PDF.Printer.Services.v2
{
    public class BasePrinterService
    {
        private readonly IValidateModel _validateModel;
        public BasePrinterService(IValidateModel validateModel)
        {
            _validateModel = validateModel;
        }
        protected Error CheckValidateSingleFile(dynamic data, string billType)
        {
            return  _validateModel.Validate(data, billType);
        }

        protected Task<PrinterPdfResponse> Ok(string pathPdf)
        {
            return Task.FromResult(new PrinterPdfResponse(pathPdf));
        }
        protected Task<PrinterPdfResponse> Failure(Error error)
        {
            return  Task.FromResult(new PrinterPdfResponse(string.Empty, true, error.Message, error.ErrorCode));
        }
        protected Task<PrinterPdfResponse> Failure(PrinterPdfResponse response)
        {
            return Task.FromResult(response);
        }
        protected Task<PrinterPdfResponse> Exception(Exception ex)
        {
            return  Task.FromResult(new PrinterPdfResponse(string.Empty, 
                true, 
                ex.ToString(), 
                StatusCodes.Status500InternalServerError.ToString()));
        }

    }
}
