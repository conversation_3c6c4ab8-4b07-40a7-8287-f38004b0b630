﻿using RazorLight;
using Snp.PDF.Printer.Services.v2.Helper;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Snp.PDF.Printer.Services.v2.Utils
{
    public class BindingDataToHtml
    {
        private static Lazy<BindingDataToHtml> _instance = new Lazy<BindingDataToHtml>(() => new BindingDataToHtml());
        public static BindingDataToHtml Instance
        {
            get
            {
                return _instance.Value;
            }
        }
        /// <summary>
        /// filename htmltemplate (default name: template.cshtml)
        /// </summary>
        private static string FileNameHtmlTemplate;

        /// <summary>
        /// dictionary IRazorLightEngine with key is billtype
        /// </summary>
        private static IDictionary<string, IRazorLightEngine> _engines;

        #region C'tor
        /// <summary>
        /// C'tor
        /// </summary>
        public BindingDataToHtml()
        {
            if (_engines == null || _engines.Count == 0)
                Init();
        }
        #endregion

        #region private methods
        private void Init()
        {
            _engines = new Dictionary<string, IRazorLightEngine>();
            var path = string.Empty;
            var firstFileName = string.Empty;
            (string Directory, string FileName) res;

            var billTypes = ConfigHelper.Instance.BillTypes;

            foreach (var billtype in billTypes)
            {
                path = StaticPath.Instance.GetPath(PathTypes.Path_HtmlTemplate_With_BillType, billType: billtype);

                res = RetrievalDirectoryAndFileName(path);

                _engines.Add(billtype, (new EngineFactory()).ForFileSystem(res.Directory));

                // chỉ lấy đại diện 1 file name, các file name đều có tên giống nhau: template.html
                if (!string.IsNullOrEmpty(firstFileName)) continue;
                firstFileName = res.FileName;
            }

            FileNameHtmlTemplate = firstFileName;
        }
        private static (string Directory, string FileName) RetrievalDirectoryAndFileName(string fullpath)
        {
            var directory = System.IO.Path.GetDirectoryName(fullpath);
            var filename = System.IO.Path.GetFileName(fullpath);
            return (directory, filename);
        }
        #endregion


        /// <summary>
        /// Fill Data Into HtmlTemplate
        /// </summary>
        /// <param name="billType"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<string> FillDataIntoHtmlTemplate(string billType, dynamic model)
        {
            try
            {
                if (!_engines.TryGetValue(billType, out IRazorLightEngine engine)) return string.Empty;

                return await engine.CompileRenderAsync(FileNameHtmlTemplate, model);

            }
            catch (Exception)
            {
                return string.Empty;
            }
        }
    }
}
