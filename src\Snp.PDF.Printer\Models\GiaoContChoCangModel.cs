﻿using System.Collections.Generic;
using SNP.PDF.Printer.Models.Base;

namespace SNP.PDF.Printer.Models
{
    public class GiaoContChoCangModel
    {
        /// <summary>
        ///     Logo công ty, lấy theo bảng cấu hình thông tin Site (AM_SITE)
        /// </summary>
        public string LogoType { get; set; }

        /// <summary>
        ///     Tên công ty, lấy theo bảng cấu hình thông tin Site (AM_SITE)
        /// </summary>
        public string TenCongTy { get; set; }

        /// <summary>
        ///     Tên phương án của phiếu EIR được chọn
        /// </summary>
        public string TenPhuongAn { get; set; }

        /// <summary>
        ///     Là site giao nhận với site phiếu EIR được chọn SITE, lấy theo bảng cấu hình thông tin Site (AM_SITE)
        ///     Chỉ in khi nơi giao khác KHA/rỗng/Site phiếu EIR
        /// </summary>
        public string SiteGiaoNhan { get; set; }

        /// <summary>
        ///     Là site phiếu EIR được chọn Site, lấy theo bảng cấu hình thông tin Site (AM_SITE)
        ///     Chỉ in khi nơi giao khác KHA/rỗng/Site phiếu EIR
        /// </summary>
        public string SitePhieuEIR { get; set; }

        /// <summary>
        ///     Là Id của phiếu EIR được chọn
        ///     Cột EIR_ID trong màn hình T61
        /// </summary>
        public string SoPhieuEIR { get; set; }

        /// <summary>
        ///     Là ngày tạo phiếu EIR, format: dd/MM/yyyy hh:mm:sss
        ///     Cột CreateDate trong màn hình T61
        /// </summary>
        public string NgayGioTaoEIR { get; set; }

        /// <summary>
        ///     Mã cổng giao nhận Container
        ///     Cột GateNo trong màn hình T61
        /// </summary>
        public string GateNo { get; set; }

        /// <summary>
        ///     Là số Bat giao nhận container
        ///     Trường số BAT trong màn hình T01
        /// </summary>
        public string BatNo { get; set; }

        /// <summary>
        ///     Là tên khách hàng tương ứng phiếu EIR được chọn
        ///     Cột CustName trong màn hình T61
        /// </summary>
        public string TenKhachHang { get; set; }

        /// <summary>
        ///     Là số bill tương ứng phiếu EIR được chọn
        ///     Trường số B/L trong màn hình C01 tương ứng Item key
        /// </summary>
        public string SoBillOfLading { get; set; }

        /// <summary>
        ///     Là tên tàu nhập/xuất + mã chuyến tương ứng phiếu EIR được chọn
        ///     Cấu trúc tên tàu / mã chuyến
        ///     Trường tàu nhập khẩu hoặc tàu xuất khẩu trong màn hình C01 tương ứng Item key
        /// </summary>
        public string TenTauMaChuyen { get; set; }

        /// <summary>
        ///     Là cảng đích tương ứng phiếu EIR được chọn
        ///     Trường FinalPort trong màn hình T61
        /// </summary>
        public string CangDich { get; set; }

        /// <summary>
        ///     Là cảng chuyển tải tương ứng phiếu EIR được chọn
        ///     Cột LL dích Port trong màn hình T61
        /// </summary>
        public string CangChuyenTai { get; set; }

        /// <summary>
        ///     Là thông tin về trọng lượng Max Gross của cont trên phiếu EIR
        ///     Trường Max gross (ton) trong màn hình C01 tương ứng Item key, chỉ thể hiện số
        /// </summary>
        public string MaxGross { get; set; }

        /// <summary>
        ///     Là thông tin về trọng lượng VGM của cont do khách hàng khai báo hoặc cảng nhập
        ///     Trường VGM (ton) trong màn hình C01 tương ứng Item key, chỉ thể hiện số
        /// </summary>
        public string VgmCustomer { get; set; }

        /// <summary>
        ///     Thông tin trọng lượng vỏ
        ///     Trường Tr.lượng vỏ (ton) trong màn hình C01 tương ứng Item key, chỉ thể hiện số
        /// </summary>
        public string TlVo { get; set; }

        /// <summary>
        ///     Là thông tin về số Đ.ký ePort của cont trên phiếu EIR
        /// </summary>
        public string SoDkEport { get; set; }

        /// <summary>
        ///     Thể hiện 1 trong 2 giá trị: “Cân” hoặc “Không cân” tương ứng với thực tế của container
        /// </summary>
        public string GhiChuThongTinCan { get; set; }

        /// <summary>
        ///     Lấy theo cấu trúc: “Xe số_xe, mooc số_mooc vi phạm tải trọng. Tải trọng tên_phương_tiện_vi_phạm số_tải_trọng tấn,
        ///     trọng lượng của container xe nhận trọng_lượng_tổng T, trọng tải tên_phương_tiện_vi_phạm = số_tải_trọng =vượt
        ///     tỷ_lệ_vượt (tương đương số_tấn_vượtT). Trong đó: Tỷ lệ vượt = (Trọng lượng tổng – tải trọng)/ tải trọng
        /// </summary>
        public string GhiChuViPhamTaiTrong { get; set; }

        /// <summary>
        ///     Là danh sách mô tả thông tin stop của Cont, lấy trong hệ thống TopO. Mỗi mô tả cách nhau dấu phẩy.
        ///     Lấy theo cấu trúc: “Stop code: trường mô tả trong màn hình CP6”. Mỗi mô tả cách nhau dấu phẩy (,)
        /// </summary>
        public string GhiChuStopCode { get; set; }

        /// <summary>
        ///     Là danh sách mô tả chỉ định của Cont, lấy trong hệ thống TopO. Mỗi mô tả cách nhau dấu phẩy.
        ///     Trường “Ghi chú” trong màn hình CP5”. Mỗi ghi chú cách nhau dấu phẩy (,)
        /// </summary>
        public string GhiChuChiDinh { get; set; }

        /// <summary>
        ///     Là danh sách comments về Cont, lấy trong hệ thống TopO. Mỗi comment cách nhau dấu phẩy.
        ///     Lấy theo cấu trúc: “Chỉ định: trường mô tả chỉ định trong màn hình CPA”. Mỗi mô tả cách nhau dấu phẩy (,)
        /// </summary>
        public string GhiChuThongTinCont { get; set; }

        /// <summary>
        ///     Thông tin hư hỏng của container
        ///     Lấy theo cấu trúc:
        ///     Trách nhiệm h/ h
        ///     :
        ///     Mô tả hư
        ///     Mô tả vị trí h/ h
        ///     kích thước h/ h lấy trong màn hình CP7. Mỗi hư hỏng cách nhau bởi dấu -
        /// </summary>
        public string GhiChuHuHong { get; set; }

        /// <summary>
        ///     Là thông tin số xe giao nhận cont trên phiếu EIR
        ///     Trường Xe trong màn hình T01
        /// </summary>
        public string SoXe { get; set; }

        /// <summary>
        ///     Là thông tin số moọc của cont trên phiếu EIR
        ///     Trường Số Mooc trong màn hình T01
        /// </summary>
        public string SoMooc { get; set; }

        /// <summary>
        ///     Là thông tin tên đầy đủ của khu vực
        /// </summary>
        public string TenSite { get; set; }

        /// <summary>
        ///     Là thông tin Slogan của site
        /// </summary>
        public string Slogan { get; set; }

        public List<GiaoContChoCangDetail> Detail { get; set; }
    }

    public class GiaoContChoCangDetail : BaseChiTietPhieuEir
    {
        /// <summary>
        ///     Nhiệt độ, thông gió, độ ẩm của Cont trên phiếu EIR
        /// </summary>
        public string Temt { get; set; }

        /// <summary>
        ///     Là số UNNO của Cont trên phiếu EIR
        ///     Trường UN trong màn hình CP2 tương ứng Item key, thể hiển tất cả cách nhau bằng dấu phẩy (,)
        /// </summary>
        public string SoUNNO { get; set; }
    }
}
